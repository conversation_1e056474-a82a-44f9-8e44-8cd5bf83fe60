-- moe_message.moe_schedule_message definition

CREATE TABLE `moe_schedule_message`
(
  `id`                       bigint unsigned NOT NULL AUTO_INCREMENT,
  `company_id`               bigint unsigned NOT NULL,
  `business_id`              bigint unsigned NOT NULL,
  `customer_id`              bigint unsigned NOT NULL,
  `contact_id`               bigint unsigned          DEFAULT '0' COMMENT 'Customer''s contact id, auto messages default to primary phone, custom messages can be specified manually',
  `creator_id`               bigint          NOT NULL DEFAULT '0' COMMENT 'The staff id that created this message',
  `updater_id`               bigint          NOT NULL DEFAULT '0' COMMENT 'The staff id that last updated this message',
  `sender_id`                bigint          NOT NULL DEFAULT '0' COMMENT 'The staff id that sent this message',
  `content`                  longtext        NOT NULL DEFAULT '' COMMENT 'Custom message content, two way message can be modified',
  `auto_message_type`        int             NOT NULL DEFAULT '0' COMMENT 'Auto message template, one way message cannot be modified',
  `auto_message_resource_id` bigint unsigned NOT NULL DEFAULT '0' COMMENT 'The resource id required by auto message, which can be appointment id',
  `status`                   varchar(255)    NOT NULL DEFAULT 'SCHEDULED' COMMENT 'SCHEDULED, SENT_SUCCESSFULLY, SENT_FAILED, DELETED',
  `send_out_at`              datetime        NOT NULL COMMENT 'Estimated delivery time',
  `sent_at`                  datetime        NULL     DEFAULT NULL COMMENT 'Actual delivery time',
  `created_at`               datetime        NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at`               datetime        NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `deleted_at`               datetime        NULL     DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `IDX_COMPANY_BUSINESS_CUSTOMER` (`company_id`, `business_id`, `customer_id`) USING BTREE,
  KEY `IDX_SEND_OUT_AT` (`send_out_at`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci;


insert into moe_payment.moe_feature(id, name, code, allow_type, enable, quota)
values (66, 'Scheduled messages', 'scheduledMessage', 2, 0, -1);

insert into moe_payment.moe_plan_feature_relation(level, code, allow_type, enable, quota)
values (1101, 'scheduledMessage', 2, 1, -1);
insert into moe_payment.moe_plan_feature_relation(level, code, allow_type, enable, quota)
values (1201, 'scheduledMessage', 2, 1, -1);

ALTER TABLE `moe_message`.`moe_schedule_message`
  ADD COLUMN `method` int NOT NULL DEFAULT 1 COMMENT '1-msg，2-email 4-call, 5-app' AFTER `sent_at`,
