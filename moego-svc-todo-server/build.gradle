plugins {
    id 'org.springframework.boot'
    id "com.qqviaja.gradle.MybatisGenerator"
}

apply from: "https://moego.s3.us-west-2.amazonaws.com/ops/sbc/${System.getProperty('sbc.scriptBranch', '')}SourceBranchControl.gradle"
applySourceBranchControlDependencies()

dependencies {
    implementation("org.mybatis.spring.boot:mybatis-spring-boot-starter:${mybatisBootStarterVersion}")
    implementation("org.mybatis.dynamic-sql:mybatis-dynamic-sql:${mybatisDynamicSqlVersion}")
    implementation("io.grpc:grpc-services:${grpcVersion}")
    runtimeOnly("com.mysql:mysql-connector-j")

    compileOnly("org.projectlombok:lombok")
    annotationProcessor("org.projectlombok:lombok")
    // see https://mapstruct.org/faq/#Can-I-use-MapStruct-together-with-Project-Lombok
    annotationProcessor("org.projectlombok:lombok-mapstruct-binding:${lombokMapstructBindingVersion}")
    implementation("org.mapstruct:mapstruct:${mapstructVersion}")
    annotationProcessor("org.mapstruct:mapstruct-processor:${mapstructVersion}")

    testImplementation("org.springframework.boot:spring-boot-starter-test")
    testImplementation("io.grpc:grpc-testing")

    // aws secrets manager
    implementation 'io.awspring.cloud:spring-cloud-aws-starter-secrets-manager:3.2.1'

    // spring cloud
    implementation 'org.springframework.cloud:spring-cloud-starter-bootstrap'
    // nacos config
    implementation("com.alibaba.cloud:spring-cloud-starter-alibaba-nacos-config") {
        exclude(group: "com.alibaba.nacos", module: "nacos-client")
    }
    implementation("com.alibaba.nacos:nacos-client:${nacosVersion}")
}

bootJar {
    archiveBaseName = 'moego-server'
    version = ''
}

configurations {
    mybatisGenerator
}
mybatisGenerator {
    verbose = true
    configFile = "${projectDir}/MyBatisGeneratorConfig.xml"

    dependencies {
        mybatisGenerator "org.mybatis.generator:mybatis-generator-core:${mybatisGeneratorCoreVersion}"
        mybatisGenerator "com.mysql:mysql-connector-j"
    }
}
