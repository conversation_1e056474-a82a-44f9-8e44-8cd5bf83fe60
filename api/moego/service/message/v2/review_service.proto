syntax = "proto3";

package moego.service.message.v2;

import "moego/models/business_customer/v1/business_customer_merge_defs.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/message/v2;messagesvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.message.v2";

// 评论服务，由于历史原因，先放到 message 里边
service ReviewService {
  // 合并评论
  rpc MergeReviews(MergeReviewsRequest) returns (MergeReviewsResponse);
}

// 合并评论请求
message MergeReviewsRequest {
  // company id
  int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];
  // merge relation
  models.business_customer.v1.BusinessCustomerMergeRelationDef merge_relation = 2;
}

// 合并评论响应
message MergeReviewsResponse {
  // success
  bool success = 1;
}
