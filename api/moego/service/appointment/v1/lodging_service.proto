// @since 2024-01-15 16:36:40
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.service.appointment.v1;

import "moego/models/appointment/v1/lodging_models.proto";
import "moego/utils/v2/list.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/appointment/v1;appointmentsvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.appointment.v1";

// lodging transfer request
message LodgingTransferRequest {
  // company id
  int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];
  // business id
  int64 business_id = 2 [(validate.rules).int64 = {gt: 0}];
  // lodging id from
  int64 lodging_id_from = 3 [(validate.rules).int64 = {gt: 0}];
  // lodging id to
  int64 lodging_id_to = 4 [(validate.rules).int64 = {gt: 0}];
}

// lodging transfer response
message LodgingTransferResponse {}

// lodging assign info request
message LodgingAssignInfoRequest {
  // business id
  int64 business_id = 1 [(validate.rules).int64 = {gt: 0}];
  // start date
  string start_date = 2 [(validate.rules).string = {pattern: "^\\d{4}-\\d{2}-\\d{2}$"}];
  // end date
  string end_date = 3 [(validate.rules).string = {pattern: "^\\d{4}-\\d{2}-\\d{2}$"}];
  // will be ignored if lodging_ids is empty
  repeated int64 lodging_ids = 4 [(validate.rules).repeated = {min_items: 0}];
  // company id
  optional int64 company_id = 5 [(validate.rules).int64 = {gt: 0}];
}

// lodging assign info response
message LodgingAssignInfoResponse {
  // lodging assign info
  repeated moego.models.appointment.v1.LodgingAssignInfo lodging_assign_info = 1;
}

// check lodging if in use request
message LodgingInUseCheckRequest {
  // company id
  int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];
  // business id. Will be ignored if set zero
  int64 business_id = 2 [(validate.rules).int64 = {gte: 0}];
  // lodging id. deprecated, use lodging_ids instead
  int64 lodging_id = 3 [
    deprecated = true,
    (validate.rules).int64 = {gte: 0}
  ];
  // lodging ids
  repeated int64 lodging_ids = 4 [(validate.rules).repeated = {
    unique: true
    max_items: 100
    items: {
      int64: {gt: 0}
    }
  }];
}

// check lodging if in use response
message LodgingInUseCheckResponse {
  // upcoming appointments. deprecated, use lodging_upcoming_appointments instead
  int32 upcoming_appointments = 1;
  // lodging id to upcoming appointment ids
  map<int64, utils.v2.Int64List> lodging_upcoming_appointments = 2;
}

// the lodging service
service LodgingService {
  // lodging unit transfer
  rpc LodgingTransfer(LodgingTransferRequest) returns (LodgingTransferResponse);
  // lodging assign info
  rpc LodgingAssignInfo(LodgingAssignInfoRequest) returns (LodgingAssignInfoResponse);
  // check lodging if in use
  rpc LodgingInUseCheck(LodgingInUseCheckRequest) returns (LodgingInUseCheckResponse);
}
