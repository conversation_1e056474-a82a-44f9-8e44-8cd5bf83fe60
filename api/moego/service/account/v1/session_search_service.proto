syntax = "proto3";

package moego.service.account.v1;

import "moego/models/account/v1/session_models.proto";
import "moego/utils/v2/condition_messages.proto";
import "moego/utils/v2/pagination_messages.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/account/v1;accountsvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.account.v1";

// search session request
message SearchSessionRequest {
  // predicate
  moego.utils.v2.Predicate predicate = 1 [(validate.rules).message.required = true];

  // order by (support multi fields), optional
  repeated moego.utils.v2.OrderBy order_bys = 2;

  // pagination
  moego.utils.v2.PaginationRequest pagination = 3 [(validate.rules).message.required = true];
}

// search session response
message SearchSessionResponse {
  // pagination
  moego.utils.v2.PaginationResponse pagination = 1;

  // session list
  repeated models.account.v1.SessionModel sessions = 2;
}

// session search service
service SessionSearchService {
  // search sessions
  rpc SearchSession(SearchSessionRequest) returns (SearchSessionResponse);
}
