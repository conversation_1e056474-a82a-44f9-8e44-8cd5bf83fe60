syntax = "proto3";

package moego.service.online_booking.v1;

import "google/protobuf/timestamp.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/online_booking/v1;onlinebookingsvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.online_booking.v1";

// Create EvaluationTestDetail request
message CreateEvaluationTestDetailRequest {
  // The id of pet, associated with the current evaluation test
  int64 pet_id = 2 [(validate.rules).int64 = {gt: 0}];
  // The id of current evaluation test
  int64 evaluation_id = 3 [(validate.rules).int64 = {gt: 0}];
  // The price of current evaluation test
  optional double service_price = 4;
  // The duration of current evaluation test, unit minute
  optional int32 duration = 5;
  // The start date of the evaluation test, yyyy-MM-dd
  string start_date = 6 [(validate.rules).string = {pattern: "^\\d{4}-\\d{2}-\\d{2}$"}];
  // The start time of the evaluation test, unit minute, 540 means 09:00
  int32 start_time = 7 [(validate.rules).int32 = {gte: 0}];
  // The end date of the evaluation test, yyyy-MM-dd
  //  optional string end_date = 8 [(validate.rules).string = {max_len: 2048}];
  // The end time of the evaluation test, unit minute, 540 means 09:00
  //  optional int32 end_time = 9;
  // createdAt
  optional google.protobuf.Timestamp created_at = 10;
  // updatedAt
  optional google.protobuf.Timestamp updated_at = 11;
  // service id, evaluation 绑定的 service id
  optional int64 service_id = 12 [(validate.rules).int64 = {gt: 0}];
}

// EvaluationTestDetail service
service EvaluationTestDetailService {}
