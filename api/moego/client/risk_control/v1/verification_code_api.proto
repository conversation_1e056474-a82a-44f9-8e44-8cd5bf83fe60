syntax = "proto3";

package moego.client.risk_control.v1;

import "moego/models/account/v1/account_defs.proto";
import "moego/models/risk_control/v1/verification_code_defs.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/client/risk_control/v1;riskcontrolapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.client.risk_control.v1";

// send verification code request
message SendVerificationCodeRequest {
  // verification identifier, contains account information and applicable scenarios
  models.risk_control.v1.VerificationIdentifierDef identifier = 1 [(validate.rules).message = {required: true}];

  // the namespace of the verification code, default is MOEGO(id=0)
  // used to send entity display and SMS quota deduction
  moego.models.account.v1.NamespaceDef namespace = 2 [(validate.rules).message = {required: true}];
}

// send verification code response
message SendVerificationCodeResponse {
  // verification token, used for verification together with the verification code
  string token = 1;
}

// verification code API
service VerificationCodeService {
  // Send verification code to phone number or email verify identity.
  // anonymous session: for register or login in
  // login session: change phone number, change email, add phone number, etc.
  //
  // Error codes:
  // - MESSAGE_AMOUNT_RUN_OUT(80109): Bought message amount run out, no message left.
  // - CODE_VERIFICATION_CODE_SENT_COUNT_LIMITED(130003): Verification code limit reached. Please try tomorrow.
  // - CODE_VERIFICATION_CODE_SEND_INTERVAL_NOT_REACHED(130004): The verification code sent too frequently.
  // - CODE_INVALID_VERIFICATION_CODE(130005): Please enter the correct verification code.
  // - CODE_VERIFICATION_CODE_EXPIRED(130006): The verification code has expired. Please resend.
  rpc SendVerificationCode(SendVerificationCodeRequest) returns (SendVerificationCodeResponse);
}
