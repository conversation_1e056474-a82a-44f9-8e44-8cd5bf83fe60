syntax = "proto3";

package moego.client.payment.v1;

import "moego/models/online_booking/v1/payments_defs.proto";
import "moego/models/online_booking/v1/selected_defs.proto";
import "moego/models/order/v1/service_charge_model.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/client/payment/v1;paymentapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.client.payment.v1";

// get pre-auth amount request
message GetPreAuthAmountRequest {
  // selected business id
  int64 business_id = 1 [(validate.rules).int64 = {gt: 0}];
  // pet selected service list
  repeated moego.models.online_booking.v1.SelectedPetServiceDef selected_pet_services = 2;
  // discount code
  optional string discount_code = 3 [(validate.rules).string = {max_len: 20}];
}

// get pre-auth amount response
message GetPreAuthAmountResponse {
  // pre-auth amount
  moego.models.online_booking.v1.PreAuthDef pre_auth = 1;
  // service charge list
  repeated moego.models.order.v1.ServiceChargeOnlineBookingView service_charges = 2;
}

// pre-auth service
service PreAuthService {
  // get pre-auth amount
  rpc GetPreAuthAmount(GetPreAuthAmountRequest) returns (GetPreAuthAmountResponse);
}
