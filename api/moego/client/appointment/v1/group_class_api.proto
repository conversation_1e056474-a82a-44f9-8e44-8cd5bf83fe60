syntax = "proto3";

package moego.client.appointment.v1;

import "moego/models/business_customer/v1/business_customer_pet_models.proto";
import "moego/models/offering/v1/group_class_models.proto";
import "moego/utils/v2/pagination_messages.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/client/appointment/v1;appointmentapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.client.appointment.v1";

// The group class service, temporary design, will be replaced by the new fulfillment service
service GroupClassService {
  // Get customer group class
  rpc ListInstances(ListInstancesParams) returns (ListInstancesResult);
  // Get customer group class session
  rpc ListSessions(ListSessionsParams) returns (ListSessionsResult);
  // Get instance detail
  rpc GetInstanceDetail(GetInstanceDetailParams) returns (GetInstanceDetailResult);
}

// list customer group class instances request
message ListInstancesParams {
  // online booking name and domain
  oneof anonymous {
    option (validate.required) = true;
    // Book online name, demo URL: booking.moego.pet?name=CrazyCutePetSpa
    string name = 1;
    // Customized URL domain, demo URL: crazycutepetspa.moego.online
    string domain = 2;
  }
  // pagination
  moego.utils.v2.PaginationRequest pagination = 3 [(validate.rules).message = {required: true}];
}

// list customer group class instances result
message ListInstancesResult {
  // group class instance view
  message GroupInstanceView {
    // group class instance
    models.offering.v1.GroupClassInstance group_class_instance = 1;
    // sessions
    repeated models.offering.v1.GroupClassSession sessions = 2;
    // pets
    repeated moego.models.business_customer.v1.BusinessCustomerPetInfoModel pets = 3;
  }
  // group class instances
  repeated GroupInstanceView group_instances = 1;
  // group classes
  repeated models.offering.v1.GroupClassModel group_classes = 2;
  // pagination
  moego.utils.v2.PaginationResponse pagination = 3;
}

// list customer group class sessions request
message ListSessionsParams {
  // online booking name and domain
  oneof anonymous {
    option (validate.required) = true;
    // Book online name, demo URL: booking.moego.pet?name=CrazyCutePetSpa
    string name = 1;
    // Customized URL domain, demo URL: crazycutepetspa.moego.online
    string domain = 2;
  }
  // status
  enum Status {
    // status is unspecified
    STATUS_UNSPECIFIED = 0;
    // past
    PAST = 1;
    // upcoming
    UPCOMING = 2;
  }
  // status
  Status status = 3 [(validate.rules).enum = {defined_only: true}];
  // pagination
  moego.utils.v2.PaginationRequest pagination = 4 [(validate.rules).message = {required: true}];
}

// list customer group class sessions result
message ListSessionsResult {
  // group class session view
  message GroupSessionView {
    // group class session
    models.offering.v1.GroupClassSession session = 1;
    // pets
    repeated moego.models.business_customer.v1.BusinessCustomerPetInfoModel pets = 2;
  }
  // group class sessions
  repeated GroupSessionView sessions = 1;
  // group class instance
  repeated GroupInstanceView group_instances = 2;
  // group class
  repeated models.offering.v1.GroupClassModel group_classes = 3;
  // pagination
  moego.utils.v2.PaginationResponse pagination = 4;
}

// group instance with sessions
message GroupInstanceView {
  // group class instance
  models.offering.v1.GroupClassInstance group_instance = 1;
  // group session
  repeated models.offering.v1.GroupClassSession sessions = 2;
}

// get instance detail params
message GetInstanceDetailParams {
  // online booking name and domain
  oneof anonymous {
    option (validate.required) = true;
    // Book online name, demo URL: booking.moego.pet?name=CrazyCutePetSpa
    string name = 1;
    // Customized URL domain, demo URL: crazycutepetspa.moego.online
    string domain = 2;
  }
  // group class instance id
  int64 group_instance_id = 3 [(validate.rules).int64 = {gt: 0}];
  // pet ids
  repeated int64 pet_ids = 4;
}

// get instance detail result
message GetInstanceDetailResult {
  // group class session
  repeated models.offering.v1.GroupClassSession sessions = 1;
  // group class instance
  models.offering.v1.GroupClassInstance group_instance = 2;
  // group class
  models.offering.v1.GroupClassModel group_class = 3;
  // Trainer
  message TrainerView {
    // the staff id
    int64 id = 1;
    // first name
    string first_name = 2;
    // last name
    string last_name = 3;
    // avatar
    string avatar_path = 4;
  }
  // trainer
  TrainerView trainer = 4;
  // pet info
  repeated moego.models.business_customer.v1.BusinessCustomerPetInfoModel pets = 5;
}
