// @since 2024-06-14 13:53:23
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.admin.membership.v1;

import "moego/models/membership/v1/membership_defs.proto";
import "moego/models/membership/v1/membership_models.proto";
import "moego/utils/v2/pagination_messages.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/admin/membership/v1;membershipapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.admin.membership.v1";

// create membership params
message CreateMembershipParams {
  // the membership def
  moego.models.membership.v1.MembershipCreateDef membership_def = 1 [(validate.rules).message = {required: true}];
}

// create membership result
message CreateMembershipResult {
  // the created membership
  moego.models.membership.v1.MembershipModel membership = 1;
}

// get membership params
message GetMembershipParams {
  // the id
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
}

// get membership result
message GetMembershipResult {
  // the membership
  moego.models.membership.v1.MembershipModel membership = 1;
}

// list membership params
message ListMembershipsParams {
  // pagination
  optional moego.utils.v2.PaginationRequest pagination = 15;
}

// list membership result
message ListMembershipsResult {
  // the membership
  repeated moego.models.membership.v1.MembershipModel memberships = 1;
  // pagination
  moego.utils.v2.PaginationResponse pagination = 15;
}

// create membership params
message UpdateMembershipParams {
  // id
  int64 id = 1;
  // the membership def
  moego.models.membership.v1.MembershipUpdateDef membership_def = 2 [(validate.rules).message = {required: true}];
}

// create membership result
message UpdateMembershipResult {
  // the updated membership
  moego.models.membership.v1.MembershipModel membership = 1;
}

// get membership params
message DeleteMembershipParams {
  // the id
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
}

// get membership result
message DeleteMembershipResult {}

// the membership service
service MembershipService {
  // create membership
  rpc CreateMembership(CreateMembershipParams) returns (CreateMembershipResult);
  // get membership
  rpc GetMembership(GetMembershipParams) returns (GetMembershipResult);
  // list membership
  rpc ListMemberships(ListMembershipsParams) returns (ListMembershipsResult);
  // update membership
  rpc UpdateMembership(UpdateMembershipParams) returns (UpdateMembershipResult);
  // delete membership
  rpc DeleteMembership(DeleteMembershipParams) returns (DeleteMembershipResult);
}
