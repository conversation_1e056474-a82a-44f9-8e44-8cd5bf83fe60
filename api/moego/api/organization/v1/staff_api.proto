syntax = "proto3";

package moego.api.organization.v1;

import "google/type/date.proto";
import "moego/models/organization/v1/staff_defs.proto";
import "moego/models/organization/v1/staff_models.proto";
import "moego/utils/v2/condition_messages.proto";
import "moego/utils/v2/pagination_messages.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/organization/v1;organizationapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.api.organization.v1";

// request for create a new staff
message CreateStaffParams {
  // staff profile def
  models.organization.v1.CreateStaffDef staff_profile = 1;
  // working business def
  optional models.organization.v1.StaffWorkingLocationDef working_location = 2;
  // access control def
  optional models.organization.v1.StaffAccessControlDef access_control = 3;
  // notification def
  optional models.organization.v1.StaffNotificationDef notification_setting = 4;
  // payroll setting def
  optional models.organization.v1.StaffPayrollSettingDef payroll_setting = 5;
  // send invite link params
  optional models.organization.v1.SendInviteLinkParamsDef invite_link = 6;
  // staff login time
  optional models.organization.v1.StaffLoginTimeDef login_time = 7;
}

// response for create a new staff
message CreateStaffResult {
  // generated staff id
  int64 id = 1;
}

// request for get staff detail
message GetStaffFullDetailParams {
  // staff id
  int64 id = 1 [(validate.rules).int64.gt = 0];
}

// response for get staff detail
message GetStaffFullDetailResult {
  // staff id
  int64 id = 1;
  // staff info
  moego.models.organization.v1.StaffBasicView staff_profile = 2;
  // working location
  moego.models.organization.v1.StaffWorkingLocationDef working_location = 3;
  // access control
  moego.models.organization.v1.StaffAccessControlDef access_control = 4;
  // notification setting
  moego.models.organization.v1.StaffNotificationDef notification_setting = 5;
  // payroll setting
  moego.models.organization.v1.StaffPayrollSettingDef payroll_setting = 6;
  // staff email
  moego.models.organization.v1.StaffEmailDef staff_email = 7;
  // staff login time
  moego.models.organization.v1.StaffLoginTimeDef login_time = 8;
}

// request for update staff
message UpdateStaffParams {
  // staff id
  int64 id = 1 [(validate.rules).int64.gt = 0];
  // staff profile def
  optional models.organization.v1.UpdateStaffDef staff_profile = 2;
  // working business def
  optional models.organization.v1.StaffWorkingLocationDef working_location = 3;
  // access control def
  optional models.organization.v1.StaffAccessControlDef access_control = 4;
  // notification def
  optional models.organization.v1.StaffNotificationDef notification_setting = 5;
  // payroll setting def
  optional models.organization.v1.StaffPayrollSettingDef payroll_setting = 6;
  // send invite link params
  optional models.organization.v1.SendInviteLinkParamsDef invite_link = 7;
  // staff login time
  optional models.organization.v1.StaffLoginTimeDef login_time = 8;
}

// response for update staff
message UpdateStaffResult {
  // updated result
  bool success = 1;
}

// request for delete staff
message DeleteStaffParams {
  // staff id
  int64 id = 1 [(validate.rules).int64.gt = 0];
}

// response for delete staff
message DeleteStaffResult {
  // deleted result
  bool success = 1;
}

// request for query staff list by pagination
message QueryStaffListByPaginationParams {
  // business ids
  repeated int64 business_ids = 1 [(validate.rules).repeated = {
    items: {
      int64: {gt: 0}
    }
    unique: true
  }];
  // preserved 2-13 for future usage, for example: query by keyword, status, role, etc.
  // order by params
  repeated moego.utils.v2.OrderBy order_bys = 14;
  // pagination params
  moego.utils.v2.PaginationRequest pagination = 15;
}

// response for query staff list by pagination
message QueryStaffListByPaginationResult {
  // staff list
  repeated models.organization.v1.StaffInfoDef staffs = 1;
  // pagination
  moego.utils.v2.PaginationResponse pagination = 2;
}

// request for get staff list by business id
message GetAllWorkingLocationStaffsParams {}

// response for get working location staff list by business id
message GetAllWorkingLocationStaffsResult {
  // staff list
  repeated models.organization.v1.LocationStaffsDef location_staffs = 1;
  // total location count
  int64 total_location_count = 2;
  // total staff count
  int64 total_staff_count = 3;
}

// request for get staff list by business ids
message GetStaffsByWorkingLocationIdsParams {
  // business ids, if empty, will get all working location staffs
  repeated int64 business_ids = 3 [(validate.rules).repeated = {
    items: {
      int64: {gt: 0}
    }
    unique: true
  }];
}

// response for get working staff list by business ids
message GetStaffsByWorkingLocationIdsResult {
  // staff list
  repeated models.organization.v1.LocationStaffsDef location_staffs = 1;
  // total location count
  int64 total_location_count = 2;
  // total staff count
  int64 total_staff_count = 3;
}

// request for get clock in out staff list
message GetClockInOutStaffsParams {
  // clock in/out date
  string date = 1 [(validate.rules).string = {pattern: "^\\d{4}-\\d{2}-\\d{2}$"}];
  // staff ids
  repeated int64 staff_ids = 2 [(validate.rules).repeated = {
    items: {
      int64: {gt: 0}
    }
    unique: true
  }];
}

// response for get clock in out staff list
message GetClockInOutStaffsResult {
  // staff list
  repeated models.organization.v1.ClockInOutStaffDef clock_in_out_staffs = 1;
}

// request for get enterprise staff list by working location ids
message GetEnterpriseStaffsByWorkingLocationIdsParams {
  // business ids, if empty, will get all working location enterprise staffs
  repeated int64 business_ids = 3 [(validate.rules).repeated = {
    items: {
      int64: {gt: 0}
    }
    unique: true
  }];
}

// response for get working staff list by business ids
message GetEnterpriseStaffsByWorkingLocationIdsResult {
  // staff list
  repeated models.organization.v1.LocationStaffsDef location_staffs = 1;
}

// get staff login time params
message GetStaffLoginTimeParams {
  // staff id
  int64 staff_id = 1 [(validate.rules).int64.gt = 0];
}

// get staff login time result
message GetStaffLoginTimeResult {
  // staff login time
  models.organization.v1.StaffLoginTimeModel login_time = 1;
}

// update staff login time params
message UpdateStaffLoginTimeParams {
  // staff id
  int64 staff_id = 1 [(validate.rules).int64.gt = 0];
  // staff login time
  models.organization.v1.StaffLoginTimeDef login_time = 2;
}

// update staff login time result
message UpdateStaffLoginTimeResult {
  // is success
  bool success = 1;
}

// get recommended staff login time in company
message GetRecommendedStaffLoginTimeParams {}

// get recommended staff login time result
message GetRecommendedStaffLoginTimeResult {
  // recommended staff login time
  models.organization.v1.StaffLoginTimeDef login_time = 1;
}

// The params of list staff group by role
message ListStaffGroupByRoleParams {
  // business id
  int64 business_id = 1 [(validate.rules).int64.gt = 0];

  // staff working date
  google.type.Date date = 2 [(validate.rules).message = {required: true}];
}

// The result of list staff group by role
message ListStaffGroupByRoleResult {
  // staff group by role
  repeated RoleStaffGroup role_staff_groups = 1;

  // Working staff ids
  repeated int64 working_staff_ids = 2;

  // Role staff group
  message RoleStaffGroup {
    // Role name
    string role_name = 1;
    // Staffs
    repeated models.organization.v1.StaffBasicView staffs = 2;
  }
}

// staff service
service StaffService {
  // create a new staff
  rpc CreateStaff(CreateStaffParams) returns (CreateStaffResult);
  // get staff detail
  rpc GetStaffFullDetail(GetStaffFullDetailParams) returns (GetStaffFullDetailResult);
  // update staff
  rpc UpdateStaff(UpdateStaffParams) returns (UpdateStaffResult);
  // delete staff
  rpc DeleteStaff(DeleteStaffParams) returns (DeleteStaffResult);
  // query staff list by pagination
  rpc QueryStaffListByPagination(QueryStaffListByPaginationParams) returns (QueryStaffListByPaginationResult);
  // get all working location staffs
  rpc GetAllWorkingLocationStaffs(GetAllWorkingLocationStaffsParams) returns (GetAllWorkingLocationStaffsResult);
  // get staffs by working locations
  rpc GetStaffsByWorkingLocationIds(GetStaffsByWorkingLocationIdsParams) returns (GetStaffsByWorkingLocationIdsResult);
  // get clock in out staffs of current staff
  rpc GetClockInOutStaffs(GetClockInOutStaffsParams) returns (GetClockInOutStaffsResult);
  // get enterprise staffs by working locations
  rpc GetEnterpriseStaffsByWorkingLocationIds(GetEnterpriseStaffsByWorkingLocationIdsParams) returns (GetEnterpriseStaffsByWorkingLocationIdsResult);
  // get staff login time
  rpc GetStaffLoginTime(GetStaffLoginTimeParams) returns (GetStaffLoginTimeResult);
  // update staff login time
  rpc UpdateStaffLoginTime(UpdateStaffLoginTimeParams) returns (UpdateStaffLoginTimeResult);
  // get recommended staff login time
  rpc GetRecommendedStaffLoginTime(GetRecommendedStaffLoginTimeParams) returns (GetRecommendedStaffLoginTimeResult);

  // List staff group by role
  rpc ListStaffGroupByRole(ListStaffGroupByRoleParams) returns (ListStaffGroupByRoleResult);
}
