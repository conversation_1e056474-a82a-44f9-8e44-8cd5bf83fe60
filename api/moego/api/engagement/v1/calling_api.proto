syntax = "proto3";

package moego.api.engagement.v1;

import "google/protobuf/empty.proto";
import "google/type/interval.proto";
import "moego/models/engagement/v1/calling_log_defs.proto";
import "moego/models/engagement/v1/calling_log_models.proto";
import "moego/models/engagement/v1/voice_models.proto";
import "moego/utils/v2/pagination_messages.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/engagement/v1;engagementapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.api.engagement.v1";

// calling service
service CallingService {
  // create calling log
  rpc CreateCallingLog(CreateCallingLogParams) returns (CreateCallingLogResult);
  // get calling log
  rpc GetCallingLog(GetCallingLogParams) returns (GetCallingLogResult);
  // update calling log
  rpc UpdateCallingLog(UpdateCallingLogParams) returns (UpdateCallingLogResult);
  // delete calling log
  rpc DeleteCallingLog(DeleteCallingLogParams) returns (DeleteCallingLogResult);
  // list calling logs
  rpc ListCallingLogs(ListCallingLogsParams) returns (ListCallingLogsResult);
  // get calling log overview
  rpc GetCallingLogOverview(GetCallingLogOverviewParams) returns (GetCallingLogOverviewResult);
  // search customer
  rpc SearchCustomer(SearchCustomerParams) returns (SearchCustomerResult);
  // get token
  rpc GetToken(google.protobuf.Empty) returns (GetTokenResult) {
    option deprecated = true;
  }
  // get calling detail
  rpc GetCallingDetail(GetCallingDetailParams) returns (GetCallingDetailResult);
  // get customer dial mask
  rpc GetCustomerDialMask(GetCustomerDialMaskParams) returns (GetCustomerMaskResult);
  // get default local phone number for B-APP
  rpc GetDefaultLocalPhoneNumber(google.protobuf.Empty) returns (GetDefaultLocalPhoneNumberResult);
  // make call on B-APP
  rpc CallFromBApp(CallFromBAppParams) returns (google.protobuf.Empty);
  // get token
  rpc GetVoipToken(GetTokenParams) returns (GetTokenResult);
  // 是否存在多个相同号码处于 unresolved 状态的 activity log
  rpc ConfirmUnresolvedRange(ConfirmUnresolvedRangeParams) returns (ConfirmUnresolvedRangeResult);
  // 将一个或多个 activity log 标记为 resolved
  rpc MarkLogResolveStatus(MarkLogResolveStatusParams) returns (MarkLogResolveStatusResult);
}

// list calling logs params
message ListCallingLogsParams {
  // filter
  message Filter {
    // filter client ids
    repeated int64 client_ids = 1 [(validate.rules).repeated = {
      max_items: 500
      items: {
        int64: {gt: 0}
      }
    }];
    // filter customer ids
    repeated int64 customer_ids = 2 [(validate.rules).repeated = {
      max_items: 500
      items: {
        int64: {gt: 0}
      }
    }];
    // direction
    repeated models.engagement.v1.CallingDirection directions = 3;
    // statuses
    repeated models.engagement.v1.Status statuses = 4;
    // categories
    repeated models.engagement.v1.Category categories = 5;
    // record types
    repeated models.engagement.v1.RecordType record_types = 6;
    // business ids
    repeated int64 business_ids = 7;
    // init time period
    optional google.type.Interval init_time_period = 8;
    // is resolved
    optional bool is_resolved = 9;
  }
  // pagination
  moego.utils.v2.PaginationRequest pagination = 1 [(validate.rules).message = {required: true}];
  // order by
  optional models.engagement.v1.CallingLogOrderBy order_by = 2;
  // deprecated, replace with CallingLogFilter
  optional Filter filter = 3 [deprecated = true];
  // company id
  int64 company_id = 4;
  // filter
  optional models.engagement.v1.CallingLogFilter log_filter = 5;
}

// list calling logs result
message ListCallingLogsResult {
  // pagination
  moego.utils.v2.PaginationResponse pagination = 1;
  // calling logs
  repeated models.engagement.v1.CallingLogView calling_logs = 2;
}

// get calling log params
message CreateCallingLogParams {
  // create calling log
  models.engagement.v1.CreateCallingLogDef create_calling_log_def = 1;
}

// get calling log result
message CreateCallingLogResult {
  // calling logs
  models.engagement.v1.CallingLogView calling_log = 1;
}

// Update calling log params
message UpdateCallingLogParams {
  // id
  int64 id = 1;
  // calling log
  models.engagement.v1.UpdateCallingLogDef update_calling_log_def = 2;
}

// update calling
message UpdateCallingLogResult {
  // calling logs
  models.engagement.v1.CallingLogView calling_log = 1;
}

// delete calling log params
message DeleteCallingLogParams {
  // id
  int64 id = 1;
}

// delete calling log result
message DeleteCallingLogResult {
  // success
  bool success = 1;
}

// get calling log params
message GetCallingLogParams {
  // id
  int64 id = 1;
}

// get calling log result
message GetCallingLogResult {
  // calling logs
  models.engagement.v1.CallingLogView calling_log = 1;
}

// get calling log overview params
message GetCallingLogOverviewParams {
  // filter
  message Filter {
    // init time period
    optional google.type.Interval init_time_period = 2;
  }
  // filter
  Filter filter = 1;
  // company id
  int64 company_id = 2;
  // business id
  int64 business_id = 3;
}

// get calling log overview result
message GetCallingLogOverviewResult {
  // calling status summary
  message CallStatusSummary {
    // call received
    models.engagement.v1.IndicatorDef call_received = 1;
    // calling answer overview
    models.engagement.v1.IndicatorDef call_answered = 2;
    // calling unanswered overview
    models.engagement.v1.IndicatorDef call_unanswered = 3;
  }
  // calling status summary
  CallStatusSummary call_status_summary = 1;
  // calling log overview
  models.engagement.v1.IndicatorDef call_in_after_hour = 2;
  // calling log overview
  models.engagement.v1.IndicatorDef voicemail_received = 3;
  // calling log overview
  models.engagement.v1.IndicatorDef average_response_time = 4;
  // resolved
  models.engagement.v1.IndicatorDef resolved = 5;
  // unresolved
  models.engagement.v1.IndicatorDef unresolved = 6;
}

// search customer params
message SearchCustomerParams {
  // keyword search for customer name, pet name, or phone number or calling client name
  string keyword = 1;
}

// search customer result
message SearchCustomerResult {
  // calling client
  message CallingClient {
    // id
    int64 id = 1;
    // first name
    string name = 2;
  }
  // customer
  message Customer {
    // id
    int64 id = 1;
    // first name
    string first_name = 2;
    // last name
    string last_name = 3;
    // color code
    string color_code = 4;
    // avatar path
    string avatar_path = 5;
    // pets
    repeated models.engagement.v1.Pet pets = 6;
  }
  // calling client
  repeated CallingClient calling_clients = 1;
  // customer
  repeated Customer customers = 2;
}

// get token params
message GetTokenParams {
  // calling source
  optional models.engagement.v1.CallingSource calling_source = 1;
}

// get token result
message GetTokenResult {
  // the token def
  string token = 1;
  // permission list
  repeated models.engagement.v1.StaffPermission permissions = 2;
}

// get calling detail params
message GetCallingDetailParams {
  // the identifier
  oneof identifier {
    // the customer id
    int64 customer_id = 1;
    // client id
    int64 client_id = 2;
  }
}

// get calling detail result
message GetCallingDetailResult {
  // the customer
  moego.models.engagement.v1.Customer customer = 1;
  // the pets
  repeated moego.models.engagement.v1.Pet pets = 2;
  // direction, 没有 calling_sid 时固定为外呼
  moego.models.engagement.v1.CallingDirection direction = 3;
  // company id
  int64 company_id = 4;
  // business id
  int64 business_id = 5;
  // company name
  string company_name = 6;
  // business name
  string business_name = 7;
  // client id
  int64 client_id = 8;
  // client name
  string client_name = 9;
  // is recording
  bool is_recording = 10;
}

// get customer dial mask params
message GetCustomerDialMaskParams {
  // the identifier
  oneof identifier {
    // the customer id
    int64 customer_id = 1;
    // client id
    int64 client_id = 2;
  }
}

// get customer mask result
message GetCustomerMaskResult {
  // the mask
  string mask = 1;
}

// get default local phone number result
message GetDefaultLocalPhoneNumberResult {
  // the phone number
  string phone_number = 1;
}

// call from B-APP params
message CallFromBAppParams {
  // local phone number, 可能为空，为空服务端会读取店家的坐席配置
  optional string local_phone_number = 1;
  // call to identifier
  oneof target_identifier {
    // the customer id
    int64 customer_id = 2;
    // the client id
    int64 client_id = 3;
    // the phone number
    string phone_number = 4;
  }
}

// ExistMultipleUnresolvedLogRequest
message ConfirmUnresolvedRangeParams {
  // 待确认的 unresolved log 过滤范围
  models.engagement.v1.CallingLogFilter filter = 3;
}

// ExistMultipleUnresolvedLogResponse
message ConfirmUnresolvedRangeResult {
  // 范围中 unresolved 状态的 log 数目
  int32 unresolved_count = 1;
}

// MarkLogAsResolvedRequest
message MarkLogResolveStatusParams {
  // 待标记为 resolved 的 log 过滤范围
  models.engagement.v1.CallingLogFilter filter = 3;
  // is resolved
  bool is_resolved = 4;
}

// MarkLogAsResolvedResponse
message MarkLogResolveStatusResult {}
