syntax = "proto3";

package moego.api.user_profile.v1;

import "moego/models/user_profile/v1/user_profile_models.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/user_profile/v1;userprofileapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.api.user_profile.v1";

// GetUserProfileParams 获取用户画像参数
message GetUserProfilesParams {
  // 用户
  repeated moego.models.user_profile.v1.User users = 1 [(validate.rules).repeated = {
    min_items: 1
    max_items: 500
  }];
}

// GetUserProfileResult 获取用户画像结果
message GetUserProfilesResult {
  // 用户画像
  repeated models.user_profile.v1.UserProfile user_profiles = 1;
}

// user profile
service UserProfileService {
  // GetUserProfile get user profile by user id
  rpc GetUserProfile(GetUserProfilesParams) returns (GetUserProfilesResult);
}
