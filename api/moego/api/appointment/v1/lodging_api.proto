// @since 2024-01-15 16:36:40
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.api.appointment.v1;

import "moego/models/appointment/v1/lodging_enums.proto";
import "moego/models/appointment/v1/lodging_models.proto";
import "moego/models/appointment/v1/pet_detail_enums.proto";
import "moego/models/customer/v1/customer_pet_enums.proto";
import "moego/models/offering/v1/lodging_enum.proto";
import "moego/models/offering/v1/lodging_unit_models.proto";
import "moego/models/offering/v1/service_enum.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/appointment/v1;appointmentapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.api.appointment.v1";

// get lodging calendar params
message GetLodgingCalendarViewParams {
  // start date
  string start_date = 1 [(validate.rules).string = {pattern: "^\\d{4}-\\d{2}-\\d{2}$"}];
  // end date
  string end_date = 2 [(validate.rules).string = {pattern: "^\\d{4}-\\d{2}-\\d{2}$"}];
  // business id
  int64 business_id = 3 [(validate.rules).int64 = {gt: 0}];
  // lodging status
  moego.models.appointment.v1.LodgingStatus lodging_status = 4 [(validate.rules).enum = {defined_only: true}];
  // lodging type
  repeated int64 lodging_types = 5;
}

// get lodging calendar result
message GetLodgingCalendarViewResult {
  // lodging calendar view
  repeated moego.models.appointment.v1.LodgingTicket lodging_tickets = 1;
  // all lodging units
  repeated moego.models.offering.v1.LodgingUnitView lodging_units = 2;
  // all pet detail infos
  repeated ServiceDetail pet_details = 3;

  // service detail
  message ServiceDetail {
    // pet detail id
    int64 id = 1;
    // pet id
    int64 pet_id = 2;
    // service start date, in yyyy-MM-dd format
    optional string start_date = 3;
    // service end date, in yyyy-MM-dd format
    optional string end_date = 4;
    // specific dates, yyyy-MM-dd
    repeated string specific_dates = 5;
    // service item type, it includes boarding, daycare
    models.offering.v1.ServiceItemType service_item_type = 6;
    // pet detail date type
    optional models.appointment.v1.PetDetailDateType date_type = 7;
  }
}

// get lodging calendar params
message GetLodgingCalendarViewV2Params {
  // start date
  string start_date = 1 [(validate.rules).string = {pattern: "^\\d{4}-\\d{2}-\\d{2}$"}];
  // end date
  string end_date = 2 [(validate.rules).string = {pattern: "^\\d{4}-\\d{2}-\\d{2}$"}];
  // business id
  int64 business_id = 3 [(validate.rules).int64 = {gt: 0}];
  // lodging status, deprecated, use lodging_occupied_status instead
  optional moego.models.appointment.v1.LodgingStatus lodging_status = 4 [
    (validate.rules).enum = {defined_only: true},
    deprecated = true
  ];
  // lodging type
  repeated int64 lodging_type_ids = 5 [(validate.rules).repeated = {
    max_items: 100
    unique: true
    items: {
      int64: {gte: 0}
    }
  }];
  // lodging occupied status, if not set, return all lodging units
  // vacant: only return lodging units that are vacant in all days
  // fully_occupied: only return lodging units that are occupied in all days
  // partially_occupied: return lodging units that are occupied in some days
  optional moego.models.appointment.v1.LodgingOccupiedStatus lodging_occupied_status = 6 [(validate.rules).enum = {defined_only: true}];
}

// get lodging calendar result
message GetLodgingCalendarViewV2Result {
  // lodging type view list
  repeated LodgingTypeView lodging_types = 1;
  // pet info view list
  repeated PetView pets = 2;
  // pet detail view list
  repeated PetDetailView pet_details = 3;
  // max pet num for all lodging types
  int32 max_pet_total_num = 4;
  // exist pet num during the period for all lodging types
  map<string, ExistPetCountView> date_to_exist_pet_count = 5;
  // pet evaluation detail view list
  repeated PetEvaluationView pet_evaluations = 6;

  /*
   * lodging type list model
   */
  message LodgingTypeView {
    // id of the lodging type
    int64 id = 1;
    // name of the lodging type
    string name = 2;
    // max pet number of this lodging type
    int32 max_pet_num = 3;
    // exist pet num during the period for all lodging unit in this lodging type.
    // more accurately, it should be named date_to_exist_pet_count
    map<string, int32> exist_pet_per_day = 4;
    // max pet num for all lodging unit in this lodging type
    int32 max_pet_total_num = 5;
    // lodging unit view list
    repeated LodgingUnitView lodging_units = 6;
    // available for all pet size
    bool all_pet_sizes = 7 [deprecated = true];
    // available pet size (only if is_available_for_all_pet_size is false)
    repeated int64 pet_size_ids = 8;
    // lodging unit type in this lodging type
    moego.models.offering.v1.LodgingUnitType lodging_unit_type = 9;
    // whether the lodging type is available for all pet size
    bool pet_size_filter = 10;
    // total capacity of lodging type, calculate based on lodging type(room or area)
    int32 total_capacity = 11;
    // occupied capacity during the period for all lodging unit in this lodging type.
    // Calculate based on lodging type(room or area)
    map<string, int32> occupied_capacity_per_day = 12;
  }

  /*
   * lodging unit list model
   */
  message LodgingUnitView {
    // id of the lodging unit
    int64 id = 1;
    // name of the lodging unit
    string name = 2;
    // lodging ticket
    repeated LodgingTicket lodging_tickets = 3;
  }

  // lodging ticket
  message LodgingTicket {
    // appointment id
    int64 appointment_id = 1;
    // customer id
    int64 customer_id = 2;
    // color code
    string color_code = 3;
    // ticket start date
    string start_date = 4;
    // ticket end date
    string end_date = 5;
    // ticket start time
    int32 start_time = 6;
    // ticket end time
    int32 end_time = 7;
    // lodging usage
    repeated LodgingUsage usages = 8;
  }

  // lodging usage
  message LodgingUsage {
    // pet id
    int64 pet_id = 1;
    // pet detail id list
    repeated int64 pet_detail_id = 2;
    // pet evaluation detail id list
    repeated int64 pet_evaluation_id = 3;
  }

  // pet detail
  message PetDetailView {
    // pet detail id
    int64 id = 1;
    // pet detail service start date, in yyyy-MM-dd format
    string start_date = 2;
    // service end date, in yyyy-MM-dd format
    string end_date = 3;
    // specific dates, yyyy-MM-dd
    repeated string specific_dates = 4;
    // service item type, it includes boarding, daycare
    models.offering.v1.ServiceItemType service_item_type = 5;
    // pet detail date type
    optional models.appointment.v1.PetDetailDateType date_type = 6;
    // is split lodging
    bool is_split_lodging = 9;
  }

  // pet evaluation detail
  message PetEvaluationView {
    // pet evaluation detail id
    int64 id = 1;
    // pet evaluation detail service start date, in yyyy-MM-dd format
    string start_date = 2;
    // pet evaluation detail service end date, in yyyy-MM-dd format
    string end_date = 3;
  }

  // pet detail
  message PetView {
    // id
    int64 id = 1;
    // pet name
    string pet_name = 4;
    // avatar path
    string avatar_path = 5;
    // pet type id
    moego.models.customer.v1.PetType pet_type = 6;
  }

  // pet count for service type per day
  message ExistPetCountView {
    // pet cnt for all service type
    int32 pet_count_all = 1;
    // pet cnt for boarding
    int32 pet_count_boarding = 2;
    // pet cnt for daycare
    int32 pet_count_daycare = 3;
    // pet cnt for evaluation
    int32 pet_count_evaluation = 4;
    // pet cnt with lodging
    int32 pet_count_with_lodging = 5;
  }
}

// get lodging list params
message GetLodgingListParams {
  // business id
  int64 business_id = 1 [(validate.rules).int64 = {gt: 0}];
  // start date
  string start_date = 2 [(validate.rules).string = {pattern: "^\\d{4}-\\d{2}-\\d{2}$"}];
  // end date
  string end_date = 3 [(validate.rules).string = {pattern: "^\\d{4}-\\d{2}-\\d{2}$"}];
  // service id
  optional int64 service_id = 4 [(validate.rules).int64 = {gt: 0}];
  // is applicable
  optional bool is_applicable = 5;
  // pet id
  optional int64 pet_id = 6 [(validate.rules).int64 = {gt: 0}];
  // evaluation service id
  optional int64 evaluation_service_id = 7 [(validate.rules).int64 = {gt: 0}];
}

// get lodging list result
message GetLodgingListResult {
  // lodging list
  repeated moego.models.appointment.v1.LodgingListView lodging_unit_list = 1;
}

// lodging unit transfer params
message LodgingTransferParams {
  // business id
  int64 business_id = 1 [(validate.rules).int64 = {gt: 0}];
  // lodging id from
  int64 lodging_id_from = 2 [(validate.rules).int64 = {gt: 0}];
  // lodging id to
  int64 lodging_id_to = 3 [(validate.rules).int64 = {gt: 0}];
}

// lodging unit transfer result
message LodgingTransferResult {}

// check lodging if in use params
message LodgingInUseCheckParams {
  // business id
  int64 business_id = 1 [(validate.rules).int64 = {gt: 0}];
  // lodging id
  int64 lodging_id = 2 [(validate.rules).int64 = {gt: 0}];
}

// check lodging if in use result
message LodgingInUseCheckResult {
  // upcoming appointments
  int32 upcoming_appointments = 1;
}

// get last lodging params
message GetLastLodgingParams {
  // business id
  int64 business_id = 1 [(validate.rules).int64 = {gt: 0}];
  // pet id
  int64 pet_id = 2 [(validate.rules).int64 = {gt: 0}];
  // service id
  int64 service_id = 3 [(validate.rules).int64 = {gt: 0}];
  // customer id
  int64 customer_id = 4 [(validate.rules).int64 = {gt: 0}];
}

// get last lodging result
message GetLastLodgingResult {
  // last lodging
  optional moego.models.appointment.v1.LodgingListView last_lodging = 1;
}

// the lodging service
service LodgingService {
  // get lodging
  rpc GetLodgingCalendarView(GetLodgingCalendarViewParams) returns (GetLodgingCalendarViewResult);
  // get lodging calendar view v2
  rpc GetLodgingCalendarViewV2(GetLodgingCalendarViewV2Params) returns (GetLodgingCalendarViewV2Result);
  // get lodging list
  rpc GetLodgingList(GetLodgingListParams) returns (GetLodgingListResult);
  // lodging unit transfer
  rpc LodgingTransfer(LodgingTransferParams) returns (LodgingTransferResult);
  // check lodging if in use
  rpc LodgingInUseCheck(LodgingInUseCheckParams) returns (LodgingInUseCheckResult);
  // get last lodging
  rpc GetLastLodging(GetLastLodgingParams) returns (GetLastLodgingResult);
}
