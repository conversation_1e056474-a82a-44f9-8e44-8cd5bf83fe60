// @since 2024-07-30 11:14:10
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.models.offering.v1;

import "google/protobuf/timestamp.proto";
import "moego/models/offering/v1/pricing_rule_defs.proto";
import "moego/models/offering/v1/pricing_rule_enums.proto";
import "moego/models/offering/v1/service_enum.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1;offeringpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.offering.v1";

// The PricingRule model
message PricingRuleModel {
  // the unique id
  int64 id = 1;

  // company id
  int64 company_id = 2;
  // apply service item type
  moego.models.offering.v1.ServiceItemType service_item_type = 3;
  // apply service type
  moego.models.offering.v1.ServiceType service_type = 4;

  // rule name
  string rule_name = 5;

  // apply to all service
  bool is_all_service_applicable = 6;
  // selected service ids, only effective when all_service is false
  repeated int64 selected_services = 7;

  // rule configuration
  moego.models.offering.v1.PricingRuleConfigurationDef rule_configuration = 8;
  // active, true means active, false means inactive
  bool is_active = 9;

  // updated staff id
  int64 updated_by = 12;
  // the create time
  google.protobuf.Timestamp created_at = 13;
  // the update time
  google.protobuf.Timestamp updated_at = 14;
  // the delete time, non-null means is deleted
  optional google.protobuf.Timestamp deleted_at = 15;
  // rule group type
  optional moego.models.offering.v1.RuleGroupType rule_group_type = 16;
  // rule apply choice type
  optional moego.models.offering.v1.RuleApplyChoiceType rule_apply_choice_type = 17;
}
