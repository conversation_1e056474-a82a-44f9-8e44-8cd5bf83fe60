syntax = "proto3";

package moego.models.business_customer.v1;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/business_customer/v1;businesscustomerpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.business_customer.v1";

// pet coat type model
message BusinessPetCoatTypeModel {
  // coat type id
  int64 id = 1;

  // coat type name
  string name = 2;

  // coat type sort. The larger the sort number, the higher the priority.
  int32 sort = 3;

  // if the coat type is deleted
  bool deleted = 4;
}

// pet coat type name view
message BusinessPetCoatTypeNameView {
  // coat type name
  string name = 2;
}
