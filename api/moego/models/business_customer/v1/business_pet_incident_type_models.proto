syntax = "proto3";

package moego.models.business_customer.v1;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/business_customer/v1;businesscustomerpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.business_customer.v1";

// pet incident type model
message BusinessPetIncidentTypeModel {
  // incident type id
  int64 id = 1;

  // incident type name
  string name = 2;

  // incident type sort. The larger the sort number, the higher the priority.
  int32 sort = 3;

  // if the pet incident type is deleted
  bool is_deleted = 4;
}
