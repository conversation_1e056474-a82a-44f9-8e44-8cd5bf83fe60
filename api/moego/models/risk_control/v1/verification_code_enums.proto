syntax = "proto3";

package moego.models.risk_control.v1;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/risk_control/v1;riskcontrolpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.risk_control.v1";

// verification code scenario
enum VerificationCodeScenario {
  // unspecified
  VERIFICATION_CODE_SCENARIO_UNSPECIFIED = 0;
  // business web
  VERIFICATION_CODE_SCENARIO_BUSINESS_WEB = 1;
  // business app
  VERIFICATION_CODE_SCENARIO_BUSINESS_APP = 2;
  // online booking
  VERIFICATION_CODE_SCENARIO_ONLINE_BOOKING = 3;
  // pet parent portal
  VERIFICATION_CODE_SCENARIO_PET_PARENT_PORTAL = 4;
  // pet parent app
  VERIFICATION_CODE_SCENARIO_PET_PARENT_APP = 5;
  // branded app
  VERIFICATION_CODE_SCENARIO_BRANDED_APP = 6;
}

// verification code method
enum VerificationCodeMethod {
  // unspecified
  VERIFICATION_CODE_METHOD_UNSPECIFIED = 0;
  // register
  VERIFICATION_CODE_METHOD_REGISTER = 1;
  // login
  VERIFICATION_CODE_METHOD_LOGIN = 2;
  // reset password
  VERIFICATION_CODE_METHOD_RESET_PASSWORD = 3;
  // change phone number, applicable to those with mobile phone numbers
  VERIFICATION_CODE_METHOD_CHANGE_PHONE_NUMBER = 4;
  // change email
  VERIFICATION_CODE_METHOD_CHANGE_EMAIL = 5;
  // add phone number, applicable to those who don’t have a mobile phone number
  VERIFICATION_CODE_METHOD_ADD_PHONE_NUMBER = 6;
  // sign up or login in
  VERIFICATION_CODE_METHOD_SIGN_UP_OR_LOGIN = 7;
}
