// @since 2-24-01-12
// <AUTHOR> <<EMAIL>>
syntax = "proto3";

package moego.models.pay_ops.v1;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/pay_ops/v1;payopspb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.pay_ops.v1";

// Represents the Stripe account information.
message StripeAccount {
  // Business information about the account.
  BusinessProfile business_profile = 1;
  // The business type (company, government_entity, individual, non_profit).
  string business_type = 2;
  // A hash containing the set of capabilities requested for this account and their states.
  Capabilities capabilities = 3;
  // Whether the account can create live charges.
  bool charges_enabled = 4;
  // Company information associated with the account.
  Company company = 5;
  // Controller information associated with the account.
  Controller controller = 6;
  // The account's country.
  string country = 7;
  // Time at which the account was connected. Measured in seconds since the Unix epoch.
  int64 created = 8;
  // Three-letter ISO currency code representing the default currency for the account.
  string default_currency = 9;
  // Always true for a deleted object.
  bool deleted = 10;
  // Whether account details have been submitted.
  bool details_submitted = 11;
  // An email address associated with the account.
  string email = 12;
  // External accounts (bank accounts and debit cards) currently attached to this account.
  repeated ExternalAccount external_accounts = 13;
  // Future requirements for the account.
  FutureRequirements future_requirements = 14;
  // Unique identifier for the object.
  string id = 15;
  // Information about a person associated with a Stripe account.
  repeated StripePerson individual = 16;
  // Metadata associated with the account.
  map<string, string> metadata = 17;
  // Object's type. Equal to "account".
  string object = 18;
  // Whether Stripe can send payouts to this account.
  bool payouts_enabled = 19;
  // Requirements for the account.
  Requirements requirements = 20;
  // Settings for customizing how the account functions within Stripe.
  Settings settings = 21; // Define Settings message as needed.
  // Terms of Service acceptance information.
  TosAcceptance tos_acceptance = 22; // Define TosAcceptance message as needed.
  // The Stripe account type (standard, express, custom).
  string type = 23;

  // external account
  message ExternalAccount {
    // map entry list
    map<string, string> data = 1;
    // size
    int32 size = 2;
  }

  // Represents the business profile information.
  message BusinessProfile {
    // The merchant category code for the account.
    string mcc = 1;
    // The customer-facing business name.
    string name = 2;
    // Internal-only description of the product sold or service provided by the business.
    string product_description = 3;
    // A publicly available mailing address for sending support issues to.
    StripeAddress support_address = 4;
    // A publicly available email address for sending support issues to.
    string support_email = 5;
    // A publicly available phone number to call with support issues.
    string support_phone = 6;
    // A publicly available website for handling support issues.
    string support_url = 7;
    // The business's publicly available website.
    string url = 8;
  }

  // Represents various payment capabilities of an account.
  message Capabilities {
    // Status of Canadian pre-authorized debits payments.
    string acss_debit_payments = 1;
    // Status of Affirm payments.
    string affirm_payments = 2;
    // Status of Afterpay Clearpay payments.
    string afterpay_clearpay_payments = 3;
    // Status of BECS Direct Debit (AU) payments.
    string au_becs_debit_payments = 4;
    // Status of Bacs Direct Debits payments.
    string bacs_debit_payments = 5;
    // Status of Bancontact payments.
    string bancontact_payments = 6;
    // Status of customer_balance payments.
    string bank_transfer_payments = 7;
    // Status of blik payments.
    string blik_payments = 8;
    // Status of boleto payments.
    string boleto_payments = 9;
    // Status of card issuing capability.
    string card_issuing = 10;
    // Status of card payments.
    string card_payments = 11;
    // Status of Cartes Bancaires payments.
    string cartes_bancaires_payments = 12;
    // Status of EPS payments.
    string eps_payments = 13;
    // Status of FPX payments.
    string fpx_payments = 14;
    // Status of giropay payments.
    string giropay_payments = 15;
    // Status of GrabPay payments.
    string grabpay_payments = 16;
    // Status of iDEAL payments.
    string ideal_payments = 17;
    // Status of JCB payments (Japan only).
    string jcb_payments = 18;
    // Status of Klarna payments.
    string klarna_payments = 19;
    // Status of konbini payments.
    string konbini_payments = 20;
    // Status of legacy payments.
    string legacy_payments = 21;
    // Status of link_payments capability.
    string link_payments = 22;
    // Status of OXXO payments.
    string oxxo_payments = 23;
    // Status of P24 payments.
    string p24_payments = 24;
    // Status of paynow payments.
    string paynow_payments = 25;
    // Status of promptpay payments.
    string promptpay_payments = 26;
    // Status of SEPA Direct Debits payments.
    string sepa_debit_payments = 27;
    // Status of Sofort payments.
    string sofort_payments = 28;
    // Status of tax reporting 1099-K (US).
    string tax_reporting_us_1099_k = 29;
    // Status of tax reporting 1099-MISC (US).
    string tax_reporting_us_1099_misc = 30;
    // Status of transfers capability.
    string transfers = 31;
    // Status of banking capability.
    string treasury = 32;
    // Status of US bank account ACH payments.
    string us_bank_account_ach_payments = 33;
  }

  // The main company information.
  message Company {
    // Address of the company.
    StripeAddress address = 1;
    // The Kana variation of the company's primary address (Japan only).
    AddressKana address_kana = 2;
    // The Kanji variation of the company's primary address (Japan only).
    AddressKanji address_kanji = 3;
    // Indicates if the company's directors have been provided.
    bool directors_provided = 4;
    // Indicates if the company's executives have been provided.
    bool executives_provided = 5;
    // Legal name of the company.
    string name = 6;
    // The Kana variation of the company's legal name (Japan only).
    string name_kana = 7;
    // The Kanji variation of the company's legal name (Japan only).
    string name_kanji = 8;
    // Indicates if the company's owners have been provided.
    bool owners_provided = 9;
    // Attestation regarding the beneficial owner information.
    OwnershipDeclaration ownership_declaration = 10;
    // Phone number of the company.
    string phone = 11;
    // Legal structure of the company.
    string structure = 12;
    // Indicates if the company's business ID number was provided.
    bool tax_id_provided = 13;
    // Jurisdiction for the tax ID (Germany-based companies only).
    string tax_id_registrar = 14;
    // Indicates if the company's VAT number was provided.
    bool vat_id_provided = 15;
    // Verification state of the company.
    Verification verification = 16;
  }

  // Address in Kana format.
  message AddressKana {
    // City/Ward.
    string city = 1;
    // Two-letter country code.
    string country = 2;
    // Block/Building number.
    string line1 = 3;
    // Building details.
    string line2 = 4;
    // ZIP or postal code.
    string postal_code = 5;
    // Prefecture.
    string state = 6;
    // Town/cho-me.
    string town = 7;
  }

  // Address in Kanji format.
  message AddressKanji {
    // City/Ward.
    string city = 1;
    // Two-letter country code.
    string country = 2;
    // Block/Building number.
    string line1 = 3;
    // Building details.
    string line2 = 4;
    // ZIP or postal code.
    string postal_code = 5;
    // Prefecture.
    string state = 6;
    // Town/cho-me.
    string town = 7;
  }

  // Declaration of ownership.
  message OwnershipDeclaration {
    // Timestamp of the beneficial owner attestation.
    int64 date = 1;
    // IP address from which the attestation was made.
    string ip = 2;
    // User-agent of the browser used for attestation.
    string user_agent = 3;
  }

  // Verification information of the company.
  message Verification {
    // Document for verification.
    Document document = 1;

    // Document details for verification.
    message Document {
      // Back of the document.
      string back = 1;
      // Description of the verification state.
      string details = 2;
      // Code specifying the verification state.
      string details_code = 3;
      // Front of the document.
      string front = 4;
    }
  }

  // Represents the controller information of an account.
  message Controller {
    // Indicates if the Connect application controls the account.
    bool is_controller = 1;
    // Type of the controller, either 'application' or 'account'.
    string type = 2;
  }

  // future requirements
  message FutureRequirements {
    // Fields that are due and can be satisfied by providing the corresponding alternative fields instead.
    repeated Alternative alternatives = 1;

    // Date on which future_requirements merges with the main requirements hash and future_requirements becomes empty.
    int64 current_deadline = 2;

    // Fields that need to be collected to keep the account enabled.
    repeated string currently_due = 3;

    // This is typed as a string for consistency with requirements.disabled_reason,
    // but it safe to assume future_requirements.disabled_reason is empty because fields in future_requirements will never disable the account.
    string disabled_reason = 4;

    // Fields that are currently_due and need to be collected again because validation or verification failed.
    repeated Errors errors = 5;

    // Fields that need to be collected assuming all volume thresholds are reached.
    repeated string eventually_due = 6;

    // Fields that weren't collected by requirements.current_deadline.
    repeated string past_due = 7;

    // Fields that may become required depending on the results of verification or review.
    repeated string pending_verification = 8;

    // Alternative fields structure.
    message Alternative {
      // Fields that can be provided to satisfy all fields in original_fields_due.
      repeated string alternative_fields_due = 1;

      // Fields that are due and can be satisfied by providing all fields in alternative_fields_due.
      repeated string original_fields_due = 2;
    }

    //  errors structure.
    message Errors {
      // The code for the type of error.
      string code = 1;

      // An informative message that indicates the error type and provides additional details about the error.
      string reason = 2;

      // The specific requirement related to this error.
      string requirement = 3;
    }
  }

  // requirements
  message Requirements {
    // Fields that are due and can be satisfied by providing the corresponding alternative fields instead.
    repeated Alternative alternatives = 1;

    // Date by which the fields in currently_due must be collected to keep the account enabled.
    // These fields may disable the account sooner if the next threshold is reached before they are collected.
    int64 current_deadline = 2;

    // Fields that need to be collected to keep the account enabled. If not collected by current_deadline,
    // these fields appear in past_due as well, and the account is disabled.
    repeated string currently_due = 3;

    // If the account is disabled, this string describes why.
    // Can be requirements.past_due, requirements.pending_verification, listed, platform_paused,
    // rejected.fraud, rejected.listed, rejected.terms_of_service, rejected.other, under_review, or other.
    string disabled_reason = 4;

    // Fields that are currently_due and need to be collected again because validation or verification failed.
    repeated Errors errors = 5;

    // Fields that need to be collected assuming all volume thresholds are reached.
    // As they become required, they appear in currently_due as well, and current_deadline becomes set.
    repeated string eventually_due = 6;

    // Fields that weren't collected by current_deadline.
    // These fields need to be collected to enable the account.
    repeated string past_due = 7;

    // Fields that may become required depending on the results of verification or review.
    // Will be an empty array unless an asynchronous verification is pending.
    // If verification fails, these fields move to eventually_due, currently_due, or past_due.
    repeated string pending_verification = 8;

    // Alternative fields structure.
    message Alternative {
      // Fields that can be provided to satisfy all fields in original_fields_due.
      repeated string alternative_fields_due = 1;

      // Fields that are due and can be satisfied by providing all fields in alternative_fields_due.
      repeated string original_fields_due = 2;
    }

    // errors structure
    message Errors {
      // The code for the type of error.
      string code = 1;

      // An informative message that indicates the error type and provides additional details about the error.
      string reason = 2;

      // The specific requirement related to this error.
      string requirement = 3;
    }
  }

  // settings
  message Settings {
    // bacs_debit_payments
    BacsDebitPayments bacs_debit_payments = 1;
    // branding
    Branding branding = 2;
    // card_issuing
    CardIssuing card_issuing = 3;
    // card_payments
    CardPayments card_payments = 4;
    // dashboard
    Dashboard dashboard = 5;
    // payments
    Payments payments = 6;
    // payouts
    Payouts payouts = 7;
    // sepa_debit_payments
    SepaDebitPayments sepa_debit_payments = 8;
    // treasury
    Treasury treasury = 9;

    // bacs debit payments
    message BacsDebitPayments {
      // The Bacs Direct Debit Display Name for this account. For payments made with Bacs Direct Debit, this will appear on the mandate, and as the statement descriptor.
      string display_name = 1;
    }

    // branding
    message Branding {
      // An icon for the account. Must be square and at least 128px x 128px.
      string icon = 1;
      // A logo for the account that will be used in Checkout instead of the icon and without the account's name next to it if provided. Must be at least 128px x 128px.
      string logo = 2;
      // A CSS hex color value representing the primary branding color for this account.
      string primary_color = 3;
      // A CSS hex color value representing the secondary branding color for this account.
      string secondary_color = 4;
    }

    // card issuing
    message CardIssuing {
      // tos acceptance
      TosAcceptance tos_acceptance = 1;

      // tos acceptance
      message TosAcceptance {
        // The Unix timestamp marking when the account representative accepted the service agreement.
        int64 date = 1;
        // The IP address from which the account representative accepted the service agreement.
        string ip = 2;
        // The user agent of the browser from which the account representative accepted the service agreement.
        string user_agent = 3;
      }
    }

    // card payments
    message CardPayments {
      // decline on message
      DeclineOn decline_on = 1;
      // The default text that appears on credit card statements when a charge is made.
      // This field prefixes any dynamic statement_descriptor specified on the charge.
      string statement_descriptor_prefix = 2;
      // The Kana variation of the default text that appears on credit card statements when a charge is made (Japan only).
      string statement_descriptor_prefix_kana = 3;
      // The Kanji variation of the default text that appears on credit card statements when a charge is made (Japan only).
      string statement_descriptor_prefix_kanji = 4;

      // decline on message
      message DeclineOn {
        // Whether Stripe automatically declines charges with an incorrect ZIP or postal code.
        bool avs_failure = 1;
        // Whether Stripe automatically declines charges with an incorrect CVC.
        bool cvc_failure = 2;
      }
    }

    // dashboard
    message Dashboard {
      // The display name for this account. This is used on the Stripe Dashboard to differentiate between accounts.
      string display_name = 1;
      // The timezone used in the Stripe Dashboard for this account. A list of possible time zone values is maintained at the IANA Time Zone Database.
      string timezone = 2;
    }

    // payments
    message Payments {
      // The default text that appears on credit card statements when a charge is made. This field
      // prefixes any dynamic statement_descriptor specified on the charge.
      string statement_descriptor = 1;

      // The Kana variation of the default text that appears on credit card statements when a charge
      // is made (Japan only).
      string statement_descriptor_kana = 2;

      // The Kanji variation of the default text that appears on credit card statements when a charge
      // is made (Japan only).
      string statement_descriptor_kanji = 3;

      // The Kana variation of the default text that appears on credit card statements when a charge
      // is made (Japan only). This field prefixes any dynamic statement_descriptor_suffix_kana
      // specified on the charge. statement_descriptor_prefix_kana is useful for maximizing descriptor
      // space for the dynamic portion.
      string statement_descriptor_prefix_kana = 4;

      // The Kanji variation of the default text that appears on credit card statements when a charge
      // is made (Japan only). This field prefixes any dynamic statement_descriptor_suffix_kanji
      // specified on the charge. statement_descriptor_prefix_kanji is useful for maximizing descriptor
      // space for the dynamic portion.
      string statement_descriptor_prefix_kanji = 5;
    }

    // payouts
    message Payouts {
      // A Boolean indicating if Stripe should try to reclaim negative balances from an attached bank account.
      // Default value is false for Custom accounts, otherwise true.
      bool debit_negative_balances = 1;
      // The schedule for payouts.
      Schedule schedule = 2;
      // The text that appears on the bank account statement for payouts.
      // If not set, this defaults to the platform's bank descriptor as set in the Dashboard.
      string statement_descriptor = 3;

      // The schedule for payouts.
      message Schedule {
        // The number of days charges for the account will be held before being paid out.
        int64 delay_days = 1;
        // How frequently funds will be paid out. One of manual (payouts only created via API call), daily, weekly, or monthly.
        string interval = 2;
        // The day of the month funds will be paid out. Only shown if interval is monthly.
        // Payouts scheduled between the 29th and 31st of the month are sent on the last day of shorter months.
        int64 monthly_anchor = 3;
        // The day of the week funds will be paid out, of the style 'monday', 'tuesday', etc.
        // Only shown if interval is weekly.
        string weekly_anchor = 4;
      }
    }

    // sepa debit payments
    message SepaDebitPayments {
      // SEPA creditor identifier that identifies the company making the payment.
      string creditor_id = 1;
    }

    // treasury
    message Treasury {
      // Acceptance terms of service.
      TosAcceptance tos_acceptance = 1;

      // Acceptance terms of service.
      message TosAcceptance {
        // The Unix timestamp marking when the account representative accepted the service agreement.
        int64 date = 1;

        // The IP address from which the account representative accepted the service agreement.
        string ip = 2;

        // The user agent of the browser from which the account representative accepted the service agreement.
        string user_agent = 3;
      }
    }
  }

  // tos acceptance
  message TosAcceptance {
    // The Unix timestamp marking when the account representative accepted their service agreement.
    int64 date = 1;

    // The IP address from which the account representative accepted their service agreement.
    string ip = 2;

    // The user's service agreement type.
    string service_agreement = 3;

    // The user agent of the browser from which the account representative accepted their service agreement.
    string user_agent = 4;
  }
}

// Represents the detailed address information for Stripe.
message StripeAddress {
  // City, district, suburb, town, or village.
  string city = 1;
  // Two-letter country code.
  string country = 2;
  // Address line 1 (e.g., street, PO Box, or company name).
  string line1 = 3;
  // Address line 2 (e.g., apartment, suite, unit, or building).
  string line2 = 4;
  // ZIP or postal code.
  string postal_code = 5;
  // State, county, province, or region.
  string state = 6;
}

// Represents the detailed information of a Stripe person.
message StripePerson {
  // The account the person is associated with.
  string account = 1;
  // address
  StripeAddress address = 2;
  // The Kana variation of the person's address (Japan only).
  AddressKana address_kana = 3;
  // The Kanji variation of the person's address (Japan only).
  AddressKanji address_kanji = 4;
  // Time at which the object was created. Measured in seconds since the Unix epoch.
  int64 created = 5;
  // Always true for a deleted object.
  bool deleted = 6;
  // dob
  Dob dob = 7;
  // The person's email address.
  string email = 8;
  // The person's first name.
  string first_name = 9;
  // The Kana variation of the person's first name (Japan only).
  string first_name_kana = 10;
  // The Kanji variation of the person's first name (Japan only).
  string first_name_kanji = 11;
  // A list of alternate names or aliases that the person is known by.
  repeated string full_name_aliases = 12;
  // Information about the upcoming new requirements for this person.
  FutureRequirements future_requirements = 13;
  // The person's gender (must be "male" or "female").
  string gender = 14;
  // Unique identifier for the object.
  string id = 15;
  // Whether the person's ID number was provided.
  bool id_number_provided = 16;
  // Whether the person's secondary ID number was provided.
  bool id_number_secondary_provided = 17;
  // The person's last name.
  string last_name = 18;
  // The Kana variation of the person's last name (Japan only).
  string last_name_kana = 19;
  // The Kanji variation of the person's last name (Japan only).
  string last_name_kanji = 20;
  // The person's maiden name.
  string maiden_name = 21;
  // Key-value pairs that you can attach to an object.
  map<string, string> metadata = 22;
  // The country where the person is a national.
  string nationality = 23;
  // Object's type. Equal to "person".
  string object = 24;
  // The person's phone number.
  string phone = 25;
  // Indicates if the person holds an important public job or function.
  string political_exposure = 26;
  // address of the person's registered office (U.K. only).
  StripeAddress registered_address = 27;
  // relationship
  Relationship relationship = 28;
  // Information about the requirements for this person.
  Requirements requirements = 29;
  // Whether the last four digits of the person's SSN have been provided (U.S. only).
  bool ssn_last_4_provided = 30;
  // verification
  Verification verification = 31;

  // Address in Kana format.
  message AddressKana {
    // City/Ward.
    string city = 1;
    // Two-letter country code (ISO 3166-1 alpha-2).
    string country = 2;
    // Block/Building number.
    string line1 = 3;
    // Building details.
    string line2 = 4;
    // ZIP or postal code.
    string postal_code = 5;
    // Prefecture.
    string state = 6;
    // Town/cho-me.
    string town = 7;
  }

  // Address in Kanji format.
  message AddressKanji {
    // City/Ward.
    string city = 1;
    // Two-letter country code (ISO 3166-1 alpha-2).
    string country = 2;
    // Block/Building number.
    string line1 = 3;
    // Building details.
    string line2 = 4;
    // ZIP or postal code.
    string postal_code = 5;
    // Prefecture.
    string state = 6;
    // Town/cho-me.
    string town = 7;
  }

  // Date of birth.
  message Dob {
    // The day of birth, between 1 and 31.
    int32 day = 1;
    // The month of birth, between 1 and 12.
    int32 month = 2;
    // The four-digit year of birth.
    int32 year = 3;
  }

  // Future requirements for a person's account.
  message FutureRequirements {
    // Alternatives to satisfy requirements.
    repeated Alternative alternatives = 1;
    // Fields currently due.
    repeated string currently_due = 2;
    // Errors in validation or verification.
    repeated Errors errors = 3;
    // Fields that will eventually be due.
    repeated string eventually_due = 4;
    // Fields that are past due.
    repeated string past_due = 5;
    // Fields pending verification.
    repeated string pending_verification = 6;

    // Alternative fields structure.
    message Alternative {
      // Alternative fields due.
      repeated string alternative_fields_due = 1;
      // Original fields due.
      repeated string original_fields_due = 2;
    }

    // Errors structure.
    message Errors {
      // Error code.
      string code = 1;
      // Error reason.
      string reason = 2;
      // Requirement related to the error.
      string requirement = 3;
    }
  }

  // Relationship information for a person.
  message Relationship {
    // Whether the person is a director.
    bool director = 1;
    // Whether the person is an executive.
    bool executive = 2;
    // Whether the person is an owner.
    bool owner = 3;
    // Percent ownership.
    double percent_ownership = 4;
    // Whether the person is the primary representative.
    bool representative = 5;
    // The person's title.
    string title = 6;
  }

  // Requirements for a person's account.
  message Requirements {
    // Alternatives to satisfy requirements.
    repeated Alternative alternatives = 1;
    // Fields currently due.
    repeated string currently_due = 2;
    // Errors in validation or verification.
    repeated Errors errors = 3;
    // Fields that will eventually be due.
    repeated string eventually_due = 4;
    // Fields that are past due.
    repeated string past_due = 5;
    // Fields pending verification.
    repeated string pending_verification = 6;

    // Alternative structure for satisfying fields.
    message Alternative {
      // Fields that can be provided to satisfy all fields in original_fields_due.
      repeated string alternative_fields_due = 1;
      // Fields that are due and can be satisfied by providing all fields in alternative_fields_due.
      repeated string original_fields_due = 2;
    }

    // Errors structure for requirement validation.
    message Errors {
      // The code for the type of error.
      string code = 1;
      // An informative message that indicates the error type and provides additional details.
      string reason = 2;
      // Requirement related to the error.
      string requirement = 3;
    }
  }

  // Verification information for a person.
  message Verification {
    // Additional document for address verification.
    AdditionalDocument additional_document = 1;
    // Description of the verification state.
    string details = 2;
    // Machine-readable code specifying the verification state.
    string details_code = 3;
    // Primary document for identity verification.
    Document document = 4;
    // Verification status.
    string status = 5;

    // Additional document structure.
    message AdditionalDocument {
      // Back of the ID.
      string back = 1;
      // Details about the document's verification state.
      string details = 2;
      // Code specifying the verification state for the document.
      string details_code = 3;
      // Front of the ID.
      string front = 4;
    }

    // Document structure.
    message Document {
      // Back of the ID.
      string back = 1;
      // Details about the document's verification state.
      string details = 2;
      // Code specifying the verification state for the document.
      string details_code = 3;
      // Front of the ID.
      string front = 4;
    }
  }
}
