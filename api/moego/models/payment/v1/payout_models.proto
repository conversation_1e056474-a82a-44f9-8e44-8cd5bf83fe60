syntax = "proto3";

package moego.models.payment.v1;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/payment/v1;paymentpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.payment.v1";

// payout model
message PayoutModel {
  // id
  string id = 1;
  // object
  string object = 2;
  // amount
  int64 amount = 3;
  // application fee
  int64 application_fee_amount = 4;
  // Date that you can expect the payout to arrive in the bank.
  // This factors in delays to account for weekends or bank holidays.
  int64 arrival_date = 5;
  // Returns true if the payout is created by an automated payout schedule
  // and false if it’s requested manually.
  bool automatic = 6;
  // ID of the balance transaction that describes the impact of this payout on your account balance.
  string balance_transaction = 7;
  // Time at which the object was created. Measured in seconds since the Unix epoch.
  int64 created = 8;
  // currency
  string currency = 9;
  // An arbitrary string attached to the object. Often useful for displaying to users.
  string description = 10;
  // ID of the bank account or card the payout is sent to.
  string destination = 11;
  // If the payout fails or cancels, this is the ID of the balance transaction
  // that reverses the initial balance transaction and returns the funds from the failed payout back in your balance.
  string failure_balance_transaction = 12;
  // Error code that provides a reason for a payout failure, if available.
  string failure_code = 13;
  // Message that provides the reason for a payout failure, if available.
  string failure_message = 14;
  // Has the value true if the object exists in live mode
  // or the value false if the object exists in test mode.
  bool livemode = 15;
  // Set of key-value pairs that you can attach to an object.
  // This can be useful for storing additional information about the object in a structured format.
  map<string, string> metadata = 16;
  // The method used to send this payout, which can be standard or instant.
  // https://docs.stripe.com/payouts/instant-payouts-banks
  string method = 17;
  // If the payout reverses another, this is the ID of the original payout.
  string original_payout = 18;
  // reconciliation status
  string reconciliation_status = 19;
  // If the payout reverses, this is the ID of the payout that reverses this payout.
  string reversed_by = 20;
  // The source balance this payout came from, which can be one of the following: card, fpx, or bank_account.
  string source_type = 21;
  // Extra information about a payout that displays on the user’s bank statement.
  string statement_descriptor = 22;
  // Current status of the payout
  string status = 23;
  // Can be bank_account or card.
  string type = 24;
  // company id
  int64 company_id = 25;
  // business id
  int64 business_id = 26;
  // payout txn
  repeated PayoutTransaction payout_transactions = 27;
}

// payout transaction
message PayoutTransaction {
  // txn id
  string id = 1;
  // txn type
  string type = 2;
  // txn amount
  int64 amount = 3;
  // fee
  int64 fee = 4;
  // net
  int64 net = 5;
  // currency
  string currency = 6;
  // transfer by
  string transferred_by = 7;
  // status
  string status = 8;
  // reason
  string reason = 9;
  // source obj
  string source_object = 10;
  // payment intent id
  string payment_intent_id = 11;
  // service type
  string service_type = 12;
  // source type
  string source_type = 13;
  // payment id
  int64 payment_id = 14;
  // order id
  int64 order_id = 15;
  // target id
  int64 target_id = 16;
  // customer id
  int64 customer_id = 17;
  // desc
  string description = 18;
  // note
  string notes = 19;
  // create time
  int64 create_time = 20;
  // available time
  int64 available_time = 21;
  // refund id
  string refund_id = 22;
  // 对应moego内部的实体类型
  enum MoegoEntityType {
    // Unspecified
    MOEGO_ENTITY_TYPE_UNSPECIFIED = 0;
    // payment
    PAYMENT = 1;
    // refund
    REFUND = 2;
  }
  // moego entity type
  MoegoEntityType moego_entity_type = 23;
  // 对应moego内部的实体id
  string moego_entity_id = 24;
}
