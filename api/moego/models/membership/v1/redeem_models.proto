syntax = "proto3";

package moego.models.membership.v1;

import "google/protobuf/timestamp.proto";
import "google/type/money.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/membership/v1;membershippb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.membership.v1";

// the membership redeem scenario
enum RedeemScenario {
  // default
  MEMBERSHIP_REDEEM_SCENARIO_UNSPECIFIED = 0;
  // redeem by checkout
  REDEEM_BY_CHECKOUT = 1;
  // fully paid scenario
  REDEEM_BY_FULLY_PAID = 2;
  // redeem by online booking submit
  REDEEM_BY_ONLINE_BOOKING_SUBMIT = 3;
}

// the boarding redeem scenario item
message BoardingRedeemScenarioItem {
  // the boarding id
  int64 boarding_id = 1;
  // order item id
  int64 order_item_id = 2;
}

// redeem scenario detail
message RedeemScenarioItem {
  // target type
  TargetType target_type = 1;
  // target id
  int64 target_id = 2;
  // amount
  int32 amount = 5;
  // price
  double price = 6;
  // order item id
  int64 order_item_id = 7;
}

// the membership redeem context
message RedeemContext {
  // the redeem scenario
  RedeemScenario scenario = 1;
  // the scenario items
  repeated RedeemScenarioItem items = 2;
}

// the membership redeem history
message IncludeBenefitView {
  // history id
  int64 id = 1;
  // membership id
  int64 membership_id = 2;
  // quantity
  int32 total_quantity = 3;
  // remaining quantity
  int32 remaining_quantity = 4;
  // is limited
  bool is_limited = 5;
  // redeem time
  google.protobuf.Timestamp redeem_time = 6;
  // item detail def
  message RedeemItemDetailView {
    // item id
    int64 item_id = 1;
    // item name
    string item_name = 2;
    // item amount
    google.type.Money price = 3;
    // item type
    TargetType item_type = 4;
  }
  // item details
  repeated RedeemItemDetailView item_details = 7;
  // is all
  bool is_all = 8;
  // discount unit
  models.membership.v1.DiscountUnit discount_unit = 9;
  // discount value
  double discount_value = 10;
}

// the membership redeem history
message RedeemHistory {
  // the history id
  int64 id = 1;
  // the invoice id
  int64 invoice_id = 2;
  // the benefit id
  int64 benefit_id = 3;
  // the service id
  int64 service_id = 4;
  // the quantity
  int32 quantity = 5;
  // the appointment date
  string appointment_date = 6;
  // the grooming id
  int64 grooming_id = 8;
  // the service name
  string service_name = 18;
}

// discount type
enum TargetType {
  // unspecified
  TARGET_TYPE_UNSPECIFIED = 0;
  // service
  SERVICE = 1;
  // add on
  ADDON = 2;
  // product
  PRODUCT = 3;
}

// discount type
enum TargetSubType {
  // unspecified
  TARGET_SUB_TYPE_UNSPECIFIED = 0;
  // Grooming
  GROOMING = 1;
  // Boarding
  BOARDING = 2;
  // Daycare
  DAYCARE = 3;
}

// unit
enum DiscountUnit {
  // unspecified
  UNIT_UNSPECIFIED = 0;
  // percent
  PERCENT = 1;
  // numerical
  NUMERICAL = 2;
}
