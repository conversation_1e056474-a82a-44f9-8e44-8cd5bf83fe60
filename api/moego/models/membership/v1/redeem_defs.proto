syntax = "proto3";

package moego.models.membership.v1;

import "moego/models/membership/v1/redeem_models.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/membership/v1;membershippb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.membership.v1";

// Benefit redeem definition
message BenefitRedeemDef {
  // the benefit id
  int64 benefit_id = 1;
  // the quantity, discount 没有数量限制
  optional int32 amount = 2;
  // feature item id
  oneof feature_item_id {
    // add-on
    int64 add_on_id = 3;
    // service
    int64 service_id = 4;
    // boarding
    moego.models.membership.v1.BoardingRedeemScenarioItem boarding = 5;
    // product
    int64 product_id = 6;
  }
}
