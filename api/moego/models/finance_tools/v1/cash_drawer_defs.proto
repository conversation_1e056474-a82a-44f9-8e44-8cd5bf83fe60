syntax = "proto3";

package moego.models.finance_tools.v1;

import "google/type/interval.proto";
import "google/type/money.proto";
import "moego/models/finance_tools/v1/cash_drawer_enums.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/finance_tools/v1;financetoolspb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.finance_tools.v1";

// A partial part to create a cash drawer report.
message CreateCashDrawerReportDef {
  // The date range
  google.type.Interval range = 1 [(validate.rules).message.required = true];
  // The starting balance
  google.type.Money start_balance = 2 [(validate.rules).message.required = true];
  // The total of cash payments
  google.type.Money payments_total = 3 [(validate.rules).message.required = true];
  // The total of cash in/out adjustments
  google.type.Money adjustments_total = 4 [(validate.rules).message.required = true];
  // The actual ending balance
  google.type.Money counted_balance = 5 [(validate.rules).message.required = true];
  // Comment
  optional string comment = 6 [(validate.rules).string = {max_len: 200}];
}

// A partial part to update a cash drawer report.
message UpdateCashDrawerReportDef {
  // The report ID
  int64 id = 1 [(validate.rules).int64.gt = 0];
  // Comment
  optional string comment = 2 [(validate.rules).string = {max_len: 200}];
}

// A partial cash adjustment to create a cash adjustment.
message CreateCashDrawerAdjustmentDef {
  // Adjustment type (direction)
  CashDrawerAdjustmentType type = 1 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
  // The amount of cash in/out adjustments. Always positive.
  google.type.Money amount = 2 [(validate.rules).message.required = true];
  // Comment
  optional string comment = 3 [(validate.rules).string = {max_len: 200}];
}
