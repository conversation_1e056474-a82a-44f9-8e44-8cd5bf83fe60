syntax = "proto3";

package moego.models.event_bus.v1;

import "google/protobuf/timestamp.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/event_bus/v1;eventbuspb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.event_bus.v1";

// MessageSendEvent
message MessageSendEvent {
  // Direction
  enum Direction {
    // unspecified
    DIRECTION_UNSPECIFIED = 0;
    // send
    SEND = 1;
    // receive
    RECEIVE = 2;
  }
  // Status
  enum Status {
    // unspecified
    STATUS_UNSPECIFIED = 0;
    // success
    SUCCESS = 1;
    // fail
    FAIL = 2;
  }

  // message id
  int64 message_id = 1;
  // customer id
  int64 customer_id = 2;
  // phone number
  string phone_number = 3;
  // staff id
  int64 staff_id = 4;

  // text
  string text = 50;
  // direction
  Direction direction = 51;
  // status
  Status status = 52;
  // start time
  google.protobuf.Timestamp time = 53;
  // fail reason
  string fail_reason = 54;
}
