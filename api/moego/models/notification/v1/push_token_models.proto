syntax = "proto3";

package moego.models.notification.v1;

import "google/protobuf/timestamp.proto";
import "moego/models/notification/v1/notification_enums.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/notification/v1;notificationpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.notification.v1";

// the push token model
message PushTokenModel {
  // record id
  int64 id = 1;
  // account id
  int64 account_id = 2;
  // company id
  int64 company_id = 3;
  // business id
  int64 business_id = 4;
  // device type
  DeviceType device_type = 5;
  // push token
  string push_token = 6;
  // source
  PushTokenSource source = 7;
  // created at
  google.protobuf.Timestamp created_at = 8;
  // updated at
  google.protobuf.Timestamp updated_at = 9;
}
