package com.moego.server.grooming.web.vo.waitlist;

import com.moego.server.business.dto.CertainAreaDTO;
import com.moego.server.grooming.dto.GroomingPetInfoDetailDTO;
import com.moego.server.grooming.dto.waitlist.DatePreferenceDTO;
import com.moego.server.grooming.dto.waitlist.Staff;
import com.moego.server.grooming.dto.waitlist.StaffPreferenceDTO;
import com.moego.server.grooming.dto.waitlist.TimePreferenceDTO;
import com.moego.server.grooming.web.vo.ob.OBRequestDetailVO;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import lombok.Data;

@Data
public class WaitListDetailVO {
    private Long id;
    private Long appointmentId;
    private DatePreferenceDTO datePreference;
    private TimePreferenceDTO timePreference;
    private StaffPreferenceDTO staffPreference;
    private LocalDate validTill;
    private LocalDateTime createTime;
    private Staff createBy;
    private String ticketComment;
    private List<CertainAreaDTO> certainAreaList;
    private CustomerVO customerInfo;
    private List<GroomingPetInfoDetailDTO> petList;
    private Boolean allPetsStartAtSameTime;
    private Integer duration;
    private Byte isPaid;
    private BigDecimal paidAmount; // 已支付金额
    private BigDecimal refundAmount; // 退款金额
    private OBRequestDetailVO.OBPrepayDetailVO prepay;
}
