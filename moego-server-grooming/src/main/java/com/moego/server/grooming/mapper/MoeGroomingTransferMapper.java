package com.moego.server.grooming.mapper;

import com.moego.server.grooming.mapperbean.MoeGroomingTransfer;

public interface MoeGroomingTransferMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_transfer
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_transfer
     *
     * @mbg.generated
     */
    int insert(MoeGroomingTransfer record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_transfer
     *
     * @mbg.generated
     */
    int insertSelective(MoeGroomingTransfer record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_transfer
     *
     * @mbg.generated
     */
    MoeGroomingTransfer selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_transfer
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(MoeGroomingTransfer record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_grooming_transfer
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(MoeGroomingTransfer record);
}
