package com.moego.server.grooming.web.vo.client;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class GroomingReportRecordVO {

    @Schema(description = "grooming report pet id")
    private Integer petId;

    @Schema(description = "grooming report id")
    private Integer reportId;

    @Schema(description = "grooming report uuid")
    private String reportUuid;

    @Schema(description = "grooming report title")
    private String title;
}
