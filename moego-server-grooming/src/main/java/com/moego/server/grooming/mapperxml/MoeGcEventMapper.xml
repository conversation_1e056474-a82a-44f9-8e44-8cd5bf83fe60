<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.moego.server.grooming.mapper.MoeGcEventMapper">
  <resultMap id="BaseResultMap" type="com.moego.server.grooming.mapperbean.MoeGcEvent">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="business_id" jdbcType="INTEGER" property="businessId" />
    <result column="grooming_id" jdbcType="INTEGER" property="groomingId" />
    <result column="staff_id" jdbcType="INTEGER" property="staffId" />
    <result column="start_time" jdbcType="BIGINT" property="startTime" />
    <result column="end_time" jdbcType="BIGINT" property="endTime" />
    <result column="event_id" jdbcType="VARCHAR" property="eventId" />
    <result column="gc_calendar_id" jdbcType="INTEGER" property="gcCalendarId" />
    <result column="event_status" jdbcType="TINYINT" property="eventStatus" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="update_time" jdbcType="BIGINT" property="updateTime" />
    <result column="company_id" jdbcType="BIGINT" property="companyId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, business_id, grooming_id, staff_id, start_time, end_time, event_id, gc_calendar_id,
    event_status, create_time, update_time, company_id
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <include refid="Base_Column_List" />
    from moe_gc_event
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from moe_gc_event
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.moego.server.grooming.mapperbean.MoeGcEvent">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into moe_gc_event (business_id, grooming_id, staff_id,
      start_time, end_time, event_id,
      gc_calendar_id, event_status, create_time,
      update_time, company_id)
    values (#{businessId,jdbcType=INTEGER}, #{groomingId,jdbcType=INTEGER}, #{staffId,jdbcType=INTEGER},
      #{startTime,jdbcType=BIGINT}, #{endTime,jdbcType=BIGINT}, #{eventId,jdbcType=VARCHAR},
      #{gcCalendarId,jdbcType=INTEGER}, #{eventStatus,jdbcType=TINYINT}, #{createTime,jdbcType=BIGINT},
      #{updateTime,jdbcType=BIGINT}, #{companyId,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" parameterType="com.moego.server.grooming.mapperbean.MoeGcEvent">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into moe_gc_event
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="businessId != null">
        business_id,
      </if>
      <if test="groomingId != null">
        grooming_id,
      </if>
      <if test="staffId != null">
        staff_id,
      </if>
      <if test="startTime != null">
        start_time,
      </if>
      <if test="endTime != null">
        end_time,
      </if>
      <if test="eventId != null">
        event_id,
      </if>
      <if test="gcCalendarId != null">
        gc_calendar_id,
      </if>
      <if test="eventStatus != null">
        event_status,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="companyId != null">
        company_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="businessId != null">
        #{businessId,jdbcType=INTEGER},
      </if>
      <if test="groomingId != null">
        #{groomingId,jdbcType=INTEGER},
      </if>
      <if test="staffId != null">
        #{staffId,jdbcType=INTEGER},
      </if>
      <if test="startTime != null">
        #{startTime,jdbcType=BIGINT},
      </if>
      <if test="endTime != null">
        #{endTime,jdbcType=BIGINT},
      </if>
      <if test="eventId != null">
        #{eventId,jdbcType=VARCHAR},
      </if>
      <if test="gcCalendarId != null">
        #{gcCalendarId,jdbcType=INTEGER},
      </if>
      <if test="eventStatus != null">
        #{eventStatus,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=BIGINT},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.moego.server.grooming.mapperbean.MoeGcEvent">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_gc_event
    <set>
      <if test="businessId != null">
        business_id = #{businessId,jdbcType=INTEGER},
      </if>
      <if test="groomingId != null">
        grooming_id = #{groomingId,jdbcType=INTEGER},
      </if>
      <if test="staffId != null">
        staff_id = #{staffId,jdbcType=INTEGER},
      </if>
      <if test="startTime != null">
        start_time = #{startTime,jdbcType=BIGINT},
      </if>
      <if test="endTime != null">
        end_time = #{endTime,jdbcType=BIGINT},
      </if>
      <if test="eventId != null">
        event_id = #{eventId,jdbcType=VARCHAR},
      </if>
      <if test="gcCalendarId != null">
        gc_calendar_id = #{gcCalendarId,jdbcType=INTEGER},
      </if>
      <if test="eventStatus != null">
        event_status = #{eventStatus,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=BIGINT},
      </if>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.moego.server.grooming.mapperbean.MoeGcEvent">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_gc_event
    set business_id = #{businessId,jdbcType=INTEGER},
      grooming_id = #{groomingId,jdbcType=INTEGER},
      staff_id = #{staffId,jdbcType=INTEGER},
      start_time = #{startTime,jdbcType=BIGINT},
      end_time = #{endTime,jdbcType=BIGINT},
      event_id = #{eventId,jdbcType=VARCHAR},
      gc_calendar_id = #{gcCalendarId,jdbcType=INTEGER},
      event_status = #{eventStatus,jdbcType=TINYINT},
      create_time = #{createTime,jdbcType=BIGINT},
      update_time = #{updateTime,jdbcType=BIGINT},
      company_id = #{companyId,jdbcType=BIGINT}
    where id = #{id,jdbcType=INTEGER}
  </update>

    <select id="selectByBusinessIdGroomingId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from moe_gc_event
        where business_id = #{businessId,jdbcType=INTEGER}
        and grooming_id = #{groomingId,jdbcType=INTEGER}
        and event_status = 1
    </select>
    <select id="selectByGcCalendarIdGroomingId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from moe_gc_event
        where business_id = #{businessId,jdbcType=INTEGER} and
        grooming_id = #{groomingId,jdbcType=INTEGER} and
        gc_calendar_id = #{gcCalendarId,jdbcType=INTEGER} and event_status = 1
        order by staff_id asc,start_time asc
    </select>
    <select id="selectByGcCalendarId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from moe_gc_event
        where business_id = #{businessId,jdbcType=INTEGER} and
        gc_calendar_id = #{gcCalendarId,jdbcType=INTEGER} and event_status = 1
        order by staff_id asc,start_time asc
    </select>
    <select id="selectByEventId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from moe_gc_event
        where business_id = #{businessId,jdbcType=INTEGER} and
        event_id = #{eventId,jdbcType=VARCHAR} and event_status = 1
        order by staff_id asc,start_time asc
    </select>
    <update id="updateSetDeleteByPrimaryKey">
        update moe_gc_event
        set event_status = 2
        where id = #{id,jdbcType=INTEGER}
    </update>
</mapper>
