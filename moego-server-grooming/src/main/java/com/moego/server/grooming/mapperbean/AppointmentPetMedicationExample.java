package com.moego.server.grooming.mapperbean;

import com.moego.idl.models.business_customer.v1.FeedingMedicationScheduleDateType;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class AppointmentPetMedicationExample {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table appointment_pet_medication
     *
     * @mbg.generated
     */
    protected String orderByClause;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table appointment_pet_medication
     *
     * @mbg.generated
     */
    protected boolean distinct;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table appointment_pet_medication
     *
     * @mbg.generated
     */
    protected List<Criteria> oredCriteria;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table appointment_pet_medication
     *
     * @mbg.generated
     */
    public AppointmentPetMedicationExample() {
        oredCriteria = new ArrayList<>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table appointment_pet_medication
     *
     * @mbg.generated
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table appointment_pet_medication
     *
     * @mbg.generated
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table appointment_pet_medication
     *
     * @mbg.generated
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table appointment_pet_medication
     *
     * @mbg.generated
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table appointment_pet_medication
     *
     * @mbg.generated
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table appointment_pet_medication
     *
     * @mbg.generated
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table appointment_pet_medication
     *
     * @mbg.generated
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table appointment_pet_medication
     *
     * @mbg.generated
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table appointment_pet_medication
     *
     * @mbg.generated
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table appointment_pet_medication
     *
     * @mbg.generated
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table appointment_pet_medication
     *
     * @mbg.generated
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> dateTypeCriteria;

        protected List<Criterion> allCriteria;

        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
            dateTypeCriteria = new ArrayList<>();
        }

        public List<Criterion> getDateTypeCriteria() {
            return dateTypeCriteria;
        }

        protected void addDateTypeCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            dateTypeCriteria.add(new Criterion(
                    condition,
                    value,
                    "com.moego.server.grooming.mapper.typehandler.FeedingMedicationScheduleDateTypeHandler"));
            allCriteria = null;
        }

        protected void addDateTypeCriterion(
                String condition,
                FeedingMedicationScheduleDateType value1,
                FeedingMedicationScheduleDateType value2,
                String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            dateTypeCriteria.add(new Criterion(
                    condition,
                    value1,
                    value2,
                    "com.moego.server.grooming.mapper.typehandler.FeedingMedicationScheduleDateTypeHandler"));
            allCriteria = null;
        }

        public boolean isValid() {
            return criteria.size() > 0 || dateTypeCriteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            if (allCriteria == null) {
                allCriteria = new ArrayList<>();
                allCriteria.addAll(criteria);
                allCriteria.addAll(dateTypeCriteria);
            }
            return allCriteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
            allCriteria = null;
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
            allCriteria = null;
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
            allCriteria = null;
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andCompanyIdIsNull() {
            addCriterion("company_id is null");
            return (Criteria) this;
        }

        public Criteria andCompanyIdIsNotNull() {
            addCriterion("company_id is not null");
            return (Criteria) this;
        }

        public Criteria andCompanyIdEqualTo(Long value) {
            addCriterion("company_id =", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotEqualTo(Long value) {
            addCriterion("company_id <>", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdGreaterThan(Long value) {
            addCriterion("company_id >", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdGreaterThanOrEqualTo(Long value) {
            addCriterion("company_id >=", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdLessThan(Long value) {
            addCriterion("company_id <", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdLessThanOrEqualTo(Long value) {
            addCriterion("company_id <=", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdIn(List<Long> values) {
            addCriterion("company_id in", values, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotIn(List<Long> values) {
            addCriterion("company_id not in", values, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdBetween(Long value1, Long value2) {
            addCriterion("company_id between", value1, value2, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotBetween(Long value1, Long value2) {
            addCriterion("company_id not between", value1, value2, "companyId");
            return (Criteria) this;
        }

        public Criteria andAppointmentIdIsNull() {
            addCriterion("appointment_id is null");
            return (Criteria) this;
        }

        public Criteria andAppointmentIdIsNotNull() {
            addCriterion("appointment_id is not null");
            return (Criteria) this;
        }

        public Criteria andAppointmentIdEqualTo(Long value) {
            addCriterion("appointment_id =", value, "appointmentId");
            return (Criteria) this;
        }

        public Criteria andAppointmentIdNotEqualTo(Long value) {
            addCriterion("appointment_id <>", value, "appointmentId");
            return (Criteria) this;
        }

        public Criteria andAppointmentIdGreaterThan(Long value) {
            addCriterion("appointment_id >", value, "appointmentId");
            return (Criteria) this;
        }

        public Criteria andAppointmentIdGreaterThanOrEqualTo(Long value) {
            addCriterion("appointment_id >=", value, "appointmentId");
            return (Criteria) this;
        }

        public Criteria andAppointmentIdLessThan(Long value) {
            addCriterion("appointment_id <", value, "appointmentId");
            return (Criteria) this;
        }

        public Criteria andAppointmentIdLessThanOrEqualTo(Long value) {
            addCriterion("appointment_id <=", value, "appointmentId");
            return (Criteria) this;
        }

        public Criteria andAppointmentIdIn(List<Long> values) {
            addCriterion("appointment_id in", values, "appointmentId");
            return (Criteria) this;
        }

        public Criteria andAppointmentIdNotIn(List<Long> values) {
            addCriterion("appointment_id not in", values, "appointmentId");
            return (Criteria) this;
        }

        public Criteria andAppointmentIdBetween(Long value1, Long value2) {
            addCriterion("appointment_id between", value1, value2, "appointmentId");
            return (Criteria) this;
        }

        public Criteria andAppointmentIdNotBetween(Long value1, Long value2) {
            addCriterion("appointment_id not between", value1, value2, "appointmentId");
            return (Criteria) this;
        }

        public Criteria andPetDetailIdIsNull() {
            addCriterion("pet_detail_id is null");
            return (Criteria) this;
        }

        public Criteria andPetDetailIdIsNotNull() {
            addCriterion("pet_detail_id is not null");
            return (Criteria) this;
        }

        public Criteria andPetDetailIdEqualTo(Long value) {
            addCriterion("pet_detail_id =", value, "petDetailId");
            return (Criteria) this;
        }

        public Criteria andPetDetailIdNotEqualTo(Long value) {
            addCriterion("pet_detail_id <>", value, "petDetailId");
            return (Criteria) this;
        }

        public Criteria andPetDetailIdGreaterThan(Long value) {
            addCriterion("pet_detail_id >", value, "petDetailId");
            return (Criteria) this;
        }

        public Criteria andPetDetailIdGreaterThanOrEqualTo(Long value) {
            addCriterion("pet_detail_id >=", value, "petDetailId");
            return (Criteria) this;
        }

        public Criteria andPetDetailIdLessThan(Long value) {
            addCriterion("pet_detail_id <", value, "petDetailId");
            return (Criteria) this;
        }

        public Criteria andPetDetailIdLessThanOrEqualTo(Long value) {
            addCriterion("pet_detail_id <=", value, "petDetailId");
            return (Criteria) this;
        }

        public Criteria andPetDetailIdIn(List<Long> values) {
            addCriterion("pet_detail_id in", values, "petDetailId");
            return (Criteria) this;
        }

        public Criteria andPetDetailIdNotIn(List<Long> values) {
            addCriterion("pet_detail_id not in", values, "petDetailId");
            return (Criteria) this;
        }

        public Criteria andPetDetailIdBetween(Long value1, Long value2) {
            addCriterion("pet_detail_id between", value1, value2, "petDetailId");
            return (Criteria) this;
        }

        public Criteria andPetDetailIdNotBetween(Long value1, Long value2) {
            addCriterion("pet_detail_id not between", value1, value2, "petDetailId");
            return (Criteria) this;
        }

        public Criteria andPetIdIsNull() {
            addCriterion("pet_id is null");
            return (Criteria) this;
        }

        public Criteria andPetIdIsNotNull() {
            addCriterion("pet_id is not null");
            return (Criteria) this;
        }

        public Criteria andPetIdEqualTo(Long value) {
            addCriterion("pet_id =", value, "petId");
            return (Criteria) this;
        }

        public Criteria andPetIdNotEqualTo(Long value) {
            addCriterion("pet_id <>", value, "petId");
            return (Criteria) this;
        }

        public Criteria andPetIdGreaterThan(Long value) {
            addCriterion("pet_id >", value, "petId");
            return (Criteria) this;
        }

        public Criteria andPetIdGreaterThanOrEqualTo(Long value) {
            addCriterion("pet_id >=", value, "petId");
            return (Criteria) this;
        }

        public Criteria andPetIdLessThan(Long value) {
            addCriterion("pet_id <", value, "petId");
            return (Criteria) this;
        }

        public Criteria andPetIdLessThanOrEqualTo(Long value) {
            addCriterion("pet_id <=", value, "petId");
            return (Criteria) this;
        }

        public Criteria andPetIdIn(List<Long> values) {
            addCriterion("pet_id in", values, "petId");
            return (Criteria) this;
        }

        public Criteria andPetIdNotIn(List<Long> values) {
            addCriterion("pet_id not in", values, "petId");
            return (Criteria) this;
        }

        public Criteria andPetIdBetween(Long value1, Long value2) {
            addCriterion("pet_id between", value1, value2, "petId");
            return (Criteria) this;
        }

        public Criteria andPetIdNotBetween(Long value1, Long value2) {
            addCriterion("pet_id not between", value1, value2, "petId");
            return (Criteria) this;
        }

        public Criteria andMedicationAmountIsNull() {
            addCriterion("medication_amount is null");
            return (Criteria) this;
        }

        public Criteria andMedicationAmountIsNotNull() {
            addCriterion("medication_amount is not null");
            return (Criteria) this;
        }

        public Criteria andMedicationAmountEqualTo(String value) {
            addCriterion("medication_amount =", value, "medicationAmount");
            return (Criteria) this;
        }

        public Criteria andMedicationAmountNotEqualTo(String value) {
            addCriterion("medication_amount <>", value, "medicationAmount");
            return (Criteria) this;
        }

        public Criteria andMedicationAmountGreaterThan(String value) {
            addCriterion("medication_amount >", value, "medicationAmount");
            return (Criteria) this;
        }

        public Criteria andMedicationAmountGreaterThanOrEqualTo(String value) {
            addCriterion("medication_amount >=", value, "medicationAmount");
            return (Criteria) this;
        }

        public Criteria andMedicationAmountLessThan(String value) {
            addCriterion("medication_amount <", value, "medicationAmount");
            return (Criteria) this;
        }

        public Criteria andMedicationAmountLessThanOrEqualTo(String value) {
            addCriterion("medication_amount <=", value, "medicationAmount");
            return (Criteria) this;
        }

        public Criteria andMedicationAmountLike(String value) {
            addCriterion("medication_amount like", value, "medicationAmount");
            return (Criteria) this;
        }

        public Criteria andMedicationAmountNotLike(String value) {
            addCriterion("medication_amount not like", value, "medicationAmount");
            return (Criteria) this;
        }

        public Criteria andMedicationAmountIn(List<String> values) {
            addCriterion("medication_amount in", values, "medicationAmount");
            return (Criteria) this;
        }

        public Criteria andMedicationAmountNotIn(List<String> values) {
            addCriterion("medication_amount not in", values, "medicationAmount");
            return (Criteria) this;
        }

        public Criteria andMedicationAmountBetween(String value1, String value2) {
            addCriterion("medication_amount between", value1, value2, "medicationAmount");
            return (Criteria) this;
        }

        public Criteria andMedicationAmountNotBetween(String value1, String value2) {
            addCriterion("medication_amount not between", value1, value2, "medicationAmount");
            return (Criteria) this;
        }

        public Criteria andMedicationUnitIsNull() {
            addCriterion("medication_unit is null");
            return (Criteria) this;
        }

        public Criteria andMedicationUnitIsNotNull() {
            addCriterion("medication_unit is not null");
            return (Criteria) this;
        }

        public Criteria andMedicationUnitEqualTo(String value) {
            addCriterion("medication_unit =", value, "medicationUnit");
            return (Criteria) this;
        }

        public Criteria andMedicationUnitNotEqualTo(String value) {
            addCriterion("medication_unit <>", value, "medicationUnit");
            return (Criteria) this;
        }

        public Criteria andMedicationUnitGreaterThan(String value) {
            addCriterion("medication_unit >", value, "medicationUnit");
            return (Criteria) this;
        }

        public Criteria andMedicationUnitGreaterThanOrEqualTo(String value) {
            addCriterion("medication_unit >=", value, "medicationUnit");
            return (Criteria) this;
        }

        public Criteria andMedicationUnitLessThan(String value) {
            addCriterion("medication_unit <", value, "medicationUnit");
            return (Criteria) this;
        }

        public Criteria andMedicationUnitLessThanOrEqualTo(String value) {
            addCriterion("medication_unit <=", value, "medicationUnit");
            return (Criteria) this;
        }

        public Criteria andMedicationUnitLike(String value) {
            addCriterion("medication_unit like", value, "medicationUnit");
            return (Criteria) this;
        }

        public Criteria andMedicationUnitNotLike(String value) {
            addCriterion("medication_unit not like", value, "medicationUnit");
            return (Criteria) this;
        }

        public Criteria andMedicationUnitIn(List<String> values) {
            addCriterion("medication_unit in", values, "medicationUnit");
            return (Criteria) this;
        }

        public Criteria andMedicationUnitNotIn(List<String> values) {
            addCriterion("medication_unit not in", values, "medicationUnit");
            return (Criteria) this;
        }

        public Criteria andMedicationUnitBetween(String value1, String value2) {
            addCriterion("medication_unit between", value1, value2, "medicationUnit");
            return (Criteria) this;
        }

        public Criteria andMedicationUnitNotBetween(String value1, String value2) {
            addCriterion("medication_unit not between", value1, value2, "medicationUnit");
            return (Criteria) this;
        }

        public Criteria andMedicationNameIsNull() {
            addCriterion("medication_name is null");
            return (Criteria) this;
        }

        public Criteria andMedicationNameIsNotNull() {
            addCriterion("medication_name is not null");
            return (Criteria) this;
        }

        public Criteria andMedicationNameEqualTo(String value) {
            addCriterion("medication_name =", value, "medicationName");
            return (Criteria) this;
        }

        public Criteria andMedicationNameNotEqualTo(String value) {
            addCriterion("medication_name <>", value, "medicationName");
            return (Criteria) this;
        }

        public Criteria andMedicationNameGreaterThan(String value) {
            addCriterion("medication_name >", value, "medicationName");
            return (Criteria) this;
        }

        public Criteria andMedicationNameGreaterThanOrEqualTo(String value) {
            addCriterion("medication_name >=", value, "medicationName");
            return (Criteria) this;
        }

        public Criteria andMedicationNameLessThan(String value) {
            addCriterion("medication_name <", value, "medicationName");
            return (Criteria) this;
        }

        public Criteria andMedicationNameLessThanOrEqualTo(String value) {
            addCriterion("medication_name <=", value, "medicationName");
            return (Criteria) this;
        }

        public Criteria andMedicationNameLike(String value) {
            addCriterion("medication_name like", value, "medicationName");
            return (Criteria) this;
        }

        public Criteria andMedicationNameNotLike(String value) {
            addCriterion("medication_name not like", value, "medicationName");
            return (Criteria) this;
        }

        public Criteria andMedicationNameIn(List<String> values) {
            addCriterion("medication_name in", values, "medicationName");
            return (Criteria) this;
        }

        public Criteria andMedicationNameNotIn(List<String> values) {
            addCriterion("medication_name not in", values, "medicationName");
            return (Criteria) this;
        }

        public Criteria andMedicationNameBetween(String value1, String value2) {
            addCriterion("medication_name between", value1, value2, "medicationName");
            return (Criteria) this;
        }

        public Criteria andMedicationNameNotBetween(String value1, String value2) {
            addCriterion("medication_name not between", value1, value2, "medicationName");
            return (Criteria) this;
        }

        public Criteria andMedicationNoteIsNull() {
            addCriterion("medication_note is null");
            return (Criteria) this;
        }

        public Criteria andMedicationNoteIsNotNull() {
            addCriterion("medication_note is not null");
            return (Criteria) this;
        }

        public Criteria andMedicationNoteEqualTo(String value) {
            addCriterion("medication_note =", value, "medicationNote");
            return (Criteria) this;
        }

        public Criteria andMedicationNoteNotEqualTo(String value) {
            addCriterion("medication_note <>", value, "medicationNote");
            return (Criteria) this;
        }

        public Criteria andMedicationNoteGreaterThan(String value) {
            addCriterion("medication_note >", value, "medicationNote");
            return (Criteria) this;
        }

        public Criteria andMedicationNoteGreaterThanOrEqualTo(String value) {
            addCriterion("medication_note >=", value, "medicationNote");
            return (Criteria) this;
        }

        public Criteria andMedicationNoteLessThan(String value) {
            addCriterion("medication_note <", value, "medicationNote");
            return (Criteria) this;
        }

        public Criteria andMedicationNoteLessThanOrEqualTo(String value) {
            addCriterion("medication_note <=", value, "medicationNote");
            return (Criteria) this;
        }

        public Criteria andMedicationNoteLike(String value) {
            addCriterion("medication_note like", value, "medicationNote");
            return (Criteria) this;
        }

        public Criteria andMedicationNoteNotLike(String value) {
            addCriterion("medication_note not like", value, "medicationNote");
            return (Criteria) this;
        }

        public Criteria andMedicationNoteIn(List<String> values) {
            addCriterion("medication_note in", values, "medicationNote");
            return (Criteria) this;
        }

        public Criteria andMedicationNoteNotIn(List<String> values) {
            addCriterion("medication_note not in", values, "medicationNote");
            return (Criteria) this;
        }

        public Criteria andMedicationNoteBetween(String value1, String value2) {
            addCriterion("medication_note between", value1, value2, "medicationNote");
            return (Criteria) this;
        }

        public Criteria andMedicationNoteNotBetween(String value1, String value2) {
            addCriterion("medication_note not between", value1, value2, "medicationNote");
            return (Criteria) this;
        }

        public Criteria andCreatedAtIsNull() {
            addCriterion("created_at is null");
            return (Criteria) this;
        }

        public Criteria andCreatedAtIsNotNull() {
            addCriterion("created_at is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedAtEqualTo(Date value) {
            addCriterion("created_at =", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtNotEqualTo(Date value) {
            addCriterion("created_at <>", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtGreaterThan(Date value) {
            addCriterion("created_at >", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtGreaterThanOrEqualTo(Date value) {
            addCriterion("created_at >=", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtLessThan(Date value) {
            addCriterion("created_at <", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtLessThanOrEqualTo(Date value) {
            addCriterion("created_at <=", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtIn(List<Date> values) {
            addCriterion("created_at in", values, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtNotIn(List<Date> values) {
            addCriterion("created_at not in", values, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtBetween(Date value1, Date value2) {
            addCriterion("created_at between", value1, value2, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtNotBetween(Date value1, Date value2) {
            addCriterion("created_at not between", value1, value2, "createdAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtIsNull() {
            addCriterion("updated_at is null");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtIsNotNull() {
            addCriterion("updated_at is not null");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtEqualTo(Date value) {
            addCriterion("updated_at =", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtNotEqualTo(Date value) {
            addCriterion("updated_at <>", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtGreaterThan(Date value) {
            addCriterion("updated_at >", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtGreaterThanOrEqualTo(Date value) {
            addCriterion("updated_at >=", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtLessThan(Date value) {
            addCriterion("updated_at <", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtLessThanOrEqualTo(Date value) {
            addCriterion("updated_at <=", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtIn(List<Date> values) {
            addCriterion("updated_at in", values, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtNotIn(List<Date> values) {
            addCriterion("updated_at not in", values, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtBetween(Date value1, Date value2) {
            addCriterion("updated_at between", value1, value2, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtNotBetween(Date value1, Date value2) {
            addCriterion("updated_at not between", value1, value2, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andDeletedAtIsNull() {
            addCriterion("deleted_at is null");
            return (Criteria) this;
        }

        public Criteria andDeletedAtIsNotNull() {
            addCriterion("deleted_at is not null");
            return (Criteria) this;
        }

        public Criteria andDeletedAtEqualTo(Date value) {
            addCriterion("deleted_at =", value, "deletedAt");
            return (Criteria) this;
        }

        public Criteria andDeletedAtNotEqualTo(Date value) {
            addCriterion("deleted_at <>", value, "deletedAt");
            return (Criteria) this;
        }

        public Criteria andDeletedAtGreaterThan(Date value) {
            addCriterion("deleted_at >", value, "deletedAt");
            return (Criteria) this;
        }

        public Criteria andDeletedAtGreaterThanOrEqualTo(Date value) {
            addCriterion("deleted_at >=", value, "deletedAt");
            return (Criteria) this;
        }

        public Criteria andDeletedAtLessThan(Date value) {
            addCriterion("deleted_at <", value, "deletedAt");
            return (Criteria) this;
        }

        public Criteria andDeletedAtLessThanOrEqualTo(Date value) {
            addCriterion("deleted_at <=", value, "deletedAt");
            return (Criteria) this;
        }

        public Criteria andDeletedAtIn(List<Date> values) {
            addCriterion("deleted_at in", values, "deletedAt");
            return (Criteria) this;
        }

        public Criteria andDeletedAtNotIn(List<Date> values) {
            addCriterion("deleted_at not in", values, "deletedAt");
            return (Criteria) this;
        }

        public Criteria andDeletedAtBetween(Date value1, Date value2) {
            addCriterion("deleted_at between", value1, value2, "deletedAt");
            return (Criteria) this;
        }

        public Criteria andDeletedAtNotBetween(Date value1, Date value2) {
            addCriterion("deleted_at not between", value1, value2, "deletedAt");
            return (Criteria) this;
        }

        public Criteria andDateTypeIsNull() {
            addCriterion("date_type is null");
            return (Criteria) this;
        }

        public Criteria andDateTypeIsNotNull() {
            addCriterion("date_type is not null");
            return (Criteria) this;
        }

        public Criteria andDateTypeEqualTo(FeedingMedicationScheduleDateType value) {
            addDateTypeCriterion("date_type =", value, "dateType");
            return (Criteria) this;
        }

        public Criteria andDateTypeNotEqualTo(FeedingMedicationScheduleDateType value) {
            addDateTypeCriterion("date_type <>", value, "dateType");
            return (Criteria) this;
        }

        public Criteria andDateTypeGreaterThan(FeedingMedicationScheduleDateType value) {
            addDateTypeCriterion("date_type >", value, "dateType");
            return (Criteria) this;
        }

        public Criteria andDateTypeGreaterThanOrEqualTo(FeedingMedicationScheduleDateType value) {
            addDateTypeCriterion("date_type >=", value, "dateType");
            return (Criteria) this;
        }

        public Criteria andDateTypeLessThan(FeedingMedicationScheduleDateType value) {
            addDateTypeCriterion("date_type <", value, "dateType");
            return (Criteria) this;
        }

        public Criteria andDateTypeLessThanOrEqualTo(FeedingMedicationScheduleDateType value) {
            addDateTypeCriterion("date_type <=", value, "dateType");
            return (Criteria) this;
        }

        public Criteria andDateTypeIn(List<FeedingMedicationScheduleDateType> values) {
            addDateTypeCriterion("date_type in", values, "dateType");
            return (Criteria) this;
        }

        public Criteria andDateTypeNotIn(List<FeedingMedicationScheduleDateType> values) {
            addDateTypeCriterion("date_type not in", values, "dateType");
            return (Criteria) this;
        }

        public Criteria andDateTypeBetween(
                FeedingMedicationScheduleDateType value1, FeedingMedicationScheduleDateType value2) {
            addDateTypeCriterion("date_type between", value1, value2, "dateType");
            return (Criteria) this;
        }

        public Criteria andDateTypeNotBetween(
                FeedingMedicationScheduleDateType value1, FeedingMedicationScheduleDateType value2) {
            addDateTypeCriterion("date_type not between", value1, value2, "dateType");
            return (Criteria) this;
        }

        public Criteria andSpecificDatesIsNull() {
            addCriterion("specific_dates is null");
            return (Criteria) this;
        }

        public Criteria andSpecificDatesIsNotNull() {
            addCriterion("specific_dates is not null");
            return (Criteria) this;
        }

        public Criteria andSpecificDatesEqualTo(String value) {
            addCriterion("specific_dates =", value, "specificDates");
            return (Criteria) this;
        }

        public Criteria andSpecificDatesNotEqualTo(String value) {
            addCriterion("specific_dates <>", value, "specificDates");
            return (Criteria) this;
        }

        public Criteria andSpecificDatesGreaterThan(String value) {
            addCriterion("specific_dates >", value, "specificDates");
            return (Criteria) this;
        }

        public Criteria andSpecificDatesGreaterThanOrEqualTo(String value) {
            addCriterion("specific_dates >=", value, "specificDates");
            return (Criteria) this;
        }

        public Criteria andSpecificDatesLessThan(String value) {
            addCriterion("specific_dates <", value, "specificDates");
            return (Criteria) this;
        }

        public Criteria andSpecificDatesLessThanOrEqualTo(String value) {
            addCriterion("specific_dates <=", value, "specificDates");
            return (Criteria) this;
        }

        public Criteria andSpecificDatesLike(String value) {
            addCriterion("specific_dates like", value, "specificDates");
            return (Criteria) this;
        }

        public Criteria andSpecificDatesNotLike(String value) {
            addCriterion("specific_dates not like", value, "specificDates");
            return (Criteria) this;
        }

        public Criteria andSpecificDatesIn(List<String> values) {
            addCriterion("specific_dates in", values, "specificDates");
            return (Criteria) this;
        }

        public Criteria andSpecificDatesNotIn(List<String> values) {
            addCriterion("specific_dates not in", values, "specificDates");
            return (Criteria) this;
        }

        public Criteria andSpecificDatesBetween(String value1, String value2) {
            addCriterion("specific_dates between", value1, value2, "specificDates");
            return (Criteria) this;
        }

        public Criteria andSpecificDatesNotBetween(String value1, String value2) {
            addCriterion("specific_dates not between", value1, value2, "specificDates");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table appointment_pet_medication
     *
     * @mbg.generated do_not_delete_during_merge
     */
    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table appointment_pet_medication
     *
     * @mbg.generated
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}
