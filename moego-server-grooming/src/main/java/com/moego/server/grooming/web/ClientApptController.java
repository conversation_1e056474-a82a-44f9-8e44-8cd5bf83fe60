package com.moego.server.grooming.web;

import com.github.pagehelper.Page;
import com.moego.common.dto.BaseBusinessCustomerIdDTO;
import com.moego.common.dto.BusinessDateTimeDTO;
import com.moego.common.enums.ClientApptConst;
import com.moego.common.params.PageQuery;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.server.business.client.IBusinessBusinessClient;
import com.moego.server.business.dto.MoeBusinessDto;
import com.moego.server.customer.client.ICustomerCustomerClient;
import com.moego.server.grooming.mapperbean.MoeGroomingAppointment;
import com.moego.server.grooming.service.client.ClientApptService;
import com.moego.server.grooming.service.client.ClientApptServiceContext;
import com.moego.server.grooming.service.client.ClientApptUtils;
import com.moego.server.grooming.service.client.ClientHistoryApptService;
import com.moego.server.grooming.service.client.ClientUpcomingApptService;
import com.moego.server.grooming.service.client.IBaseClientApptService;
import com.moego.server.grooming.service.dto.client.ClientCancelApptDTO;
import com.moego.server.grooming.service.dto.client.ClientUpdateApptDTO;
import com.moego.server.grooming.service.dto.client.ListClientApptDTO;
import com.moego.server.grooming.web.query.ClientApptQuery;
import com.moego.server.grooming.web.vo.client.ClientApptDetailVO;
import com.moego.server.grooming.web.vo.client.ClientApptListVO;
import com.moego.server.grooming.web.vo.client.UpdateApptVO;
import jakarta.validation.Valid;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @since 2022/12/2
 */
@Slf4j
@RestController
@RequestMapping("/grooming/client")
@AllArgsConstructor
public class ClientApptController {

    private final ClientApptServiceContext apptServiceContext;
    private final ClientApptService apptService;

    private final IBusinessBusinessClient businessClient;
    private final ICustomerCustomerClient customerClient;

    public ListClientApptDTO buildListClientApptDTO(List<BaseBusinessCustomerIdDTO> customerIdDTOList) {
        // Get current data/time in business time zone
        List<Integer> businessIdList = customerIdDTOList.stream()
                .map(BaseBusinessCustomerIdDTO::getBusinessId)
                .distinct()
                .collect(Collectors.toList());
        Map<Integer, MoeBusinessDto> businessDtoMap = businessClient.getOnlyBusinessInfoBatch(businessIdList);
        Map<Integer, BusinessDateTimeDTO> businessDateTimeDTOMap =
                ClientApptUtils.buildBusinessDateTime(businessDtoMap);
        ListClientApptDTO listClientApptDTO = new ListClientApptDTO();
        listClientApptDTO.setCustomerIdDTOList(customerIdDTOList);
        listClientApptDTO.setBusinessDateTimeDTOMap(businessDateTimeDTOMap);
        return listClientApptDTO;
    }

    @PostMapping("/appts")
    @Auth(AuthType.ACCOUNT)
    public ClientApptListVO getApptList(@RequestBody ClientApptQuery clientApptQuery, AuthContext context) {
        List<BaseBusinessCustomerIdDTO> customerIdDTOList = customerClient.getLinkBusinessList(context.accountId());
        if (CollectionUtils.isEmpty(customerIdDTOList)) {
            return new ClientApptListVO();
        }
        IBaseClientApptService clientApptService = apptServiceContext.getApptService(clientApptQuery.getApptType());
        ListClientApptDTO listClientApptDTO = buildListClientApptDTO(customerIdDTOList);
        Page<MoeGroomingAppointment> page = clientApptService.getApptList(listClientApptDTO, clientApptQuery);
        return apptService.convertDO2VO(page);
    }

    public PageQuery buildPageQuery(PageQuery.OrderEnum order) {
        return new PageQuery()
                .setPageNum(1)
                .setPageSize(1)
                .setSortList(Arrays.asList(
                        new PageQuery.SortQuery().setSortBy("appointment_date").setOrder(order),
                        new PageQuery.SortQuery()
                                .setSortBy("appointment_start_time")
                                .setOrder(order)));
    }

    @GetMapping("/appt/coming-next")
    @Auth(AuthType.ACCOUNT)
    public ClientApptDetailVO getComingNextAppt(AuthContext context) {
        List<BaseBusinessCustomerIdDTO> customerIdDTOList = customerClient.getLinkBusinessList(context.accountId());
        if (CollectionUtils.isEmpty(customerIdDTOList)) {
            return new ClientApptDetailVO();
        }
        IBaseClientApptService apptService = apptServiceContext.getApptService(ClientApptConst.APPT_TYPE_UPCOMING);
        if (apptService instanceof ClientUpcomingApptService upcomingApptService) {
            ListClientApptDTO listClientApptDTO = buildListClientApptDTO(customerIdDTOList);
            PageQuery pageQuery = buildPageQuery(PageQuery.OrderEnum.asc);
            return upcomingApptService.getComingNextAppt(listClientApptDTO, pageQuery);
        }
        return new ClientApptDetailVO();
    }

    @GetMapping("/appt/last-finished")
    @Auth(AuthType.ACCOUNT)
    public ClientApptDetailVO getLastFinishedAppt(AuthContext context) {
        List<BaseBusinessCustomerIdDTO> customerIdDTOList = customerClient.getLinkBusinessList(context.accountId());
        if (CollectionUtils.isEmpty(customerIdDTOList)) {
            return new ClientApptDetailVO();
        }
        IBaseClientApptService apptService = apptServiceContext.getApptService(ClientApptConst.APPT_TYPE_HISTORY);
        if (apptService instanceof ClientHistoryApptService historyApptService) {
            ListClientApptDTO listClientApptDTO = buildListClientApptDTO(customerIdDTOList);
            PageQuery pageQuery = buildPageQuery(PageQuery.OrderEnum.desc);
            return historyApptService.getLastFinishedAppt(listClientApptDTO, pageQuery);
        }
        return new ClientApptDetailVO();
    }

    @GetMapping("/appt")
    @Auth(AuthType.ACCOUNT)
    public ClientApptDetailVO getApptDetail(@RequestParam("bookingId") String bookingId, AuthContext context) {
        List<BaseBusinessCustomerIdDTO> customerIdDTOList = customerClient.getLinkBusinessList(context.accountId());
        if (CollectionUtils.isEmpty(customerIdDTOList)) {
            return new ClientApptDetailVO();
        }
        return apptService.getApptDetail(bookingId, customerIdDTOList);
    }

    @DeleteMapping("/appt")
    @Auth(AuthType.ACCOUNT)
    public boolean cancelAppt(@RequestParam("bookingId") String bookingId, AuthContext context) {
        List<BaseBusinessCustomerIdDTO> customerIdDTOList = customerClient.getLinkBusinessList(context.accountId());
        if (CollectionUtils.isEmpty(customerIdDTOList)) {
            return false;
        }
        ClientCancelApptDTO cancelApptDTO = new ClientCancelApptDTO()
                .setBookingId(bookingId)
                .setClientId(context.accountId().intValue())
                .setCustomerIdDTOList(customerIdDTOList);
        return apptService.cancelAppt(cancelApptDTO);
    }

    @PostMapping("/appt")
    @Auth(AuthType.ACCOUNT)
    public boolean rescheduleAppt(@Valid @RequestBody ClientUpdateApptDTO updateApptDTO, AuthContext context) {
        List<BaseBusinessCustomerIdDTO> customerIdDTOList = customerClient.getLinkBusinessList(context.accountId());
        if (CollectionUtils.isEmpty(customerIdDTOList)) {
            return false;
        }
        updateApptDTO
                .setCustomerIdDTOList(customerIdDTOList)
                .setClientId(context.accountId().intValue());
        UpdateApptVO updateApptVO = apptService.updateAppt(updateApptDTO);
        return updateApptVO.updateSuccess();
    }

    @PostMapping("/appt-v2")
    @Auth(AuthType.ACCOUNT)
    public UpdateApptVO rescheduleApptV2(@Valid @RequestBody ClientUpdateApptDTO updateApptDTO, AuthContext context) {
        List<BaseBusinessCustomerIdDTO> customerIdDTOList = customerClient.getLinkBusinessList(context.accountId());
        if (CollectionUtils.isEmpty(customerIdDTOList)) {
            return UpdateApptVO.builder().updateSuccess(false).build();
        }
        updateApptDTO
                .setCustomerIdDTOList(customerIdDTOList)
                .setClientId(context.accountId().intValue());
        return apptService.updateAppt(updateApptDTO);
    }
}
