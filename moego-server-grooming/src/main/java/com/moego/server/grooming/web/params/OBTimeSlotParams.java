package com.moego.server.grooming.web.params;

import com.moego.server.grooming.dto.ob.OBPetDataDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.annotation.Nullable;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2022/10/12
 */
@Data
public class OBTimeSlotParams {

    @Schema(description = "existing client id")
    private Integer customerId;

    @Deprecated // todo: remove businessId
    @Schema(description = "business id")
    private Integer businessId;

    // @NotBlank todo: open this after removing businessId
    @Deprecated
    @Schema(description = "business name")
    private String businessName;

    @Nullable
    @Pattern(message = "Invalid date format, valid example: 2022-02-08", regexp = "^(\\d{4}-\\d{2}-\\d{2})$")
    @Schema(description = "start date of the query")
    private String date;

    @Nullable
    @Schema(description = "one of date or dates must be provided, uses dates first")
    private List<LocalDate> dates;

    @Schema(description = "number of days of results")
    private Integer count;

    @Deprecated
    @Max(365)
    @Schema(description = "the number of days for the furthest query, max value is 365")
    private Integer farthestDay;

    @NotNull
    @Schema(description = "service id")
    private List<Integer> serviceIds;

    @Deprecated
    @Schema(description = "enable smart schedule flag")
    private boolean querySmartScheduling;

    @Schema(description = "pet service info, key is petId, value is service id array")
    private Map<Integer, List<Integer>> petServices;

    @Schema(description = "latitude")
    private String lat;

    @Schema(description = "longitude")
    private String lng;

    @Schema(description = "zipcode")
    private String zipcode;

    @Schema(
            description =
                    "full time slot data on the first available date, returns at most one per half day of the remaining days")
    private Boolean queryPerHalfDay;

    @Schema(description = "staff id")
    private List<Integer> staffIdList;

    @Schema(description = "The query time slots ends at the end of the month of the first available date")
    private Boolean queryEndOfTheMonth;

    @NotEmpty
    @Schema(description = "pet param info, include weight and breedId")
    private List<OBPetDataDTO> petParamList;
}
