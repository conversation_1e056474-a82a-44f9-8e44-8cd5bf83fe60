package com.moego.server.grooming.service;

import static java.util.Comparator.comparing;
import static java.util.Objects.requireNonNullElseGet;
import static java.util.function.Function.identity;
import static java.util.stream.Collectors.toMap;

import com.moego.common.enums.StaffEnum;
import com.moego.lib.common.util.JsonUtil;
import com.moego.server.business.api.IBusinessStaffService;
import com.moego.server.business.dto.MoeStaffDto;
import com.moego.server.grooming.mapper.ObConfigTeamMapper;
import com.moego.server.grooming.mapperbean.ObConfigTeam;
import com.moego.server.grooming.mapperbean.ObConfigTeamExample;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OBConfigTeamService {

    private final ObConfigTeamMapper obConfigTeamMapper;
    private final IBusinessStaffService staffApi;

    /**
     * 查询 business 所有 team 配置，如果 staff 没有配置 team 则返回默认 team 配置。
     *
     * <p> NOTE：返回值已经按照 sort 降序排列。
     *
     * @param businessId business id
     * @return team config list
     */
    public List<ObConfigTeam> listForAllStaff(Integer businessId) {
        List<MoeStaffDto> staffs = getStaffs(businessId);

        Map<Integer, ObConfigTeam> staffIdToTeam =
                list(businessId, staffs).stream().collect(toMap(ObConfigTeam::getStaffId, identity()));

        return staffs.stream()
                .sorted(comparing(MoeStaffDto::getSort).reversed())
                .map(staff -> requireNonNullElseGet(staffIdToTeam.get(staff.getId()), () -> toTeam(staff)))
                .toList();
    }

    /**
     * 查询 business 所有 team 配置，假如 staff 已经被删除，则会删除对应的 team 配置。
     *
     * @param businessId business id
     * @return team config list
     */
    public List<ObConfigTeam> list(Integer businessId) {
        return list(businessId, getStaffs(businessId));
    }

    /**
     * Reset team config for specific business.
     *
     * @param businessId business id
     * @param teams      teams
     */
    @Transactional(rollbackFor = Exception.class)
    public int reset(Integer businessId, List<ObConfigTeam> teams) {
        ObConfigTeamExample example = new ObConfigTeamExample();
        example.createCriteria().andBusinessIdEqualTo(businessId);
        obConfigTeamMapper.deleteByExample(example);

        return !ObjectUtils.isEmpty(teams) ? obConfigTeamMapper.batchInsertSelective(teams) : 0;
    }

    private static ObConfigTeam toTeam(MoeStaffDto staff) {
        ObConfigTeam team = new ObConfigTeam();
        team.setBusinessId(staff.getBusinessId());
        team.setStaffId(staff.getId());
        team.setIntroduction("");
        team.setInstagramLink("");
        team.setTags(JsonUtil.toJson(List.of()));
        team.setIsEnabled(false);
        return team;
    }

    private List<MoeStaffDto> getStaffs(Integer businessId) {
        return staffApi.getStaffListByBusinessId(businessId, false).stream()
                .filter(s -> Objects.equals(s.getStatus(), StaffEnum.STATUS_NORMAL))
                .toList();
    }

    private List<ObConfigTeam> list(Integer businessId, List<MoeStaffDto> allStaffs) {
        ObConfigTeamExample example = new ObConfigTeamExample();
        example.createCriteria().andBusinessIdEqualTo(businessId);
        List<ObConfigTeam> teams = obConfigTeamMapper.selectByExample(example);

        Set<Integer> activeStaffIds = allStaffs.stream().map(MoeStaffDto::getId).collect(Collectors.toSet());

        return teams.stream()
                .filter(team -> activeStaffIds.contains(team.getStaffId()))
                .toList();
    }
}
