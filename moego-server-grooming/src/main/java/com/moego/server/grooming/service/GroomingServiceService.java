package com.moego.server.grooming.service;

import static com.moego.common.utils.CommonUtil.isNormal;
import static com.moego.lib.common.exception.ExceptionUtil.bizException;
import static java.util.Comparator.comparing;
import static java.util.function.Function.identity;
import static java.util.stream.Collectors.groupingBy;

import com.moego.common.dto.CompanyFunctionControlDto;
import com.moego.common.enums.BooleanEnum;
import com.moego.common.enums.CompanyFunctionControlConst;
import com.moego.common.enums.CustomerContactEnum;
import com.moego.common.enums.CustomerPetEnum;
import com.moego.common.enums.DeleteStatusEnum;
import com.moego.common.enums.GroomingAppointmentEnum;
import com.moego.common.enums.ResponseCodeEnum;
import com.moego.common.enums.ServiceEnum;
import com.moego.common.exception.CommonException;
import com.moego.common.utils.CommonUtil;
import com.moego.common.utils.DateUtil;
import com.moego.common.utils.PageUtil;
import com.moego.common.utils.Pagination;
import com.moego.common.utils.PrimitiveTypeUtil;
import com.moego.common.utils.StringMoegoUtil;
import com.moego.idl.models.appointment.v1.PetDetailDateType;
import com.moego.idl.models.business_customer.v1.BusinessPetSizeModel;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.marketing.v1.DiscountCodeModel;
import com.moego.idl.models.marketing.v1.DiscountCodeType;
import com.moego.idl.models.offering.v1.CustomizedServiceView;
import com.moego.idl.models.offering.v1.ServiceBundleSaleMappingModel;
import com.moego.idl.models.offering.v1.ServiceItemType;
import com.moego.idl.models.offering.v1.ServiceOverrideType;
import com.moego.idl.models.offering.v1.ServicePriceUnit;
import com.moego.idl.models.offering.v2.PetDetailCalculateDef;
import com.moego.idl.models.offering.v2.PetDetailCalculateResultDef;
import com.moego.idl.service.business_customer.v1.BusinessPetSizeServiceGrpc;
import com.moego.idl.service.business_customer.v1.ListPetSizeRequest;
import com.moego.idl.service.marketing.v1.DiscountCodeServiceGrpc;
import com.moego.idl.service.marketing.v1.GetDiscountCodeByCodeInput;
import com.moego.idl.service.marketing.v1.GetDiscountCodeByCodeOutput;
import com.moego.idl.service.offering.v1.BatchGetCustomizedServiceRequest;
import com.moego.idl.service.offering.v1.BatchGetCustomizedServiceResponse;
import com.moego.idl.service.offering.v1.CustomizedServiceQueryCondition;
import com.moego.idl.service.offering.v1.ListBundleServicesRequest;
import com.moego.idl.service.offering.v1.RemoveServiceFilterRequest;
import com.moego.idl.service.offering.v1.ServiceManagementServiceGrpc;
import com.moego.lib.common.exception.BizException;
import com.moego.lib.common.migrate.MigrateHelper;
import com.moego.lib.common.migrate.MigrateInfo;
import com.moego.lib.common.thread.ThreadPool;
import com.moego.lib.common.util.JsonUtil;
import com.moego.server.business.client.IBusinessBusinessClient;
import com.moego.server.business.client.IBusinessTaxClient;
import com.moego.server.business.dto.MoeBusinessDto;
import com.moego.server.business.dto.MoeBusinessTaxDto;
import com.moego.server.business.params.InfoIdParams;
import com.moego.server.customer.client.IPetClient;
import com.moego.server.customer.dto.CustomerPetDetailDTO;
import com.moego.server.grooming.constant.GroomingCategoryConstant;
import com.moego.server.grooming.dto.ApplicableServiceByCategoryDTO;
import com.moego.server.grooming.dto.CustomerServiceQueryDTO;
import com.moego.server.grooming.dto.MoeGroomingServiceDTO;
import com.moego.server.grooming.dto.PetApplicableServiceDTO;
import com.moego.server.grooming.dto.PetServiceDTO;
import com.moego.server.grooming.dto.ServiceCategoryDTO;
import com.moego.server.grooming.dto.ServiceChargeDTO;
import com.moego.server.grooming.dto.ob.CalculateServiceAmountDTO;
import com.moego.server.grooming.dto.ob.OBServiceListDto;
import com.moego.server.grooming.helper.CompanyHelper;
import com.moego.server.grooming.mapper.MoeBookOnlineStaffServiceMapper;
import com.moego.server.grooming.mapper.MoeGroomingAddonApplicableServiceMapper;
import com.moego.server.grooming.mapper.MoeGroomingAppointmentMapper;
import com.moego.server.grooming.mapper.MoeGroomingServiceBreedBindingMapper;
import com.moego.server.grooming.mapper.MoeGroomingServiceCategoryMapper;
import com.moego.server.grooming.mapper.MoeGroomingServiceCoatBindingMapper;
import com.moego.server.grooming.mapper.MoeGroomingServiceMapper;
import com.moego.server.grooming.mapperbean.MoeBookOnlineService;
import com.moego.server.grooming.mapperbean.MoeBookOnlineStaffService;
import com.moego.server.grooming.mapperbean.MoeGroomingAddonApplicableService;
import com.moego.server.grooming.mapperbean.MoeGroomingAddonApplicableServiceExample;
import com.moego.server.grooming.mapperbean.MoeGroomingCustomerServices;
import com.moego.server.grooming.mapperbean.MoeGroomingService;
import com.moego.server.grooming.mapperbean.MoeGroomingServiceBreedBinding;
import com.moego.server.grooming.mapperbean.MoeGroomingServiceCategory;
import com.moego.server.grooming.mapperbean.MoeGroomingServiceCoatBinding;
import com.moego.server.grooming.mapperbean.MoeGroomingServiceLocation;
import com.moego.server.grooming.mapstruct.GroomingServiceMapper;
import com.moego.server.grooming.params.BookOnlinePetParams;
import com.moego.server.grooming.params.PetDataForServiceParams;
import com.moego.server.grooming.params.PetServicePageParams;
import com.moego.server.grooming.params.QueryServiceByPetIdsParams;
import com.moego.server.grooming.params.QueryServiceParams;
import com.moego.server.grooming.params.ServiceDeleteParams;
import com.moego.server.grooming.params.ob.ServiceSingleParams;
import com.moego.server.grooming.service.dto.CalculateResult;
import com.moego.server.grooming.service.dto.CalculateServiceAmount;
import com.moego.server.grooming.service.dto.ReportServiceDto;
import com.moego.server.grooming.service.dto.ServiceFilterDTO;
import com.moego.server.grooming.service.dto.ServiceGroupByCategoryDto;
import com.moego.server.grooming.service.dto.ServicePetTypeBreedsDTO;
import com.moego.server.grooming.service.dto.ServiceSaveDto;
import com.moego.server.grooming.service.dto.ServiceTaxUpdateForApptDto;
import com.moego.server.grooming.service.dto.ServiceUpdateDto;
import com.moego.server.grooming.web.vo.CustomerDeleteSaveServiceVo;
import com.moego.server.grooming.web.vo.PetServicePageVO;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.IdentityHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.math3.util.Pair;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class GroomingServiceService {

    @Autowired
    private MoeGroomingAppointmentMapper moeGroomingAppointmentMapper;

    @Autowired
    private MoeGroomingServiceMapper groomingServiceMapper;

    @Autowired
    private MoeGroomingServiceCategoryMapper groomingServiceCategoryMapper;

    @Autowired
    private MoeGroomingServiceBreedBindingMapper groomingServiceBreedBindingMapper;

    @Autowired
    private MoeGroomingServiceCoatBindingMapper groomingServiceCoatBindingMapper;

    @Autowired
    private MoeGroomingAddonApplicableServiceMapper groomingAddonApplicableServiceMapper;

    @Autowired
    private CompanyGroomingServiceService companyGroomingServiceService;

    @Autowired
    private CompanyGroomingServiceQueryService companyGroomingServiceQueryService;

    @Autowired
    private MoeGroomingCustomerServicesService customerServicesService;

    @Autowired
    private MoeGroomingAppointmentService groomingAppointmentService;

    @Autowired
    private InvoiceService invoiceService;

    @Autowired
    private MoeBookOnlineStaffServiceMapper moeBookOnlineStaffServiceMapper;

    @Autowired
    private IBusinessBusinessClient iBusinessBusinessClient;

    @Autowired
    private IBusinessTaxClient iBusinessTaxClient;

    @Autowired
    private IPetClient iPetClient;

    @Resource
    private ServiceChargeService serviceChargeService;

    @Resource
    private DiscountCodeServiceGrpc.DiscountCodeServiceBlockingStub discountCodeClient;

    @Autowired
    private ServiceManagementServiceGrpc.ServiceManagementServiceBlockingStub serviceManagementServiceClient;

    @Autowired
    private BusinessPetSizeServiceGrpc.BusinessPetSizeServiceBlockingStub businessPetSizeServiceClient;

    @Resource
    private MigrateHelper migrateHelper;

    @Autowired
    private com.moego.idl.service.offering.v2.PricingRuleServiceGrpc.PricingRuleServiceBlockingStub
            pricingRuleServiceStub;

    @Autowired
    private MembershipService membershipService;

    @Autowired
    private CompanyHelper companyHelper;
    /**
     * 获取商户下的所有服务映射关系，并根据serviceID 分组
     *
     * @param businessId
     * @return
     * <AUTHOR>
     */
    public Map<Integer, List<Integer>> getServiceIdToStaffMap(Integer businessId) {
        List<MoeBookOnlineStaffService> serviceStaffs =
                moeBookOnlineStaffServiceMapper.selectByBusinessId(businessId, null);
        Map<Integer, List<Integer>> resultMap = new HashMap<>();
        serviceStaffs.forEach(relation -> {
            if (resultMap.containsKey(relation.getServiceId())) {
                resultMap.get(relation.getServiceId()).add(relation.getStaffId());
            } else {
                List<Integer> tmpList = new ArrayList<>();
                tmpList.add(relation.getStaffId());
                resultMap.put(relation.getServiceId(), tmpList);
            }
        });
        return resultMap;
    }

    public List<ServiceGroupByCategoryDto> getEditServiceWithCategory(
            Long companyId, Integer businessId, Byte type, Byte inactive, ServiceItemType serviceItemType) {
        // 查询所有的service
        List<MoeGroomingService> serviceList = groomingServiceMapper.selectByBusinessIdType(
                companyId, businessId, type, inactive, serviceItemType.getNumber());

        Map<Integer, MoeBookOnlineService> obServiceMap = companyGroomingServiceQueryService.obServiceQueryByIds(
                companyId,
                businessId,
                serviceList.stream().map(MoeGroomingService::getId).toList());

        Map<Integer, List<Integer>> serviceIdMap = getServiceIdToStaffMap(businessId);
        // 查询breed绑定
        Map<Integer, List<MoeGroomingServiceBreedBinding>> serviceBreedMap =
                getServiceBreedBindingMap(companyId, businessId);
        // 查询 coat 绑定
        Map<Integer, List<Integer>> serviceCoatBindingMap =
                companyGroomingServiceQueryService.getServiceCoatBindingMapNew(companyId, businessId);

        Map<Integer, List<MoeGroomingServiceLocation>> serviceLocatiomMap =
                companyGroomingServiceService.queryServiceLocationMap(companyId);

        // 查询 bundle service id list
        Map<Long, List<Long>> bundleServiceMap = getBundleServiceMap(
                companyId,
                serviceList.stream()
                        .map(MoeGroomingService::getId)
                        .filter(CommonUtil::isNormal)
                        .map(Integer::longValue)
                        .distinct()
                        .toList());

        List<ServiceUpdateDto> serviceUpdateDefaultList = new ArrayList<>();
        for (MoeGroomingService moeGroomingService : serviceList) {
            if (moeGroomingService.getCategoryId().equals(0)) {
                ServiceUpdateDto serviceUpdateDto = companyGroomingServiceService.buildServiceDTO(
                        moeGroomingService,
                        serviceIdMap,
                        serviceBreedMap,
                        serviceCoatBindingMap,
                        serviceLocatiomMap,
                        bundleServiceMap,
                        obServiceMap);
                serviceUpdateDefaultList.add(serviceUpdateDto);
            }
        }
        // 查询service所有的category
        List<MoeGroomingServiceCategory> serviceCategoryList =
                companyGroomingServiceQueryService.groomingServiceCategorySelectByBusinessId(
                        companyId, businessId, type, serviceItemType);
        List<ServiceGroupByCategoryDto> categoryWithServiceList = new ArrayList<>();
        // 处理默认的分组
        ServiceGroupByCategoryDto categoryDefaultDto = new ServiceGroupByCategoryDto();
        categoryDefaultDto.setType(type);
        categoryDefaultDto.setName("");
        categoryDefaultDto.setCategoryId(0);
        categoryDefaultDto.setServiceList(new ArrayList<>(serviceUpdateDefaultList));
        categoryWithServiceList.add(categoryDefaultDto);
        // 处理非默认的分组
        for (MoeGroomingServiceCategory serviceCategory : serviceCategoryList) {
            ServiceGroupByCategoryDto serviceGroupByCategoryDto = new ServiceGroupByCategoryDto();
            serviceGroupByCategoryDto.setCategoryId(serviceCategory.getId());
            serviceGroupByCategoryDto.setName(serviceCategory.getName());
            serviceGroupByCategoryDto.setType(serviceCategory.getType());
            List<ServiceUpdateDto> serviceUpdateDtoList = new ArrayList<>();
            for (MoeGroomingService service : serviceList) {
                if (service.getCategoryId().equals(serviceCategory.getId())) {
                    ServiceUpdateDto serviceUpdateDto = companyGroomingServiceService.buildServiceDTO(
                            service,
                            serviceIdMap,
                            serviceBreedMap,
                            serviceCoatBindingMap,
                            serviceLocatiomMap,
                            bundleServiceMap,
                            obServiceMap);
                    serviceUpdateDtoList.add(serviceUpdateDto);
                }
            }
            serviceGroupByCategoryDto.setServiceList(serviceUpdateDtoList);
            categoryWithServiceList.add(serviceGroupByCategoryDto);
        }
        return categoryWithServiceList;
    }

    @Nonnull
    public Map<Long, List<Long>> getBundleServiceMap(Long companyId, Collection<Long> serviceIds) {
        return serviceManagementServiceClient
                .listBundleServices(ListBundleServicesRequest.newBuilder()
                        .setCompanyId(companyId)
                        .addAllServiceIds(serviceIds)
                        .build())
                .getBundleServicesList()
                .stream()
                .collect(groupingBy(
                        ServiceBundleSaleMappingModel::getServiceId,
                        Collectors.mapping(ServiceBundleSaleMappingModel::getBundleServiceId, Collectors.toList())));
    }

    public PetServicePageVO getServiceByPage(Long companyId, Integer businessId, PetServicePageParams params) {
        // 查询所有的service
        org.springframework.data.util.Pair<List<MoeGroomingService>, Pagination> paginationPair = PageUtil.selectPage(
                params.getPagination(),
                () -> companyGroomingServiceQueryService.groomingServiceSelectByBusinessIdTypeKeyword(
                        companyId, businessId, params.getType(), params.getInactive(), params.getKeyword()));
        List<ServiceUpdateDto> serviceList = paginationPair.getFirst().stream()
                .map(service -> {
                    ServiceUpdateDto serviceUpdateDto = new ServiceUpdateDto();
                    BeanUtils.copyProperties(service, serviceUpdateDto);
                    serviceUpdateDto.setServiceId(service.getId());
                    return serviceUpdateDto;
                })
                .toList();

        List<ServiceGroupByCategoryDto> categoryWithServiceList = new ArrayList<>();

        List<Integer> categoryIdList = serviceList.stream()
                .map(ServiceUpdateDto::getCategoryId)
                .distinct()
                .toList();
        if (categoryIdList.contains(0)) {
            // 处理默认的分组
            ServiceGroupByCategoryDto defaultCategoryService = new ServiceGroupByCategoryDto();
            defaultCategoryService.setType(params.getType());
            defaultCategoryService.setName(Strings.EMPTY);
            defaultCategoryService.setCategoryId(0);
            defaultCategoryService.setServiceList(serviceList.stream()
                    .filter(service -> Objects.equals(0, service.getCategoryId()))
                    .toList());
            categoryWithServiceList.add(defaultCategoryService);
        }

        // 处理非默认的分组
        // 查询service所有的category
        List<ServiceGroupByCategoryDto> namedCategoryServiceList =
                companyGroomingServiceQueryService
                        .groomingServiceCategorySelectByBusinessId(companyId, businessId, params.getType(), null)
                        .stream()
                        .filter(category -> categoryIdList.contains(category.getId()))
                        .sorted(comparing(MoeGroomingServiceCategory::getSort).reversed())
                        .map(category -> {
                            ServiceGroupByCategoryDto serviceGroupByCategoryDto = new ServiceGroupByCategoryDto();
                            serviceGroupByCategoryDto.setType(category.getType());
                            serviceGroupByCategoryDto.setName(category.getName());
                            serviceGroupByCategoryDto.setCategoryId(category.getId());
                            serviceGroupByCategoryDto.setServiceList(serviceList.stream()
                                    .filter(service -> Objects.equals(category.getId(), service.getCategoryId()))
                                    .toList());
                            return serviceGroupByCategoryDto;
                        })
                        .toList();
        categoryWithServiceList.addAll(namedCategoryServiceList);

        return new PetServicePageVO(paginationPair.getSecond(), categoryWithServiceList);
    }

    /**
     * 查询 service binding 关系，以 Map 返回，key 为 serviceId，值为 bindingList
     *
     * @param businessId
     * @return
     */
    public Map<Integer, List<MoeGroomingServiceBreedBinding>> getServiceBreedBindingMap(
            Long companyId, Integer businessId) {
        if (companyId == null) {
            var companyDto = iBusinessBusinessClient.getCompanyByBusinessId(businessId);
            companyId = companyDto.getId().longValue();
        }
        return groomingServiceBreedBindingMapper.selectByCompanyIdAndServiceId(companyId, null).stream()
                .collect(groupingBy(MoeGroomingServiceBreedBinding::getServiceId));
    }

    /**
     * 查询支持 Grooming 的 addon, 仅用于兼容旧接口
     */
    public Set<Integer> getGroomingAddons(Long companyId) {
        if (companyId == null) {
            return Collections.emptySet();
        }
        MoeGroomingAddonApplicableServiceExample example = new MoeGroomingAddonApplicableServiceExample();
        example.createCriteria()
                .andCompanyIdEqualTo(companyId)
                .andServiceItemTypeEqualTo(ServiceItemType.GROOMING_VALUE);
        List<MoeGroomingAddonApplicableService> groomingAddonApplicableServices =
                groomingAddonApplicableServiceMapper.selectByExample(example);

        return groomingAddonApplicableServices.stream()
                .map(MoeGroomingAddonApplicableService::getAddonId)
                .map(Long::intValue)
                .collect(Collectors.toSet());
    }

    /**
     * 查询 service coat binding 关系
     * key 为 service id
     * value 为 coat name list，coat name 为小写且去空格
     *
     * @return
     */
    public Boolean selectServiceByTagId(Long companyId, Integer tagId) {
        return groomingServiceMapper.selectServiceCountByTagId(companyId, tagId) > 0;
    }

    /**
     * @param saveDto
     * @param businessId
     * @param companyId
     * @return
     */
    public Integer createService(ServiceSaveDto saveDto, Integer businessId, Long companyId) {
        if (saveDto.getCategoryId() != 0) {
            MoeGroomingServiceCategory serviceCategory =
                    groomingServiceCategoryMapper.selectByPrimaryKey(saveDto.getCategoryId());
            if (serviceCategory == null || !businessId.equals(serviceCategory.getBusinessId())) {
                throw new CommonException(ResponseCodeEnum.SERVICE_CATEGORY_NOT_FOUND);
            }
        }
        if (checkServiceNameIsExist(businessId, saveDto.getName(), saveDto.getType())) {
            throw new CommonException(ResponseCodeEnum.SERVICE_NAME_IS_EXIST);
        }

        MoeGroomingService service = new MoeGroomingService();
        BeanUtils.copyProperties(saveDto, service);
        service.setBusinessId(businessId);
        service.setCompanyId(companyId);
        service.setCreateTime(DateUtil.get10Timestamp());
        service.setUpdateTime(DateUtil.get10Timestamp());
        service.setType(saveDto.getType());
        boolean isAllowServiceFilter = isServiceFilterAllowed(businessId);
        if (isAllowServiceFilter && Objects.equals(saveDto.getWeightFilter(), ServiceEnum.FILTER_OPEN)) {
            // 只有打开weight_filter开关时才保存weight range
            service.setWeightDownLimit(saveDto.getWeightRange()[0]);
            service.setWeightUpLimit(saveDto.getWeightRange()[1]);
        }
        boolean result = groomingServiceMapper.insertSelective(service) > 0;
        Integer serviceId = service.getId();
        if (result) {
            MoeGroomingService serviceSort = new MoeGroomingService();
            serviceSort.setId(serviceId);
            serviceSort.setSort(serviceId);
            groomingServiceMapper.updateByPrimaryKeySelective(serviceSort);

            // customizedBreed参数检查
            if (isAllowServiceFilter && Objects.equals(saveDto.getBreedFilter(), ServiceEnum.FILTER_OPEN)) {
                // breed filter打开时才更新binding breeding
                updateServiceAndBreedBinding(businessId, companyId, serviceId, saveDto.getCustomizedBreed());
            }
            if (Objects.equals(saveDto.getCoatFilter(), ServiceEnum.FILTER_OPEN)) {
                MoeGroomingServiceCoatBinding coatBinding = new MoeGroomingServiceCoatBinding();
                coatBinding.setBusinessId(businessId);
                coatBinding.setCompanyId(companyId);
                coatBinding.setServiceId(serviceId);
                coatBinding.setCoatIdList(JsonUtil.toJson(saveDto.getCustomizedCoat()));
                groomingServiceCoatBindingMapper.insertOrUpdate(coatBinding);
            }
        }
        return serviceId;
    }

    public Boolean updateServiceForApptInvoice(Integer tokenBusinessId, ServiceTaxUpdateForApptDto serviceUpdateDto) {
        // 不同步修改service tax
        //        MoeGroomingService serviceUpdate = new MoeGroomingService();
        //        serviceUpdate.setId(serviceUpdateDto.getServiceId());
        //        serviceUpdate.setTaxId(serviceUpdateDto.getTaxId());
        //        groomingServiceMapper.updateByPrimaryKeySelective(serviceUpdate);
        // 同步invoice
        //        invoiceService.saveWithAppointmentId(tokenBusinessId, serviceUpdateDto.getGroomingId());
        //        return true;
        return invoiceService.updateInvoiceServiceTax(tokenBusinessId, serviceUpdateDto);
    }

    public int updateServiceList(
            Integer businessId, Long companyId, List<ServiceSingleParams> serviceSingleParamsList) {
        List<MoeGroomingService> serviceList = serviceSingleParamsList.stream()
                .map(service -> {
                    if (!CollectionUtils.isEmpty(service.getStaffIdList())) {
                        // staffIdList 不为空，更新 service 和 staff 的关系
                        updateServiceAndStaffRelation(
                                businessId, companyId, service.getServiceId(), service.getStaffIdList());
                    }
                    MoeGroomingService groomingService = new MoeGroomingService();
                    groomingService.setBusinessId(businessId);
                    groomingService.setBookOnlineAvailable(service.getBookOnlineAvailable());
                    groomingService.setShowBasePrice(service.getShowBasePrice());
                    groomingService.setIsAllStaff(service.getIsAllStaff());
                    groomingService.setUpdateTime(DateUtil.get10Timestamp());
                    return groomingService;
                })
                .toList();
        return groomingServiceMapper.batchUpdateService(serviceList);
    }

    public void checkServiceIdWithBidAndNameIsValid(
            Long companyId, Integer businessId, Integer serviceId, String name) {
        MoeGroomingService service = groomingServiceMapper.selectByPrimaryKey(serviceId); // name && auth check
        if (service == null
                || (!service.getCompanyId().equals(companyId)
                        && !service.getBusinessId().equals(businessId))) {
            throw bizException(Code.CODE_SERVICE_NOT_FOUND);
        }

        // 检测名字是否重复
        if (name != null
                && !name.equals(service.getName())
                && checkServiceNameIsExist(businessId, name, service.getType(), serviceId)) {
            throw bizException(Code.CODE_SERVICE_NAME_IS_EXIST);
        }
    }

    public Boolean updateService(ServiceUpdateDto updateDto, Integer businessId, Long companyId, Integer operatorId) {
        MoeGroomingService service =
                groomingServiceMapper.selectByPrimaryKey(updateDto.getServiceId()); // auth check && update service
        if (service == null || !service.getCompanyId().equals(companyId)) {
            throw new BizException(Code.CODE_SERVICE_NOT_FOUND_VALUE);
        }
        Byte type = service.getType();
        // 检测名字是否重复
        if (updateDto.getName() != null
                && !updateDto.getName().equals(service.getName())
                && checkServiceNameIsExist(businessId, updateDto.getName(), type, updateDto.getServiceId())) {
            throw new BizException(Code.CODE_SERVICE_NAME_IS_EXIST_VALUE);
        }

        MoeGroomingService updateServiceBean = new MoeGroomingService();
        // 判断是否改动inactive的值，是的话需要修改sort值
        if (updateDto.getInactive() != null && !updateDto.getInactive().equals(service.getInactive())) {
            Integer maxSort = groomingServiceMapper.getMaxSortByCategoryId(
                    businessId, service.getCategoryId(), updateDto.getInactive());
            updateServiceBean.setSort(maxSort == null ? 0 : maxSort + 1); // sort值设置成一个比较大的值，排在前面的位置
        }

        if (updateDto.getStaffIdList() != null) {
            // staffIdList 为null，表示不更新， 为empty，表示清空service和staff的关系
            updateServiceAndStaffRelation(businessId, companyId, updateDto.getServiceId(), updateDto.getStaffIdList());
        }
        BeanUtils.copyProperties(updateDto, updateServiceBean);
        updateServiceBean.setId(updateDto.getServiceId());
        updateServiceBean.setBusinessId(businessId);
        updateServiceBean.setUpdateTime(DateUtil.get10Timestamp());
        updateServiceBean.setType(service.getType());
        // 根据权限判断是否更新weight、breed配置
        boolean isAllowServiceFilter = isServiceFilterAllowed(businessId);
        if (isAllowServiceFilter && Objects.equals(updateDto.getWeightFilter(), ServiceEnum.FILTER_OPEN)) {
            // 参数已校验，只有打开weight_filter开关时才保存weight range
            updateServiceBean.setWeightDownLimit(updateDto.getWeightRange()[0]);
            updateServiceBean.setWeightUpLimit(updateDto.getWeightRange()[1]);
        }
        updateServiceBean.setCompanyId(companyId);
        groomingServiceMapper.updateByPrimaryKeySelectiveWithBidCid(updateServiceBean);

        // customizedBreed 参数检查
        if (isAllowServiceFilter && Objects.equals(updateDto.getBreedFilter(), ServiceEnum.FILTER_OPEN)) {
            // breed filter 打开时才更新 binding breeding
            updateServiceAndBreedBinding(
                    businessId, companyId, updateDto.getServiceId(), updateDto.getCustomizedBreed());
        }
        if (Objects.equals(updateDto.getCoatFilter(), ServiceEnum.FILTER_OPEN)) {
            MoeGroomingServiceCoatBinding coatBinding = new MoeGroomingServiceCoatBinding();
            coatBinding.setBusinessId(businessId);
            coatBinding.setCompanyId(companyId);
            coatBinding.setServiceId(updateDto.getServiceId());
            coatBinding.setCoatIdList(JsonUtil.toJson(updateDto.getCustomizedCoat()));
            groomingServiceCoatBindingMapper.insertOrUpdate(coatBinding);
        }

        // 将最新的duration、price、tax应用到upcoming预约上
        if (updateDto.getApplyUpcomingAppt() != null && updateDto.getApplyUpcomingAppt()) {
            ThreadPool.execute(() -> groomingAppointmentService.serviceApplyToUpcomingAppts(
                    businessId, updateDto.getServiceId(), operatorId));
        }
        return true;
    }

    /**
     * 当删除此 coat 时，更新绑定关系
     * 如果 service 只绑定了一个 coat，更新 service 的 coatFilter 为 close（即不过滤）
     *
     * @param businessId
     * @param coatId
     */
    public Boolean updateCoatBindingAfterDeleteCoat(Integer businessId, Integer coatId) {
        MigrateInfo migrateInfo = migrateHelper.getMigrationInfo(businessId);
        long companyId = migrateInfo.companyId();
        groomingServiceCoatBindingMapper
                .selectByCompanyIdAndCoatId(companyId, coatId)
                .forEach(coatBinding -> {
                    List<Integer> coatIdList = JsonUtil.toList(coatBinding.getCoatIdList(), Integer.class);
                    if (CollectionUtils.isEmpty(coatIdList)) {
                        return;
                    }
                    coatIdList.removeIf(id -> Objects.equals(id, coatId));
                    if (CollectionUtils.isEmpty(coatIdList)) {
                        MoeGroomingService service = groomingServiceMapper.selectByPrimaryKey(
                                coatBinding.getServiceId()); // update coat filter
                        if (Objects.nonNull(service) && Objects.equals(companyId, service.getCompanyId())) {
                            service.setCoatFilter(ServiceEnum.FILTER_CLOSE);
                            service.setUpdateTime(DateUtil.get10Timestamp());
                            groomingServiceMapper.updateByPrimaryKeySelective(service);
                        }
                    }
                    coatBinding.setCoatIdList(JsonUtil.toJson(coatIdList));
                    groomingServiceCoatBindingMapper.updateByPrimaryKey(coatBinding);
                });
        return Boolean.TRUE;
    }

    /**
     * 如果 service 只有一个 breed，当删除此 breed 时，更新 service 的 breedFilter 为 close（即不过滤）
     *
     * @param businessId
     * @param breedName
     */
    public Boolean updateBreedBindingAfterDeleteBreed(Integer businessId, String breedName) {
        groomingServiceBreedBindingMapper
                .selectByBusinessIdAndBreedName(businessId, breedName)
                .forEach(breedBinding -> {
                    MoeGroomingService service = groomingServiceMapper.selectByPrimaryKey(
                            breedBinding.getServiceId()); // breed filter update
                    if (Objects.nonNull(service) && Objects.equals(businessId, service.getBusinessId())) {
                        service.setBreedFilter(ServiceEnum.FILTER_CLOSE);
                        service.setUpdateTime(DateUtil.get10Timestamp());
                        groomingServiceMapper.updateByPrimaryKeySelective(service);
                    }
                    MoeGroomingServiceBreedBinding updateRecord = new MoeGroomingServiceBreedBinding();
                    updateRecord.setId(breedBinding.getId());
                    updateRecord.setIsAll(true);
                    updateRecord.setBreedNames("");
                    updateRecord.setBreedNameList(JsonUtil.toJson(List.of()));
                    updateRecord.setUpdateTime(DateUtil.get10Timestamp());
                    groomingServiceBreedBindingMapper.updateByPrimaryKeySelective(updateRecord);
                });
        return Boolean.TRUE;
    }

    /**
     * 校验更新service接口参数
     *
     * @Param filterDTO
     */
    public void checkServiceFilterParams(ServiceFilterDTO filterDTO) {
        // weight range检查
        Byte weightFilter = filterDTO.getWeightFilter();
        BigDecimal[] weightRange = filterDTO.getWeightRange();
        if (Objects.equals(weightFilter, ServiceEnum.FILTER_OPEN) && weightRange != null) {
            if (weightRange.length != 2
                    || weightRange[0].compareTo(BigDecimal.ZERO) < 0
                    || weightRange[0].compareTo(weightRange[1]) > 0) {
                throw bizException(Code.CODE_PARAMS_ERROR, "weight range error.");
            }
        }

        // breeds检查
        Byte breedFilter = filterDTO.getBreedFilter();
        List<ServicePetTypeBreedsDTO> customizedBreed = filterDTO.getCustomizedBreed();
        if (Objects.equals(breedFilter, ServiceEnum.FILTER_OPEN) && CollectionUtils.isEmpty(customizedBreed)) {
            throw bizException(Code.CODE_PARAMS_ERROR, "breed list could not be empty.");
        }

        // coat check
        Byte coatUseFilter = filterDTO.getCoatFilter();
        List<Integer> customizedCoatFilter = filterDTO.getCustomizedCoat();
        if (Objects.equals(coatUseFilter, ServiceEnum.FILTER_OPEN) && CollectionUtils.isEmpty(customizedCoatFilter)) {
            throw bizException(Code.CODE_PARAMS_ERROR, "coat filter could not be empty.");
        }

        // pet size check
        Boolean petSizeFilter = filterDTO.getPetSizeFilter();
        List<Long> customizedPetSizes = filterDTO.getCustomizedPetSizes();
        if (petSizeFilter != null && petSizeFilter && CollectionUtils.isEmpty(customizedPetSizes)) {
            throw bizException(Code.CODE_PARAMS_ERROR, "pet size filter could not be empty.");
        }
    }

    /**
     * 更新service、breeds的绑定关系
     * <p>
     * 1.查询当前service的所有绑定关系
     * 2.新的绑定关系：已存在的更新，不存在的插入
     * 3.老的绑定关系没在新的：更新状态为invalid
     *
     * @param businessId
     * @param serviceId
     * @param updateBreedDTOs
     */
    private void updateServiceAndBreedBinding(
            Integer businessId, Long companyId, Integer serviceId, List<ServicePetTypeBreedsDTO> updateBreedDTOs) {
        // 1.查询当前service的所有绑定关系
        List<MoeGroomingServiceBreedBinding> existingRecords =
                groomingServiceBreedBindingMapper.selectByCompanyIdAndServiceId(companyId, serviceId);
        Map<Integer, MoeGroomingServiceBreedBinding> existingBindingMap = existingRecords.stream()
                .collect(Collectors.toMap(MoeGroomingServiceBreedBinding::getPetTypeId, identity(), (b1, b2) -> b1));

        // 2.新的绑定关系：已存在的更新，不存在的插入
        List<MoeGroomingServiceBreedBinding> updateRecords = new ArrayList<>();
        List<MoeGroomingServiceBreedBinding> newRecords = new ArrayList<>();
        for (ServicePetTypeBreedsDTO dto : updateBreedDTOs) {
            MoeGroomingServiceBreedBinding saveRecord = new MoeGroomingServiceBreedBinding();
            saveRecord.setBreedNames(""); // 老字段填充空字符串表示不使用
            saveRecord.setStatus(ServiceEnum.SERVICE_BREED_BINDING_STATUS_NORMAL);
            if (dto.getIsAll() || CollectionUtils.isEmpty(dto.getBreeds())) {
                saveRecord.setIsAll(true);
                saveRecord.setBreedNameList(JsonUtil.toJson(List.of()));
            } else {
                saveRecord.setIsAll(false);
                saveRecord.setBreedNameList(JsonUtil.toJson(dto.getBreeds()));
            }
            // 使用remove方法获取，这样最后就会剩下不在新的记录里的
            MoeGroomingServiceBreedBinding existingRecord = existingBindingMap.remove(dto.getPetTypeId());
            long now = DateUtil.get10Timestamp();
            if (existingRecord != null) {
                saveRecord.setId(existingRecord.getId());
                saveRecord.setUpdateTime(now);
                updateRecords.add(saveRecord);
            } else {
                saveRecord.setBusinessId(businessId);
                saveRecord.setCompanyId(companyId);
                saveRecord.setServiceId(serviceId);
                saveRecord.setPetTypeId(dto.getPetTypeId());
                saveRecord.setCreateTime(now);
                saveRecord.setUpdateTime(now);
                newRecords.add(saveRecord);
            }
        }
        // 3.老的绑定关系没在新的记录：更新状态为 invalid
        existingBindingMap.forEach((petTypeId, record) -> {
            MoeGroomingServiceBreedBinding updateRecord = new MoeGroomingServiceBreedBinding();
            updateRecord.setId(record.getId());
            updateRecord.setStatus(ServiceEnum.SERVICE_BREED_BINDING_STATUS_INVALID);
            updateRecord.setUpdateTime(DateUtil.get10Timestamp());
            updateRecords.add(updateRecord);
        });

        // 插入、更新操作
        if (!CollectionUtils.isEmpty(newRecords)) {
            groomingServiceBreedBindingMapper.batchInsertRecords(newRecords);
        }
        if (!CollectionUtils.isEmpty(updateRecords)) {
            groomingServiceBreedBindingMapper.batchUpdateRecords(updateRecords);
        }
    }

    /**
     * service by breed权限控制：仅69以上用户可以使用
     *
     * @param businessId
     * @return
     */
    public boolean isServiceFilterAllowed(Integer businessId) {
        CompanyFunctionControlDto controlDto = iBusinessBusinessClient.queryCompanyPermissionByBusinessId(businessId);
        return (controlDto != null
                && Objects.equals(controlDto.getPremiumType(), CompanyFunctionControlConst.PREMIUM_TYPE_69));
    }

    private void updateServiceAndStaffRelation(
            Integer businessId, Long companyId, Integer serviceId, List<Integer> staffIdList) {
        // 清空之前该serviceId下的映射关系
        moeBookOnlineStaffServiceMapper.deleteByServiceId(businessId, serviceId);
        if (staffIdList.isEmpty()) {
            return;
        }
        // 添加新的映射关系
        List<MoeBookOnlineStaffService> records = new ArrayList<>();
        staffIdList.forEach(staffId -> {
            MoeBookOnlineStaffService tmpRecord = new MoeBookOnlineStaffService();
            tmpRecord.setBusinessId(businessId);
            tmpRecord.setCompanyId(companyId);
            tmpRecord.setServiceId(serviceId);
            tmpRecord.setStaffId(staffId);
            tmpRecord.setCreateTime(DateUtil.get10Timestamp());
            tmpRecord.setUpdateTime(tmpRecord.getCreateTime());
            records.add(tmpRecord);
        });
        moeBookOnlineStaffServiceMapper.batchInsertSelective(records);
    }

    public Boolean checkServiceNameIsExist(Integer businessId, String name, Byte type) {
        return checkServiceNameIsExist(businessId, name, type, null);
    }

    public Boolean checkServiceNameIsExist(Integer businessId, String name, Byte type, Integer updateServiceId) {
        return groomingServiceMapper.getServiceCountWithName(null, businessId, name, type, updateServiceId) > 0;
    }

    public Boolean checkBusinessTaxId(Integer businessId, Integer taxId) {
        MoeBusinessTaxDto tax = iBusinessTaxClient.queryTaxById(businessId, taxId);
        return (tax != null && businessId.equals(tax.getBusinessId()));
    }

    public Boolean checkCompanyTaxId(Long companyId, Integer taxId) {
        MoeBusinessTaxDto tax = iBusinessTaxClient.getTaxById(
                InfoIdParams.builder().infoId(taxId).build());
        return (tax != null && companyId.equals(tax.getCompanyId()));
    }

    public Boolean sortService(Long companyId, List<Integer> idList) {
        List<MoeGroomingService> serviceList =
                groomingServiceMapper.getServicesByCompanyIdServiceIds(companyId, idList);
        List<Integer> serviceIdList =
                serviceList.stream().map(MoeGroomingService::getId).toList();

        List<MoeGroomingService> updateServiceList = new ArrayList<>();
        int sortIndex = 0;
        for (int i = idList.size(); 0 < i; --i) {
            Integer id = idList.get(i - 1);
            if (serviceIdList.contains(id)) {
                MoeGroomingService service = new MoeGroomingService();
                service.setId(id);
                service.setSort(sortIndex);
                updateServiceList.add(service);
                ++sortIndex;
            }
        }

        if (!updateServiceList.isEmpty()) {
            groomingServiceMapper.batchUpdateSort(updateServiceList);
        }
        return true;
    }

    public Boolean deleteService(Integer serviceId, Long companyId, Integer businessId, Integer staffId) {
        // 检验service是否有绑定将来的upcoming
        ServiceDeleteParams deleteParams = new ServiceDeleteParams();
        deleteParams.setCompanyId(companyId);
        deleteParams.setServiceId(serviceId);
        deleteParams.setStaffId(staffId);
        if (isServiceBandingUpcoming(deleteParams)) {
            throw new CommonException(ResponseCodeEnum.SERVICE_HAVE_BINDING);
        }
        MoeGroomingService service = new MoeGroomingService();
        service.setId(serviceId);
        service.setCompanyId(companyId);
        service.setStatus(DeleteStatusEnum.STATUS_DELETE);
        service.setUpdateTime(DateUtil.get10Timestamp());
        groomingServiceMapper.updateByPrimaryKeySelectiveWithBidCid(service);
        // delete save price
        Map<Integer, MoeBusinessDto> businessDtoMap = iBusinessBusinessClient.getBusinessByCompanyId(
                deleteParams.getCompanyId().intValue());
        for (MoeBusinessDto businessDto : businessDtoMap.values()) {
            CustomerDeleteSaveServiceVo deleteVo = new CustomerDeleteSaveServiceVo();
            deleteVo.setServiceId(serviceId);
            customerServicesService.deleteCustomerService(
                    businessDto.getCompanyId().longValue(), businessDto.getId(), deleteVo);
        }
        // remove service filter in add-on setting
        serviceManagementServiceClient.removeServiceFilter(RemoveServiceFilterRequest.newBuilder()
                .setCompanyId(companyId)
                .setServiceId(serviceId)
                .build());
        return true;
    }

    /**
     * 根据businessId、serviceIds查询service信息，businessId作为查询条件，增加一层校验
     *
     * @param businessId
     * @param serviceIds
     * @return
     */
    public List<MoeGroomingServiceDTO> getServicesByServiceIds(Integer businessId, List<Integer> serviceIds) {
        if (serviceIds == null || serviceIds.isEmpty()) {
            return Collections.emptyList();
        }
        List<MoeGroomingService> moeGroomingServiceList;
        if (businessId == null) {
            moeGroomingServiceList = groomingServiceMapper.getServicesByServiceIds(serviceIds);
        } else {
            moeGroomingServiceList = companyGroomingServiceQueryService.groomingServiceSelectByBusinessIdServiceIds(
                    businessId, serviceIds);
        }

        List<MoeGroomingServiceDTO> moeGroomingServiceDTOList = new ArrayList<>();
        for (MoeGroomingService moeGroomingService : moeGroomingServiceList) {
            moeGroomingServiceDTOList.add(
                    GroomingServiceMapper.INSTANCE.entityToMoeGroomingServiceDTO(moeGroomingService));
        }
        return moeGroomingServiceDTOList;
    }

    /**
     * 查询结果包含已删除 service
     */
    public Map<Integer, MoeGroomingService> getServiceMap(Integer businessId, List<Integer> serviceIds) {
        if (CollectionUtils.isEmpty(serviceIds)) {
            return Map.of();
        }
        List<MoeGroomingService> serviceList =
                companyGroomingServiceQueryService.groomingServiceSelectByBusinessIdServiceIds(businessId, serviceIds);
        return serviceList.stream().collect(Collectors.toMap(MoeGroomingService::getId, service -> service));
    }

    /**
     * 计算totalDuration， 有副作用
     *
     * @param businessId
     * @param customerId
     * @param petServices 调用后该数组被修改
     * @param services
     * @return
     */
    public Integer getServicesWithCustomize(
            Integer businessId,
            Integer customerId,
            Map<Integer, List<Integer>> petServices,
            List<Integer> serviceIds,
            List<MoeGroomingServiceDTO> services) {
        List<Integer> petIds = new ArrayList<>(petServices.keySet());
        Integer totalDuration = 0;
        if (!petIds.isEmpty() && !PrimitiveTypeUtil.isNullOrZero(customerId)) {
            List<MoeGroomingCustomerServices> customerServices =
                    companyGroomingServiceQueryService.getCustomizeServices(
                            businessId, customerId, petIds, serviceIds, ServiceEnum.SAVE_TYPE_TIME);
            for (Map.Entry<Integer, List<Integer>> entry : petServices.entrySet()) {
                Integer petId = entry.getKey();
                List<Integer> selectedSrvs = entry.getValue();
                for (Integer selectedSrv : selectedSrvs) {
                    // get customized srv if existing
                    MoeGroomingCustomerServices customSrv = getByPetAndServiceIds(petId, selectedSrv, customerServices);
                    if (customSrv != null) {
                        totalDuration += customSrv.getServiceTime();
                    } else {
                        totalDuration += Optional.ofNullable(getByServiceId(selectedSrv, services))
                                .map(MoeGroomingServiceDTO::getDuration)
                                .orElse(0);
                    }
                }
            }
        }
        return totalDuration;
    }

    public CalculateServiceAmountDTO getCustomizedServicePriceAndTax(
            Integer businessId,
            List<BookOnlinePetParams> petData,
            @Nullable String discountCode,
            @Nullable Integer staffId, // auto assign/BD 会传 null
            @Nullable Integer customerId) {

        var companyId = companyHelper.mustGetCompanyId(businessId);

        var customizedServiceList = listCustomizedService(companyId, businessId, staffId, petData);
        if (customizedServiceList.isEmpty()) {
            return emptyCalculateServiceAmountDTO();
        }

        // pricing rule 的计算依赖 pet id，给 new pet 生成一个 virtual id
        petData = generateVirtualIdForNewPets(petData);

        petData = hasDaycare(customizedServiceList)
                ? getFirstDatePetDetailForDaycare(petData) // 如果是 daycare service，只计算第一天
                : petData;

        var serviceChargeList = getAutoApplyServiceCharges(businessId, customizedServiceList);

        var taxIdToTax = getTaxMap(serviceChargeList, customizedServiceList);

        var petIdToServiceList = new HashMap<Integer, List<MoeGroomingServiceDTO>>(); // TODO(Freeman): 上线之后下个版本删除
        var services = new ArrayList<MoeGroomingServiceDTO>();
        var serviceAmountDTOList = new ArrayList<CalculateServiceAmount>();

        fillForServices(
                petData,
                staffId,
                customizedServiceList,
                petIdToServiceList, // side effect
                services, // side effect
                serviceAmountDTOList, // side effect
                taxIdToTax);

        fillForAddons(
                petData,
                staffId,
                customizedServiceList,
                petIdToServiceList, // side effect
                services, // side effect
                serviceAmountDTOList, // side effect
                taxIdToTax);

        applyPricingRule(serviceAmountDTOList, companyId, businessId, petData, petIdToServiceList); // side effect

        BigDecimal totalPrice = serviceAmountDTOList.stream()
                .map(CalculateServiceAmount::serviceAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        BigDecimal totalServiceCharge = BigDecimal.ZERO;
        if (!CollectionUtils.isEmpty(serviceChargeList)) {
            for (ServiceChargeDTO serviceCharge : serviceChargeList) {
                BigDecimal serviceChargePrice = serviceCharge.getPrice();
                totalServiceCharge = totalServiceCharge.add(serviceChargePrice);
                serviceAmountDTOList.add(new CalculateServiceAmount(
                        0,
                        serviceChargePrice,
                        taxIdToTax.get(serviceCharge.getTaxId()),
                        0,
                        ServiceOverrideType.SERVICE_OVERRIDE_TYPE_UNSPECIFIED,
                        null));
            }
        }

        BigDecimal totalDiscount = BigDecimal.ZERO;
        BigDecimal totalTax = BigDecimal.ZERO;
        List<Long> usedMembershipIds = List.of();
        if (StringUtils.hasText(discountCode)) {
            GetDiscountCodeByCodeOutput discountCodeOutput =
                    discountCodeClient.getDiscountCodeByCode(GetDiscountCodeByCodeInput.newBuilder()
                            .setBusinessId(businessId)
                            .setDiscountCode(discountCode)
                            .build());
            DiscountCodeModel discountCodeModel = discountCodeOutput.getDiscountCodeModel();
            BigDecimal discountCodeAmount = BigDecimal.valueOf(discountCodeModel.getAmount());
            BigDecimal totalPriceWithCharges = totalPrice.add(totalServiceCharge);
            BigDecimal nowDiscountAmount = discountCodeAmount.compareTo(totalPriceWithCharges) > 0
                    ? totalPriceWithCharges
                    : discountCodeAmount;
            if (discountCodeModel.getAllowedAllThing() || discountCodeModel.getAllowedAllServices()) {
                Pair<BigDecimal, BigDecimal> extracted = getDiscountAndTaxAmount(
                        discountCodeModel, nowDiscountAmount, totalPriceWithCharges, serviceAmountDTOList);
                totalDiscount = totalDiscount.add(extracted.getFirst());
                totalTax = totalTax.add(extracted.getSecond());
                // 未使用 discount code 的 service list
                serviceAmountDTOList.clear();
            } else if (!CollectionUtils.isEmpty(discountCodeModel.getServiceIdsList())
                    || !CollectionUtils.isEmpty(discountCodeModel.getAddOnIdsList())) {
                List<CalculateServiceAmount> useDiscountCodeServiceList = serviceAmountDTOList.stream()
                        .filter(service -> Stream.concat(
                                        discountCodeModel.getServiceIdsList().stream(),
                                        discountCodeModel.getAddOnIdsList().stream())
                                .anyMatch(id -> id == service.serviceId()))
                        .toList();
                Pair<BigDecimal, BigDecimal> extracted = getDiscountAndTaxAmount(
                        discountCodeModel, nowDiscountAmount, totalPrice, useDiscountCodeServiceList);
                totalDiscount = totalDiscount.add(extracted.getFirst());
                totalTax = totalTax.add(extracted.getSecond());
                // 未使用 discount code 的 service list
                serviceAmountDTOList.removeAll(useDiscountCodeServiceList);
            }
        } else {
            CalculateResult calculateResult =
                    membershipService.useMembership(businessId, companyId, customerId, serviceAmountDTOList);
            if (calculateResult.totalDiscount().compareTo(BigDecimal.ZERO) > 0) {
                usedMembershipIds = calculateResult.usedMembershipIds();
                totalDiscount = totalDiscount.add(calculateResult.totalDiscount());
                totalTax = totalTax.add(calculateResult.totalTax());
                // 未使用 discount code 的 service list
                serviceAmountDTOList.removeAll(calculateResult.usedMembership());
            }
        }

        for (CalculateServiceAmount service : serviceAmountDTOList) {
            totalTax = totalTax.add(calculateServiceTax(service.serviceAmount(), service.taxRate()));
        }

        return CalculateServiceAmountDTO.builder()
                .serviceAmount(totalPrice)
                .taxAmount(totalTax)
                .serviceChargeAmount(totalServiceCharge)
                .discountAmount(totalDiscount)
                .serviceChargeList(serviceChargeList)
                .petServicesMap(petIdToServiceList)
                .services(services)
                .usedMembershipIds(usedMembershipIds)
                .build();
    }

    private List<ServiceChargeDTO> getAutoApplyServiceCharges(
            Integer businessId,
            List<BatchGetCustomizedServiceResponse.ServiceWithCustomizedInfo> customizedServiceList) {

        var serviceItemTypes = customizedServiceList.stream()
                .map(e -> e.getCustomizedService().getServiceItemType())
                .collect(Collectors.toSet());

        // 按照 service item type 来过滤 service charge
        return serviceChargeService.getMandatoryServiceChargeList(businessId).stream()
                .filter(serviceCharge -> ObjectUtils.isEmpty(serviceCharge.getServiceItemTypes())
                        || serviceItemTypes.stream()
                                .anyMatch(serviceItemType ->
                                        serviceCharge.getServiceItemTypes().contains(serviceItemType)))
                .toList();
    }

    private static void fillForAddons(
            List<BookOnlinePetParams> petData,
            Integer staffId,
            List<BatchGetCustomizedServiceResponse.ServiceWithCustomizedInfo> customizedServiceList,
            HashMap<Integer, List<MoeGroomingServiceDTO>> petIdToServiceList,
            ArrayList<MoeGroomingServiceDTO> services,
            ArrayList<CalculateServiceAmount> serviceAmountDTOList,
            Map<Integer, BigDecimal> taxIdToTax) {

        for (var petAndAddonList : buildPetToAddonsMap(petData).entrySet()) {
            var pet = petAndAddonList.getKey();
            for (var addon : petAndAddonList.getValue()) {
                var petId = !pet.isVirtualPetId() ? pet.getPetId() : null;
                var customizedService = findCustomizedService(customizedServiceList, addon.getId(), staffId, petId);
                if (customizedService == null) {
                    continue;
                }

                petIdToServiceList
                        .computeIfAbsent(pet.getPetId(), k -> new ArrayList<>())
                        .add(GroomingServiceMapper.INSTANCE.customizedServiceViewToDTO(customizedService));

                services.add(GroomingServiceMapper.INSTANCE.customizedServiceViewToDTO(customizedService));

                for (int i = 0; i < calculateCount(pet, addon); i++) {
                    serviceAmountDTOList.add(CalculateServiceAmount.builder()
                            .serviceId(Math.toIntExact(customizedService.getId()))
                            .serviceAmount(BigDecimal.valueOf(customizedService.getPrice()))
                            .taxRate(taxIdToTax.get(Math.toIntExact(customizedService.getTaxId())))
                            .petId(pet.getPetId())
                            .priceOverrideType(customizedService.getPriceOverrideType())
                            .date(null)
                            .build());
                }
            }
        }
    }

    private static void fillForServices(
            List<BookOnlinePetParams> petData,
            @Nullable Integer staffId,
            List<BatchGetCustomizedServiceResponse.ServiceWithCustomizedInfo> customizedServiceList,
            HashMap<Integer, List<MoeGroomingServiceDTO>> petIdToServiceList,
            ArrayList<MoeGroomingServiceDTO> services,
            ArrayList<CalculateServiceAmount> serviceAmountDTOList,
            Map<Integer, BigDecimal> taxIdToTax) {

        for (var petAndServiceIdList : buildPetToServicesMap(petData).entrySet()) {
            var pet = petAndServiceIdList.getKey();
            for (var serviceId : petAndServiceIdList.getValue()) {
                // 这里注意，只有在查询 customized service 时，才需要判断是否为 virtual pet id
                var petId = !pet.isVirtualPetId() ? pet.getPetId() : null;
                var customizedService = findCustomizedService(customizedServiceList, serviceId, staffId, petId);
                if (customizedService == null) {
                    continue;
                }

                petIdToServiceList
                        .computeIfAbsent(pet.getPetId(), k -> new ArrayList<>())
                        .add(GroomingServiceMapper.INSTANCE.customizedServiceViewToDTO(customizedService));

                services.add(GroomingServiceMapper.INSTANCE.customizedServiceViewToDTO(customizedService));

                var start = pet.getStartDate();
                var end = Optional.ofNullable(pet.getEndDate()).orElseGet(pet::getStartDate);
                if (!StringUtils.hasText(start) || !StringUtils.hasText(end)) { // grooming 可能不会传这个值
                    serviceAmountDTOList.add(CalculateServiceAmount.builder()
                            .serviceId(Math.toIntExact(customizedService.getId()))
                            .serviceAmount(BigDecimal.valueOf(customizedService.getPrice()))
                            .taxRate(taxIdToTax.get(Math.toIntExact(customizedService.getTaxId())))
                            .petId(pet.getPetId())
                            .priceOverrideType(customizedService.getPriceOverrideType())
                            .date(null)
                            .build());

                } else {

                    var endDate = isCalculateByNight(customizedService)
                            ? LocalDate.parse(end)
                            : LocalDate.parse(end).plusDays(1);

                    for (var date : LocalDate.parse(start).datesUntil(endDate).toList()) {
                        serviceAmountDTOList.add(CalculateServiceAmount.builder()
                                .serviceId(Math.toIntExact(customizedService.getId()))
                                .serviceAmount(BigDecimal.valueOf(customizedService.getPrice()))
                                .taxRate(taxIdToTax.get(Math.toIntExact(customizedService.getTaxId())))
                                .petId(pet.getPetId())
                                .priceOverrideType(customizedService.getPriceOverrideType())
                                .date(date.toString())
                                .build());
                    }
                }
            }
        }
    }

    private static boolean isCalculateByNight(CustomizedServiceView customizedService) {
        return customizedService.getServiceItemType() == ServiceItemType.BOARDING
                && customizedService.getPriceUnit() == ServicePriceUnit.PER_NIGHT;
    }

    private Map<Integer, BigDecimal> getTaxMap(
            List<ServiceChargeDTO> serviceChargeList,
            List<BatchGetCustomizedServiceResponse.ServiceWithCustomizedInfo> customizedServiceList) {
        var taxIds = new HashSet<Integer>();

        for (var serviceCharge : serviceChargeList) {
            taxIds.add(serviceCharge.getTaxId());
        }

        for (var service : customizedServiceList) {
            taxIds.add(Math.toIntExact(service.getCustomizedService().getTaxId()));
        }

        return getTaxMap(taxIds);
    }

    private static CalculateServiceAmountDTO emptyCalculateServiceAmountDTO() {
        return CalculateServiceAmountDTO.builder()
                .serviceAmount(BigDecimal.ZERO)
                .taxAmount(BigDecimal.ZERO)
                .serviceChargeAmount(BigDecimal.ZERO)
                .discountAmount(BigDecimal.ZERO)
                .serviceChargeList(List.of())
                .petServicesMap(Map.of())
                .services(List.of())
                .build();
    }

    private static int calculateCount(BookOnlinePetParams pet, BookOnlinePetParams.Addon addon) {
        if (addon.getIsEveryDay() == null && addon.getDateType() == null) { // grooming
            return 1;
        }

        // boarding
        if (addon.getDateType() != null) {
            var start = pet.getStartDate();
            if (!StringUtils.hasText(start)) {
                throw bizException(Code.CODE_PARAMS_ERROR, "start date is empty");
            }
            var end = Optional.ofNullable(pet.getEndDate())
                    .filter(StringUtils::hasText)
                    .orElse(start);

            var dateType = Optional.ofNullable(PetDetailDateType.forNumber(addon.getDateType()))
                    .orElseThrow(
                            () -> bizException(Code.CODE_PARAMS_ERROR, "date type is invalid: " + addon.getDateType()));

            long days =
                    switch (dateType) {
                        case PET_DETAIL_DATE_EVERYDAY -> LocalDate.parse(start)
                                .datesUntil(LocalDate.parse(end))
                                .count();
                        case PET_DETAIL_DATE_EVERYDAY_INCLUDE_CHECKOUT_DAY -> LocalDate.parse(start)
                                .datesUntil(LocalDate.parse(end).plusDays(1))
                                .count();
                        case PET_DETAIL_DATE_SPECIFIC_DATE -> Optional.ofNullable(addon.getDates()).stream()
                                .mapToLong(Collection::size)
                                .sum();
                        case PET_DETAIL_DATE_DATE_POINT -> 1;
                        default -> throw bizException(
                                Code.CODE_PARAMS_ERROR, "date type is invalid: " + addon.getDateType());
                    };

            return (int) (days * getQuantityPerDay(addon));
        }

        // daycare
        var start = pet.getStartDate();
        if (!StringUtils.hasText(start)) {
            throw bizException(Code.CODE_PARAMS_ERROR, "start date is empty");
        }

        if (addon.getIsEveryDay()) {
            return getQuantityPerDay(addon);
        }

        var dates = Optional.ofNullable(addon.getDates()).orElse(List.of());
        return dates.contains(start) // 选择的 addon 必须在 daycare service 日期范围内
                ? getQuantityPerDay(addon)
                : 0;
    }

    private static int getQuantityPerDay(BookOnlinePetParams.Addon addon) {
        return addon.getQuantityPerDay() != null ? addon.getQuantityPerDay() : 1;
    }

    private static List<BookOnlinePetParams> getFirstDatePetDetailForDaycare(List<BookOnlinePetParams> petData) {

        var firstDate = petData.stream()
                .map(BookOnlinePetParams::getStartDate)
                .filter(StringUtils::hasText)
                .sorted()
                .findFirst()
                .orElseThrow(() -> bizException(Code.CODE_PARAMS_ERROR, "start date is empty"));

        return petData.stream()
                .filter(e -> Objects.equals(e.getStartDate(), firstDate))
                .toList();
    }

    private static IdentityHashMap<BookOnlinePetParams, List<Integer>> buildPetToServicesMap(
            List<BookOnlinePetParams> petData) {
        var petToServices = new IdentityHashMap<BookOnlinePetParams, List<Integer>>();
        for (var pet : petData) {
            var services = petToServices.computeIfAbsent(pet, k -> new ArrayList<>());
            if (isNormal(pet.getServiceId())) {
                services.add(pet.getServiceId());
            }
        }
        return petToServices;
    }

    private static IdentityHashMap<BookOnlinePetParams, List<BookOnlinePetParams.Addon>> buildPetToAddonsMap(
            List<BookOnlinePetParams> petData) {

        var petToAddons = new IdentityHashMap<BookOnlinePetParams, List<BookOnlinePetParams.Addon>>();

        for (var pet : petData) {
            var addons = petToAddons.computeIfAbsent(pet, k -> new ArrayList<>());
            if (pet.getAddons() != null) { // 优先使用新字段，如果新字段为空，向后兼容使用旧字段
                addons.addAll(pet.getAddons());
            } else if (pet.getAddOnIds() != null) {
                for (var addOnId : pet.getAddOnIds()) {
                    var addon = new BookOnlinePetParams.Addon();
                    addon.setId(addOnId);
                    addons.add(addon);
                }
            }
        }

        return petToAddons;
    }

    private static boolean hasDaycare(
            List<BatchGetCustomizedServiceResponse.ServiceWithCustomizedInfo> customizedServiceList) {
        return customizedServiceList.stream()
                .anyMatch(e -> e.getCustomizedService().getServiceItemType() == ServiceItemType.DAYCARE);
    }

    /**
     * @param serviceAmountDTOList 有 side effect，会修改 serviceAmountDTOList
     */
    private void applyPricingRule(
            ArrayList<CalculateServiceAmount> serviceAmountDTOList,
            long companyId,
            int businessId,
            List<BookOnlinePetParams> petData,
            HashMap<Integer, List<MoeGroomingServiceDTO>> petIdToServiceList) {

        var petDetails = listPetDetailWithPricingRuleApplied(
                companyId, businessId, petData, petIdToServiceList, serviceAmountDTOList);
        if (petDetails.isEmpty()) {
            return;
        }

        for (int i = 0; i < serviceAmountDTOList.size(); i++) {
            // 如果 pet id 和 service id match，替换为 pricing rule 的价格
            // 并且将 serviceAmountDTOList 中的对象替换为新的
            var calculateServiceAmount = serviceAmountDTOList.get(i);
            if (calculateServiceAmount.priceOverrideType() == ServiceOverrideType.CLIENT) {
                // override by client 优先级比 pricing rule 更高
                // See
                // https://moego.atlassian.net/wiki/spaces/~************************/pages/*********/Service+by+Staff+-+PRD#Detail
                continue;
            }

            var petDetail = petDetails.stream()
                    .filter(e -> e.getPetId() == calculateServiceAmount.petId() // 这里需要 virtual pet id
                            && e.getServiceId() == calculateServiceAmount.serviceId()
                            && Objects.equals(e.getServiceDate(), calculateServiceAmount.date()))
                    .findFirst()
                    .orElse(null);
            if (petDetail == null) {
                continue;
            }

            serviceAmountDTOList.set(
                    i,
                    calculateServiceAmount.toBuilder()
                            .serviceAmount(BigDecimal.valueOf(petDetail.getAdjustedPrice()))
                            .build());
        }
    }

    private List<PetDetailCalculateResultDef> listPetDetailWithPricingRuleApplied(
            long companyId,
            int businessId,
            List<BookOnlinePetParams> petDetails,
            Map<Integer, List<MoeGroomingServiceDTO>> petIdToServices,
            Collection<CalculateServiceAmount> serviceAmountDTOList) {

        var bdServiceIds = petIdToServices.values().stream()
                .flatMap(Collection::stream)
                .filter(e -> e.getServiceItemType() == ServiceItemType.BOARDING
                        || e.getServiceItemType() == ServiceItemType.DAYCARE) // pricing rule 只适用于 boarding 和 daycare
                .map(MoeGroomingServiceDTO::getId)
                .collect(Collectors.toSet());

        Map<Pair<Integer /* pet id */, Integer /* service id */>, MoeGroomingServiceDTO> petIdAndServiceIdToService =
                petIdToServices.entrySet().stream()
                        .flatMap(entry -> entry.getValue().stream()
                                .filter(service -> service.getServiceItemType() == ServiceItemType.BOARDING
                                        || service.getServiceItemType()
                                                == ServiceItemType
                                                        .DAYCARE) // pricing rule only applies to boarding and daycare
                                .map(service -> {
                                    var pair = Pair.create(entry.getKey(), service.getId());
                                    return Map.entry(pair, service);
                                }))
                        .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));

        if (bdServiceIds.isEmpty()) {
            return List.of();
        }

        var petIdToPetDetails = petDetails.stream()
                .filter(e -> bdServiceIds.contains(e.getServiceId()))
                .collect(groupingBy(BookOnlinePetParams::getPetId));

        var defs = new ArrayList<PetDetailCalculateDef>();

        for (var en : petIdToPetDetails.entrySet()) {
            for (var petDetail : en.getValue()) {
                boolean hasCustomizedPrice = serviceAmountDTOList.stream()
                        .filter(e -> e.petId() == petDetail.getPetId() && e.serviceId() == petDetail.getServiceId())
                        .anyMatch(e -> Objects.equals(ServiceOverrideType.CLIENT, e.priceOverrideType()));
                if (hasCustomizedPrice) {
                    continue;
                }

                var startDate = petDetail.getStartDate();
                var endDate = Optional.ofNullable(petDetail.getEndDate()).orElseGet(petDetail::getStartDate);
                if (!StringUtils.hasText(startDate) || !StringUtils.hasText(endDate)) {
                    continue;
                }

                var service =
                        petIdAndServiceIdToService.get(Pair.create(petDetail.getPetId(), petDetail.getServiceId()));
                if (service == null) {
                    continue;
                }

                var end = isCalculateByNight(service)
                        ? LocalDate.parse(endDate)
                        : LocalDate.parse(endDate).plusDays(1);

                for (var date : LocalDate.parse(startDate).datesUntil(end).toList()) {
                    var builder = PetDetailCalculateDef.newBuilder();
                    builder.setPetId(en.getKey());
                    builder.setServiceId(petDetail.getServiceId());
                    builder.setServicePrice(service.getPrice().doubleValue());
                    builder.setServiceDate(date.toString());
                    defs.add(builder.build());
                }
            }
        }

        if (defs.isEmpty()) {
            return List.of();
        }

        return pricingRuleServiceStub
                .calculatePricingRule(com.moego.idl.service.offering.v2.CalculatePricingRuleRequest.newBuilder()
                        .addAllPetDetails(defs)
                        .setCompanyId(companyId)
                        .build())
                .getPetDetailsList();
    }

    private static boolean isCalculateByNight(MoeGroomingServiceDTO service) {
        return service.getServiceItemType() == ServiceItemType.BOARDING
                && service.getPriceUnit() == ServicePriceUnit.PER_NIGHT;
    }

    private static List<BookOnlinePetParams> generateVirtualIdForNewPets(List<BookOnlinePetParams> petDetails) {

        var virtualId = petDetails.stream()
                        .map(BookOnlinePetParams::getPetId)
                        .filter(CommonUtil::isNormal)
                        .max(Integer::compareTo)
                        .orElse(0)
                + 1;

        var newPetToId = new HashMap<BookOnlinePetParams, Integer>();
        for (var petDetail : petDetails) {
            if (!isNormal(petDetail.getPetId())) {
                newPetToId.putIfAbsent(getNewPet(petDetail), virtualId++);
            }
        }

        var result = new ArrayList<BookOnlinePetParams>();

        for (var petDetail : petDetails) {
            var pet = new BookOnlinePetParams();
            BeanUtils.copyProperties(petDetail, pet);
            if (!isNormal(pet.getPetId())) {
                pet.setPetId(newPetToId.get(getNewPet(petDetail)));
                pet.setVirtualPetId(true);
            }
            result.add(pet);
        }

        return result;
    }

    private static BookOnlinePetParams getNewPet(BookOnlinePetParams e) {
        var pet = new BookOnlinePetParams();
        pet.setAvatarPath(e.getAvatarPath());
        pet.setPetName(e.getPetName());
        pet.setBreed(e.getBreed());
        pet.setBreedMix(e.getBreedMix());
        pet.setPetTypeId(e.getPetTypeId());
        pet.setGender(e.getGender());
        pet.setBirthday(e.getBirthday());
        pet.setWeight(e.getWeight());
        pet.setFixed(e.getFixed());
        pet.setBehavior(e.getBehavior());
        pet.setHairLength(e.getHairLength());
        pet.setExpiryNotification(e.getExpiryNotification());
        pet.setVetName(e.getVetName());
        pet.setVetPhone(e.getVetPhone());
        pet.setVetAddress(e.getVetAddress());
        pet.setEmergencyContactName(e.getEmergencyContactName());
        pet.setEmergencyContactPhone(e.getEmergencyContactPhone());
        pet.setHealthIssues(e.getHealthIssues());
        pet.setVaccineList(e.getVaccineList());
        pet.setPetImage(e.getPetImage());
        pet.setPetQuestionAnswers(e.getPetQuestionAnswers());
        return pet;
    }

    private List<BatchGetCustomizedServiceResponse.ServiceWithCustomizedInfo> listCustomizedService(
            long companyId, Integer businessId, @Nullable Integer staffId, List<BookOnlinePetParams> petData) {

        var builder = BatchGetCustomizedServiceRequest.newBuilder();

        builder.setCompanyId(companyId);

        for (var petAndServiceList : buildPetToServicesMap(petData).entrySet()) {
            var pet = petAndServiceList.getKey();
            for (var serviceId : petAndServiceList.getValue()) {
                var condBuilder = CustomizedServiceQueryCondition.newBuilder()
                        .setServiceId(serviceId)
                        .setBusinessId(businessId);
                if (isNormal(pet.getPetId())) {
                    condBuilder.setPetId(pet.getPetId());
                }
                if (isNormal(staffId)) {
                    condBuilder.setStaffId(staffId);
                }
                builder.addQueryConditionList(condBuilder.build());
            }
        }

        for (var petAndAddonList : buildPetToAddonsMap(petData).entrySet()) {
            var pet = petAndAddonList.getKey();
            for (var addon : petAndAddonList.getValue()) {
                var condBuilder = CustomizedServiceQueryCondition.newBuilder()
                        .setServiceId(addon.getId())
                        .setBusinessId(businessId);
                if (isNormal(pet.getPetId())) {
                    condBuilder.setPetId(pet.getPetId());
                }
                if (isNormal(staffId)) {
                    condBuilder.setStaffId(staffId);
                }
                builder.addQueryConditionList(condBuilder.build());
            }
        }

        if (builder.getQueryConditionListList().isEmpty()) {
            return List.of();
        }

        return serviceManagementServiceClient
                .batchGetCustomizedService(builder.build())
                .getCustomizedServiceListList();
    }

    private Map<Integer, BigDecimal> getTaxMap(Collection<Integer> taxIdList) {
        return iBusinessTaxClient
                .getTaxListByIds(taxIdList.stream()
                        .filter(CommonUtil::isNormal)
                        .distinct()
                        .toList())
                .stream()
                .collect(Collectors.toMap(MoeBusinessTaxDto::getId, tax -> BigDecimal.valueOf(tax.getTaxRate())));
    }

    private Pair<BigDecimal, BigDecimal> getDiscountAndTaxAmount(
            DiscountCodeModel discountCodeModel,
            BigDecimal nowDiscountAmount,
            BigDecimal totalPrice,
            List<CalculateServiceAmount> serviceAmountDTOList) {

        BigDecimal totalDiscount = BigDecimal.ZERO;
        BigDecimal totalTax = BigDecimal.ZERO;

        DiscountCodeType discountCodeType = discountCodeModel.getType();
        if (DiscountCodeType.DISCOUNT_CODE_TYPE_PERCENTAGE.equals(discountCodeType)) {
            for (CalculateServiceAmount serviceAmountDTO : serviceAmountDTOList) {
                BigDecimal currentDiscountAmount = serviceAmountDTO
                        .serviceAmount()
                        .multiply(nowDiscountAmount)
                        .divide(BigDecimal.valueOf(100), RoundingMode.HALF_EVEN);
                totalDiscount = totalDiscount.add(currentDiscountAmount);
                BigDecimal currentSubTotal = serviceAmountDTO.serviceAmount().subtract(currentDiscountAmount);
                totalTax = totalTax.add(calculateServiceTax(currentSubTotal, serviceAmountDTO.taxRate()));
            }
        } else if (DiscountCodeType.DISCOUNT_CODE_TYPE_FIXED_AMOUNT.equals(discountCodeType)) {
            BigDecimal tempDiscountAmount = nowDiscountAmount;
            for (int i = 0; i < serviceAmountDTOList.size(); i++) {
                CalculateServiceAmount serviceAmountDTO = serviceAmountDTOList.get(i);
                if (i == serviceAmountDTOList.size() - 1) {
                    totalDiscount = totalDiscount.add(tempDiscountAmount);
                    BigDecimal currentSubTotal =
                            serviceAmountDTO.serviceAmount().subtract(tempDiscountAmount);
                    totalTax = totalTax.add(calculateServiceTax(currentSubTotal, serviceAmountDTO.taxRate()));
                } else {
                    BigDecimal currentDiscountAmount = serviceAmountDTO
                            .serviceAmount()
                            .multiply(nowDiscountAmount)
                            .divide(totalPrice, RoundingMode.HALF_EVEN);
                    tempDiscountAmount = tempDiscountAmount.subtract(currentDiscountAmount);
                    totalDiscount = totalDiscount.add(currentDiscountAmount);
                    BigDecimal currentSubTotal =
                            serviceAmountDTO.serviceAmount().subtract(currentDiscountAmount);
                    totalTax = totalTax.add(calculateServiceTax(currentSubTotal, serviceAmountDTO.taxRate()));
                }
            }
        }

        return new Pair<>(totalDiscount, totalTax);
    }

    /**
     * 计算service tax
     *
     * @param servicePrice 服务价格
     * @param taxRate      tax rate，百分比，需要除以100计算
     * @return
     */
    public static BigDecimal calculateServiceTax(BigDecimal servicePrice, BigDecimal taxRate) {
        if (servicePrice == null || taxRate == null || taxRate.compareTo(BigDecimal.ZERO) == 0) {
            return BigDecimal.ZERO;
        }
        return servicePrice.multiply(taxRate.scaleByPowerOfTen(-2)).setScale(2, RoundingMode.HALF_UP);
    }

    private MoeGroomingCustomerServices getByPetAndServiceIds(
            Integer petId, Integer serviceId, List<MoeGroomingCustomerServices> customerServices) {
        return customerServices.stream()
                .filter(s -> Objects.equals(petId, s.getPetId()) && Objects.equals(serviceId, s.getServiceId()))
                .findFirst()
                .orElse(null);
    }

    private MoeGroomingServiceDTO getByServiceId(Integer serviceId, List<MoeGroomingServiceDTO> serviceDTOS) {
        return serviceDTOS.stream()
                .filter(s -> Objects.equals(serviceId, s.getId()))
                .findFirst()
                .orElse(null);
    }

    @Nullable
    private static CustomizedServiceView findCustomizedService(
            List<BatchGetCustomizedServiceResponse.ServiceWithCustomizedInfo> customizedServiceList,
            int serviceId,
            @Nullable Integer staffId,
            @Nullable Integer petId) {
        return customizedServiceList.stream()
                .filter(e -> {
                    var cond = e.getQueryCondition();
                    return serviceId == cond.getServiceId()
                            && (!isNormal(petId) && !isNormal(cond.getPetId())
                                    || isNormal(petId) && petId == cond.getPetId())
                            && (!isNormal(staffId) && !isNormal(cond.getStaffId())
                                    || isNormal(staffId) && staffId == cond.getStaffId());
                })
                .findFirst()
                .map(BatchGetCustomizedServiceResponse.ServiceWithCustomizedInfo::getCustomizedService)
                .orElse(null);
    }

    public List<MoeBookOnlineStaffService> getServiceStaffByServiceId(Integer businessId, Integer serviceId) {
        return moeBookOnlineStaffServiceMapper.selectByBusinessId(businessId, serviceId);
    }

    public List<MoeBookOnlineStaffService> getServiceStaffByServiceIds(Integer businessId, List<Integer> serviceIds) {
        if (CollectionUtils.isEmpty(serviceIds)) {
            return List.of();
        }
        return moeBookOnlineStaffServiceMapper.getServiceListByServiceIdList(businessId, serviceIds);
    }

    /**
     * @param staffIdList     需要过滤的员工列表
     * @param serviceList     需要支持的服务列表
     * @param serviceStaffIds <serviceId, staffIdList> 数据库记录的 service 和 staff 的映射关系
     * @return 能够提供 serviceList 所有服务的员工列表
     */
    public List<Integer> getStaffIdsForAllService(
            List<Integer> staffIdList,
            List<MoeGroomingService> serviceList,
            Map<Integer, List<Integer>> serviceStaffIds) {
        Set<Integer> availableStaffIds = new HashSet<>(staffIdList);
        serviceList.stream()
                // add on service 不需要员工  计算多个主服务的staff 交集
                .filter(dto -> ServiceEnum.TYPE_SERVICE.equals(dto.getType()))
                // is_all_staff is on，无须过滤
                .filter(dto -> !CustomerContactEnum.IS_ALL_STAFF.equals(dto.getIsAllStaff()))
                .forEach(service -> {
                    availableStaffIds.retainAll(
                            new HashSet<>(serviceStaffIds.getOrDefault(service.getId(), List.of())));
                });
        return new ArrayList<>(availableStaffIds);
    }

    public List<ServiceCategoryDTO> queryPetServices(Long companyId, QueryServiceParams queryServiceParams) {
        // 查询所有的service和category
        List<ServiceCategoryDTO> serviceCategorys =
                companyGroomingServiceQueryService.groomingServiceCategorySelectCategoryByCidOrBid(
                        companyId, queryServiceParams.getBusinessId(), queryServiceParams.getType());
        List<PetServiceDTO> petServices =
                companyGroomingServiceQueryService.groomingServiceSelectByBusinessIdServiceIds(
                        companyId, queryServiceParams.getBusinessId(), queryServiceParams.getType());

        // 如果petId 或者 customerId为空，肯定没有save price和time， 不用覆盖
        if (queryServiceParams.getCustomerId() != null && queryServiceParams.getPetId() != null) {
            // 查询宠物是否saveprice
            CustomerServiceQueryDTO customerServiceQueryDTO = new CustomerServiceQueryDTO();
            customerServiceQueryDTO.setBusinessId(queryServiceParams.getBusinessId());
            customerServiceQueryDTO.setCustomerId(queryServiceParams.getCustomerId());
            customerServiceQueryDTO.setPetId(queryServiceParams.getPetId());
            customerServiceQueryDTO.setCompanyId(companyId);
            List<MoeGroomingCustomerServices> moeGroomingCustomerServices =
                    companyGroomingServiceQueryService.queryCustomerServiceByProperties(customerServiceQueryDTO);
            // 比对服务是否是save
            for (PetServiceDTO petServiceDTO : petServices) {
                petServiceDTO.setIsSavePrice(false);
                petServiceDTO.setIsSaveTime(false);
                for (MoeGroomingCustomerServices moeGroomingCustomerService : moeGroomingCustomerServices) {
                    if (petServiceDTO.getId().equals(moeGroomingCustomerService.getServiceId())) {
                        int saveType = moeGroomingCustomerService.getSaveType().intValue();
                        if (saveType == 1) {
                            petServiceDTO.setIsSavePrice(true);
                            petServiceDTO.setPrice(moeGroomingCustomerService.getServiceFee());
                        }
                        if (saveType == 2) {
                            petServiceDTO.setIsSaveTime(true);
                            petServiceDTO.setDuration(moeGroomingCustomerService.getServiceTime());
                        }
                    }
                }
            }
        }
        // 组装数据返回
        // 添加没有category的service组
        ServiceCategoryDTO serviceCategoryDTO = new ServiceCategoryDTO();
        serviceCategoryDTO.setSort(0);
        serviceCategoryDTO.setId(0);
        serviceCategorys.add(0, serviceCategoryDTO);

        for (ServiceCategoryDTO serviceCategory : serviceCategorys) {
            if (serviceCategory.getPetServices() == null) {
                serviceCategory.setPetServices(new ArrayList<>());
            }
            for (PetServiceDTO petServiceDTO : petServices) {
                // 过滤设置了inactive的服务，这里不下发
                if (serviceCategory.getId().equals(petServiceDTO.getCategoryId())
                        && BooleanEnum.INACTIVE_FALSE.equals(petServiceDTO.getInactive())) {
                    serviceCategory.getPetServices().add(petServiceDTO);
                }
            }
        }

        // 排序
        sortServiceAndCategory(serviceCategorys);

        return serviceCategorys;
    }

    public List<PetServiceDTO> queryApplicableService(Long companyId, QueryServiceParams queryServiceParams) {
        Integer businessId = queryServiceParams.getBusinessId();
        Integer petId = queryServiceParams.getPetId();
        Integer customerId = queryServiceParams.getCustomerId();
        List<PetServiceDTO> petServiceList =
                companyGroomingServiceQueryService.groomingServiceSelectActiveServiceByBusinessId(
                        companyId, businessId, queryServiceParams.getType());

        // 如果 petId 或者 customerId 为空，肯定没有 save price 和 time，不用覆盖
        if (Objects.nonNull(customerId) && Objects.nonNull(petId)) {
            CustomerServiceQueryDTO customerServiceQueryDTO = new CustomerServiceQueryDTO();
            customerServiceQueryDTO.setBusinessId(businessId);
            customerServiceQueryDTO.setCustomerId(customerId);
            customerServiceQueryDTO.setPetId(petId);
            customerServiceQueryDTO.setCompanyId(companyId);
            List<MoeGroomingCustomerServices> customerServiceList =
                    companyGroomingServiceQueryService.queryCustomerServiceByProperties(customerServiceQueryDTO);
            for (PetServiceDTO petServiceDTO : petServiceList) {
                petServiceDTO.setIsSavePrice(false);
                petServiceDTO.setIsSaveTime(false);
                customerServiceList.stream()
                        .filter(customerService -> petServiceDTO.getId().equals(customerService.getServiceId()))
                        .findFirst()
                        .ifPresent(customerService -> {
                            if (ServiceEnum.SAVE_TYPE_PRICE.equals(customerService.getSaveType())) {
                                petServiceDTO.setIsSavePrice(true);
                                petServiceDTO.setPrice(customerService.getServiceFee());
                            }
                            if (ServiceEnum.SAVE_TYPE_TIME.equals(customerService.getSaveType())) {
                                petServiceDTO.setIsSaveTime(true);
                                petServiceDTO.setDuration(customerService.getServiceTime());
                            }
                        });
            }
        }

        petServiceList.forEach(service -> service.setServiceFilter(Boolean.TRUE));
        if (Objects.isNull(petId) || !isServiceFilterAllowed(businessId)) {
            return petServiceList;
        }

        // 如果所有 service 都没有打开 filter 开关，则全部返回 True
        boolean allClosed = petServiceList.stream()
                .allMatch(service -> Objects.equals(service.getWeightFilter(), ServiceEnum.FILTER_CLOSE)
                        && Objects.equals(service.getBreedFilter(), ServiceEnum.FILTER_CLOSE)
                        && Objects.equals(service.getCoatFilter(), ServiceEnum.FILTER_CLOSE)
                        && Objects.equals(service.getPetSizeFilter(), ServiceEnum.FILTER_CLOSE)
                        && Objects.equals(service.getAddOnServiceFilter(), ServiceEnum.FILTER_CLOSE));
        if (allClosed) {
            return petServiceList;
        }

        // 查询 pet 信息
        List<CustomerPetDetailDTO> existingPets = iPetClient.getCustomerPetListByIdList(List.of(petId));
        if (CollectionUtils.isEmpty(existingPets)
                || Objects.equals(existingPets.get(0).getStatus(), CustomerPetEnum.STATUS_DELETE)) {
            return petServiceList;
        }
        CustomerPetDetailDTO petDetail = existingPets.get(0);
        PetDataForServiceParams petData = new PetDataForServiceParams();
        petData.setBreed(petDetail.getBreed());
        petData.setWeight(petDetail.getWeight());
        petData.setPetTypeId(petDetail.getPetTypeId());
        petData.setCoat(petDetail.getHairLength());

        MigrateInfo migrateInfo = migrateHelper.getMigrationInfo(businessId);
        if (migrateInfo != null && migrateInfo.isMigrate()) {
            setPetSizeId(companyId, List.of(petData));
        }

        // 查询 breed 绑定关系
        Map<Integer, List<MoeGroomingServiceBreedBinding>> serviceBreedMap =
                getServiceBreedBindingMap(companyId, businessId);
        // 查询 coat 绑定关系
        Map<Integer, List<String>> serviceCoatMap =
                companyGroomingServiceQueryService.getServiceCoatStringBindingMapNew(companyId, businessId);
        // 查询 addon service 绑定关系
        Set<Integer> groomingAddonIds = getGroomingAddons(companyId);

        for (PetServiceDTO service : petServiceList) {
            if (isApplicableService(serviceBreedMap, serviceCoatMap, groomingAddonIds, petData, service)) {
                continue;
            }
            service.setServiceFilter(Boolean.FALSE);
        }

        return petServiceList;
    }

    public List<ApplicableServiceByCategoryDTO> queryApplicableServiceV2(
            Long companyId, QueryServiceParams queryServiceParams) {
        List<PetServiceDTO> petServiceList = queryApplicableService(companyId, queryServiceParams);
        List<MoeGroomingServiceCategory> serviceCategoryList =
                companyGroomingServiceQueryService.groomingServiceCategorySelectByBusinessId(
                        companyId,
                        queryServiceParams.getBusinessId(),
                        Objects.isNull(queryServiceParams.getType())
                                ? null
                                : queryServiceParams.getType().byteValue(),
                        ServiceItemType.GROOMING);
        MoeGroomingServiceCategory defaultCategory = new MoeGroomingServiceCategory();
        defaultCategory.setId(0);
        defaultCategory.setName(GroomingCategoryConstant.UNCATEGORIZED);
        serviceCategoryList.add(0, defaultCategory);
        Map<Integer, List<PetServiceDTO>> serviceMap =
                petServiceList.stream().collect(groupingBy(PetServiceDTO::getCategoryId));
        List<ApplicableServiceByCategoryDTO> result = new ArrayList<>();
        for (MoeGroomingServiceCategory category : serviceCategoryList) {
            if (!serviceMap.containsKey(category.getId())) {
                continue;
            }
            ApplicableServiceByCategoryDTO dto = new ApplicableServiceByCategoryDTO();
            dto.setCategoryId(category.getId());
            dto.setCategoryName(category.getName());
            dto.setServiceList(serviceMap.get(category.getId()));
            result.add(dto);
        }
        return result;
    }

    private boolean isApplicableService(
            Map<Integer, List<MoeGroomingServiceBreedBinding>> serviceBreedMap,
            Map<Integer, List<String>> serviceCoatMap,
            Set<Integer> groomingAddonIds,
            PetDataForServiceParams petData,
            PetServiceDTO service) {
        // 按照 weight 过滤, 优先使用 pet size，如果没配置才使用 pet weight
        if (StringUtils.hasText(petData.getWeight())) {
            if (Objects.equals(service.getPetSizeFilter(), ServiceEnum.FILTER_OPEN) && petData.getPetSizeId() != null) {
                var allowedPetSizeIds = JsonUtil.toList(service.getAllowedPetSizeList(), Long.class);
                if (!allowedPetSizeIds.contains(petData.getPetSizeId())) {
                    return false;
                }
            } else {
                if (Objects.equals(service.getWeightFilter(), ServiceEnum.FILTER_OPEN)) {
                    BigDecimal weight = new BigDecimal(petData.getWeight());
                    // weight = 0 跳过 weight 条件判断，因为很多 pet 的重量默认设置成了 0
                    // 不在 weight range 内，则不符合条件
                    if (weight.compareTo(BigDecimal.ZERO) != 0
                            && (weight.compareTo(service.getWeightDownLimit()) < 0
                                    || weight.compareTo(service.getWeightUpLimit()) > 0)) {
                        return false;
                    }
                }
            }
        }

        // 按照 breed 过滤
        if (Objects.equals(service.getBreedFilter(), ServiceEnum.FILTER_OPEN)
                && petData.getPetTypeId() != null
                && StringUtils.hasText(petData.getBreed())
                && serviceBreedMap.containsKey(service.getId())) {
            List<MoeGroomingServiceBreedBinding> breedBindingList = serviceBreedMap.get(service.getId());
            // pet type 不一致，则不加入 applicable service
            if (breedBindingList.stream()
                    .noneMatch(breedBinding -> Objects.equals(breedBinding.getPetTypeId(), petData.getPetTypeId()))) {
                return false;
            }
            // breedNames 不包含 breed，则不加入 applicable service
            for (MoeGroomingServiceBreedBinding binding : breedBindingList) {
                // 非当前 petTypeId 的 binding，跳过
                if (!Objects.equals(binding.getPetTypeId(), petData.getPetTypeId())) {
                    continue;
                }
                if (!matchBreedFilter(binding, petData.getBreed())) {
                    return false;
                }
            }
        }
        // 按照 coat 过滤
        if (Objects.equals(service.getCoatFilter(), ServiceEnum.FILTER_OPEN)
                && StringUtils.hasText(petData.getCoat())
                && serviceCoatMap.containsKey(service.getId())) {
            List<String> coatList = serviceCoatMap.get(service.getId());
            if (!coatList.contains(StringMoegoUtil.compressAndLowerCase(petData.getCoat()))) {
                return false;
            }
        }

        // 根据 addon service filter 过滤
        if (Objects.equals(service.getAddOnServiceFilter(), ServiceEnum.FILTER_OPEN)) {
            if (!groomingAddonIds.contains(service.getId())) {
                return false;
            }
        }

        return true;
    }

    private boolean matchBreedFilter(MoeGroomingServiceBreedBinding binding, String breed) {
        List<String> breedNameList = StringUtils.hasText(binding.getBreedNameList())
                ? JsonUtil.toList(binding.getBreedNameList(), String.class)
                : List.of();
        return binding.getIsAll() || breedNameList.stream().anyMatch(breedName -> breedName.equalsIgnoreCase(breed));
    }

    public Map<Integer, ReportServiceDto> queryServiceWithCategory(Integer businessId, Set<Integer> serviceIds) {
        List<ReportServiceDto> services =
                companyGroomingServiceQueryService.queryServiceWithCategoryByServiceIds(businessId, serviceIds);
        return services.stream().collect(Collectors.toMap(ReportServiceDto::getId, s -> s));
    }

    public OBServiceListDto queryPetServicesForOBClient(QueryServiceByPetIdsParams queryServiceParams) {
        var companyDto = iBusinessBusinessClient.getCompanyByBusinessId(queryServiceParams.getBusinessId());
        if (companyDto == null || companyDto.getId() == null) {
            return null;
        }
        long companyId = companyDto.getId();

        // 查询所有的service和category
        List<ServiceCategoryDTO> serviceCategories =
                companyGroomingServiceQueryService.groomingServiceCategorySelectCategoryByCidOrBid(
                        null, queryServiceParams.getBusinessId(), null);
        List<PetServiceDTO> petServices =
                companyGroomingServiceQueryService.groomingServiceSelectByBusinessIdServiceIds(
                        companyId, queryServiceParams.getBusinessId(), null);
        petServices = petServices.stream()
                .filter(petServiceDTO -> petServiceDTO
                        .getBookOnlineAvailable()
                        .equals(GroomingAppointmentEnum.GROOMING_SVC_BOOK_ONLINE_YES))
                .collect(Collectors.toList());
        // 添加没有category的service组
        ServiceCategoryDTO serviceCategory0DTOSrv = new ServiceCategoryDTO();
        serviceCategory0DTOSrv.setSort(Integer.MAX_VALUE);
        serviceCategory0DTOSrv.setId(0);
        serviceCategory0DTOSrv.setBusinessId(queryServiceParams.getBusinessId());
        serviceCategory0DTOSrv.setType(ServiceEnum.TYPE_SERVICE);
        serviceCategories.add(serviceCategory0DTOSrv);

        ServiceCategoryDTO serviceCategory0DTOAddon = new ServiceCategoryDTO();
        serviceCategory0DTOAddon.setSort(Integer.MAX_VALUE);
        serviceCategory0DTOAddon.setId(0);
        serviceCategory0DTOAddon.setBusinessId(queryServiceParams.getBusinessId());
        serviceCategory0DTOAddon.setType(ServiceEnum.TYPE_ADD_ONS);
        serviceCategories.add(serviceCategory0DTOAddon);

        // 对服务分组
        List<ServiceCategoryDTO> serviceList = new ArrayList<>();
        List<ServiceCategoryDTO> addonsList = new ArrayList<>();
        for (ServiceCategoryDTO serviceCategory : serviceCategories) {
            if (serviceCategory.getPetServices() == null) {
                serviceCategory.setPetServices(new ArrayList<>());
            }
            for (PetServiceDTO petServiceDTO : petServices) {
                // 过滤inactive=1的service
                if (BooleanEnum.INACTIVE_FALSE.equals(petServiceDTO.getInactive())
                        && serviceCategory.getId().equals(petServiceDTO.getCategoryId())) {
                    // categoryId == 0 的类型有两个
                    if (petServiceDTO.getCategoryId() == 0) {
                        // 如果一个服务的category为0， 需要特殊处理
                        if (serviceCategory.getType().equals(petServiceDTO.getType())) {
                            serviceCategory.getPetServices().add(petServiceDTO);
                        }
                    } else {
                        serviceCategory.getPetServices().add(petServiceDTO);
                    }
                }
            }
            if (ServiceEnum.TYPE_SERVICE.equals(serviceCategory.getType())) {
                serviceList.add(serviceCategory);
            } else {
                // type=2
                addonsList.add(serviceCategory);
            }
        }
        sortServiceAndCategory(addonsList);
        sortServiceAndCategory(serviceList);

        OBServiceListDto resultMap = new OBServiceListDto();
        resultMap.setServiceList(serviceList);
        resultMap.setAddonsList(addonsList);

        // 如果petId 或者 customerId为空，肯定没有save price和time
        if (queryServiceParams.getCustomerId() != null && queryServiceParams.getPetIds() != null) {
            Map<Integer, List<ServiceCategoryDTO>> petServiceList = new HashMap<>();
            Map<Integer, List<ServiceCategoryDTO>> petAddonsList = new HashMap<>();

            queryServiceParams.getPetIds().forEach(petId -> {
                // 过滤petId == null，避免返回报错
                if (petId == null) {
                    return;
                }
                List<ServiceCategoryDTO> tmpServiceList = new ArrayList<>();
                List<ServiceCategoryDTO> tmpAddonsList = new ArrayList<>();
                petServiceList.put(petId, tmpServiceList);
                petAddonsList.put(petId, tmpAddonsList);
                // clone origin service for this pet; collect all srv and over write price and time if needes.
                List<PetServiceDTO> currentPetAllSrv = new ArrayList<>();
                serviceList.forEach(srv -> {
                    ServiceCategoryDTO currentSrv = srv.clone();
                    tmpServiceList.add(currentSrv);
                    currentPetAllSrv.addAll(currentSrv.getPetServices());
                });
                addonsList.forEach(srv -> {
                    ServiceCategoryDTO currentSrv = srv.clone();
                    tmpAddonsList.add(currentSrv);
                    currentPetAllSrv.addAll(currentSrv.getPetServices());
                });
                // 查询宠物是否saveprice
                CustomerServiceQueryDTO customerServiceQueryDTO = new CustomerServiceQueryDTO();
                customerServiceQueryDTO.setBusinessId(queryServiceParams.getBusinessId());
                customerServiceQueryDTO.setCustomerId(queryServiceParams.getCustomerId());
                customerServiceQueryDTO.setPetId(petId);
                customerServiceQueryDTO.setCompanyId(companyId);
                List<MoeGroomingCustomerServices> moeGroomingCustomerServices =
                        companyGroomingServiceQueryService.queryCustomerServiceByProperties(customerServiceQueryDTO);
                // 比对服务是否是save
                for (PetServiceDTO petServiceDTO : currentPetAllSrv) {
                    petServiceDTO.setIsSavePrice(false);
                    petServiceDTO.setIsSaveTime(false);
                    for (MoeGroomingCustomerServices moeGroomingCustomerService : moeGroomingCustomerServices) {
                        if (petServiceDTO.getId().equals(moeGroomingCustomerService.getServiceId())) {
                            int saveType =
                                    moeGroomingCustomerService.getSaveType().intValue();
                            if (saveType == 1) {
                                petServiceDTO.setIsSavePrice(true);
                                petServiceDTO.setPrice(moeGroomingCustomerService.getServiceFee());
                            }
                            if (saveType == 2) {
                                petServiceDTO.setIsSaveTime(true);
                                petServiceDTO.setDuration(moeGroomingCustomerService.getServiceTime());
                            }
                        }
                    }
                }
                // add customize service to result
                resultMap.setPetServiceList(petServiceList);
                resultMap.setPetAddonsList(petAddonsList);
            });
        }

        // 查询applicable service，权限控制
        if (isServiceFilterAllowed(queryServiceParams.getBusinessId())) {
            List<PetServiceDTO> sortedServiceList = new ArrayList<>();
            serviceList.forEach(service -> sortedServiceList.addAll(service.getPetServices()));
            addonsList.forEach(addon -> sortedServiceList.addAll(addon.getPetServices()));

            resultMap.setApplicableServiceList(getApplicableServiceList(
                    queryServiceParams.getBusinessId(), sortedServiceList, queryServiceParams.getPetData()));
        }

        return resultMap;
    }

    /**
     * 根据pet信息查询applicable service，按petData的顺序返回
     * service默认都是all weight, all breeds
     * applicable service返回结果是在所有service里去掉打开weight、breed filter开关，且不满足weight、breed条件的
     * 当weight为空时，则跳过weight条件筛选，即默认所有service都满足
     *
     * @param businessId  商家id
     * @param petServices service/add-on 列表（已排序的service列表，顺序是按照category sort及service sort字段排的）
     * @param petDataList 要筛选service的宠物信息
     * @return
     */
    public List<PetApplicableServiceDTO> getApplicableServiceList(
            Integer businessId, List<PetServiceDTO> petServices, List<PetDataForServiceParams> petDataList) {
        if (CollectionUtils.isEmpty(petDataList)) {
            return Collections.emptyList();
        }
        // 如果所有service都没有打开filter开关，applicable service列表等于所有service，没有显示必要，返回空列表
        boolean allClosed = petServices.stream()
                .allMatch(service -> Objects.equals(service.getWeightFilter(), ServiceEnum.FILTER_CLOSE)
                        && Objects.equals(service.getBreedFilter(), ServiceEnum.FILTER_CLOSE)
                        && Objects.equals(service.getCoatFilter(), ServiceEnum.FILTER_CLOSE)
                        && Objects.equals(service.getPetSizeFilter(), ServiceEnum.FILTER_CLOSE)
                        && Objects.equals(service.getAddOnServiceFilter(), ServiceEnum.FILTER_CLOSE));
        if (allClosed) {
            return Collections.emptyList();
        }

        Map<Integer, PetDataForServiceParams> petMap = petDataList.stream()
                .collect(Collectors.toMap(PetDataForServiceParams::getPetId, identity(), (p1, p2) -> p1));

        Set<Integer> existingPetIds = petDataList.stream()
                .map(PetDataForServiceParams::getPetId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        MigrateInfo migrateInfo = migrateHelper.getMigrationInfo(businessId);
        if (migrateInfo != null && migrateInfo.isMigrate()) {
            setPetSizeId(migrateInfo.companyId(), petDataList);
        }
        Long companyId =
                Optional.ofNullable(migrateInfo).map(MigrateInfo::companyId).orElse(null);

        // 查询breed绑定
        Map<Integer, List<MoeGroomingServiceBreedBinding>> serviceBreedMap =
                getServiceBreedBindingMap(companyId, businessId);
        // 查询 coat 绑定
        Map<Integer, List<String>> serviceCoatMap =
                companyGroomingServiceQueryService.getServiceCoatStringBindingMapNew(companyId, businessId);
        // 查询 addon service filter 规则
        Set<Integer> groomingAddonIds = getGroomingAddons(companyId);

        // 查询并填充existing pet信息
        List<CustomerPetDetailDTO> existingPets =
                iPetClient.getCustomerPetListByIdList(new ArrayList<>(existingPetIds));
        Map<Integer, CustomerPetDetailDTO> existingPetMap = existingPets.stream()
                .filter(pet -> Objects.equals(pet.getBusinessId(), businessId)
                        && Objects.equals(pet.getStatus(), CustomerPetEnum.STATUS_NORMAL))
                .collect(Collectors.toMap(CustomerPetDetailDTO::getPetId, p -> p));
        petDataList.forEach(petData -> {
            PetDataForServiceParams pet = petMap.get(petData.getPetId());
            CustomerPetDetailDTO petDetail = existingPetMap.get(petData.getPetId());
            if (petDetail != null) {
                petData.setBreed(StringUtils.hasText(pet.getBreed()) ? pet.getBreed() : petDetail.getBreed());
                petData.setWeight(StringUtils.hasText(pet.getWeight()) ? pet.getWeight() : petDetail.getWeight());
                petData.setPetTypeId(
                        Objects.nonNull(pet.getPetTypeId()) ? pet.getPetTypeId() : petDetail.getPetTypeId());
                petData.setCoat(StringUtils.hasText(pet.getCoat()) ? pet.getCoat() : petDetail.getHairLength());
            }
        });

        // 过滤inactive类型的service
        petServices.removeIf(s -> Objects.equals(s.getInactive(), BooleanEnum.INACTIVE_TRUE));

        List<PetApplicableServiceDTO> resultList = new ArrayList<>();
        for (PetDataForServiceParams petData : petDataList) {
            List<Integer> applicableServices = new ArrayList<>();
            List<Integer> applicableAddons = new ArrayList<>();
            petServices.stream()
                    .filter(service ->
                            isApplicableService(serviceBreedMap, serviceCoatMap, groomingAddonIds, petData, service))
                    .forEach(service -> {
                        if (Objects.equals(service.getType(), ServiceEnum.TYPE_ADD_ONS)) {
                            applicableAddons.add(service.getId());
                        } else {
                            applicableServices.add(service.getId());
                        }
                    });
            PetApplicableServiceDTO resultDto = new PetApplicableServiceDTO();
            resultDto.setPetId(petData.getPetId());
            resultDto.setApplicableService(applicableServices);
            resultDto.setApplicableAddon(applicableAddons);
            resultList.add(resultDto);
        }

        return resultList;
    }

    // make public for test
    public boolean stringListContainsCI(String container, String toMatch) {
        if (container == null || toMatch == null) {
            return false;
        }
        final String containerLC = container.toLowerCase();
        final String toMatchLC = toMatch.toLowerCase();
        return (containerLC.equals(toMatchLC)
                || containerLC.startsWith(toMatchLC + ",")
                || containerLC.endsWith("," + toMatchLC)
                || containerLC.contains("," + toMatchLC + ","));
    }

    /**
     * 对类别和服务列表排序
     *
     * @param serviceCategories
     */
    private void sortServiceAndCategory(List<ServiceCategoryDTO> serviceCategories) {
        // 排序
        Collections.sort(serviceCategories, (o1, o2) -> o2.getSort().compareTo(o1.getSort()));
        for (ServiceCategoryDTO serviceCategory : serviceCategories) {
            Collections.sort(
                    serviceCategory.getPetServices(), (o1, o2) -> o2.getSort().compareTo(o1.getSort()));
        }
    }

    /**
     * 当service删除的时候，判断是否有upcoming预约，有返回true，没有返回false
     */
    public Boolean isServiceBandingUpcoming(ServiceDeleteParams deleteParams) {

        Map<Integer, MoeBusinessDto> businessDtoMap = iBusinessBusinessClient.getBusinessByCompanyId(
                deleteParams.getCompanyId().intValue());
        for (MoeBusinessDto businessDto : businessDtoMap.values()) {
            LocalDateTime now = LocalDateTime.now();
            String date = DateUtil.convertLocalDateToDateString(now, businessDto.getTimezoneName(), "yyyy-MM-dd");
            Integer nowMinutes = DateUtil.getNowMinutes(businessDto.getTimezoneName());
            var upcomingCount = moeGroomingAppointmentMapper.selectHasUpcomingService(
                    date, nowMinutes, businessDto.getId(), deleteParams.getServiceId());
            if (upcomingCount != null && upcomingCount > 0) {
                return true;
            }
        }
        return false;
    }

    public void setPetSizeId(Long companyId, List<PetDataForServiceParams> petDataList) {
        List<BusinessPetSizeModel> petSizeList = businessPetSizeServiceClient
                .listPetSize(
                        ListPetSizeRequest.newBuilder().setCompanyId(companyId).build())
                .getSizesList();
        petDataList.forEach(petData -> {
            if (!StringUtils.hasText(petData.getWeight())) {
                return;
            }
            var weight = new BigDecimal(petData.getWeight());
            var roundedWeight = weight.setScale(0, RoundingMode.HALF_UP).intValue();
            petSizeList.stream()
                    .filter(size -> size.getWeightLow() <= roundedWeight && size.getWeightHigh() >= roundedWeight)
                    .findFirst()
                    .ifPresentOrElse(
                            petSizeModel -> petData.setPetSizeId(petSizeModel.getId()),
                            () -> petData.setPetSizeId(-1L)); // 如果未命中任何一个 Pet size，则传 -1，一定不会满足 Pet size filter 的条件
        });
    }
}
