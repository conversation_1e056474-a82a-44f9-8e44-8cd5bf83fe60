package com.moego.server.grooming.web;

import com.moego.common.enums.ResponseCodeEnum;
import com.moego.common.exception.CommonException;
import com.moego.common.response.ResponseResult;
import com.moego.common.utils.BindingErrorUtil;
import com.moego.idl.service.organization.v1.GetStaffDetailRequest;
import com.moego.idl.service.organization.v1.StaffServiceGrpc;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.migrate.MigrateHelper;
import com.moego.server.grooming.dto.GroomingPackageDTO;
import com.moego.server.grooming.dto.GroomingPackageInfoDTO;
import com.moego.server.grooming.dto.GroomingPackageServiceInfoDTO;
import com.moego.server.grooming.params.PackageUsedParams;
import com.moego.server.grooming.service.MoePackageService;
import com.moego.server.grooming.service.params.QueryCustomerPackageInfoParam;
import jakarta.validation.Valid;
import java.util.Collections;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
public class MoeGroomingPackageController {

    @Autowired
    private MoePackageService moePackageService;

    @Autowired
    private MigrateHelper migrateHelper;

    @Autowired
    private StaffServiceGrpc.StaffServiceBlockingStub staffServiceBlockingStub;

    /**
     * 顾客已购买package列表
     * Account Structure 迁移后, staff 只能看到 working location 下的 package
     *
     * @param context
     * @param customerId
     * @return
     */
    @GetMapping("/grooming/package/list")
    @Auth(AuthType.BUSINESS)
    public ResponseResult<List<GroomingPackageDTO>> queryCustomerPackageList(
            AuthContext context, @RequestParam Integer customerId) {
        long companyId = AuthContext.get().companyId();
        long staffId = AuthContext.get().staffId();
        final var businessIds = staffServiceBlockingStub
                .getStaffDetail(GetStaffDetailRequest.newBuilder()
                        .setId(staffId)
                        .setCompanyId(companyId)
                        .build())
                .getStaff()
                .getWorkingLocationListList()
                .stream()
                .map(l -> (int) l.getId())
                .toList();

        return moePackageService.queryCustomerPackageList(companyId, businessIds, customerId);
    }

    /**
     * package service history信息
     *
     * @param param DONE(Frank): package Id -》 businessId校验
     *              AuthType是不是应该为business
     *
     * @return
     */
    @GetMapping("/grooming/package/info")
    @Auth(AuthType.BUSINESS)
    public ResponseResult<GroomingPackageInfoDTO> queryCustomerPackageInfo(
            AuthContext context, @Valid QueryCustomerPackageInfoParam param) {
        Integer businessId = migrateHelper.isMigrate(context) ? null : context.getBusinessId();
        if (null == moePackageService.queryGroomingPackage(businessId, param.getId())) {
            throw new CommonException(ResponseCodeEnum.PACKAGE_NOT_FOUND, "not found package for given id");
        }

        return moePackageService.queryCustomerPackageInfo(param);
    }

    /**
     * 根据service id 查询 可用package
     *
     * @param context
     * @param serviceId
     * @param customerId
     * @return
     * @deprecated by Freeman, 这个接口的返回值已经不符合现在 package 模型定义，和前端确认过已经没有使用
     */
    @GetMapping("/grooming/packages")
    @Auth(AuthType.BUSINESS)
    @Deprecated(since = "2024/10/28")
    public ResponseResult<List<GroomingPackageServiceInfoDTO>> queryCustomerPackageByServiceId(
            AuthContext context, @RequestParam Integer serviceId, @RequestParam Integer customerId) {
        return ResponseResult.success(moePackageService.queryCustomerPackageByServiceIds(
                context.getBusinessId(), Collections.singletonList(serviceId), customerId));
    }

    /**
     * 使用package
     *
     * @param context
     * @param packageUsedParams
     * @return
     */
    @PutMapping("/grooming/package/used")
    @Auth(AuthType.BUSINESS)
    public ResponseResult<Integer> usePackageForGrooming(
            AuthContext context,
            @Validated @RequestBody List<PackageUsedParams> packageUsedParams,
            BindingResult bindingResult) {
        BindingErrorUtil.handleResult(bindingResult);
        for (PackageUsedParams packageUsedParam : packageUsedParams) {
            packageUsedParam.setBusinessId(context.getBusinessId());
        }
        return moePackageService.usePackageForGrooming(packageUsedParams);
    }

    /**
     * @param context
     * @param id
     * @return
     */
    @DeleteMapping("/grooming/package")
    @Auth(AuthType.BUSINESS)
    public Boolean deleteCustomerPackage(AuthContext context, @RequestParam Integer id) {
        var businessId = context.getBusinessId();
        var staffId = context.getStaffId();
        return moePackageService.deleteCustomerPackage(businessId, staffId, id);
    }
}
