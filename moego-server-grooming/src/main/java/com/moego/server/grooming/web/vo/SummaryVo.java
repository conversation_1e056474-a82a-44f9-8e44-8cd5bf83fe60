package com.moego.server.grooming.web.vo;

import com.moego.server.grooming.web.dto.SummaryDto;
import java.util.Optional;
import lombok.Data;

@Data
public class SummaryVo {

    private StaffSummary staffSummary;
    private BusinessSummary businessSummary;

    @Data
    public static class StaffSummary {

        private Service service;
        private LongestWorkingDay longestWorkingDay;
        private TakeCaredPet takeCaredPet;
        private MostPetBreed mostPetBreed;

        @Data
        public static class Service {

            private Integer totalServiceHours;
        }

        @Data
        public static class TakeCaredPet {

            private Integer count;
        }

        @Data
        public static class LongestWorkingDay {

            private String date;
            private Integer hours;
        }

        @Data
        public static class MostPetBreed {

            private String breed;
            private Integer count;
        }

        public static SummaryVo.StaffSummary from(SummaryDto.StaffSummary staffSummary) {
            if (staffSummary == null) {
                return null;
            }
            SummaryVo.StaffSummary result = new SummaryVo.StaffSummary();
            Optional.ofNullable(staffSummary.getService())
                    .map(svc -> {
                        SummaryVo.StaffSummary.Service service = new SummaryVo.StaffSummary.Service();
                        service.setTotalServiceHours((int) Math.round(svc.getTotalServiceTimeInMin() / 60.0));
                        return service;
                    })
                    .ifPresent(result::setService);
            Optional.ofNullable(staffSummary.getTakeCaredPet())
                    .map(takeCaredPet -> {
                        SummaryVo.StaffSummary.TakeCaredPet caredPet = new SummaryVo.StaffSummary.TakeCaredPet();
                        caredPet.setCount(takeCaredPet.getCount());
                        return caredPet;
                    })
                    .ifPresent(result::setTakeCaredPet);
            Optional.ofNullable(staffSummary.getLongestWorkingDay())
                    .map(lwk -> {
                        SummaryVo.StaffSummary.LongestWorkingDay workingDay =
                                new SummaryVo.StaffSummary.LongestWorkingDay();
                        workingDay.setHours((int) Math.round(lwk.getMinutes() / 60.0));
                        workingDay.setDate(lwk.getDate());
                        return workingDay;
                    })
                    .ifPresent(result::setLongestWorkingDay);
            Optional.ofNullable(staffSummary.getMostPetBreed())
                    .map(mpb -> {
                        SummaryVo.StaffSummary.MostPetBreed mostPetBreed = new SummaryVo.StaffSummary.MostPetBreed();
                        mostPetBreed.setBreed(mpb.getBreed());
                        mostPetBreed.setCount(mpb.getCount());
                        return mostPetBreed;
                    })
                    .ifPresent(result::setMostPetBreed);
            return result;
        }
    }

    @Data
    public static class BusinessSummary {

        private OnlineBookingSummary onlineBookingSummary;
        private MoegoPaySummary moeGoPaySummary;
        private StaffSummary staffSummaries;

        public static SummaryVo.BusinessSummary from(SummaryDto.BusinessSummary businessSummary) {
            if (businessSummary == null) {
                return null;
            }
            SummaryVo.BusinessSummary result = new BusinessSummary();
            Optional.ofNullable(businessSummary.getMoeGoPaySummary())
                    .map(MoegoPaySummary::from)
                    .ifPresent(result::setMoeGoPaySummary);
            Optional.ofNullable(businessSummary.getOnlineBookingSummary())
                    .map(OnlineBookingSummary::from)
                    .ifPresent(result::setOnlineBookingSummary);
            Optional.ofNullable(businessSummary.getStaffSummary())
                    .map(StaffSummary::from)
                    .ifPresent(result::setStaffSummaries);
            return result;
        }

        @Data
        public static class OnlineBookingSummary {

            private Boolean enabled;
            private Integer count;

            public static SummaryVo.BusinessSummary.OnlineBookingSummary from(
                    SummaryDto.BusinessSummary.OnlineBookingSummary onlineBookingSummary) {
                if (onlineBookingSummary == null) {
                    return null;
                }
                SummaryVo.BusinessSummary.OnlineBookingSummary result =
                        new SummaryVo.BusinessSummary.OnlineBookingSummary();
                result.setEnabled(onlineBookingSummary.isEnabled());
                result.setCount(onlineBookingSummary.getCount());
                return result;
            }
        }

        @Data
        public static class MoegoPaySummary {

            private Boolean enabled;
            private Integer count;
            private Double totalAmount;

            public static SummaryVo.BusinessSummary.MoegoPaySummary from(
                    SummaryDto.BusinessSummary.MoegoPaySummary moegoPaySummary) {
                if (moegoPaySummary == null) {
                    return null;
                }
                SummaryVo.BusinessSummary.MoegoPaySummary result = new SummaryVo.BusinessSummary.MoegoPaySummary();
                result.setEnabled(moegoPaySummary.isEnabled());
                result.setCount(moegoPaySummary.getCount());
                result.setTotalAmount(moegoPaySummary.getTotalAmount());
                return result;
            }
        }
    }
}
