package com.moego.server.grooming.mapper;

import com.moego.server.grooming.mapperbean.AppointmentPetMedication;
import com.moego.server.grooming.mapperbean.AppointmentPetMedicationExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface AppointmentPetMedicationMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table appointment_pet_medication
     *
     * @mbg.generated
     */
    long countByExample(AppointmentPetMedicationExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table appointment_pet_medication
     *
     * @mbg.generated
     */
    int deleteByExample(AppointmentPetMedicationExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table appointment_pet_medication
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table appointment_pet_medication
     *
     * @mbg.generated
     */
    int insert(AppointmentPetMedication record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table appointment_pet_medication
     *
     * @mbg.generated
     */
    int insertSelective(AppointmentPetMedication record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table appointment_pet_medication
     *
     * @mbg.generated
     */
    List<AppointmentPetMedication> selectByExample(AppointmentPetMedicationExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table appointment_pet_medication
     *
     * @mbg.generated
     */
    AppointmentPetMedication selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table appointment_pet_medication
     *
     * @mbg.generated
     */
    int updateByExampleSelective(
            @Param("record") AppointmentPetMedication record,
            @Param("example") AppointmentPetMedicationExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table appointment_pet_medication
     *
     * @mbg.generated
     */
    int updateByExample(
            @Param("record") AppointmentPetMedication record,
            @Param("example") AppointmentPetMedicationExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table appointment_pet_medication
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(AppointmentPetMedication record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table appointment_pet_medication
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(AppointmentPetMedication record);
}
