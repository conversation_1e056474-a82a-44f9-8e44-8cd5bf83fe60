package com.moego.server.grooming.service.statemachine.action;

import com.google.protobuf.util.Timestamps;
import com.moego.idl.models.appointment.v1.AppointmentStatus;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.service.appointment.v1.AppointmentServiceGrpc.AppointmentServiceBlockingStub;
import com.moego.idl.service.appointment.v1.UpdateAppointmentSelectiveRequest;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.server.grooming.enums.AppointmentStatusEnum;
import com.moego.server.grooming.mapperbean.MoeGroomingAppointment;
import com.moego.server.grooming.service.statemachine.context.ActionContext;
import java.time.Instant;
import java.util.Date;
import java.util.Objects;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class ReadyAction implements IStateTransitionAction {

    private final AppointmentServiceBlockingStub appointmentStub;

    @Override
    public boolean suit(AppointmentStatusEnum newStatus) {
        return AppointmentStatusEnum.READY.equals(newStatus);
    }

    @Override
    public int execute(MoeGroomingAppointment moeGroomingAppointment, ActionContext actionContext) {
        if (Objects.equals(moeGroomingAppointment.getStatus(), AppointmentStatusEnum.CANCELED.getValue())
                || Objects.equals(moeGroomingAppointment.getStatus(), AppointmentStatusEnum.FINISHED.getValue())) {
            throw ExceptionUtil.bizException(
                    Code.CODE_PARAMS_ERROR,
                    String.format(
                            "Appointment[%d] status is %s, can not ready for pickup",
                            moeGroomingAppointment.getId(), moeGroomingAppointment.getStatus()));
        }

        if (Objects.equals(moeGroomingAppointment.getStatus(), AppointmentStatusEnum.READY.getValue())) {
            // already ready
            return 0;
        }

        // 保留之前的逻辑：where id = xx and status in (unconfirmed, confirmed, check-in)
        if (!Objects.equals(moeGroomingAppointment.getStatus().intValue(), AppointmentStatus.UNCONFIRMED_VALUE)
                && !Objects.equals(moeGroomingAppointment.getStatus().intValue(), AppointmentStatus.CONFIRMED_VALUE)
                && !Objects.equals(moeGroomingAppointment.getStatus().intValue(), AppointmentStatus.CHECKED_IN_VALUE)) {
            return 0;
        }

        var builder = UpdateAppointmentSelectiveRequest.newBuilder();
        builder.setId(moeGroomingAppointment.getId());
        builder.setStatus(AppointmentStatus.READY);
        builder.setUpdateTime(Timestamps.fromDate(new Date()));
        Optional.ofNullable(AuthContext.get().staffId()).ifPresent(builder::setUpdatedById);
        builder.setReadyTime(Instant.now().getEpochSecond());
        builder.setStatusBeforeReady(AppointmentStatus.forNumber(moeGroomingAppointment.getStatus()));

        if (moeGroomingAppointment.getCheckInTime() == null || moeGroomingAppointment.getCheckInTime() == 0L) {
            builder.setCheckInTime(Instant.now().getEpochSecond());
        }

        return appointmentStub.updateAppointmentSelective(builder.build()).getAffectedRows();
    }

    @Override
    public int revert(MoeGroomingAppointment moeGroomingAppointment) {
        if (!Objects.equals(moeGroomingAppointment.getStatus(), AppointmentStatusEnum.READY.getValue())) {
            throw ExceptionUtil.bizException(
                    Code.CODE_PARAMS_ERROR,
                    String.format(
                            "Appointment[%d] status is %s, can not revert ready for pickup",
                            moeGroomingAppointment.getId(), moeGroomingAppointment.getStatus()));
        }

        // 保留之前的逻辑：where id = xx and status in (ready)
        if (!Objects.equals(moeGroomingAppointment.getStatus().intValue(), AppointmentStatus.READY_VALUE)) {
            return 0;
        }

        var builder = UpdateAppointmentSelectiveRequest.newBuilder();
        builder.setId(moeGroomingAppointment.getId());
        builder.setStatus(AppointmentStatus.forNumber(
                moeGroomingAppointment.getStatusBeforeReady().getValue()));
        builder.setUpdateTime(Timestamps.fromDate(new Date()));
        Optional.ofNullable(AuthContext.get().staffId()).ifPresent(builder::setUpdatedById);
        builder.setStatusBeforeReady(AppointmentStatus.APPOINTMENT_STATUS_UNSPECIFIED);
        builder.setReadyTime(0L);

        // 如果回退到 check in 的前置状态，那么 check in 的时间需要清空
        if (!Objects.equals(moeGroomingAppointment.getStatusBeforeReady(), AppointmentStatusEnum.CHECK_IN)) {
            builder.setCheckInTime(0L);
        }

        return appointmentStub.updateAppointmentSelective(builder.build()).getAffectedRows();
    }
}
