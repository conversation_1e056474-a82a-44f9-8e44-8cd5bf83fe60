package com.moego.server.grooming.web.vo;

import com.moego.common.enums.ServiceItemEnum;
import java.util.List;
import lombok.Data;

@Data
public class PetDetailQueryVo {

    // deprecated, use startDate and endDate instead
    @Deprecated(since = "2024-10-18")
    private String appointmentDate;

    private String startDate;

    private String endDate;

    private Integer businessId;
    private Integer isWaitingList;
    private List<Integer> staffIdList;
    /**
     * filter pet details service item type fields
     *
     * @see ServiceItemEnum
     */
    private List<Integer> serviceItems;

    /**
     * filter appointment service type include fields
     */
    private List<Integer> serviceTypeIncludes;

    /**
     * filter appointment date range begin
     */
    private String startDateBegin;

    /**
     * filter appointment date range finish
     */
    private String startDateFinish;

    /**
     * filter appointment end date range begin
     */
    private String endDateBegin;

    /**
     * filter appointment end date range finish
     */
    private String endDateFinish;
}
