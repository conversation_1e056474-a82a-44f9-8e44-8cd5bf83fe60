package com.moego.server.grooming.mapperbean;

import java.util.Date;

/**
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table moe_grooming_report
 */
public class MoeGroomingReport {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_report.id
     *
     * @mbg.generated
     */
    private Integer id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_report.business_id
     *
     * @mbg.generated
     */
    private Integer businessId;

    /**
     * Database Column Remarks:
     *   customer id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_report.customer_id
     *
     * @mbg.generated
     */
    private Integer customerId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_report.grooming_id
     *
     * @mbg.generated
     */
    private Integer groomingId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_report.pet_id
     *
     * @mbg.generated
     */
    private Integer petId;

    /**
     * Database Column Remarks:
     *   pet type id: 1-dog, 2-cat
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_report.pet_type_id
     *
     * @mbg.generated
     */
    private Integer petTypeId;

    /**
     * Database Column Remarks:
     *   category name, reserved field
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_report.uuid
     *
     * @mbg.generated
     */
    private String uuid;

    /**
     * Database Column Remarks:
     *   used template publish time, for version check
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_report.template_publish_time
     *
     * @mbg.generated
     */
    private Date templatePublishTime;

    /**
     * Database Column Remarks:
     *   grooming report template without question list
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_report.template_json
     *
     * @mbg.generated
     */
    private String templateJson;

    /**
     * Database Column Remarks:
     *   grooming report fill in content, json format
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_report.content_json
     *
     * @mbg.generated
     */
    private String contentJson;

    /**
     * Database Column Remarks:
     *   grooming report status: Created/Draft/Ready/Sent
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_report.status
     *
     * @mbg.generated
     */
    private String status;

    /**
     * Database Column Remarks:
     *   grooming report submitted time
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_report.submitted_time
     *
     * @mbg.generated
     */
    private Date submittedTime;

    /**
     * Database Column Remarks:
     *   grooming report link opened count
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_report.link_opened_count
     *
     * @mbg.generated
     */
    private Integer linkOpenedCount;

    /**
     * Database Column Remarks:
     *   last update staff id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_report.update_by
     *
     * @mbg.generated
     */
    private Integer updateBy;

    /**
     * Database Column Remarks:
     *   create time
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_report.create_time
     *
     * @mbg.generated
     */
    private Date createTime;

    /**
     * Database Column Remarks:
     *   update time
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_report.update_time
     *
     * @mbg.generated
     */
    private Date updateTime;

    /**
     * Database Column Remarks:
     *   theme code
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_report.theme_code
     *
     * @mbg.generated
     */
    private String themeCode;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_grooming_report.company_id
     *
     * @mbg.generated
     */
    private Long companyId;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_report.id
     *
     * @return the value of moe_grooming_report.id
     *
     * @mbg.generated
     */
    public Integer getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_report.id
     *
     * @param id the value for moe_grooming_report.id
     *
     * @mbg.generated
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_report.business_id
     *
     * @return the value of moe_grooming_report.business_id
     *
     * @mbg.generated
     */
    public Integer getBusinessId() {
        return businessId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_report.business_id
     *
     * @param businessId the value for moe_grooming_report.business_id
     *
     * @mbg.generated
     */
    public void setBusinessId(Integer businessId) {
        this.businessId = businessId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_report.customer_id
     *
     * @return the value of moe_grooming_report.customer_id
     *
     * @mbg.generated
     */
    public Integer getCustomerId() {
        return customerId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_report.customer_id
     *
     * @param customerId the value for moe_grooming_report.customer_id
     *
     * @mbg.generated
     */
    public void setCustomerId(Integer customerId) {
        this.customerId = customerId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_report.grooming_id
     *
     * @return the value of moe_grooming_report.grooming_id
     *
     * @mbg.generated
     */
    public Integer getGroomingId() {
        return groomingId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_report.grooming_id
     *
     * @param groomingId the value for moe_grooming_report.grooming_id
     *
     * @mbg.generated
     */
    public void setGroomingId(Integer groomingId) {
        this.groomingId = groomingId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_report.pet_id
     *
     * @return the value of moe_grooming_report.pet_id
     *
     * @mbg.generated
     */
    public Integer getPetId() {
        return petId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_report.pet_id
     *
     * @param petId the value for moe_grooming_report.pet_id
     *
     * @mbg.generated
     */
    public void setPetId(Integer petId) {
        this.petId = petId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_report.pet_type_id
     *
     * @return the value of moe_grooming_report.pet_type_id
     *
     * @mbg.generated
     */
    public Integer getPetTypeId() {
        return petTypeId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_report.pet_type_id
     *
     * @param petTypeId the value for moe_grooming_report.pet_type_id
     *
     * @mbg.generated
     */
    public void setPetTypeId(Integer petTypeId) {
        this.petTypeId = petTypeId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_report.uuid
     *
     * @return the value of moe_grooming_report.uuid
     *
     * @mbg.generated
     */
    public String getUuid() {
        return uuid;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_report.uuid
     *
     * @param uuid the value for moe_grooming_report.uuid
     *
     * @mbg.generated
     */
    public void setUuid(String uuid) {
        this.uuid = uuid == null ? null : uuid.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_report.template_publish_time
     *
     * @return the value of moe_grooming_report.template_publish_time
     *
     * @mbg.generated
     */
    public Date getTemplatePublishTime() {
        return templatePublishTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_report.template_publish_time
     *
     * @param templatePublishTime the value for moe_grooming_report.template_publish_time
     *
     * @mbg.generated
     */
    public void setTemplatePublishTime(Date templatePublishTime) {
        this.templatePublishTime = templatePublishTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_report.template_json
     *
     * @return the value of moe_grooming_report.template_json
     *
     * @mbg.generated
     */
    public String getTemplateJson() {
        return templateJson;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_report.template_json
     *
     * @param templateJson the value for moe_grooming_report.template_json
     *
     * @mbg.generated
     */
    public void setTemplateJson(String templateJson) {
        this.templateJson = templateJson == null ? null : templateJson.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_report.content_json
     *
     * @return the value of moe_grooming_report.content_json
     *
     * @mbg.generated
     */
    public String getContentJson() {
        return contentJson;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_report.content_json
     *
     * @param contentJson the value for moe_grooming_report.content_json
     *
     * @mbg.generated
     */
    public void setContentJson(String contentJson) {
        this.contentJson = contentJson == null ? null : contentJson.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_report.status
     *
     * @return the value of moe_grooming_report.status
     *
     * @mbg.generated
     */
    public String getStatus() {
        return status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_report.status
     *
     * @param status the value for moe_grooming_report.status
     *
     * @mbg.generated
     */
    public void setStatus(String status) {
        this.status = status == null ? null : status.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_report.submitted_time
     *
     * @return the value of moe_grooming_report.submitted_time
     *
     * @mbg.generated
     */
    public Date getSubmittedTime() {
        return submittedTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_report.submitted_time
     *
     * @param submittedTime the value for moe_grooming_report.submitted_time
     *
     * @mbg.generated
     */
    public void setSubmittedTime(Date submittedTime) {
        this.submittedTime = submittedTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_report.link_opened_count
     *
     * @return the value of moe_grooming_report.link_opened_count
     *
     * @mbg.generated
     */
    public Integer getLinkOpenedCount() {
        return linkOpenedCount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_report.link_opened_count
     *
     * @param linkOpenedCount the value for moe_grooming_report.link_opened_count
     *
     * @mbg.generated
     */
    public void setLinkOpenedCount(Integer linkOpenedCount) {
        this.linkOpenedCount = linkOpenedCount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_report.update_by
     *
     * @return the value of moe_grooming_report.update_by
     *
     * @mbg.generated
     */
    public Integer getUpdateBy() {
        return updateBy;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_report.update_by
     *
     * @param updateBy the value for moe_grooming_report.update_by
     *
     * @mbg.generated
     */
    public void setUpdateBy(Integer updateBy) {
        this.updateBy = updateBy;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_report.create_time
     *
     * @return the value of moe_grooming_report.create_time
     *
     * @mbg.generated
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_report.create_time
     *
     * @param createTime the value for moe_grooming_report.create_time
     *
     * @mbg.generated
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_report.update_time
     *
     * @return the value of moe_grooming_report.update_time
     *
     * @mbg.generated
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_report.update_time
     *
     * @param updateTime the value for moe_grooming_report.update_time
     *
     * @mbg.generated
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_report.theme_code
     *
     * @return the value of moe_grooming_report.theme_code
     *
     * @mbg.generated
     */
    public String getThemeCode() {
        return themeCode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_report.theme_code
     *
     * @param themeCode the value for moe_grooming_report.theme_code
     *
     * @mbg.generated
     */
    public void setThemeCode(String themeCode) {
        this.themeCode = themeCode == null ? null : themeCode.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_grooming_report.company_id
     *
     * @return the value of moe_grooming_report.company_id
     *
     * @mbg.generated
     */
    public Long getCompanyId() {
        return companyId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_grooming_report.company_id
     *
     * @param companyId the value for moe_grooming_report.company_id
     *
     * @mbg.generated
     */
    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }
}
