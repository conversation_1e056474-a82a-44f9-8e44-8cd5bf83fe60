package com.moego.server.grooming.server;

import com.moego.server.grooming.api.IAbandonPetServiceBase;
import com.moego.server.grooming.dto.AbandonPetDTO;
import com.moego.server.grooming.mapper.MoeBookOnlineAbandonRecordMapper;
import com.moego.server.grooming.mapper.MoeBookOnlineAbandonRecordPetMapper;
import com.moego.server.grooming.mapperbean.MoeBookOnlineAbandonRecord;
import com.moego.server.grooming.mapperbean.MoeBookOnlineAbandonRecordPetExample;
import com.moego.server.grooming.mapstruct.AbandonRecordPetMapper;
import java.util.List;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@RestController
@RequiredArgsConstructor
public class AbandonPetServer extends IAbandonPetServiceBase {

    private final MoeBookOnlineAbandonRecordMapper abandonRecordMapper;
    private final MoeBookOnlineAbandonRecordPetMapper abandonPetMapper;

    @Override
    public List<AbandonPetDTO> listByAbandonRecordId(Integer abandonRecordId) {
        MoeBookOnlineAbandonRecord abandonRecord = Optional.ofNullable(
                        abandonRecordMapper.selectByPrimaryKey(abandonRecordId))
                .filter(e -> !e.getIsDeleted())
                .orElse(null);
        if (abandonRecord == null) {
            return List.of();
        }

        MoeBookOnlineAbandonRecordPetExample example = new MoeBookOnlineAbandonRecordPetExample();
        example.createCriteria()
                .andBusinessIdEqualTo(abandonRecord.getBusinessId())
                .andBookingFlowIdEqualTo(abandonRecord.getBookingFlowId());
        return abandonPetMapper.selectByExample(example).stream()
                .map(AbandonRecordPetMapper.INSTANCE::entityToDTO)
                .toList();
    }
}
