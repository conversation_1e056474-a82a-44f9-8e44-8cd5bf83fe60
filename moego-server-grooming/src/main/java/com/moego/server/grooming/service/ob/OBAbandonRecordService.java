package com.moego.server.grooming.service.ob;

import static com.moego.common.utils.CommonUtil.isNormal;
import static com.moego.lib.common.exception.ExceptionUtil.bizException;
import static com.moego.server.grooming.dto.AbandonRecordDTO.AbandonStatus.NOT_RECOVERED_STATUSES;
import static com.moego.server.grooming.dto.AbandonRecordDTO.AbandonStep.listRecoverableSteps;
import static java.time.temporal.ChronoUnit.DAYS;
import static java.time.temporal.ChronoUnit.HOURS;

import com.moego.common.dto.clients.ClientsFilterDTO;
import com.moego.common.params.CustomerIdsParams;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.offering.v1.CustomizedServiceView;
import com.moego.idl.service.offering.v1.BatchGetCustomizedServiceRequest;
import com.moego.idl.service.offering.v1.BatchGetCustomizedServiceResponse;
import com.moego.idl.service.offering.v1.CustomizedServiceQueryCondition;
import com.moego.idl.service.offering.v1.ServiceManagementServiceGrpc;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.core.TypeRef;
import com.moego.lib.common.migrate.MigrateHelper;
import com.moego.lib.common.migrate.MigrateInfo;
import com.moego.lib.common.thread.ThreadPool;
import com.moego.lib.common.util.JsonUtil;
import com.moego.server.business.client.IBusinessStaffClient;
import com.moego.server.business.dto.MoeStaffDto;
import com.moego.server.business.params.StaffIdParams;
import com.moego.server.customer.api.ICustomerAddressService;
import com.moego.server.customer.api.ICustomerCustomerService;
import com.moego.server.customer.dto.CustomerAddressDto;
import com.moego.server.customer.dto.CustomerProfileRequestDTO;
import com.moego.server.customer.params.CustomerPetAddParams;
import com.moego.server.customer.params.FindAddressIdsByKeywordParam;
import com.moego.server.customer.params.FindCustomerIdsByKeywordParam;
import com.moego.server.grooming.dto.CustomerHasRequestDTO;
import com.moego.server.grooming.dto.ob.AssociateCustomerAndPetDTO;
import com.moego.server.grooming.dto.ob.AssociateCustomerDTO;
import com.moego.server.grooming.enums.AbandonDeleteTypeEnum;
import com.moego.server.grooming.enums.OBStepEnum;
import com.moego.server.grooming.eventbus.OnlineBookingProducer;
import com.moego.server.grooming.mapper.MoeBookOnlineAbandonRecordEventLogMapper;
import com.moego.server.grooming.mapper.MoeBookOnlineAbandonRecordMapper;
import com.moego.server.grooming.mapper.MoeBookOnlineAbandonRecordPetMapper;
import com.moego.server.grooming.mapper.param.SearchAbandonedRecordParam;
import com.moego.server.grooming.mapperbean.MoeBookOnlineAbandonRecord;
import com.moego.server.grooming.mapperbean.MoeBookOnlineAbandonRecordEventLog;
import com.moego.server.grooming.mapperbean.MoeBookOnlineAbandonRecordExample;
import com.moego.server.grooming.mapperbean.MoeBookOnlineAbandonRecordPet;
import com.moego.server.grooming.mapperbean.MoeBookOnlineQuestion;
import com.moego.server.grooming.mapstruct.AbandonRecordMapper;
import com.moego.server.grooming.mapstruct.AbandonRecordPetMapper;
import com.moego.server.grooming.mapstruct.GroomingServiceMapper;
import com.moego.server.grooming.mapstruct.ProfileRequestMapper;
import com.moego.server.grooming.mapstruct.QuestionMapper;
import com.moego.server.grooming.mapstruct.StaffMapper;
import com.moego.server.grooming.service.CompanyGroomingServiceQueryService;
import com.moego.server.grooming.service.dto.ob.OBAbandonRecordDTO;
import com.moego.server.grooming.service.utils.BusinessInfoHelper;
import com.moego.server.grooming.utils.OBAbandonedUtil;
import com.moego.server.grooming.web.params.SearchAbandonedClientParam;
import com.moego.server.grooming.web.vo.client.StaffDetailVO;
import com.moego.server.grooming.web.vo.ob.AbandonClientRecordVO;
import jakarta.annotation.Nullable;
import java.math.BigDecimal;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 * @since 2023/5/23
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OBAbandonRecordService {

    private final MoeBookOnlineAbandonRecordMapper abandonRecordMapper;
    private final MoeBookOnlineAbandonRecordEventLogMapper abandonRecordEventLogMapper;
    private final MoeBookOnlineAbandonRecordPetMapper abandonRecordPetMapper;
    private final OBQuestionService questionService;
    private final CompanyGroomingServiceQueryService companyGroomingServiceQueryService;
    private final OBCustomerService customerService;
    private final OBPetService petService;
    private final IBusinessStaffClient iBusinessStaffClient;
    private final ICustomerCustomerService customerApi;
    private final ICustomerAddressService customerAddressApi;
    private final OBAddressService obAddressService;
    private final MigrateHelper migrateHelper;
    private final BusinessInfoHelper businessInfoHelper;
    private final ServiceManagementServiceGrpc.ServiceManagementServiceBlockingStub serviceStub;
    private final OnlineBookingProducer onlineBookingProducer;

    public Integer deleteAbandonRecords(Integer businessId, Integer customerId) {
        MoeBookOnlineAbandonRecordExample example = new MoeBookOnlineAbandonRecordExample();
        example.createCriteria()
                .andBusinessIdEqualTo(businessId)
                .andCustomerIdEqualTo(customerId)
                .andIsDeletedEqualTo(Boolean.FALSE);
        MoeBookOnlineAbandonRecord record = new MoeBookOnlineAbandonRecord();
        record.setIsDeleted(true);
        record.setDeleteType(AbandonDeleteTypeEnum.MANUAL.getType());
        return abandonRecordMapper.updateByExampleSelective(record, example);
    }

    public void deleteExistingClientAbandonRecords(Integer businessId, Set<Integer> customerIds) {
        if (Objects.isNull(businessId) || CollectionUtils.isEmpty(customerIds)) {
            return;
        }
        MoeBookOnlineAbandonRecordExample example = new MoeBookOnlineAbandonRecordExample();
        example.createCriteria()
                .andBusinessIdEqualTo(businessId)
                .andCustomerIdIn(new ArrayList<>(customerIds))
                .andIsDeletedEqualTo(Boolean.FALSE)
                .andAbandonStatusIn(new ArrayList<>(SearchAbandonedClientParam.AbandonStatus.NOT_RECOVERED_STATUSES));
        MoeBookOnlineAbandonRecord record = new MoeBookOnlineAbandonRecord();
        record.setIsDeleted(true);
        record.setDeleteType(AbandonDeleteTypeEnum.MANUAL.getType());
        abandonRecordMapper.updateByExampleSelective(record, example);
    }

    public void deleteNewVisitorAbandonRecords(Integer businessId, Set<String> phoneNumbers) {
        if (Objects.isNull(businessId) || CollectionUtils.isEmpty(phoneNumbers)) {
            return;
        }
        MoeBookOnlineAbandonRecordExample example = new MoeBookOnlineAbandonRecordExample();
        example.createCriteria()
                .andBusinessIdEqualTo(businessId)
                .andPhoneNumberIn(new ArrayList<>(phoneNumbers))
                .andLeadTypeEqualTo(SearchAbandonedClientParam.LeadType.NEW_VISITOR.getValue())
                .andIsDeletedEqualTo(Boolean.FALSE)
                .andAbandonStatusIn(new ArrayList<>(SearchAbandonedClientParam.AbandonStatus.NOT_RECOVERED_STATUSES));
        MoeBookOnlineAbandonRecord record = new MoeBookOnlineAbandonRecord();
        record.setIsDeleted(true);
        record.setDeleteType(AbandonDeleteTypeEnum.MANUAL.getType());
        abandonRecordMapper.updateByExampleSelective(record, example);
    }

    public void deleteAbandonClientRecord(String bookingFlowId) {
        MoeBookOnlineAbandonRecord existingRecord = abandonRecordMapper.selectUndeletedAbandonRecordByBookingFlowId(
                AuthContext.get().getBusinessId(), bookingFlowId);
        if (Objects.isNull(existingRecord)) {
            throw bizException(Code.CODE_PARAMS_ERROR, "Abandon record not exist");
        }
        // Delete abandoned record before this record
        abandonRecordMapper.deleteNotRecoveredAbandonedRecords(
                existingRecord.getBusinessId(),
                AbandonDeleteTypeEnum.MANUAL.getType(),
                existingRecord.getAbandonTime(),
                existingRecord.getCustomerId(),
                existingRecord.getPhoneNumber());
    }

    public AbandonClientRecordVO getAbandonClientRecord(String bookingFlowId) {
        MoeBookOnlineAbandonRecord abandonRecord = abandonRecordMapper.selectUndeletedAbandonRecordByBookingFlowId(
                AuthContext.get().getBusinessId(), bookingFlowId);
        if (Objects.isNull(abandonRecord)) {
            throw bizException(Code.CODE_PARAMS_ERROR, "Abandon record not exist");
        }
        List<MoeBookOnlineAbandonRecordPet> abandonRecordPetList = List.of();
        if (OBStepEnum.valueOf(abandonRecord.getAbandonStep()).getOrder() > OBStepEnum.select_pet.getOrder()) {
            abandonRecordPetList = abandonRecordPetMapper.listRecordPetByBookingFlowId(
                    AuthContext.get().getBusinessId(), bookingFlowId);
        }
        // Replace custom questions label with answer
        Map<String, MoeBookOnlineQuestion> questionMap =
                questionService.getCustomQuestionMap(AuthContext.get().getBusinessId());
        AbandonClientRecordVO clientRecordVO = AbandonRecordMapper.INSTANCE.entity2VO(abandonRecord);
        AbandonClientRecordVO.AbandonClientRecordVOBuilder builder = clientRecordVO.toBuilder();
        // Existing client take biz's customer info instead of abandon data
        CustomerHasRequestDTO requestDTO = customerService.getCustomerHasRequestUpdate(
                AuthContext.get().getBusinessId(), abandonRecord.getCustomerId());
        if (Objects.nonNull(abandonRecord.getCustomerId())) {
            builder.hasRequestUpdate(requestDTO.hasRequestUpdate())
                    .customer(replaceExistingClient(abandonRecord, requestDTO.mergedProfile(), questionMap))
                    .preference(replaceExistingPreference(abandonRecord, requestDTO.mergedProfile()));
        }
        if (Objects.nonNull(abandonRecord.getStaffId())) {
            builder.staff(buildStaffDetail(abandonRecord.getBusinessId(), abandonRecord.getStaffId()));
        }
        if (Objects.nonNull(abandonRecord.getAddressId())) {
            builder.address(buildAddressDetail(abandonRecord.getAddressId()));
        }
        if (!CollectionUtils.isEmpty(abandonRecordPetList)) {
            buildPetDetail(
                    AuthContext.get().getBusinessId(),
                    abandonRecord,
                    abandonRecordPetList,
                    questionMap,
                    builder,
                    requestDTO.mergedProfile());
        } else {
            builder.pets(List.of()).services(List.of());
        }
        return builder.build();
    }

    public AbandonClientRecordVO.ClientDetailVO replaceExistingClient(
            MoeBookOnlineAbandonRecord abandonRecord,
            CustomerProfileRequestDTO mergedProfile,
            Map<String, MoeBookOnlineQuestion> questionMap) {
        CustomerProfileRequestDTO.ClientProfileDTO mergedClient;
        if (Objects.isNull(mergedProfile) || Objects.isNull(mergedClient = mergedProfile.getClient())) {
            return AbandonRecordMapper.INSTANCE.entity2ClientDetailVO(abandonRecord);
        }
        Map<String, Object> questionAnswerMap;
        if (StringUtils.hasText(abandonRecord.getCustomerQuestionAnswers())) {
            questionAnswerMap = JsonUtil.toBean(abandonRecord.getCustomerQuestionAnswers(), new TypeRef<>() {});
        } else {
            questionAnswerMap = Map.of();
        }
        return ProfileRequestMapper.INSTANCE.dto2AbandonClientDetailVO(mergedClient).toBuilder()
                .referer(abandonRecord.getReferer())
                .customerId(abandonRecord.getCustomerId())
                .questionAnswerList(questionAnswer2List(
                        QuestionMapper.INSTANCE.replaceRequestAnswer(
                                questionAnswerMap, mergedClient.getCustomQuestions()),
                        questionMap))
                .build();
    }

    public AbandonClientRecordVO.ClientPreferenceVO replaceExistingPreference(
            MoeBookOnlineAbandonRecord abandonRecord, CustomerProfileRequestDTO mergedProfile) {
        CustomerProfileRequestDTO.ClientProfileDTO mergedClient;
        if (Objects.isNull(mergedProfile) || Objects.isNull(mergedClient = mergedProfile.getClient())) {
            return AbandonRecordMapper.INSTANCE.entity2PreferenceVO(abandonRecord);
        }
        return ProfileRequestMapper.INSTANCE.dto2AbandonClientPreferenceVO(mergedClient);
    }

    /**
     * Build staff detail
     *
     * @param businessId business id
     * @param staffId    selected staff id
     * @return staff detail
     */
    public StaffDetailVO buildStaffDetail(Integer businessId, Integer staffId) {
        StaffIdParams staffIdParams = new StaffIdParams();
        staffIdParams.setBusinessId(businessId);
        staffIdParams.setStaffId(staffId);
        MoeStaffDto staffDTO = iBusinessStaffClient.getStaff(staffIdParams);
        return StaffMapper.INSTANCE.dto2VO(staffDTO);
    }

    /**
     * Build address detail
     *
     * @param addressId selected address id
     * @return address detail
     */
    public AbandonClientRecordVO.AddressDetailVO buildAddressDetail(Integer addressId) {
        CustomerAddressDto addressDto = obAddressService.getCustomerAddress(addressId);
        return AbandonRecordMapper.INSTANCE.dto2AddressDetailVO(addressDto);
    }

    /**
     * Build pet detail and service detail
     *
     * @param abandonRecordPetList abandon pet record and selected service
     * @param questionMap          custom question map
     * @param recordVOBuilder      abandon record builder
     * @param requestDTO           customer profile request
     */
    public void buildPetDetail(
            Integer businessId,
            MoeBookOnlineAbandonRecord abandonRecord,
            List<MoeBookOnlineAbandonRecordPet> abandonRecordPetList,
            Map<String, MoeBookOnlineQuestion> questionMap,
            AbandonClientRecordVO.AbandonClientRecordVOBuilder recordVOBuilder,
            CustomerProfileRequestDTO requestDTO) {
        Map<Integer, CustomerProfileRequestDTO.PetProfileDTO> petMap;
        if (Objects.nonNull(requestDTO) && !CollectionUtils.isEmpty(requestDTO.getPets())) {
            petMap = requestDTO.getPets().stream()
                    .collect(Collectors.toMap(CustomerProfileRequestDTO.PetProfileDTO::getPetId, Function.identity()));
        } else {
            petMap = Map.of();
        }
        List<CompletableFuture<Void>> futures = new ArrayList<>();
        Set<Integer> serviceIds = new HashSet<>();

        var condList = new ArrayList<CustomizedServiceQueryCondition>();

        List<AbandonClientRecordVO.AbandonPetDetailVO> pets = abandonRecordPetList.stream()
                .map(abandonRecordPet -> {
                    var cb = CustomizedServiceQueryCondition.newBuilder();
                    if (isNormal(abandonRecordPet.getServiceId())) {
                        cb.setServiceId(abandonRecordPet.getServiceId());
                        cb.setBusinessId(businessId);
                        if (isNormal(abandonRecordPet.getPetId())) {
                            cb.setPetId(abandonRecordPet.getPetId());
                        }
                        if (isNormal(abandonRecord.getStaffId())) {
                            cb.setStaffId(abandonRecord.getStaffId());
                        }
                        condList.add(cb.build());
                    }

                    // Aggregate serviceId and addOnIds
                    if (Objects.nonNull(abandonRecordPet.getServiceId())) {
                        serviceIds.add(abandonRecordPet.getServiceId());
                    }
                    List<Integer> addOnIds = StringUtils.hasText(abandonRecordPet.getAddonIds())
                            ? JsonUtil.toList(abandonRecordPet.getAddonIds(), Integer.class)
                            : List.of();
                    if (!CollectionUtils.isEmpty(addOnIds)) {
                        serviceIds.addAll(addOnIds);
                    }
                    Map<String, Object> questionAnswerMap = Map.of();
                    if (StringUtils.hasText(abandonRecordPet.getPetQuestionAnswers())) {
                        questionAnswerMap =
                                JsonUtil.toBean(abandonRecordPet.getPetQuestionAnswers(), new TypeRef<>() {});
                    }
                    // Existing pet take biz's pet info instead of abandon data
                    if (Objects.nonNull(abandonRecordPet.getPetId())) {
                        CustomerProfileRequestDTO.PetProfileDTO pet = petMap.get(abandonRecordPet.getPetId());
                        if (pet != null) {
                            return ProfileRequestMapper.INSTANCE.dto2AbandonPetDetailVO(pet).toBuilder()
                                    .serviceId(abandonRecordPet.getServiceId())
                                    .addOnIds(addOnIds)
                                    .questionAnswerList(questionAnswer2List(
                                            QuestionMapper.INSTANCE.replaceRequestAnswer(
                                                    questionAnswerMap, pet.getCustomQuestions()),
                                            questionMap))
                                    .build();
                        } else {
                            // Pet not found in business, reset petId
                            Integer petId = abandonRecordPet.getPetId();
                            futures.add(CompletableFuture.runAsync(
                                    () -> resetPetIdByBookingFlowId(abandonRecordPet, petId)));
                            abandonRecordPet.setPetId(null);
                        }
                    }
                    AbandonClientRecordVO.AbandonPetDetailVO abandonPetDetailVO =
                            AbandonRecordPetMapper.INSTANCE.entity2VO(abandonRecordPet);
                    if (StringUtils.hasText(abandonRecordPet.getPetQuestionAnswers())) {
                        return abandonPetDetailVO.toBuilder()
                                .questionAnswerList(questionAnswer2List(questionAnswerMap, questionMap))
                                .build();
                    }
                    return abandonPetDetailVO;
                })
                .toList();
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
        recordVOBuilder.pets(pets);
        if (CollectionUtils.isEmpty(serviceIds)) {
            recordVOBuilder.services(List.of());
            recordVOBuilder.petDetails(List.of());
        } else {
            recordVOBuilder.services(
                    companyGroomingServiceQueryService
                            .groomingServiceSelectByBusinessIdServiceIds(businessId, new ArrayList<>(serviceIds))
                            .stream()
                            .map(GroomingServiceMapper.INSTANCE::entity2VO)
                            .toList());

            var pds = !condList.isEmpty()
                    ? buildAbandonClientRecordVOPetDetail(abandonRecord, abandonRecordPetList, condList)
                    : List.<AbandonClientRecordVO.PetDetail>of();
            recordVOBuilder.petDetails(pds);
        }
    }

    private List<AbandonClientRecordVO.PetDetail> buildAbandonClientRecordVOPetDetail(
            MoeBookOnlineAbandonRecord abandonRecord,
            List<MoeBookOnlineAbandonRecordPet> abandonRecordPetList,
            ArrayList<CustomizedServiceQueryCondition> condList) {

        var builder = BatchGetCustomizedServiceRequest.newBuilder()
                .setCompanyId(businessInfoHelper.getCompanyIdByBusinessId(abandonRecord.getBusinessId()));
        builder.addAllQueryConditionList(condList);

        var customizedServiceList =
                serviceStub.batchGetCustomizedService(builder.build()).getCustomizedServiceListList();

        var petDetails = new ArrayList<AbandonClientRecordVO.PetDetail>();
        var abandonPets = abandonRecordPetList.stream()
                .filter(e -> isNormal(e.getServiceId()))
                .toList();
        for (var abandonRecordPet : abandonPets) {
            var customizedService = findCustomizedService(
                    customizedServiceList,
                    abandonRecordPet.getServiceId(),
                    abandonRecordPet.getPetId(),
                    abandonRecord.getStaffId());
            if (customizedService == null) {
                continue;
            }

            var pd = AbandonClientRecordVO.PetDetail.builder()
                    .petId(abandonRecordPet.getPetId())
                    .serviceId(abandonRecordPet.getServiceId())
                    .staffId(abandonRecord.getStaffId())
                    .serviceName(customizedService.getName())
                    .serviceType((byte) customizedService.getType().getNumber())
                    .duration(customizedService.getDuration())
                    .price(BigDecimal.valueOf(customizedService.getPrice()))
                    .priceOverrideType(customizedService.getPriceOverrideType())
                    .durationOverrideType(customizedService.getDurationOverrideType())
                    .build();

            petDetails.add(pd);
        }

        return petDetails;
    }

    @Nullable
    private static CustomizedServiceView findCustomizedService(
            List<BatchGetCustomizedServiceResponse.ServiceWithCustomizedInfo> customizedServiceList,
            Integer serviceId,
            @Nullable Integer petId,
            @Nullable Integer staffId) {
        return customizedServiceList.stream()
                .filter(e -> {
                    var cond = e.getQueryCondition();

                    return serviceId == cond.getServiceId()
                            && (!isNormal(petId) && !isNormal(cond.getPetId())
                                    || isNormal(petId) && petId == cond.getPetId())
                            && (!isNormal(staffId) && !isNormal(cond.getStaffId())
                                    || isNormal(staffId) && staffId == cond.getStaffId());
                })
                .findFirst()
                .map(BatchGetCustomizedServiceResponse.ServiceWithCustomizedInfo::getCustomizedService)
                .orElse(null);
    }

    private void resetPetIdByBookingFlowId(MoeBookOnlineAbandonRecordPet abandonRecordPet, Integer petId) {
        abandonRecordPetMapper.resetPetIdByBookingFlowId(
                abandonRecordPet.getBusinessId(),
                abandonRecordPet.getBookingFlowId(),
                Collections.singletonList(petId));
    }

    public void unAssociateCustomer(AssociateCustomerDTO associateCustomerDTO) {
        MoeBookOnlineAbandonRecordExample example = new MoeBookOnlineAbandonRecordExample();
        example.createCriteria()
                .andBusinessIdEqualTo(associateCustomerDTO.businessId())
                .andCustomerIdEqualTo(associateCustomerDTO.customerId())
                .andIsDeletedEqualTo(Boolean.FALSE)
                .andAbandonStatusIn(new ArrayList<>(SearchAbandonedClientParam.AbandonStatus.NOT_RECOVERED_STATUSES));
        example.setOrderByClause("abandon_time desc");
        List<MoeBookOnlineAbandonRecord> abandonRecords = abandonRecordMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(abandonRecords)) {
            return;
        }
        abandonRecords.forEach(abandonRecord -> {
            abandonRecordMapper.resetCustomerIdById(abandonRecord.getId());
            abandonRecordPetMapper.resetPetIdByBookingFlowId(
                    abandonRecord.getBusinessId(), abandonRecord.getBookingFlowId(), Collections.emptyList());
        });
        log.warn(
                "un associate customer and pet success, businessId: {}, customerId: {}",
                associateCustomerDTO.businessId(),
                associateCustomerDTO.customerId());
    }

    public Boolean associateCustomer(AssociateCustomerDTO associateCustomerDTO) {
        String phone = associateCustomerDTO.phoneNumber();
        if (!StringUtils.hasText(phone)) {
            return false;
        }

        MigrateInfo migrateInfo = migrateHelper.getMigrationInfo(associateCustomerDTO.businessId());
        List<MoeBookOnlineAbandonRecord> abandonRecords;
        if (migrateInfo.isMigrate()) {
            abandonRecords = listNonRecoveredRecordByPhoneForCompany(migrateInfo.companyId(), phone);
            if (CollectionUtils.isEmpty(abandonRecords)) {
                return Boolean.FALSE;
            }

            // 将 abandon records 按照 businessID 分组，每组取 update time 最大的
            abandonRecords.stream()
                    .collect(Collectors.groupingBy(MoeBookOnlineAbandonRecord::getBusinessId))
                    .forEach((businessId, records) -> setCustomerId(records, associateCustomerDTO.customerId()));
        } else {
            abandonRecords = listNonRecoveredRecordByPhoneForBusiness(associateCustomerDTO.businessId(), phone);
            if (CollectionUtils.isEmpty(abandonRecords)) {
                return Boolean.FALSE;
            }

            setCustomerId(abandonRecords, associateCustomerDTO.customerId());
        }
        return Boolean.TRUE;
    }

    private void setCustomerId(List<MoeBookOnlineAbandonRecord> abandonRecords, Integer customerId) {
        List<Integer> noCustomerIdAbandonRecordIds = abandonRecords.stream()
                .filter(e -> e.getCustomerId() == null)
                .map(MoeBookOnlineAbandonRecord::getId)
                .toList();

        if (!ObjectUtils.isEmpty(noCustomerIdAbandonRecordIds)) {
            MoeBookOnlineAbandonRecord updateBean = new MoeBookOnlineAbandonRecord();
            updateBean.setCustomerId(customerId);

            MoeBookOnlineAbandonRecordExample example = new MoeBookOnlineAbandonRecordExample();
            example.createCriteria().andIdIn(noCustomerIdAbandonRecordIds).andCustomerIdIsNull();

            abandonRecordMapper.updateByExampleSelective(updateBean, example);
        }
    }

    public Boolean associateCustomerAndPet(AssociateCustomerAndPetDTO associateCustomerAndPetDTO) {
        String phoneNumber = associateCustomerAndPetDTO.phoneNumber();
        if (!StringUtils.hasText(phoneNumber)) {
            return false;
        }

        Integer businessId = associateCustomerAndPetDTO.businessId();
        Integer customerId = associateCustomerAndPetDTO.customerId();

        MigrateInfo migrateInfo = migrateHelper.getMigrationInfo(businessId);
        List<MoeBookOnlineAbandonRecord> abandonRecords;
        if (migrateInfo.isMigrate()) {
            abandonRecords = listNonRecoveredRecordByPhoneForCompany(migrateInfo.companyId(), phoneNumber);
            if (CollectionUtils.isEmpty(abandonRecords)) {
                return Boolean.FALSE;
            }
            // 将 abandon records 按照 businessID 分组，每组取 update time 最大的
            Map<Integer, List<MoeBookOnlineAbandonRecord>> abandonRecordMapByBusinessId =
                    abandonRecords.stream().collect(Collectors.groupingBy(MoeBookOnlineAbandonRecord::getBusinessId));
            for (Map.Entry<Integer, List<MoeBookOnlineAbandonRecord>> entry : abandonRecordMapByBusinessId.entrySet()) {
                Integer currentBusinessId = entry.getKey();
                List<MoeBookOnlineAbandonRecord> currentAbandonRecords = entry.getValue();

                // associate customer
                setCustomerId(currentAbandonRecords, customerId);

                // associate pets
                List<AssociateCustomerAndPetDTO.AssociatePetDTO> pets = associateCustomerAndPetDTO.pets();
                if (CollectionUtils.isEmpty(pets)) {
                    return Boolean.TRUE;
                }

                for (MoeBookOnlineAbandonRecord abandonRecord : currentAbandonRecords) {
                    setPetId(currentBusinessId, abandonRecord.getBookingFlowId(), pets);
                }
            }

        } else {
            abandonRecords = listNonRecoveredRecordByPhoneForBusiness(businessId, phoneNumber);
            if (CollectionUtils.isEmpty(abandonRecords)) {
                return Boolean.FALSE;
            }

            // associate customer
            setCustomerId(abandonRecords, customerId);

            // associate pets
            List<AssociateCustomerAndPetDTO.AssociatePetDTO> pets = associateCustomerAndPetDTO.pets();
            if (CollectionUtils.isEmpty(pets)) {
                return Boolean.TRUE;
            }

            for (MoeBookOnlineAbandonRecord abandonRecord : abandonRecords) {
                setPetId(businessId, abandonRecord.getBookingFlowId(), pets);
            }
        }

        return Boolean.TRUE;
    }

    private void setPetId(
            Integer businessId, String bookingFlowId, List<AssociateCustomerAndPetDTO.AssociatePetDTO> pets) {
        Map<String, Integer> petKeyMap = pets.stream()
                .collect(Collectors.toMap(
                        pet -> pet.petTypeId() + pet.breed() + pet.petName(),
                        AssociateCustomerAndPetDTO.AssociatePetDTO::petId));
        List<MoeBookOnlineAbandonRecordPet> abandonRecordPetList =
                abandonRecordPetMapper.listRecordPetByBookingFlowId(businessId, bookingFlowId);
        abandonRecordPetList.forEach(abandonRecordPet -> {
            if (Objects.nonNull(abandonRecordPet.getPetId())) {
                return;
            }
            String petKey =
                    abandonRecordPet.getPetTypeId() + abandonRecordPet.getBreed() + abandonRecordPet.getPetName();
            Integer petId = petKeyMap.get(petKey);
            if (Objects.isNull(petId)) {
                return;
            }
            MoeBookOnlineAbandonRecordPet updateRecordPet = new MoeBookOnlineAbandonRecordPet();
            updateRecordPet.setId(abandonRecordPet.getId());
            updateRecordPet.setPetId(petId);
            abandonRecordPetMapper.updateByPrimaryKeySelective(updateRecordPet);
        });
    }

    private List<MoeBookOnlineAbandonRecord> listNonRecoveredRecordByPhoneForBusiness(
            Integer businessId, String phoneNumber) {
        MoeBookOnlineAbandonRecordExample example = new MoeBookOnlineAbandonRecordExample();
        example.setOrderByClause("update_time DESC");
        example.createCriteria()
                .andBusinessIdEqualTo(businessId)
                .andPhoneNumberEqualTo(phoneNumber)
                .andAbandonTimeGreaterThanOrEqualTo(
                        Instant.now().minus(30, DAYS).getEpochSecond())
                .andIsDeletedEqualTo(false)
                .andAbandonStatusIn(List.copyOf(NOT_RECOVERED_STATUSES))
                .andAbandonStepIn(
                        listRecoverableSteps().stream().map(Enum::name).toList());
        return abandonRecordMapper.selectByExample(example);
    }

    private List<MoeBookOnlineAbandonRecord> listNonRecoveredRecordByPhoneForCompany(
            Long companyId, String phoneNumber) {
        MoeBookOnlineAbandonRecordExample example = new MoeBookOnlineAbandonRecordExample();
        example.setOrderByClause("update_time DESC");
        example.createCriteria()
                .andCompanyIdEqualTo(companyId)
                .andPhoneNumberEqualTo(phoneNumber)
                .andAbandonTimeGreaterThanOrEqualTo(
                        Instant.now().minus(30, DAYS).getEpochSecond())
                .andIsDeletedEqualTo(false)
                .andAbandonStatusIn(List.copyOf(NOT_RECOVERED_STATUSES))
                .andAbandonStepIn(
                        listRecoverableSteps().stream().map(Enum::name).toList());
        return abandonRecordMapper.selectByExample(example);
    }

    public List<AbandonClientRecordVO.QuestionAnswerVO> questionAnswer2List(
            Map<String, Object> questionAnswerMap, Map<String, MoeBookOnlineQuestion> questionMap) {
        if (CollectionUtils.isEmpty(questionAnswerMap)) {
            return List.of();
        }
        return questionAnswerMap.entrySet().stream()
                .map(entry -> {
                    MoeBookOnlineQuestion question = questionMap.get(entry.getKey());
                    if (Objects.isNull(question)) {
                        return AbandonClientRecordVO.QuestionAnswerVO.builder()
                                .key(entry.getKey())
                                .question("")
                                .answer(entry.getValue().toString())
                                .build();
                    }
                    return AbandonClientRecordVO.QuestionAnswerVO.builder()
                            .key(entry.getKey())
                            .question(question.getQuestion())
                            .answer(entry.getValue().toString())
                            .build();
                })
                .toList();
    }

    public void batchAddNewPet(String bookingFlowId) {
        MoeBookOnlineAbandonRecord abandonRecord = abandonRecordMapper.selectUndeletedAbandonRecordByBookingFlowId(
                AuthContext.get().getBusinessId(), bookingFlowId);
        if (Objects.isNull(abandonRecord) || Objects.isNull(abandonRecord.getCustomerId())) {
            throw bizException(Code.CODE_PARAMS_ERROR, "Abandon record not found");
        }
        List<MoeBookOnlineAbandonRecordPet> abandonRecordPetList = abandonRecordPetMapper.listRecordPetByBookingFlowId(
                AuthContext.get().getBusinessId(), bookingFlowId);
        if (CollectionUtils.isEmpty(abandonRecordPetList)) {
            return;
        }
        abandonRecordPetList.forEach(abandonRecordPet -> {
            // New pet need to be created
            if (Objects.nonNull(abandonRecordPet.getPetId())) {
                return;
            }
            CustomerPetAddParams customerPetAddParams = AbandonRecordPetMapper.INSTANCE.entity2Params(abandonRecordPet);
            customerPetAddParams.setCustomerId(abandonRecord.getCustomerId());
            Integer petId = petService.addPet(customerPetAddParams);
            MoeBookOnlineAbandonRecordPet updateRecordPet = new MoeBookOnlineAbandonRecordPet();
            updateRecordPet.setId(abandonRecordPet.getId());
            updateRecordPet.setPetId(petId);
            abandonRecordPetMapper.updateByPrimaryKeySelective(updateRecordPet);
        });
    }

    public Set<Integer> listCustomerIdByFilter(ClientsFilterDTO clientsFilterDTO) {
        return abandonRecordMapper.listCustomerIdByFilter(clientsFilterDTO, OBStepEnum.listRecoverableSteps());
    }

    /**
     * Get the latest abandoned record of the customer
     *
     * @param businessId business id
     * @param customerId customer id
     * @return the latest abandoned record
     */
    public MoeBookOnlineAbandonRecord getLatestAbandonedRecord(Integer businessId, Integer customerId) {
        MoeBookOnlineAbandonRecordExample example = new MoeBookOnlineAbandonRecordExample();
        example.createCriteria()
                .andBusinessIdEqualTo(businessId)
                .andCustomerIdEqualTo(customerId)
                .andIsDeletedEqualTo(Boolean.FALSE)
                .andAbandonStatusIn(new ArrayList<>(SearchAbandonedClientParam.AbandonStatus.NOT_RECOVERED_STATUSES));
        example.setOrderByClause("abandon_time desc");
        List<MoeBookOnlineAbandonRecord> abandonRecords = abandonRecordMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(abandonRecords)) {
            return null;
        }
        return abandonRecords.get(0);
    }

    public Boolean updateLastTextedTime(Integer businessId, Integer customerId, Long textedTime) {
        MoeBookOnlineAbandonRecord latest = getLatestAbandonedRecord(businessId, customerId);
        if (Objects.isNull(latest)) {
            return Boolean.FALSE;
        }
        MoeBookOnlineAbandonRecord updateRecord = new MoeBookOnlineAbandonRecord();
        updateRecord.setId(latest.getId());
        updateRecord.setAbandonStatus(
                SearchAbandonedClientParam.AbandonStatus.CONTACTED.name().toLowerCase());
        updateRecord.setLastTextedTime(textedTime);
        boolean updateResult = abandonRecordMapper.updateByPrimaryKeySelective(updateRecord) > 0;
        if (updateResult) {
            deleteHistoricalAbandonedRecords(businessId, customerId, latest.getAbandonTime());
        }
        return updateResult;
    }

    public Boolean updateLastEmailedTime(CustomerIdsParams params, Long emailedTime) {
        if (Objects.isNull(params.getBusinessId()) || CollectionUtils.isEmpty(params.getCustomerIds())) {
            return Boolean.FALSE;
        }
        List<OBAbandonRecordDTO> abandonRecords = abandonRecordMapper.getLatestAbandonedRecordByCustomerIds(
                params.getBusinessId(), params.getCustomerIds());
        if (CollectionUtils.isEmpty(abandonRecords)) {
            return Boolean.FALSE;
        }
        ThreadPool.execute(() -> abandonRecords.forEach(record -> {
            MoeBookOnlineAbandonRecord updateRecord = new MoeBookOnlineAbandonRecord();
            updateRecord.setId(record.getId());
            updateRecord.setAbandonStatus(
                    SearchAbandonedClientParam.AbandonStatus.CONTACTED.name().toLowerCase());
            updateRecord.setLastEmailedTime(emailedTime);
            int affectedRows = abandonRecordMapper.updateByPrimaryKeySelective(updateRecord);
            if (affectedRows > 0) {
                deleteHistoricalAbandonedRecords(
                        record.getBusinessId(), record.getCustomerId(), record.getAbandonTime());
            }
        }));
        return Boolean.TRUE;
    }

    public void deleteHistoricalAbandonedRecords(Integer businessId, Integer customerId, Long abandonTime) {
        MoeBookOnlineAbandonRecordExample example = new MoeBookOnlineAbandonRecordExample();
        example.createCriteria()
                .andBusinessIdEqualTo(businessId)
                .andCustomerIdEqualTo(customerId)
                .andIsDeletedEqualTo(Boolean.FALSE)
                .andAbandonTimeLessThan(abandonTime)
                .andAbandonTimeGreaterThan(Instant.now().minus(30, DAYS).getEpochSecond())
                .andAbandonStatusEqualTo(SearchAbandonedClientParam.AbandonStatus.ABANDONED.getValue());
        MoeBookOnlineAbandonRecord update = new MoeBookOnlineAbandonRecord();
        update.setIsDeleted(Boolean.TRUE);
        update.setDeleteType(AbandonDeleteTypeEnum.HISTORICAL_DATA_OVERWRITTEN.getType());
        abandonRecordMapper.updateByExampleSelective(update, example);
    }

    public List<MoeBookOnlineAbandonRecord> listBySearchParam(Integer businessId, SearchAbandonedClientParam param) {
        SearchAbandonedClientParam.TimeRange timeRange = param.getFilter().timeRange();
        if (timeRange == null || timeRange.startTimeSec() == null || timeRange.endTimeSec() == null) {
            throw bizException(Code.CODE_PARAMS_ERROR);
        }
        String keyword = Optional.ofNullable(param.getQuery())
                .map(SearchAbandonedClientParam.Query::keyword)
                .orElse(null);
        Set<String> bookingFlowIds = new HashSet<>();
        Set<Integer> customerIds = new HashSet<>();
        Set<Integer> addressIds = new HashSet<>();
        boolean hasKeyword = StringUtils.hasText(keyword);
        // The result of query by keyword is or relationship
        if (hasKeyword) {
            bookingFlowIds = getBookingFlowIdsByKeyword(businessId, keyword);
            customerIds = getCustomerIdsByKeyword(businessId, timeRange, keyword);
            addressIds = getAddressIdsByKeyword(businessId, timeRange, keyword);
            if (CollectionUtils.isEmpty(bookingFlowIds)
                    && CollectionUtils.isEmpty(customerIds)
                    && CollectionUtils.isEmpty(addressIds)) {
                return List.of();
            }
        }
        SearchAbandonedRecordParam searchParam = AbandonRecordMapper.INSTANCE.filter2param(param.getFilter());
        searchParam.setBusinessId(businessId);
        searchParam.setHasKeyword(hasKeyword);
        searchParam.setKeywordBookingFlowIds(bookingFlowIds);
        searchParam.setKeywordCustomerIds(customerIds);
        searchParam.setKeywordAddressIds(addressIds);
        List<MoeBookOnlineAbandonRecord> records = abandonRecordMapper.searchByFilterPram(searchParam);
        return OBAbandonedUtil.filterLatestRecords(records);
    }

    private Set<String> getBookingFlowIdsByKeyword(Integer businessId, String keyword) {
        if (!StringUtils.hasText(keyword)) {
            return new HashSet<>();
        }
        Set<String> ids = new HashSet<>();
        ids.addAll(abandonRecordMapper.selectBookingFlowIdsByKeyword(businessId, keyword));
        ids.addAll(abandonRecordPetMapper.selectBookingFlowIdsByKeyword(businessId, keyword));
        return ids;
    }

    private Set<Integer> getCustomerIdsByKeyword(
            Integer businessId, SearchAbandonedClientParam.TimeRange timeRange, String keyword) {
        Set<Integer> allCustomerIds = abandonRecordMapper.getCustomerIds(
                businessId,
                timeRange.startTimeSec(),
                timeRange.endTimeSec(),
                new ArrayList<>(OBStepEnum.listRecoverableSteps()));
        return customerApi.findCustomerIdsByKeyword(new FindCustomerIdsByKeywordParam()
                .setBusinessId(businessId)
                .setCustomerIds(allCustomerIds)
                .setKeyword(keyword));
    }

    private Set<Integer> getAddressIdsByKeyword(
            Integer businessId, SearchAbandonedClientParam.TimeRange timeRange, String keyword) {
        Set<Integer> allAddressIds = abandonRecordMapper.getAddressIds(
                businessId,
                timeRange.startTimeSec(),
                timeRange.endTimeSec(),
                new ArrayList<>(OBStepEnum.listRecoverableSteps()));
        return customerAddressApi.findAddressIdsByKeyword(new FindAddressIdsByKeywordParam()
                .setBusinessId(businessId)
                .setAddressIds(allAddressIds)
                .setKeyword(keyword));
    }

    /**
     * Trigger event for abandoned record
     * 目前任务设置是，每隔 15 分钟的一次任务扫最近 20 分钟的记录
     */
    public void abandonRecordSendEvent() {
        long endTime = Instant.now().minus(1, HOURS).getEpochSecond();
        long startTime = endTime - 20 * 60;
        List<Integer> abandonRecordIdList = abandonRecordMapper.selectByAbandonTimeRange(startTime, endTime);
        if (CollectionUtils.isEmpty(abandonRecordIdList)) {
            return;
        }

        // 循环前，通过 filter 处理重复记录
        MoeBookOnlineAbandonRecordExample example = new MoeBookOnlineAbandonRecordExample();
        MoeBookOnlineAbandonRecordExample.Criteria criteria = example.createCriteria();
        criteria.andIdIn(abandonRecordIdList);
        var abandonRecordList = abandonRecordMapper.selectByExample(example);
        var filterARList = OBAbandonedUtil.filterLatestRecords(abandonRecordList);

        // 开始循环
        for (var abandonRecord : filterARList) {
            try {
                // 有唯一索引，插入成功相当于拿到锁，否则跳过推送
                var eventLog = new MoeBookOnlineAbandonRecordEventLog();
                eventLog.setAbandonRecordId(abandonRecord.getId());
                abandonRecordEventLogMapper.insertSelective(eventLog);
                // 开始触发 event
                onlineBookingProducer.pushOnlineBookingAbandonedEvent(abandonRecord);
            } catch (DuplicateKeyException e) {
                log.info("abandon record event log already exist, abandonRecordId: {}", abandonRecord.getId());
            }
        }
    }
}
