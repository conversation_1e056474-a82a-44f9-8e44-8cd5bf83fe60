package com.moego.server.grooming.mapper;

import com.moego.server.grooming.mapperbean.ObConfigTeam;
import com.moego.server.grooming.mapperbean.ObConfigTeamExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface ObConfigTeamMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table ob_config_team
     *
     * @mbg.generated
     */
    long countByExample(ObConfigTeamExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table ob_config_team
     *
     * @mbg.generated
     */
    int deleteByExample(ObConfigTeamExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table ob_config_team
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table ob_config_team
     *
     * @mbg.generated
     */
    int insert(ObConfigTeam record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table ob_config_team
     *
     * @mbg.generated
     */
    int insertSelective(ObConfigTeam record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table ob_config_team
     *
     * @mbg.generated
     */
    List<ObConfigTeam> selectByExample(ObConfigTeamExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table ob_config_team
     *
     * @mbg.generated
     */
    ObConfigTeam selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table ob_config_team
     *
     * @mbg.generated
     */
    int updateByExampleSelective(@Param("record") ObConfigTeam record, @Param("example") ObConfigTeamExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table ob_config_team
     *
     * @mbg.generated
     */
    int updateByExample(@Param("record") ObConfigTeam record, @Param("example") ObConfigTeamExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table ob_config_team
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(ObConfigTeam record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table ob_config_team
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(ObConfigTeam record);

    /**
     * Batch insert, use default value if the property is null.
     *
     * @param entities entities
     * @return insert count
     */
    int batchInsertSelective(@Param("entities") List<ObConfigTeam> entities);
}
