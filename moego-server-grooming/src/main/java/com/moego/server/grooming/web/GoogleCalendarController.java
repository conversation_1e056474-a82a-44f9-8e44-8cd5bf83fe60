package com.moego.server.grooming.web;

import com.moego.common.constant.CommonConstant;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.server.grooming.service.CalendarSyncService;
import com.moego.server.grooming.service.dto.GoogleCalendarAuthInfoDto;
import com.moego.server.grooming.service.dto.GoogleCalendarSettingDto;
import com.moego.server.grooming.web.vo.CreateCalendarSyncSettingVo;
import com.moego.server.grooming.web.vo.GoogleCalendarOauthVo;
import com.moego.server.grooming.web.vo.UpdateCalendarSyncSettingVo;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/grooming/google/calendar")
public class GoogleCalendarController {

    @Autowired
    private CalendarSyncService calendarSyncService;

    @GetMapping("/setting")
    @Auth(AuthType.BUSINESS)
    public GoogleCalendarSettingDto queryGoogleCalendarSetting(AuthContext context) {
        return calendarSyncService.getCalendarSettingDto(context.getBusinessId(), context.getStaffId(), true);
    }

    @PostMapping("/setting")
    @Auth(AuthType.BUSINESS)
    public GoogleCalendarSettingDto createGooGleCalendarSetting(
            AuthContext context, @Valid @RequestBody CreateCalendarSyncSettingVo settingVo) {
        return calendarSyncService.createGooGleCalendarSetting(
                context.getBusinessId(), context.companyId(), context.getStaffId(), settingVo);
    }

    @PutMapping("/setting")
    @Auth(AuthType.BUSINESS)
    public Boolean updateGooGleCalendarSetting(
            AuthContext context, @Valid @RequestBody UpdateCalendarSyncSettingVo settingVo) {
        return calendarSyncService.updateGooGleCalendarSetting(
                context.getBusinessId(), context.getStaffId(), settingVo);
    }

    @PostMapping("/oauth/save")
    @Auth(AuthType.BUSINESS)
    public GoogleCalendarAuthInfoDto saveOauthResult(
            AuthContext context, @Valid @RequestBody GoogleCalendarOauthVo oauthVo) {
        return calendarSyncService.saveAuthResult(
                context.getBusinessId(),
                context.companyId(),
                context.getStaffId(),
                oauthVo.getCode(),
                oauthVo.getScope());
    }

    @PostMapping("/event/webhook")
    @Auth(AuthType.ANONYMOUS)
    public void watchEventWebHook(HttpServletRequest request) {
        String channelUuid = request.getHeader(CommonConstant.X_GOOG_CHANNEL_ID);
        calendarSyncService.freshWebHookLastNotifyTime(channelUuid);
    }
}
