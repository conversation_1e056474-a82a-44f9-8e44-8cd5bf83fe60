package com.moego.server.grooming.service;

import com.moego.idl.models.errors.v1.Code;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.server.grooming.mapper.MoeQuestionnaireMapper;
import com.moego.server.grooming.mapperbean.MoeQuestionnaire;
import com.moego.server.grooming.web.params.SubmitQuestionnaireParams;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class MoeQuestionnaireService {

    private final MoeQuestionnaireMapper moeQuestionnaireMapper;

    public MoeQuestionnaire getMoeQuestionnaire(Long businessId, Long staffId) {
        return moeQuestionnaireMapper.queryOne(businessId, staffId);
    }

    public void submitQuestionnaire(SubmitQuestionnaireParams params) {
        if (moeQuestionnaireMapper.queryOne(params.businessId(), params.staffId()) != null) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "Questionnaire already submitted");
        }
        MoeQuestionnaire record = new MoeQuestionnaire();
        record.setCompanyId(params.companyId());
        record.setBusinessId(params.businessId());
        record.setCreatedBy(params.staffId());
        record.setFormDetail(params.formDetail());
        moeQuestionnaireMapper.insertSelective(record);
    }
}
