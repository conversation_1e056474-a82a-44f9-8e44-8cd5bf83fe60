package com.moego.server.grooming.mapperbean;

import com.moego.server.grooming.mapper.po.DatePreferencePO;
import com.moego.server.grooming.mapper.po.StaffPreferencePO;
import com.moego.server.grooming.mapper.po.TimePreferencePO;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table moe_wait_list
 */
public class MoeWaitList {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_wait_list.id
     *
     * @mbg.generated
     */
    private Long id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_wait_list.business_id
     *
     * @mbg.generated
     */
    private Long businessId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_wait_list.company_id
     *
     * @mbg.generated
     */
    private Long companyId;

    /**
     * Database Column Remarks:
     *   预约订单主表id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_wait_list.appointment_id
     *
     * @mbg.generated
     */
    private Long appointmentId;

    /**
     * Database Column Remarks:
     *   support 1: Exact date 2: Day of the week (multiple choice) 3: Any date
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_wait_list.date_preference
     *
     * @mbg.generated
     */
    private DatePreferencePO datePreference;

    /**
     * Database Column Remarks:
     *   support 1: Exact start time 2: Morning/Afternoon/Evening 3: Any time
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_wait_list.time_preference
     *
     * @mbg.generated
     */
    private TimePreferencePO timePreference;

    /**
     * Database Column Remarks:
     *   support 1: Select specific staff (multiple choice)  2: Anyone
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_wait_list.staff_preference
     *
     * @mbg.generated
     */
    private StaffPreferencePO staffPreference;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_wait_list.valid_from
     *
     * @mbg.generated
     */
    private LocalDate validFrom;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_wait_list.valid_till
     *
     * @mbg.generated
     */
    private LocalDate validTill;

    /**
     * Database Column Remarks:
     *   create time
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_wait_list.created_at
     *
     * @mbg.generated
     */
    private LocalDateTime createdAt;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_wait_list.created_by
     *
     * @mbg.generated
     */
    private Long createdBy;

    /**
     * Database Column Remarks:
     *   update time
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_wait_list.updated_at
     *
     * @mbg.generated
     */
    private LocalDateTime updatedAt;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_wait_list.updated_by
     *
     * @mbg.generated
     */
    private Long updatedBy;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_wait_list.deleted_at
     *
     * @mbg.generated
     */
    private LocalDateTime deletedAt;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_wait_list.id
     *
     * @return the value of moe_wait_list.id
     *
     * @mbg.generated
     */
    public Long getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_wait_list.id
     *
     * @param id the value for moe_wait_list.id
     *
     * @mbg.generated
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_wait_list.business_id
     *
     * @return the value of moe_wait_list.business_id
     *
     * @mbg.generated
     */
    public Long getBusinessId() {
        return businessId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_wait_list.business_id
     *
     * @param businessId the value for moe_wait_list.business_id
     *
     * @mbg.generated
     */
    public void setBusinessId(Long businessId) {
        this.businessId = businessId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_wait_list.company_id
     *
     * @return the value of moe_wait_list.company_id
     *
     * @mbg.generated
     */
    public Long getCompanyId() {
        return companyId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_wait_list.company_id
     *
     * @param companyId the value for moe_wait_list.company_id
     *
     * @mbg.generated
     */
    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_wait_list.appointment_id
     *
     * @return the value of moe_wait_list.appointment_id
     *
     * @mbg.generated
     */
    public Long getAppointmentId() {
        return appointmentId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_wait_list.appointment_id
     *
     * @param appointmentId the value for moe_wait_list.appointment_id
     *
     * @mbg.generated
     */
    public void setAppointmentId(Long appointmentId) {
        this.appointmentId = appointmentId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_wait_list.date_preference
     *
     * @return the value of moe_wait_list.date_preference
     *
     * @mbg.generated
     */
    public DatePreferencePO getDatePreference() {
        return datePreference;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_wait_list.date_preference
     *
     * @param datePreference the value for moe_wait_list.date_preference
     *
     * @mbg.generated
     */
    public void setDatePreference(DatePreferencePO datePreference) {
        this.datePreference = datePreference;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_wait_list.time_preference
     *
     * @return the value of moe_wait_list.time_preference
     *
     * @mbg.generated
     */
    public TimePreferencePO getTimePreference() {
        return timePreference;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_wait_list.time_preference
     *
     * @param timePreference the value for moe_wait_list.time_preference
     *
     * @mbg.generated
     */
    public void setTimePreference(TimePreferencePO timePreference) {
        this.timePreference = timePreference;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_wait_list.staff_preference
     *
     * @return the value of moe_wait_list.staff_preference
     *
     * @mbg.generated
     */
    public StaffPreferencePO getStaffPreference() {
        return staffPreference;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_wait_list.staff_preference
     *
     * @param staffPreference the value for moe_wait_list.staff_preference
     *
     * @mbg.generated
     */
    public void setStaffPreference(StaffPreferencePO staffPreference) {
        this.staffPreference = staffPreference;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_wait_list.valid_from
     *
     * @return the value of moe_wait_list.valid_from
     *
     * @mbg.generated
     */
    public LocalDate getValidFrom() {
        return validFrom;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_wait_list.valid_from
     *
     * @param validFrom the value for moe_wait_list.valid_from
     *
     * @mbg.generated
     */
    public void setValidFrom(LocalDate validFrom) {
        this.validFrom = validFrom;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_wait_list.valid_till
     *
     * @return the value of moe_wait_list.valid_till
     *
     * @mbg.generated
     */
    public LocalDate getValidTill() {
        return validTill;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_wait_list.valid_till
     *
     * @param validTill the value for moe_wait_list.valid_till
     *
     * @mbg.generated
     */
    public void setValidTill(LocalDate validTill) {
        this.validTill = validTill;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_wait_list.created_at
     *
     * @return the value of moe_wait_list.created_at
     *
     * @mbg.generated
     */
    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_wait_list.created_at
     *
     * @param createdAt the value for moe_wait_list.created_at
     *
     * @mbg.generated
     */
    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_wait_list.created_by
     *
     * @return the value of moe_wait_list.created_by
     *
     * @mbg.generated
     */
    public Long getCreatedBy() {
        return createdBy;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_wait_list.created_by
     *
     * @param createdBy the value for moe_wait_list.created_by
     *
     * @mbg.generated
     */
    public void setCreatedBy(Long createdBy) {
        this.createdBy = createdBy;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_wait_list.updated_at
     *
     * @return the value of moe_wait_list.updated_at
     *
     * @mbg.generated
     */
    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_wait_list.updated_at
     *
     * @param updatedAt the value for moe_wait_list.updated_at
     *
     * @mbg.generated
     */
    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_wait_list.updated_by
     *
     * @return the value of moe_wait_list.updated_by
     *
     * @mbg.generated
     */
    public Long getUpdatedBy() {
        return updatedBy;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_wait_list.updated_by
     *
     * @param updatedBy the value for moe_wait_list.updated_by
     *
     * @mbg.generated
     */
    public void setUpdatedBy(Long updatedBy) {
        this.updatedBy = updatedBy;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_wait_list.deleted_at
     *
     * @return the value of moe_wait_list.deleted_at
     *
     * @mbg.generated
     */
    public LocalDateTime getDeletedAt() {
        return deletedAt;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_wait_list.deleted_at
     *
     * @param deletedAt the value for moe_wait_list.deleted_at
     *
     * @mbg.generated
     */
    public void setDeletedAt(LocalDateTime deletedAt) {
        this.deletedAt = deletedAt;
    }
}
