package com.moego.server.grooming.service.dto;

import java.math.BigDecimal;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class MoeGroomingServiceDto {

    private Integer id;
    private Integer businessId;
    private Integer categoryId;
    private String name;
    private String description;
    private Byte type;
    private Integer taxId;
    private BigDecimal price;
    private Integer duration;
    private Byte inactive;
    private Integer sort;
    private String colorCode;
    private Byte status;
    private Long createTime;
    private Long updateTime;
    private Byte showBasePrice;
    private Byte bookOnlineAvailable;
    private Byte isAllStaff;
    private Byte isSavePrice;
    private Byte isSaveDuration;
}
