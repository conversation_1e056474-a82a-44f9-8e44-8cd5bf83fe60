package com.moego.server.grooming.mapstruct;

import com.moego.server.grooming.dto.waitlist.DatePreferenceDTO;
import com.moego.server.grooming.mapper.po.DatePreferencePO;
import com.moego.server.grooming.web.params.waitlist.DatePreference;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface DatePreferenceMapper {
    DatePreferenceMapper INSTANCE = Mappers.getMapper(DatePreferenceMapper.class);

    DatePreferenceDTO toDto(DatePreferencePO po);

    DatePreferencePO toEntity(DatePreference param);
}
