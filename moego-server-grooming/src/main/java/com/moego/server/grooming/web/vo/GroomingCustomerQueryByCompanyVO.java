package com.moego.server.grooming.web.vo;

import com.moego.server.grooming.enums.AppointmentStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;

@Data
public class GroomingCustomerQueryByCompanyVO {

    private Integer companyId;
    private Integer customerId;
    /**
     * 1: asc
     * 2: desc
     */
    private Integer orderType = 2;
    /**
     * 1 createTime
     * 2 appointmentDate
     */
    private Integer orderBy;
    /**
     * 1 upcoming, 2 history, 3 waiting, 4 cancelled, 5 no show
     */
    private Integer type;

    /**
     *
     */
    List<Integer> paymentStatus;

    @Deprecated // use appointmentStatusList instead
    List<Integer> appointmentStatus;

    @Schema(description = "预约状态，1 - unconfirmed，2 - confirmed，3 - finished，4 - cancelled，5 - ready，6 - checkin")
    List<AppointmentStatusEnum> appointmentStatusList;

    private String appointmentDate;
    private Integer endTime;
    private Boolean skipCancel;

    private Integer pageSize = 20;
    private Integer pageNum = 1;

    private String appointmentTime;
    private Integer time;
}
