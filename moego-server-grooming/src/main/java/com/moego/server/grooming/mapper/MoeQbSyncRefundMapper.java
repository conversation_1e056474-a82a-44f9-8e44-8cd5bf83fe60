package com.moego.server.grooming.mapper;

import com.moego.server.grooming.mapperbean.MoeQbSyncRefund;
import com.moego.server.grooming.mapperbean.MoeQbSyncRefundExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface MoeQbSyncRefundMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_qb_sync_refund
     *
     * @mbg.generated
     */
    long countByExample(MoeQbSyncRefundExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_qb_sync_refund
     *
     * @mbg.generated
     */
    int deleteByExample(MoeQbSyncRefundExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_qb_sync_refund
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_qb_sync_refund
     *
     * @mbg.generated
     */
    int insert(MoeQbSyncRefund record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_qb_sync_refund
     *
     * @mbg.generated
     */
    int insertSelective(MoeQbSyncRefund record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_qb_sync_refund
     *
     * @mbg.generated
     */
    List<MoeQbSyncRefund> selectByExampleWithBLOBs(MoeQbSyncRefundExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_qb_sync_refund
     *
     * @mbg.generated
     */
    List<MoeQbSyncRefund> selectByExample(MoeQbSyncRefundExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_qb_sync_refund
     *
     * @mbg.generated
     */
    MoeQbSyncRefund selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_qb_sync_refund
     *
     * @mbg.generated
     */
    int updateByExampleSelective(
            @Param("record") MoeQbSyncRefund record, @Param("example") MoeQbSyncRefundExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_qb_sync_refund
     *
     * @mbg.generated
     */
    int updateByExampleWithBLOBs(
            @Param("record") MoeQbSyncRefund record, @Param("example") MoeQbSyncRefundExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_qb_sync_refund
     *
     * @mbg.generated
     */
    int updateByExample(@Param("record") MoeQbSyncRefund record, @Param("example") MoeQbSyncRefundExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_qb_sync_refund
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(MoeQbSyncRefund record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_qb_sync_refund
     *
     * @mbg.generated
     */
    int updateByPrimaryKeyWithBLOBs(MoeQbSyncRefund record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_qb_sync_refund
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(MoeQbSyncRefund record);

    List<MoeQbSyncRefund> selectByBusinessIdAndPaymentId(
            @Param("businessId") Integer businessId,
            @Param("realmId") String realmId,
            @Param("paymentId") Integer paymentId);

    List<MoeQbSyncRefund> selectByBusinessIdAndRefundIds(
            @Param("businessId") Integer businessId, @Param("refundIds") List<Integer> refundIds);
}
