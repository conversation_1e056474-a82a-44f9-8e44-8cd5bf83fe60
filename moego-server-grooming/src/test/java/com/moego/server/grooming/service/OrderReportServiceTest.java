package com.moego.server.grooming.service;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

import com.moego.common.enums.InvoiceStatusEnum;
import com.moego.idl.models.order.v1.OrderModel;
import com.moego.server.grooming.dto.CustomerGrooming;
import com.moego.server.grooming.dto.ServiceChargeDTO;
import com.moego.server.grooming.enums.AppointmentStatusEnum;
import com.moego.server.grooming.mapper.MoeGroomingAppointmentMapper;
import com.moego.server.grooming.mapper.MoeGroomingServiceOperationMapper;
import com.moego.server.grooming.mapperbean.MoeGroomingInvoice;
import com.moego.server.grooming.mapperbean.MoeGroomingInvoiceItem;
import com.moego.server.grooming.mapperbean.MoeGroomingServiceOperation;
import com.moego.server.grooming.service.dto.ReportAppointmentDAO;
import com.moego.server.grooming.service.report.ReportOrderService;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class OrderReportServiceTest {

    @Mock
    private MoeGroomingAppointmentMapper moeGroomingAppointmentMapper;

    @Mock
    private MoeGroomingAppointmentMapper appointmentMapper;

    @Mock
    private MoeGroomingServiceOperationMapper serviceOperationMapper;

    @Mock
    private OrderService orderService;

    @Mock
    private MoeGroomingAppointmentService moeGroomingAppointmentService;

    @InjectMocks
    private ReportOrderService reportOrderService;

    private static final Integer BUSINESS_ID = 12345678;
    private static final String START_DATE = "2022-07-01";
    private static final String END_DATE = "2022-07-31";

    private MoeGroomingInvoice invoice;
    private MoeGroomingServiceOperation operation;
    private ServiceChargeDTO serviceCharge;
    private CustomerGrooming customerGrooming;

    @BeforeEach
    void setUp() {
        // Setup invoice
        invoice = new MoeGroomingInvoice();
        invoice.setId(1);
        invoice.setOrderType(OrderModel.OrderType.ORIGIN.name());
        invoice.setType(InvoiceStatusEnum.TYPE_APPOINTMENT);
        invoice.setBusinessId(BUSINESS_ID);
        invoice.setGroomingId(1);
        invoice.setTotalAmount(new BigDecimal("100.00"));
        invoice.setPaidAmount(new BigDecimal("50.00"));
        invoice.setRemainAmount(new BigDecimal("50.00"));

        // Setup operation
        operation = new MoeGroomingServiceOperation();
        operation.setId(1L);
        operation.setGroomingServiceId(1);
        operation.setStaffId(1);

        // Setup service charge
        serviceCharge = new ServiceChargeDTO();
        serviceCharge.setId(1L);
        serviceCharge.setName("Test Service");
        serviceCharge.setPrice(new BigDecimal("50.00"));

        // Setup customer grooming
        customerGrooming = new CustomerGrooming();
        customerGrooming.setId(1);
        customerGrooming.setBusinessId(BUSINESS_ID);
    }

    @Test
    void queryGroomingCustomerAppointment_WhenValidInput_ShouldReturnAppointments() {
        // Arrange
        // Setup customer grooming with petServiceList
        CustomerGrooming customerGrooming = new CustomerGrooming();
        customerGrooming.setId(1);
        customerGrooming.setBusinessId(BUSINESS_ID);
        customerGrooming.setPetServiceList(new ArrayList<>()); // 初始化空列表而不是 null
        customerGrooming.setServiceTypeInclude(1); // 添加服务类型
        customerGrooming.setStatus(AppointmentStatusEnum.UNCONFIRMED.getValue().intValue()); // 设置状态
        customerGrooming.setCheckInTime(null); // 设置 check-in 时间

        when(moeGroomingAppointmentMapper.queryGroomingCustomerAppointment(anyList(), anyInt()))
                .thenReturn(List.of(customerGrooming));
        when(serviceOperationMapper.selectByGroomingIdList(anyList())).thenReturn(List.of(operation));
        when(orderService.getListByGroomingIds(anyInt(), anyList(), anyString()))
                .thenReturn(List.of(invoice));

        // Mock moeGroomingAppointmentService
        when(moeGroomingAppointmentService.getCompatibleStatus(AppointmentStatusEnum.UNCONFIRMED.getValue()))
                .thenReturn(1);
        when(moeGroomingAppointmentService.getCompatibleAppointmentStatus(
                        eq(AppointmentStatusEnum.UNCONFIRMED.getValue()), any()))
                .thenReturn(AppointmentStatusEnum.UNCONFIRMED);

        // Act
        List<CustomerGrooming> result = reportOrderService.queryGroomingCustomerAppointment(BUSINESS_ID, List.of(1), 1);

        // Assert
        assertThat(result).isNotNull().hasSize(1).first().satisfies(grooming -> {
            assertThat(grooming.getBusinessId()).isEqualTo(BUSINESS_ID);
            assertThat(grooming.getAppointmentStatus()).isEqualTo(AppointmentStatusEnum.UNCONFIRMED);
        });
    }

    @Test
    void queryUnpaidApptsWithAmount_WhenValidInput_ShouldReturnAppointments() {
        // Arrange
        ReportAppointmentDAO appointment = new ReportAppointmentDAO();
        appointment.setId(1);

        // 创建可变列表
        List<ReportAppointmentDAO> appointments = new ArrayList<>();
        appointments.add(appointment);

        // Mock invoice with remain amount
        MoeGroomingInvoice unpaidInvoice = new MoeGroomingInvoice();
        unpaidInvoice.setId(1);
        unpaidInvoice.setType(InvoiceStatusEnum.TYPE_APPOINTMENT);
        unpaidInvoice.setOrderType(OrderModel.OrderType.ORIGIN.name());
        unpaidInvoice.setGroomingId(1); // 必须和 appointment.getId() 匹配
        unpaidInvoice.setRemainAmount(new BigDecimal("50.00")); // 确保有未支付金额
        unpaidInvoice.setTotalAmount(new BigDecimal("100.00")); // 添加总金额
        unpaidInvoice.setPaidAmount(new BigDecimal("50.00")); // 添加已支付金额
        unpaidInvoice.setBusinessId(BUSINESS_ID); // 添加业务ID

        when(appointmentMapper.queryUnpaidApptsWithAmount(eq(BUSINESS_ID), eq(START_DATE), eq(END_DATE)))
                .thenReturn(appointments);

        when(orderService.getListByGroomingIds(eq(BUSINESS_ID), anyList(), any()))
                .thenReturn(List.of(unpaidInvoice));

        // Act
        List<ReportAppointmentDAO> result =
                reportOrderService.queryUnpaidApptsWithAmount(BUSINESS_ID, START_DATE, END_DATE);

        // Assert
        assertThat(result).isNotNull().hasSize(1).first().satisfies(appt -> assertThat(appt.getRemainAmount())
                .isEqualTo(new BigDecimal("50.00")));
    }

    @Test
    void getServiceChargeName_WhenValidInput_ShouldReturnServiceNames() {
        // Arrange
        MoeGroomingInvoiceItem item = new MoeGroomingInvoiceItem();
        item.setServiceId(1);
        item.setServiceName("Test Service");

        Map<Long, ServiceChargeDTO> serviceChargeMap = Map.of(1L, serviceCharge);

        // Act
        List<String> result = reportOrderService.getServiceChargeName(List.of(item), serviceChargeMap);

        // Assert
        assertThat(result).isNotNull().hasSize(1).containsExactly("Test Service");
    }

    @Test
    void queryUnpaidApptsCountWithAmount_ShouldReturnCorrectCount() {
        // Arrange
        ReportAppointmentDAO appointment = new ReportAppointmentDAO();
        appointment.setId(1);
        List<ReportAppointmentDAO> appointments = new ArrayList<>();
        appointments.add(appointment);

        when(appointmentMapper.queryUnpaidApptsWithAmount(eq(BUSINESS_ID), eq(START_DATE), eq(END_DATE)))
                .thenReturn(appointments);

        when(orderService.getListByGroomingIds(eq(BUSINESS_ID), anyList(), any()))
                .thenReturn(List.of(invoice));

        // Act
        Integer result = reportOrderService.queryUnpaidApptsCountWithAmount(BUSINESS_ID, START_DATE, END_DATE);

        // Assert
        assertThat(result).isEqualTo(1);
    }
}
