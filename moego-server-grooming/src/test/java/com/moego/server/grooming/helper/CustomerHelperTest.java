package com.moego.server.grooming.helper;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

import com.moego.idl.models.business_customer.v1.BusinessCustomerInfoModel;
import com.moego.idl.models.business_customer.v1.BusinessCustomerNoteModel;
import com.moego.idl.models.business_customer.v1.BusinessPetNoteModel;
import com.moego.idl.service.business_customer.v1.BatchGetCustomerInfoRequest;
import com.moego.idl.service.business_customer.v1.BatchGetCustomerInfoResponse;
import com.moego.idl.service.business_customer.v1.BatchListCustomerNoteRequest;
import com.moego.idl.service.business_customer.v1.BatchListCustomerNoteResponse;
import com.moego.idl.service.business_customer.v1.BatchListPetNoteRequest;
import com.moego.idl.service.business_customer.v1.BatchListPetNoteResponse;
import com.moego.idl.service.business_customer.v1.BusinessCustomerNoteServiceGrpc;
import com.moego.idl.service.business_customer.v1.BusinessCustomerServiceGrpc;
import com.moego.idl.service.business_customer.v1.BusinessCustomerTagServiceGrpc;
import com.moego.idl.service.business_customer.v1.BusinessPetNoteServiceGrpc;
import com.moego.server.customer.api.IPetBelongingService;
import com.moego.server.customer.client.IPetClient;
import java.util.List;
import java.util.Map;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class CustomerHelperTest {

    @Mock
    private BusinessCustomerServiceGrpc.BusinessCustomerServiceBlockingStub businessCustomerClient;

    @Mock
    private BusinessCustomerNoteServiceGrpc.BusinessCustomerNoteServiceBlockingStub customerNoteClient;

    @Mock
    private BusinessCustomerTagServiceGrpc.BusinessCustomerTagServiceBlockingStub customerTagClient;

    @Mock
    private BusinessPetNoteServiceGrpc.BusinessPetNoteServiceBlockingStub petNoteClient;

    @Mock
    private IPetBelongingService petBelongingClient;

    @Mock
    private IPetClient iPetClient;

    @InjectMocks
    private CustomerHelper customerHelper;

    @Test
    void getCustomerInfos_withValidCustomerIds_returnsExpectedResult() {
        List<Long> customerIds = List.of(1L, 2L, 3L);
        var request =
                BatchGetCustomerInfoRequest.newBuilder().addAllIds(customerIds).build();
        when(businessCustomerClient.batchGetCustomerInfo(request))
                .thenReturn(BatchGetCustomerInfoResponse.newBuilder()
                        .addAllCustomers(List.of(
                                BusinessCustomerInfoModel.newBuilder().setId(1L).build(),
                                BusinessCustomerInfoModel.newBuilder().setId(2L).build(),
                                BusinessCustomerInfoModel.newBuilder().setId(3L).build()))
                        .build());

        var result = customerHelper.getCustomerInfos(customerIds);

        var expect = Map.of(
                1L,
                BusinessCustomerInfoModel.newBuilder().setId(1L).build(),
                2L,
                BusinessCustomerInfoModel.newBuilder().setId(2L).build(),
                3L,
                BusinessCustomerInfoModel.newBuilder().setId(3L).build());

        assertThat(result).isEqualTo(expect);
    }

    @Test
    void getCustomerInfos_withEmptyCustomerIds_returnsEmptyResult() {
        List<Long> customerIds = List.of();

        Map<Long, BusinessCustomerInfoModel> result = customerHelper.getCustomerInfos(customerIds);
        assertThat(result).isEmpty();
    }

    @Test
    void getPetNoteDTOs_withValidPetIds_returnsExpectedResult() {
        List<Long> petIds = List.of(1L, 2L, 3L);
        var request = BatchListPetNoteRequest.newBuilder().addAllPetIds(petIds).build();
        when(petNoteClient.batchListPetNote(request))
                .thenReturn(BatchListPetNoteResponse.newBuilder()
                        .putPetNotesMap(
                                1L,
                                BatchListPetNoteResponse.PetNotes.newBuilder().build())
                        .putPetNotesMap(
                                2L,
                                BatchListPetNoteResponse.PetNotes.newBuilder().build())
                        .putPetNotesMap(
                                3L,
                                BatchListPetNoteResponse.PetNotes.newBuilder().build())
                        .build());

        var result = customerHelper.getPetNoteDTOs(petIds);
        var expect = Map.of(1L, List.of(), 2L, List.of(), 3L, List.of());
        assertThat(result).isEqualTo(expect);
    }

    @Test
    void getPetNoteDTOs_withEmptyPetIds_returnsEmptyResult() {
        List<Long> petIds = List.of();

        Map<Long, List<BusinessPetNoteModel>> result = customerHelper.getPetNoteDTOs(petIds);
        assertThat(result).isEmpty();
    }

    @Test
    void getCustomerNoteDTOs_withValidCustomerIds_returnsExpectedResult() {
        List<Long> customerIds = List.of(1L, 2L, 3L);
        var request = BatchListCustomerNoteRequest.newBuilder()
                .addAllCustomerIds(customerIds)
                .build();
        when(customerNoteClient.batchListCustomerNote(request))
                .thenReturn(BatchListCustomerNoteResponse.newBuilder()
                        .putCustomerNotesMap(
                                1L,
                                BatchListCustomerNoteResponse.CustomerNotes.newBuilder()
                                        .build())
                        .putCustomerNotesMap(
                                2L,
                                BatchListCustomerNoteResponse.CustomerNotes.newBuilder()
                                        .build())
                        .putCustomerNotesMap(
                                3L,
                                BatchListCustomerNoteResponse.CustomerNotes.newBuilder()
                                        .build())
                        .build());

        Map<Long, List<BusinessCustomerNoteModel>> result = customerHelper.getCustomerNoteDTOs(customerIds);
        var expect = Map.of(1L, List.of(), 2L, List.of(), 3L, List.of());
        assertThat(result).isEqualTo(expect);
    }

    @Test
    void getCustomerNoteDTOs_withEmptyCustomerIds_returnsEmptyResult() {
        List<Long> customerIds = List.of();

        Map<Long, List<BusinessCustomerNoteModel>> result = customerHelper.getCustomerNoteDTOs(customerIds);
        assertThat(result).isEmpty();
    }
}
