package com.moego.server.retail.service.params;

import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;

@Data
public class CartAddDiscountParams {

    @NotNull
    private Integer cartId;

    private String discountType;

    private BigDecimal discountAmount;

    private BigDecimal discountRate;

    private Long discountCodeId;

    private List<Long> itemIds;
}
