package com.moego.server.retail.mapperbean;

import java.math.BigDecimal;

public class Product {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column product.id
     *
     * @mbg.generated
     */
    private Integer id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column product.business_id
     *
     * @mbg.generated
     */
    private Integer businessId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column product.name
     *
     * @mbg.generated
     */
    private String name;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column product.image_url
     *
     * @mbg.generated
     */
    private String imageUrl;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column product.description
     *
     * @mbg.generated
     */
    private String description;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column product.sku
     *
     * @mbg.generated
     */
    private String sku;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column product.supplier_id
     *
     * @mbg.generated
     */
    private Integer supplierId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column product.category_id
     *
     * @mbg.generated
     */
    private Integer categoryId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column product.supply_price
     *
     * @mbg.generated
     */
    private BigDecimal supplyPrice;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column product.tax_rate
     *
     * @mbg.generated
     */
    private BigDecimal taxRate;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column product.retail_price
     *
     * @mbg.generated
     */
    private BigDecimal retailPrice;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column product.special_price
     *
     * @mbg.generated
     */
    private BigDecimal specialPrice;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column product.enable_staff_commission
     *
     * @mbg.generated
     */
    private Boolean enableStaffCommission;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column product.create_time
     *
     * @mbg.generated
     */
    private Long createTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column product.update_time
     *
     * @mbg.generated
     */
    private Long updateTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column product.tax_id
     *
     * @mbg.generated
     */
    private Integer taxId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column product.stock
     *
     * @mbg.generated
     */
    private Integer stock;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column product.deleted
     *
     * @mbg.generated
     */
    private Boolean deleted;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column product.barcode
     *
     * @mbg.generated
     */
    private String barcode;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column product.company_id
     *
     * @mbg.generated
     */
    private Long companyId;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column product.id
     *
     * @return the value of product.id
     *
     * @mbg.generated
     */
    public Integer getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column product.id
     *
     * @param id the value for product.id
     *
     * @mbg.generated
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column product.business_id
     *
     * @return the value of product.business_id
     *
     * @mbg.generated
     */
    public Integer getBusinessId() {
        return businessId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column product.business_id
     *
     * @param businessId the value for product.business_id
     *
     * @mbg.generated
     */
    public void setBusinessId(Integer businessId) {
        this.businessId = businessId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column product.name
     *
     * @return the value of product.name
     *
     * @mbg.generated
     */
    public String getName() {
        return name;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column product.name
     *
     * @param name the value for product.name
     *
     * @mbg.generated
     */
    public void setName(String name) {
        this.name = name == null ? null : name.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column product.image_url
     *
     * @return the value of product.image_url
     *
     * @mbg.generated
     */
    public String getImageUrl() {
        return imageUrl;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column product.image_url
     *
     * @param imageUrl the value for product.image_url
     *
     * @mbg.generated
     */
    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl == null ? null : imageUrl.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column product.description
     *
     * @return the value of product.description
     *
     * @mbg.generated
     */
    public String getDescription() {
        return description;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column product.description
     *
     * @param description the value for product.description
     *
     * @mbg.generated
     */
    public void setDescription(String description) {
        this.description = description == null ? null : description.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column product.sku
     *
     * @return the value of product.sku
     *
     * @mbg.generated
     */
    public String getSku() {
        return sku;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column product.sku
     *
     * @param sku the value for product.sku
     *
     * @mbg.generated
     */
    public void setSku(String sku) {
        this.sku = sku == null ? null : sku.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column product.supplier_id
     *
     * @return the value of product.supplier_id
     *
     * @mbg.generated
     */
    public Integer getSupplierId() {
        return supplierId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column product.supplier_id
     *
     * @param supplierId the value for product.supplier_id
     *
     * @mbg.generated
     */
    public void setSupplierId(Integer supplierId) {
        this.supplierId = supplierId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column product.category_id
     *
     * @return the value of product.category_id
     *
     * @mbg.generated
     */
    public Integer getCategoryId() {
        return categoryId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column product.category_id
     *
     * @param categoryId the value for product.category_id
     *
     * @mbg.generated
     */
    public void setCategoryId(Integer categoryId) {
        this.categoryId = categoryId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column product.supply_price
     *
     * @return the value of product.supply_price
     *
     * @mbg.generated
     */
    public BigDecimal getSupplyPrice() {
        return supplyPrice;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column product.supply_price
     *
     * @param supplyPrice the value for product.supply_price
     *
     * @mbg.generated
     */
    public void setSupplyPrice(BigDecimal supplyPrice) {
        this.supplyPrice = supplyPrice;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column product.tax_rate
     *
     * @return the value of product.tax_rate
     *
     * @mbg.generated
     */
    public BigDecimal getTaxRate() {
        return taxRate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column product.tax_rate
     *
     * @param taxRate the value for product.tax_rate
     *
     * @mbg.generated
     */
    public void setTaxRate(BigDecimal taxRate) {
        this.taxRate = taxRate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column product.retail_price
     *
     * @return the value of product.retail_price
     *
     * @mbg.generated
     */
    public BigDecimal getRetailPrice() {
        return retailPrice;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column product.retail_price
     *
     * @param retailPrice the value for product.retail_price
     *
     * @mbg.generated
     */
    public void setRetailPrice(BigDecimal retailPrice) {
        this.retailPrice = retailPrice;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column product.special_price
     *
     * @return the value of product.special_price
     *
     * @mbg.generated
     */
    public BigDecimal getSpecialPrice() {
        return specialPrice;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column product.special_price
     *
     * @param specialPrice the value for product.special_price
     *
     * @mbg.generated
     */
    public void setSpecialPrice(BigDecimal specialPrice) {
        this.specialPrice = specialPrice;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column product.enable_staff_commission
     *
     * @return the value of product.enable_staff_commission
     *
     * @mbg.generated
     */
    public Boolean getEnableStaffCommission() {
        return enableStaffCommission;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column product.enable_staff_commission
     *
     * @param enableStaffCommission the value for product.enable_staff_commission
     *
     * @mbg.generated
     */
    public void setEnableStaffCommission(Boolean enableStaffCommission) {
        this.enableStaffCommission = enableStaffCommission;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column product.create_time
     *
     * @return the value of product.create_time
     *
     * @mbg.generated
     */
    public Long getCreateTime() {
        return createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column product.create_time
     *
     * @param createTime the value for product.create_time
     *
     * @mbg.generated
     */
    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column product.update_time
     *
     * @return the value of product.update_time
     *
     * @mbg.generated
     */
    public Long getUpdateTime() {
        return updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column product.update_time
     *
     * @param updateTime the value for product.update_time
     *
     * @mbg.generated
     */
    public void setUpdateTime(Long updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column product.tax_id
     *
     * @return the value of product.tax_id
     *
     * @mbg.generated
     */
    public Integer getTaxId() {
        return taxId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column product.tax_id
     *
     * @param taxId the value for product.tax_id
     *
     * @mbg.generated
     */
    public void setTaxId(Integer taxId) {
        this.taxId = taxId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column product.stock
     *
     * @return the value of product.stock
     *
     * @mbg.generated
     */
    public Integer getStock() {
        return stock;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column product.stock
     *
     * @param stock the value for product.stock
     *
     * @mbg.generated
     */
    public void setStock(Integer stock) {
        this.stock = stock;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column product.deleted
     *
     * @return the value of product.deleted
     *
     * @mbg.generated
     */
    public Boolean getDeleted() {
        return deleted;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column product.deleted
     *
     * @param deleted the value for product.deleted
     *
     * @mbg.generated
     */
    public void setDeleted(Boolean deleted) {
        this.deleted = deleted;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column product.barcode
     *
     * @return the value of product.barcode
     *
     * @mbg.generated
     */
    public String getBarcode() {
        return barcode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column product.barcode
     *
     * @param barcode the value for product.barcode
     *
     * @mbg.generated
     */
    public void setBarcode(String barcode) {
        this.barcode = barcode == null ? null : barcode.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column product.company_id
     *
     * @return the value of product.company_id
     *
     * @mbg.generated
     */
    public Long getCompanyId() {
        return companyId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column product.company_id
     *
     * @param companyId the value for product.company_id
     *
     * @mbg.generated
     */
    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    public boolean hasValue() {
        if (businessId != null && businessId != 0) {
            return true;
        }

        if (supplierId != null && supplierId != 0) {
            return true;
        }

        if (categoryId != null && categoryId != 0) {
            return true;
        }

        if (name != null) {
            return true;
        }

        if (imageUrl != null) {
            return true;
        }

        if (description != null) {
            return true;
        }

        if (sku != null) {
            return true;
        }

        if (stock != null) {
            return true;
        }

        if (supplyPrice != null) {
            return true;
        }

        if (retailPrice != null) {
            return true;
        }

        if (specialPrice != null) {
            return true;
        }

        if (enableStaffCommission != null) {
            return true;
        }

        if (taxRate != null) {
            return true;
        }

        if (barcode != null) {
            return true;
        }

        return false;
    }
}
