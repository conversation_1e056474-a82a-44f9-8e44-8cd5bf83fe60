package com.moego.server.retail.mapper;

import com.moego.server.retail.mapperbean.StockEvent;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface StockEventMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table stock_event
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table stock_event
     *
     * @mbg.generated
     */
    int insert(StockEvent record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table stock_event
     *
     * @mbg.generated
     */
    int insertSelective(StockEvent record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table stock_event
     *
     * @mbg.generated
     */
    StockEvent selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table stock_event
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(StockEvent record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table stock_event
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(StockEvent record);

    List<StockEvent> selectListByProductId(@Param("productId") Integer productId);

    StockEvent selectLatestByProductId(@Param("productId") Integer productId);

    List<StockEvent> selectLatestList(@Param("productIds") List<Integer> productIds);
}
