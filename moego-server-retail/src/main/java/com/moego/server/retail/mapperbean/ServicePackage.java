package com.moego.server.retail.mapperbean;

import com.moego.server.retail.dto.PackageInfoDto.Service;
import java.math.BigDecimal;
import java.util.List;

public class ServicePackage {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column service_package.id
     *
     * @mbg.generated
     */
    private Integer id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column service_package.package_id
     *
     * @mbg.generated
     */
    private Integer packageId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column service_package.service_id
     *
     * @mbg.generated
     */
    private Integer serviceId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column service_package.unit_price
     *
     * @mbg.generated
     */
    private BigDecimal unitPrice;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column service_package.quantity
     *
     * @mbg.generated
     */
    private Integer quantity;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column service_package.create_time
     *
     * @mbg.generated
     */
    private Long createTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column service_package.update_time
     *
     * @mbg.generated
     */
    private Long updateTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column service_package.services
     *
     * @mbg.generated
     */
    private List<Service> services;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column service_package.id
     *
     * @return the value of service_package.id
     *
     * @mbg.generated
     */
    public Integer getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column service_package.id
     *
     * @param id the value for service_package.id
     *
     * @mbg.generated
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column service_package.package_id
     *
     * @return the value of service_package.package_id
     *
     * @mbg.generated
     */
    public Integer getPackageId() {
        return packageId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column service_package.package_id
     *
     * @param packageId the value for service_package.package_id
     *
     * @mbg.generated
     */
    public void setPackageId(Integer packageId) {
        this.packageId = packageId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column service_package.service_id
     *
     * @return the value of service_package.service_id
     *
     * @mbg.generated
     */
    public Integer getServiceId() {
        return serviceId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column service_package.service_id
     *
     * @param serviceId the value for service_package.service_id
     *
     * @mbg.generated
     */
    public void setServiceId(Integer serviceId) {
        this.serviceId = serviceId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column service_package.unit_price
     *
     * @return the value of service_package.unit_price
     *
     * @mbg.generated
     */
    public BigDecimal getUnitPrice() {
        return unitPrice;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column service_package.unit_price
     *
     * @param unitPrice the value for service_package.unit_price
     *
     * @mbg.generated
     */
    public void setUnitPrice(BigDecimal unitPrice) {
        this.unitPrice = unitPrice;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column service_package.quantity
     *
     * @return the value of service_package.quantity
     *
     * @mbg.generated
     */
    public Integer getQuantity() {
        return quantity;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column service_package.quantity
     *
     * @param quantity the value for service_package.quantity
     *
     * @mbg.generated
     */
    public void setQuantity(Integer quantity) {
        this.quantity = quantity;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column service_package.create_time
     *
     * @return the value of service_package.create_time
     *
     * @mbg.generated
     */
    public Long getCreateTime() {
        return createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column service_package.create_time
     *
     * @param createTime the value for service_package.create_time
     *
     * @mbg.generated
     */
    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column service_package.update_time
     *
     * @return the value of service_package.update_time
     *
     * @mbg.generated
     */
    public Long getUpdateTime() {
        return updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column service_package.update_time
     *
     * @param updateTime the value for service_package.update_time
     *
     * @mbg.generated
     */
    public void setUpdateTime(Long updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column service_package.services
     *
     * @return the value of service_package.services
     *
     * @mbg.generated
     */
    public List<Service> getServices() {
        return services;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column service_package.services
     *
     * @param services the value for service_package.services
     *
     * @mbg.generated
     */
    public void setServices(List<Service> services) {
        this.services = services;
    }
}
