package com.moego.server.retail.eventbus;

import com.moego.idl.models.order.v1.OrderEventModel;
import com.moego.idl.models.order.v1.OrderSourceType;
import com.moego.lib.event_bus.consumer.AbstractConsumer;
import com.moego.lib.event_bus.event.EventRecord;
import com.moego.server.retail.mapperbean.Invoice;
import com.moego.server.retail.service.InvoiceService;
import com.moego.server.retail.service.OrderService;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
public class OrderConsumer extends AbstractConsumer<OrderEventModel> {

    private static final String TOPIC_NAME = "moego.svc.order.completed";
    private final OrderService orderService;
    private final InvoiceService invoiceService;

    @Override
    public String topicName() {
        return TOPIC_NAME;
    }

    @Override
    public void consume(EventRecord<OrderEventModel> event) {
        log.info("Received order event: {}", event.id());
        final var order = event.detail().getOrder();
        if (!Objects.equals(order.getSourceType(), OrderSourceType.PACKAGE)) {
            return;
        }

        Invoice invoice = orderService.getOrderInvoiceWithItems(
                Math.toIntExact(event.detail().getOrder().getId()));
        invoiceService.processRetailInvoiceAfterComplete(
                event.detail().getOrder().getCompanyId(), invoice);
    }
}
