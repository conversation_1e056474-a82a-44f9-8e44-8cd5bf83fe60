package com.moego.server.business.service;

import static com.moego.common.utils.PageUtil.hasEmptyCollectionFilter;
import static com.moego.common.utils.PageUtil.selectPage;
import static com.moego.server.business.common.consts.DataSourceConst.READER;

import com.moego.common.dto.CompanyFunctionControlDto;
import com.moego.common.enums.CompanyFunctionControlConst;
import com.moego.common.enums.ResponseCodeEnum;
import com.moego.common.exception.CommonException;
import com.moego.common.utils.AccountUtil;
import com.moego.common.utils.DateUtil;
import com.moego.common.utils.Pagination;
import com.moego.common.utils.PrimitiveTypeUtil;
import com.moego.idl.models.organization.v1.CompanyPreferenceSettingModel;
import com.moego.idl.models.organization.v1.StaffModel;
import com.moego.idl.service.enterprise.v1.EnterpriseServiceGrpc;
import com.moego.idl.service.enterprise.v1.GetEnterpriseRequest;
import com.moego.idl.service.metadata.v1.GetKeyRequest;
import com.moego.idl.service.metadata.v1.GetValueRequest;
import com.moego.idl.service.metadata.v1.MetadataServiceGrpc;
import com.moego.idl.service.organization.v1.BusinessServiceGrpc;
import com.moego.idl.service.organization.v1.CompanyServiceGrpc;
import com.moego.idl.service.organization.v1.GetCompanyPreferenceSettingRequest;
import com.moego.idl.service.organization.v1.GetStaffsByAccountIdRequest;
import com.moego.idl.service.organization.v1.GetWorkingLocationListByCompaniesStaffRequest;
import com.moego.idl.service.organization.v1.QueryCompaniesByIdsRequest;
import com.moego.idl.service.organization.v1.StaffServiceGrpc;
import com.moego.server.business.converter.StaffMapConverter;
import com.moego.server.business.dto.BusinessIdWithLevelDto;
import com.moego.server.business.dto.MoeStaffDto;
import com.moego.server.business.dto.StaffCompanyDto;
import com.moego.server.business.mapper.MoeBusinessMapper;
import com.moego.server.business.mapper.MoeCompanyMapper;
import com.moego.server.business.mapper.MoeStaffMapper;
import com.moego.server.business.mapper.MoeVanMapper;
import com.moego.server.business.mapperbean.MoeBusiness;
import com.moego.server.business.mapperbean.MoeCompany;
import com.moego.server.business.mapperbean.MoeStaff;
import com.moego.server.business.params.DescribeCompaniesParams;
import com.moego.server.business.service.bo.CompanyWithBusinessesBO;
import com.moego.server.business.service.dto.BusinessCountDto;
import com.moego.server.business.web.vo.BusinessExtraInfo;
import com.moego.server.message.api.INotificationService;
import com.moego.server.payment.api.IPaymentStripeService;
import com.moego.server.payment.params.CheckMoeGoPayStatusRequest;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR>
 * @since 2021/3/11 7:58 PM
 */
@Service
@Slf4j
public class CompanyService {

    @Autowired
    private MoeCompanyMapper moeCompanyMapper;

    @Autowired
    private MoeBusinessMapper moeBusinessMapper;

    @Autowired
    private MoeVanMapper vanMapper;

    @Autowired
    private MoeStaffMapper staffMapper;

    @Autowired
    private RelevantAccountService relevantAccountService;

    @Autowired
    private INotificationService notificationService;

    @Autowired
    private IPaymentStripeService paymentStripeService;

    @Autowired
    private MetadataServiceGrpc.MetadataServiceBlockingStub metadataServiceBlockingStub;

    @Autowired
    private EnterpriseServiceGrpc.EnterpriseServiceBlockingStub enterpriseServiceBlockingStub;

    @Autowired
    private StaffServiceGrpc.StaffServiceBlockingStub staffServiceBlockingStub;

    @Autowired
    private BusinessServiceGrpc.BusinessServiceBlockingStub businessServiceBlockingStub;

    @Autowired
    private CompanyServiceGrpc.CompanyServiceBlockingStub companyServiceBlockingStub;

    public int createCompany(MoeCompany company) {
        log.info("create new company for {}", company.getAccountId());
        company.setCreateTime(DateUtil.get10Timestamp());
        company.setUpdateTime(company.getCreateTime());
        return moeCompanyMapper.insertSelective(company);
    }

    /**
     * @param companyId
     * @return
     * @deprecated please use {@link #getAccountCompany} instead.
     */
    @Deprecated
    public MoeCompany getCompanyById(Integer companyId) {
        return moeCompanyMapper.useDataSource(READER).selectByPrimaryKey(companyId);
    }

    public MoeCompany getCompanyByIdFromMaster(Integer companyId) {
        return moeCompanyMapper.selectByPrimaryKey(companyId);
    }

    /**
     * ownerId 必须是companyId的owner 或是 company 的 enterprise master account
     * @param ownerId
     * @param companyId
     * @return
     */
    public MoeCompany getAccountCompany(Integer ownerId, Integer companyId) {
        MoeCompany company = moeCompanyMapper.selectByPrimaryKey(companyId);
        if (company == null) {
            return null;
        }
        // is company owner,return company
        if (company.getAccountId().equals(ownerId)) {
            return company;
        }
        // company not belong to any enterprise
        if (company.getEnterpriseId() == 0) {
            return null;
        }
        var enterpriseRes = enterpriseServiceBlockingStub.getEnterprise(GetEnterpriseRequest.newBuilder()
                .setId(company.getEnterpriseId().longValue())
                .build());
        var enterprise = enterpriseRes.getEnterprise();
        if (!Objects.equals(enterprise.getAccountId(), ownerId.longValue())) {
            // company not belong to any enterprise or ownerId not own this enterprise
            return null;
        }
        return company;
    }

    public MoeCompany getCompanyByBusinessId(Integer businessId) {
        return moeCompanyMapper.useDataSource(READER).selectByBusinessId(businessId);
    }

    public CompanyFunctionControlDto queryCompanyPermissionByCompanyId(Integer companyId) {
        MoeCompany company = getCompanyById(companyId);
        if (Objects.isNull(company)) {
            throw new CommonException(ResponseCodeEnum.COMPANY_NOT_FOUND);
        }
        return AccountUtil.getCompanyControlDto(
                company.getLevel(), company.getIsNewPricing(), company.getEnableStripeReader());
    }

    public CompanyFunctionControlDto queryCompanyPermissionByBusinessId(Integer businessId) {
        MoeCompany company = getCompanyByBusinessId(businessId);
        if (Objects.isNull(company)) {
            throw new CommonException(ResponseCodeEnum.COMPANY_NOT_FOUND);
        }
        return AccountUtil.getCompanyControlDto(
                company.getLevel(), company.getIsNewPricing(), company.getEnableStripeReader());
    }

    /**
     * 判断当前的订阅计划是 Free 或者 Starter
     *
     * @param businessId
     * @return
     */
    public boolean isFreeOrStarterPlan(Integer businessId) {
        CompanyFunctionControlDto controlDto = queryCompanyPermissionByBusinessId(businessId);
        return (Objects.nonNull(controlDto)
                && (Objects.equals(controlDto.getPremiumType(), CompanyFunctionControlConst.PREMIUM_TYPE_FREE)
                        || Objects.equals(controlDto.getPremiumType(), CompanyFunctionControlConst.PREMIUM_TYPE_39)));
    }

    public MoeCompany getCompanyByStaffId(Integer staffId) {
        return moeCompanyMapper.selectByStaffId(staffId);
    }

    public Map<Integer, BusinessExtraInfo> getExtraInfoForOtherBusiness(Long accountId) {
        List<MoeStaff> staffList = staffMapper.getStaffByBusinessOrderForAccount(accountId.intValue());
        if (CollectionUtils.isEmpty(staffList)) {
            // AS 迁移前才会调这个接口，没有迁移前的 staff 直接返回空
            return Collections.emptyMap();
        }
        List<MoeStaffDto> staffDtoList = new ArrayList<>();
        staffList.forEach(staff -> {
            MoeStaffDto staffDto = new MoeStaffDto();
            BeanUtils.copyProperties(staff, staffDto);
            staffDtoList.add(staffDto);
        });
        Map<Integer, Integer> unreadCountByBusinessId = notificationService.getStaffUnreadCount(staffDtoList);
        return unreadCountByBusinessId.entrySet().stream()
                .collect(Collectors.toMap(Map.Entry::getKey, entry -> new BusinessExtraInfo(entry.getValue())));
    }

    /**
     * 获取该账号拥有的公司
     *
     * @param accountId
     * @return
     */
    public List<MoeCompany> getAccountCompanyList(Integer accountId) {
        return moeCompanyMapper.selectByAccountId(accountId);
    }

    public Map<Integer, BusinessIdWithLevelDto> queryBusinessIdsByCompanyId(List<Integer> companyIds) {
        if (CollectionUtils.isEmpty(companyIds)) {
            return Collections.emptyMap();
        }
        List<MoeCompany> companyList = moeCompanyMapper.getCompanyByIdList(companyIds);
        if (CollectionUtils.isEmpty(companyList)) {
            return Collections.emptyMap();
        }
        List<MoeBusiness> businessList = moeBusinessMapper.selectAllBusinessByCompanyId(companyIds);
        Map<Integer, MoeCompany> companyMap =
                companyList.stream().collect(Collectors.toMap(MoeCompany::getId, moeCompany -> moeCompany));
        Map<Integer, List<MoeBusiness>> businessListMap =
                businessList.stream().collect(Collectors.groupingBy(MoeBusiness::getCompanyId));
        Map<Integer, BusinessIdWithLevelDto> companyBidMap = new HashMap<>();
        for (var entry : companyMap.entrySet()) {
            Integer companyId = entry.getKey();
            MoeCompany company = companyMap.get(companyId);
            BusinessIdWithLevelDto levelDto = new BusinessIdWithLevelDto();
            levelDto.setCompanyId(companyId);
            levelDto.setLevel(company.getLevel());
            List<MoeBusiness> businesses = businessListMap.get(companyId);
            levelDto.setBusinessIds(businesses.stream().map(MoeBusiness::getId).collect(Collectors.toList()));
            companyBidMap.put(companyId, levelDto);
        }
        return companyBidMap;
    }

    public List<StaffCompanyDto> getCompaniesForStaff(Integer accountId) {
        List<StaffCompanyDto> resultList = new ArrayList<>();
        // AS 迁移后的company的staff
        resultList.addAll(getMigratedStaffCompanyDto(accountId));
        // AS 未迁移的company的staff
        var businessStaffResultList = moeCompanyMapper.selectCompaniesByAccountId(accountId);

        // 聚合list,并去重,优先级:enterprise staff > 迁移后的company的staff > 未迁移的company的staff
        var bidSet = resultList.stream().map(StaffCompanyDto::getBusinessId).collect(Collectors.toSet());
        for (StaffCompanyDto result : businessStaffResultList) {
            if (!bidSet.contains(result.getBusinessId())) {
                bidSet.add(result.getBusinessId());
                resultList.add(result);
            }
        }

        if (!CollectionUtils.isEmpty(resultList)) {
            // 待查询的所有businessId
            List<Integer> bidList = bidSet.stream().toList();
            List<BusinessCountDto> vanUsedList = vanMapper.selectUsedVanByBidList(bidList);
            List<BusinessCountDto> staffUsedList = staffMapper.selectUsedStaffByBidList(bidList);
            var bIdToVanUsed = vanUsedList.stream()
                    .collect(Collectors.toMap(BusinessCountDto::getBusinessId, Function.identity()));
            var bIdToStaffUsedLis = staffUsedList.stream()
                    .collect(Collectors.toMap(BusinessCountDto::getBusinessId, Function.identity()));
            for (StaffCompanyDto staffDto : resultList) {
                if (bIdToVanUsed.containsKey(staffDto.getBusinessId())) {
                    staffDto.setUsedVanCount(
                            bIdToVanUsed.get(staffDto.getBusinessId()).getCount());
                }
                if (staffDto.getUsedVanCount() == null) {
                    staffDto.setUsedVanCount(0);
                }
                if (bIdToStaffUsedLis.containsKey(staffDto.getBusinessId())) {
                    staffDto.setUsedStaffCount(
                            bIdToStaffUsedLis.get(staffDto.getBusinessId()).getCount());
                }
                if (staffDto.getUsedStaffCount() == null) {
                    staffDto.setUsedStaffCount(0);
                }
            }
        }
        return resultList;
    }

    public List<Integer> getTargetLevelBusinessIds(
            List<Integer> filterCompanyIds, Integer startLevel, Integer endLevel) {
        return moeCompanyMapper.selectTargetLevelBusinessIds(filterCompanyIds, startLevel, endLevel);
    }

    public List<Integer> getAllBusinessIds(Integer businessId) {
        return moeCompanyMapper.selectAllBusinessIdsInOneCompany(businessId);
    }

    public List<Integer> getAllRelevantBusinessIdsForOb(Integer businessId) {
        Set<Integer> businessIdsSet = new HashSet<>();
        businessIdsSet.addAll(getAllBusinessIds(businessId));
        businessIdsSet.addAll(relevantAccountService.getAllRelevantBusinessIdsForOb(businessId));
        return new ArrayList<>(businessIdsSet);
    }

    public int updateCompany(MoeCompany company) {
        if (company.getId() == null) {
            log.error("company id is null");
            throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "please set company id");
        }
        company.setUpdateTime(DateUtil.get10Timestamp());
        return moeCompanyMapper.updateByPrimaryKeySelective(company);
    }

    public Integer queryCompanyRemainVanNumByCompanyId(Integer companyId) {
        if (PrimitiveTypeUtil.isNullOrZeroOrNegative(companyId)) {
            return 0;
        }
        MoeCompany company = moeCompanyMapper.useDataSource(READER).selectByPrimaryKey(companyId);
        int existVanCount = vanMapper.useDataSource(READER).selectAllVanByCompanyId(companyId);
        // 只记录额外买的，mobile自带的van不需要记录
        Integer remainVanNumber = company.getVansNum();
        // 减去当前系统内有的van数量
        remainVanNumber -= existVanCount;
        return remainVanNumber;
    }

    public Pair<List<MoeCompany>, Pagination> describeCompanies(DescribeCompaniesParams params, Pagination pagination) {
        if (hasEmptyCollectionFilter(params.ids(), params.accountIds())) {
            return Pair.of(Collections.emptyList(), new Pagination(pagination.pageNum(), pagination.pageSize(), 0));
        }
        return selectPage(pagination, () -> moeCompanyMapper.describeCompanies(params));
    }

    public Integer queryNextCompanyId(Integer companyId) {
        return moeCompanyMapper.queryNextCompanyId(companyId);
    }

    public boolean isMoeGoPayEnable(int companyId) {
        List<MoeBusiness> businessList = moeBusinessMapper.getBusinessByCompanyId(companyId);
        if (CollectionUtils.isEmpty(businessList)) {
            return false;
        }
        // TODO: use company id directly after company id is ready in moego-server-payment
        var businessIds = businessList.stream().map(MoeBusiness::getId).collect(Collectors.toSet());
        var request = new CheckMoeGoPayStatusRequest(null, businessIds);
        var checkResult = paymentStripeService.checkMoeGoPayStatus(request);
        return checkResult.businessMoeGoPayStatusMap().values().stream().anyMatch(Boolean.TRUE::equals);
    }

    public List<CompanyWithBusinessesBO> getEnterpriseCompanyAndBusinessForStaff(
            Integer staffId, Integer enterpriseId) {
        // todo 新增 staff 对company权限校验
        var companies = moeCompanyMapper.selectAllCompanyInOneEnterprise(enterpriseId);
        if (companies.isEmpty()) {
            return Collections.emptyList();
        }
        List<CompanyWithBusinessesBO> result = new ArrayList<>();
        for (MoeCompany company : companies) {
            var bo = new CompanyWithBusinessesBO();
            bo.setCompany(company);
            bo.setBusinessInfos(new ArrayList<>());
            result.add(bo);
        }
        var companyIdToBO =
                result.stream().collect(Collectors.toMap(bo -> bo.getCompany().getId(), i -> i));
        List<MoeBusiness> businessList = moeBusinessMapper.selectAllBusinessByCompanyId(
                companyIdToBO.keySet().stream().toList());
        for (MoeBusiness business : businessList) {
            if (companyIdToBO.containsKey(business.getCompanyId())) {
                companyIdToBO.get(business.getCompanyId()).getBusinessInfos().add(business);
            }
        }
        return result;
    }

    public List<StaffModel> getMigrationStaffByAccountId(Integer accountId) {
        var allStaffs = staffServiceBlockingStub
                .getStaffsByAccountId(GetStaffsByAccountIdRequest.newBuilder()
                        .setAccountId(accountId)
                        .build())
                .getStaffsList();
        var migratedStaff =
                allStaffs.stream().filter(staff -> staff.getBusinessId() == 0L).toList();
        if (migratedStaff.isEmpty()) {
            return new ArrayList<>();
        }
        return migratedStaff;
    }

    public List<MoeStaff> getMigratedMoeStaff(Integer accountId) {
        var migratedStaff = getMigrationStaffByAccountId(accountId);
        if (migratedStaff.isEmpty()) {
            return new ArrayList<>();
        }
        var paramBuilder = GetWorkingLocationListByCompaniesStaffRequest.newBuilder();
        migratedStaff.forEach(
                staff -> paramBuilder.addParams(GetWorkingLocationListByCompaniesStaffRequest.Param.newBuilder()
                        .setStaffId(staff.getId())
                        .setCompanyId(staff.getCompanyId())
                        .build()));
        var cIdToWorkingLocations = businessServiceBlockingStub
                .getWorkingLocationListByCompaniesStaff(paramBuilder.build())
                .getCompanyLocationsMapMap();
        List<MoeStaff> moeStaffs = new ArrayList<>();
        migratedStaff.forEach(staff -> {
            var workingLocations = cIdToWorkingLocations.get(staff.getCompanyId());
            if (workingLocations == null || workingLocations.getLocationsList().isEmpty()) {
                return;
            }
            workingLocations.getLocationsList().forEach(location -> {
                var moeStaff = StaffMapConverter.INSTANCE.toStaff(staff);
                moeStaff.setBusinessId((int) location.getId());
                moeStaffs.add(moeStaff);
            });
        });
        return moeStaffs;
    }

    public List<StaffCompanyDto> getMigratedStaffCompanyDto(Integer accountId) {
        var migratedStaff = getMigrationStaffByAccountId(accountId);
        if (migratedStaff.isEmpty()) {
            return new ArrayList<>();
        }
        var paramBuilder = GetWorkingLocationListByCompaniesStaffRequest.newBuilder();
        migratedStaff.forEach(
                staff -> paramBuilder.addParams(GetWorkingLocationListByCompaniesStaffRequest.Param.newBuilder()
                        .setStaffId(staff.getId())
                        .setCompanyId(staff.getCompanyId())
                        .build()));
        var cIdToWorkingLocations = businessServiceBlockingStub
                .getWorkingLocationListByCompaniesStaff(paramBuilder.build())
                .getCompanyLocationsMapMap();
        var cIdToModel = companyServiceBlockingStub
                .queryCompaniesByIds(QueryCompaniesByIdsRequest.newBuilder()
                        .addAllCompanyIds(cIdToWorkingLocations.keySet())
                        .build())
                .getCompanyIdToCompanyMap();
        List<StaffCompanyDto> resultList = new ArrayList<>();
        migratedStaff.forEach(staff -> {
            var workingLocations = cIdToWorkingLocations.get(staff.getCompanyId());
            var company = cIdToModel.get(staff.getCompanyId());
            if (workingLocations == null || workingLocations.getLocationsList().isEmpty() || company == null) {
                return;
            }
            workingLocations.getLocationsList().forEach(location -> {
                var s = new StaffCompanyDto();
                s.setEmployeeCategory((byte) staff.getEmployeeCategoryValue());
                s.setBusinessName(location.getName());
                s.setAppType((byte) location.getBusinessTypeValue());
                s.setBusinessMode((byte) location.getBusinessModeValue());
                s.setCompanyId((int) company.getId());
                s.setAvatarPath(location.getAvatarPath());
                s.setBusinessId((int) location.getId());
                s.setAddress1(location.getAddress().getAddress1());
                s.setAddress2(location.getAddress().getAddress2());
                s.setAddressCity(location.getAddress().getCity());
                s.setAddressState(location.getAddress().getState());
                s.setAddressZipcode(location.getAddress().getZipcode());
                s.setAddressCountry(location.getAddress().getCountry());
                s.setCountry(company.getCountry());
                s.setCompanyName(company.getName());
                s.setOwnerAccountId((int) company.getAccountId());
                resultList.add(s);
            });
        });
        return resultList;
    }

    public boolean isInBoardingWhiteList(Long companyId) {
        try {
            String boardingWhiteListKey = "allow_boarding_and_daycare";
            var key = metadataServiceBlockingStub
                    .getKey(GetKeyRequest.newBuilder()
                            .setName(boardingWhiteListKey)
                            .build())
                    .getKey();
            return metadataServiceBlockingStub
                    .getValue(GetValueRequest.newBuilder()
                            .setKeyId(key.getId())
                            .setOwnerId(companyId)
                            .build())
                    .getValue()
                    .getValue()
                    .equals("true");
        } catch (Exception e) {
            return false;
        }
    }

    public CompanyPreferenceSettingModel getCompanyPreferenceSetting(Long companyId) {
        return companyServiceBlockingStub
                .getCompanyPreferenceSetting(GetCompanyPreferenceSettingRequest.newBuilder()
                        .setCompanyId(companyId)
                        .build())
                .getPreferenceSetting();
    }
}
