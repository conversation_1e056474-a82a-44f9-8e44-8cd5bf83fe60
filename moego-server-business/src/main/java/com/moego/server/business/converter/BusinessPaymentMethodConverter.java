package com.moego.server.business.converter;

import com.moego.server.business.mapperbean.MoeBusinessPaymentMethod;
import com.moego.server.business.service.dto.MoeBusinessPaymentMethodDTO;
import java.util.List;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface BusinessPaymentMethodConverter {

    BusinessPaymentMethodConverter INSTANCE = Mappers.getMapper(BusinessPaymentMethodConverter.class);

    MoeBusinessPaymentMethodDTO toDTO(MoeBusinessPaymentMethod entity);

    List<MoeBusinessPaymentMethodDTO> toDTOList(List<MoeBusinessPaymentMethod> entityList);
}
