package com.moego.server.business.service.dto;

import com.moego.common.dto.CurrencyDto;
import com.moego.common.dto.LabelTypeDto;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class PreferenceOptionsDto {

    private List<LabelTypeDto> calendarFormatList;
    private List<CurrencyDto> currencyList;
    private List<String> countryArray;
    private List<LabelTypeDto> dateFormatList;
    private List<LabelTypeDto> numberFormatList;
    private List<LabelTypeDto> timeFormatList;
    private List<LabelTypeDto> unitOfWeightList;
    private List<LabelTypeDto> unitOfDistanceList;
}
