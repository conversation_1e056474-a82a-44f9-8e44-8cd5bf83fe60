package com.moego.server.business.service;

import static com.moego.common.utils.PageUtil.hasEmptyCollectionFilter;
import static com.moego.common.utils.PageUtil.selectPage;
import static com.moego.server.business.common.consts.DataSourceConst.READER;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.moego.common.dto.BusinessDateTimeDTO;
import com.moego.common.dto.BusinessPreferenceDto;
import com.moego.common.enums.BooleanEnum;
import com.moego.common.enums.BusinessCalendarEnum;
import com.moego.common.enums.BusinessConst;
import com.moego.common.enums.PaymentSettingConst;
import com.moego.common.enums.ResponseCodeEnum;
import com.moego.common.enums.StaffEnum;
import com.moego.common.exception.CommonException;
import com.moego.common.utils.CommonUtil;
import com.moego.common.utils.CountryUtils;
import com.moego.common.utils.DateUtil;
import com.moego.common.utils.Pagination;
import com.moego.common.utils.TypeUtil;
import com.moego.idl.models.account.v1.AccountModel;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.organization.v1.CompanyPreferenceSettingModel;
import com.moego.idl.models.smart_scheduler.v1.UpdateSmartScheduleSettingDef;
import com.moego.idl.service.smart_scheduler.v1.GetSmartScheduleSettingRequest;
import com.moego.idl.service.smart_scheduler.v1.GetSmartScheduleSettingResponse;
import com.moego.idl.service.smart_scheduler.v1.SmartScheduleSettingServiceGrpc.SmartScheduleSettingServiceBlockingStub;
import com.moego.idl.service.smart_scheduler.v1.UpdateSmartScheduleSettingRequest;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.lib.common.migrate.MigrateHelper;
import com.moego.lib.common.migrate.MigrateInfo;
import com.moego.lib.common.thread.ThreadPool;
import com.moego.server.business.converter.BusinessConverter;
import com.moego.server.business.converter.BusinessTaxConverter;
import com.moego.server.business.dto.BatchQueryBusinessResult;
import com.moego.server.business.dto.CalendarSettingDTO;
import com.moego.server.business.dto.MoeBusinessDto;
import com.moego.server.business.dto.OBBusinessInfoDTO;
import com.moego.server.business.dto.SendDailyEmailDto;
import com.moego.server.business.dto.ServiceAreaSettingDTO;
import com.moego.server.business.dto.SmartScheduleSettingDTO;
import com.moego.server.business.dto.TaskBusinessInfoDto;
import com.moego.server.business.enums.CalendarCardAreaShowType;
import com.moego.server.business.mapper.MoeBusinessMapper;
import com.moego.server.business.mapper.MoeBusinessTaxMapper;
import com.moego.server.business.mapper.MoeCalendarMapper;
import com.moego.server.business.mapper.MoeStaffMapper;
import com.moego.server.business.mapperbean.MoeBusiness;
import com.moego.server.business.mapperbean.MoeBusinessExample;
import com.moego.server.business.mapperbean.MoeCalendar;
import com.moego.server.business.mapperbean.MoeStaff;
import com.moego.server.business.params.BatchQueryBusinessParams;
import com.moego.server.business.params.DescribeBusinessesParams;
import com.moego.server.business.params.SmartScheduleSettingParams;
import com.moego.server.business.service.dto.BusinessOptionsDto;
import com.moego.server.business.service.dto.BusinessSettingDTO;
import com.moego.server.business.service.dto.CalendarSettingUpdateDTO;
import com.moego.server.business.service.dto.PreferenceOptionsDto;
import com.moego.server.business.service.util.PreferenceHelper;
import com.moego.server.grooming.client.IGroomingOnlineBookingClient;
import com.moego.server.message.client.IMessageAutoClient;
import com.moego.server.message.dto.MoeBusinessAutoReplyDto;
import com.moego.server.payment.client.IPaymentPaymentClient;
import com.moego.server.payment.client.IPaymentSettingClient;
import com.moego.server.payment.dto.PaymentSettingDTO;
import com.moego.server.payment.params.PrimaryPayTypeParams;
import feign.FeignException;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.temporal.ChronoField;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

@Service
@Slf4j
@CacheConfig(cacheNames = {"businessInfo"})
public class BusinessService {

    @Autowired
    private AccountService accountService;

    @Autowired
    private MoeCalendarMapper calendarMapper;

    @Autowired
    private CompanyService companyService;

    @Autowired
    private IGroomingOnlineBookingClient groomingOnlineBookingClient;

    @Autowired
    private IMessageAutoClient iMessageAutoClient;

    @Autowired
    private IPaymentPaymentClient iPaymentPaymentClient;

    @Autowired
    private MoeBusinessMapper moeBusinessMapper;

    @Autowired
    private IPaymentSettingClient paymentSettingClient;

    @Autowired
    private MoeStaffMapper staffMapper;

    @Autowired
    private StaffService staffService;

    @Autowired
    private MoeBusinessTaxMapper moeBusinessTaxMapper;

    @Autowired
    private PreferenceHelper preferenceHelper;

    @Autowired
    private MigrateHelper migrateHelper;

    @Autowired
    private SmartScheduleSettingServiceBlockingStub smartScheduleSettingServiceBlockingStub;

    public MoeBusiness getBusinessInfo(Integer businessId) {
        MoeBusiness business = moeBusinessMapper.useDataSource(READER).selectByPrimaryKey(businessId);
        if (business == null) {
            throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "business id is invalid");
        }
        // query company van num
        business.setMaxVansNum(companyService.queryCompanyRemainVanNumByCompanyId(business.getCompanyId()));
        return business;
    }

    public boolean isBusinessAvailable(Integer businessId) {
        MoeBusiness business = moeBusinessMapper.selectByPrimaryKey(businessId);
        if (business == null) {
            return false;
        }
        return true;
    }

    public BusinessSettingDTO getBusinessCurrentSetting(Integer businessId) {
        BusinessSettingDTO dto = new BusinessSettingDTO();
        var entityList = moeBusinessTaxMapper.getTaxList(businessId);
        dto.setTax(BusinessTaxConverter.INSTANCE.toDTOList(entityList));
        dto.setInfo(getBusinessInfo(businessId));
        dto.setPreference(getBusinessPreference(businessId));
        return dto;
    }

    public List<MoeBusiness> getBusinessByIdList(List<Integer> idList) {
        return moeBusinessMapper.getBusinessByIdList(idList);
    }

    public BusinessPreferenceDto getOnlyBusinessPreference(MoeBusiness moeBusiness) {
        BusinessPreferenceDto businessPreferenceDto = new BusinessPreferenceDto();
        // 从business获取数据
        businessPreferenceDto.setCountry(moeBusiness.getCountry());
        businessPreferenceDto.setTimezoneName(moeBusiness.getTimezoneName());
        businessPreferenceDto.setCurrencyCode(moeBusiness.getCurrencyCode());
        businessPreferenceDto.setCurrencySymbol(moeBusiness.getCurrencySymbol());
        businessPreferenceDto.setDateFormatType(moeBusiness.getDateFormatType().intValue());
        businessPreferenceDto.setCalendarFormatType(
                moeBusiness.getCalendarFormatType().intValue());
        businessPreferenceDto.setTimeFormatType(moeBusiness.getTimeFormatType().intValue());
        businessPreferenceDto.setTimezoneSeconds(moeBusiness.getTimezoneSeconds());
        businessPreferenceDto.setUnitOfWeightType(
                moeBusiness.getUnitOfWeightType().intValue());
        businessPreferenceDto.setNumberFormatType(
                moeBusiness.getNumberFormatType().intValue());
        // 再处理数据
        businessPreferenceDto.setNeedSendCode(moeBusiness.getCountry().equals(BusinessConst.COUNTRY_US)
                || moeBusiness.getCountry().equals(BusinessConst.COUNTRY_US2));
        businessPreferenceDto.setDateFormat(TypeUtil.getDateFormat(businessPreferenceDto.getDateFormatType(), false));
        businessPreferenceDto.setCalendarFormat(
                TypeUtil.MAP.get("week_start_type_" + businessPreferenceDto.getCalendarFormatType()));
        businessPreferenceDto.setTimeFormat(
                TypeUtil.MAP.get("time_format_" + businessPreferenceDto.getTimeFormatType()));
        businessPreferenceDto.setUnitOfWeight(
                TypeUtil.MAP.get("united_weight_" + businessPreferenceDto.getUnitOfWeightType()));
        businessPreferenceDto.setUnitOfDistance(
                TypeUtil.MAP.get("united_distance_" + businessPreferenceDto.getUnitOfDistanceType()));
        businessPreferenceDto.setNumberFormat(
                TypeUtil.MAP.get("number_format_" + businessPreferenceDto.getNumberFormatType()));
        return businessPreferenceDto;
    }

    /**
     * 将type类型的数据，转换为前端可用数据
     */
    public BusinessPreferenceDto getBusinessPreference(Integer businessId) {
        MoeBusiness moeBusiness = getBusinessInfo(businessId);
        BusinessPreferenceDto businessPreferenceDto = new BusinessPreferenceDto();
        // 从business获取数据
        businessPreferenceDto.setCountry(moeBusiness.getCountry());
        businessPreferenceDto.setTimezoneName(moeBusiness.getTimezoneName());
        businessPreferenceDto.setCurrencyCode(moeBusiness.getCurrencyCode());
        businessPreferenceDto.setCurrencySymbol(moeBusiness.getCurrencySymbol());
        businessPreferenceDto.setDateFormatType(moeBusiness.getDateFormatType().intValue());
        businessPreferenceDto.setCalendarFormatType(
                moeBusiness.getCalendarFormatType().intValue());
        businessPreferenceDto.setTimeFormatType(moeBusiness.getTimeFormatType().intValue());
        businessPreferenceDto.setTimezoneSeconds(moeBusiness.getTimezoneSeconds());
        businessPreferenceDto.setUnitOfWeightType(
                moeBusiness.getUnitOfWeightType().intValue());
        businessPreferenceDto.setUnitOfDistanceType(
                moeBusiness.getUnitOfDistanceType().intValue());
        businessPreferenceDto.setNumberFormatType(
                moeBusiness.getNumberFormatType().intValue());
        businessPreferenceDto.setMessageSendBy(moeBusiness.getMessageSendBy());
        businessPreferenceDto.setNotificationSoundEnable(moeBusiness.getNotificationSoundEnable());
        // 再处理数据
        businessPreferenceDto.setNeedSendCode(moeBusiness.getCountry().equals(BusinessConst.COUNTRY_US)
                || moeBusiness.getCountry().equals(BusinessConst.COUNTRY_US2));
        businessPreferenceDto.setDateFormat(TypeUtil.getDateFormat(businessPreferenceDto.getDateFormatType(), false));
        businessPreferenceDto.setCalendarFormat(
                TypeUtil.MAP.get("week_start_type_" + businessPreferenceDto.getCalendarFormatType()));
        businessPreferenceDto.setTimeFormat(
                TypeUtil.MAP.get("time_format_" + businessPreferenceDto.getTimeFormatType()));
        businessPreferenceDto.setUnitOfWeight(
                TypeUtil.MAP.get("united_weight_" + businessPreferenceDto.getUnitOfWeightType()));
        businessPreferenceDto.setUnitOfDistance(
                TypeUtil.MAP.get("united_distance_" + businessPreferenceDto.getUnitOfDistanceType()));
        businessPreferenceDto.setNumberFormat(
                TypeUtil.MAP.get("number_format_" + businessPreferenceDto.getNumberFormatType()));
        // set default auto reply status
        businessPreferenceDto.setAutoReplyStatus(BusinessPreferenceDto.AUTO_REPLY_STATUS_CLOSE);
        try {
            // FIXME  message 的配置需要放到减少跨服务调用的
            MoeBusinessAutoReplyDto autoReplyDto = iMessageAutoClient.getBusinessAutoReplyForBusiness(businessId);
            if (autoReplyDto != null && BusinessPreferenceDto.AUTO_REPLY_STATUS_OPEN.equals(autoReplyDto.getStatus())) {
                businessPreferenceDto.setAutoReplyStatus(BusinessPreferenceDto.AUTO_REPLY_STATUS_OPEN);
            }
        } catch (FeignException ex) {
            log.error("get auto reply status failed", ex);
        }
        return businessPreferenceDto;
    }

    /**
     * 针对已经迁移的用户，从 company 获取 preference 信息
     */
    public BusinessPreferenceDto getBusinessPreferenceFromCompanyLevel(Integer businessId) {
        MoeBusiness moeBusiness = getBusinessInfo(businessId);
        BusinessPreferenceDto businessPreferenceDto = new BusinessPreferenceDto();

        // 从business获取数据
        businessPreferenceDto.setCountry(moeBusiness.getCountry());
        businessPreferenceDto.setTimezoneName(moeBusiness.getTimezoneName());
        businessPreferenceDto.setCurrencyCode(moeBusiness.getCurrencyCode());
        businessPreferenceDto.setCurrencySymbol(moeBusiness.getCurrencySymbol());
        businessPreferenceDto.setDateFormatType(moeBusiness.getDateFormatType().intValue());
        businessPreferenceDto.setCalendarFormatType(
                moeBusiness.getCalendarFormatType().intValue());
        businessPreferenceDto.setTimeFormatType(moeBusiness.getTimeFormatType().intValue());
        businessPreferenceDto.setTimezoneSeconds(moeBusiness.getTimezoneSeconds());
        businessPreferenceDto.setUnitOfWeightType(
                moeBusiness.getUnitOfWeightType().intValue());
        businessPreferenceDto.setUnitOfDistanceType(
                moeBusiness.getUnitOfDistanceType().intValue());
        businessPreferenceDto.setNumberFormatType(
                moeBusiness.getNumberFormatType().intValue());
        businessPreferenceDto.setMessageSendBy(moeBusiness.getMessageSendBy());
        businessPreferenceDto.setNotificationSoundEnable(moeBusiness.getNotificationSoundEnable());

        // 从 company 获取数据，覆盖 Business 的数据
        CompanyPreferenceSettingModel companyPreferenceSettingModel = preferenceHelper.getCompanyPreferenceSetting(
                moeBusiness.getCompanyId().longValue());
        if (Objects.nonNull(companyPreferenceSettingModel)) {
            businessPreferenceDto.setCurrencyCode(companyPreferenceSettingModel.getCurrencyCode());
            businessPreferenceDto.setCurrencySymbol(companyPreferenceSettingModel.getCurrencySymbol());
            businessPreferenceDto.setDateFormatType(
                    companyPreferenceSettingModel.getDateFormatType().getNumber());
            businessPreferenceDto.setTimeFormatType(
                    companyPreferenceSettingModel.getTimeFormatType().getNumber());
            businessPreferenceDto.setUnitOfWeightType(
                    companyPreferenceSettingModel.getUnitOfWeightType().getNumber());
            businessPreferenceDto.setUnitOfDistanceType(
                    companyPreferenceSettingModel.getUnitOfDistanceType().getNumber());
            businessPreferenceDto.setNotificationSoundEnable(
                    companyPreferenceSettingModel.getNotificationSoundEnable()
                            ? BooleanEnum.VALUE_TRUE
                            : BooleanEnum.VALUE_FALSE);
            businessPreferenceDto.setCountry(
                    companyPreferenceSettingModel.getCountry().getName());
            businessPreferenceDto.setTimezoneName(
                    companyPreferenceSettingModel.getTimeZone().getName());
            businessPreferenceDto.setTimezoneSeconds(
                    companyPreferenceSettingModel.getTimeZone().getSeconds());
        }

        // 再处理数据
        businessPreferenceDto.setNeedSendCode(moeBusiness.getCountry().equals(BusinessConst.COUNTRY_US)
                || moeBusiness.getCountry().equals(BusinessConst.COUNTRY_US2));
        businessPreferenceDto.setDateFormat(TypeUtil.getDateFormat(businessPreferenceDto.getDateFormatType(), false));
        businessPreferenceDto.setCalendarFormat(
                TypeUtil.MAP.get("week_start_type_" + businessPreferenceDto.getCalendarFormatType()));
        businessPreferenceDto.setTimeFormat(
                TypeUtil.MAP.get("time_format_" + businessPreferenceDto.getTimeFormatType()));
        businessPreferenceDto.setUnitOfWeight(
                TypeUtil.MAP.get("united_weight_" + businessPreferenceDto.getUnitOfWeightType()));
        businessPreferenceDto.setUnitOfDistance(
                TypeUtil.MAP.get("united_distance_" + businessPreferenceDto.getUnitOfDistanceType()));
        businessPreferenceDto.setNumberFormat(
                TypeUtil.MAP.get("number_format_" + businessPreferenceDto.getNumberFormatType()));
        // set default auto reply status
        businessPreferenceDto.setAutoReplyStatus(BusinessPreferenceDto.AUTO_REPLY_STATUS_CLOSE);
        try {
            // FIXME  message 的配置需要放到减少跨服务调用的
            MoeBusinessAutoReplyDto autoReplyDto = iMessageAutoClient.getBusinessAutoReplyForBusiness(businessId);
            if (autoReplyDto != null && BusinessPreferenceDto.AUTO_REPLY_STATUS_OPEN.equals(autoReplyDto.getStatus())) {
                businessPreferenceDto.setAutoReplyStatus(BusinessPreferenceDto.AUTO_REPLY_STATUS_OPEN);
            }
        } catch (FeignException ex) {
            log.error("get auto reply status failed", ex);
        }
        return businessPreferenceDto;
    }

    public Boolean updateBusinessSelective(MoeBusiness business) {
        business.setUpdateTime(DateUtil.get10Timestamp());
        if (business.getAppType() != null) {
            log.warn("business[{}] change app type to {}", business.getId(), business.getAppType());
        }
        // 切换address修改的州不在白名单范围内时，同时把processFeePayByClient关闭
        if (business.getAddress1() != null) {
            Boolean needClose =
                    paymentSettingClient.isNeedCloseProcessingFeeByClient(business.getId(), business.getAddressState());
            // 异步关闭processingFee的开关
            if (BooleanUtils.isTrue(needClose)) {
                ThreadPool.execute(() -> {
                    PaymentSettingDTO dto = new PaymentSettingDTO();
                    dto.setBusinessId(business.getId());
                    dto.setProcessingFeePayBy(PaymentSettingConst.PROCESSING_FEE_PAY_BY_BUSINESS);
                    iPaymentPaymentClient.updatePaymentSetting(dto);
                });
            }
        }

        moeBusinessMapper.updateByPrimaryKeySelective(business);
        if (business.getAddress1() != null) {
            MoeBusiness businessInfo = moeBusinessMapper.selectByPrimaryKey(business.getId());
            MoeBusiness saveBusinessInfo = new MoeBusiness();
            saveBusinessInfo.setId(business.getId());
            saveBusinessInfo.setAddress(CommonUtil.getFullAddress(
                    businessInfo.getAddress1(),
                    businessInfo.getAddress2(),
                    businessInfo.getAddressCity(),
                    businessInfo.getAddressState(),
                    businessInfo.getAddressCountry(),
                    businessInfo.getAddressZipcode()));
            moeBusinessMapper.updateByPrimaryKeySelective(saveBusinessInfo);
        }
        // 双写到 payment_setting 表，后续避免用 business 表的这个
        if (business.getPrimaryPayType() != null) {
            paymentSettingClient.updatePrimaryPayTypeByBusinessId(PrimaryPayTypeParams.builder()
                    .businessId(business.getId())
                    .primaryPayType(business.getPrimaryPayType())
                    .build());
        }
        return true;
    }

    public Map<Integer, MoeBusinessDto> getBusinessByCompanyId(Integer companyId) {
        List<MoeBusiness> businessList = moeBusinessMapper.getBusinessByCompanyId(companyId);
        Map<Integer, MoeBusinessDto> businessDtoMap = new HashMap<>();
        for (MoeBusiness business : businessList) {
            MoeBusinessDto businessDto = new MoeBusinessDto();
            BeanUtils.copyProperties(business, businessDto);
            businessDtoMap.put(business.getId(), businessDto);
        }
        return businessDtoMap;
    }

    public List<Integer> getBusinessIdsByCompanyId(Long companyId) {
        List<MoeBusiness> businessList = moeBusinessMapper.getBusinessByCompanyId(companyId.intValue());
        return businessList.stream().map(MoeBusiness::getId).toList();
    }

    public List<SendDailyEmailDto> getAllNeedSendDailyBusiness() {
        List<MoeBusiness> businessList = moeBusinessMapper.selectAllBusiness();
        Map<Integer, String> businessEmailMap = getBusinessOwnerEmailMap(null); // FIXME: 这行可能存在性能问题
        List<SendDailyEmailDto> emailDtos = new ArrayList<>();
        for (MoeBusiness moeBusiness : businessList) {
            String ownerEmail = businessEmailMap.get(moeBusiness.getId());
            if (StringUtils.isEmpty(ownerEmail)) {
                continue;
            }

            SendDailyEmailDto dailyEmailDto = new SendDailyEmailDto(
                    moeBusiness.getId(),
                    moeBusiness.getTimezoneName(),
                    moeBusiness.getTimezoneSeconds(),
                    moeBusiness.getCurrencySymbol(),
                    ownerEmail,
                    moeBusiness.getBusinessName(),
                    moeBusiness.getSendDaily(),
                    moeBusiness.getTimeFormatType());
            emailDtos.add(dailyEmailDto);
        }
        return emailDtos;
    }

    public List<Integer> getAllBusinessIds() {
        return moeBusinessMapper.selectAllBusinessIds();
    }

    public MoeBusinessDto getBusinessInfoForApi(Integer businessId) {
        MoeBusinessDto moeBusinessDto = new MoeBusinessDto();
        MoeBusiness moeBusiness = getBusinessInfo(businessId);
        BeanUtils.copyProperties(moeBusiness, moeBusinessDto);
        String ownerAccountEmail = getOwnerAccountEmail(businessId);
        moeBusinessDto.setOwnerEmail("");
        if (ownerAccountEmail != null) {
            moeBusinessDto.setOwnerEmail(ownerAccountEmail);
        }
        BusinessPreferenceDto businessPreferenceDto = getBusinessPreference(businessId);
        BeanUtils.copyProperties(businessPreferenceDto, moeBusinessDto);
        return moeBusinessDto;
    }

    public MoeBusinessDto getBusinessInfoWithOwnerEmailV2(Integer businessId) {
        MoeBusiness moeBusiness = getBusinessInfo(businessId);
        MoeBusinessDto moeBusinessDto = BusinessConverter.INSTANCE.toBusinessDTO(moeBusiness);
        String ownerAccountEmail = getOwnerAccountEmail(businessId);
        moeBusinessDto.setOwnerEmail("");
        if (ownerAccountEmail != null) {
            moeBusinessDto.setOwnerEmail(ownerAccountEmail);
        }
        // 获取 type 对应的 format
        BusinessPreferenceDto preferenceDto = getBusinessPreference(businessId);
        moeBusinessDto.setTimeFormat(preferenceDto.getTimeFormat());
        moeBusinessDto.setUnitOfWeight(preferenceDto.getUnitOfWeight());
        moeBusinessDto.setDateFormat(preferenceDto.getDateFormat());
        moeBusinessDto.setCalendarFormat(preferenceDto.getCalendarFormat());
        moeBusinessDto.setNumberFormat(preferenceDto.getNumberFormat());
        moeBusinessDto.setNeedSendCode(CountryUtils.isUnitedStates(preferenceDto.getCountry()));
        return moeBusinessDto;
    }

    public MoeBusinessDto getOnlyBusinessInfo(Integer businessId) {
        MoeBusinessDto moeBusinessDto = new MoeBusinessDto();
        MoeBusiness moeBusiness = getBusinessInfo(businessId);
        BeanUtils.copyProperties(moeBusiness, moeBusinessDto);
        BusinessPreferenceDto businessPreferenceDto = getOnlyBusinessPreference(moeBusiness);
        BeanUtils.copyProperties(businessPreferenceDto, moeBusinessDto);
        moeBusinessDto.setNeedSendCode(CountryUtils.isUnitedStates(moeBusiness.getCountry()));
        return moeBusinessDto;
    }

    public OBBusinessInfoDTO getOnlyBusinessInfoForOB(Integer businessId) {
        OBBusinessInfoDTO obBusinessInfoDTO = new OBBusinessInfoDTO();
        MoeBusiness moeBusiness = getBusinessInfo(businessId);
        BeanUtils.copyProperties(moeBusiness, obBusinessInfoDTO);
        BusinessPreferenceDto businessPreferenceDto = getOnlyBusinessPreference(moeBusiness);
        BeanUtils.copyProperties(businessPreferenceDto, obBusinessInfoDTO);
        obBusinessInfoDTO.setNeedSendCode(CountryUtils.isUnitedStates(moeBusiness.getCountry()));
        return obBusinessInfoDTO;
    }

    public Map<Integer, String> getBusinessOwnerEmailMap(List<Integer> businessIdList) {
        // businessId -> owner's accountId
        Map<Long, Long> businessAccountMap = staffService.getAccountIdForBusinessOwner(businessIdList);

        // accountId -> account model
        Map<Integer, AccountModel> accountMap = accountService.getAccountByIdList(
                businessAccountMap.values().stream().map(Long::intValue).collect(Collectors.toList()));

        // businessId -> email
        Map<Integer, String> businessEmailMap = new HashMap<>();
        for (var entry : businessAccountMap.entrySet()) {
            Long businessId = entry.getKey();
            Long accountId = businessAccountMap.get(businessId);
            if (accountId == null) {
                continue;
            }
            AccountModel account = accountMap.get(accountId.intValue());
            if (account != null) {
                businessEmailMap.put(businessId.intValue(), account.getEmail());
            }
        }
        return businessEmailMap;
    }

    public List<OBBusinessInfoDTO> getBusinessInfoListForOB(List<Integer> businessIdList) {
        List<MoeBusiness> businessList = moeBusinessMapper.getBusinessByIdList(businessIdList);
        // Get business owner email
        Map<Integer, String> businessOwnerEmailMap = getBusinessOwnerEmailMap(businessIdList);
        return businessList.stream()
                .map(business -> {
                    OBBusinessInfoDTO businessInfoDTO = new OBBusinessInfoDTO();
                    BeanUtils.copyProperties(business, businessInfoDTO);
                    businessInfoDTO.setOwnerEmail(businessOwnerEmailMap.getOrDefault(business.getId(), ""));
                    businessInfoDTO.setDateFormat(TypeUtil.getDateFormat(business.getDateFormatType(), false));
                    businessInfoDTO.setCalendarFormat(
                            TypeUtil.MAP.get("week_start_type_" + business.getCalendarFormatType()));
                    businessInfoDTO.setTimeFormat(TypeUtil.MAP.get("time_format_" + business.getTimeFormatType()));
                    businessInfoDTO.setUnitOfWeight(
                            TypeUtil.MAP.get("united_weight_" + business.getUnitOfWeightType()));
                    businessInfoDTO.setNumberFormat(
                            TypeUtil.MAP.get("number_format_" + business.getNumberFormatType()));
                    return businessInfoDTO;
                })
                .collect(Collectors.toList());
    }

    /**
     * 批量获取businessInfo 和 Preference
     *
     * @param ids
     * @return
     */
    public Map<Integer, MoeBusinessDto> getOnlyBusinessInfoBatch(List<Integer> ids) {
        List<MoeBusiness> businessByIdList = getBusinessByIdList(ids);
        if (CollectionUtils.isEmpty(businessByIdList)) {
            log.error("根据[ {} ] 获取到的business列表为空", ids.toString());
            return null;
        }
        Map<Integer, MoeBusinessDto> resultMap = new HashMap<>();
        businessByIdList.forEach(b -> {
            MoeBusinessDto moeBusinessDto = new MoeBusinessDto();
            BeanUtils.copyProperties(b, moeBusinessDto);
            BusinessPreferenceDto businessPreferenceDto = getOnlyBusinessPreference(b);
            BeanUtils.copyProperties(businessPreferenceDto, moeBusinessDto);
            resultMap.put(b.getId(), moeBusinessDto);
        });
        return resultMap;
    }

    public String getOwnerAccountEmail(Integer businessId) {
        MigrateInfo migrateInfo = migrateHelper.getMigrationInfo(businessId);
        MoeStaff queryStaff = new MoeStaff();
        if (migrateInfo.isMigrate()) {
            queryStaff.setCompanyId((int) migrateInfo.companyId());
        } else {
            queryStaff.setBusinessId(businessId);
        }
        queryStaff.setEmployeeCategory(StaffEnum.EMPLOYEE_CATEGORY_OWNER);
        queryStaff.setStatus(StaffEnum.STATUS_NORMAL);
        List<MoeStaff> staffList = staffMapper.queryMoeStaff(queryStaff);
        // AS 有一些脏数据，business 的 companyID 是负数，导致查不到 owner staff，先直接返回 null
        if (staffList.isEmpty()) {
            return null;
        }
        Integer accountId = staffList.get(0).getAccountId();
        AccountModel account = accountService.getAccountById(accountId);
        return account != null ? account.getEmail() : null;
    }

    public BusinessOptionsDto getBusinessOptions() {
        BusinessOptionsDto optionsMap = new BusinessOptionsDto();
        PreferenceOptionsDto preferenceOptionsDto = new PreferenceOptionsDto();
        preferenceOptionsDto.setCalendarFormatList(TypeUtil.CALENDAR_FORMAT_LIST);
        List<String> countryList = new ArrayList<>(Arrays.asList(TypeUtil.COUNTRIES));
        preferenceOptionsDto.setCountryArray(countryList);
        preferenceOptionsDto.setCurrencyList(TypeUtil.CURRENCY_LIST);
        preferenceOptionsDto.setDateFormatList(TypeUtil.DATE_FORMAT_LIST);
        preferenceOptionsDto.setNumberFormatList(TypeUtil.NUMBER_FORMAT_LIST);
        preferenceOptionsDto.setTimeFormatList(TypeUtil.TIME_FORMAT_LIST);
        preferenceOptionsDto.setUnitOfWeightList(TypeUtil.UNIT_OF_WEIGHT_LIST);
        preferenceOptionsDto.setUnitOfDistanceList(TypeUtil.UNIT_OF_DISTANCE_LIST);
        optionsMap.setPreference(preferenceOptionsDto);
        return optionsMap;
    }

    public CalendarSettingDTO getCalendarSetting(int businessId) {
        MoeCalendar calendar = calendarMapper.selectByBusinessId(businessId);
        if (calendar == null) {
            return CalendarSettingDTO.defaultValueBuilder()
                    .businessId(businessId)
                    .build();
        }

        return CalendarSettingDTO.builder()
                .businessId(businessId)
                .calendarViewStartAt(calendar.getCalendarViewStartAt())
                .calendarViewEndAt(calendar.getCalendarViewEndAt())
                .showAddress(calendar.getShowAddress().intValue())
                .showClientName(calendar.getShowClientName().intValue())
                .showColorCodeWith(calendar.getShowColorCodeWith().intValue())
                .showColorCodeDisplay(calendar.getShowColorCodeDisplay())
                .showComment(calendar.getShowComment().intValue())
                .showPetName(calendar.getShowPetName().intValue())
                .showPopupView(calendar.getShowPopupView().intValue())
                .showServiceName(calendar.getShowServiceName().intValue())
                .updateBy(calendar.getUpdateBy())
                .weeklyViewZoomLevel(calendar.getWeeklyViewZoomLevel().intValue())
                .calendarZoomLevel(calendar.getCalendarZoomLevel().intValue())
                .updateTime(calendar.getUpdateTime())
                .showAddressType(CalendarCardAreaShowType.fromValue(calendar.getShowAddressType()))
                .showPetCode(Objects.nonNull(calendar.getShowPetCode()) && calendar.getShowPetCode() != 0)
                .showPrice(Objects.nonNull(calendar.getShowPrice()) && calendar.getShowPrice() != 0)
                .showVaccinationAlert(
                        Objects.nonNull(calendar.getShowVaccinationAlert()) && calendar.getShowVaccinationAlert() != 0)
                .showAgreementNotSignedAlert(Objects.nonNull(calendar.getShowAgreementNotSignedAlert())
                        && calendar.getShowAgreementNotSignedAlert() != 0)
                .showCurrentTimeIndicator(calendar.getShowCurrentTimeIndicator())
                .showStaffServiceArea(calendar.getShowStaffServiceArea())
                .showStaffEstimatedRevenue(calendar.getShowStaffEstimatedRevenue())
                .showSlotLocation(calendar.getShowSlotLocation())
                .showSlotTime(calendar.getShowSlotTime())
                .build();
    }

    // FIXME(pc, P2): 可能产生脏数据
    public void updateCalendarSetting(Long companyId, int businessId, int accountId, CalendarSettingUpdateDTO setting) {
        MoeCalendar calendar = calendarMapper.selectByBusinessId(businessId);

        MoeCalendar moeCalendar = new MoeCalendar();
        moeCalendar.setCompanyId(companyId);
        moeCalendar.setBusinessId(businessId);
        moeCalendar.setUpdateBy(accountId);
        moeCalendar.setUpdateTime(System.currentTimeMillis() / 1000);

        if (calendar != null) {
            // update only
            moeCalendar.setId(calendar.getId());
            assignValue(setting, moeCalendar);
            calendarMapper.updateByPrimaryKeySelective(moeCalendar);
        } else {
            // insert for new item
            fillDefault(moeCalendar);
            assignValue(setting, moeCalendar);
            calendarMapper.insertSelective(moeCalendar);
        }
    }

    private void assignValue(CalendarSettingUpdateDTO source, MoeCalendar target) {
        if (source.getCalendarViewStartAt() != null) {
            target.setCalendarViewStartAt(source.getCalendarViewStartAt());
        }
        if (source.getCalendarViewEndAt() != null) {
            target.setCalendarViewEndAt(source.getCalendarViewEndAt());
        }
        if (source.getShowAddress() != null) {
            target.setShowAddress(source.getShowAddress().byteValue());
        }
        if (source.getShowClientName() != null) {
            target.setShowClientName(source.getShowClientName().byteValue());
        }
        if (source.getShowColorCodeWith() != null) {
            target.setShowColorCodeWith(source.getShowColorCodeWith().byteValue());
        }
        if (source.getShowColorCodeDisplay() != null) {
            target.setShowColorCodeDisplay(source.getShowColorCodeDisplay());
        }
        if (source.getShowComment() != null) {
            target.setShowComment(source.getShowComment().byteValue());
        }
        if (source.getShowPetName() != null) {
            target.setShowPetName(source.getShowPetName().byteValue());
        }
        if (source.getShowPopupView() != null) {
            target.setShowPopupView(source.getShowPopupView().byteValue());
        }
        if (source.getShowServiceName() != null) {
            target.setShowServiceName(source.getShowServiceName().byteValue());
        }
        if (source.getWeeklyViewZoomLevel() != null) {
            target.setWeeklyViewZoomLevel(source.getWeeklyViewZoomLevel().byteValue());
        }
        if (source.getCalendarZoomLevel() != null) {
            target.setCalendarZoomLevel(source.getCalendarZoomLevel().byteValue());
        }
        if (Objects.nonNull(source.getShowAddressType())) {
            target.setShowAddressType((byte) source.getShowAddressType().getValue());
        }
        if (Objects.nonNull(source.getShowPetCode())) {
            target.setShowPetCode(source.getShowPetCode() ? BooleanEnum.VALUE_TRUE : BooleanEnum.VALUE_FALSE);
        }
        if (Objects.nonNull(source.getShowPrice())) {
            target.setShowPrice(source.getShowPrice() ? BooleanEnum.VALUE_TRUE : BooleanEnum.VALUE_FALSE);
        }
        if (Objects.nonNull(source.getShowVaccinationAlert())) {
            target.setShowVaccinationAlert(
                    source.getShowVaccinationAlert() ? BooleanEnum.VALUE_TRUE : BooleanEnum.VALUE_FALSE);
        }
        if (Objects.nonNull(source.getShowAgreementNotSignedAlert())) {
            target.setShowAgreementNotSignedAlert(
                    source.getShowAgreementNotSignedAlert() ? BooleanEnum.VALUE_TRUE : BooleanEnum.VALUE_FALSE);
        }
        if (Objects.nonNull(source.getShowCurrentTimeIndicator())) {
            target.setShowCurrentTimeIndicator(source.getShowCurrentTimeIndicator());
        }
        if (source.getShowStaffServiceArea() != null) {
            target.setShowStaffServiceArea(source.getShowStaffServiceArea());
        }
        if (source.getShowStaffEstimatedRevenue() != null) {
            target.setShowStaffEstimatedRevenue(source.getShowStaffEstimatedRevenue());
        }
        if (source.getShowSlotLocation() != null) {
            target.setShowSlotLocation(source.getShowSlotLocation());
        }
        if (source.getShowSlotTime() != null) {
            target.setShowSlotTime(source.getShowSlotTime());
        }
    }

    private void fillDefault(MoeCalendar moeCalendar) {
        moeCalendar.setCalendarViewStartAt(BusinessCalendarEnum.DEFAULT_CALENDAR_VIEW_START_AT);
        moeCalendar.setCalendarViewEndAt(BusinessCalendarEnum.DEFAULT_CALENDAR_VIEW_END_AT);
        moeCalendar.setShowAddress(Byte.valueOf("1"));
        moeCalendar.setShowClientName(Byte.valueOf("1"));
        moeCalendar.setShowColorCodeWith(Byte.valueOf("1"));
        moeCalendar.setShowPetName(Byte.valueOf("1"));
        moeCalendar.setShowPopupView(Byte.valueOf("1"));
        moeCalendar.setShowServiceName(Byte.valueOf("1"));
        moeCalendar.setWeeklyViewZoomLevel(Byte.valueOf("1"));
        moeCalendar.setCalendarZoomLevel(Byte.valueOf("1"));
        moeCalendar.setShowStaffServiceArea(false);
    }

    public Integer getBusinessId(String obName) {
        return groomingOnlineBookingClient.getBusinessId(obName);
    }

    public List<MoeBusiness> getCompanyBusinessList(Integer companyId) {
        return moeBusinessMapper.getBusinessByCompanyId(companyId);
    }

    public List<MoeBusiness> getBusinessListByCompanyIds(List<Integer> companyIds) {
        if (CollectionUtils.isEmpty(companyIds)) {
            return List.of();
        }
        return moeBusinessMapper.selectAllBusinessByCompanyId(companyIds);
    }

    /**
     * 获取 CACD 设置（目前只有开关）
     *
     * @param businessId businessId
     * @return ServiceAreaSettingDTO
     */
    public ServiceAreaSettingDTO getServiceAreaSetting(Integer businessId) {
        MoeBusiness business = moeBusinessMapper.selectByPrimaryKey(businessId);
        if (business == null) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "No business found for given id: " + businessId);
        }

        return new ServiceAreaSettingDTO()
                .setBusinessId(business.getId())
                .setServiceAreaEnable(business.getServiceAreaEnable());
    }

    public SmartScheduleSettingDTO getSmartScheduleSetting(Long companyId, Long tokenBusinessId) {
        Integer businessId;
        try {
            businessId = getBusinessIdForMobile(companyId, tokenBusinessId);
        } catch (Exception e) {
            log.warn("get mobile business id error", e);
            // 非 Mobile business，返回默认值即可
            return SmartScheduleSettingDTO.builder()
                    .businessId(tokenBusinessId.intValue())
                    .maxDistance(30)
                    .maxTime(45)
                    .serviceRange(50)
                    .build();
        }
        MoeBusiness business = moeBusinessMapper.selectByPrimaryKey(businessId);
        if (business == null) {
            throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "No business Found for given id: " + businessId);
        }

        GetSmartScheduleSettingResponse smartScheduleSetting =
                smartScheduleSettingServiceBlockingStub.getSmartScheduleSetting(
                        GetSmartScheduleSettingRequest.newBuilder()
                                .setTokenCompanyId(companyId)
                                .build());

        return SmartScheduleSettingDTO.builder()
                .businessId(business.getId())
                .startLocationLat(business.getSmartScheduleStartLat())
                .startLocationLng(business.getSmartScheduleStartLng())
                .endLocationLat(business.getSmartScheduleEndLat())
                .endLocationLng(business.getSmartScheduleEndLng())
                .startLocationAddr(business.getSmartScheduleStartAddr())
                .endLocationAddr(business.getSmartScheduleEndAddr())
                .maxDistance(business.getSmartScheduleMaxDist())
                .maxTime(business.getSmartScheduleMaxTime())
                .serviceRange(business.getSmartScheduleServiceRange())
                .unitOfDistanceType(business.getUnitOfDistanceType())
                .serviceAreaEnable(
                        smartScheduleSetting.getSmartScheduleSetting().getServiceAreaEnable() ? (byte) 1 : 0)
                .build();
    }

    public SmartScheduleSettingDTO updateSmartScheduleSetting(
            Integer companyId, Integer businessId, SmartScheduleSettingParams setting) {
        log.info("update business {} from param : {}", businessId, setting);
        if (setting.getServiceAreaEnable() == null) {
            // 老接口不再支持 ss 字段的更新，只支持 CACD 开关的更新，抛异常提示用户更新 app
            throw ExceptionUtil.bizException(
                    Code.CODE_PARAMS_ERROR, "Save failed. Please update the app to the newest version.");
        }

        smartScheduleSettingServiceBlockingStub.updateSmartScheduleSetting(
                UpdateSmartScheduleSettingRequest.newBuilder()
                        .setSmartScheduleSetting(UpdateSmartScheduleSettingDef.newBuilder()
                                .setServiceAreaEnable(setting.getServiceAreaEnable() == 1)
                                .build())
                        .setTokenCompanyId(companyId)
                        .build());

        MoeBusiness moeBusiness = new MoeBusiness();
        moeBusiness.setId(businessId);
        moeBusiness.setServiceAreaEnable(setting.getServiceAreaEnable());
        moeBusinessMapper.updateByPrimaryKeySelective(moeBusiness);

        return SmartScheduleSettingDTO.builder()
                .businessId(businessId)
                .startLocationLat(setting.getStartLocationLat())
                .startLocationLng(setting.getStartLocationLng())
                .endLocationLat(setting.getEndLocationLat())
                .endLocationLng(setting.getEndLocationLng())
                .startLocationAddr(setting.getStartLocationAddr())
                .endLocationAddr(setting.getEndLocationAddr())
                .maxDistance(setting.getMaxDistance())
                .maxTime(setting.getMaxTime())
                .serviceRange(setting.getServiceRange())
                .serviceAreaEnable(setting.getServiceAreaEnable())
                .build();
    }

    public Integer insertStaff(MoeStaff staff) {
        MoeBusiness business = moeBusinessMapper.selectByPrimaryKey(staff.getBusinessId());
        if (business == null || !staffService.checkStaffNumIsEnough(business.getCompanyId())) {
            throw new CommonException(ResponseCodeEnum.CAN_NOT_CREATE_STAFF);
        }
        staff.setCompanyId(business.getCompanyId());
        return staffService.insert(staff);
    }

    public BusinessDateTimeDTO getBusinessDateTime(Integer businessId) {
        MoeBusiness business = moeBusinessMapper.useDataSource(READER).selectByPrimaryKey(businessId);
        if (Objects.isNull(business)) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "No business Found for given id: " + businessId);
        }
        LocalDateTime localDateTime = LocalDateTime.now(ZoneId.of(business.getTimezoneName()));
        return new BusinessDateTimeDTO()
                .setBusinessId(business.getId())
                .setCurrentDate(localDateTime.toLocalDate().toString())
                .setCurrentMinutes((int) localDateTime.getLong(ChronoField.MINUTE_OF_DAY))
                .setLocalDateTime(localDateTime)
                .setTimezoneName(business.getTimezoneName());
    }

    public List<BusinessDateTimeDTO> listBusinessDateTime(List<Integer> businessIds) {
        if (ObjectUtils.isEmpty(businessIds)) {
            return List.of();
        }
        return moeBusinessMapper.getBusinessByIdList(businessIds).stream()
                .map(business -> {
                    LocalDateTime localDateTime = LocalDateTime.now(ZoneId.of(business.getTimezoneName()));
                    return new BusinessDateTimeDTO()
                            .setBusinessId(business.getId())
                            .setCurrentDate(localDateTime.toLocalDate().toString())
                            .setCurrentMinutes((int) localDateTime.getLong(ChronoField.MINUTE_OF_DAY))
                            .setLocalDateTime(localDateTime)
                            .setTimezoneName(business.getTimezoneName());
                })
                .toList();
    }

    public List<Integer> getUSBusinessId() {
        return moeBusinessMapper.getUSBusinessId();
    }

    public Pair<List<MoeBusiness>, Pagination> describeBusinesses(
            DescribeBusinessesParams params, Pagination pagination) {
        if (hasEmptyCollectionFilter(
                params.ids(), params.companyIds(), params.ownerIds(), params.countries(), params.companyNames())) {
            return Pair.of(Collections.emptyList(), new Pagination(pagination.pageNum(), pagination.pageSize(), 0));
        }
        return selectPage(pagination, () -> moeBusinessMapper.describeBusinesses(params));
    }

    public Integer getCompanyIdByBusinessId(Integer businessId) {
        return moeBusinessMapper.useDataSource(READER).getCompanyIdByBusinessId(businessId);
    }

    public Boolean isBusinessRetailEnable(Integer businessId) {
        MoeBusiness business = moeBusinessMapper.selectByPrimaryKey(businessId);
        if (business == null) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "No business Found for given id: " + businessId);
        }
        return Objects.equals(business.getRetailEnable(), BooleanEnum.VALUE_TRUE);
    }

    public Map<Integer, Boolean> isBusinessRetailEnable(List<Integer> businessIdList) {
        Map<Integer, Boolean> result = new HashMap<>();
        if (CollectionUtils.isEmpty(businessIdList)) {
            return result;
        }
        List<MoeBusiness> business = moeBusinessMapper.getBusinessByIdList(businessIdList);
        for (MoeBusiness moeBusiness : business) {
            result.put(moeBusiness.getId(), Objects.equals(moeBusiness.getRetailEnable(), BooleanEnum.VALUE_TRUE));
        }
        return result;
    }

    public BatchQueryBusinessResult batchQueryBusinessInfo(BatchQueryBusinessParams params) {
        Integer pageNum = params.getPageNum();
        if (pageNum == null || pageNum <= 0) {
            pageNum = 1;
        }
        Integer pageSize = params.getPageSize();
        if (pageSize == null || pageSize <= 0) {
            pageSize = 3000;
        }
        PageHelper.startPage(pageNum, pageSize);
        List<TaskBusinessInfoDto> dtoList =
                moeBusinessMapper.batchQueryBusinessInfoByParam(params.getIsPremium(), params.getIsPushCalendar());
        for (TaskBusinessInfoDto infodto : dtoList) {
            infodto.setDateFormat(TypeUtil.getDateFormat(infodto.getDateFormatType(), false));
        }
        var pageInfo = new PageInfo<>(dtoList);
        if (pageInfo.getPageNum() < pageNum) {
            return new BatchQueryBusinessResult(Collections.emptyMap(), pageNum, false, pageInfo.getTotal(), 0);
        }
        return new BatchQueryBusinessResult(
                dtoList.stream().collect(Collectors.toMap(TaskBusinessInfoDto::getId, Function.identity())),
                pageInfo.getPageNum(),
                pageInfo.isHasNextPage(),
                pageInfo.getTotal(),
                pageInfo.getSize());
    }

    public Integer getBusinessIdByInvitationCode(String invitationCode) {
        if (StringUtils.isBlank(invitationCode)) {
            return null;
        }
        MoeBusinessExample example = new MoeBusinessExample();
        example.createCriteria().andInvitationCodeEqualTo(invitationCode);
        List<MoeBusiness> businesses = moeBusinessMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(businesses)) {
            return null;
        }
        return businesses.get(0).getId();
    }

    public Integer getBusinessIdForMobile(Long companyId, Long tokenBusinessId) {
        if (!migrateHelper.isMigrate(AuthContext.get())) {
            // 迁移前的用户，还是使用 token 里面的 Business Id
            return tokenBusinessId.intValue();
        }

        MoeBusinessExample example = new MoeBusinessExample();
        example.createCriteria()
                .andCompanyIdEqualTo(companyId.intValue())
                .andAppTypeIn(List.of(BusinessConst.APP_TYPE_MOBILE, BusinessConst.APP_TYPE_HYBRID));

        List<MoeBusiness> moeBusinesses =
                moeBusinessMapper.useDataSource(READER).selectByExample(example);
        if (CollectionUtils.isEmpty(moeBusinesses)) {
            return tokenBusinessId.intValue();
        }
        if (moeBusinesses.size() > 1) {
            throw ExceptionUtil.bizException(Code.CODE_SERVER_ERROR, "more than one mobile business found");
        }
        return moeBusinesses.get(0).getId();
    }
}
