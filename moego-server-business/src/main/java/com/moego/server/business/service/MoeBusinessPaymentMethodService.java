package com.moego.server.business.service;

import com.moego.common.dto.SortDto;
import com.moego.common.enums.BooleanEnum;
import com.moego.common.enums.PaymentMethodEnum;
import com.moego.common.enums.ResponseCodeEnum;
import com.moego.common.exception.CommonException;
import com.moego.common.response.ResponseResult;
import com.moego.common.utils.CommonUtil;
import com.moego.common.utils.SortUtils;
import com.moego.server.business.dto.PaymentMethodDto;
import com.moego.server.business.mapper.MoeBusinessPaymentMethodMapper;
import com.moego.server.business.mapperbean.MoeBusinessPaymentMethod;
import com.moego.server.business.web.vo.PaymentMethodStatusVo;
import com.moego.server.business.web.vo.PaymentMethodVo;
import com.moego.server.customer.dto.AddResultDTO;
import com.moego.server.grooming.client.IGroomingOnlineBookingClient;
import java.util.ArrayList;
import java.util.List;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class MoeBusinessPaymentMethodService {

    @Autowired
    private MoeBusinessPaymentMethodMapper moeBusinessPaymentMethodMapper;

    @Autowired
    private IGroomingOnlineBookingClient groomingOnlineBookingClient;

    @Transactional
    public ResponseResult<AddResultDTO> addPayment(Long companyId, PaymentMethodVo paymentMethodVo) {
        if (StringUtils.isBlank(paymentMethodVo.getMethod())) {
            throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "method is not null");
        }
        paymentMethodVo.setMethod(paymentMethodVo.getMethod().trim());
        MoeBusinessPaymentMethod m = moeBusinessPaymentMethodMapper.selectByMethod(
                paymentMethodVo.getBusinessId(), paymentMethodVo.getMethod(), null);
        if (m != null) {
            throw new CommonException(ResponseCodeEnum.PAYMENT_METHOD_NAME_EXISTS);
        }
        MoeBusinessPaymentMethod moeBusinessPaymentMethod = new MoeBusinessPaymentMethod();
        BeanUtils.copyProperties(paymentMethodVo, moeBusinessPaymentMethod);
        moeBusinessPaymentMethod.setCompanyId(companyId);
        moeBusinessPaymentMethod.setName(paymentMethodVo.getMethod());
        moeBusinessPaymentMethod.setCreateTime(CommonUtil.get10Timestamp());
        moeBusinessPaymentMethod.setUpdateTime(CommonUtil.get10Timestamp());
        //        moeBusinessPaymentMethod.setStatus(DeleteStatusEnum.STATUS_NORMAL);
        //        moeBusinessPaymentMethod.setInactive(BooleanEnum.INACTIVE_FALSE);
        moeBusinessPaymentMethodMapper.insertSelective(moeBusinessPaymentMethod);
        // 更新methodId&sort
        MoeBusinessPaymentMethod record = new MoeBusinessPaymentMethod();
        record.setId(moeBusinessPaymentMethod.getId());
        record.setMethodId(moeBusinessPaymentMethod.getId());
        record.setSort(moeBusinessPaymentMethod.getId());
        moeBusinessPaymentMethodMapper.updateByPrimaryKeySelective(record);
        AddResultDTO addResultDTO = new AddResultDTO();
        addResultDTO.setResult(true);
        addResultDTO.setId(moeBusinessPaymentMethod.getId());
        return ResponseResult.success(addResultDTO);
    }

    /**
     * 当某个支付方式是最后一个有效的支付方式，既不能删除，也不能关闭
     *
     * @param businessId
     * @param paymentMethodId
     * @return
     */
    public Boolean checkLastOnePaymentMethod(Integer businessId, Integer paymentMethodId) {
        List<MoeBusinessPaymentMethod> paymentMethodList =
                moeBusinessPaymentMethodMapper.getEffectivePayment(businessId);
        if (paymentMethodList.size() == 1 && paymentMethodList.get(0).getId().equals(paymentMethodId)) {
            // 只剩一个有效的支付方式，且是当前这个，所以，禁止关闭
            return false;
        }
        return true;
    }

    @Transactional
    public ResponseResult<Integer> modifyPayment(PaymentMethodVo paymentMethodVo) {
        if (StringUtils.isBlank(paymentMethodVo.getMethod())) {
            throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "method should not be null");
        }
        paymentMethodVo.setMethod(paymentMethodVo.getMethod().trim());
        MoeBusinessPaymentMethod m = moeBusinessPaymentMethodMapper.selectByMethod(
                paymentMethodVo.getBusinessId(), paymentMethodVo.getMethod(), paymentMethodVo.getId());
        if (m != null) {
            throw new CommonException(ResponseCodeEnum.PAYMENT_METHOD_NAME_EXISTS);
        }
        MoeBusinessPaymentMethod thisMethod =
                moeBusinessPaymentMethodMapper.selectByPrimaryKey(paymentMethodVo.getId());
        if (thisMethod.getType().equals(PaymentMethodEnum.TYPE_DEFAULT_TRUE)) {
            throw new CommonException(ResponseCodeEnum.DEFAULT_METHOD_OPER_FALSE);
        }
        checkAuth(paymentMethodVo.getId(), paymentMethodVo.getBusinessId());
        MoeBusinessPaymentMethod moeBusinessPaymentMethod = new MoeBusinessPaymentMethod();
        moeBusinessPaymentMethod.setId(paymentMethodVo.getId());
        moeBusinessPaymentMethod.setName(paymentMethodVo.getMethod());
        moeBusinessPaymentMethod.setUpdateTime(CommonUtil.get10Timestamp());
        int i = moeBusinessPaymentMethodMapper.updateByPrimaryKeySelective(moeBusinessPaymentMethod);
        return ResponseResult.success(i);
    }

    private ResponseResult<MoeBusinessPaymentMethod> checkAuth(Integer id, Integer businessId) {
        MoeBusinessPaymentMethod moeBusinessPaymentMethod = moeBusinessPaymentMethodMapper.selectByPrimaryKey(id);
        if (moeBusinessPaymentMethod == null) {
            throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "not found object");
        }

        if (!moeBusinessPaymentMethod.getBusinessId().equals(businessId)) {
            throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "no auth operation");
        }
        return ResponseResult.success(moeBusinessPaymentMethod);
    }

    @Transactional
    public ResponseResult<Integer> deletePayment(Integer id, Integer tokenBusinessId) {
        checkAuth(id, tokenBusinessId);

        if (!checkLastOnePaymentMethod(tokenBusinessId, id)) {
            throw new CommonException(ResponseCodeEnum.THE_LAST_INACTIVE);
        }
        MoeBusinessPaymentMethod thisMethod = moeBusinessPaymentMethodMapper.selectByPrimaryKey(id);
        if (thisMethod.getType().equals(PaymentMethodEnum.TYPE_DEFAULT_TRUE)) {
            throw new CommonException(ResponseCodeEnum.DEFAULT_METHOD_OPER_FALSE);
        }
        Integer i = moeBusinessPaymentMethodMapper.deletePayment(id);
        return ResponseResult.success(i);
    }

    @Transactional
    public ResponseResult<Integer> modifyPaymentStatus(PaymentMethodStatusVo statusVo, Integer tokenBusinessId) {
        ResponseResult<MoeBusinessPaymentMethod> x = checkAuth(statusVo.getId(), tokenBusinessId);
        if (statusVo.getInactive().equals(BooleanEnum.INACTIVE_TRUE)) {
            if (!checkLastOnePaymentMethod(tokenBusinessId, statusVo.getId())) {
                throw new CommonException(ResponseCodeEnum.THE_LAST_INACTIVE);
            }
        }

        MoeBusinessPaymentMethod data = x.getData();
        if (data.getMethodId().equals(PaymentMethodEnum.METHOD_CREDIT_CARD)) {
            if (statusVo.getInactive().equals(BooleanEnum.INACTIVE_TRUE)) {
                // 关闭create credit也会关闭ob的开关
                groomingOnlineBookingClient.updateBusinessBookOnlineSetting(tokenBusinessId);
            }
        }
        MoeBusinessPaymentMethod method = new MoeBusinessPaymentMethod();
        method.setInactive(statusVo.getInactive());
        method.setId(statusVo.getId());
        method.setUpdateTime(CommonUtil.get10Timestamp());

        int i = moeBusinessPaymentMethodMapper.updateByPrimaryKeySelective(method);
        return ResponseResult.success(i);
    }

    @Transactional
    public ResponseResult<Integer> sortPaymentMethod(List<Integer> idList, Integer tokenBusinessId) {
        if (idList.size() == 0) {
            return ResponseResult.success(0);
        }
        // 非本商家method不得修改
        List<Integer> businessIds = moeBusinessPaymentMethodMapper.selectBusinessIdByIdList(idList);
        if (businessIds == null || businessIds.size() == 0) {
            throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "not auth operation");
        }
        for (Integer b : businessIds) {
            if (!tokenBusinessId.equals(b)) {
                throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "not auth operation");
            }
        }
        List<SortDto> sort = SortUtils.sort(idList);
        if (sort.size() == 0) {
            return ResponseResult.success(0);
        }
        moeBusinessPaymentMethodMapper.sortPaymentMethod(sort);
        return ResponseResult.success(sort.size());
    }

    public List<MoeBusinessPaymentMethod> getPaymentByBusiness(Integer tokenBusinessId) {
        return moeBusinessPaymentMethodMapper.getPaymentByBusiness(tokenBusinessId);
    }

    public Boolean checkCreditCardIsActive(Integer businessId) {
        // 返回的是inactive 0  credit card可用
        return moeBusinessPaymentMethodMapper.getCreditCardInactive(businessId) == 0;
    }

    public List<PaymentMethodDto> getActivePaymentByBusiness(Integer businessId) {
        List<MoeBusinessPaymentMethod> paymentMethods =
                moeBusinessPaymentMethodMapper.getActivePaymentByBusiness(businessId);
        List<PaymentMethodDto> paymentMethodDtos = new ArrayList<>();
        for (MoeBusinessPaymentMethod paymentMethod : paymentMethods) {
            PaymentMethodDto paymentMethodDto = new PaymentMethodDto();
            paymentMethodDto.setMethodId(paymentMethod.getMethodId());
            paymentMethodDto.setName(paymentMethod.getName());
            paymentMethodDto.setType(paymentMethod.getType());
            paymentMethodDtos.add(paymentMethodDto);
        }
        return paymentMethodDtos;
    }
}
