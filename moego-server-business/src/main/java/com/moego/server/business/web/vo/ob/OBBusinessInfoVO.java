package com.moego.server.business.web.vo.ob;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2023/2/20
 */
@Data
@Accessors(chain = true)
public class OBBusinessInfoVO {

    @Schema(description = "Business id")
    private Integer businessId;

    @Schema(description = "Business info name")
    private String businessName;

    @Schema(description = "Business info phone number")
    private String phoneNumber;

    @Schema(description = "Business info logo path")
    private String avatarPath;

    @Schema(description = "Full address")
    private String address;

    @Schema(description = "Address 1")
    private String address1;

    @Schema(description = "Address 2")
    private String address2;

    @Schema(description = "Address city")
    private String addressCity;

    @Schema(description = "Address state")
    private String addressState;

    @Schema(description = "Address zipcode")
    private String addressZipcode;

    @Schema(description = "Address country")
    private String addressCountry;

    @Schema(description = "Address latitude")
    private String addressLat;

    @Schema(description = "Address longitude")
    private String addressLng;

    @Schema(description = "Business website")
    private String website;

    @Schema(description = "Business facebook")
    private String facebook;

    @Schema(description = "Business instagram")
    private String instagram;

    @Schema(description = "Business google")
    private String google;

    @Schema(description = "Business yelp")
    private String yelp;
}
