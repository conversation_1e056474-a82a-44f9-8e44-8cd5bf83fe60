package com.moego.server.business.mapperbean;

import java.math.BigDecimal;

public class MoeReferralBonusRewardRule {

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_referral_bonus_reward_rule.id
     *
     * @mbg.generated
     */
    private Integer id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_referral_bonus_reward_rule.target_number
     *
     * @mbg.generated
     */
    private Integer targetNumber;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_referral_bonus_reward_rule.bonus_amount
     *
     * @mbg.generated
     */
    private BigDecimal bonusAmount;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_referral_bonus_reward_rule.status
     *
     * @mbg.generated
     */
    private Byte status;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_referral_bonus_reward_rule.create_time
     *
     * @mbg.generated
     */
    private Long createTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_referral_bonus_reward_rule.update_time
     *
     * @mbg.generated
     */
    private Long updateTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_referral_bonus_reward_rule.bonus_name
     *
     * @mbg.generated
     */
    private String bonusName;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_referral_bonus_reward_rule.img_src
     *
     * @mbg.generated
     */
    private String imgSrc;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_referral_bonus_reward_rule.tier
     *
     * @mbg.generated
     */
    private Byte tier;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_referral_bonus_reward_rule.id
     *
     * @return the value of moe_referral_bonus_reward_rule.id
     *
     * @mbg.generated
     */
    public Integer getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_referral_bonus_reward_rule.id
     *
     * @param id the value for moe_referral_bonus_reward_rule.id
     *
     * @mbg.generated
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_referral_bonus_reward_rule.target_number
     *
     * @return the value of moe_referral_bonus_reward_rule.target_number
     *
     * @mbg.generated
     */
    public Integer getTargetNumber() {
        return targetNumber;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_referral_bonus_reward_rule.target_number
     *
     * @param targetNumber the value for moe_referral_bonus_reward_rule.target_number
     *
     * @mbg.generated
     */
    public void setTargetNumber(Integer targetNumber) {
        this.targetNumber = targetNumber;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_referral_bonus_reward_rule.bonus_amount
     *
     * @return the value of moe_referral_bonus_reward_rule.bonus_amount
     *
     * @mbg.generated
     */
    public BigDecimal getBonusAmount() {
        return bonusAmount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_referral_bonus_reward_rule.bonus_amount
     *
     * @param bonusAmount the value for moe_referral_bonus_reward_rule.bonus_amount
     *
     * @mbg.generated
     */
    public void setBonusAmount(BigDecimal bonusAmount) {
        this.bonusAmount = bonusAmount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_referral_bonus_reward_rule.status
     *
     * @return the value of moe_referral_bonus_reward_rule.status
     *
     * @mbg.generated
     */
    public Byte getStatus() {
        return status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_referral_bonus_reward_rule.status
     *
     * @param status the value for moe_referral_bonus_reward_rule.status
     *
     * @mbg.generated
     */
    public void setStatus(Byte status) {
        this.status = status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_referral_bonus_reward_rule.create_time
     *
     * @return the value of moe_referral_bonus_reward_rule.create_time
     *
     * @mbg.generated
     */
    public Long getCreateTime() {
        return createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_referral_bonus_reward_rule.create_time
     *
     * @param createTime the value for moe_referral_bonus_reward_rule.create_time
     *
     * @mbg.generated
     */
    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_referral_bonus_reward_rule.update_time
     *
     * @return the value of moe_referral_bonus_reward_rule.update_time
     *
     * @mbg.generated
     */
    public Long getUpdateTime() {
        return updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_referral_bonus_reward_rule.update_time
     *
     * @param updateTime the value for moe_referral_bonus_reward_rule.update_time
     *
     * @mbg.generated
     */
    public void setUpdateTime(Long updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_referral_bonus_reward_rule.bonus_name
     *
     * @return the value of moe_referral_bonus_reward_rule.bonus_name
     *
     * @mbg.generated
     */
    public String getBonusName() {
        return bonusName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_referral_bonus_reward_rule.bonus_name
     *
     * @param bonusName the value for moe_referral_bonus_reward_rule.bonus_name
     *
     * @mbg.generated
     */
    public void setBonusName(String bonusName) {
        this.bonusName = bonusName == null ? null : bonusName.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_referral_bonus_reward_rule.img_src
     *
     * @return the value of moe_referral_bonus_reward_rule.img_src
     *
     * @mbg.generated
     */
    public String getImgSrc() {
        return imgSrc;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_referral_bonus_reward_rule.img_src
     *
     * @param imgSrc the value for moe_referral_bonus_reward_rule.img_src
     *
     * @mbg.generated
     */
    public void setImgSrc(String imgSrc) {
        this.imgSrc = imgSrc == null ? null : imgSrc.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_referral_bonus_reward_rule.tier
     *
     * @return the value of moe_referral_bonus_reward_rule.tier
     *
     * @mbg.generated
     */
    public Byte getTier() {
        return tier;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_referral_bonus_reward_rule.tier
     *
     * @param tier the value for moe_referral_bonus_reward_rule.tier
     *
     * @mbg.generated
     */
    public void setTier(Byte tier) {
        this.tier = tier;
    }
}
