package com.moego.server.business.web;

import com.moego.idl.models.errors.v1.Code;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.server.business.dto.BusinessAgreementDTO;
import com.moego.server.business.service.BusinessService;
import com.moego.server.business.service.MoeBusinessAgreementService;
import com.moego.server.grooming.client.IGroomingOnlineBookingClient;
import com.moego.server.grooming.params.ob.OBAnonymousParams;
import java.util.List;
import java.util.Objects;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @since 2022/10/11
 */
@RestController
@RequestMapping("/business/ob/v2")
@Slf4j
@AllArgsConstructor
public class BookOnlineV2Controller {

    private final MoeBusinessAgreementService moeBusinessAgreementService;

    private final BusinessService businessService;

    private final IGroomingOnlineBookingClient onlineBookingClient;

    @GetMapping("/client/agreement")
    @Auth(AuthType.OB)
    public List<BusinessAgreementDTO> getBookOnlineClientAgreements(
            Integer businessId, // todo: todo: remove businessId
            String businessName,
            String servicesType,
            OBAnonymousParams anonymousParams) {
        businessId = toBusinessId(businessId, businessName, anonymousParams);
        return moeBusinessAgreementService.queryBusinessOBAgreementInfoList(
                businessId, servicesType, AuthContext.get().getCustomerId());
    }

    private Integer toBusinessId(Integer businessId, String businessName, OBAnonymousParams anonymousParams) {
        if (businessId != null) {
            return businessId;
        }
        if ((Objects.nonNull(anonymousParams.getName()) || Objects.nonNull(anonymousParams.getDomain()))
                && Objects.nonNull(businessId = onlineBookingClient.getBusinessIdByOBNameOrDomain(anonymousParams))) {
            return businessId;
        }
        if (StringUtils.hasText(businessName)) {
            return businessService.getBusinessId(businessName);
        }
        throw ExceptionUtil.bizException(Code.CODE_BOOK_ONLINE_SITE_NOT_FOUND);
    }
}
