package com.moego.server.business.service;

import com.moego.common.enums.ResponseCodeEnum;
import com.moego.common.exception.CommonException;
import com.moego.idl.models.business_customer.v1.BusinessPetSizeUpsertDef;
import com.moego.idl.service.business_customer.v1.BatchUpsertPetSizeRequest;
import com.moego.idl.service.business_customer.v1.BusinessPetSizeServiceGrpc;
import com.moego.idl.service.business_customer.v1.ListPetSizeRequest;
import com.moego.server.business.dto.PetSizeDTO;
import com.moego.server.grooming.client.IBookOnlinePetLimitClient;
import com.moego.server.grooming.dto.CompanyBusinessIdDTO;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class PetSizeService {

    @Autowired
    private IBookOnlinePetLimitClient iBookOnlinePetLimitClient;

    @Autowired
    private BusinessPetSizeServiceGrpc.BusinessPetSizeServiceBlockingStub petSizeServiceBlockingStub;

    public List<PetSizeDTO> getPetSize(Long companyId, Integer businessId) {
        var request = ListPetSizeRequest.newBuilder()
                .setCompanyId(companyId)
                .setBusinessId(businessId)
                .build();
        var response = petSizeServiceBlockingStub.listPetSize(request);
        return response.getSizesList().stream()
                .map(petSize -> {
                    PetSizeDTO petSizeDTO = new PetSizeDTO();
                    petSizeDTO.setId(petSize.getId());
                    petSizeDTO.setName(petSize.getName());
                    petSizeDTO.setWeightLow(petSize.getWeightLow());
                    petSizeDTO.setWeightHigh(petSize.getWeightHigh());
                    petSizeDTO.setBusinessId(businessId);
                    return petSizeDTO;
                })
                .toList();
    }

    @Transactional(rollbackFor = Exception.class)
    public Integer savePetSize(Long companyId, Integer businessId, List<PetSizeDTO> petSizeDTOList) {
        // id list to be updated
        Map<Long, PetSizeDTO> petSizeDTOMap = petSizeDTOList.stream()
                .filter(petSizeDTO -> Objects.nonNull(petSizeDTO.getId()))
                .collect(Collectors.toMap(PetSizeDTO::getId, Function.identity()));

        List<Long> inUsedPetSizeIdList =
                iBookOnlinePetLimitClient.getInUsedPetSizeIdListV2(new CompanyBusinessIdDTO(companyId, businessId));
        var originPetSizeList = getPetSize(companyId, businessId);
        var petSizeToDeleteList = originPetSizeList.stream()
                .filter(petSize -> !petSizeDTOMap.containsKey(petSize.getId()))
                .toList();

        // check if deleted pet size is in used
        var deletedButInUsedPetSize =
                petSizeToDeleteList.stream().anyMatch(petSize -> inUsedPetSizeIdList.contains(petSize.getId()));
        if (deletedButInUsedPetSize) {
            throw new CommonException(ResponseCodeEnum.PET_SIZE_IN_USED);
        }

        savePetSizeGrpc(companyId, businessId, petSizeDTOList);
        return 0;
    }

    private void savePetSizeGrpc(Long companyId, Integer businessId, List<PetSizeDTO> petSizeDTOList) {

        var petSizeToCreateList = petSizeDTOList.stream()
                .filter(petSizeDTO -> Objects.isNull(petSizeDTO.getId()))
                .map(petSizeDTO -> BusinessPetSizeUpsertDef.newBuilder()
                        .setName(petSizeDTO.getName())
                        .setWeightLow(petSizeDTO.getWeightLow())
                        .setWeightHigh(petSizeDTO.getWeightHigh())
                        .build())
                .toList();

        var petSizeToUpdateMap = petSizeDTOList.stream()
                .filter(petSizeDTO -> Objects.nonNull(petSizeDTO.getId()))
                .collect(Collectors.toMap(PetSizeDTO::getId, petSizeDTO -> BusinessPetSizeUpsertDef.newBuilder()
                        .setName(petSizeDTO.getName())
                        .setWeightLow(petSizeDTO.getWeightLow())
                        .setWeightHigh(petSizeDTO.getWeightHigh())
                        .build()));

        var request = BatchUpsertPetSizeRequest.newBuilder()
                .setCompanyId(companyId)
                .setBusinessId(businessId)
                .addAllSizesToCreate(petSizeToCreateList)
                .putAllSizesToUpdate(petSizeToUpdateMap)
                .build();

        petSizeServiceBlockingStub.batchUpsertPetSize(request);
    }
}
