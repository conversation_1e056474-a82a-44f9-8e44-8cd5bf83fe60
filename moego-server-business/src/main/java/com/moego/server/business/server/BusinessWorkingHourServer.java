package com.moego.server.business.server;

import com.moego.server.business.api.IBusinessWorkingHourServiceBase;
import com.moego.server.business.dto.BusinessWorkingHourDetailDTO;
import com.moego.server.business.service.BusinessWorkingHourService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

@RestController
public class BusinessWorkingHourServer extends IBusinessWorkingHourServiceBase {

    @Autowired
    private BusinessWorkingHourService workingHourService;

    @Override
    public BusinessWorkingHourDetailDTO getBusinessWorkingHour(Integer businessId) {
        return workingHourService.getBusinessWorkingHourDetail(businessId);
    }

    @Override
    public Boolean updateBusinessWorkingHour(BusinessWorkingHourDetailDTO workingHourDetailDTO) {
        workingHourService.saveBusinessWorkingHour(workingHourDetailDTO);
        return Boolean.TRUE;
    }
}
