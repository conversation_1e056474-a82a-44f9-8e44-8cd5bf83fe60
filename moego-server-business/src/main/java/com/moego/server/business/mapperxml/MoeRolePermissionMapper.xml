<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.moego.server.business.mapper.MoeRolePermissionMapper">
  <resultMap id="BaseResultMap" type="com.moego.server.business.mapperbean.MoeRolePermission">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="role_id" jdbcType="INTEGER" property="roleId" />
    <result column="permission_id" jdbcType="INTEGER" property="permissionId" />
  </resultMap>
  <sql id="Base_Column_List">
    id, role_id, permission_id
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from moe_role_permission
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from moe_role_permission
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.moego.server.business.mapperbean.MoeRolePermission">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into moe_role_permission (role_id, permission_id)
    values (#{roleId,jdbcType=INTEGER}, #{permissionId,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.moego.server.business.mapperbean.MoeRolePermission">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into moe_role_permission
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="roleId != null">
        role_id,
      </if>
      <if test="permissionId != null">
        permission_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="roleId != null">
        #{roleId,jdbcType=INTEGER},
      </if>
      <if test="permissionId != null">
        #{permissionId,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.moego.server.business.mapperbean.MoeRolePermission">
    update moe_role_permission
    <set>
      <if test="roleId != null">
        role_id = #{roleId,jdbcType=INTEGER},
      </if>
      <if test="permissionId != null">
        permission_id = #{permissionId,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.moego.server.business.mapperbean.MoeRolePermission">
    update moe_role_permission
    set role_id = #{roleId,jdbcType=INTEGER},
      permission_id = #{permissionId,jdbcType=INTEGER}
    where id = #{id,jdbcType=INTEGER}
  </update>

  <insert id="insertBatch" parameterType="com.moego.server.business.mapperbean.MoeRolePermission">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into moe_role_permission (role_id, permission_id)
    values
    <foreach collection="moeRolePermissionList" separator="," item="moeRolePermission">
      (
      #{moeRolePermission.roleId,jdbcType=INTEGER},
      #{moeRolePermission.permissionId,jdbcType=INTEGER}
      )
    </foreach>


  </insert>

  <delete id="deleteByRoleId" parameterType="java.lang.Integer">
    delete from moe_role_permission
    where role_id = #{roleId,jdbcType=INTEGER}
  </delete>



</mapper>