package com.moego.server.business.mapperbean;

import java.util.Date;

/**
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table moe_business_email
 */
public class MoeBusinessEmail {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_email.id
     *
     * @mbg.generated
     */
    private Long id;

    /**
     * Database Column Remarks:
     *   business id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_email.business_id
     *
     * @mbg.generated
     */
    private Integer businessId;

    /**
     * Database Column Remarks:
     *   notification email(online booking, online invoice, daily appointment)
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_email.notification_email
     *
     * @mbg.generated
     */
    private String notificationEmail;

    /**
     * Database Column Remarks:
     *   create time
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_email.create_time
     *
     * @mbg.generated
     */
    private Date createTime;

    /**
     * Database Column Remarks:
     *   update time
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_email.update_time
     *
     * @mbg.generated
     */
    private Date updateTime;

    /**
     * Database Column Remarks:
     *   company_id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_email.company_id
     *
     * @mbg.generated
     */
    private Long companyId;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_email.id
     *
     * @return the value of moe_business_email.id
     *
     * @mbg.generated
     */
    public Long getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_email.id
     *
     * @param id the value for moe_business_email.id
     *
     * @mbg.generated
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_email.business_id
     *
     * @return the value of moe_business_email.business_id
     *
     * @mbg.generated
     */
    public Integer getBusinessId() {
        return businessId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_email.business_id
     *
     * @param businessId the value for moe_business_email.business_id
     *
     * @mbg.generated
     */
    public void setBusinessId(Integer businessId) {
        this.businessId = businessId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_email.notification_email
     *
     * @return the value of moe_business_email.notification_email
     *
     * @mbg.generated
     */
    public String getNotificationEmail() {
        return notificationEmail;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_email.notification_email
     *
     * @param notificationEmail the value for moe_business_email.notification_email
     *
     * @mbg.generated
     */
    public void setNotificationEmail(String notificationEmail) {
        this.notificationEmail = notificationEmail == null ? null : notificationEmail.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_email.create_time
     *
     * @return the value of moe_business_email.create_time
     *
     * @mbg.generated
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_email.create_time
     *
     * @param createTime the value for moe_business_email.create_time
     *
     * @mbg.generated
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_email.update_time
     *
     * @return the value of moe_business_email.update_time
     *
     * @mbg.generated
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_email.update_time
     *
     * @param updateTime the value for moe_business_email.update_time
     *
     * @mbg.generated
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_email.company_id
     *
     * @return the value of moe_business_email.company_id
     *
     * @mbg.generated
     */
    public Long getCompanyId() {
        return companyId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_email.company_id
     *
     * @param companyId the value for moe_business_email.company_id
     *
     * @mbg.generated
     */
    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }
}
