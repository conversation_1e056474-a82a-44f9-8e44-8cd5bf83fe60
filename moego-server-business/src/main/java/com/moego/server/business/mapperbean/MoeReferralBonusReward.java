package com.moego.server.business.mapperbean;

public class MoeReferralBonusReward {

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_referral_bonus_reward.id
     *
     * @mbg.generated
     */
    private Integer id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_referral_bonus_reward.referral_id
     *
     * @mbg.generated
     */
    private Integer referralId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_referral_bonus_reward.bonus_rule_id
     *
     * @mbg.generated
     */
    private Integer bonusRuleId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_referral_bonus_reward.status
     *
     * @mbg.generated
     */
    private Byte status;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_referral_bonus_reward.create_time
     *
     * @mbg.generated
     */
    private Long createTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_referral_bonus_reward.update_time
     *
     * @mbg.generated
     */
    private Long updateTime;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_referral_bonus_reward.id
     *
     * @return the value of moe_referral_bonus_reward.id
     *
     * @mbg.generated
     */
    public Integer getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_referral_bonus_reward.id
     *
     * @param id the value for moe_referral_bonus_reward.id
     *
     * @mbg.generated
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_referral_bonus_reward.referral_id
     *
     * @return the value of moe_referral_bonus_reward.referral_id
     *
     * @mbg.generated
     */
    public Integer getReferralId() {
        return referralId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_referral_bonus_reward.referral_id
     *
     * @param referralId the value for moe_referral_bonus_reward.referral_id
     *
     * @mbg.generated
     */
    public void setReferralId(Integer referralId) {
        this.referralId = referralId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_referral_bonus_reward.bonus_rule_id
     *
     * @return the value of moe_referral_bonus_reward.bonus_rule_id
     *
     * @mbg.generated
     */
    public Integer getBonusRuleId() {
        return bonusRuleId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_referral_bonus_reward.bonus_rule_id
     *
     * @param bonusRuleId the value for moe_referral_bonus_reward.bonus_rule_id
     *
     * @mbg.generated
     */
    public void setBonusRuleId(Integer bonusRuleId) {
        this.bonusRuleId = bonusRuleId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_referral_bonus_reward.status
     *
     * @return the value of moe_referral_bonus_reward.status
     *
     * @mbg.generated
     */
    public Byte getStatus() {
        return status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_referral_bonus_reward.status
     *
     * @param status the value for moe_referral_bonus_reward.status
     *
     * @mbg.generated
     */
    public void setStatus(Byte status) {
        this.status = status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_referral_bonus_reward.create_time
     *
     * @return the value of moe_referral_bonus_reward.create_time
     *
     * @mbg.generated
     */
    public Long getCreateTime() {
        return createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_referral_bonus_reward.create_time
     *
     * @param createTime the value for moe_referral_bonus_reward.create_time
     *
     * @mbg.generated
     */
    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_referral_bonus_reward.update_time
     *
     * @return the value of moe_referral_bonus_reward.update_time
     *
     * @mbg.generated
     */
    public Long getUpdateTime() {
        return updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_referral_bonus_reward.update_time
     *
     * @param updateTime the value for moe_referral_bonus_reward.update_time
     *
     * @mbg.generated
     */
    public void setUpdateTime(Long updateTime) {
        this.updateTime = updateTime;
    }
}
