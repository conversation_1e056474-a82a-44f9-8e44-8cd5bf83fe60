package com.moego.server.business.service;

import com.moego.common.enums.LinkAccountConst;
import com.moego.idl.models.account.v1.AccountModel;
import com.moego.idl.models.account.v1.AccountStatus;
import com.moego.server.business.mapper.MoeBusinessMapper;
import com.moego.server.business.mapper.MoeCompanyMapper;
import com.moego.server.business.mapper.MoeRelevantAccountMapper;
import com.moego.server.business.mapper.MoeRelevantCompanyMapper;
import com.moego.server.business.mapper.MoeStaffMapper;
import com.moego.server.business.mapperbean.MoeBusiness;
import com.moego.server.business.mapperbean.MoeCompany;
import com.moego.server.business.mapperbean.MoeRelevantAccount;
import com.moego.server.business.mapperbean.MoeRelevantCompany;
import com.moego.server.business.mapperbean.MoeRelevantCompanyExample;
import com.moego.server.business.mapperbean.MoeStaff;
import com.moego.server.business.service.dto.BusinessInfoWithAccountDto;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class RelevantAccountService {

    @Autowired
    private MoeRelevantAccountMapper relevantAccountMapper;

    @Autowired
    private MoeCompanyMapper companyMapper;

    @Autowired
    private MoeBusinessMapper businessMapper;

    @Autowired
    private MoeStaffMapper staffMapper;

    @Autowired
    private AccountService accountService;

    @Autowired
    private MoeRelevantCompanyMapper relevantCompanyMapper;

    private static final Byte OB_BOOK_NOW_TRUE = (byte) 1;

    public List<BusinessInfoWithAccountDto> getAccountRelevantInfo(Integer accountId) {
        MoeRelevantAccount relevantAccount = relevantAccountMapper.selectByAccountId(accountId);
        if (relevantAccount == null || relevantAccount.getGroupId() == null) {
            return Collections.emptyList();
        } else {
            if (LinkAccountConst.ACCESS_LEVEL_ONLY_ME.equals(relevantAccount.getAccessLevel())) {
                return Collections.emptyList();
            }
            List<MoeRelevantAccount> relevantAccountList = relevantAccountMapper.selectByGroupIdAndAccessLevel(
                    relevantAccount.getGroupId(), relevantAccount.getAccessLevel());
            if (CollectionUtils.isEmpty(relevantAccountList)) {
                return Collections.emptyList();
            }
            List<Integer> accountIdList = new ArrayList<>();
            for (MoeRelevantAccount moeRelevantAccount : relevantAccountList) {
                accountIdList.add(moeRelevantAccount.getAccountId());
            }
            List<BusinessInfoWithAccountDto> businessInfoDtoList = new ArrayList<>();
            Map<Integer, MoeStaff> accountIdStaffMap = accountService.getLastLoginStaffByMultiAccount(accountIdList);
            for (Integer relevantAccountId : accountIdList) {
                if (accountId.equals(relevantAccountId)) {
                    continue;
                }

                AccountModel accountModel = accountService.getAccountById(relevantAccountId);
                if (accountModel == null || AccountStatus.ACCOUNT_STATUS_DELETED.equals(accountModel.getStatus())) {
                    continue;
                }

                BusinessInfoWithAccountDto businessInfoDto = new BusinessInfoWithAccountDto();
                businessInfoDto.setAccountId(relevantAccountId);
                businessInfoDto.setAccountEmail(accountModel.getEmail());
                businessInfoDto.setAccountFirstName(accountModel.getFirstName());
                businessInfoDto.setAccountLastName(accountModel.getLastName());

                MoeStaff staff = accountIdStaffMap.get(relevantAccountId);
                if (staff != null) {
                    businessInfoDto.setCompanyId(staff.getCompanyId());
                    businessInfoDto.setStaffToken(staff.getToken());
                    businessInfoDto.setStaffId(staff.getId());
                    businessInfoDto.setEmployeeCategory(staff.getEmployeeCategory());
                    businessInfoDto.setBusinessId(staff.getBusinessId());
                    businessInfoDto.setCompanyId(staff.getCompanyId());
                    businessInfoDto.setAvatarPath(staff.getAvatarPath());
                }
                businessInfoDtoList.add(businessInfoDto);
            }
            return businessInfoDtoList;
        }
    }

    public List<Integer> getRelevantAccountIds(Integer accountId) {
        MoeRelevantAccount relevantAccount = relevantAccountMapper.selectByAccountId(accountId);
        if (relevantAccount == null || relevantAccount.getGroupId() == null) {
            return Collections.emptyList();
        } else {
            if (LinkAccountConst.ACCESS_LEVEL_ONLY_ME.equals(relevantAccount.getAccessLevel())) {
                return Collections.emptyList();
            }
            List<MoeRelevantAccount> relevantAccountList = relevantAccountMapper.selectByGroupIdAndAccessLevel(
                    relevantAccount.getGroupId(), relevantAccount.getAccessLevel());
            if (CollectionUtils.isEmpty(relevantAccountList)) {
                return Collections.emptyList();
            }
            return relevantAccountList.stream()
                    .map(MoeRelevantAccount::getAccountId)
                    .distinct()
                    .collect(Collectors.toList());
        }
    }

    public List<Integer> getAllRelevantBusinessIdsForOb(Integer businessId) {
        MoeBusiness business = businessMapper.selectByPrimaryKey(businessId);
        if (business == null) {
            return Collections.emptyList();
        }
        MoeCompany company = companyMapper.selectByPrimaryKey(business.getCompanyId());
        if (company == null) {
            return Collections.emptyList();
        }
        MoeRelevantAccount relevantAccount = relevantAccountMapper.selectByAccountId(company.getAccountId());
        Set<Integer> businessIds = new HashSet<>();
        if (relevantAccount != null
                && relevantAccount.getGroupId() != null
                && OB_BOOK_NOW_TRUE.equals(relevantAccount.getObBookNow())) {
            List<MoeRelevantAccount> relevantAccountList =
                    relevantAccountMapper.selectByGroupIdForOb(relevantAccount.getGroupId());
            if (!CollectionUtils.isEmpty(relevantAccountList)) {
                List<Integer> accountIdList = new ArrayList<>();
                for (MoeRelevantAccount moeRelevantAccount : relevantAccountList) {
                    accountIdList.add(moeRelevantAccount.getAccountId());
                }
                List<MoeStaff> staffList = staffMapper.selectOwnerStaffByAccountIds(accountIdList);
                List<Integer> cidList = new ArrayList<>();
                for (MoeStaff staff : staffList) {
                    if (!staff.getBusinessId().equals(0)) {
                        // AS 迁移前
                        businessIds.add(staff.getBusinessId());
                    } else {
                        // AS 迁移后
                        cidList.add(staff.getCompanyId());
                    }
                }
                // AS 迁移后
                if (!CollectionUtils.isEmpty(cidList)) {
                    businessMapper
                            .selectAllBusinessByCompanyId(cidList)
                            .forEach(business1 -> businessIds.add(business1.getId()));
                }
            }
        }
        // AS 拆分 company 后，需要 link 多个 company 的 ob
        MoeRelevantCompanyExample example = new MoeRelevantCompanyExample();
        example.createCriteria().andCompanyIdEqualTo(company.getId().longValue());
        MoeRelevantCompany relevantCompany = relevantCompanyMapper.selectByExample(example).stream()
                .findFirst()
                .orElse(null);
        if (relevantCompany != null && relevantCompany.getGroupId() != null) {
            MoeRelevantCompanyExample example2 = new MoeRelevantCompanyExample();
            example2.createCriteria().andGroupIdEqualTo(relevantCompany.getGroupId());
            List<MoeRelevantCompany> relevantCompanyList = relevantCompanyMapper.selectByExample(example2);
            if (!CollectionUtils.isEmpty(relevantCompanyList)) {
                List<Integer> companyIds = relevantCompanyList.stream()
                        .map(MoeRelevantCompany::getCompanyId)
                        .map(Long::intValue)
                        .toList();
                businessMapper
                        .selectAllBusinessByCompanyId(companyIds)
                        .forEach(business1 -> businessIds.add(business1.getId()));
            }
        }
        return businessIds.stream().toList();
    }

    public Integer getFranchisorAccountId(Integer businessId) {
        MoeCompany company = companyMapper.selectByBusinessId(businessId);
        if (Objects.isNull(company)) {
            return null;
        }
        MoeRelevantAccount relevantAccount = relevantAccountMapper.selectByAccountId(company.getAccountId());
        // franchise account 必须为单向 link account
        if (Objects.isNull(relevantAccount)
                || !Objects.equals(relevantAccount.getAccessLevel(), LinkAccountConst.ACCESS_LEVEL_ONLY_ME)) {
            return null;
        }
        List<MoeRelevantAccount> relevantAccountList =
                relevantAccountMapper.selectByGroupIdForOb(relevantAccount.getGroupId());
        if (CollectionUtils.isEmpty(relevantAccountList)) {
            return null;
        }
        // 获取 franchisor account id
        return relevantAccountList.stream()
                .filter(a -> !Objects.equals(a.getAccessLevel(), LinkAccountConst.ACCESS_LEVEL_ONLY_ME))
                .findFirst()
                .map(MoeRelevantAccount::getAccountId)
                .orElse(null);
    }
}
