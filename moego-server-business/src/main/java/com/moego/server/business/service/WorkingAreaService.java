package com.moego.server.business.service;

import static com.moego.common.enums.serviceAreaConst.ANY_AREA;

import com.moego.common.utils.DateUtil;
import com.moego.idl.models.errors.v1.Code;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.lib.common.migrate.MigrateHelper;
import com.moego.lib.common.migrate.MigrateInfo;
import com.moego.server.business.dto.StaffOverrideAreaDetailDTO;
import com.moego.server.business.dto.StaffWorkingAreaDetailDTO;
import com.moego.server.business.dto.StaffWorkingAreaRangeDto;
import com.moego.server.business.dto.StaffWorkingHourDetailDTO;
import com.moego.server.business.dto.WorkingAreaDto;
import com.moego.server.business.mapper.MoeGeoareaMapper;
import com.moego.server.business.mapper.MoeStaffServiceAreaMapper;
import com.moego.server.business.mapperbean.MoeGeoarea;
import com.moego.server.business.mapperbean.MoeStaff;
import com.moego.server.business.mapperbean.MoeStaffServiceArea;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

@Service
@Slf4j
public class WorkingAreaService {

    @Autowired
    private WorkingDailyService workingDailyService;

    @Autowired
    private StaffWorkingAreaService staffWorkingAreaService;

    @Autowired
    private StaffWorkingHourService staffWorkingHourService;

    @Autowired
    private StaffOverrideAreaService staffOverrideAreaService;

    @Autowired
    private StaffService staffService;

    @Autowired
    private MoeGeoareaMapper areaMapper;

    @Autowired
    private MoeStaffServiceAreaMapper moeStaffServiceAreaMapper;

    @Autowired
    private MigrateHelper migrateHelper;

    public List<StaffWorkingAreaRangeDto> queryRangeShiftManagementWorkArea(
            int businessId, String startDateRequest, String endDateRequest) {
        // 参数检查
        if (StringUtils.isEmpty(endDateRequest) || StringUtils.isEmpty(startDateRequest)) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "date is null or empty");
        }

        // 查询状态正常的员工列表
        MigrateInfo migrateInfo = migrateHelper.getMigrationInfo(businessId);
        List<Integer> staffIdList = staffService.getStaffListByBusinessId(businessId, false, migrateInfo).stream()
                .map(MoeStaff::getId)
                .toList();
        if (CollectionUtils.isEmpty(staffIdList)) {
            return Collections.emptyList();
        }

        return queryStaffWorkAreaByRange(businessId, staffIdList, startDateRequest, endDateRequest, true);
    }

    public List<Integer> getAreaIdsFromWorkingArea(List<StaffWorkingAreaRangeDto> staffWorkingAreaRangeDtos) {
        if (CollectionUtils.isEmpty(staffWorkingAreaRangeDtos)) {
            return Collections.emptyList();
        }
        return staffWorkingAreaRangeDtos.stream()
                .map(k -> k.getWorkingAreaRange().entrySet())
                .flatMap(Collection::stream)
                .map(Map.Entry::getValue)
                .flatMap(Collection::stream)
                .map(WorkingAreaDto::getAreaId)
                .distinct()
                .toList();
    }

    public List<WorkingAreaDto> getWorkingAreas(List<StaffWorkingAreaRangeDto> staffWorkingAreaRangeDtos) {
        if (CollectionUtils.isEmpty(staffWorkingAreaRangeDtos)) {
            return Collections.emptyList();
        }
        return staffWorkingAreaRangeDtos.stream()
                .map(k -> k.getWorkingAreaRange().entrySet())
                .flatMap(Collection::stream)
                .map(Map.Entry::getValue)
                .flatMap(Collection::stream)
                .toList();
    }

    public void areaIdDeleteCheck(Integer businessId, List<WorkingAreaDto> workingAreaDtos) {
        if (CollectionUtils.isEmpty(workingAreaDtos)) {
            return;
        }
        List<Integer> areaIds = workingAreaDtos.stream()
                .map(WorkingAreaDto::getAreaId)
                .filter(k -> !k.equals(Integer.valueOf(ANY_AREA)))
                .distinct()
                .toList();
        if (CollectionUtils.isEmpty(areaIds)) {
            return;
        }
        Map<Integer, MoeGeoarea> areaMap = areaMapper.queryAreasNotDelete(businessId, areaIds, null).stream()
                .collect(Collectors.toMap(MoeGeoarea::getId, Function.identity()));
        for (WorkingAreaDto workingArea : workingAreaDtos) {
            if (!areaMap.containsKey(workingArea.getAreaId())) {
                workingArea.setAreaId(Integer.valueOf(ANY_AREA));
            }
        }
    }

    public List<StaffWorkingAreaRangeDto> queryStaffWorkAreaByRange(
            int businessId,
            List<Integer> staffIdList,
            String startDate,
            String endDate,
            boolean withDefaultWorkingArea) {
        if (CollectionUtils.isEmpty(staffIdList)) {
            return Collections.emptyList();
        }

        startDate = DateUtil.convertToDateString(startDate);
        endDate = DateUtil.convertToDateString(endDate);

        // 日期转换为一段时间
        List<LocalDate> localDateRange =
                workingDailyService.getLocalDateRange(LocalDate.parse(startDate), LocalDate.parse(endDate));

        // 获取 override 设置
        Map<Pair<Integer, LocalDate>, List<WorkingAreaDto>> staffOverrideDetailMap =
                staffOverrideAreaService.getStaffOverrideWorkingAreaMap(businessId, staffIdList, startDate, endDate);

        // 获取 working area 设置
        Map<Integer, Map<LocalDate, List<WorkingAreaDto>>> staffWorkingArea =
                staffWorkingAreaService.getStaffWorkingArea(businessId, staffIdList, localDateRange);

        // 开始计算&组装返回值
        return staffIdList.stream()
                .map(staffId -> getStaffWorkingArea(
                        staffId, localDateRange, staffOverrideDetailMap, staffWorkingArea, withDefaultWorkingArea))
                .toList();
    }

    private static StaffWorkingAreaRangeDto getStaffWorkingArea(
            Integer staffId,
            List<LocalDate> localDateRange,
            Map<Pair<Integer, LocalDate>, List<WorkingAreaDto>> staffOverrideDetailMap,
            Map<Integer, Map<LocalDate, List<WorkingAreaDto>>> staffWorkingArea,
            boolean withDefaultWorkingArea) {
        // 组装staff数据
        StaffWorkingAreaRangeDto staffRangeDto = new StaffWorkingAreaRangeDto();
        staffRangeDto.setStaffId(staffId);
        // 组装所有的area
        Map<String, List<WorkingAreaDto>> areaRangeMap = new HashMap<>(32);
        localDateRange.forEach(localDate -> {
            String dateStr = localDate.toString();
            if (withDefaultWorkingArea) {
                // 使用 默认设置
                areaRangeMap.put(dateStr, List.of(new WorkingAreaDto(Integer.valueOf(ANY_AREA))));
            }
            if (staffWorkingArea != null
                    && staffWorkingArea.containsKey(staffId)
                    && staffWorkingArea.get(staffId).containsKey(localDate)) {
                // 使用 working area 覆盖
                areaRangeMap.put(dateStr, staffWorkingArea.get(staffId).get(localDate));
            }
            if (staffOverrideDetailMap != null && staffOverrideDetailMap.containsKey(Pair.of(staffId, localDate))) {
                // 使用 override localDate 覆盖
                areaRangeMap.put(dateStr, staffOverrideDetailMap.get(Pair.of(staffId, localDate)));
            }
        });
        staffRangeDto.setWorkingAreaRange(areaRangeMap);
        return staffRangeDto;
    }

    public void transToShiftManagement(Integer companyId, Integer businessId) {
        MigrateInfo migrateInfo = migrateHelper.getMigrationInfo(businessId);
        List<Integer> staffIdList = staffService.getStaffListByBusinessId(businessId, false, migrateInfo).stream()
                .map(MoeStaff::getId)
                .toList();
        if (CollectionUtils.isEmpty(staffIdList)) {
            return;
        }

        // 转换时，参考 working hour 的 scheduleType、startDate、endDate 信息
        Map<Integer, StaffWorkingHourDetailDTO> staffWorkingHourDetails =
                staffWorkingHourService.getStaffWorkingHourDetail(businessId, staffIdList);

        // 获取数据切换的开始时间，本月第一天
        LocalDate startDate = DateUtil.getCurrentMonthStart(DateUtil.convertLocalDateToDate(LocalDate.now()))
                .toInstant()
                .atZone(ZoneId.systemDefault())
                .toLocalDate();

        // 拉取绝对开始时间之后的所有 moeStaffServiceArea 信息
        // key: staffId, value: List<StaffDateServiceArea>
        Map<Integer, List<MoeStaffServiceArea>> staffAreas =
                moeStaffServiceAreaMapper
                        .queryByBusinessNotDelete(businessId, null, startDate.toString(), null)
                        .stream()
                        .collect(Collectors.groupingBy(MoeStaffServiceArea::getStaffId, Collectors.toList()));

        List<StaffOverrideAreaDetailDTO> staffOverrideAreaDetailDTOS = new ArrayList<>();
        List<StaffWorkingAreaDetailDTO> staffWorkingAreaDetailDTOS = new ArrayList<>();
        staffIdList.forEach(staffId -> {
            Pair<StaffWorkingAreaDetailDTO, List<StaffOverrideAreaDetailDTO>> pair = transPerStaffToShiftManagement(
                    staffWorkingHourDetails.get(staffId), staffAreas.get(staffId), startDate);
            if (pair == null) {
                return;
            }
            if (pair.getLeft() != null) {
                staffWorkingAreaDetailDTOS.add(pair.getLeft());
            }
            if (!CollectionUtils.isEmpty(pair.getRight())) {
                staffOverrideAreaDetailDTOS.addAll(pair.getRight());
            }
        });
        staffWorkingAreaService.saveStaffWorkingArea(companyId, businessId, staffWorkingAreaDetailDTOS);
        staffOverrideAreaService.saveStaffOverrideArea(companyId, businessId, staffOverrideAreaDetailDTOS);
    }

    public Pair<StaffWorkingAreaDetailDTO, List<StaffOverrideAreaDetailDTO>> transPerStaffToShiftManagement(
            StaffWorkingHourDetailDTO staffWorkingHourDetail,
            List<MoeStaffServiceArea> staffDateAreas,
            LocalDate absoluteStartDate) {
        if (CollectionUtils.isEmpty(staffDateAreas)) {
            return null;
        }

        // 根据 staffDateAreas 计算待切换数据的最晚日期
        // 如果 StaffDateServiceArea 最晚设置日期 或 workingHour endDate 距离现在不足一个月，则全转为 override
        LocalDate latestStaffDate = staffDateAreas.stream()
                .map(staffArea -> LocalDate.parse(staffArea.getDate()))
                .max(LocalDate::compareTo)
                .orElse(absoluteStartDate);

        // 计算绝对结束时间
        LocalDate absoluteEndDate = absoluteStartDate.plusMonths(1);
        if (latestStaffDate.isBefore(absoluteEndDate)
                || staffWorkingHourDetail.getEndDate().isBefore(absoluteEndDate)) {
            return Pair.of(
                    null,
                    staffDateAreas.stream()
                            .map(this::getStaffOverrideAreaDetailDTO)
                            .toList());
        }
        absoluteEndDate = latestStaffDate;

        // 超过绝对结束时间的都转为 override
        LocalDate finalAbsoluteEndDate = absoluteEndDate;
        List<StaffOverrideAreaDetailDTO> staffOverrideAreaDetailDTOS = new ArrayList<>(staffDateAreas.stream()
                .filter(staffArea -> LocalDate.parse(staffArea.getDate()).isAfter(finalAbsoluteEndDate))
                .map(this::getStaffOverrideAreaDetailDTO)
                .toList());

        // key: 一个周期中的第 x 天, value: areaId
        Map<Integer, Integer> rotatingArea = new HashMap<>();

        // 计算两个周期之间的间隔 interval
        Byte frequency = staffWorkingHourDetail.getScheduleType();
        // 异常数据，全转为 override area
        if (frequency < 1 || frequency > 4) {
            return Pair.of(
                    null,
                    staffDateAreas.stream()
                            .map(this::getStaffOverrideAreaDetailDTO)
                            .toList());
        }
        int interval = 7 * frequency;

        // key: date, value: StaffDateServiceArea
        Map<String, MoeStaffServiceArea> staffDateAreaMap = staffDateAreas.stream()
                .collect(Collectors.toMap(MoeStaffServiceArea::getDate, Function.identity(), (k1, k2) -> k2));

        // 绝对开始日期到绝对结束日期这段时间内，每隔 interval 天，为一个周期
        // 统计周期中每一天的最多的 areaId，作为该周期的 areaId，若有多个则随机选一个
        for (int i = 0; i < interval; i++) {
            // 按 areaId 进行统计
            Map<Integer, List<MoeStaffServiceArea>> areaIdCollect = new HashMap<>();
            for (LocalDate cur = absoluteStartDate.plusDays(i);
                    !cur.isAfter(absoluteEndDate);
                    cur = cur.plusDays(interval)) {
                if (staffDateAreaMap.containsKey(cur.toString())) {
                    MoeStaffServiceArea staffDateServiceArea = staffDateAreaMap.get(cur.toString());
                    if (!areaIdCollect.containsKey(staffDateServiceArea.getAreaId())) {
                        areaIdCollect.put(staffDateServiceArea.getAreaId(), new ArrayList<>());
                    }
                    areaIdCollect.get(staffDateServiceArea.getAreaId()).add(staffDateServiceArea);
                } else {
                    // 没有设置的日期，设置为 ANY_AREA
                    if (!areaIdCollect.containsKey(Integer.valueOf(ANY_AREA))) {
                        areaIdCollect.put(Integer.valueOf(ANY_AREA), new ArrayList<>());
                    }
                    LocalDate finalCur = cur;
                    areaIdCollect.get(Integer.valueOf(ANY_AREA)).add(new MoeStaffServiceArea() {
                        {
                            setBusinessId(staffWorkingHourDetail.getBusinessId());
                            setStaffId(staffWorkingHourDetail.getStaffId());
                            setDate(finalCur.toString());
                            setAreaId(Integer.valueOf(ANY_AREA));
                        }
                    });
                }
            }

            Map.Entry<Integer, List<MoeStaffServiceArea>> maxCntArea = areaIdCollect.entrySet().stream()
                    .max(Comparator.comparingInt(a -> a.getValue().size()))
                    .orElse(null);
            // 若各周期中没有重复设置的 areaId，则都转为 override
            if (maxCntArea == null || maxCntArea.getValue().size() < 2) {
                staffOverrideAreaDetailDTOS.addAll(areaIdCollect.values().stream()
                        .flatMap(List::stream)
                        .map(this::getStaffOverrideAreaDetailDTO)
                        .toList());
                continue;
            }
            // 记录为周期中第 i 天的 areaId
            rotatingArea.put(i, maxCntArea.getKey());
            // 其它的设置为 override
            staffOverrideAreaDetailDTOS.addAll(areaIdCollect.entrySet().stream()
                    .filter(k -> !k.getKey().equals(maxCntArea.getKey()))
                    .flatMap(k -> k.getValue().stream())
                    .map(this::getStaffOverrideAreaDetailDTO)
                    .toList());
        }

        // 没有周期性的设置，全转为 override
        if (rotatingArea.isEmpty()) {
            return Pair.of(null, staffOverrideAreaDetailDTOS);
        }

        // 构造 StaffWorkingAreaDetailDTO
        StaffWorkingAreaDetailDTO staffWorkingAreaDetailDTO =
                StaffWorkingAreaService.getDefaultStaffWorkingAreaDetailDTO(
                        staffWorkingHourDetail.getBusinessId(), staffWorkingHourDetail.getStaffId());
        staffWorkingAreaDetailDTO.setScheduleType(staffWorkingHourDetail.getScheduleType());
        staffWorkingAreaDetailDTO.setStartDate(staffWorkingHourDetail.getStartDate());
        staffWorkingAreaDetailDTO.setEndDate(staffWorkingHourDetail.getEndDate());

        for (var entry : rotatingArea.entrySet()) {
            List<WorkingAreaDto> workingAreaDtos = StaffWorkingAreaService.getStaffWorkingAreaByCurrentDate(
                    staffWorkingAreaDetailDTO, absoluteStartDate.plusDays(entry.getKey()));
            workingAreaDtos.get(0).setAreaId(entry.getValue());
        }
        return Pair.of(staffWorkingAreaDetailDTO, staffOverrideAreaDetailDTOS);
    }

    private StaffOverrideAreaDetailDTO getStaffOverrideAreaDetailDTO(MoeStaffServiceArea staffArea) {
        StaffOverrideAreaDetailDTO staffOverrideAreaDetailDTO = new StaffOverrideAreaDetailDTO();
        staffOverrideAreaDetailDTO.setBusinessId(staffArea.getBusinessId());
        staffOverrideAreaDetailDTO.setStaffId(staffArea.getStaffId());
        staffOverrideAreaDetailDTO.setOverrideDate(LocalDate.parse(staffArea.getDate()));
        staffOverrideAreaDetailDTO.setWorkingArea(List.of(new WorkingAreaDto(staffArea.getAreaId())));
        return staffOverrideAreaDetailDTO;
    }
}
