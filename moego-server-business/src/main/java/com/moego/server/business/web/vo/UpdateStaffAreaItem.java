package com.moego.server.business.web.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class UpdateStaffAreaItem {

    private Integer staffId;
    private String date;

    /* area 需要已经存在 */
    private Integer areaId;

    /* 是否更改staff的默认service area，若为true则忽略date field */
    private Boolean isDefault;
}
