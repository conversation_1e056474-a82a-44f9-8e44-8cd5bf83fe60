package com.moego.server.business.web;

import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthType;
import com.moego.server.business.service.HelloService;
import com.moego.server.business.web.vo.debugging.HelloWellVO;
import com.moego.server.business.web.vo.debugging.HelloWorldDong;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 后端新人打卡专用 Controller
 * 用于练习第一次创建变更&部署
 *
 * <AUTHOR> Huang
 */
@Slf4j
@RestController
@RequestMapping(path = "/business/debugging")
public class HelloWorldController {

    @Autowired
    private HelloService helloService;

    @GetMapping("/well")
    @Auth(AuthType.ANONYMOUS)
    public HelloWellVO helloWell() throws InterruptedException {
        String msg = helloService.sayHello("well");
        return new HelloWellVO().setMessage(msg);
    }

    @GetMapping("/zhangdong")
    @Auth(AuthType.ANONYMOUS)
    public HelloWorldDong helloDong() throws InterruptedException {
        return new HelloWorldDong().setMsg("zhangdong");
    }
}
