package com.moego.server.business.service;

import static com.moego.common.enums.ServiceAreaEnum.POLYGON;
import static com.moego.common.enums.ServiceAreaEnum.ZIPCODE;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.when;

import com.moego.common.dto.BusinessPreferenceDto;
import com.moego.idl.models.appointment.v1.StaffPetDetail;
import com.moego.idl.models.order.v1.OrderDetailModel;
import com.moego.idl.models.order.v1.OrderModel;
import com.moego.idl.service.appointment.v1.GetStaffPetDetailsResponse;
import com.moego.idl.service.appointment.v1.PetDetailServiceGrpc;
import com.moego.idl.service.order.v1.GetOrderListResponse;
import com.moego.idl.service.order.v1.OrderServiceGrpc;
import com.moego.lib.common.exception.BizException;
import com.moego.lib.common.migrate.MigrateHelper;
import com.moego.lib.common.migrate.MigrateInfo;
import com.moego.server.business.dto.CertainAreaDTO;
import com.moego.server.business.dto.StaffOverrideAreaDetailDTO;
import com.moego.server.business.dto.TimeRangeDto;
import com.moego.server.business.dto.WorkingAreaDto;
import com.moego.server.business.dto.WorkingTimeAndAreaDto;
import com.moego.server.business.mapper.MoeGeoareaMapper;
import com.moego.server.business.mapperbean.MoeGeoarea;
import com.moego.server.business.mapperbean.MoeStaffOverrideArea;
import com.moego.server.business.params.BatchGetAreasByLocationParams;
import com.moego.server.business.params.GetAreasByLocationParams;
import com.moego.server.business.params.LocationParams;
import com.moego.server.business.service.dto.StaffDateServiceArea;
import com.moego.server.business.web.vo.CertainArea;
import com.moego.server.business.web.vo.UpdateStaffAreaItem;
import com.moego.server.business.web.vo.UpdateStaffAreaResult;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.util.Pair;

@ExtendWith(MockitoExtension.class)
class StaffServiceTest {
    @Mock
    MoeGeoareaMapper areaMapper;

    @Mock
    StaffOverrideAreaService staffOverrideAreaService;

    @InjectMocks
    StaffService staffService;

    @Mock
    MigrateHelper migrateHelper;

    @Mock
    private BusinessService businessService;

    @Mock
    private PetDetailServiceGrpc.PetDetailServiceBlockingStub petDetailServiceBlockingStub;

    @Mock
    private OrderServiceGrpc.OrderServiceBlockingStub orderServiceStub;

    @Test
    void checkInsidePolygon() {
        Boolean inside = staffService.checkInsidePolygon(null, 1.0, 2.0);
        assertNull(inside);
        inside = staffService.checkInsidePolygon(
                List.of(List.of(0.0, 0.0), List.of(2.0, 0.0), List.of(2.0, 2.0)), null, null);
        assertNull(inside);
        inside = staffService.checkInsidePolygon(
                List.of(List.of(0.0, 0.0), List.of(2.0, 0.0), List.of(2.0, 2.0)), 1.5, 0.5);
        assertEquals(Boolean.TRUE, inside);
        inside = staffService.checkInsidePolygon(
                List.of(List.of(0.0, 0.0), List.of(2.0, 0.0), List.of(2.0, 2.0)), 1.0, 2.0);
        assertEquals(Boolean.FALSE, inside);
    }

    @Test
    void serviceAreaCheckInside() {
        StaffService spyStaffService = Mockito.spy(staffService);
        doReturn(Boolean.TRUE).when(spyStaffService).checkInsidePolygon(any(), any(), any());
        assertTrue(spyStaffService.serviceAreaCheckInside(
                new MoeGeoarea() {
                    {
                        setAreaType(POLYGON);
                        setAreaPolygon("[[0,0],[2,0],[2,2]]");
                    }
                },
                1.5,
                1.0,
                null));
        doReturn(null).when(spyStaffService).checkInsidePolygon(any(), any(), any());
        assertFalse(spyStaffService.serviceAreaCheckInside(
                new MoeGeoarea() {
                    {
                        setAreaType(POLYGON);
                        setAreaPolygon("[[0,0],[2,0],[2,2]]");
                    }
                },
                1.5,
                1.0,
                null));
        doReturn(Boolean.FALSE).when(spyStaffService).checkInsidePolygon(any(), any(), any());
        assertFalse(spyStaffService.serviceAreaCheckInside(
                new MoeGeoarea() {
                    {
                        setAreaType(POLYGON);
                        setAreaPolygon("[[0,0],[2,0],[2,2]]");
                    }
                },
                1.5,
                1.0,
                null));

        assertTrue(spyStaffService.serviceAreaCheckInside(
                new MoeGeoarea() {
                    {
                        setAreaType(ZIPCODE);
                        setZipCodes(List.of("1003", "1001"));
                    }
                },
                null,
                null,
                "1001"));
        assertFalse(spyStaffService.serviceAreaCheckInside(
                new MoeGeoarea() {
                    {
                        setAreaType(ZIPCODE);
                        setZipCodes(List.of("1003", "1002"));
                    }
                },
                null,
                null,
                "1001"));
    }

    @Test
    void getAreasByLocation() {
        when(areaMapper.useDataSource(any())).thenReturn(areaMapper);
        when(areaMapper.queryAreasNotDelete(any(), any(), any()))
                .thenReturn(List.of(
                        new MoeGeoarea() {
                            {
                                setId(1);
                            }
                        },
                        new MoeGeoarea() {
                            {
                                setId(2);
                            }
                        }));
        when(migrateHelper.getMigrationInfo(1L)).thenReturn(new MigrateInfo(0, false));
        StaffService spyStaffService = Mockito.spy(staffService);
        doReturn(true, false, false, true, false, false)
                .when(spyStaffService)
                .serviceAreaCheckInside(any(), any(), any(), any());
        Map<Long, List<CertainAreaDTO>> areas = spyStaffService.getAreasByLocation(new BatchGetAreasByLocationParams(
                1L,
                List.of(1, 2, 3),
                List.of(
                        new GetAreasByLocationParams(1L, "0.0", "0.0", null),
                        new GetAreasByLocationParams(2L, "1.5", "1.0", null),
                        new GetAreasByLocationParams(3L, null, null, "1001"))));
        assertEquals(1, areas.get(1L).get(0).getAreaId());
        assertEquals(1, areas.get(1L).size());
        assertEquals(2, areas.get(2L).get(0).getAreaId());
        assertEquals(1, areas.get(2L).size());
        assertEquals(0, areas.get(3L).size());
    }

    @Test
    void getMoeGeoareasByLocation() {
        when(areaMapper.queryAreasNotDelete(any(), any(), any()))
                .thenReturn(List.of(
                        new MoeGeoarea() {
                            {
                                setId(1);
                            }
                        },
                        new MoeGeoarea() {
                            {
                                setId(2);
                            }
                        }));
        StaffService spyStaffService = Mockito.spy(staffService);
        doReturn(true, false).when(spyStaffService).serviceAreaCheckInside(any(), any(), any(), any());
        List<MoeGeoarea> areas = spyStaffService.getMoeGeoareasByLocation(
                1, List.of(1, 2, 3), new GetAreasByLocationParams(1L, "0.0", "0.0", null));
        assertEquals(1, areas.get(0).getId());
        assertEquals(1, areas.size());
        doReturn(false, true).when(spyStaffService).serviceAreaCheckInside(any(), any(), any(), any());
        areas = spyStaffService.getMoeGeoareasByLocation(
                1, List.of(1, 2, 3), new GetAreasByLocationParams(1L, "0.0", "0.0", null));
        assertEquals(2, areas.get(0).getId());
        assertEquals(1, areas.size());
        doReturn(false, false).when(spyStaffService).serviceAreaCheckInside(any(), any(), any(), any());
        areas = spyStaffService.getMoeGeoareasByLocation(
                1, List.of(1, 2, 3), new GetAreasByLocationParams(1L, "0.0", "0.0", null));
        assertEquals(0, areas.size());
        doReturn(true, true).when(spyStaffService).serviceAreaCheckInside(any(), any(), any(), any());
        areas = spyStaffService.getMoeGeoareasByLocation(
                1, List.of(1, 2, 3), new GetAreasByLocationParams(1L, "0.0", "0.0", null));
        assertEquals(1, areas.get(0).getId());
        assertEquals(2, areas.get(1).getId());
        assertEquals(2, areas.size());
    }

    @Test
    void isLocationInsideAreaBatchV2() {}

    @Test
    void validateUpdateRequest() {
        Integer businessId = 1000;
        // area name 校验入参中重复
        assertThrows(
                BizException.class,
                () -> staffService.validateUpdateRequest(
                        businessId,
                        List.of(
                                new CertainArea() {
                                    {
                                        setAreaName("area1");
                                    }
                                },
                                new CertainArea() {
                                    {
                                        setAreaName("area1");
                                    }
                                })));

        // area name 校验入参与数据库重复
        when(areaMapper.queryAreasNotDelete(any(), any(), any())).thenReturn(List.of(new MoeGeoarea() {
            {
                setId(1);
                setAreaName("areaname");
            }
        }));
        assertThrows(
                BizException.class,
                () -> staffService.validateUpdateRequest(businessId, List.of(new CertainArea() {
                    {
                        setAreaName("areaname");
                    }
                })));
        // area name 重复性校验区分大小写
        staffService.validateUpdateRequest(businessId, List.of(new CertainArea() {
            {
                setAreaName("AreaName");
            }
        }));
        // area name 校验不更新场景
        staffService.validateUpdateRequest(businessId, List.of(new CertainArea() {
            {
                setAreaId(1);
                setAreaName("areaname");
            }
        }));
    }

    @Test
    void updateStaffOverrideArea() {
        Integer businessId = 100;
        List<UpdateStaffAreaItem> updates = List.of(
                new UpdateStaffAreaItem(1, "2023-09-19", 0, false), // 原不存在该 override area，写入一条 any area
                new UpdateStaffAreaItem(2, "2023-09-01", 2, false),
                new UpdateStaffAreaItem(3, "2023-09-29", 3, false),
                new UpdateStaffAreaItem(5, "2023-09-08", 0, false), // 删除存在的 override area
                new UpdateStaffAreaItem(0, "2023-09-09", 6, false) // 跳过更新
                );
        when(staffOverrideAreaService.getStaffOverrideAreaDetailMap(
                        businessId, List.of(1, 2, 3, 5), "2023-09-01", "2023-09-29"))
                .thenReturn(Map.of(
                        org.apache.commons.lang3.tuple.Pair.of(5, LocalDate.parse("2023-09-08")),
                                new MoeStaffOverrideArea() {
                                    {
                                        setId(55);
                                    }
                                },
                        org.apache.commons.lang3.tuple.Pair.of(3, LocalDate.parse("2023-09-29")),
                                new MoeStaffOverrideArea() {
                                    {
                                        setAreaData(List.of(new WorkingAreaDto(33)));
                                    }
                                }));
        List<UpdateStaffAreaResult> result = new ArrayList<>();
        Pair<List<StaffOverrideAreaDetailDTO>, List<Integer>> updatesAndDels =
                staffService.updateStaffOverrideArea(businessId, updates, result);
        assertEquals(4, result.size());
        assertEquals(3, updatesAndDels.getFirst().size());
        assertEquals(
                -1,
                updatesAndDels
                        .getFirst()
                        .get(0)
                        .getWorkingArea()
                        .get(0)
                        .getAreaId()); // 原不存在该 override area，写入一条 any area
        assertEquals(3, updatesAndDels.getFirst().size());
        assertEquals(1, updatesAndDels.getSecond().size());
        assertEquals(55, updatesAndDels.getSecond().get(0));
    }

    @Test
    void getLocationCACDResult() {
        StaffService spyStaffService = Mockito.spy(staffService);

        Map<Integer, Map<String, List<TimeRangeDto>>> staffsWorkTime = Map.of(
                1,
                Map.of(
                        "2023-09-01",
                        List.of(new TimeRangeDto(11, 110)),
                        "2023-09-02",
                        List.of(new TimeRangeDto(12, 120))),
                2,
                Map.of(
                        "2023-09-02",
                        List.of(new TimeRangeDto(22, 220)),
                        "2023-09-03",
                        List.of(new TimeRangeDto(23, 230), new TimeRangeDto(30, 40))));
        List<StaffDateServiceArea> staffsWorkArea = List.of(
                new StaffDateServiceArea() {
                    {
                        setStaffId(1);
                        setDate("2023-09-01");
                    }
                },
                new StaffDateServiceArea() {
                    {
                        setStaffId(1);
                        setDate("2023-09-02");
                    }
                },
                new StaffDateServiceArea() {
                    {
                        setStaffId(2);
                        setDate("2023-09-02");
                    }
                });
        doReturn(
                        List.of(new WorkingTimeAndAreaDto(11, 110, 100)),
                        List.of(new WorkingTimeAndAreaDto(12, 120, 200)),
                        List.of(new WorkingTimeAndAreaDto(22, 220, 300)),
                        List.of())
                .when(spyStaffService)
                .getCACDResult(any(), any(), any());
        Map<String, Map<Integer, List<WorkingTimeAndAreaDto>>> result =
                spyStaffService.getLocationCACDResult(staffsWorkTime, staffsWorkArea, new LocationParams("", "", ""));
        assertNotNull(result);
    }

    @Test
    void getCACDResult() {
        StaffService spyStaffService = Mockito.spy(staffService);

        List<TimeRangeDto> staffWorkTime = List.of(new TimeRangeDto(10, 20), new TimeRangeDto(30, 40));
        List<WorkingTimeAndAreaDto> result =
                spyStaffService.getCACDResult(staffWorkTime, null, new LocationParams("", "", ""));
        assertEquals(2, result.size());

        StaffDateServiceArea staffWorkArea = new StaffDateServiceArea();
        staffWorkArea.setAreaId(100);
        doReturn(false).when(spyStaffService).serviceAreaCheckInside(any(), any(), any(), any());
        result = spyStaffService.getCACDResult(staffWorkTime, staffWorkArea, new LocationParams("", "", ""));
        assertEquals(0, result.size());

        doReturn(true).when(spyStaffService).serviceAreaCheckInside(any(), any(), any(), any());
        result = spyStaffService.getCACDResult(staffWorkTime, staffWorkArea, new LocationParams("", "", ""));
        assertEquals(2, result.size());
        assertEquals(10, result.get(0).getStartTime());
        assertEquals(20, result.get(0).getEndTime());
        assertEquals(100, result.get(0).getAreaId());
        assertEquals(30, result.get(1).getStartTime());
        assertEquals(40, result.get(1).getEndTime());
        assertEquals(100, result.get(1).getAreaId());
    }

    @Test
    void getStaffEstimatedRevenueByDate() {
        // Mock businessService
        BusinessPreferenceDto businessPreferenceDto = new BusinessPreferenceDto();
        businessPreferenceDto.setTimezoneName("Asia/Shanghai");
        when(businessService.getBusinessPreference(anyInt())).thenReturn(businessPreferenceDto);

        // 准备测试数据
        Long companyId = 100L;
        Long businessId = 200L;
        List<Long> staffIds = Arrays.asList(1001L, 1002L, 1003L);
        String startDate = "2024-03-01";
        String endDate = "2024-03-31";

        // Mock petDetailServiceBlockingStub 返回数据
        GetStaffPetDetailsResponse mockResponse = GetStaffPetDetailsResponse.newBuilder()
                .addAllStaffPetDetails(Arrays.asList(
                        // staff 1001 的数据，两个预约
                        createStaffPetDetail(1001L, "2024-03-01", 1L), // appointmentId = 1
                        createStaffPetDetail(1001L, "2024-03-01", 1L), // appointmentId = 1，pet detail 2
                        createStaffPetDetail(
                                1001L, "2024-03-01", 1L), // appointmentId = 1，pet detail 3 验证多 pet detail 场景
                        createStaffPetDetail(1001L, "2024-03-01", 2L), // appointmentId = 2
                        createStaffPetDetail(1001L, "2024-03-02", 3L), // appointmentId = 3
                        createStaffPetDetail(1001L, "2024-03-02", 4L), // appointmentId = 4

                        // staff 1002 的数据
                        createStaffPetDetail(1002L, "2024-03-01", 5L), // appointmentId = 5
                        createStaffPetDetail(1002L, "2024-03-31", 6L) // appointmentId = 6
                        ))
                .build();

        when(petDetailServiceBlockingStub.getStaffPetDetails(any())).thenReturn(mockResponse);

        // Mock orderServiceStub 返回数据
        GetOrderListResponse orderListResponse = GetOrderListResponse.newBuilder()
                .addAllOrderList(Arrays.asList(
                        createOrderDetail(1L, 100.0), // appointmentId = 1, totalAmount = 100
                        createOrderDetail(2L, 50.0), // appointmentId = 2, totalAmount = 50
                        createOrderDetail(3L, 150.0), // appointmentId = 3, totalAmount = 150
                        createOrderDetail(4L, 0.0), // appointmentId = 4, totalAmount = 0
                        createOrderDetail(5L, 200.0), // appointmentId = 5, totalAmount = 200
                        createOrderDetail(6L, 300.0) // appointmentId = 6, totalAmount = 300
                        ))
                .build();

        when(orderServiceStub.getOrderList(any())).thenReturn(orderListResponse);

        // 构建预期结果
        Map<Long, Map<String, BigDecimal>> expect = new HashMap<>();

        // staff 1001 的预期数据
        expect.put(1001L, new HashMap<>() {
            {
                put("2024-03-01", new BigDecimal("150.0")); // 100 + 50
                put("2024-03-02", new BigDecimal("150.0")); // 150 + 0
            }
        });

        // staff 1002 的预期数据
        expect.put(1002L, new HashMap<>() {
            {
                put("2024-03-01", new BigDecimal("200.0")); // 200
                put("2024-03-31", new BigDecimal("300.0")); // 300
            }
        });

        // 调用测试方法并验证结果
        Map<Long, Map<String, BigDecimal>> result =
                staffService.getStaffEstimatedRevenueByDate(companyId, businessId, staffIds, startDate, endDate);

        assertEquals(expect, result);
    }

    private StaffPetDetail createStaffPetDetail(Long staffId, String date, Long appointmentId) {
        return StaffPetDetail.newBuilder()
                .setStaffId(staffId)
                .setStartDate(date)
                .setAppointmentId(appointmentId)
                .build();
    }

    private OrderDetailModel createOrderDetail(Long appointmentId, Double totalAmount) {
        return OrderDetailModel.newBuilder()
                .setOrder(OrderModel.newBuilder()
                        .setSourceId(appointmentId)
                        .setTotalAmount(totalAmount)
                        .build())
                .build();
    }
}
