package com.moego.server.business.helper;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.moego.idl.service.metadata.v1.ExtractValuesResponse;
import com.moego.idl.service.metadata.v1.MetadataServiceGrpc;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class MetadataHelperTest {

    @Mock
    private MetadataServiceGrpc.MetadataServiceBlockingStub metadataClient;

    @InjectMocks
    private MetadataHelper metadataHelper;

    @Test
    void isUseStaffWorkingArea_WithCache_ReturnsCachedValue() {

        when(metadataClient.extractValues(any()))
                .thenReturn(ExtractValuesResponse.newBuilder()
                        .putValues(
                                MetadataHelper.SHIFT_SERVICE_AREA_ENABLE_KEY,
                                MetadataHelper.SHIFT_SERVICE_AREA_ENABLE_VALUE)
                        .build());

        boolean result = metadataHelper.isUseStaffWorkingArea(12345, true);

        assertThat(result).isTrue();
    }

    @Test
    void isUseStaffWorkingArea_WithoutCache_FetchesFromRemote() {
        when(metadataClient.extractValues(any()))
                .thenReturn(ExtractValuesResponse.newBuilder()
                        .putValues(
                                MetadataHelper.SHIFT_SERVICE_AREA_ENABLE_KEY,
                                MetadataHelper.SHIFT_SERVICE_AREA_ENABLE_VALUE)
                        .build());

        boolean result = metadataHelper.isUseStaffWorkingArea(12345, false);

        assertThat(result).isTrue();
    }
}
