package com.moego.lib.aws;

import com.moego.lib.utils.StringUtils;
import com.moego.lib.utils.model.Pair;
import java.net.URI;
import java.util.List;
import java.util.Map;
import java.util.function.Consumer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials;
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.mediaconvert.MediaConvertClient;
import software.amazon.awssdk.services.mediaconvert.model.AacAudioDescriptionBroadcasterMix;
import software.amazon.awssdk.services.mediaconvert.model.AacCodecProfile;
import software.amazon.awssdk.services.mediaconvert.model.AacCodingMode;
import software.amazon.awssdk.services.mediaconvert.model.AacRateControlMode;
import software.amazon.awssdk.services.mediaconvert.model.AacRawFormat;
import software.amazon.awssdk.services.mediaconvert.model.AacSettings;
import software.amazon.awssdk.services.mediaconvert.model.AacSpecification;
import software.amazon.awssdk.services.mediaconvert.model.AccelerationMode;
import software.amazon.awssdk.services.mediaconvert.model.AccelerationSettings;
import software.amazon.awssdk.services.mediaconvert.model.AfdSignaling;
import software.amazon.awssdk.services.mediaconvert.model.AntiAlias;
import software.amazon.awssdk.services.mediaconvert.model.AudioCodec;
import software.amazon.awssdk.services.mediaconvert.model.AudioCodecSettings;
import software.amazon.awssdk.services.mediaconvert.model.AudioDefaultSelection;
import software.amazon.awssdk.services.mediaconvert.model.AudioDescription;
import software.amazon.awssdk.services.mediaconvert.model.AudioLanguageCodeControl;
import software.amazon.awssdk.services.mediaconvert.model.AudioSelector;
import software.amazon.awssdk.services.mediaconvert.model.AudioTypeControl;
import software.amazon.awssdk.services.mediaconvert.model.Av1AdaptiveQuantization;
import software.amazon.awssdk.services.mediaconvert.model.Av1BitDepth;
import software.amazon.awssdk.services.mediaconvert.model.Av1FilmGrainSynthesis;
import software.amazon.awssdk.services.mediaconvert.model.Av1FramerateControl;
import software.amazon.awssdk.services.mediaconvert.model.Av1FramerateConversionAlgorithm;
import software.amazon.awssdk.services.mediaconvert.model.Av1QvbrSettings;
import software.amazon.awssdk.services.mediaconvert.model.Av1RateControlMode;
import software.amazon.awssdk.services.mediaconvert.model.Av1Settings;
import software.amazon.awssdk.services.mediaconvert.model.Av1SpatialAdaptiveQuantization;
import software.amazon.awssdk.services.mediaconvert.model.CancelJobRequest;
import software.amazon.awssdk.services.mediaconvert.model.CaptionSelector;
import software.amazon.awssdk.services.mediaconvert.model.ColorMetadata;
import software.amazon.awssdk.services.mediaconvert.model.ColorSpace;
import software.amazon.awssdk.services.mediaconvert.model.ContainerSettings;
import software.amazon.awssdk.services.mediaconvert.model.ContainerType;
import software.amazon.awssdk.services.mediaconvert.model.CreateJobRequest;
import software.amazon.awssdk.services.mediaconvert.model.DeinterlaceAlgorithm;
import software.amazon.awssdk.services.mediaconvert.model.Deinterlacer;
import software.amazon.awssdk.services.mediaconvert.model.DeinterlacerControl;
import software.amazon.awssdk.services.mediaconvert.model.DeinterlacerMode;
import software.amazon.awssdk.services.mediaconvert.model.DescribeEndpointsRequest;
import software.amazon.awssdk.services.mediaconvert.model.DescribeEndpointsResponse;
import software.amazon.awssdk.services.mediaconvert.model.DestinationSettings;
import software.amazon.awssdk.services.mediaconvert.model.DropFrameTimecode;
import software.amazon.awssdk.services.mediaconvert.model.FileGroupSettings;
import software.amazon.awssdk.services.mediaconvert.model.FrameCaptureSettings;
import software.amazon.awssdk.services.mediaconvert.model.GetJobRequest;
import software.amazon.awssdk.services.mediaconvert.model.GetJobTemplateRequest;
import software.amazon.awssdk.services.mediaconvert.model.GetPresetRequest;
import software.amazon.awssdk.services.mediaconvert.model.H264AdaptiveQuantization;
import software.amazon.awssdk.services.mediaconvert.model.H264CodecLevel;
import software.amazon.awssdk.services.mediaconvert.model.H264CodecProfile;
import software.amazon.awssdk.services.mediaconvert.model.H264DynamicSubGop;
import software.amazon.awssdk.services.mediaconvert.model.H264EntropyEncoding;
import software.amazon.awssdk.services.mediaconvert.model.H264FieldEncoding;
import software.amazon.awssdk.services.mediaconvert.model.H264FlickerAdaptiveQuantization;
import software.amazon.awssdk.services.mediaconvert.model.H264FramerateControl;
import software.amazon.awssdk.services.mediaconvert.model.H264FramerateConversionAlgorithm;
import software.amazon.awssdk.services.mediaconvert.model.H264GopBReference;
import software.amazon.awssdk.services.mediaconvert.model.H264GopSizeUnits;
import software.amazon.awssdk.services.mediaconvert.model.H264InterlaceMode;
import software.amazon.awssdk.services.mediaconvert.model.H264ParControl;
import software.amazon.awssdk.services.mediaconvert.model.H264QualityTuningLevel;
import software.amazon.awssdk.services.mediaconvert.model.H264QvbrSettings;
import software.amazon.awssdk.services.mediaconvert.model.H264RateControlMode;
import software.amazon.awssdk.services.mediaconvert.model.H264RepeatPps;
import software.amazon.awssdk.services.mediaconvert.model.H264SceneChangeDetect;
import software.amazon.awssdk.services.mediaconvert.model.H264Settings;
import software.amazon.awssdk.services.mediaconvert.model.H264SlowPal;
import software.amazon.awssdk.services.mediaconvert.model.H264SpatialAdaptiveQuantization;
import software.amazon.awssdk.services.mediaconvert.model.H264Syntax;
import software.amazon.awssdk.services.mediaconvert.model.H264Telecine;
import software.amazon.awssdk.services.mediaconvert.model.H264TemporalAdaptiveQuantization;
import software.amazon.awssdk.services.mediaconvert.model.H264UnregisteredSeiTimecode;
import software.amazon.awssdk.services.mediaconvert.model.H265AdaptiveQuantization;
import software.amazon.awssdk.services.mediaconvert.model.H265CodecLevel;
import software.amazon.awssdk.services.mediaconvert.model.H265CodecProfile;
import software.amazon.awssdk.services.mediaconvert.model.H265DynamicSubGop;
import software.amazon.awssdk.services.mediaconvert.model.H265FlickerAdaptiveQuantization;
import software.amazon.awssdk.services.mediaconvert.model.H265FramerateControl;
import software.amazon.awssdk.services.mediaconvert.model.H265FramerateConversionAlgorithm;
import software.amazon.awssdk.services.mediaconvert.model.H265GopBReference;
import software.amazon.awssdk.services.mediaconvert.model.H265GopSizeUnits;
import software.amazon.awssdk.services.mediaconvert.model.H265InterlaceMode;
import software.amazon.awssdk.services.mediaconvert.model.H265ParControl;
import software.amazon.awssdk.services.mediaconvert.model.H265QualityTuningLevel;
import software.amazon.awssdk.services.mediaconvert.model.H265QvbrSettings;
import software.amazon.awssdk.services.mediaconvert.model.H265RateControlMode;
import software.amazon.awssdk.services.mediaconvert.model.H265SampleAdaptiveOffsetFilterMode;
import software.amazon.awssdk.services.mediaconvert.model.H265SceneChangeDetect;
import software.amazon.awssdk.services.mediaconvert.model.H265Settings;
import software.amazon.awssdk.services.mediaconvert.model.H265SlowPal;
import software.amazon.awssdk.services.mediaconvert.model.H265SpatialAdaptiveQuantization;
import software.amazon.awssdk.services.mediaconvert.model.H265Telecine;
import software.amazon.awssdk.services.mediaconvert.model.H265TemporalAdaptiveQuantization;
import software.amazon.awssdk.services.mediaconvert.model.H265Tiles;
import software.amazon.awssdk.services.mediaconvert.model.H265UnregisteredSeiTimecode;
import software.amazon.awssdk.services.mediaconvert.model.HlsCaptionLanguageSetting;
import software.amazon.awssdk.services.mediaconvert.model.HlsClientCache;
import software.amazon.awssdk.services.mediaconvert.model.HlsCodecSpecification;
import software.amazon.awssdk.services.mediaconvert.model.HlsDirectoryStructure;
import software.amazon.awssdk.services.mediaconvert.model.HlsGroupSettings;
import software.amazon.awssdk.services.mediaconvert.model.HlsIFrameOnlyManifest;
import software.amazon.awssdk.services.mediaconvert.model.HlsManifestCompression;
import software.amazon.awssdk.services.mediaconvert.model.HlsManifestDurationFormat;
import software.amazon.awssdk.services.mediaconvert.model.HlsOutputSelection;
import software.amazon.awssdk.services.mediaconvert.model.HlsProgramDateTime;
import software.amazon.awssdk.services.mediaconvert.model.HlsSegmentControl;
import software.amazon.awssdk.services.mediaconvert.model.HlsSettings;
import software.amazon.awssdk.services.mediaconvert.model.HlsStreamInfResolution;
import software.amazon.awssdk.services.mediaconvert.model.HlsTimedMetadataId3Frame;
import software.amazon.awssdk.services.mediaconvert.model.Input;
import software.amazon.awssdk.services.mediaconvert.model.InputClipping;
import software.amazon.awssdk.services.mediaconvert.model.InputDeblockFilter;
import software.amazon.awssdk.services.mediaconvert.model.InputDenoiseFilter;
import software.amazon.awssdk.services.mediaconvert.model.InputFilterEnable;
import software.amazon.awssdk.services.mediaconvert.model.InputPsiControl;
import software.amazon.awssdk.services.mediaconvert.model.InputRotate;
import software.amazon.awssdk.services.mediaconvert.model.InputScanType;
import software.amazon.awssdk.services.mediaconvert.model.InputTimecodeSource;
import software.amazon.awssdk.services.mediaconvert.model.Job;
import software.amazon.awssdk.services.mediaconvert.model.JobSettings;
import software.amazon.awssdk.services.mediaconvert.model.JobStatus;
import software.amazon.awssdk.services.mediaconvert.model.JobTemplate;
import software.amazon.awssdk.services.mediaconvert.model.JobTemplateListBy;
import software.amazon.awssdk.services.mediaconvert.model.ListJobTemplatesRequest;
import software.amazon.awssdk.services.mediaconvert.model.ListJobsRequest;
import software.amazon.awssdk.services.mediaconvert.model.ListJobsResponse;
import software.amazon.awssdk.services.mediaconvert.model.ListPresetsRequest;
import software.amazon.awssdk.services.mediaconvert.model.M3u8NielsenId3;
import software.amazon.awssdk.services.mediaconvert.model.M3u8PcrControl;
import software.amazon.awssdk.services.mediaconvert.model.M3u8Scte35Source;
import software.amazon.awssdk.services.mediaconvert.model.M3u8Settings;
import software.amazon.awssdk.services.mediaconvert.model.Mp4CslgAtom;
import software.amazon.awssdk.services.mediaconvert.model.Mp4FreeSpaceBox;
import software.amazon.awssdk.services.mediaconvert.model.Mp4MoovPlacement;
import software.amazon.awssdk.services.mediaconvert.model.Mp4Settings;
import software.amazon.awssdk.services.mediaconvert.model.Order;
import software.amazon.awssdk.services.mediaconvert.model.Output;
import software.amazon.awssdk.services.mediaconvert.model.OutputGroup;
import software.amazon.awssdk.services.mediaconvert.model.OutputGroupSettings;
import software.amazon.awssdk.services.mediaconvert.model.OutputGroupType;
import software.amazon.awssdk.services.mediaconvert.model.OutputSettings;
import software.amazon.awssdk.services.mediaconvert.model.Preset;
import software.amazon.awssdk.services.mediaconvert.model.PresetListBy;
import software.amazon.awssdk.services.mediaconvert.model.Rectangle;
import software.amazon.awssdk.services.mediaconvert.model.RespondToAfd;
import software.amazon.awssdk.services.mediaconvert.model.S3DestinationAccessControl;
import software.amazon.awssdk.services.mediaconvert.model.S3DestinationSettings;
import software.amazon.awssdk.services.mediaconvert.model.S3ObjectCannedAcl;
import software.amazon.awssdk.services.mediaconvert.model.ScalingBehavior;
import software.amazon.awssdk.services.mediaconvert.model.TimecodeConfig;
import software.amazon.awssdk.services.mediaconvert.model.TimedMetadata;
import software.amazon.awssdk.services.mediaconvert.model.VideoCodec;
import software.amazon.awssdk.services.mediaconvert.model.VideoCodecSettings;
import software.amazon.awssdk.services.mediaconvert.model.VideoDescription;
import software.amazon.awssdk.services.mediaconvert.model.VideoPreprocessor;
import software.amazon.awssdk.services.mediaconvert.model.VideoSelector;
import software.amazon.awssdk.services.mediaconvert.model.VideoTimecodeInsertion;

public class MediaConvertAPI implements AutoCloseable {

    private static final Logger log = LoggerFactory.getLogger(MediaConvertAPI.class);
    private final MediaConvertClient client;

    public MediaConvertAPI(Region region) {
        this(region, getEndpoint(region));
    }

    public MediaConvertAPI(Region region, String endpoint) {
        this.client = MediaConvertClient.builder()
                .region(region)
                .endpointOverride(URI.create(endpoint))
                .build();
    }

    public MediaConvertAPI(Region region, String accessKey, String accessSecret) {
        this(region, null, accessKey, accessSecret);
    }

    public MediaConvertAPI(Region region, String endpoint, String accessKey, String accessSecret) {
        var uri = URI.create(StringUtils.isBlank(endpoint) ? getEndpoint(region) : endpoint);
        var credential = AwsBasicCredentials.create(accessKey, accessSecret);
        var provider = StaticCredentialsProvider.create(credential);

        this.client = MediaConvertClient.builder()
                .region(region)
                .credentialsProvider(provider)
                .endpointOverride(uri)
                .build();
    }

    // get job
    public Job getJob(String id) {
        var request = GetJobRequest.builder().id(id).build();
        var response = this.client.getJob(request);
        return (response == null) ? null : response.job();
    }

    // cancel job
    public boolean cancelJob(String id) {
        try {
            var request = CancelJobRequest.builder().id(id).build();
            var response = this.client.cancelJob(request);
            return (response != null && response.sdkHttpResponse().isSuccessful());
        } catch (Exception e) {
            log.error("cancel job '{}' occur EXCEPTION", id, e);
            return false;
        }
    }

    // list job
    public ListJobsResponse listJob(JobStatus status, String queue, String next, Integer limit) {
        var builder = ListJobsRequest.builder();
        builder.order(Order.DESCENDING);
        if (status != null) {
            builder.status(status);
        }
        if (!StringUtils.isBlank(queue)) {
            builder.queue(queue);
        }
        if (!StringUtils.isBlank(next)) {
            builder.nextToken(next);
        }
        if (limit != null) {
            builder.maxResults(limit);
        }

        return this.client.listJobs(builder.build());
    }

    // create job by template
    public String createJob(String role, Input input, String template) {
        var settings = JobSettings.builder().inputs(input).build();
        return createJob(role, null, null, template, settings, null, null, null);
    }

    // create job by output settings
    public String createJob(String role, Input input, List<OutputGroup> groups) {
        var settings = JobSettings.builder().inputs(input).outputGroups(groups).build();
        return createJob(role, null, null, null, settings, null, null, null);
    }

    // create job by output settings
    public String createJob(
            String role,
            Input input,
            AccelerationMode mode,
            TimecodeConfig timecodeConfig,
            List<OutputGroup> groups,
            Map<String, String> metadata,
            Map<String, String> tags) {
        var builder = JobSettings.builder();
        builder.inputs(input);
        builder.outputGroups(groups);
        if (timecodeConfig != null) {
            builder.timecodeConfig(timecodeConfig);
        }
        var settings = builder.build();
        if (mode == null) {
            return createJob(role, null, null, null, settings, null, metadata, tags);
        }

        var acceleration = AccelerationSettings.builder().mode(mode).build();
        return createJob(role, null, null, null, settings, acceleration, metadata, tags);
    }

    // create job
    public String createJob(
            String role,
            Integer priority,
            String queue,
            String template,
            JobSettings settings,
            AccelerationSettings acceleration,
            Map<String, String> metadata,
            Map<String, String> tags) {
        var builder = CreateJobRequest.builder();
        builder.role(role);
        builder.settings(settings);
        if (priority != null) {
            builder.priority(priority);
        }
        if (!StringUtils.isBlank(queue)) {
            builder.queue(queue);
        }
        if (!StringUtils.isBlank(template)) {
            builder.jobTemplate(template);
        }
        if (acceleration != null) {
            builder.accelerationSettings(acceleration);
        }
        if (metadata != null && !metadata.isEmpty()) {
            builder.userMetadata(metadata);
        }
        if (tags != null && !tags.isEmpty()) {
            builder.tags(tags);
        }

        var response = this.client.createJob(builder.build());
        return response.job().id();
    }

    // get output preset
    public Preset getPreset(String name) {
        var request = GetPresetRequest.builder().name(name).build();
        var response = this.client.getPreset(request);
        return response.preset();
    }

    // list output preset
    public Pair<String, List<Preset>> listPresets(PresetListBy listBy, String category, String next, Integer limit) {
        var builder = ListPresetsRequest.builder();
        if (listBy != null) {
            builder.listBy(listBy);
        }
        if (!StringUtils.isBlank(category)) {
            builder.category(category);
        }
        if (!StringUtils.isBlank(next)) {
            builder.nextToken(next);
        }
        if (limit != null && 0 < limit) {
            builder.maxResults(limit);
        } else {
            builder.maxResults(20);
        }
        var response = this.client.listPresets(builder.build());
        return Pair.of(response.nextToken(), response.presets());
    }

    // foreach output preset
    public int foreachPreset(PresetListBy listBy, String category, Consumer<Preset> consumer) {
        int count = 0;
        final int pageSize = 20;
        for (String next = null; ; ) {
            var builder = ListPresetsRequest.builder();
            if (listBy != null) {
                builder.listBy(listBy);
            }
            if (!StringUtils.isBlank(category)) {
                builder.category(category);
            }
            if (!StringUtils.isBlank(next)) {
                builder.nextToken(next);
            }
            builder.maxResults(pageSize);
            var response = this.client.listPresets(builder.build());
            int n = response.presets().size();
            if (0 < n) {
                response.presets().forEach(consumer);
                count += n;
            }

            if (StringUtils.isBlank(response.nextToken()) || n < pageSize) {
                break;
            }

            next = response.nextToken();
        }

        return count;
    }

    // count output preset
    public int countPreset(PresetListBy listBy, String category) {
        return foreachPreset(listBy, category, preset -> {});
    }

    // get job template
    public JobTemplate getTemplate(String name) {
        var request = GetJobTemplateRequest.builder().name(name).build();
        var response = this.client.getJobTemplate(request);
        return response.jobTemplate();
    }

    // list job template
    public Pair<String, List<JobTemplate>> listTemplate(
            JobTemplateListBy listBy, String category, String next, Integer limit) {
        var builder = ListJobTemplatesRequest.builder();
        if (listBy != null) {
            builder.listBy(listBy);
        }
        if (!StringUtils.isBlank(category)) {
            builder.category(category);
        }
        if (!StringUtils.isBlank(next)) {
            builder.nextToken(next);
        }
        if (limit != null && 0 < limit) {
            builder.maxResults(limit);
        } else {
            builder.maxResults(10);
        }
        var response = this.client.listJobTemplates(builder.build());
        return Pair.of(response.nextToken(), response.jobTemplates());
    }

    // foreach job template
    public int foreachTemplate(JobTemplateListBy listBy, String category, Consumer<JobTemplate> consumer) {
        int count = 0;
        final int pageSize = 10;
        for (String next = null; ; ) {
            var builder = ListJobTemplatesRequest.builder();
            if (listBy != null) {
                builder.listBy(listBy);
            }
            if (!StringUtils.isBlank(category)) {
                builder.category(category);
            }
            if (!StringUtils.isBlank(next)) {
                builder.nextToken(next);
            }
            builder.maxResults(pageSize);
            var response = this.client.listJobTemplates(builder.build());
            int n = response.jobTemplates().size();
            if (0 < n) {
                response.jobTemplates().forEach(consumer);
                count += n;
            }

            if (StringUtils.isBlank(response.nextToken()) || n < pageSize) {
                break;
            }

            next = response.nextToken();
        }

        return count;
    }

    // count job template
    public int countTemplate(JobTemplateListBy listBy, String category) {
        return foreachTemplate(listBy, category, template -> {});
    }

    // get API endpoint
    public static String getEndpoint(Region region) {
        try (var client = MediaConvertClient.builder().region(region).build()) {
            var request = DescribeEndpointsRequest.builder().maxResults(10).build();
            DescribeEndpointsResponse response = client.describeEndpoints(request);
            if (response == null
                    || response.endpoints() == null
                    || response.endpoints().isEmpty()) {
                return null;
            }

            return response.endpoints().get(0).url();
        }
    }

    public static AudioSelector defaultAudioSelector() {
        return AudioSelector.builder()
                .defaultSelection(AudioDefaultSelection.DEFAULT)
                .offset(0)
                .build();
    }

    public static VideoSelector defaultVideoSelector() {
        return VideoSelector.builder()
                .colorSpace(ColorSpace.FOLLOW)
                .rotate(InputRotate.AUTO)
                .build();
    }

    public static ContainerSettings defaultMP4Container() {
        return buildContainerSettingsForMP4(
                Mp4CslgAtom.INCLUDE, Mp4FreeSpaceBox.EXCLUDE, Mp4MoovPlacement.PROGRESSIVE_DOWNLOAD);
    }

    public static Input buildInput(String path) {
        return buildInput(path, null, defaultAudioSelector(), defaultVideoSelector());
    }

    public static Input buildInput(
            String path, CaptionSelector captionSelector, AudioSelector audioSelector, VideoSelector videoSelector) {
        Map<String, CaptionSelector> captionMap = null;
        if (captionSelector != null) {
            captionMap = Map.of("Caption Selector 1", captionSelector);
        }
        Map<String, AudioSelector> audioMap = null;
        if (audioSelector != null) {
            audioMap = Map.of("Audio Selector 1", audioSelector);
        }

        return buildInput(
                path, captionMap, audioMap, videoSelector, null, null, null, 0, InputTimecodeSource.EMBEDDED, null);
    }

    public static Input buildInput(
            String path,
            Map<String, CaptionSelector> captionSelector,
            Map<String, AudioSelector> audioSelector,
            VideoSelector videoSelector,
            List<InputClipping> clippings,
            Rectangle crop,
            Rectangle position,
            Integer filterStrength,
            InputTimecodeSource timecodeSource,
            String timecodeStart) {
        return buildInput(
                path,
                captionSelector,
                audioSelector,
                videoSelector,
                clippings,
                crop,
                position,
                filterStrength,
                null,
                InputFilterEnable.AUTO,
                InputPsiControl.USE_PSI,
                InputDeblockFilter.DISABLED,
                InputDenoiseFilter.DISABLED,
                timecodeSource,
                timecodeStart);
    }

    public static Input buildInput(
            String path,
            Map<String, CaptionSelector> captionSelector,
            Map<String, AudioSelector> audioSelector,
            VideoSelector videoSelector,
            List<InputClipping> clippings,
            Rectangle crop,
            Rectangle position,
            Integer filterStrength,
            InputScanType scanType,
            InputFilterEnable filterEnable,
            InputPsiControl psiControl,
            InputDeblockFilter deblockFilter,
            InputDenoiseFilter denoiseFilter,
            InputTimecodeSource timecodeSource,
            String timecodeStart) {
        var builder = Input.builder();
        builder.fileInput(path);
        if (captionSelector != null && !captionSelector.isEmpty()) {
            builder.captionSelectors(captionSelector);
        }
        if (audioSelector != null && !audioSelector.isEmpty()) {
            builder.audioSelectors(audioSelector);
        }
        if (videoSelector != null) {
            builder.videoSelector(videoSelector);
        }
        if (filterEnable != null) {
            builder.filterEnable(filterEnable);
        }
        if (filterStrength != null) {
            builder.filterStrength(filterStrength);
        }
        if (deblockFilter != null) {
            builder.deblockFilter(deblockFilter);
        }
        if (denoiseFilter != null) {
            builder.denoiseFilter(denoiseFilter);
        }
        if (psiControl != null) {
            builder.psiControl(psiControl);
        }
        if (crop != null) {
            builder.crop(crop);
        }
        if (position != null) {
            builder.position(position);
        }
        if (scanType != null) {
            builder.inputScanType(scanType);
        }
        if (clippings != null && !clippings.isEmpty()) {
            builder.inputClippings(clippings);
        }
        if (timecodeSource != null) {
            builder.timecodeSource(timecodeSource);
            if (InputTimecodeSource.SPECIFIEDSTART == timecodeSource) {
                builder.timecodeStart(timecodeStart);
            }
        }

        return builder.build();
    }

    public static OutputGroup buildOutputGroupForMp4(
            String name, String customName, String outputPath, S3ObjectCannedAcl acl, Output... output) {
        var settings = buildOutputGroupSettingsForFile(outputPath, acl);
        return OutputGroup.builder()
                .name(name)
                .customName(customName)
                .outputGroupSettings(settings)
                .outputs(output)
                .build();
    }

    public static OutputGroup buildOutputGroupForHLS(
            String name, String customName, String outputPath, S3ObjectCannedAcl acl, List<Output> outputs) {
        var hlsGroupSettings = buildHlsGroupSettings(outputPath, acl);
        var outputGroupSettings = OutputGroupSettings.builder()
                .type(OutputGroupType.HLS_GROUP_SETTINGS)
                .hlsGroupSettings(hlsGroupSettings)
                .build();
        return OutputGroup.builder()
                .name(name)
                .customName(customName)
                .outputGroupSettings(outputGroupSettings)
                .outputs(outputs)
                .build();
    }

    public static OutputGroup buildOutputGroupForThumbs(
            String name,
            String customName,
            String outputPath,
            S3ObjectCannedAcl acl,
            String ext,
            Integer width,
            Integer height,
            Integer maxCaptures,
            Integer quality,
            Integer sharpness) {
        var settings = buildOutputGroupSettingsForFile(outputPath, acl);
        var output = buildOutputForThumbs(ext, width, height, maxCaptures, quality, sharpness);
        return OutputGroup.builder()
                .name(name)
                .customName(customName)
                .outputGroupSettings(settings)
                .outputs(output)
                .build();
    }

    public static Output buildOutputForMp4(
            ContainerSettings container, AudioDescription audio, VideoDescription video) {
        return Output.builder()
                .extension("mp4")
                .containerSettings(container)
                .audioDescriptions(audio)
                .videoDescription(video)
                .build();
    }

    public static Output buildOutputForMp4(AudioDescription audio, VideoDescription video) {
        return buildOutputForMp4(defaultMP4Container(), audio, video);
    }

    public static Output buildOutputForMp4WithAV1QVBR(
            Integer width, Integer height, Integer sharpness, Integer maxBitrate, Integer qvbrQualityLevel) {
        var settings = buildAv1SettingsByQVBR(
                Av1AdaptiveQuantization.MEDIUM,
                Av1FramerateControl.INITIALIZE_FROM_SOURCE,
                null,
                null,
                81.0,
                maxBitrate,
                15,
                1,
                qvbrQualityLevel,
                null);
        return buildOutputForMp4WithAV1(width, height, sharpness, 160000, 48000, settings);
    }

    public static Output buildOutputForMp4WithAV1(
            Integer width,
            Integer height,
            Integer sharpness,
            Integer audioBitrate,
            Integer audioSampleRate,
            Av1Settings av1Settings) {
        var videoDescription = buildVideoDescriptionByAV1(width, height, sharpness, av1Settings);
        var audioDescription = buildAudioDescriptionByAAC("Audio Selector 1", 0, audioBitrate, audioSampleRate);
        return buildOutputForMp4(audioDescription, videoDescription);
    }

    public static Output buildOutputForMp4WithH264QVBR(
            Integer width,
            Integer height,
            Integer sharpness,
            Integer maxBitrate,
            Integer maxAverageBitRate,
            Integer qvbrQualityLevel,
            H264CodecProfile profile) {
        var settings = buildH264SettingsByQBVR(
                H264AdaptiveQuantization.AUTO,
                H264CodecLevel.AUTO,
                profile,
                20000000,
                maxBitrate,
                maxAverageBitRate,
                qvbrQualityLevel,
                1);
        return buildOutputForMp4WithH264(width, height, sharpness, 160000, 48000, settings);
    }

    public static Output buildOutputForMp4WithH264CBR(
            Integer width, Integer height, Integer sharpness, Integer bitRate, H264CodecProfile profile) {
        var settings = buildH264SettingsByCBR(
                H264AdaptiveQuantization.AUTO,
                H264CodecLevel.AUTO,
                profile,
                H264FramerateControl.INITIALIZE_FROM_SOURCE,
                null,
                null,
                H264GopSizeUnits.SECONDS,
                2.0,
                90,
                3000000,
                bitRate,
                3,
                3,
                1);
        return buildOutputForMp4WithH264(width, height, sharpness, 160000, 48000, settings);
    }

    public static Output buildOutputForMp4WithH264(
            Integer width,
            Integer height,
            Integer sharpness,
            Integer audioBitrate,
            Integer audioSampleRate,
            H264Settings h264Settings) {
        var videoDescription = buildVideoDescriptionByH264(width, height, sharpness, h264Settings);
        var audioDescription = buildAudioDescriptionByAAC("Audio Selector 1", 0, audioBitrate, audioSampleRate);
        return buildOutputForMp4(audioDescription, videoDescription);
    }

    public static Output buildOutputForMp4WithH265QVBR(
            Integer width,
            Integer height,
            Integer sharpness,
            Integer maxBitrate,
            Integer maxAverageBitRate,
            Integer qvbrQualityLevel,
            H265CodecProfile profile) {
        var settings = buildH265SettingsByQVBR(
                H265AdaptiveQuantization.AUTO,
                H265CodecLevel.AUTO,
                profile,
                H265QualityTuningLevel.SINGLE_PASS_HQ,
                9000000,
                maxBitrate,
                maxAverageBitRate,
                qvbrQualityLevel,
                1);
        return buildOutputForMp4WithH265(width, height, sharpness, 160000, 48000, settings);
    }

    public static Output buildOutputForMp4WithH265CBR(
            Integer width, Integer height, Integer sharpness, Integer bitrate, H265CodecProfile profile) {
        var settings = buildH265SettingsByCBR(
                H265AdaptiveQuantization.AUTO,
                H265CodecLevel.AUTO,
                profile,
                H265FlickerAdaptiveQuantization.DISABLED,
                H265QualityTuningLevel.SINGLE_PASS,
                H265FramerateControl.INITIALIZE_FROM_SOURCE,
                null,
                null,
                H265Tiles.DISABLED,
                H265GopSizeUnits.SECONDS,
                2.0,
                90,
                9000000,
                bitrate,
                3,
                3,
                1);
        return buildOutputForMp4WithH265(width, height, sharpness, 160000, 48000, settings);
    }

    public static Output buildOutputForMp4WithH265(
            Integer width,
            Integer height,
            Integer sharpness,
            Integer audioBitrate,
            Integer audioSampleRate,
            H265Settings h265Settings) {
        var videoDescription = buildVideoDescriptionByH265(width, height, sharpness, h265Settings);
        var audioDescription = buildAudioDescriptionByAAC("Audio Selector 1", 0, audioBitrate, audioSampleRate);
        return buildOutputForMp4(audioDescription, videoDescription);
    }

    public static Output buildOutputForAppleHLS(
            String nameModifier,
            String segmentModifier,
            ContainerSettings container,
            AudioDescription audio,
            VideoDescription video) {
        var hlsSettings = buildHlsSettings(segmentModifier, "program_audio", HlsIFrameOnlyManifest.EXCLUDE);
        var outputSettings = OutputSettings.builder().hlsSettings(hlsSettings).build();

        return Output.builder()
                .nameModifier(nameModifier)
                .outputSettings(outputSettings)
                .containerSettings(container)
                .audioDescriptions(audio)
                .videoDescription(video)
                .build();
    }

    public static Output buildOutputForAppleHLS(
            String nameModifier,
            String segmentModifier,
            Integer width,
            Integer height,
            Integer sharpness,
            M3u8Settings m3u8Settings,
            H264Settings h264Settings) {
        var containerSettings = ContainerSettings.builder()
                .container(ContainerType.M3_U8)
                .m3u8Settings(m3u8Settings)
                .build();
        var audioDescription = buildAudioDescriptionByAAC("Audio Selector 1", 0, 96000, 48000);
        var videoDescription = buildVideoDescriptionByH264(width, height, sharpness, h264Settings);
        return buildOutputForAppleHLS(
                nameModifier, segmentModifier, containerSettings, audioDescription, videoDescription);
    }

    public static Output buildOutputForThumbs(String ext, ContainerSettings container, VideoDescription video) {
        return Output.builder()
                .extension(ext)
                .containerSettings(container)
                .videoDescription(video)
                .build();
    }

    public static Output buildOutputForThumbs(
            String ext, Integer width, Integer height, Integer maxCaptures, Integer quality, Integer sharpness) {
        var frameCaptureSettings = FrameCaptureSettings.builder()
                .framerateNumerator(1)
                .framerateDenominator(1)
                .maxCaptures(maxCaptures)
                .quality(quality)
                .build();
        var codecSettings = VideoCodecSettings.builder()
                .codec(VideoCodec.FRAME_CAPTURE)
                .frameCaptureSettings(frameCaptureSettings)
                .build();
        var videoDescription = buildVideoDescription(width, height, sharpness, null, null, codecSettings, null);
        var containerSettings = buildContainerSettings(ContainerType.RAW);

        return buildOutputForThumbs(ext, containerSettings, videoDescription);
    }

    public static OutputGroupSettings buildOutputGroupSettingsForFile(String outputPath, S3ObjectCannedAcl acl) {
        return OutputGroupSettings.builder()
                .type(OutputGroupType.FILE_GROUP_SETTINGS)
                .fileGroupSettings(buildFileGroupSettings(outputPath, acl))
                .build();
    }

    public static DestinationSettings buildDestinationSettings(S3ObjectCannedAcl acl) {
        var accessControl = S3DestinationAccessControl.builder().cannedAcl(acl).build();
        var settings =
                S3DestinationSettings.builder().accessControl(accessControl).build();
        return DestinationSettings.builder().s3Settings(settings).build();
    }

    public static ContainerSettings buildContainerSettingsForM3U8(
            Integer audioFramesPerPes,
            Integer pmtPid,
            Integer privateMetadataPid,
            Integer scte35Pid,
            Integer timedMetadataPid,
            Integer videoPid,
            List<Integer> audioPid) {
        var m3u8Settings = buildM3u8Settings(
                audioFramesPerPes, pmtPid, privateMetadataPid, scte35Pid, timedMetadataPid, videoPid, audioPid);
        return ContainerSettings.builder()
                .container(ContainerType.M3_U8)
                .m3u8Settings(m3u8Settings)
                .build();
    }

    public static ContainerSettings buildContainerSettingsForMP4(
            Mp4CslgAtom cslgAtom, Mp4FreeSpaceBox freeSpaceBox, Mp4MoovPlacement moovPlacement) {
        var builder = Mp4Settings.builder();
        if (cslgAtom != null) {
            builder.cslgAtom(cslgAtom);
        }
        if (freeSpaceBox != null) {
            builder.freeSpaceBox(freeSpaceBox);
        }
        if (moovPlacement != null) {
            builder.moovPlacement(moovPlacement);
        }

        return ContainerSettings.builder()
                .container(ContainerType.MP4)
                .mp4Settings(builder.build())
                .build();
    }

    public static ContainerSettings buildContainerSettings(ContainerType type) {
        return ContainerSettings.builder().container(type).build();
    }

    public static VideoDescription buildVideoDescriptionByH264(
            Integer width, Integer height, Integer sharpness, H264Settings h264Settings) {
        var settings = VideoCodecSettings.builder()
                .codec(VideoCodec.H_264)
                .h264Settings(h264Settings)
                .build();
        var processor = defaultVideoPreprocessor();
        return buildVideoDescription(
                width, height, sharpness, RespondToAfd.NONE, AfdSignaling.NONE, settings, processor);
    }

    public static VideoDescription buildVideoDescriptionByH265(
            Integer width, Integer height, Integer sharpness, H265Settings h265Settings) {
        var settings = VideoCodecSettings.builder()
                .codec(VideoCodec.H_265)
                .h265Settings(h265Settings)
                .build();
        var processor = defaultVideoPreprocessor();
        return buildVideoDescription(
                width, height, sharpness, RespondToAfd.NONE, AfdSignaling.NONE, settings, processor);
    }

    public static VideoDescription buildVideoDescriptionByAV1(
            Integer width, Integer height, Integer sharpness, Av1Settings settings) {
        var codecSettings = VideoCodecSettings.builder()
                .codec(VideoCodec.AV1)
                .av1Settings(settings)
                .build();
        return buildVideoDescription(
                width, height, sharpness, RespondToAfd.NONE, AfdSignaling.NONE, codecSettings, null);
    }

    public static VideoDescription buildVideoDescription(
            Integer width,
            Integer height,
            Integer sharpness,
            RespondToAfd respondToAfd,
            AfdSignaling afdSignaling,
            VideoCodecSettings settings,
            VideoPreprocessor processor) {
        var builder = VideoDescription.builder();
        if (width != null) {
            builder.width(width);
        }
        if (height != null) {
            builder.height(height);
        }
        if (sharpness != null) {
            builder.sharpness(sharpness);
        }
        builder.scalingBehavior(ScalingBehavior.DEFAULT);
        builder.antiAlias(AntiAlias.ENABLED);
        builder.timecodeInsertion(VideoTimecodeInsertion.DISABLED);
        builder.colorMetadata(ColorMetadata.INSERT);
        builder.dropFrameTimecode(DropFrameTimecode.ENABLED);
        builder.codecSettings(settings);
        if (respondToAfd != null) {
            builder.respondToAfd(respondToAfd);
        }
        if (afdSignaling != null) {
            builder.afdSignaling(afdSignaling);
        }
        if (processor != null) {
            builder.videoPreprocessors(processor);
        }

        return builder.build();
    }

    public static H264Settings buildH264SettingsByQBVR(
            H264AdaptiveQuantization adaptiveQuantization,
            H264CodecLevel codecLevel,
            H264CodecProfile codecProfile,
            Integer hrdBufferSize,
            Integer maxBitRate,
            Integer maxAverageBitRate,
            Integer qvbrQualityLevel,
            Integer slices) {
        var builder = H264QvbrSettings.builder();
        if (maxAverageBitRate != null) {
            builder.maxAverageBitrate(maxAverageBitRate);
        }
        if (qvbrQualityLevel != null) {
            builder.qvbrQualityLevel(qvbrQualityLevel);
        }
        return buildH264SettingsByQBVR(
                adaptiveQuantization,
                codecLevel,
                codecProfile,
                H264DynamicSubGop.ADAPTIVE,
                H264FramerateControl.INITIALIZE_FROM_SOURCE,
                null,
                null,
                H264GopSizeUnits.SECONDS,
                2.0,
                90,
                hrdBufferSize,
                maxBitRate,
                3,
                3,
                slices,
                builder.build());
    }

    public static H264Settings buildH264SettingsByQBVR(
            H264AdaptiveQuantization adaptiveQuantization,
            H264CodecLevel codecLevel,
            H264CodecProfile codecProfile,
            H264DynamicSubGop dynamicSubGop,
            H264FramerateControl framerateControl,
            Integer frameRateDenominator,
            Integer frameRateNumerator,
            H264GopSizeUnits gopSizeUnits,
            Double gopSize,
            Integer hrdBufferInitialFillPercentage,
            Integer hrdBufferSize,
            Integer maxBitRate,
            Integer numberBFramesBetweenReferenceFrames,
            Integer numberReferenceFrames,
            Integer slices,
            H264QvbrSettings settings) {
        return buildH264Settings(
                adaptiveQuantization,
                codecLevel,
                codecProfile,
                dynamicSubGop,
                H264FlickerAdaptiveQuantization.ENABLED,
                H264QualityTuningLevel.SINGLE_PASS_HQ,
                framerateControl,
                frameRateDenominator,
                frameRateNumerator,
                H264GopBReference.ENABLED,
                gopSizeUnits,
                gopSize,
                hrdBufferInitialFillPercentage,
                hrdBufferSize,
                null,
                maxBitRate,
                numberBFramesBetweenReferenceFrames,
                numberReferenceFrames,
                slices,
                H264RateControlMode.QVBR,
                settings);
    }

    public static H264Settings buildH264SettingsByCBR(
            H264AdaptiveQuantization adaptiveQuantization,
            H264CodecLevel codecLevel,
            H264CodecProfile codecProfile,
            H264FramerateControl framerateControl,
            Integer frameRateDenominator,
            Integer frameRateNumerator,
            H264GopSizeUnits gopSizeUnits,
            Double gopSize,
            Integer hrdBufferInitialFillPercentage,
            Integer hrdBufferSize,
            Integer bitRate,
            Integer numberBFramesBetweenReferenceFrames,
            Integer numberReferenceFrames,
            Integer slices) {
        return buildH264Settings(
                adaptiveQuantization,
                codecLevel,
                codecProfile,
                null,
                H264FlickerAdaptiveQuantization.ENABLED,
                H264QualityTuningLevel.SINGLE_PASS,
                framerateControl,
                frameRateDenominator,
                frameRateNumerator,
                H264GopBReference.DISABLED,
                gopSizeUnits,
                gopSize,
                hrdBufferInitialFillPercentage,
                hrdBufferSize,
                bitRate,
                null,
                numberBFramesBetweenReferenceFrames,
                numberReferenceFrames,
                slices,
                H264RateControlMode.CBR,
                null);
    }

    public static H264Settings buildH264Settings(
            H264AdaptiveQuantization adaptiveQuantization,
            H264CodecLevel codecLevel,
            H264CodecProfile codecProfile,
            H264DynamicSubGop dynamicSubGop,
            H264FlickerAdaptiveQuantization flickerAdaptiveQuantization,
            H264QualityTuningLevel qualityTuningLevel,
            H264FramerateControl frameRateControl,
            Integer frameRateDenominator,
            Integer frameRateNumerator,
            H264GopBReference gopBReference,
            H264GopSizeUnits gopSizeUnits,
            Double gopSize,
            Integer hrdBufferInitialFillPercentage,
            Integer hrdBufferSize,
            Integer bitRate,
            Integer maxBitRate,
            Integer numberBFramesBetweenReferenceFrames,
            Integer numberReferenceFrames,
            Integer slices,
            H264RateControlMode rateControlMode,
            H264QvbrSettings qvbrSettings) {
        var builder = H264Settings.builder();
        builder.entropyEncoding(H264EntropyEncoding.CABAC);
        builder.fieldEncoding(H264FieldEncoding.PAFF);
        builder.framerateConversionAlgorithm(H264FramerateConversionAlgorithm.DUPLICATE_DROP);
        builder.gopClosedCadence(1);
        builder.interlaceMode(H264InterlaceMode.PROGRESSIVE);
        builder.minIInterval(0);
        builder.repeatPps(H264RepeatPps.DISABLED);
        builder.sceneChangeDetect(H264SceneChangeDetect.ENABLED);
        builder.slowPal(H264SlowPal.DISABLED);
        builder.syntax(H264Syntax.DEFAULT);
        builder.telecine(H264Telecine.NONE);
        builder.unregisteredSeiTimecode(H264UnregisteredSeiTimecode.DISABLED);
        builder.parControl(H264ParControl.SPECIFIED);
        builder.parDenominator(1);
        builder.parNumerator(1);
        builder.softness(0);

        if (adaptiveQuantization != null) {
            builder.adaptiveQuantization(adaptiveQuantization);
            if (H264AdaptiveQuantization.AUTO != adaptiveQuantization) {
                builder.spatialAdaptiveQuantization(H264SpatialAdaptiveQuantization.ENABLED);
                builder.temporalAdaptiveQuantization(H264TemporalAdaptiveQuantization.ENABLED);
                if (flickerAdaptiveQuantization != null) {
                    builder.flickerAdaptiveQuantization(flickerAdaptiveQuantization);
                }
            }
        }
        if (bitRate != null) {
            builder.bitrate(bitRate);
        }
        if (codecLevel != null) {
            builder.codecLevel(codecLevel);
        }
        if (codecProfile != null) {
            builder.codecProfile(codecProfile);
        }
        if (dynamicSubGop != null) {
            builder.dynamicSubGop(dynamicSubGop);
        }
        if (frameRateControl != null) {
            builder.framerateControl(frameRateControl);
            if (H264FramerateControl.SPECIFIED == frameRateControl) {
                if (frameRateDenominator != null && frameRateNumerator != null) {
                    builder.framerateDenominator(frameRateDenominator);
                    builder.framerateNumerator(frameRateNumerator);
                }
            }
        }
        if (gopBReference != null) {
            builder.gopBReference(gopBReference);
        }
        if (gopSize != null) {
            builder.gopSize(gopSize);
        }
        if (gopSizeUnits != null) {
            builder.gopSizeUnits(gopSizeUnits);
        }
        if (hrdBufferInitialFillPercentage != null) {
            builder.hrdBufferInitialFillPercentage(hrdBufferInitialFillPercentage);
        }
        if (hrdBufferSize != null) {
            builder.hrdBufferSize(hrdBufferSize);
        }
        if (maxBitRate != null) {
            builder.maxBitrate(maxBitRate);
        }
        if (numberBFramesBetweenReferenceFrames != null) {
            builder.numberBFramesBetweenReferenceFrames(numberBFramesBetweenReferenceFrames);
        }
        if (numberReferenceFrames != null) {
            builder.numberReferenceFrames(3);
        }
        if (qualityTuningLevel != null) {
            builder.qualityTuningLevel(qualityTuningLevel);
        }
        builder.rateControlMode(rateControlMode);
        if (H264RateControlMode.QVBR == rateControlMode && qvbrSettings != null) {
            builder.qvbrSettings(qvbrSettings);
        }
        if (slices != null) {
            builder.slices(slices);
        }
        return builder.build();
    }

    public static H265Settings buildH265SettingsByQVBR(
            H265AdaptiveQuantization adaptiveQuantization,
            H265CodecLevel codecLevel,
            H265CodecProfile codecProfile,
            H265QualityTuningLevel qualityTuningLevel,
            Integer hrdBufferSize,
            Integer maxBitRate,
            Integer maxAverageBitRate,
            Integer qvbrQualityLevel,
            Integer slices) {
        var builder = H265QvbrSettings.builder();
        if (maxAverageBitRate != null) {
            builder.maxAverageBitrate(maxAverageBitRate);
        }
        if (qvbrQualityLevel != null) {
            builder.qvbrQualityLevel(qvbrQualityLevel);
        }

        return buildH265SettingsByQVBR(
                adaptiveQuantization,
                codecLevel,
                codecProfile,
                H265DynamicSubGop.ADAPTIVE,
                H265FlickerAdaptiveQuantization.ENABLED,
                qualityTuningLevel,
                H265FramerateControl.INITIALIZE_FROM_SOURCE,
                null,
                null,
                H265Tiles.ENABLED,
                H265GopSizeUnits.SECONDS,
                2.0,
                90,
                hrdBufferSize,
                maxBitRate,
                3,
                3,
                slices,
                builder.build());
    }

    public static H265Settings buildH265SettingsByQVBR(
            H265AdaptiveQuantization adaptiveQuantization,
            H265CodecLevel codecLevel,
            H265CodecProfile codecProfile,
            H265DynamicSubGop dynamicSubGop,
            H265FlickerAdaptiveQuantization flickerAdaptiveQuantization,
            H265QualityTuningLevel qualityTuningLevel,
            H265FramerateControl frameRateControl,
            Integer frameRateDenominator,
            Integer frameRateNumerator,
            H265Tiles tiles,
            H265GopSizeUnits gopSizeUnits,
            Double gopSize,
            Integer hrdBufferInitialFillPercentage,
            Integer hrdBufferSize,
            Integer maxBitRate,
            Integer numberBFramesBetweenReferenceFrames,
            Integer numberReferenceFrames,
            Integer slices,
            H265QvbrSettings qvbrSettings) {
        return buildH265Settings(
                adaptiveQuantization,
                codecLevel,
                codecProfile,
                dynamicSubGop,
                flickerAdaptiveQuantization,
                H265ParControl.SPECIFIED,
                qualityTuningLevel,
                frameRateControl,
                frameRateDenominator,
                frameRateNumerator,
                tiles,
                H265GopBReference.ENABLED,
                gopSizeUnits,
                gopSize,
                hrdBufferInitialFillPercentage,
                hrdBufferSize,
                null,
                maxBitRate,
                numberBFramesBetweenReferenceFrames,
                numberReferenceFrames,
                slices,
                H265RateControlMode.QVBR,
                qvbrSettings);
    }

    public static H265Settings buildH265SettingsByCBR(
            H265AdaptiveQuantization adaptiveQuantization,
            H265CodecLevel codecLevel,
            H265CodecProfile codecProfile,
            H265FlickerAdaptiveQuantization flickerAdaptiveQuantization,
            H265QualityTuningLevel qualityTuningLevel,
            H265FramerateControl frameRateControl,
            Integer frameRateDenominator,
            Integer frameRateNumerator,
            H265Tiles tiles,
            H265GopSizeUnits gopSizeUnits,
            Double gopSize,
            Integer hrdBufferInitialFillPercentage,
            Integer hrdBufferSize,
            Integer bitRate,
            Integer numberBFramesBetweenReferenceFrames,
            Integer numberReferenceFrames,
            Integer slices) {
        return buildH265Settings(
                adaptiveQuantization,
                codecLevel,
                codecProfile,
                null,
                flickerAdaptiveQuantization,
                H265ParControl.SPECIFIED,
                qualityTuningLevel,
                frameRateControl,
                frameRateDenominator,
                frameRateNumerator,
                tiles,
                H265GopBReference.ENABLED,
                gopSizeUnits,
                gopSize,
                hrdBufferInitialFillPercentage,
                hrdBufferSize,
                bitRate,
                null,
                numberBFramesBetweenReferenceFrames,
                numberReferenceFrames,
                slices,
                H265RateControlMode.CBR,
                null);
    }

    public static H265Settings buildH265Settings(
            H265AdaptiveQuantization adaptiveQuantization,
            H265CodecLevel codecLevel,
            H265CodecProfile codecProfile,
            H265DynamicSubGop dynamicSubGop,
            H265FlickerAdaptiveQuantization flickerAdaptiveQuantization,
            H265ParControl parControl,
            H265QualityTuningLevel qualityTuningLevel,
            H265FramerateControl frameRateControl,
            Integer frameRateDenominator,
            Integer frameRateNumerator,
            H265Tiles tiles,
            H265GopBReference gopBReference,
            H265GopSizeUnits gopSizeUnits,
            Double gopSize,
            Integer hrdBufferInitialFillPercentage,
            Integer hrdBufferSize,
            Integer bitRate,
            Integer maxBitRate,
            Integer numberBFramesBetweenReferenceFrames,
            Integer numberReferenceFrames,
            Integer slices,
            H265RateControlMode rateControlMode,
            H265QvbrSettings qvbrSettings) {
        var builder = H265Settings.builder();
        builder.framerateConversionAlgorithm(H265FramerateConversionAlgorithm.DUPLICATE_DROP);
        builder.gopClosedCadence(1);
        builder.interlaceMode(H265InterlaceMode.PROGRESSIVE);
        builder.minIInterval(0);
        builder.rateControlMode(rateControlMode);
        builder.sceneChangeDetect(H265SceneChangeDetect.ENABLED);
        builder.sampleAdaptiveOffsetFilterMode(H265SampleAdaptiveOffsetFilterMode.ADAPTIVE);
        builder.slowPal(H265SlowPal.DISABLED);
        builder.telecine(H265Telecine.NONE);
        builder.unregisteredSeiTimecode(H265UnregisteredSeiTimecode.DISABLED);

        if (adaptiveQuantization != null) {
            builder.adaptiveQuantization(adaptiveQuantization);
            if (H265AdaptiveQuantization.AUTO != adaptiveQuantization) {
                builder.spatialAdaptiveQuantization(H265SpatialAdaptiveQuantization.ENABLED);
                builder.temporalAdaptiveQuantization(H265TemporalAdaptiveQuantization.ENABLED);
                if (flickerAdaptiveQuantization != null) {
                    builder.flickerAdaptiveQuantization(flickerAdaptiveQuantization);
                }
            }
        }
        if (bitRate != null) {
            builder.bitrate(bitRate);
        }
        if (codecLevel != null) {
            builder.codecLevel(codecLevel);
        }
        if (codecProfile != null) {
            builder.codecProfile(codecProfile);
        }
        if (dynamicSubGop != null) {
            builder.dynamicSubGop(dynamicSubGop);
        }
        if (frameRateControl != null) {
            builder.framerateControl(frameRateControl);
            if (H265FramerateControl.SPECIFIED == frameRateControl) {
                if (frameRateDenominator != null && frameRateNumerator != null) {
                    builder.framerateDenominator(frameRateDenominator);
                    builder.framerateNumerator(frameRateNumerator);
                }
            }
        }
        if (gopBReference != null) {
            builder.gopBReference(gopBReference);
        }
        if (gopSize != null) {
            builder.gopSize(gopSize);
        }
        if (gopSizeUnits != null) {
            builder.gopSizeUnits(gopSizeUnits);
        }
        if (hrdBufferInitialFillPercentage != null) {
            builder.hrdBufferInitialFillPercentage(hrdBufferInitialFillPercentage);
        }
        if (hrdBufferSize != null) {
            builder.hrdBufferSize(hrdBufferSize);
        }
        if (maxBitRate != null) {
            builder.maxBitrate(maxBitRate);
        }
        if (numberBFramesBetweenReferenceFrames != null) {
            builder.numberBFramesBetweenReferenceFrames(numberBFramesBetweenReferenceFrames);
        }
        if (numberReferenceFrames != null) {
            builder.numberReferenceFrames(3);
        }
        if (parControl != null) {
            builder.parControl(parControl);
            if (H265ParControl.SPECIFIED == parControl) {
                builder.parDenominator(1);
                builder.parNumerator(1);
            }
        }
        if (qualityTuningLevel != null) {
            builder.qualityTuningLevel(qualityTuningLevel);
        }
        if (slices != null) {
            builder.slices(slices);
        }
        if (tiles != null) {
            builder.tiles(tiles);
        }
        if (H265RateControlMode.QVBR == rateControlMode && qvbrSettings != null) {
            builder.qvbrSettings(qvbrSettings);
        }
        return builder.build();
    }

    public static Av1Settings buildAv1SettingsByQVBR(
            Av1AdaptiveQuantization adaptiveQuantization,
            Av1FramerateControl frameRateControl,
            Integer frameRateDenominator,
            Integer frameRateNumerator,
            Double gopSize,
            Integer maxBitRate,
            Integer numberBFramesBetweenReferenceFrames,
            Integer slices,
            Integer qvbrQualityLevel,
            Double qvbrQualityLevelFineTune) {
        var builder = Av1QvbrSettings.builder();
        if (qvbrQualityLevel != null) {
            builder.qvbrQualityLevel(qvbrQualityLevel);
        }
        if (qvbrQualityLevelFineTune != null) {
            builder.qvbrQualityLevelFineTune(qvbrQualityLevelFineTune);
        }
        return buildAv1Settings(
                adaptiveQuantization,
                null,
                null,
                Av1FramerateConversionAlgorithm.DUPLICATE_DROP,
                frameRateControl,
                frameRateDenominator,
                frameRateNumerator,
                gopSize,
                maxBitRate,
                numberBFramesBetweenReferenceFrames,
                slices,
                Av1RateControlMode.QVBR,
                builder.build());
    }

    public static Av1Settings buildAv1Settings(
            Av1AdaptiveQuantization adaptiveQuantization,
            Av1FilmGrainSynthesis filmGrainSynthesis,
            Av1BitDepth bitDepth,
            Av1FramerateConversionAlgorithm frameRateConversionAlgorithm,
            Av1FramerateControl frameRateControl,
            Integer frameRateDenominator,
            Integer frameRateNumerator,
            Double gopSize,
            Integer maxBitRate,
            Integer numberBFramesBetweenReferenceFrames,
            Integer slices,
            Av1RateControlMode rateControlMode,
            Av1QvbrSettings qvbrSettings) {
        var builder = Av1Settings.builder();
        if (bitDepth != null) {
            builder.bitDepth(bitDepth);
        }
        if (filmGrainSynthesis != null) {
            builder.filmGrainSynthesis(filmGrainSynthesis);
        }
        if (adaptiveQuantization != null) {
            builder.adaptiveQuantization(adaptiveQuantization);
        }
        if (frameRateConversionAlgorithm != null) {
            builder.framerateConversionAlgorithm(frameRateConversionAlgorithm);
        }
        if (frameRateControl != null) {
            builder.framerateControl(frameRateControl);
            if (Av1FramerateControl.SPECIFIED == frameRateControl) {
                if (frameRateDenominator != null && frameRateNumerator != null) {
                    builder.framerateDenominator(frameRateDenominator);
                    builder.framerateNumerator(frameRateNumerator);
                }
            }
        }
        if (gopSize != null) {
            builder.gopSize(gopSize);
        }
        if (maxBitRate != null) {
            builder.maxBitrate(maxBitRate);
        }
        if (numberBFramesBetweenReferenceFrames != null) {
            builder.numberBFramesBetweenReferenceFrames(numberBFramesBetweenReferenceFrames);
        }
        if (slices != null) {
            builder.slices(slices);
        }
        builder.rateControlMode(rateControlMode);
        if (Av1RateControlMode.QVBR == rateControlMode && qvbrSettings != null) {
            builder.qvbrSettings(qvbrSettings);
        }
        builder.spatialAdaptiveQuantization(Av1SpatialAdaptiveQuantization.ENABLED);

        return builder.build();
    }

    public static AudioDescription buildAudioDescription(String name, Integer audioType, AudioCodecSettings settings) {
        return AudioDescription.builder()
                .audioSourceName(name)
                .audioType(audioType)
                .audioTypeControl(AudioTypeControl.FOLLOW_INPUT)
                .languageCodeControl(AudioLanguageCodeControl.FOLLOW_INPUT)
                .codecSettings(settings)
                .build();
    }

    public static AudioDescription buildAudioDescriptionByAAC(
            String name, Integer audioType, Integer bitRate, Integer sampleRate) {
        var aacSettings = buildAacSettings(bitRate, sampleRate);
        var settings = AudioCodecSettings.builder()
                .codec(AudioCodec.AAC)
                .aacSettings(aacSettings)
                .build();
        return buildAudioDescription(name, audioType, settings);
    }

    public static AacSettings buildAacSettings(Integer bitRate, Integer sampleRate) {
        return AacSettings.builder()
                .codecProfile(AacCodecProfile.LC)
                .rateControlMode(AacRateControlMode.CBR)
                .codingMode(AacCodingMode.CODING_MODE_2_0)
                .bitrate(bitRate)
                .sampleRate(sampleRate)
                .rawFormat(AacRawFormat.NONE)
                .specification(AacSpecification.MPEG4)
                .audioDescriptionBroadcasterMix(AacAudioDescriptionBroadcasterMix.NORMAL)
                .build();
    }

    public static VideoPreprocessor defaultVideoPreprocessor() {
        var deinterlacer = Deinterlacer.builder()
                .algorithm(DeinterlaceAlgorithm.INTERPOLATE)
                .control(DeinterlacerControl.NORMAL)
                .mode(DeinterlacerMode.DEINTERLACE)
                .build();
        return VideoPreprocessor.builder().deinterlacer(deinterlacer).build();
    }

    public static FileGroupSettings buildFileGroupSettings(String outputPath, S3ObjectCannedAcl acl) {
        var builder = FileGroupSettings.builder();
        builder.destination(outputPath);
        if (acl != null) {
            builder.destinationSettings(buildDestinationSettings(acl));
        }
        return builder.build();
    }

    public static HlsGroupSettings buildHlsGroupSettings(String outputPath, S3ObjectCannedAcl acl) {
        var builder = HlsGroupSettings.builder();
        builder.directoryStructure(HlsDirectoryStructure.SINGLE_DIRECTORY);
        builder.manifestDurationFormat(HlsManifestDurationFormat.INTEGER);
        builder.streamInfResolution(HlsStreamInfResolution.INCLUDE);
        builder.clientCache(HlsClientCache.ENABLED);
        builder.captionLanguageSetting(HlsCaptionLanguageSetting.OMIT);
        builder.manifestCompression(HlsManifestCompression.NONE);
        builder.codecSpecification(HlsCodecSpecification.RFC_4281);
        builder.outputSelection(HlsOutputSelection.MANIFESTS_AND_SEGMENTS);
        builder.programDateTime(HlsProgramDateTime.EXCLUDE);
        builder.programDateTimePeriod(600);
        builder.timedMetadataId3Frame(HlsTimedMetadataId3Frame.PRIV);
        builder.timedMetadataId3Period(10);
        builder.destination(outputPath);
        if (acl != null) {
            builder.destinationSettings(buildDestinationSettings(acl));
        }
        builder.segmentControl(HlsSegmentControl.SEGMENTED_FILES);
        builder.minFinalSegmentLength(0.0);
        builder.segmentLength(4);
        builder.minSegmentLength(0);
        return builder.build();
    }

    public static HlsSettings buildHlsSettings(String modifier, String groupId, HlsIFrameOnlyManifest manifest) {
        return HlsSettings.builder()
                .segmentModifier(modifier)
                .audioGroupId(groupId)
                .iFrameOnlyManifest(manifest)
                .build();
    }

    public static M3u8Settings buildM3u8Settings(
            Integer audioFramesPerPes,
            Integer pmtPid,
            Integer privateMetadataPid,
            Integer scte35Pid,
            Integer timedMetadataPid,
            Integer videoPid,
            List<Integer> audioPid) {
        return M3u8Settings.builder()
                .audioFramesPerPes(audioFramesPerPes)
                .pcrControl(M3u8PcrControl.PCR_EVERY_PES_PACKET)
                .pmtPid(pmtPid)
                .privateMetadataPid(privateMetadataPid)
                .programNumber(1)
                .patInterval(0)
                .pmtInterval(0)
                .scte35Source(M3u8Scte35Source.NONE)
                .scte35Pid(scte35Pid)
                .nielsenId3(M3u8NielsenId3.NONE)
                .timedMetadata(TimedMetadata.NONE)
                .timedMetadataPid(timedMetadataPid)
                .videoPid(videoPid)
                .audioPids(audioPid)
                .build();
    }

    @Override
    public void close() throws Exception {
        this.client.close();
    }
}
