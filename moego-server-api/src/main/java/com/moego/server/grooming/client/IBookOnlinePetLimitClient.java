package com.moego.server.grooming.client;

import com.moego.server.grooming.api.IBookOnlinePetLimitService;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * <AUTHOR>
 */
@FeignClient(
        value = "moego-grooming-server",
        url = "${moego.server.url.grooming}",
        contextId = "IBookOnlinePetLimitClient")
public interface IBookOnlinePetLimitClient extends IBookOnlinePetLimitService {}
