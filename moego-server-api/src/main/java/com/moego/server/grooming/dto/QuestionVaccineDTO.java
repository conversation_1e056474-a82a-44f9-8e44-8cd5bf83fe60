package com.moego.server.grooming.dto;

import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import lombok.Getter;
import lombok.Setter;

@JsonDeserialize(using = QuestionVaccineDTO.QuestionVaccineDTODeserializer.class)
public class QuestionVaccineDTO {

    public static final String KEY_specificVaccineIds = "specificVaccineIds";
    public static final String KEY_basedOnVaccineSettings = "basedOnVaccineSettings";
    public static final String KEY_showExpirationDate = "showExpirationDate";
    public static final String KEY_requireExpirationDate = "requireExpirationDate";
    public static final String KEY_showVaccineDocument = "showVaccineDocument";
    public static final String KEY_requireVaccineDocument = "requireVaccineDocument";

    private static final Set<String> KNOWN_KEYS = Set.of(
            KEY_specificVaccineIds,
            KEY_basedOnVaccineSettings,
            KEY_showExpirationDate,
            KEY_requireExpirationDate,
            KEY_showVaccineDocument,
            KEY_requireVaccineDocument);

    @Getter
    @Setter
    private List<Integer> specificVaccineIds;

    @Getter
    @Setter
    private Boolean basedOnVaccineSettings;

    @Getter
    @Setter
    private Boolean showExpirationDate;

    @Getter
    @Setter
    private Boolean requireExpirationDate;

    @Getter
    @Setter
    private Boolean showVaccineDocument;

    @Getter
    @Setter
    private Boolean requireVaccineDocument;

    private final Map<String, Object> otherProperties = new HashMap<>();

    @JsonAnySetter
    public void setOtherProperty(String key, Object value) {
        otherProperties.put(key, value);
    }

    @JsonAnyGetter
    public Map<String, Object> getOtherProperties() {
        return otherProperties;
    }

    public static class QuestionVaccineDTODeserializer extends JsonDeserializer<QuestionVaccineDTO> {
        @Override
        public QuestionVaccineDTO deserialize(JsonParser jp, DeserializationContext ctx) throws IOException {
            JsonNode node = jp.getCodec().readTree(jp);
            QuestionVaccineDTO dto = new QuestionVaccineDTO();

            if (node.isArray()) {
                // 旧结构: [1, 2, 3]
                List<Integer> specificVaccineIds = new ArrayList<>();
                for (JsonNode element : node) {
                    specificVaccineIds.add(element.asInt());
                }
                dto.setSpecificVaccineIds(specificVaccineIds);
                dto.setBasedOnVaccineSettings(false);
            } else {
                // 新结构:
                // {
                //     "specificVaccineIds": [1, 2, 3],
                //     "basedOnVaccineSettings": true,
                //     "showExpirationDate": true,
                //     "requireExpirationDate": true,
                //     "showVaccineDocument": true,
                //     "requireVaccineDocument": true
                // }
                if (node.has(KEY_specificVaccineIds)) {
                    List<Integer> specificVaccineIds = new ArrayList<>();
                    JsonNode idsNode = node.get(KEY_specificVaccineIds);
                    if (idsNode.isArray()) {
                        for (JsonNode idNode : idsNode) {
                            specificVaccineIds.add(idNode.asInt());
                        }
                    }
                    dto.setSpecificVaccineIds(specificVaccineIds);
                }

                if (node.has(KEY_basedOnVaccineSettings)) {
                    dto.setBasedOnVaccineSettings(
                            node.get(KEY_basedOnVaccineSettings).asBoolean());
                }

                if (node.has(KEY_showExpirationDate)) {
                    dto.setShowExpirationDate(node.get(KEY_showExpirationDate).asBoolean());
                }
                if (node.has(KEY_requireExpirationDate)) {
                    dto.setRequireExpirationDate(
                            node.get(KEY_requireExpirationDate).asBoolean());
                }
                if (node.has(KEY_showVaccineDocument)) {
                    dto.setShowVaccineDocument(node.get(KEY_showVaccineDocument).asBoolean());
                }
                if (node.has(KEY_requireVaccineDocument)) {
                    dto.setRequireVaccineDocument(
                            node.get(KEY_requireVaccineDocument).asBoolean());
                }

                // 处理其他属性
                node.fields().forEachRemaining(entry -> {
                    if (!KNOWN_KEYS.contains(entry.getKey())) {
                        dto.setOtherProperty(entry.getKey(), entry.getValue());
                    }
                });
            }

            return dto;
        }
    }
}
