package com.moego.server.grooming.params.groomingreport;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import java.util.List;
import lombok.Data;

@Data
public class GroomingReportSettingParams {

    @JsonIgnore
    private Integer businessId;

    @Schema(description = "sending type: 1-manually, 2-automatically")
    @Max(2)
    @Min(1)
    private Integer sendingType;

    @Schema(description = "sending method: 1-sms, 2-email")
    private List<Byte> sendingMethodList;

    @JsonIgnore
    private Integer updateBy;
}
