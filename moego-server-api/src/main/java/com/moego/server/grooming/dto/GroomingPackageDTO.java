package com.moego.server.grooming.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;

@Data
public class GroomingPackageDTO {

    private Integer id;
    private Integer cartPackageId;
    private Integer customerId;
    private Integer staffId;
    private Integer retailInvoiceItemId;
    private String confirmationId;
    private String packageName;
    private String packageDesc;
    private BigDecimal packagePrice;
    private Long purchaseTime;
    private Long startTime;
    private Long endTime;
    private Long createTime;
    private Long updateTime;
    private Integer status;
    private Boolean used;
    private Integer totalRemainingQuantity;
    private boolean applied;

    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(type = "string", description = "business id")
    private Integer businessId;

    @Schema(description = "format: yyyy-MM-dd, 9999-01-01 means never expired")
    private String expirationDate;

    public interface ExpirationDate {
        String NEVER_EXPIRE = "9999-01-01";
    }
}
