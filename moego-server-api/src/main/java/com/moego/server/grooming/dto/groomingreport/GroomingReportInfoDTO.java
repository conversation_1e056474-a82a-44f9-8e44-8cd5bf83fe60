package com.moego.server.grooming.dto.groomingreport;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;

/**
 * Grooming report 记录，moe_grooming_report 表对象
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class GroomingReportInfoDTO {

    @Schema(description = "grooming report id")
    private Integer id;

    private Long companyId;
    private Integer businessId;
    private Integer customerId;
    private Integer groomingId;
    private Integer petId;

    @Schema(description = "pet type id: 1-dog,2-cat")
    private Integer petTypeId;

    @Schema(description = "unique id for grooming report")
    private String uuid;

    @Schema(description = "unique id for share")
    private String uuidForShare;

    @Schema(description = "grooming report used template published time")
    private Long templatePublishTime;

    @Schema(description = "grooming report status")
    private String status;

    @Schema(description = "last update staff id")
    private Integer updateBy;

    @Schema(description = "last submitted time")
    private Long submittedTime;

    @Schema(description = "link opened count")
    private Integer linkOpenedCount;

    private Long createTime;
    private Long updateTime;

    @Schema(description = "theme code")
    private String themeCode;

    @Schema(description = "current used template")
    private GroomingReportTemplate template;

    @Schema(description = "fill in content")
    private GroomingReportContent content;

    @Data
    public static class GroomingReportContent {

        @Schema(description = "feedback questions")
        private List<GroomingReportQuestion> feedbacks;

        @Schema(description = "pet condition questions")
        private List<GroomingReportQuestion> petConditions;

        @Schema(description = "showcase: before and after urls")
        private List<String> showcase;

        @Schema(description = "recommendation")
        private GroomingRecommendation recommendation;
    }

    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class GroomingReportTemplate {

        private String thankYouMessage;
        private String themeColor;
        private String lightThemeColor;
        private String themeCode;
        private Boolean showShowcase;
        private Boolean showOverallFeedback;
        private Boolean requireBeforePhoto;
        private Boolean requireAfterPhoto;
        private Boolean showPetCondition;
        private Boolean showServiceStaffName;
        private Boolean showNextAppointment;
        private Byte nextAppointmentDateFormatType;
        private Boolean showReviewBooster;
        private Boolean showYelpReview;
        private String yelpReviewLink;
        private Boolean showGoogleReview;
        private String googleReviewLink;
        private Boolean showFacebookReview;
        private String facebookReviewLink;
        private String title;
    }

    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class GroomingReportQuestion {

        private Integer id;
        private String category;
        private String type;
        private String key;
        private String title;
        private Boolean required;
        private Boolean show;
        // extra json 解析字段
        private List<String> options;
        private List<String> choices;
        private List<String> customOptions; // 前端自定义添加选项，默认选中
        private String text;
        private String placeholder;
        private BodyViewUrl urls;
    }
}
