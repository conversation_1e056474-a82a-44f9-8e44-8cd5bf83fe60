package com.moego.server.grooming.api;

import com.moego.common.enums.BookOnlineDepositConst;
import com.moego.common.enums.DepositPaymentTypeEnum;
import com.moego.server.grooming.dto.BookOnlineDepositDTO;
import com.moego.server.grooming.dto.BookOnlineDepositDetailDTO;
import com.moego.server.grooming.dto.DepositDto;
import com.moego.server.grooming.params.MoeBookOnlineDepositBatchQueryByCompanyVO;
import com.moego.server.grooming.params.MoeBookOnlineDepositBatchQueryVO;
import com.moego.server.grooming.params.MoeBookOnlineDepositVO;
import jakarta.annotation.Nullable;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;
import lombok.Builder;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

public interface IBookOnlineDepositService {
    @PostMapping("/service/grooming/ob/deposit/createOrUpdate")
    Boolean createOrUpdateOBDeposit(@RequestBody MoeBookOnlineDepositVO obDepositVo);

    @PostMapping("/service/grooming/ob/deposit/update")
    Boolean updateOBDepositByPaymentId(@RequestBody MoeBookOnlineDepositVO depositVo);

    @GetMapping("/service/grooming/ob/deposit")
    BookOnlineDepositDTO getOBDepositByPaymentId(
            @RequestParam("businessId") Integer businessId, @RequestParam("paymentId") Integer paymentId);

    @GetMapping("/service/grooming/ob/deposits")
    @Deprecated
    List<BookOnlineDepositDTO> getOBDepositByPaymentIds(
            @RequestParam("businessId") Integer businessId, @RequestParam("paymentId") Set<Integer> paymentIds);

    @PostMapping("/service/grooming/ob/deposits")
    List<BookOnlineDepositDTO> getOBDepositByPaymentIds(@RequestBody MoeBookOnlineDepositBatchQueryVO req);

    @PostMapping("/service/grooming/ob/v2/deposits")
    List<BookOnlineDepositDTO> getOBDepositByPaymentIdsV2(@RequestBody MoeBookOnlineDepositBatchQueryByCompanyVO req);

    @GetMapping("/service/grooming/ob/deposit/by/groomingId")
    BookOnlineDepositDTO getOBDepositByGroomingId(
            @RequestParam("businessId") Integer businessId, @RequestParam("groomingId") Integer groomingId);

    /**
     * Get ob deposit by bookingRequestId, return null if not found.
     *
     * @param businessId       businessId, nullable
     * @param bookingRequestId bookingRequestId
     * @return {@link BookOnlineDepositDTO}
     */
    @Nullable
    @GetMapping("/service/grooming/ob/deposit/getOBDepositByBookingRequestId")
    BookOnlineDepositDTO getOBDepositByBookingRequestId(
            @RequestParam(required = false) @Nullable Integer businessId,
            @RequestParam("bookingRequestId") long bookingRequestId);

    /**
     * List ob deposit by bookingRequestIds.
     *
     * @param businessId        businessId, nullable
     * @param bookingRequestIds bookingRequestIds
     * @return list of {@link BookOnlineDepositDTO}
     */
    @PostMapping("/service/grooming/ob/deposit/getOBDepositByBookingRequestIds")
    List<BookOnlineDepositDTO> listOBDepositByBookingRequestIds(
            @RequestParam(required = false) @Nullable Integer businessId,
            @RequestBody Collection<Long> bookingRequestIds);

    @GetMapping("/service/grooming/ob/deposit/by/groomingIds")
    List<BookOnlineDepositDTO> getOBDepositByGroomingIds(@RequestParam("groomingIds") Collection<Integer> groomingIds);

    @PostMapping("/service/grooming/ob/deposit/update/by/groomingId")
    Boolean updateOBDepositByGroomingId(@RequestBody MoeBookOnlineDepositVO depositVo);

    @GetMapping("/service/grooming/ob/deposit/getOBDepositWithPaymentByGroomingId")
    BookOnlineDepositDetailDTO getOBDepositWithPaymentByGroomingId(
            @RequestParam("businessId") Integer businessId, @RequestParam("groomingId") Integer groomingId);

    @PostMapping("/service/grooming/ob/discountCodeIds")
    Integer migrateDiscountCodeIds(@RequestBody Map<Long, Long> discountCodeIdMap);

    /**
     * Get Deposit by guid, return null if not found.
     *
     * @param guid guid
     * @return {@link DepositDto}
     */
    @Nullable
    @PostMapping("/service/grooming/ob/deposit/getByGuid")
    BookOnlineDepositDTO getByGuid(@RequestParam("guid") String guid);

    /**
     * Insert a new record, return the id of the new record.
     *
     * <p> null fields will be ignored.
     *
     * @param param {@link InsertParam}
     * @return id
     */
    @PostMapping("/service/grooming/ob/deposit/insert")
    int insert(@RequestBody @NotNull @Valid InsertParam param);

    /**
     * Update a record by id, return the affected row count.
     *
     * <p> null fields will be ignored.
     *
     * @param param {@link UpdateParam}
     * @return affected row count
     */
    @PutMapping("/service/grooming/ob/deposit/selectiveUpdate")
    int update(@RequestBody @NotNull @Valid UpdateParam param);

    /**
     * @param groomingId       groomingId or bookingRequestId must be set
     * @param bookingRequestId groomingId or bookingRequestId must be set
     * @param status           {@link BookOnlineDepositConst}
     * @param depositType      {@link DepositPaymentTypeEnum}
     */
    @Builder(toBuilder = true)
    record InsertParam(
            @NotNull Integer businessId,
            @Nullable Long companyId,
            @Nullable Integer groomingId,
            @Nullable Long bookingRequestId,
            @Nullable String guid,
            @Nullable Integer paymentId,
            @Nullable BigDecimal amount,
            @Nullable BigDecimal tipsAmount,
            @Nullable BigDecimal convenienceFee,
            @Nullable Byte status,
            @Nullable BigDecimal serviceTotal,
            @Nullable BigDecimal taxAmount,
            @Nullable BigDecimal serviceChargeAmount,
            @Nullable BigDecimal discountAmount,
            @Nullable Long discountCodeId,
            @NotNull Byte depositType,
            @Nullable BookOnlineDepositDTO.PreAuth preauthInfo) {}

    /**
     * @param status {@link BookOnlineDepositConst}
     */
    @Builder(toBuilder = true)
    record UpdateParam(
            int id,
            @Nullable Integer groomingId,
            @Nullable Long bookingRequestId,
            @Nullable Integer paymentId,
            @Nullable BigDecimal amount,
            @Nullable BigDecimal tipsAmount,
            @Nullable BigDecimal convenienceFee,
            @Nullable Byte status,
            @Nullable BigDecimal serviceTotal,
            @Nullable BigDecimal taxAmount,
            @Nullable BigDecimal serviceChargeAmount,
            @Nullable BigDecimal discountAmount,
            @Nullable Long discountCodeId) {}
}
