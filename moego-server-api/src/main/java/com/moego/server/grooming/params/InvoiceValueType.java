package com.moego.server.grooming.params;

import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2022/5/9 15:27
 */
public enum InvoiceValueType {
    AMOUNT("amount"),
    PERCENTAGE("percentage");

    String type;

    InvoiceValueType(String type) {
        this.type = type;
    }

    public boolean check(String type) {
        return Objects.equals(this.type, type);
    }

    public String value() {
        return this.type;
    }
}
