package com.moego.server.grooming.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;

@Data
public class PrepayAmountDTO {

    @Schema(description = "full pay 的服务总价 或 deposit 金额，包含 service、service charge，不包含 tax、booking fee")
    private BigDecimal subTotal;

    @Schema(description = "选中的服务的tax金额，当查询 deposit 时，tax 为空")
    private BigDecimal taxAmount;

    @Schema(description = "本次支付的 booking fee")
    @Deprecated(since = "2024-09-25", forRemoval = true)
    private BigDecimal fee;

    @Schema(description = "随机产生的 id，用于后续支付和提交流程绑定")
    private String guid;

    @Schema(description = "初始化 processing fee")
    private BigDecimal initProcessingFee;

    @Schema(description = "需要支付的 service charge 费用")
    private BigDecimal serviceChargeAmount;

    @Schema(description = "mandatory 的 service charge 列表")
    private List<ServiceChargeDTO> serviceChargeList;

    @Schema(description = "本次支付的折扣金额")
    private BigDecimal discountAmount;

    @Schema(description = "使用的 membership ids")
    private List<Long> usedMembershipIds;
}
