package com.moego.server.grooming.dto;

import com.moego.idl.models.online_booking.v1.AcceptCustomerType;
import com.moego.idl.models.online_booking.v1.AcceptPetEntryType;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class GroomingQuestionDTO {

    private Integer businessId;
    private Integer id;
    private String question;
    private String placeholder;
    private Byte isShow;
    private Byte isRequired;
    private Byte type;
    private Byte isAllowDelete;
    private Byte isAllowChange;
    private Byte isAllowEdit;
    private Integer sort;
    private Byte status;
    private Long createTime;
    private Long updateTime;
    private Byte questionType;
    private String extraJson;
    private String key;
    /**
     * @see AcceptCustomerType
     */
    private Integer acceptCustomerType;
    /**
     * @see AcceptPetEntryType
     */
    private Integer acceptPetEntryType;

    public interface Type {
        byte PET = 1;
        byte PET_OWNER = 2;
    }
}
