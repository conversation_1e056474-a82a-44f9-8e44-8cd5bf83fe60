package com.moego.server.grooming.dto.calendarcard;

import io.swagger.v3.oas.annotations.Hidden;
import java.math.BigDecimal;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ServiceInfoDTO {
    @Hidden
    private Integer petDetailId;

    private Long serviceId;
    private String serviceName;
    private String colorCode;
    private Integer serviceTime;
    private BigDecimal servicePrice;
}
