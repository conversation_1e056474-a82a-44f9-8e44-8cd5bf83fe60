package com.moego.server.customer.dto;

import com.moego.common.dto.BaseBusinessCustomerIdDTO;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @since 2022/12/6
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class CustomerShareInfoDTO extends BaseBusinessCustomerIdDTO {

    /**
     * @see com.moego.common.enums.BusinessCustomerConst
     */
    private Byte shareApptStatus;

    /**
     * @see com.moego.common.enums.BusinessCustomerConst
     */
    private Byte shareRangeType;

    /**
     * share_range_type = 1
     */
    private Integer inNDays;

    /**
     * share_range_type = 2
     */
    private Integer limitN;

    /**
     * share_range_type = 3
     */
    private List<Integer> shareApptIdList;
}
