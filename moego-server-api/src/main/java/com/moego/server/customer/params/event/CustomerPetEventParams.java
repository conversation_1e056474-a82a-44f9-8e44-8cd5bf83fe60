package com.moego.server.customer.params.event;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2024/4/9
 */
@Data
@Accessors(chain = true)
public class CustomerPetEventParams {
    private Long companyId;
    private Integer businessId;
    private Integer customerId;
    private Integer petId;
    private CustomerPetEvent event;

    public enum CustomerPetEvent {
        DELETED,
        PASS_AWAY,
    }
}
