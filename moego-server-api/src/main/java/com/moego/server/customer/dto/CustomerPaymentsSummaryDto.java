package com.moego.server.customer.dto;

import java.math.BigDecimal;
import lombok.Data;

// FIXME: 不可重名, 否则 swagger 无法解析, 也许升级 springfox 能解决这个问题
@Data
public class CustomerPaymentsSummaryDto {

    private BigDecimal totalPaid;
    private BigDecimal totalOutstandingBalance;
    private BigDecimal outstandingBalanceDue;
    private BigDecimal availableCredit;
    private BigDecimal totalPaymentAmount;
}
