package com.moego.server.customer.api;

import com.moego.server.customer.dto.CustomerAddressDto;
import com.moego.server.customer.dto.CustomerProfileRequestDTO;
import jakarta.validation.Valid;
import java.util.List;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

public interface ICustomerProfileRequestService {
    /**
     * DONE(account structure)
     */
    @PostMapping("/service/customer/profile-request/upsert")
    Boolean updateCustomerAndProfileRequest(
            @Valid @RequestBody CustomerProfileRequestDTO profileRequestDTO,
            @RequestParam("autoAcceptConflict") Boolean autoAcceptConflict);

    /**
     * DONE(account structure): 保留 business 维度
     */
    @GetMapping("/service/customer/profile-request")
    CustomerProfileRequestDTO getCustomerProfileRequest(
            @RequestParam("businessId") Integer businessId, @RequestParam("customerId") Integer customerId);

    /**
     * DONE(account structure): 保留 business 维度
     */
    @GetMapping("/service/customer/profile-requests")
    List<CustomerProfileRequestDTO> getCustomerProfileRequest(
            @RequestParam("businessId") Integer businessId, @RequestParam("customerId") List<Integer> customerIds);

    /**
     * DONE(account structure): 保留 business 维度
     */
    @DeleteMapping("/service/customer/profile-request")
    Boolean deleteCustomerProfileRequest(
            @RequestParam("businessId") Integer businessId, @RequestParam("customerId") Integer customerId);

    /**
     * DONE(account structure): 保留 business 维度
     */
    @GetMapping("/service/customer/pet/profile-with-request")
    CustomerProfileRequestDTO.PetProfileDTO getPetProfileWithRequest(
            @RequestParam("businessId") Integer businessId,
            @RequestParam("customerId") Integer customerId,
            @RequestParam("petId") Integer petId);

    /**
     * DONE(account structure): 保留 business 维度
     */
    @GetMapping("/service/customer/pets/profile-with-request")
    List<CustomerProfileRequestDTO.PetProfileDTO> getPetsProfileWithRequest(
            @RequestParam("businessId") Integer businessId, @RequestParam("customerId") Integer customerId);

    /**
     * DONE(account structure): 保留 business 维度
     */
    @GetMapping("/service/customer/client/profile-with-request")
    CustomerProfileRequestDTO.ClientProfileDTO getClientWithProfileRequest(
            @RequestParam("businessId") Integer businessId, @RequestParam("customerId") Integer customerId);

    @GetMapping("/service/customer/address/profile-with-request")
    List<CustomerAddressDto> getAddressWithProfileRequest(
            @RequestParam("businessId") Integer businessId, @RequestParam("customerId") Integer customerId);
}
