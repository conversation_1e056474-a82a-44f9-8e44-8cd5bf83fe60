package com.moego.server.customer.params;

import jakarta.validation.constraints.NotNull;
import java.util.List;
import lombok.Data;
import lombok.ToString;

/**
 * add agreement parameters for unsigned agreement or ob agreement
 * <AUTHOR>  v1
 * <AUTHOR>  v2
 */
@Data
@ToString
public class AddCustomerAgreementParams {

    private String unsignedId;
    private Boolean isFormBookOnline = false;
    private Boolean isFormIntakeForm = false;

    @NotNull
    private String signature;

    private Integer businessId;
    private Integer agreementId;
    private Integer customerId;

    private Long rawCreateTime;
    private Long rawUpdateTime;

    private List<String> inputs;
}
