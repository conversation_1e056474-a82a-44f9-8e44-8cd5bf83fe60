package com.moego.server.customer.dto;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.util.List;
import lombok.Data;

@Data
public class IntakeFormCreateWithDetailsParam {
    @NotNull
    private String title;

    private String themeColor;

    private String message;

    // "是否card选项 1：显示 0：不显示"
    private Byte isCardShow;

    // "是否card选项 1： 必填 0：不必填"
    private Byte isCardRequired;

    @NotNull
    private Long businessId;

    @NotNull
    private Long companyId;

    private List<Detail> formDetails;

    @Data
    public static class Detail {

        @NotNull
        @Size(max = 1000)
        private String question;

        @Size(max = 255)
        private String placeholder;

        @NotNull
        private Byte questionType;

        private Byte isShow;

        private Byte isRequired;

        @NotNull
        private Byte type;

        private Byte isAllowDelete;

        private Byte isAllowChange;

        private Byte isAllowEdit;

        private Integer sort;

        private String extraJson;
        /**
         * for intake form client
         */
        private String key;
    }
}
