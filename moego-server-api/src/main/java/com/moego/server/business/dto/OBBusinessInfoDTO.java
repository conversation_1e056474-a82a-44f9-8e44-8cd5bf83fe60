package com.moego.server.business.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2022/11/10
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class OBBusinessInfoDTO {

    private Integer id;
    private Integer companyId;
    private String businessName;
    private String avatarPath;
    private String phoneNumber;
    private String website;
    private String address;
    private String address1;
    private String address2;
    private String addressCity;
    private String addressState;
    private String addressZipcode;
    private String addressCountry;
    private String addressLat;
    private String addressLng;
    private String country;
    private String countryAlpha2Code;
    private String currencySymbol;
    private String currencyCode;
    private Byte timeFormatType;
    private String facebook;
    private String instagram;
    private String google;
    private String yelp;
    private String tiktok;
    private String timeFormat;
    private String unitOfWeight;
    private String calendarFormat;
    private String dateFormat;
    private Byte unitOfWeightType;
    private String timezoneName;
    private Integer timezoneSeconds;
    private Byte dateFormatType;
    private Byte calendarFormatType;
    private Byte numberFormatType;
    private String numberFormat;
    private Byte appType;
    private Byte businessMode;
    private Byte source;
    private Byte clockInOutEnable;
    private Byte clockInOutNotify;
    private Byte isEnableAccessCode;
    private Integer smartScheduleMaxDist;
    private Integer smartScheduleMaxTime;
    /**
     * move to moe_business_book_online.book_online_name
     */
    @Deprecated
    private String bookOnlineName;

    private Long createTime;
    private Long updateTime;

    @Deprecated
    private String countryCode;

    /**
     * 增加business表已有属性
     */
    private Byte primaryPayType;

    private String smartScheduleStartLat;
    private String smartScheduleStartLng;
    private String smartScheduleEndLat;
    private String smartScheduleEndLng;
    private Integer smartScheduleServiceRange;
    private String smartScheduleStartAddr;
    private String smartScheduleEndAddr;
    private String knowFrom;
    private Byte locations;
    private Byte staffMembers;

    private String ownerEmail;

    private Boolean needSendCode;

    private Byte unitOfDistanceType;
}
