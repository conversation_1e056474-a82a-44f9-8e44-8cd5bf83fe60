package com.moego.server.business.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;

@Data
public class PayrollExceptionDTO {

    private Integer id;
    private Integer businessId;

    @Schema(description = "关联 serviceId")
    private Integer serviceId;

    @Schema(description = "service 计算的 rate")
    private BigDecimal rate;

    @Schema(description = "是否应用到全部 staff")
    private Boolean isAllStaff;

    @Schema(description = "应用到的 staff id 列表")
    private List<Integer> staffIdList;

    private Boolean isDelete;
    private Integer createTime;
    private Integer updateTime;
}
