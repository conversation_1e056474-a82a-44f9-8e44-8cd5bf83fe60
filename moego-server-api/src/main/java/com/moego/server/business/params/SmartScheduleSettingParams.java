package com.moego.server.business.params;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SmartScheduleSettingParams {

    // miles
    private Integer maxDistance;

    // minutes
    private Integer maxTime;

    private String startLocationLat;
    private String startLocationLng;
    private String endLocationLat;
    private String endLocationLng;

    private String startLocationAddr;
    private String endLocationAddr;

    // miles
    private Integer serviceRange;

    @Schema(description = " primaryPayType for inner use: 1 stripe, 2 square , default 0")
    private Byte primaryPayType;

    /**
     * certain area for certain days, enable(1)
     */
    private Byte serviceAreaEnable;
}
