package com.moego.server.business.params;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.moego.server.business.dto.AddressInfo;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.PositiveOrZero;
import java.util.List;
import lombok.Data;

@Data
public class SaveSmartScheduleLocationParams {

    @JsonIgnore
    private Integer businessId; // 参数传递，当前请求的 businessId

    @JsonIgnore
    private Integer operatorId; // 参数传递，当前请求的 staffId

    @Schema(description = "record id: 有传是修改，不传是新增")
    @PositiveOrZero
    private Integer id;

    @NotNull
    private String startAddr;

    @NotNull
    private String startLat;

    @NotNull
    private String startLng;

    @Schema(description = "start address 额外的地址信息(address1, address2, city, state, zipcode, country), 兼容旧版可不传")
    private AddressInfo startAddrInfo;

    @NotNull
    private String endAddr;

    @NotNull
    private String endLat;

    @NotNull
    private String endLng;

    @Schema(description = "end address 额外的地址信息(address1, address2, city, state, zipcode, country), 兼容旧版可不传")
    private AddressInfo endAddrInfo;

    @Schema(description = "关联的 van id 列表：不传不更新，传空数组会解绑之前的 van")
    private List<Integer> assignedVanIdList;
}
