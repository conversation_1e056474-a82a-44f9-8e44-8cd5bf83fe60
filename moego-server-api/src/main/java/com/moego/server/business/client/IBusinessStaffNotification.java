package com.moego.server.business.client;

import com.moego.server.business.api.IBusinessStaffNotificationService;
import org.springframework.cloud.openfeign.FeignClient;

@FeignClient(
        value = "moego-business-server",
        url = "${moego.server.url.business}",
        contextId = "IBusinessStaffNotification")
public interface IBusinessStaffNotification extends IBusinessStaffNotificationService {}
