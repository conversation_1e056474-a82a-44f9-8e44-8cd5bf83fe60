/*
 * @since 2023-07-06 17:15:21
 * <AUTHOR> <<EMAIL>>
 */

package com.moego.server.message.vo;

import static com.moego.common.enums.ResponseCodeEnum.PARAMS_ERROR;

import com.moego.common.exception.CommonException;
import com.moego.common.utils.Pagination;
import jakarta.annotation.PostConstruct;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.Builder;

@Builder(toBuilder = true)
public record DescribeMessageDetailsVO(
        Integer companyId,
        @Min(1) int businessId,
        Integer customerId,
        Integer targetType,
        Integer targetId,
        Integer type,
        Integer method,
        Integer source,
        Integer messageType,
        Integer minId, // not included
        Integer maxId, // included
        boolean includeDeleted,
        @NotNull Pagination pagination) {
    @PostConstruct
    void validate() {
        if (customerId != null) {
            return;
        }
        if (targetType == null || targetId == null) {
            throw new CommonException(PARAMS_ERROR, "targetType and targetId must be provided when customerId is null");
        }
    }
}
