package com.moego.server.message.enums;

import com.moego.common.utils.DateUtil;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2020-08-15 21:42
 */
public enum ReminderTypeEnum {
    //    1:first;2:second;3:pet birthday;4:rebook
    // dismissValidDays的初始化设置：原来的获取是等于queryRecentDays * 5，因此除了pet birthday/rebook
    // reminder设置成了35，其它都设置成现有的queryRecentDays * 5，与原来的保持一致，有需要再单独设置
    APPOINT_FIRST(1, "first appointment", 7, MessageTargetTypeEnums.REMINDER_APPOINTMENT_FIRST, 35),
    APPOINT_SECOND(2, "second appointment", 7, MessageTargetTypeEnums.REMINDER_APPOINTMENT_SECOND, 35),
    PET_BIRTHDAY(3, "pet birthday", 30, MessageTargetTypeEnums.REMINDER_PET_BIRTHDAY, 35),
    REBOOK(4, "rebook", 30, MessageTargetTypeEnums.REMINDER_REBOOK, 35),
    REPEAT(5, "repeat", 7, null, 35), // 没有找到对应的消息类型，应该是不需要发送消息
    ARRIVAL_WINDOW(6, "arrival window", 0, null, 0), // 没有找到对应的消息类型，应该是不需要发送消息
    APPOINT_REMIND(7, "appoint reminder", 7, MessageTargetTypeEnums.REMINDER_APPOINTMENT_REMIND, 35),
    EXPIRY_CUSTOMER_REMINDER(8, "expiry customer reminder", 8, null, 40),
    /**
     * fixed https://moego.atlassian.net/browse/ERP-687
     */
    LAPSED_CLIENT(9, "lapsed client dismiss", 0, null, 0),
    COF_LINK_REMINDER(10, "Unsubmitted card on file reminder ", 7, MessageTargetTypeEnums.COF_LINK, 0);

    private Integer reminderType;
    private String desc;
    private Integer queryRecentlyDays;
    private MessageTargetTypeEnums messageTargetTypeEnums;
    private Integer dismissValidDays; // dismiss reminder后多少天之内不需要再发送reminder

    ReminderTypeEnum(
            Integer reminderType,
            String desc,
            Integer queryRecentlyDays,
            MessageTargetTypeEnums messageTargetTypeEnums,
            Integer dismissValidDays) {
        this.reminderType = reminderType;
        this.desc = desc;
        this.queryRecentlyDays = queryRecentlyDays;
        this.messageTargetTypeEnums = messageTargetTypeEnums;
        this.dismissValidDays = dismissValidDays;
    }

    public Integer getReminderType() {
        return reminderType;
    }

    public String getDesc() {
        return desc;
    }

    public Integer getQueryRecentlyDays() {
        return queryRecentlyDays;
    }

    public MessageTargetTypeEnums getMessageTargetTypeEnums() {
        return messageTargetTypeEnums;
    }

    public static ReminderTypeEnum getByReminderType(Integer reminderType) {
        if (Objects.isNull(reminderType)) {
            return null;
        }
        for (ReminderTypeEnum reminderTypeEnum : values()) {
            if (reminderTypeEnum.getReminderType().equals(reminderType)) {
                return reminderTypeEnum;
            }
        }
        return null;
    }

    /**
     * 获取recently dismiss的开始时间，根据dismissValidDays计算得到
     *
     * @return
     */
    public Integer getDismissFromTime() {
        Integer now = DateUtil.get10TimestampInteger();
        return now - dismissValidDays * 24 * 3600;
    }
}
