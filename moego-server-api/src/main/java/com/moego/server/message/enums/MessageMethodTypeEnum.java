package com.moego.server.message.enums;

import com.fasterxml.jackson.annotation.JsonValue;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@Getter
@Schema(description = "message method", type = "integer")
public enum MessageMethodTypeEnum {
    MESSAGE_METHOD_UNKNOWN(0),
    MESSAGE_METHOD_MSG(1),
    MESSAGE_METHOD_EMAIL(2),
    MESSAGE_METHOD_CALL(4),
    MESSAGE_METHOD_APP(5);

    private final Integer value;

    @JsonValue
    public Integer getValue() {
        return value;
    }

    public static MessageMethodTypeEnum fromInteger(Integer value) {
        if (value == null) {
            return MESSAGE_METHOD_UNKNOWN;
        }
        for (MessageMethodTypeEnum messageMethodTypeEnum : MessageMethodTypeEnum.values()) {
            if (messageMethodTypeEnum.value.equals(value)) {
                return messageMethodTypeEnum;
            }
        }
        return MESSAGE_METHOD_UNKNOWN;
    }
}
