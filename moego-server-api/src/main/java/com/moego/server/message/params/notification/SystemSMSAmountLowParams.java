package com.moego.server.message.params.notification;

import com.moego.common.dto.notificationDto.NotificationEnum;
import com.moego.common.dto.notificationDto.NotificationExtraApptCommonDto;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SystemSMSAmountLowParams extends NotificationParams {

    private String title = "SMS amount low";
    private String type = NotificationEnum.TYPE_SUSTEM_SMS_AMOUNT_LOW;
    private NotificationExtraApptCommonDto webPushDto;
    private Boolean isNotifyBusinessOwner = true;
    private String mobilePushTitle = "";
    private String mobilePushBody = "";
}
