package com.moego.server.message.params;

import lombok.Data;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 * @date 2020-07-05 16:18
 */
@Data
public class SendMessageCustomerParams {

    private Integer customerId;
    private String customerName;
    private String customerNumber;
    private String customerEmail;
    /**
     * 在/message/send/toCustomer/one接口内，需要用contactId获取发送号码
     * staff如果没有查看号码的权限，customerNumber会包含星号
     */
    private Integer contactId;

    @Deprecated
    private String contactName;

    public void setContactName(String contactName) {
        this.contactName = contactName;
        if (StringUtils.isEmpty(this.customerName)) {
            this.customerName = contactName;
        }
    }

    @Override
    public String toString() {
        return ("SendMessageCustomerParams{" + "customerId="
                + customerId
                + ", customerName='"
                + customerName
                + '\''
                + ", customerNumber='"
                + customerNumber
                + '\''
                + ", customerEmail='"
                + customerEmail
                + '\''
                + ", contactId="
                + contactId
                + ", contactName='"
                + contactName
                + '\''
                + '}');
    }
}
