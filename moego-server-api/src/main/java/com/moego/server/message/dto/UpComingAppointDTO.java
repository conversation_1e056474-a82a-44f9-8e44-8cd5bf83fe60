package com.moego.server.message.dto;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import lombok.Data;

@Data
public class UpComingAppointDTO {

    private String businessName;
    private String businessAvatarPath;

    private Date appointmentDate;
    private Integer appointmentStartTime;
    private Integer appointmentEndTime;

    private BigDecimal estimatePrice;

    private String customerLastName;
    private String customerFirstName;
    // Client phone number
    private String clientPhoneNumber;

    // client full address(包含 city zipcode)
    private String address1;
    private String address2;
    private String country;
    private String state;
    // City
    private String city;
    // Zipcode
    private String zipcode;

    private String clientFullAddress;

    private String createByLastName;
    private String createByFirstName;

    private String ticketComments;
    private String notice;

    private Long createTime;
    private Long updateTime;

    private String email;

    private String appointmentStartDateTime;
    private List<UpcomingPetDetailDTO> petDetails;
}
