package com.moego.server.message.dto;

import com.moego.server.message.enums.VerificationCodeScenarioEnum;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2023/2/10
 */
@Data
@Accessors(chain = true)
public class SendEmailDTO {

    private VerificationCodeScenarioEnum scenario;

    private Integer businessId;

    /**
     * Sender name
     */
    private String fromName;

    /**
     * Sender email
     */
    private String fromEmail;

    /**
     * Recipient email
     */
    private String toEmail;

    /**
     * Verification code
     */
    private String code;
}
