package com.moego.server.message.params.notification;

import com.moego.common.dto.notificationDto.NotificationEnum;
import com.moego.common.dto.notificationDto.NotificationExtraCalendarReminderDto;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class NotificationCalendarReminderParams extends NotificationParams {

    private String title = "calendar reminder";
    private String type = NotificationEnum.TYPE_MOBILE_APPOINTMENT_REMINDER;
    private NotificationExtraCalendarReminderDto webPushDto;
    private String mobilePushTitle = "Appointment in {remainingTime} mins";
    private String mobilePushBody = "{appointmentTime:Hour} with {customerFullName} - {petNameBreeds}";
}
