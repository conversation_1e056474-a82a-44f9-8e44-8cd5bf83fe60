package com.moego.server.payment.api;

import com.moego.common.dto.FeatureQuotaDto;
import com.moego.server.payment.dto.CompanyFeatureRelationDTO;
import com.moego.server.payment.params.CompanyFeatureRelationQueueParams;
import com.moego.server.payment.params.InsertCompanyFeatureRelationParams;
import jakarta.validation.Valid;
import java.util.Map;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

public interface IPaymentPlanService {
    @GetMapping("/service/payment/plan/queryCompanyPlanFeatureByCid")
    Map<String, FeatureQuotaDto> queryCompanyPlanFeatureByCid(@RequestParam("companyId") Integer companyId);

    @GetMapping("/service/payment/plan/queryCompanyPlanFeatureByCidCode")
    FeatureQuotaDto queryCompanyPlanFeatureByCidCode(
            @RequestParam("companyId") Integer companyId, @RequestParam("code") String code);

    @GetMapping("/service/payment/plan/queryCompanyPlanFeatureByBid")
    Map<String, FeatureQuotaDto> queryCompanyPlanFeatureByBid(@RequestParam("businessId") Integer businessId);

    @GetMapping("/service/payment/plan/checkFeatureCodeIsEnableByBid")
    Boolean checkFeatureCodeIsEnableByBid(
            @RequestParam("businessId") Integer businessId, @RequestParam("code") String code);

    @GetMapping("/service/payment/plan/checkFeatureCodeIsEnableByCid")
    Boolean checkFeatureCodeIsEnableByCid(
            @RequestParam("companyId") Integer companyId, @RequestParam("code") String code);

    @PostMapping("/service/payment/plan/getCompanyFeatureRelationByPages")
    CompanyFeatureRelationDTO getCompanyFeatureRelationByPages(
            @RequestBody @Valid CompanyFeatureRelationQueueParams request);

    @PostMapping("/service/payment/plan/company-feature-relation")
    int insertCompanyFeatureRelation(@RequestBody @Valid InsertCompanyFeatureRelationParams request);

    @DeleteMapping("/service/payment/plan/company-feature-relation")
    int deleteCompanyFeatureRelation(@RequestParam("companyId") Integer id);
}
