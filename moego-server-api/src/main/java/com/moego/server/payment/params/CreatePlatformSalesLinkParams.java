package com.moego.server.payment.params;

import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2023/10/25
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CreatePlatformSalesLinkParams {
    // emai
    @Email(message = "Email should be valid")
    private String email;

    @Min(value = 0, message = "Subscription term 12 24 36")
    private Integer subTerm;

    @NotNull
    private Boolean showAnnuallyTerm;

    @NotNull
    private Boolean showMonthlyTerm;

    @NotNull
    private Boolean showHardware;

    private String creator;

    @Min(value = 0, message = "0 5=5% 10=10% 15=15%   number=number%")
    private Integer subPriceDiscount;

    @NotNull
    private Boolean needHardware;

    @NotNull
    private Boolean isBdPlan;

    @Min(value = 0, message = "hardware discount 0 10")
    private Integer hardwareDiscount;

    private Integer vansNum;

    private Integer locationNum;

    @NotNull(message = "0 mobile 1salon  2 mobile and salon")
    private Byte companyType;

    @NotNull(message = "2 or 3")
    private Byte premiumType;

    // 新增字段，先不加 NotEmpty 约束，等 MIS 发完版再加上约束
    // @NotEmpty
    private String tier;

    private BigDecimal spif;

    private String terminalCardRate;
    private String nonTerminalCardRate;
    private String minMonthlyTransaction;
    private Boolean isCustomRate;
    private Boolean showAccounting;
}
