package com.moego.server.payment.dto;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import java.math.BigDecimal;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2025/1/6
 */
@Data
public class CompanyProcessingFeeDTO {
    @Max(4)
    @Min(0)
    private BigDecimal onlineFeeRate;

    @Max(100)
    @Min(0)
    private Integer onlineFeeCents;

    @Max(4)
    @Min(0)
    private BigDecimal readerFeeRate;

    @Max(100)
    @Min(0)
    private Integer readerFeeCents;
}
