package com.moego.server.payment.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2021/3/15 7:51 PM
 */
@Data
@Schema(description = "短信包价格表")
public class MessagePriceDTO {

    @Schema(description = "需要回传的主键ID")
    Integer id;

    @Schema(description = "短信购买条数")
    Integer amount;

    @Schema(description = "对应价格")
    BigDecimal price;

    @Schema(description = "1-USA CA  2-UK AU  3-spain")
    Byte type;
}
