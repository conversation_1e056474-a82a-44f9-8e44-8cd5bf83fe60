package com.moego.server.payment.params;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
@Schema(description = "创建企业的 stripe customer")
public class CreateEnterpriseCustomerParams {
    @NotNull
    private Long enterpriseId;

    private Long accountId;

    private Boolean createTestClock;

    @NotNull
    private String chargeToken;
}
