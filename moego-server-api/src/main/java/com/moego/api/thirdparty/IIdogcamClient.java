package com.moego.api.thirdparty;

import com.moego.api.thirdparty.dto.IDogCamCameraDto;
import com.moego.api.thirdparty.dto.IDogCamViewerDto;
import java.util.List;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(name = "idogcam-client", url = "https://idogcam.com/", contextId = "IIdogcamClient")
public interface IIdogcamClient {
    @GetMapping(value = "/idogcammoegoJSON.php")
    List<IDogCamCameraDto> queryCameraList(
            @RequestParam String key, @RequestParam String kennelid, @RequestParam String erpcode);

    @PostMapping(value = "/idogcamviewer.php")
    String fetchViewerHtml(@RequestParam String id, @RequestBody IDogCamViewerDto body);
}
