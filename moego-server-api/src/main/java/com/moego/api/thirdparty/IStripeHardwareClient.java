package com.moego.api.thirdparty;

import com.moego.server.payment.dto.stripe.HardwareOrderCollection;
import com.moego.server.payment.dto.stripe.HardwareOrderDetail;
import com.moego.server.payment.dto.stripe.HardwareProductCollection;
import com.moego.server.payment.dto.stripe.ShippingMethodCollection;
import com.moego.server.payment.dto.stripe.TerminalHardwareSku;
import java.util.Map;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestHeader;

/**
 * <AUTHOR>
 * @since 2023/10/20
 */
@FeignClient(
        name = "stripe-hardware-client",
        url = "https://api.stripe.com/v1/terminal",
        contextId = "IStripeHardwareClient")
public interface IStripeHardwareClient {

    @GetMapping("/hardware_products")
    HardwareProductCollection listProducts(@RequestHeader Map<String, Object> headerMap);

    /**
     * ref: https://stripe.com/docs/api/terminal/hardware_skus/list
     * @param headerMap
     * @param queryParam  country: US, product: product id
     * @return
     */
    @GetMapping("/hardware_skus")
    TerminalHardwareSku retrieveProductSkus(
            @RequestHeader Map<String, Object> headerMap, @SpringQueryMap Map<String, Object> queryParam);

    @GetMapping("/hardware_shipping_methods")
    ShippingMethodCollection listShippingMethods(
            @RequestHeader Map<String, Object> headerMap, @SpringQueryMap Map<String, Object> queryParam);

    @GetMapping("/hardware_orders/preview")
    HardwareOrderDetail previewOrder(
            @RequestHeader Map<String, Object> headMap, @SpringQueryMap Map<String, Object> queryParam);

    @PostMapping("/hardware_orders")
    HardwareOrderDetail createOrder(
            @RequestHeader Map<String, Object> headMap, @SpringQueryMap Map<String, Object> queryParam);

    @GetMapping("/hardware_orders/{orderId}")
    HardwareOrderDetail retrieveOrder(@RequestHeader Map<String, Object> headMap, @PathVariable String orderId);

    @GetMapping("/hardware_orders")
    HardwareOrderCollection listOrders(
            @RequestHeader Map<String, Object> headMap, @SpringQueryMap Map<String, Object> queryParam);
}
