buildscript {
  configurations['classpath'].resolutionStrategy.eachDependency {
    if (requested.group == 'org.jooq') {
      useVersion "${jooqVersion}"
    }
  }
}

plugins {
  id 'java'
  id 'io.spring.dependency-management' version "${springDependencyManagementVersion}"
  id 'org.springframework.boot' version "${springBootVersion}"
  id 'nu.studer.jooq' version "${jooqPluginVersion}"

  id 'com.diffplug.spotless' version "${spotlessVersion}" apply false
  id "com.github.spotbugs" version "${spotbugsVersion}" apply false

  // 添加 jacoco 插件
  id 'jacoco'
  id 'net.razvan.jacoco-to-cobertura' version "1.2.0"
}

repositories {
  mavenCentral()
}

dependencyManagement {
  imports {
    mavenBom "org.springframework.cloud:spring-cloud-dependencies:${springCloudVersion}"
  }
}

tasks.register('installGitHook', Copy) {
  from "${rootProject.rootDir}/.githooks"
  into { new File(rootProject.rootDir, '.git/hooks') }
  fileMode 0775
}
compileJava.dependsOn installGitHook

apply from: "https://moego.s3.us-west-2.amazonaws.com/ops/sbc/${System.getProperty('sbc.scriptBranch', '')}SourceBranchControl.gradle"
applySourceBranchControlDependencies()

// spotless
apply plugin: 'com.diffplug.spotless'
spotless {
  encoding 'UTF-8'
  java {
    toggleOffOn()
    removeUnusedImports()
    trimTrailingWhitespace()
    endWithNewline()
    palantirJavaFormat()

    targetExclude(
      "build/generated/**",
      "src/main/java/**/mapper/*",
      "src/main/java/**/entity/*",
      "src/main/jooq/**"
    )

    custom('Refuse wildcard imports', {
      if (it =~ /\nimport .*\*;/) {
        throw new IllegalStateException("Do not use wildcard imports, 'spotlessApply' cannot resolve this issue, please fix it manually.")
      }
    } as Closure<String>)
  }
}
// spotbugs
apply plugin: 'com.github.spotbugs'
spotbugs {
  spotbugsTest.enabled = false
  omitVisitors.addAll 'FindReturnRef', 'MethodReturnCheck', 'DontReusePublicIdentifiers'
  excludeFilter.set(file("${rootDir}/config/spotbugs/exclude.xml"))
}

dependencies {
  implementation 'org.mybatis.spring.boot:mybatis-spring-boot-starter:3.0.2'
  implementation 'org.postgresql:postgresql'
  implementation 'org.mapstruct:mapstruct:1.5.3.Final'

  implementation 'io.awspring.cloud:spring-cloud-aws-starter-secrets-manager:3.0.5'

  compileOnly 'org.projectlombok:lombok'
  annotationProcessor 'org.projectlombok:lombok'
  annotationProcessor 'org.springframework.boot:spring-boot-configuration-processor'
  annotationProcessor 'org.mapstruct:mapstruct-processor:1.5.3.Final'


  testImplementation 'org.springframework.boot:spring-boot-starter-test'
  testImplementation 'org.mybatis.spring.boot:mybatis-spring-boot-starter-test:3.0.2'

  testAnnotationProcessor "org.mapstruct:mapstruct-processor:1.5.3.Final"

  implementation 'org.springframework.boot:spring-boot-starter-jooq'
  implementation 'org.jooq:jooq-postgres-extensions'
  jooqGenerator 'org.jooq:jooq-postgres-extensions'
  jooqGenerator 'org.postgresql:postgresql'
}

compileJava {
  options.compilerArgs << '-parameters'
}

tasks.withType(JavaCompile) {
  options.encoding = 'UTF-8'
}

tasks.named("bootJar") {
  archiveBaseName = 'moego-server'
}

tasks.named('test') {
  useJUnitPlatform()
}

// 添加 jacoco report task
jacocoTestReport {
  reports {
    xml {
      required = true
      destination file("${rootProject.buildDir}/reports/jacoco/test/jacocoTestReport.xml")
    }
  }
  afterEvaluate {
    classDirectories.setFrom(files(classDirectories.files.collect {
      fileTree(dir: it, exclude: [
        "**/*ConverterImpl*",
        "**/*MapperImpl*",
        "**/jooq/generated/**",
      ])
    }))
  }
}


jooq {
  // use jOOQ version defined in Spring Boot
//  version = dependencyManagement.importedProperties['jooq.version']
  version = "${jooqVersion}"
//  edition = JooqEdition.OSS

  configurations {
    main {
      generateSchemaSourceOnCompilation = false  // we don't want to generate sources on compilation every time
      generationTool {
        logging = org.jooq.meta.jaxb.Logging.WARN
        jdbc {
          driver = 'org.postgresql.Driver'
          url = '***********************************************************'
          user = 'moego_developer_240310_eff7a0dc'
          password = 'G0MxI7NM_jX_f7Ky73vnrwej97xg1tly'
//          properties {
//            property {
//              key = 'PAGE_SIZE'
//              value = 2048
//            }
//          }
        }
        generator {
          name = 'org.jooq.codegen.DefaultGenerator'
          database {
            name = 'org.jooq.meta.postgres.PostgresDatabase'
            inputSchema = 'public'
            includes = '.*'
            excludes = 'cosid.* | tmp.* | regexp_.* | awsdms_ddl_audit | pg_stat_.*'
            forcedTypes {
              forcedType {
                userType = 'java.lang.Integer'
                lambdaConverter {
                  from = 'Integer::valueOf'
                  to = 'Integer::shortValue'
                }
                includeTypes = 'smallint'
              }
            }
          }
          target {
            packageName = 'com.moego.svc.account.repository.jooq'
            directory = 'src/main/jooq'
          }
          generate {
            deprecated = false
            routines = false
            records = true
            fluentSetters = true
          }
          strategy.name = "org.jooq.codegen.DefaultGeneratorStrategy"
        }
      }
    }
  }
}
