package com.moego.lib.utils;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

import org.junit.jupiter.api.Test;

class GeoUtilsTest {

    @Test
    void isPointInRectangle() {
        assertTrue(GeoUtils.isPointInRectangle(
                GeoUtils.Rectangle.from(new GeoUtils.Point(2.25015551312983, 103.34497558883511), 1000, 1000),
                new GeoUtils.Point(2.248485459979784, 103.34317265781519)));
        assertFalse(GeoUtils.isPointInRectangle(
                GeoUtils.Rectangle.from(new GeoUtils.Point(2.2536307896553454, 103.34720602121247), 1000, 1000),
                new GeoUtils.Point(2.248485459979784, 103.34317265781519)));
    }
}
