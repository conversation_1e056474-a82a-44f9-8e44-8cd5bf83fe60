package com.moego.svc.activitylog.server.service.mapper.impl;

import com.moego.svc.activitylog.server.service.mapper.Mapper;
import java.util.Map;
import java.util.Set;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@AllArgsConstructor
public class AutoMessageTypeMapper implements Mapper<String> {

    private static final Map<String, String> VALUE_MAP = Map.of(
            "1", "When new appt is booked",
            "2", "When appt is rescheduled",
            "3", "When appt is cancelled",
            "4", "ready for pickup",
            "5", "send ETA",
            "6", "When appt is confirmed by client",
            "7", "When appt is cancelled by client");

    @Override
    public Map<String, String> map(Set<String> values) {
        return VALUE_MAP;
    }

    @Override
    public String getName(String value) {
        return VALUE_MAP.get(value);
    }
}
