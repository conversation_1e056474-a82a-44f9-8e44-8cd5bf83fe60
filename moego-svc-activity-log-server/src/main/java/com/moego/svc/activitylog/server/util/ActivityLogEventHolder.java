package com.moego.svc.activitylog.server.util;

import com.moego.svc.activitylog.event.ActivityLogEvent;
import jakarta.annotation.Nullable;
import lombok.experimental.UtilityClass;

/**
 * <AUTHOR>
 * @since 2023/12/26
 */
@UtilityClass
public class ActivityLogEventHolder {

    private static final ThreadLocal<ActivityLogEvent> event = new InheritableThreadLocal<>();

    @Nullable
    public static ActivityLogEvent get() {
        return event.get();
    }

    public static void set(ActivityLogEvent activityLogEvent) {
        event.set(activityLogEvent);
    }

    public static void remove() {
        event.remove();
    }
}
