package com.moego.svc.activitylog.server.util;

import java.util.Collection;
import java.util.function.Consumer;
import java.util.function.Supplier;
import lombok.experimental.UtilityClass;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR>
 */
@UtilityClass
public class Options {

    private static final Logger log = LoggerFactory.getLogger(Options.class);

    /**
     * Do when not empty.
     *
     * @param collection collection
     * @param consumer   consumer
     * @param <T>        type
     */
    public static <T> void doWhenNotEmpty(Collection<T> collection, Consumer<Collection<T>> consumer) {
        if (collection != null && !collection.isEmpty()) {
            consumer.accept(collection);
        }
    }

    /**
     * Do when not null.
     *
     * @param t        t
     * @param consumer consumer
     * @param <T>      type
     */
    public static <T> void doWhenNotNull(T t, Consumer<T> consumer) {
        if (t != null) {
            consumer.accept(t);
        }
    }

    /**
     * Just do it, return null if exception occurred.
     *
     * @param supplier supplier
     * @param <T>      type
     * @return result
     */
    public static <T> T justDo(Supplier<T> supplier) {
        try {
            return supplier.get();
        } catch (Exception e) {
            log.error("Just do it error", e);
            return null;
        }
    }
}
