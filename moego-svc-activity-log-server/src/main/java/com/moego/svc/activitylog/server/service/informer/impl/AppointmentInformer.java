package com.moego.svc.activitylog.server.service.informer.impl;

import com.moego.server.grooming.api.IGroomingAppointmentService;
import com.moego.server.grooming.dto.AppointmentDTO;
import com.moego.svc.activitylog.event.enums.ResourceType;
import com.moego.svc.activitylog.server.service.informer.AbstractStaffOperatorCustomerOwnerInformer;
import com.moego.svc.activitylog.server.service.mapper.impl.MinuteToTimeMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * For resource type {@link ResourceType#APPOINTMENT}.
 *
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class AppointmentInformer extends AbstractStaffOperatorCustomerOwnerInformer<AppointmentDTO> {

    private final IGroomingAppointmentService appointmentApi;
    private final MinuteToTimeMapper minuteToTimeMapper;

    @Override
    public String getOwnerId(AppointmentDTO appointmentDTO) {
        return String.valueOf(appointmentDTO.getCustomerId());
    }

    @Override
    public String resourceType() {
        return ResourceType.APPOINTMENT.toString();
    }

    @Override
    public AppointmentDTO resource(String resourceId) {
        return appointmentApi.getAppointmentById(Integer.parseInt(resourceId));
    }

    @Override
    public String resourceName(AppointmentDTO resource) {
        var date = resource.getAppointmentDate();
        var time = minuteToTimeMapper.getName(String.valueOf(resource.getAppointmentStartTime()));
        return "Appointment at %s %s".formatted(date, time);
    }

    @Override
    public boolean support(String resourceType) {
        return resourceType().equalsIgnoreCase(resourceType);
    }
}
