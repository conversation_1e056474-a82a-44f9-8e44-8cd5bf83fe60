package com.moego.svc.activitylog.server.service.informer.impl;

import com.moego.svc.activitylog.event.enums.ResourceType;
import com.moego.svc.activitylog.server.service.informer.AbstractStaffOperatorInformer;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2023/12/18
 */
@Component
public class ProfileRequestInformer extends AbstractStaffOperatorInformer<String> {
    @Override
    public String resourceType() {
        return ResourceType.CUSTOMER_PROFILE_REQUEST.toString();
    }
}
