package com.moego.svc.activitylog.server.service.informer;

import com.moego.server.business.api.IBusinessStaffService;
import com.moego.server.business.dto.MoeStaffDto;
import com.moego.server.business.params.StaffIdParams;
import com.moego.server.customer.api.ICustomerCustomerService;
import com.moego.server.customer.dto.MoeBusinessCustomerDTO;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * Has staff operator and customer owner informer.
 *
 * @param <Resource> resource type
 */
public abstract class AbstractStaffOperatorCustomerOwnerInformer<Resource>
        implements HasOperatorHasOwnerInformer<Resource, MoeStaffDto, MoeBusinessCustomerDTO> {

    private IBusinessStaffService staffApi;
    private ICustomerCustomerService customerApi;

    @Autowired
    public void setStaffApi(IBusinessStaffService staffApi) {
        this.staffApi = staffApi;
    }

    @Autowired
    public void setCustomerApi(ICustomerCustomerService customerApi) {
        this.customerApi = customerApi;
    }

    @Override
    public MoeStaffDto operator(String operatorId) {
        StaffIdParams params = new StaffIdParams();
        params.setStaffId(Integer.valueOf(operatorId));
        return staffApi.getStaff(params);
    }

    @Override
    public String operatorName(MoeStaffDto operator) {
        return operator.getFirstName() + " " + operator.getLastName();
    }

    @Override
    public MoeBusinessCustomerDTO owner(String ownerId) {
        return customerApi.getCustomerWithDeleted(Integer.parseInt(ownerId));
    }

    @Override
    public String ownerName(MoeBusinessCustomerDTO owner) {
        return owner.getFirstName() + " " + owner.getLastName();
    }
}
