package com.moego.server.task.job;

import com.moego.idl.service.fulfillment.v1.ExecuteCompensationTaskRequest;
import com.moego.idl.service.fulfillment.v1.FulfillmentServiceGrpc;
import lombok.RequiredArgsConstructor;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class FulfillmentTask {
    private final FulfillmentServiceGrpc.FulfillmentServiceBlockingStub fulfillmentStub;

    @Scheduled(cron = "0 */10 * * * *")
    public void executeCompensationTask() {
        fulfillmentStub.executeCompensationTask(ExecuteCompensationTaskRequest.getDefaultInstance());
    }
}
