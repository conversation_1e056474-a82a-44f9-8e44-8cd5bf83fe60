package com.moego.server.task.job;

import com.google.protobuf.Empty;
import com.moego.idl.service.accounting.v1.AccountingServiceGrpc;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class AccountingRetrySyncTask {

    @Autowired
    private AccountingServiceGrpc.AccountingServiceBlockingStub accountingServiceBlockingStub;

    /**
     * 重试accounting数据同步，每分钟调用一次
     */
    @Scheduled(cron = "0 0/1 * * * ?")
    public void retrySyncData() {
        log.info("retry accounting sync task begin");
        var resp = accountingServiceBlockingStub.retrySync(Empty.newBuilder().build());
        log.info("retry accounting sync task success,{}", resp);
    }
}
