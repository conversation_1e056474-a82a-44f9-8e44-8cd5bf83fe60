package com.moego.server.task.job;

import com.google.protobuf.util.Timestamps;
import com.moego.idl.service.appointment.v1.OutboxServiceGrpc.OutboxServiceBlockingStub;
import com.moego.idl.service.appointment.v1.PushEventRequest;
import java.time.Instant;
import lombok.RequiredArgsConstructor;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class AppointmentOutboxTask {

    private final OutboxServiceBlockingStub outboxStub;

    /**
     * 检查 outbox 是否有待发送信息并推送
     *
     * @see <a href="https://moego.atlassian.net/browse/MER-2754">MER-2754</a>
     */
    @Scheduled(cron = "0 0/10 * * * ?")
    public void processOutbox() {
        var end = Instant.now().minusSeconds(300); // 延迟 5 min 扫描待推送消息
        var start = end.minusSeconds(1800); // 检查过去 30 min 的数据

        outboxStub.pushEvent(PushEventRequest.newBuilder()
                .setStart(Timestamps.fromSeconds(start.getEpochSecond()))
                .setEnd(Timestamps.fromSeconds(end.getEpochSecond()))
                .build());
    }
}
