package com.moego.svc.account.utils;

import com.moego.lib.common.exception.BizException;
import java.util.Arrays;
import java.util.Collections;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

public class PasswordUtilsTest {

    @Test
    public void testPasswordLength() {
        Assertions.assertThrowsExactly(
                BizException.class,
                () -> PasswordUtils.checkPasswordStrength("<EMAIL>", "123456"),
                PasswordUtils.LENGTH_ERROR_MESSAGE);
    }

    @Test
    public void testPasswordPattern() {
        var email = "<EMAIL>";
        String[] weakPasswords = new String[] {
            "********", // 只有数字
            "abcdefgh", // 只有小写字母
            "ABCDEFGH", // 只有大写字母
            "!@#$%^&*", // 只有特殊字符
            "1234567a", // 数字 + 小写字母
            "1234567A", // 数字 + 大写字母
            "1234567!", // 数字 + 特殊字符
            "abcdefgA", // 小写字母 + 大写字母
            "abcdefg!", // 小写字母 + 特殊字符
            "ABCDEFG!", // 大写字母 + 特殊字符
            "123456aA", // 数字 + 小写字母 + 大写字母
            "123456a!", // 数字 + 小写字母 + 特殊字符
            "123456A!", // 数字 + 大写字母 + 特殊字符
            "abcdefA!", // 小写字母 + 大写字母 + 特殊字符
        };
        for (String weakPassword : weakPasswords) {
            Assertions.assertThrowsExactly(
                    BizException.class,
                    () -> PasswordUtils.checkPasswordStrength(email, weakPassword),
                    PasswordUtils.PATTERN_ERROR_MESSAGE);

            // weakPassword 打乱字符顺序
            for (int i = 0; i < 10; i++) {
                var charList = Arrays.asList(weakPassword.split(""));
                Collections.shuffle(charList);
                var shuffledPassword = String.join("", charList);

                Assertions.assertThrowsExactly(
                        BizException.class,
                        () -> PasswordUtils.checkPasswordStrength(email, shuffledPassword),
                        PasswordUtils.PATTERN_ERROR_MESSAGE);
            }
        }
    }

    @Test
    public void testPasswordContainsPartOfEmail() {
        var email = "<EMAIL>";
        var password = "Example123@";
        Assertions.assertThrowsExactly(
                BizException.class,
                () -> PasswordUtils.checkPasswordStrength(email, email),
                PasswordUtils.PART_OF_EMAIL_MESSAGE);
        Assertions.assertThrowsExactly(
                BizException.class,
                () -> PasswordUtils.checkPasswordStrength(email, email.toLowerCase()),
                PasswordUtils.PART_OF_EMAIL_MESSAGE);
        Assertions.assertThrowsExactly(
                BizException.class,
                () -> PasswordUtils.checkPasswordStrength(email, password),
                PasswordUtils.PART_OF_EMAIL_MESSAGE);
        Assertions.assertThrowsExactly(
                BizException.class,
                () -> PasswordUtils.checkPasswordStrength(email, password.toLowerCase()),
                PasswordUtils.PART_OF_EMAIL_MESSAGE);
    }

    @Test
    public void testStrongPassword() {
        var email = "<EMAIL>";
        Assertions.assertDoesNotThrow(() -> PasswordUtils.checkPasswordStrength(email, "12345aA!"));
        Assertions.assertDoesNotThrow(() -> PasswordUtils.checkPasswordStrength(email, "12345Exa@"));
    }
}
