package com.moego.svc.online.booking.mapper;

import static com.moego.svc.online.booking.mapper.BookingRequestDynamicSqlSupport.bookingRequest;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatCode;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;

import com.moego.svc.online.booking.entity.BookingRequest;
import java.time.LocalDate;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.condition.DisabledIfEnvironmentVariable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.transaction.annotation.Transactional;

@SpringBootTest
@DisabledIfEnvironmentVariable(named = "CI", matches = "true|1")
class BookingRequestMapperTest {

    @Autowired
    private BookingRequestMapper mapper;

    @Test
    @Transactional
    void testPostgresDateType() {
        long id = mapper.selectOne(c -> c.orderBy(bookingRequest.id).limit(1))
                .map(BookingRequest::getId)
                .orElseThrow();

        // update date
        var e = get(id);
        e.setStartDate(LocalDate.now().toString());
        mapper.updateByPrimaryKeySelective(e);
        e = get(id);
        assertThat(e.getStartDate()).isEqualTo(LocalDate.now().toString());

        // set date to null
        e = get(id);
        e.setStartDate(null);
        mapper.update(c -> c.set(bookingRequest.startDate).equalToNull().where(bookingRequest.id, isEqualTo(id)));
        e = get(id);
        assertThat(e.getStartDate()).isNull();

        // insert
        e = get(id);
        e.setStartDate(LocalDate.now().toString());
        mapper.insertSelective(e);

        // set illegal date
        var it = get(id);
        it.setStartDate("2021-02-31");
        assertThatCode(() -> mapper.updateByPrimaryKeySelective(it))
                .isInstanceOf(DataIntegrityViolationException.class)
                .hasMessageContaining("date/time field value out of range");
    }

    private BookingRequest get(long id) {
        return mapper.selectByPrimaryKey(id).orElseThrow();
    }
}
