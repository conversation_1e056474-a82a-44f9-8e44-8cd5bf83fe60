package com.moego.svc.organization.service;

import static org.junit.jupiter.api.Assertions.assertEquals;

import com.moego.idl.models.organization.v1.Tenant;
import com.moego.idl.models.organization.v1.WeightUnit;
import com.moego.idl.service.business_customer.v1.BusinessCustomerInitializationServiceGrpc;
import com.moego.idl.service.business_customer.v1.InitSettingDef;
import com.moego.idl.service.business_customer.v1.InitializeBusinessCustomerModuleRequest;
import com.moego.idl.service.business_customer.v1.InitializeBusinessCustomerModuleResponse;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

@ExtendWith(MockitoExtension.class)
@ActiveProfiles("unit-test")
@SpringBootTest
@Transactional
@Disabled // please remove this line and replace jdbc source to moego_organization_unit_test if you want to test this
// file
public class CopyCustomerSettingTest {
    @Autowired
    BusinessCustomerInitializationServiceGrpc.BusinessCustomerInitializationServiceBlockingStub
            businessCustomerInitializationServiceBlockingStub;

    @Test
    void addTodo() {
        var request = InitializeBusinessCustomerModuleRequest.newBuilder()
                .setTenant(Tenant.newBuilder().setCompanyId(104251))
                .setInitSetting(InitSettingDef.newBuilder()
                        .setCopyFromTenant(Tenant.newBuilder().setCompanyId(104252))
                        .setWeightUnitValue(WeightUnit.POUND_VALUE))
                .build();
        InitializeBusinessCustomerModuleResponse initializeBusinessCustomerModuleResponse =
                businessCustomerInitializationServiceBlockingStub.initializeBusinessCustomerModule(request);
        long customerId = initializeBusinessCustomerModuleResponse.getCustomerId();
        System.out.println("customerId: " + customerId);
        assertEquals(104252, customerId);
    }
}
