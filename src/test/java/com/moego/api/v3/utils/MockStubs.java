/*
 * @since 2023-06-02 21:16:21
 * <AUTHOR> <<EMAIL>>
 */

package com.moego.api.v3.utils;

import static com.moego.api.v3.utils.TestUtils.ACCOUNTS;

import com.moego.idl.models.account.v1.AccountModel;
import com.moego.idl.service.account.v1.AccountSearchServiceGrpc.AccountSearchServiceBlockingStub;
import com.moego.idl.service.account.v1.AccountServiceGrpc.AccountServiceBlockingStub;
import com.moego.idl.service.account.v1.GetAccountRequest;
import com.moego.idl.service.account.v1.SessionServiceGrpc.SessionServiceBlockingStub;
import com.moego.idl.service.admin_permission.v1.RoleBindingServiceGrpc.RoleBindingServiceBlockingStub;
import com.moego.idl.service.admin_permission.v1.RoleServiceGrpc.RoleServiceBlockingStub;
import com.moego.idl.service.agreement.v1.AgreementRecordServiceGrpc;
import com.moego.idl.service.agreement.v1.AgreementServiceGrpc;
import com.moego.idl.service.ai_assistant.v1.ConversationTemplateServiceGrpc.ConversationTemplateServiceBlockingStub;
import com.moego.idl.service.membership.v1.MembershipServiceGrpc.MembershipServiceBlockingStub;
import com.moego.idl.service.metadata.v1.MetadataServiceGrpc.MetadataServiceBlockingStub;
import com.moego.idl.service.organization.v1.BusinessServiceGrpc;
import com.moego.idl.service.organization.v1.CompanyServiceGrpc;
import com.moego.idl.service.organization.v1.TaxRuleServiceGrpc.TaxRuleServiceBlockingStub;
import com.moego.idl.service.permission.v1.PermissionServiceGrpc;
import com.moego.idl.service.reporting.v1.ReportServiceGrpc;
import com.moego.idl.service.subscription.v1.SubscriptionServiceGrpc.SubscriptionServiceBlockingStub;
import com.moego.server.business.client.IBusinessBusinessClient;
import com.moego.server.business.client.IBusinessSessionClient;
import com.moego.server.business.client.IBusinessStaffClient;
import com.moego.server.customer.client.ICustomerCustomerClient;
import com.moego.server.customer.client.ICustomerGroomingClient;
import com.moego.server.message.client.INotificationClient;
import com.moego.server.payment.api.IPaymentBusinessDisputeService;
import com.moego.server.payment.api.IPaymentRefundService;
import com.moego.server.payment.api.IPaymentTransactionHistoryService;
import com.moego.server.payment.api.IPaymentVerificationService;
import com.moego.server.payment.client.IPaymentBillingClient;
import com.moego.server.payment.client.IPaymentSubscriptionClient;
import org.junit.jupiter.api.BeforeEach;
import org.springframework.boot.test.mock.mockito.MockBean;

// avoid creating multiple spring context
// every test class should extend this class
// no @MockBean is allowed in any test class
public abstract class MockStubs {
    @MockBean
    protected SubscriptionServiceBlockingStub subscriptionClient;

    @MockBean
    protected MembershipServiceBlockingStub membershipClient;

    @MockBean
    protected TaxRuleServiceBlockingStub taxClient;

    @MockBean
    protected ICustomerGroomingClient iCustomerGroomingClient;

    @MockBean
    protected IBusinessStaffClient iBusinessStaffClient;

    @MockBean
    protected IBusinessBusinessClient iBusinessBusinessClient;

    @MockBean
    protected AccountSearchServiceBlockingStub accountSearchServiceClient;

    @MockBean
    protected AccountServiceBlockingStub accountServiceClient;

    @MockBean
    protected IBusinessSessionClient iBusinessSessionClient;

    @MockBean
    protected ICustomerCustomerClient iCustomerCustomerClient;

    @MockBean
    protected INotificationClient iNotificationClient;

    @MockBean
    protected IPaymentSubscriptionClient iPaymentSubscriptionClient;

    @MockBean
    protected IPaymentBillingClient iPaymentBillingClient;

    @MockBean
    protected IPaymentBusinessDisputeService iPaymentBusinessDisputeClient;

    @MockBean
    protected IPaymentTransactionHistoryService iPaymentTransactionHistoryClient;

    @MockBean
    protected IPaymentVerificationService iPaymentVerificationClient;

    @MockBean
    protected IPaymentRefundService iPaymentRefundClient;

    @MockBean
    protected MetadataServiceBlockingStub metadataServiceClient;

    @MockBean
    protected RoleBindingServiceBlockingStub roleBindingServiceClient;

    @MockBean
    protected RoleServiceBlockingStub roleServiceClient;

    @MockBean
    protected SessionServiceBlockingStub sessionServiceClient;

    @MockBean
    protected ConversationTemplateServiceBlockingStub conversationTemplateServiceClient;

    @MockBean
    protected AgreementServiceGrpc.AgreementServiceBlockingStub agreementServiceClient;

    @MockBean
    protected AgreementRecordServiceGrpc.AgreementRecordServiceBlockingStub agreementRecordServiceClient;

    @MockBean
    protected ReportServiceGrpc.ReportServiceBlockingStub reportServiceBlockingStub;

    @MockBean
    protected PermissionServiceGrpc.PermissionServiceBlockingStub permissionServiceClient;

    @MockBean
    protected BusinessServiceGrpc.BusinessServiceBlockingStub businessServiceClient;

    @MockBean
    protected CompanyServiceGrpc.CompanyServiceBlockingStub companyServiceClient;

    @BeforeEach
    void mockCommonStubs() {
        TestUtils.mockStub(accountServiceClient::getAccount, this::toGetAccountResponse);
    }

    protected AccountModel toGetAccountResponse(GetAccountRequest request) {
        var builder = AccountModel.newBuilder();
        return switch (request.getIdentifierCase()) {
            case ID -> ACCOUNTS.getOrDefault(
                    request.getId(), builder.setId(request.getId()).build());
            case EMAIL -> ACCOUNTS.getOrDefault(
                    request.getEmail(), builder.setEmail(request.getEmail()).build());
            case PHONE_NUMBER -> ACCOUNTS.getOrDefault(
                    request.getPhoneNumber(),
                    builder.setPhoneNumber(request.getPhoneNumber()).build());
            default -> throw new IllegalArgumentException("invalid identifier case");
        };
    }
}
