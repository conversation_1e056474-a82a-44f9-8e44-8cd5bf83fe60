package com.moego.client.api.v1.online_booking.service;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.moego.client.api.v1.shared.helper.EvaluationHelper;
import com.moego.idl.client.online_booking.v1.BoardingAddon;
import com.moego.idl.client.online_booking.v1.Pet;
import com.moego.idl.client.online_booking.v1.PetServices;
import com.moego.idl.client.online_booking.v1.Service.Boarding;
import com.moego.idl.models.appointment.v1.PetDetailDateType;
import com.moego.idl.models.offering.v1.CustomizedServiceView;
import com.moego.idl.models.offering.v1.ServiceItemType;
import com.moego.idl.models.offering.v1.ServiceOverrideType;
import com.moego.idl.models.offering.v1.ServicePriceUnit;
import com.moego.idl.models.offering.v1.ServiceType;
import com.moego.idl.models.offering.v2.PetDetailCalculateResultDef;
import com.moego.idl.service.offering.v1.BatchGetCustomizedServiceRequest;
import com.moego.idl.service.offering.v1.BatchGetCustomizedServiceResponse;
import com.moego.idl.service.offering.v1.CustomizedServiceQueryCondition;
import com.moego.idl.service.offering.v1.ServiceManagementServiceGrpc;
import com.moego.idl.service.offering.v2.CalculatePricingRuleRequest;
import com.moego.idl.service.offering.v2.CalculatePricingRuleResponse;
import com.moego.idl.service.offering.v2.PricingRuleServiceGrpc;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class CashierServiceTest {

    @Mock
    private ServiceManagementServiceGrpc.ServiceManagementServiceBlockingStub serviceManagementServiceClient;

    @Mock
    private PricingRuleServiceGrpc.PricingRuleServiceBlockingStub pricingRuleStub;

    @Mock
    private EvaluationHelper evaluationHelper;

    @InjectMocks
    private CashierService cashierService;

    /**
     * {@link CashierService#getCustomizedServicePrice(Long, Integer, List)}
     */
    @Test
    void getCustomizedServicePrice_WhenExistingPet_WithBoardingService() {
        // Given
        Long companyId = 1L;
        int businessId = 1;
        long serviceId = 100L;
        long petId = 1L;

        Boarding boarding = Boarding.newBuilder()
                .setServiceId(serviceId)
                .setStartDate("2025-03-20")
                .setEndDate("2025-03-22")
                .build();

        List<PetServices> petDatas = List.of(PetServices.newBuilder()
                .setPet(Pet.newBuilder().setPetId(petId).build())
                .addServices(com.moego.idl.client.online_booking.v1.Service.newBuilder()
                        .setBoarding(boarding)
                        .build())
                .build());

        CustomizedServiceQueryCondition condition = CustomizedServiceQueryCondition.newBuilder()
                .setPetId(petId)
                .setServiceId(serviceId)
                .setBusinessId(businessId)
                .build();

        CustomizedServiceView boardingService = CustomizedServiceView.newBuilder()
                .setId(serviceId)
                .setType(ServiceType.SERVICE)
                .setServiceItemType(ServiceItemType.BOARDING)
                .setPriceUnit(ServicePriceUnit.PER_NIGHT)
                .setPrice(100)
                .setPriceOverrideType(ServiceOverrideType.SERVICE_OVERRIDE_TYPE_UNSPECIFIED)
                .build();

        BatchGetCustomizedServiceResponse response = BatchGetCustomizedServiceResponse.newBuilder()
                .addCustomizedServiceList(BatchGetCustomizedServiceResponse.ServiceWithCustomizedInfo.newBuilder()
                        .setQueryCondition(condition)
                        .setCustomizedService(boardingService)
                        .build())
                .build();

        when(serviceManagementServiceClient.batchGetCustomizedService(any(BatchGetCustomizedServiceRequest.class)))
                .thenReturn(response);

        PetDetailCalculateResultDef petDetail = PetDetailCalculateResultDef.newBuilder()
                .setPetId(petId)
                .setServiceId(serviceId)
                .setAdjustedPrice(100)
                .setServiceDate("2025-03-20")
                .build();

        when(pricingRuleStub.calculatePricingRule(any(CalculatePricingRuleRequest.class)))
                .thenReturn(CalculatePricingRuleResponse.newBuilder()
                        .addPetDetails(petDetail)
                        .build());

        when(evaluationHelper.listEvaluation(any())).thenReturn(Map.of());

        // When
        BigDecimal result = cashierService.getCustomizedServicePrice(companyId, businessId, petDatas);

        // Then
        assertThat(result).isEqualByComparingTo(new BigDecimal("200"));
    }

    /**
     * {@link CashierService#getCustomizedServicePrice(Long, Integer, List)}
     */
    @Test
    void getCustomizedServicePrice_WhenExistingPet_WithBoardingService_WithPricingRule() {
        // Given
        Long companyId = 1L;
        int businessId = 1;
        long serviceId = 100L;
        long petId = 1L;

        Boarding boarding = Boarding.newBuilder()
                .setServiceId(serviceId)
                .setStartDate("2025-03-20")
                .setEndDate("2025-03-22")
                .build();

        List<PetServices> petDatas = List.of(PetServices.newBuilder()
                .setPet(Pet.newBuilder().setPetId(petId).build())
                .addServices(com.moego.idl.client.online_booking.v1.Service.newBuilder()
                        .setBoarding(boarding)
                        .build())
                .build());

        CustomizedServiceQueryCondition condition = CustomizedServiceQueryCondition.newBuilder()
                .setPetId(petId)
                .setServiceId(serviceId)
                .setBusinessId(businessId)
                .build();

        CustomizedServiceView boardingService = CustomizedServiceView.newBuilder()
                .setId(serviceId)
                .setType(ServiceType.SERVICE)
                .setServiceItemType(ServiceItemType.BOARDING)
                .setPriceUnit(ServicePriceUnit.PER_NIGHT)
                .setPrice(100)
                .setPriceOverrideType(ServiceOverrideType.SERVICE_OVERRIDE_TYPE_UNSPECIFIED)
                .build();

        BatchGetCustomizedServiceResponse response = BatchGetCustomizedServiceResponse.newBuilder()
                .addCustomizedServiceList(BatchGetCustomizedServiceResponse.ServiceWithCustomizedInfo.newBuilder()
                        .setQueryCondition(condition)
                        .setCustomizedService(boardingService)
                        .build())
                .build();

        when(serviceManagementServiceClient.batchGetCustomizedService(any(BatchGetCustomizedServiceRequest.class)))
                .thenReturn(response);

        PetDetailCalculateResultDef petDetail = PetDetailCalculateResultDef.newBuilder()
                .setPetId(petId)
                .setServiceId(serviceId)
                .setAdjustedPrice(90)
                .setServiceDate("2025-03-20")
                .build();

        when(pricingRuleStub.calculatePricingRule(any(CalculatePricingRuleRequest.class)))
                .thenReturn(CalculatePricingRuleResponse.newBuilder()
                        .addPetDetails(petDetail)
                        .build());

        when(evaluationHelper.listEvaluation(any())).thenReturn(Map.of());

        // When
        BigDecimal result = cashierService.getCustomizedServicePrice(companyId, businessId, petDatas);

        // Then
        assertThat(result).isEqualByComparingTo(new BigDecimal("190"));
    }

    /**
     * {@link CashierService#getCustomizedServicePrice(Long, Integer, List)}
     */
    @Test
    void getCustomizedServicePrice_WhenNewPet_WithBoardingService_WithPricingRule() {
        // Given
        Long companyId = 1L;
        int businessId = 1;
        long serviceId = 100L;
        long virtualPetId = 1L;

        Boarding boarding = Boarding.newBuilder()
                .setServiceId(serviceId)
                .setStartDate("2025-03-20")
                .setEndDate("2025-03-22")
                .build();

        List<PetServices> petDatas = List.of(PetServices.newBuilder()
                .setPet(Pet.newBuilder().build())
                .addServices(com.moego.idl.client.online_booking.v1.Service.newBuilder()
                        .setBoarding(boarding)
                        .build())
                .build());

        CustomizedServiceQueryCondition condition = CustomizedServiceQueryCondition.newBuilder()
                .setServiceId(serviceId)
                .setBusinessId(businessId)
                .build();

        CustomizedServiceView boardingService = CustomizedServiceView.newBuilder()
                .setId(serviceId)
                .setType(ServiceType.SERVICE)
                .setServiceItemType(ServiceItemType.BOARDING)
                .setPriceUnit(ServicePriceUnit.PER_NIGHT)
                .setPrice(100)
                .setPriceOverrideType(ServiceOverrideType.SERVICE_OVERRIDE_TYPE_UNSPECIFIED)
                .build();

        BatchGetCustomizedServiceResponse response = BatchGetCustomizedServiceResponse.newBuilder()
                .addCustomizedServiceList(BatchGetCustomizedServiceResponse.ServiceWithCustomizedInfo.newBuilder()
                        .setQueryCondition(condition)
                        .setCustomizedService(boardingService)
                        .build())
                .build();

        when(serviceManagementServiceClient.batchGetCustomizedService(any(BatchGetCustomizedServiceRequest.class)))
                .thenReturn(response);

        PetDetailCalculateResultDef petDetail = PetDetailCalculateResultDef.newBuilder()
                .setPetId(virtualPetId)
                .setServiceId(serviceId)
                .setAdjustedPrice(90)
                .setServiceDate("2025-03-20")
                .build();

        when(pricingRuleStub.calculatePricingRule(any(CalculatePricingRuleRequest.class)))
                .thenReturn(CalculatePricingRuleResponse.newBuilder()
                        .addPetDetails(petDetail)
                        .build());

        when(evaluationHelper.listEvaluation(any())).thenReturn(Map.of());

        // When
        BigDecimal result = cashierService.getCustomizedServicePrice(companyId, businessId, petDatas);

        // Then
        assertThat(result).isEqualByComparingTo(new BigDecimal("190"));
    }

    /**
     * {@link CashierService#getCustomizedServicePrice(Long, Integer, List)}
     */
    @Test
    void getCustomizedServicePrice_WhenNewPet_WithBoardingServiceAndAddOn_WithPricingRule() {
        // Given
        Long companyId = 1L;
        int businessId = 1;
        long serviceId = 100L;
        long addOnId = 200L;
        long virtualPetId = 1L;

        Boarding boarding = Boarding.newBuilder()
                .setServiceId(serviceId)
                .setStartDate("2025-03-20")
                .setEndDate("2025-03-22")
                .addAddons(BoardingAddon.newBuilder()
                        .setId(addOnId)
                        .setQuantityPerDay(1)
                        .setDateType(PetDetailDateType.PET_DETAIL_DATE_EVERYDAY)
                        .build())
                .build();

        List<PetServices> petDatas = List.of(PetServices.newBuilder()
                .setPet(Pet.newBuilder().build())
                .addServices(com.moego.idl.client.online_booking.v1.Service.newBuilder()
                        .setBoarding(boarding)
                        .build())
                .build());

        CustomizedServiceQueryCondition serviceCondition = CustomizedServiceQueryCondition.newBuilder()
                .setServiceId(serviceId)
                .setBusinessId(businessId)
                .build();

        CustomizedServiceView boardingService = CustomizedServiceView.newBuilder()
                .setId(serviceId)
                .setType(ServiceType.SERVICE)
                .setServiceItemType(ServiceItemType.BOARDING)
                .setPriceUnit(ServicePriceUnit.PER_NIGHT)
                .setPrice(100)
                .setPriceOverrideType(ServiceOverrideType.SERVICE_OVERRIDE_TYPE_UNSPECIFIED)
                .build();

        CustomizedServiceQueryCondition addOnCondition = CustomizedServiceQueryCondition.newBuilder()
                .setServiceId(addOnId)
                .setBusinessId(businessId)
                .build();

        CustomizedServiceView addOnService = CustomizedServiceView.newBuilder()
                .setId(addOnId)
                .setType(ServiceType.ADDON)
                .setServiceItemType(ServiceItemType.GROOMING)
                .setPriceUnit(ServicePriceUnit.PER_SESSION)
                .setPrice(50)
                .setPriceOverrideType(ServiceOverrideType.SERVICE_OVERRIDE_TYPE_UNSPECIFIED)
                .build();

        BatchGetCustomizedServiceResponse response = BatchGetCustomizedServiceResponse.newBuilder()
                .addCustomizedServiceList(BatchGetCustomizedServiceResponse.ServiceWithCustomizedInfo.newBuilder()
                        .setQueryCondition(serviceCondition)
                        .setCustomizedService(boardingService)
                        .build())
                .addCustomizedServiceList(BatchGetCustomizedServiceResponse.ServiceWithCustomizedInfo.newBuilder()
                        .setQueryCondition(addOnCondition)
                        .setCustomizedService(addOnService)
                        .build())
                .build();

        when(serviceManagementServiceClient.batchGetCustomizedService(any(BatchGetCustomizedServiceRequest.class)))
                .thenReturn(response);

        PetDetailCalculateResultDef petDetail = PetDetailCalculateResultDef.newBuilder()
                .setPetId(virtualPetId)
                .setServiceId(serviceId)
                .setAdjustedPrice(90)
                .setServiceDate("2025-03-20")
                .build();

        when(pricingRuleStub.calculatePricingRule(any(CalculatePricingRuleRequest.class)))
                .thenReturn(CalculatePricingRuleResponse.newBuilder()
                        .addPetDetails(petDetail)
                        .build());

        when(evaluationHelper.listEvaluation(any())).thenReturn(Map.of());

        // When
        BigDecimal result = cashierService.getCustomizedServicePrice(companyId, businessId, petDatas);

        // Then
        assertThat(result).isEqualByComparingTo(new BigDecimal("290"));
    }
}
