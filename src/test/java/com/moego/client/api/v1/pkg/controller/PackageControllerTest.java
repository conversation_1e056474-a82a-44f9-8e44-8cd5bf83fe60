package com.moego.client.api.v1.pkg.controller;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.google.protobuf.Timestamp;
import com.moego.client.api.v1.pkg.service.PackageCustomerService;
import com.moego.client.api.v1.pkg.service.PackageOrderService;
import com.moego.client.api.v1.pkg.service.PackagePaymentService;
import com.moego.client.api.v1.pkg.service.PackageService;
import com.moego.client.api.v1.shared.helper.BusinessHelper;
import com.moego.client.api.v1.shared.helper.OfferingHelper;
import com.moego.idl.client.pkg.v1.ListCustomerPackagesParams;
import com.moego.idl.client.pkg.v1.ListCustomerPackagesResult;
import com.moego.idl.client.pkg.v1.ListPackagesParams;
import com.moego.idl.client.pkg.v1.ListPackagesResult;
import com.moego.idl.models.offering.v1.ServiceBriefView;
import com.moego.idl.models.pkg.v1.CustomerPackageModel;
import com.moego.idl.models.pkg.v1.Item;
import com.moego.idl.models.pkg.v1.PackageModel;
import com.moego.idl.models.pkg.v1.RemainingItem;
import com.moego.idl.service.business_customer.v1.BusinessCustomerServiceGrpc;
import com.moego.idl.utils.v2.PaginationRequest;
import com.moego.idl.utils.v2.PaginationResponse;
import com.moego.lib.common.auth.AuthContext;
import com.moego.server.grooming.api.IGroomingOnlineBookingService;
import com.moego.server.grooming.dto.ob.OBBusinessDTO;
import com.moego.server.grooming.params.ob.OBAnonymousParams;
import io.grpc.stub.StreamObserver;
import java.util.Arrays;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class PackageControllerTest {

    @Mock
    private PackageService packageService;

    @Mock
    private IGroomingOnlineBookingService onlineBookingService;

    @Mock
    private BusinessCustomerServiceGrpc.BusinessCustomerServiceBlockingStub businessCustomerService;

    @Mock
    private PackageOrderService orderService;

    @Mock
    private PackageCustomerService packageCustomerService;

    @Mock
    private PackagePaymentService paymentService;

    @Mock
    private BusinessHelper businessHelper;

    @Mock
    private OfferingHelper offeringHelper;

    @Mock
    private StreamObserver<ListPackagesResult> packagesResponseObserver;

    @Mock
    private StreamObserver<ListCustomerPackagesResult> customerPackagesResponseObserver;

    @InjectMocks
    private PackageController packageController;

    @Captor
    private ArgumentCaptor<ListPackagesResult> packagesResultCaptor;

    @Captor
    private ArgumentCaptor<ListCustomerPackagesResult> customerPackagesResultCaptor;

    @BeforeEach
    void globalSetUp() {
        List<ServiceBriefView> serviceBriefViews = Arrays.asList(
                ServiceBriefView.newBuilder()
                        .setId(1L)
                        .setName("Grooming Service 1")
                        .setDescription("Basic grooming service")
                        .setPrice(2500) // $25.00
                        .setDuration(60) // 60 minutes
                        .build(),
                ServiceBriefView.newBuilder()
                        .setId(2L)
                        .setName("Grooming Service 2")
                        .setDescription("Premium grooming service")
                        .setPrice(4000) // $40.00
                        .setDuration(90) // 90 minutes
                        .build());

        when(offeringHelper.getServiceBriefViews(anyLong(), anyList())).thenReturn(serviceBriefViews);
    }

    @Nested
    @DisplayName("listPackages")
    class ListPackagesTests {

        private OBBusinessDTO businessDTO;
        private List<PackageModel> allPackages;
        private ListPackagesParams request;

        @BeforeEach
        void setUp() {
            // Create business DTO
            businessDTO = new OBBusinessDTO();
            businessDTO.setBusinessId(1);
            businessDTO.setCompanyId(100L);

            // Create service items
            Item item1 = Item.newBuilder()
                    .setQuantity(2)
                    .addAllServiceIds(Arrays.asList(1L))
                    .build();

            Item item2 = Item.newBuilder()
                    .setQuantity(1)
                    .addAllServiceIds(Arrays.asList(2L))
                    .build();

            Item combinedItem = Item.newBuilder()
                    .setQuantity(1)
                    .addAllServiceIds(Arrays.asList(1L, 2L))
                    .build();

            // Create package list with services
            PackageModel package1 = PackageModel.newBuilder()
                    .setId(1)
                    .setName("Package 1")
                    .setIsActive(true)
                    .setEnableOnlineBooking(true)
                    .setPrice(10000) // $100.00
                    .setDescription("Package with basic services")
                    .addItems(item1)
                    .build();

            PackageModel package2 = PackageModel.newBuilder()
                    .setId(2)
                    .setName("Package 2")
                    .setIsActive(true)
                    .setEnableOnlineBooking(true)
                    .setPrice(15000) // $150.00
                    .setDescription("Package with premium services")
                    .addItems(item2)
                    .build();

            PackageModel package3 = PackageModel.newBuilder()
                    .setId(3)
                    .setName("Package 3")
                    .setIsActive(false)
                    .setEnableOnlineBooking(true)
                    .setPrice(12000) // $120.00
                    .setDescription("Inactive package with services")
                    .addItems(combinedItem)
                    .build();

            PackageModel package4 = PackageModel.newBuilder()
                    .setId(4)
                    .setName("Package 4")
                    .setIsActive(true)
                    .setEnableOnlineBooking(false)
                    .setPrice(9000) // $90.00
                    .setDescription("Package not available for online booking")
                    .build();

            allPackages = Arrays.asList(package1, package2, package3, package4);

            // Create request parameters
            request = ListPackagesParams.newBuilder()
                    .setDomain("test.domain.com")
                    .setPagination(PaginationRequest.newBuilder()
                            .setPageNum(1)
                            .setPageSize(10)
                            .build())
                    .setOnlyActive(true)
                    .build();

            // Mock service calls
            when(onlineBookingService.mustGetBusinessDTOByOBNameOrDomain(any(OBAnonymousParams.class)))
                    .thenReturn(businessDTO);
            when(packageService.listPackages(anyLong(), anyList())).thenReturn(allPackages);
        }

        @Test
        @DisplayName("Should return filtered package list")
        void shouldReturnFilteredPackages() {
            // Execute test
            packageController.listPackages(request, packagesResponseObserver);

            // Verify results
            verify(packagesResponseObserver).onNext(packagesResultCaptor.capture());

            ListPackagesResult result = packagesResultCaptor.getValue();

            // Should only return packages that are active and enabled for online booking
            assertThat(result.getPackagesList()).hasSize(2).extracting("id").containsExactly(1L, 2L);

            // Verify service information
            PackageModel firstPackage = result.getPackages(0);
            assertThat(firstPackage.getItemsCount()).isEqualTo(1);
            assertThat(firstPackage.getItems(0).getServiceIdsCount()).isEqualTo(1);
            assertThat(firstPackage.getItems(0).getServiceIds(0)).isEqualTo(1L);
            assertThat(firstPackage.getItems(0).getQuantity()).isEqualTo(2);

            PackageModel secondPackage = result.getPackages(1);
            assertThat(secondPackage.getItemsCount()).isEqualTo(1);
            assertThat(secondPackage.getItems(0).getServiceIds(0)).isEqualTo(2L);
            assertThat(secondPackage.getItems(0).getQuantity()).isEqualTo(1);

            // Verify pagination info
            PaginationResponse pagination = result.getPagination();
            assertThat(pagination.getPageNum()).isEqualTo(1);
            assertThat(pagination.getPageSize()).isEqualTo(10);
            assertThat(pagination.getTotal()).isEqualTo(2); // Total should be the filtered count
        }

        @Test
        @DisplayName("Should return all online booking enabled packages when onlyActive is false")
        void shouldReturnAllOnlineBookingEnabledPackages() {
            // Modify request params to not restrict to active only
            request = ListPackagesParams.newBuilder()
                    .setDomain("test.domain.com")
                    .setPagination(PaginationRequest.newBuilder()
                            .setPageNum(1)
                            .setPageSize(10)
                            .build())
                    .setOnlyActive(false)
                    .build();

            // Execute test
            packageController.listPackages(request, packagesResponseObserver);

            // Verify results
            verify(packagesResponseObserver).onNext(packagesResultCaptor.capture());

            ListPackagesResult result = packagesResultCaptor.getValue();

            // Should return all packages enabled for online booking, including inactive
            // ones
            assertThat(result.getPackagesList())
                    .hasSize(3)
                    .extracting("enableOnlineBooking")
                    .containsOnly(true);

            // Verify package with multiple service IDs
            PackageModel package3 = result.getPackagesList().stream()
                    .filter(p -> p.getId() == 3)
                    .findFirst()
                    .orElseThrow();

            assertThat(package3.getItemsCount()).isEqualTo(1);
            Item item = package3.getItems(0);
            assertThat(item.getServiceIdsCount()).isEqualTo(2);
            assertThat(item.getServiceIdsList()).containsExactly(1L, 2L);

            // Verify pagination info
            PaginationResponse pagination = result.getPagination();
            assertThat(pagination.getPageNum()).isEqualTo(1);
            assertThat(pagination.getPageSize()).isEqualTo(10);
            assertThat(pagination.getTotal()).isEqualTo(3); // Total should be the filtered count
        }
    }

    @Nested
    @DisplayName("listCustomerPackages")
    class ListCustomerPackagesTests {

        private long futureTimestampSeconds;
        private long pastTimestampSeconds;
        private OBBusinessDTO businessDTO;

        @BeforeEach
        void setUp() {
            // Future timestamp (not expired)
            futureTimestampSeconds = System.currentTimeMillis() / 1000 + 86400; // 1 day in the future
            // Past timestamp (expired)
            pastTimestampSeconds = System.currentTimeMillis() / 1000 - 86400; // 1 day in the past

            // Setup business DTO
            businessDTO = new OBBusinessDTO();
            businessDTO.setBusinessId(1);
            businessDTO.setCompanyId(100L);

            // Mock service call for all tests in this class
            when(onlineBookingService.mustGetBusinessDTOByOBNameOrDomain(any(OBAnonymousParams.class)))
                    .thenReturn(businessDTO);
        }

        @Test
        @DisplayName("Should return customer's package list")
        void shouldReturnCustomerPackages() {
            // Create remaining items
            RemainingItem item1 = RemainingItem.newBuilder()
                    .addAllServiceIds(Arrays.asList(1L))
                    .setTotalQuantity(3)
                    .setRemainingQuantity(2) // Used once
                    .build();

            RemainingItem item2 = RemainingItem.newBuilder()
                    .addAllServiceIds(Arrays.asList(2L))
                    .setTotalQuantity(1)
                    .setRemainingQuantity(1) // Unused
                    .build();

            // Mock packages with services
            CustomerPackageModel package1 = CustomerPackageModel.newBuilder()
                    .setPackageId(1L)
                    .setCustomerId(123L)
                    .setPackageName("Customer Package 1")
                    .setEndTime(Timestamp.newBuilder()
                            .setSeconds(futureTimestampSeconds)
                            .build())
                    .addRemainingItems(item1)
                    .build();

            CustomerPackageModel package2 = CustomerPackageModel.newBuilder()
                    .setPackageId(2L)
                    .setCustomerId(123L)
                    .setPackageName("Customer Package 2")
                    .setEndTime(Timestamp.newBuilder()
                            .setSeconds(futureTimestampSeconds)
                            .build())
                    .addRemainingItems(item2)
                    .build();

            List<CustomerPackageModel> customerPackages = Arrays.asList(package1, package2);

            // Request parameters
            ListCustomerPackagesParams request = ListCustomerPackagesParams.newBuilder()
                    .setDomain("test.domain.com")
                    .build();

            long customerId = 123L;
            try (MockedStatic<AuthContext> authContextMock = Mockito.mockStatic(AuthContext.class)) {
                // Mock static method
                AuthContext authContext = Mockito.mock(AuthContext.class);
                authContextMock.when(AuthContext::get).thenReturn(authContext);
                when(authContext.customerId()).thenReturn(customerId);

                // Mock service return
                when(packageService.listCustomerPackages(eq(100L), eq(1L), eq(customerId)))
                        .thenReturn(customerPackages);

                // Execute test
                packageController.listCustomerPackages(request, customerPackagesResponseObserver);
            }

            // Verify results
            verify(customerPackagesResponseObserver).onNext(customerPackagesResultCaptor.capture());

            ListCustomerPackagesResult result = customerPackagesResultCaptor.getValue();

            // Verify returned list
            assertThat(result.getPackagesList())
                    .hasSize(2)
                    .extracting("packageName")
                    .containsExactly("Customer Package 1", "Customer Package 2");

            // Verify service information
            CustomerPackageModel firstPackage = result.getPackages(0);
            assertThat(firstPackage.getRemainingItemsCount()).isEqualTo(1);
            assertThat(firstPackage.getRemainingItems(0).getServiceIds(0)).isEqualTo(1L);
            assertThat(firstPackage.getRemainingItems(0).getTotalQuantity()).isEqualTo(3);
            assertThat(firstPackage.getRemainingItems(0).getRemainingQuantity()).isEqualTo(2);

            CustomerPackageModel secondPackage = result.getPackages(1);
            assertThat(secondPackage.getRemainingItemsCount()).isEqualTo(1);
            assertThat(secondPackage.getRemainingItems(0).getServiceIds(0)).isEqualTo(2L);
            assertThat(secondPackage.getRemainingItems(0).getRemainingQuantity())
                    .isEqualTo(1);
        }

        @Test
        @DisplayName("Should filter packages by expiration status - only active")
        void shouldFilterPackagesByExpirationStatusActive() {
            // Create remaining items
            RemainingItem item1 = RemainingItem.newBuilder()
                    .addAllServiceIds(Arrays.asList(1L))
                    .setTotalQuantity(2)
                    .setRemainingQuantity(1)
                    .build();

            RemainingItem item2 = RemainingItem.newBuilder()
                    .addAllServiceIds(Arrays.asList(2L))
                    .setTotalQuantity(3)
                    .setRemainingQuantity(2)
                    .build();

            // Mock packages - mix of expired and active
            CustomerPackageModel activePackage1 = CustomerPackageModel.newBuilder()
                    .setPackageId(1L)
                    .setCustomerId(123L)
                    .setPackageName("Active Package 1")
                    .setEndTime(Timestamp.newBuilder()
                            .setSeconds(futureTimestampSeconds)
                            .build())
                    .addRemainingItems(item1)
                    .build();

            CustomerPackageModel activePackage2 = CustomerPackageModel.newBuilder()
                    .setPackageId(2L)
                    .setCustomerId(123L)
                    .setPackageName("Active Package 2")
                    .setEndTime(Timestamp.newBuilder()
                            .setSeconds(futureTimestampSeconds)
                            .build())
                    .addRemainingItems(item2)
                    .build();

            CustomerPackageModel expiredPackage = CustomerPackageModel.newBuilder()
                    .setPackageId(3L)
                    .setCustomerId(123L)
                    .setPackageName("Expired Package")
                    .setEndTime(Timestamp.newBuilder()
                            .setSeconds(pastTimestampSeconds)
                            .build())
                    .build();

            List<CustomerPackageModel> allCustomerPackages =
                    Arrays.asList(activePackage1, activePackage2, expiredPackage);

            // Request parameters with filter for non-expired packages
            ListCustomerPackagesParams request = ListCustomerPackagesParams.newBuilder()
                    .setDomain("test.domain.com")
                    .setFilter(ListCustomerPackagesParams.Filter.newBuilder()
                            .setIsExpired(false)
                            .build())
                    .build();

            long customerId = 123L;
            try (MockedStatic<AuthContext> authContextMock = Mockito.mockStatic(AuthContext.class)) {
                // Mock static method
                AuthContext authContext = Mockito.mock(AuthContext.class);
                authContextMock.when(AuthContext::get).thenReturn(authContext);
                when(authContext.customerId()).thenReturn(customerId);

                // Mock service return
                when(packageService.listCustomerPackages(eq(100L), eq(1L), eq(customerId)))
                        .thenReturn(allCustomerPackages);

                // Execute test
                packageController.listCustomerPackages(request, customerPackagesResponseObserver);
            }

            // Verify results
            verify(customerPackagesResponseObserver).onNext(customerPackagesResultCaptor.capture());

            ListCustomerPackagesResult result = customerPackagesResultCaptor.getValue();

            // Verify only active packages are returned (not expired)
            assertThat(result.getPackagesList())
                    .hasSize(2)
                    .extracting("packageName")
                    .containsExactly("Active Package 1", "Active Package 2");

            // Verify services in the first package
            CustomerPackageModel activePackage = result.getPackages(0);
            assertThat(activePackage.getRemainingItemsCount()).isEqualTo(1);
            assertThat(activePackage.getRemainingItems(0).getServiceIds(0)).isEqualTo(1L);
            assertThat(activePackage.getRemainingItems(0).getRemainingQuantity())
                    .isEqualTo(1);
        }

        @Test
        @DisplayName("Should filter packages by expiration status - only expired")
        void shouldFilterPackagesByExpirationStatusExpired() {
            // Create remaining items
            RemainingItem expiredItem1 = RemainingItem.newBuilder()
                    .addAllServiceIds(Arrays.asList(1L))
                    .setTotalQuantity(2)
                    .setRemainingQuantity(0) // All used
                    .build();

            RemainingItem expiredItem2 = RemainingItem.newBuilder()
                    .addAllServiceIds(Arrays.asList(2L))
                    .setTotalQuantity(3)
                    .setRemainingQuantity(1) // 1 remaining but expired
                    .build();

            // Mock packages - mix of expired and active
            CustomerPackageModel activePackage1 = CustomerPackageModel.newBuilder()
                    .setPackageId(1L)
                    .setCustomerId(123L)
                    .setPackageName("Active Package 1")
                    .setEndTime(Timestamp.newBuilder()
                            .setSeconds(futureTimestampSeconds)
                            .build())
                    .build();

            CustomerPackageModel expiredPackage1 = CustomerPackageModel.newBuilder()
                    .setPackageId(3L)
                    .setCustomerId(123L)
                    .setPackageName("Expired Package 1")
                    .setEndTime(Timestamp.newBuilder()
                            .setSeconds(pastTimestampSeconds)
                            .build())
                    .addRemainingItems(expiredItem1)
                    .build();

            CustomerPackageModel expiredPackage2 = CustomerPackageModel.newBuilder()
                    .setPackageId(4L)
                    .setCustomerId(123L)
                    .setPackageName("Expired Package 2")
                    .setEndTime(Timestamp.newBuilder()
                            .setSeconds(pastTimestampSeconds - 1000)
                            .build())
                    .addRemainingItems(expiredItem2)
                    .build();

            List<CustomerPackageModel> allCustomerPackages =
                    Arrays.asList(activePackage1, expiredPackage1, expiredPackage2);

            // Request parameters with filter for expired packages
            ListCustomerPackagesParams request = ListCustomerPackagesParams.newBuilder()
                    .setDomain("test.domain.com")
                    .setFilter(ListCustomerPackagesParams.Filter.newBuilder()
                            .setIsExpired(true)
                            .build())
                    .build();

            long customerId = 123L;
            try (MockedStatic<AuthContext> authContextMock = Mockito.mockStatic(AuthContext.class)) {
                // Mock static method
                AuthContext authContext = Mockito.mock(AuthContext.class);
                authContextMock.when(AuthContext::get).thenReturn(authContext);
                when(authContext.customerId()).thenReturn(customerId);

                // Mock service return
                when(packageService.listCustomerPackages(eq(100L), eq(1L), eq(customerId)))
                        .thenReturn(allCustomerPackages);

                // Execute test
                packageController.listCustomerPackages(request, customerPackagesResponseObserver);
            }

            // Verify results
            verify(customerPackagesResponseObserver).onNext(customerPackagesResultCaptor.capture());

            ListCustomerPackagesResult result = customerPackagesResultCaptor.getValue();

            // Verify only expired packages are returned
            assertThat(result.getPackagesList())
                    .hasSize(2)
                    .extracting("packageName")
                    .containsExactly("Expired Package 1", "Expired Package 2");

            // Verify service information in expired packages
            CustomerPackageModel firstExpiredPackage = result.getPackages(0);
            assertThat(firstExpiredPackage.getRemainingItemsCount()).isEqualTo(1);
            assertThat(firstExpiredPackage.getRemainingItems(0).getServiceIds(0))
                    .isEqualTo(1L);
            assertThat(firstExpiredPackage.getRemainingItems(0).getRemainingQuantity())
                    .isEqualTo(0);

            CustomerPackageModel secondExpiredPackage = result.getPackages(1);
            assertThat(secondExpiredPackage.getRemainingItemsCount()).isEqualTo(1);
            assertThat(secondExpiredPackage.getRemainingItems(0).getServiceIds(0))
                    .isEqualTo(2L);
            assertThat(secondExpiredPackage.getRemainingItems(0).getRemainingQuantity())
                    .isEqualTo(1);
        }
    }
}
