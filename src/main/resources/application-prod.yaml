spring:
  config:
    import:
      - "aws-secretsmanager:moego/production/datasource?prefix=secret.datasource.default."
      - "aws-secretsmanager:moego/production/datasource/grooming?prefix=secret.datasource.grooming."
      - "aws-secretsmanager:moego/production/redis?prefix=secret.redis."
      - "aws-secretsmanager:moego/production/mq?prefix=secret.mq."
      - "aws-secretsmanager:moego/production/growthbook?prefix=secret.growthbook."
  datasource:
    hikari:
      # See https://github.com/brettwooldridge/HikariCP?tab=readme-ov-file#frequently-used
      maximum-pool-size: 100
  data:
    redis:
      key:
        delimiter: ':'
        prefix: ''


moego:
  messaging:
    pulsar:
      tenant: moego
  daily-report:
    client-url: https://client.moego.pet/daily/report/%s
