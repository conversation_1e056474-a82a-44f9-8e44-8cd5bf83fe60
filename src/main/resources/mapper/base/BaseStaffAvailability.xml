<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.moego.svc.organization.mapper.base.BaseStaffAvailability">
  <resultMap id="BaseResultMap" type="com.moego.svc.organization.entity.StaffAvailability">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="company_id" jdbcType="BIGINT" property="companyId" />
    <result column="business_id" jdbcType="BIGINT" property="businessId" />
    <result column="staff_id" jdbcType="BIGINT" property="staffId" />
    <result column="slot_schedule_type" jdbcType="INTEGER" property="slotScheduleType" />
    <result column="slot_start_sunday" jdbcType="DATE" property="slotStartSunday" />
    <result column="time_schedule_type" jdbcType="INTEGER" property="timeScheduleType" />
    <result column="time_start_sunday" jdbcType="DATE" property="timeStartSunday" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, company_id, business_id, staff_id, slot_schedule_type, slot_start_sunday, time_schedule_type, 
    time_start_sunday, created_at, updated_at
  </sql>
  <select id="selectByExample" parameterType="com.moego.svc.organization.entity.StaffAvailabilityExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from staff_availability
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from staff_availability
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from staff_availability
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.moego.svc.organization.entity.StaffAvailabilityExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from staff_availability
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.moego.svc.organization.entity.StaffAvailability">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into staff_availability (company_id, business_id, staff_id, 
      slot_schedule_type, slot_start_sunday, time_schedule_type, 
      time_start_sunday, created_at, updated_at
      )
    values (#{companyId,jdbcType=BIGINT}, #{businessId,jdbcType=BIGINT}, #{staffId,jdbcType=BIGINT}, 
      #{slotScheduleType,jdbcType=INTEGER}, #{slotStartSunday,jdbcType=DATE}, #{timeScheduleType,jdbcType=INTEGER}, 
      #{timeStartSunday,jdbcType=DATE}, #{createdAt,jdbcType=TIMESTAMP}, #{updatedAt,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.moego.svc.organization.entity.StaffAvailability">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into staff_availability
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="companyId != null">
        company_id,
      </if>
      <if test="businessId != null">
        business_id,
      </if>
      <if test="staffId != null">
        staff_id,
      </if>
      <if test="slotScheduleType != null">
        slot_schedule_type,
      </if>
      <if test="slotStartSunday != null">
        slot_start_sunday,
      </if>
      <if test="timeScheduleType != null">
        time_schedule_type,
      </if>
      <if test="timeStartSunday != null">
        time_start_sunday,
      </if>
      <if test="createdAt != null">
        created_at,
      </if>
      <if test="updatedAt != null">
        updated_at,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="companyId != null">
        #{companyId,jdbcType=BIGINT},
      </if>
      <if test="businessId != null">
        #{businessId,jdbcType=BIGINT},
      </if>
      <if test="staffId != null">
        #{staffId,jdbcType=BIGINT},
      </if>
      <if test="slotScheduleType != null">
        #{slotScheduleType,jdbcType=INTEGER},
      </if>
      <if test="slotStartSunday != null">
        #{slotStartSunday,jdbcType=DATE},
      </if>
      <if test="timeScheduleType != null">
        #{timeScheduleType,jdbcType=INTEGER},
      </if>
      <if test="timeStartSunday != null">
        #{timeStartSunday,jdbcType=DATE},
      </if>
      <if test="createdAt != null">
        #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedAt != null">
        #{updatedAt,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.moego.svc.organization.entity.StaffAvailabilityExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from staff_availability
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update staff_availability
    <set>
      <if test="row.id != null">
        id = #{row.id,jdbcType=BIGINT},
      </if>
      <if test="row.companyId != null">
        company_id = #{row.companyId,jdbcType=BIGINT},
      </if>
      <if test="row.businessId != null">
        business_id = #{row.businessId,jdbcType=BIGINT},
      </if>
      <if test="row.staffId != null">
        staff_id = #{row.staffId,jdbcType=BIGINT},
      </if>
      <if test="row.slotScheduleType != null">
        slot_schedule_type = #{row.slotScheduleType,jdbcType=INTEGER},
      </if>
      <if test="row.slotStartSunday != null">
        slot_start_sunday = #{row.slotStartSunday,jdbcType=DATE},
      </if>
      <if test="row.timeScheduleType != null">
        time_schedule_type = #{row.timeScheduleType,jdbcType=INTEGER},
      </if>
      <if test="row.timeStartSunday != null">
        time_start_sunday = #{row.timeStartSunday,jdbcType=DATE},
      </if>
      <if test="row.createdAt != null">
        created_at = #{row.createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="row.updatedAt != null">
        updated_at = #{row.updatedAt,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update staff_availability
    set id = #{row.id,jdbcType=BIGINT},
      company_id = #{row.companyId,jdbcType=BIGINT},
      business_id = #{row.businessId,jdbcType=BIGINT},
      staff_id = #{row.staffId,jdbcType=BIGINT},
      slot_schedule_type = #{row.slotScheduleType,jdbcType=INTEGER},
      slot_start_sunday = #{row.slotStartSunday,jdbcType=DATE},
      time_schedule_type = #{row.timeScheduleType,jdbcType=INTEGER},
      time_start_sunday = #{row.timeStartSunday,jdbcType=DATE},
      created_at = #{row.createdAt,jdbcType=TIMESTAMP},
      updated_at = #{row.updatedAt,jdbcType=TIMESTAMP}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.moego.svc.organization.entity.StaffAvailability">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update staff_availability
    <set>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=BIGINT},
      </if>
      <if test="businessId != null">
        business_id = #{businessId,jdbcType=BIGINT},
      </if>
      <if test="staffId != null">
        staff_id = #{staffId,jdbcType=BIGINT},
      </if>
      <if test="slotScheduleType != null">
        slot_schedule_type = #{slotScheduleType,jdbcType=INTEGER},
      </if>
      <if test="slotStartSunday != null">
        slot_start_sunday = #{slotStartSunday,jdbcType=DATE},
      </if>
      <if test="timeScheduleType != null">
        time_schedule_type = #{timeScheduleType,jdbcType=INTEGER},
      </if>
      <if test="timeStartSunday != null">
        time_start_sunday = #{timeStartSunday,jdbcType=DATE},
      </if>
      <if test="createdAt != null">
        created_at = #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedAt != null">
        updated_at = #{updatedAt,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.moego.svc.organization.entity.StaffAvailability">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update staff_availability
    set company_id = #{companyId,jdbcType=BIGINT},
      business_id = #{businessId,jdbcType=BIGINT},
      staff_id = #{staffId,jdbcType=BIGINT},
      slot_schedule_type = #{slotScheduleType,jdbcType=INTEGER},
      slot_start_sunday = #{slotStartSunday,jdbcType=DATE},
      time_schedule_type = #{timeScheduleType,jdbcType=INTEGER},
      time_start_sunday = #{timeStartSunday,jdbcType=DATE},
      created_at = #{createdAt,jdbcType=TIMESTAMP},
      updated_at = #{updatedAt,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>