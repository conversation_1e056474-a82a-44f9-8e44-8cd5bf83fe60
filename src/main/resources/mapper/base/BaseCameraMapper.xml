<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.moego.svc.organization.mapper.base.BaseCameraMapper">
  <resultMap id="BaseResultMap" type="com.moego.svc.organization.entity.Camera">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="company_id" jdbcType="BIGINT" property="companyId" />
    <result column="business_id" jdbcType="BIGINT" property="businessId" />
    <result column="config_id" jdbcType="BIGINT" property="configId" />
    <result column="type" jdbcType="TINYINT" property="type" />
    <result column="origin_camera_id" jdbcType="VARCHAR" property="originCameraId" />
    <result column="origin_camera_title" jdbcType="VARCHAR" property="originCameraTitle" />
    <result column="origin_status" jdbcType="INTEGER" property="originStatus" />
    <result column="video_url" jdbcType="VARCHAR" property="videoUrl" />
    <result column="is_active" jdbcType="BIT" property="isActive" />
    <result column="visibility_type" jdbcType="INTEGER" property="visibilityType" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
    <result column="auth" jdbcType="VARCHAR" property="auth" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, company_id, business_id, config_id, type, origin_camera_id, origin_camera_title, 
    origin_status, video_url, is_active, visibility_type, created_at, updated_at, auth
  </sql>
  <select id="selectByExample" parameterType="com.moego.svc.organization.entity.CameraExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from camera
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from camera
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from camera
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.moego.svc.organization.entity.CameraExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from camera
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.moego.svc.organization.entity.Camera">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into camera (company_id, business_id, config_id, 
      type, origin_camera_id, origin_camera_title, 
      origin_status, video_url, is_active, 
      visibility_type, created_at, updated_at, 
      auth)
    values (#{companyId,jdbcType=BIGINT}, #{businessId,jdbcType=BIGINT}, #{configId,jdbcType=BIGINT}, 
      #{type,jdbcType=TINYINT}, #{originCameraId,jdbcType=VARCHAR}, #{originCameraTitle,jdbcType=VARCHAR}, 
      #{originStatus,jdbcType=INTEGER}, #{videoUrl,jdbcType=VARCHAR}, #{isActive,jdbcType=BIT}, 
      #{visibilityType,jdbcType=INTEGER}, #{createdAt,jdbcType=TIMESTAMP}, #{updatedAt,jdbcType=TIMESTAMP}, 
      #{auth,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.moego.svc.organization.entity.Camera">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into camera
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="companyId != null">
        company_id,
      </if>
      <if test="businessId != null">
        business_id,
      </if>
      <if test="configId != null">
        config_id,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="originCameraId != null">
        origin_camera_id,
      </if>
      <if test="originCameraTitle != null">
        origin_camera_title,
      </if>
      <if test="originStatus != null">
        origin_status,
      </if>
      <if test="videoUrl != null">
        video_url,
      </if>
      <if test="isActive != null">
        is_active,
      </if>
      <if test="visibilityType != null">
        visibility_type,
      </if>
      <if test="createdAt != null">
        created_at,
      </if>
      <if test="updatedAt != null">
        updated_at,
      </if>
      <if test="auth != null">
        auth,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="companyId != null">
        #{companyId,jdbcType=BIGINT},
      </if>
      <if test="businessId != null">
        #{businessId,jdbcType=BIGINT},
      </if>
      <if test="configId != null">
        #{configId,jdbcType=BIGINT},
      </if>
      <if test="type != null">
        #{type,jdbcType=TINYINT},
      </if>
      <if test="originCameraId != null">
        #{originCameraId,jdbcType=VARCHAR},
      </if>
      <if test="originCameraTitle != null">
        #{originCameraTitle,jdbcType=VARCHAR},
      </if>
      <if test="originStatus != null">
        #{originStatus,jdbcType=INTEGER},
      </if>
      <if test="videoUrl != null">
        #{videoUrl,jdbcType=VARCHAR},
      </if>
      <if test="isActive != null">
        #{isActive,jdbcType=BIT},
      </if>
      <if test="visibilityType != null">
        #{visibilityType,jdbcType=INTEGER},
      </if>
      <if test="createdAt != null">
        #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedAt != null">
        #{updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="auth != null">
        #{auth,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.moego.svc.organization.entity.CameraExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from camera
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update camera
    <set>
      <if test="row.id != null">
        id = #{row.id,jdbcType=BIGINT},
      </if>
      <if test="row.companyId != null">
        company_id = #{row.companyId,jdbcType=BIGINT},
      </if>
      <if test="row.businessId != null">
        business_id = #{row.businessId,jdbcType=BIGINT},
      </if>
      <if test="row.configId != null">
        config_id = #{row.configId,jdbcType=BIGINT},
      </if>
      <if test="row.type != null">
        type = #{row.type,jdbcType=TINYINT},
      </if>
      <if test="row.originCameraId != null">
        origin_camera_id = #{row.originCameraId,jdbcType=VARCHAR},
      </if>
      <if test="row.originCameraTitle != null">
        origin_camera_title = #{row.originCameraTitle,jdbcType=VARCHAR},
      </if>
      <if test="row.originStatus != null">
        origin_status = #{row.originStatus,jdbcType=INTEGER},
      </if>
      <if test="row.videoUrl != null">
        video_url = #{row.videoUrl,jdbcType=VARCHAR},
      </if>
      <if test="row.isActive != null">
        is_active = #{row.isActive,jdbcType=BIT},
      </if>
      <if test="row.visibilityType != null">
        visibility_type = #{row.visibilityType,jdbcType=INTEGER},
      </if>
      <if test="row.createdAt != null">
        created_at = #{row.createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="row.updatedAt != null">
        updated_at = #{row.updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="row.auth != null">
        auth = #{row.auth,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update camera
    set id = #{row.id,jdbcType=BIGINT},
      company_id = #{row.companyId,jdbcType=BIGINT},
      business_id = #{row.businessId,jdbcType=BIGINT},
      config_id = #{row.configId,jdbcType=BIGINT},
      type = #{row.type,jdbcType=TINYINT},
      origin_camera_id = #{row.originCameraId,jdbcType=VARCHAR},
      origin_camera_title = #{row.originCameraTitle,jdbcType=VARCHAR},
      origin_status = #{row.originStatus,jdbcType=INTEGER},
      video_url = #{row.videoUrl,jdbcType=VARCHAR},
      is_active = #{row.isActive,jdbcType=BIT},
      visibility_type = #{row.visibilityType,jdbcType=INTEGER},
      created_at = #{row.createdAt,jdbcType=TIMESTAMP},
      updated_at = #{row.updatedAt,jdbcType=TIMESTAMP},
      auth = #{row.auth,jdbcType=VARCHAR}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.moego.svc.organization.entity.Camera">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update camera
    <set>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=BIGINT},
      </if>
      <if test="businessId != null">
        business_id = #{businessId,jdbcType=BIGINT},
      </if>
      <if test="configId != null">
        config_id = #{configId,jdbcType=BIGINT},
      </if>
      <if test="type != null">
        type = #{type,jdbcType=TINYINT},
      </if>
      <if test="originCameraId != null">
        origin_camera_id = #{originCameraId,jdbcType=VARCHAR},
      </if>
      <if test="originCameraTitle != null">
        origin_camera_title = #{originCameraTitle,jdbcType=VARCHAR},
      </if>
      <if test="originStatus != null">
        origin_status = #{originStatus,jdbcType=INTEGER},
      </if>
      <if test="videoUrl != null">
        video_url = #{videoUrl,jdbcType=VARCHAR},
      </if>
      <if test="isActive != null">
        is_active = #{isActive,jdbcType=BIT},
      </if>
      <if test="visibilityType != null">
        visibility_type = #{visibilityType,jdbcType=INTEGER},
      </if>
      <if test="createdAt != null">
        created_at = #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedAt != null">
        updated_at = #{updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="auth != null">
        auth = #{auth,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.moego.svc.organization.entity.Camera">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update camera
    set company_id = #{companyId,jdbcType=BIGINT},
      business_id = #{businessId,jdbcType=BIGINT},
      config_id = #{configId,jdbcType=BIGINT},
      type = #{type,jdbcType=TINYINT},
      origin_camera_id = #{originCameraId,jdbcType=VARCHAR},
      origin_camera_title = #{originCameraTitle,jdbcType=VARCHAR},
      origin_status = #{originStatus,jdbcType=INTEGER},
      video_url = #{videoUrl,jdbcType=VARCHAR},
      is_active = #{isActive,jdbcType=BIT},
      visibility_type = #{visibilityType,jdbcType=INTEGER},
      created_at = #{createdAt,jdbcType=TIMESTAMP},
      updated_at = #{updatedAt,jdbcType=TIMESTAMP},
      auth = #{auth,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>