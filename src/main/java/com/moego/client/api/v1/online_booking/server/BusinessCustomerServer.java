package com.moego.client.api.v1.online_booking.server;

import com.moego.client.api.v1.online_booking.service.ContextService;
import com.moego.client.api.v1.online_booking.service.OBLoginService;
import com.moego.client.api.v1.online_booking.utils.OBSessionUtil;
import com.moego.common.enums.BusinessConst;
import com.moego.common.enums.ClientSourceEnum;
import com.moego.idl.client.online_booking.v1.BusinessCustomerServiceGrpc;
import com.moego.idl.client.online_booking.v1.ChangeLoginPhoneNumberParams;
import com.moego.idl.client.online_booking.v1.ChangeLoginPhoneNumberResult;
import com.moego.idl.client.online_booking.v1.ConfirmPhoneNumberParams;
import com.moego.idl.client.online_booking.v1.ConfirmPhoneNumberResult;
import com.moego.idl.client.online_booking.v1.CreateCustomerParams;
import com.moego.idl.client.online_booking.v1.CreateCustomerResult;
import com.moego.idl.client.online_booking.v1.UpdateCustomerParams;
import com.moego.idl.client.online_booking.v1.UpdateCustomerResult;
import com.moego.idl.client.online_booking.v1.VerifyPhoneNumberParams;
import com.moego.idl.client.online_booking.v1.VerifyPhoneNumberResult;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.organization.v1.LocationModel;
import com.moego.idl.models.organization.v1.Tenant;
import com.moego.idl.service.business_customer.v1.BusinessCustomerContactServiceGrpc;
import com.moego.idl.service.business_customer.v1.BusinessCustomerServiceGrpc.BusinessCustomerServiceBlockingStub;
import com.moego.idl.service.business_customer.v1.GetCustomerInfoRequest;
import com.moego.idl.service.business_customer.v1.GetCustomerPrimaryContactRequest;
import com.moego.idl.service.business_customer.v1.UpdateCustomerRequest;
import com.moego.idl.service.organization.v1.BusinessServiceGrpc;
import com.moego.idl.service.organization.v1.GetLocationDetailRequest;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.core.TypeRef;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.lib.common.grpc.server.GrpcService;
import com.moego.lib.common.util.JsonUtil;
import com.moego.lib.common.util.RequestUtils;
import com.moego.server.customer.api.ICustomerComposeService;
import com.moego.server.customer.api.ICustomerCustomerService;
import com.moego.server.customer.client.ICustomerOnlineBookingClient;
import com.moego.server.customer.dto.SaveCustomerPetResultDto;
import com.moego.server.customer.params.CreateOBLoginTokenParams;
import com.moego.server.customer.params.SaveWithPetCustomerVo;
import com.moego.server.customer.params.UpdateCustomerInfoParams;
import com.moego.server.grooming.api.IBookOnlineQuestionService;
import com.moego.server.grooming.api.IGroomingOnlineBookingService;
import com.moego.server.grooming.dto.ob.BookOnlineQuestionSaveDTO;
import com.moego.server.grooming.dto.ob.OBBusinessDTO;
import com.moego.server.grooming.params.ob.OBAnonymousParams;
import com.moego.server.message.dto.VerifyCodeDTO;
import com.moego.server.message.enums.VerificationCodeScenarioEnum;
import io.grpc.stub.StreamObserver;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

@Slf4j
@GrpcService
@RequiredArgsConstructor
public class BusinessCustomerServer extends BusinessCustomerServiceGrpc.BusinessCustomerServiceImplBase {

    private final ContextService contextService;
    private final BusinessCustomerServiceBlockingStub businessCustomerServiceBlockingStub;
    private final BusinessCustomerContactServiceGrpc.BusinessCustomerContactServiceBlockingStub
            businessCustomerContactServiceBlockingStub;
    private final OBLoginService obLoginService;
    private final ICustomerCustomerService iCustomerCustomerService;
    private final BusinessServiceGrpc.BusinessServiceBlockingStub businessServiceBlockingStub;

    private final IGroomingOnlineBookingService onlineBookingApi;
    private final ICustomerOnlineBookingClient customerOnlineBookingApi;
    private final ICustomerComposeService iCustomerComposeApi;
    private final IBookOnlineQuestionService bookOnlineQuestionApi;

    @Override
    @Auth(AuthType.OB)
    public void createCustomer(CreateCustomerParams request, StreamObserver<CreateCustomerResult> responseObserver) {
        // 检查 ob new client 会话上下文里的 phone number 是否与请求的 phone number 一致
        var phoneNumber =
                AuthContext.get().getSubSessionDataStringValue(OBSessionUtil.OB_NEW_CLIENT_SESSION_PHONE_NUMBER_KEY);
        if (!StringUtils.hasText(phoneNumber)) {
            throw ExceptionUtil.bizException(
                    Code.CODE_PARAMS_ERROR, "Invalid session. Please refresh the page and try again.");
        }
        if (!Objects.equals(phoneNumber, request.getCustomer().getPhoneNumber())) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "Phone number mismatch.");
        }

        var obAnonymous = new OBAnonymousParams().setDomain(request.getDomain()).setName(request.getName());
        var obBusiness = onlineBookingApi.mustGetBusinessDTOByOBNameOrDomain(obAnonymous);

        // 创建 customer
        var customerId = createCustomer(obBusiness, request.getCustomer());

        // 更新 session
        CreateOBLoginTokenParams params = CreateOBLoginTokenParams.builder()
                .customerId(customerId)
                .ip(RequestUtils.getIP())
                .userAgent(RequestUtils.getUserAgent())
                .refererLink(RequestUtils.getReferer())
                .mainSession(OBSessionUtil.mustGetOBSession(obAnonymous))
                .build();
        customerOnlineBookingApi.genLoginToken(params);

        responseObserver.onNext(CreateCustomerResult.getDefaultInstance());
        responseObserver.onCompleted();
    }

    private int createCustomer(OBBusinessDTO biz, CreateCustomerParams.Customer customer) {
        SaveWithPetCustomerVo param = new SaveWithPetCustomerVo();
        param.setPreferredBusinessId(Long.valueOf(biz.getBusinessId()));
        param.setFirstName(customer.getFirstName());
        param.setLastName(customer.getLastName());
        param.setPhoneNumber(customer.getPhoneNumber());
        param.setAvatarPath("");
        param.setEmail(customer.getEmail());

        if (customer.hasBirthday()) {
            var birthday = customer.getBirthday();
            param.setBirthday(LocalDateTime.ofInstant(
                    Instant.ofEpochSecond(birthday.getSeconds(), birthday.getNanos()), ZoneId.systemDefault()));
        }

        if (customer.hasAddress()
                && customer.getAddress().hasLat()
                && customer.getAddress().hasLng()) {
            // create customer address
            var address = customer.getAddress();
            param.setAddress1(address.getAddress1());
            param.setCity(address.getCity());
            param.setState(address.getState());
            param.setZipcode(address.getZipcode());
            param.setCountry(address.getCountry());
            param.setAddress2(address.getAddress2());
            param.setLat(address.getLat());
            param.setLng(address.getLng());
        }

        if (customer.hasAdditionalInfo()) {
            var additionalInfo = customer.getAdditionalInfo();
            if (additionalInfo.hasReferralSourceId()) {
                param.setReferralSourceId(additionalInfo.getReferralSourceId());
            }
            if (additionalInfo.hasReferralSourceDesc()) {
                param.setReferralSourceDesc(additionalInfo.getReferralSourceDesc());
            }
            if (additionalInfo.hasPreferredGroomerId()) {
                param.setPreferredGroomerId(additionalInfo.getPreferredGroomerId());
            }
            if (additionalInfo.hasPreferredFrequencyDay()) {
                param.setPreferredFrequencyDay(additionalInfo.getPreferredFrequencyDay());
            }
            if (additionalInfo.hasPreferredFrequencyType()) {
                param.setPreferredFrequencyType((byte) additionalInfo.getPreferredFrequencyType());
            }
            if (additionalInfo.getPreferredDayCount() > 0) {
                param.setPreferredDay(additionalInfo.getPreferredDayList().toArray(Integer[]::new));
            }
            if (additionalInfo.getPreferredTimeCount() > 0) {
                param.setPreferredTime(additionalInfo.getPreferredTimeList().toArray(Integer[]::new));
            }
        }

        param.setPetList(List.of());
        param.setSource(ClientSourceEnum.SOURCE_ONLINE_BOOKING.getSource());

        SaveCustomerPetResultDto res =
                iCustomerComposeApi.createCustomerAndPets(biz.getCompanyId(), biz.getBusinessId(), 0, param);

        if (!Boolean.TRUE.equals(res.getResult())) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "Failed to create customer.");
        }

        var customerId = res.getId();

        if (CollectionUtils.isEmpty(customer.getAnswersMapMap())) {
            return customerId;
        }

        // save custom questions
        var answersMap = customer.getAnswersMapMap();
        var customQuestion = new BookOnlineQuestionSaveDTO();
        customQuestion.setBusinessId(biz.getBusinessId());
        customQuestion.setCompanyId(biz.getCompanyId());
        customQuestion.setCustomerId(customerId);

        Map<String, Object> clientQuestionMap = JsonUtil.toBean(JsonUtil.toJson(answersMap), new TypeRef<>() {});
        customQuestion.setClientCustomQuestionMap(clientQuestionMap);

        bookOnlineQuestionApi.saveCustomerQuestionSave(customQuestion);

        return customerId;
    }

    @Override
    @Auth(AuthType.OB_EXISTING_CLIENT)
    public void updateCustomer(UpdateCustomerParams request, StreamObserver<UpdateCustomerResult> responseObserver) {
        var ctx = contextService.buildOnlineBookingContext(request.getDomain(), request.getName());
        businessCustomerServiceBlockingStub.updateCustomer(UpdateCustomerRequest.newBuilder()
                .setTenant(Tenant.newBuilder().setCompanyId(ctx.getCompanyId()).build())
                .setId(request.getId())
                .setCustomer(request.getCustomer())
                .build());
        responseObserver.onNext(UpdateCustomerResult.newBuilder().build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.OB_EXISTING_CLIENT)
    public void verifyPhoneNumber(
            VerifyPhoneNumberParams request, StreamObserver<VerifyPhoneNumberResult> responseObserver) {
        var ctx = contextService.buildOnlineBookingContext(request.getDomain(), request.getName());
        if (request.hasId() && request.getId() != ctx.getCustomerId()) {
            throw ExceptionUtil.bizException(
                    Code.CODE_PARAMS_ERROR, "Some error occurred. Please refresh the page and try again.");
        }
        if (request.hasId()) {
            var primaryContact = businessCustomerContactServiceBlockingStub
                    .getCustomerPrimaryContact(GetCustomerPrimaryContactRequest.newBuilder()
                            .setCustomerId(request.getId())
                            .build())
                    .getContact();
            if (!Objects.equals(primaryContact.getPhoneNumber(), request.getPhoneNumber())) {
                throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "Please enter the correct phone number.");
            }
        }
        var location = businessServiceBlockingStub
                .getLocationDetail(GetLocationDetailRequest.newBuilder()
                        .setId(ctx.getBusinessId())
                        .build())
                .getLocation();
        if (!isUS(location)) {
            throw ExceptionUtil.bizException(
                    Code.CODE_PARAMS_ERROR,
                    "Not supported now. Please contact your business support for more information.");
        }
        var customer = businessCustomerServiceBlockingStub
                .getCustomerInfo(GetCustomerInfoRequest.newBuilder()
                        .setId(ctx.getCustomerId())
                        .build())
                .getCustomer();

        var token = obLoginService.sendPhoneVerificationCode(location, customer, request.getPhoneNumber());
        responseObserver.onNext(
                VerifyPhoneNumberResult.newBuilder().setToken(token).build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.OB_EXISTING_CLIENT)
    public void confirmPhoneNumber(
            ConfirmPhoneNumberParams request, StreamObserver<ConfirmPhoneNumberResult> responseObserver) {
        var ctx = contextService.buildOnlineBookingContext(request.getDomain(), request.getName());
        VerifyCodeDTO verifyCodeDTO = new VerifyCodeDTO()
                .setScenario(VerificationCodeScenarioEnum.OB_CHANGE_PHONE_NUMBER)
                .setBusinessId(ctx.getBusinessId())
                .setCustomerId(ctx.getCustomerId())
                .setAccount(request.getPhoneNumber())
                .setCode(request.getCode())
                .setToken(request.getToken());
        obLoginService.verifyPhoneCode(verifyCodeDTO);
        responseObserver.onNext(
                ConfirmPhoneNumberResult.newBuilder().setSuccess(true).build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.OB_EXISTING_CLIENT)
    public void changeLoginPhoneNumber(
            ChangeLoginPhoneNumberParams request, StreamObserver<ChangeLoginPhoneNumberResult> responseObserver) {
        var ctx = contextService.buildOnlineBookingContext(request.getDomain(), request.getName());
        VerifyCodeDTO verifyCodeDTO = new VerifyCodeDTO()
                .setScenario(VerificationCodeScenarioEnum.OB_CHANGE_PHONE_NUMBER)
                .setBusinessId(ctx.getBusinessId())
                .setCustomerId(ctx.getCustomerId())
                .setAccount(request.getPhoneNumber())
                .setCode(request.getCode())
                .setToken(request.getToken());
        obLoginService.verifyPhoneCode(verifyCodeDTO);

        var param = new UpdateCustomerInfoParams();
        param.setCustomerId(ctx.getCustomerId());
        param.setPhoneNumber(request.getPhoneNumber());
        var res = iCustomerCustomerService.updateCustomer(ctx.getCompanyId(), ctx.getBusinessId(), 0, param);
        responseObserver.onNext(ChangeLoginPhoneNumberResult.newBuilder()
                .setSuccess(res != null && res.getSuccess())
                .build());
        responseObserver.onCompleted();
    }

    private boolean isUS(LocationModel business) {
        return business.getAddress().getCountry().equals(BusinessConst.COUNTRY_US)
                || business.getAddress().getCountry().equals(BusinessConst.COUNTRY_US2);
    }
}
