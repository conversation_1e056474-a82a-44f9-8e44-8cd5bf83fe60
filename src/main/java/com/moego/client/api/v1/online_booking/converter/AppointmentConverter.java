package com.moego.client.api.v1.online_booking.converter;

import com.moego.common.enums.ServiceItemEnum;
import com.moego.idl.client.online_booking.v1.AppointmentSummaryItem;
import com.moego.idl.client.online_booking.v1.GetAppointmentDetailResult;
import com.moego.idl.client.online_booking.v1.ReschedulePetFeedingMedicationParams;
import com.moego.idl.client.online_booking.v1.UpdateAppointmentParams;
import com.moego.idl.models.appointment.v1.AppointmentModel;
import com.moego.idl.models.appointment.v1.AppointmentUpdatedBy;
import com.moego.idl.models.appointment.v1.PetDetailModel;
import com.moego.idl.models.appointment.v1.PetScheduleDef;
import com.moego.idl.models.appointment.v1.UpdatePetDetailDef;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.offering.v1.ServiceItemType;
import com.moego.idl.service.appointment.v1.ReschedulePetFeedingMedicationRequest;
import com.moego.idl.service.appointment.v1.UpdateAppointmentSelectiveRequest;
import com.moego.lib.common.exception.ExceptionUtil;
import java.util.List;
import java.util.Map;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;
import org.springframework.util.CollectionUtils;

@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        unmappedSourcePolicy = ReportingPolicy.IGNORE)
public interface AppointmentConverter {

    AppointmentConverter INSTANCE = Mappers.getMapper(AppointmentConverter.class);

    @Mapping(target = "bookingRequestId", ignore = true)
    @Mapping(target = "appointmentId", source = "id")
    @Mapping(target = "isBookingRequest", expression = "java(false)")
    @Mapping(target = "startDate", source = "appointmentDate")
    @Mapping(target = "startTime", source = "appointmentStartTime")
    @Mapping(target = "endDate", source = "appointmentEndDate")
    @Mapping(target = "endTime", source = "appointmentEndTime")
    @Mapping(target = "mainCareType", source = "serviceTypeInclude", qualifiedByName = "toMainCareType")
    AppointmentSummaryItem toAppointmentSummary(AppointmentModel model);

    @Mapping(target = "bookingRequestId", ignore = true)
    @Mapping(target = "appointmentId", source = "id")
    @Mapping(target = "isBookingRequest", expression = "java(false)")
    @Mapping(target = "startDate", source = "appointmentDate")
    @Mapping(target = "startTime", source = "appointmentStartTime")
    @Mapping(target = "endDate", source = "appointmentEndDate")
    @Mapping(target = "endTime", source = "appointmentEndTime")
    @Mapping(target = "appointmentStatus", source = "status")
    @Mapping(target = "mainCareType", source = "serviceTypeInclude", qualifiedByName = "toMainCareType")
    GetAppointmentDetailResult.AppointmentItem toAppointmentDetail(AppointmentModel model);

    @Named("toMainCareType")
    default ServiceItemType getMainCareType(Integer serviceTypeInclude) {
        var mainServiceItemType = ServiceItemEnum.getMainServiceItemType(serviceTypeInclude);
        return switch (mainServiceItemType) {
            case GROOMING -> ServiceItemType.GROOMING;
            case BOARDING -> ServiceItemType.BOARDING;
            case DAYCARE -> ServiceItemType.DAYCARE;
            case EVALUATION -> ServiceItemType.EVALUATION;
            case DOG_WALKING -> ServiceItemType.DOG_WALKING;
            case GROUP_CLASS -> ServiceItemType.GROUP_CLASS;
            default -> throw ExceptionUtil.bizException(
                    Code.CODE_PARAMS_ERROR, "Unsupported service item type: " + mainServiceItemType);
        };
    }

    default ServiceItemType getMainCareType(List<Integer> serviceItemTypes) {
        return getMainCareType(ServiceItemEnum.convertBitValueList(serviceItemTypes));
    }

    default ReschedulePetFeedingMedicationRequest toRescheduleRequest(
            long companyId,
            long businessId,
            long appointmentId,
            List<ReschedulePetFeedingMedicationParams.PetScheduleDef> schedules,
            Map<Long, PetDetailModel> existPetDetailMap) {
        return ReschedulePetFeedingMedicationRequest.newBuilder()
                .setAppointmentId(appointmentId)
                .addAllSchedules(toPetScheduleDef(schedules, existPetDetailMap))
                .setCompanyId(companyId)
                .setBusinessId(businessId)
                .build();
    }

    default List<PetScheduleDef> toPetScheduleDef(
            List<ReschedulePetFeedingMedicationParams.PetScheduleDef> schedules,
            Map<Long, PetDetailModel> existPetDetailMap) {
        if (CollectionUtils.isEmpty(schedules)) {
            return List.of();
        }
        return schedules.stream()
                .map(k -> toPetScheduleDef(k, existPetDetailMap))
                .toList();
    }

    default PetScheduleDef toPetScheduleDef(
            ReschedulePetFeedingMedicationParams.PetScheduleDef schedule, Map<Long, PetDetailModel> existPetDetailMap) {
        return PetScheduleDef.newBuilder()
                .setPetId(existPetDetailMap.get(schedule.getPetDetailId()).getPetId())
                .addAllFeedings(schedule.getFeedingsList())
                .addAllMedications(schedule.getMedicationsList())
                .build();
    }

    default UpdateAppointmentSelectiveRequest toUpdateAppointmentSelectiveRequest(UpdateAppointmentParams request) {
        var builder = UpdateAppointmentSelectiveRequest.newBuilder()
                .setId(request.getAppointmentId())
                .setUpdateByType(AppointmentUpdatedBy.BY_CLIENT_PORTAL);
        for (var petAndService : request.getPetAndServicesList()) {
            for (var petService : petAndService.getServicesList()) {
                builder.addPetDetails(toUpdatePetDetailDef(petService));
            }
            for (var petAddOn : petAndService.getAddOnsList()) {
                builder.addPetDetails(toUpdatePetDetailDef(petAddOn));
            }
        }
        return builder.build();
    }

    default UpdatePetDetailDef toUpdatePetDetailDef(UpdateAppointmentParams.UpdateServiceDetailParams request) {
        var builder = UpdatePetDetailDef.newBuilder()
                .setId(request.getPetDetailId())
                .addAllSpecificDates(request.getSpecificDatesList());
        if (request.hasDateType()) {
            builder.setDateType(request.getDateType());
        }
        if (request.hasStartDate()) {
            builder.setStartDate(request.getStartDate());
        }
        if (request.hasStartTime()) {
            builder.setStartTime(request.getStartTime());
        }
        if (request.hasEndDate()) {
            builder.setEndDate(request.getEndDate());
        }
        if (request.hasEndTime()) {
            builder.setEndTime(request.getEndTime());
        }
        if (request.hasQuantityPerDay()) {
            builder.setQuantityPerDay(request.getQuantityPerDay());
        }

        return builder.build();
    }

    default UpdatePetDetailDef toUpdatePetDetailDef(UpdateAppointmentParams.UpdateAddOnDetailParams request) {
        var builder = UpdatePetDetailDef.newBuilder().setId(request.getPetDetailId());
        if (request.hasDateType()) {
            builder.setDateType(request.getDateType());
            switch (request.getDateType()) {
                case PET_DETAIL_DATE_DATE_POINT -> {
                    builder.setStartDate(request.getSpecificDates(0));
                    builder.setEndDate(request.getSpecificDates(0));
                }
                case PET_DETAIL_DATE_SPECIFIC_DATE -> builder.addAllSpecificDates(request.getSpecificDatesList());
                default -> {}
            }
        }
        if (request.hasStartTime()) {
            builder.setStartTime(request.getStartTime());
        }
        if (request.hasEndTime()) {
            builder.setEndTime(request.getEndTime());
        }
        if (request.hasQuantityPerDay()) {
            builder.setQuantityPerDay(request.getQuantityPerDay());
        }
        return builder.build();
    }
}
