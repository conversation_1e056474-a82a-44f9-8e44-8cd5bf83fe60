package com.moego.client.api.v1.shared.helper;

import com.moego.idl.models.business_customer.v1.PetEvaluationModel;
import com.moego.idl.models.customer.v1.EvaluationStatus;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.offering.v1.EvaluationBriefView;
import com.moego.idl.models.offering.v1.EvaluationModel;
import com.moego.idl.models.offering.v1.ServiceBriefView;
import com.moego.idl.models.organization.v1.Tenant;
import com.moego.idl.service.business_customer.v1.BusinessPetEvaluationServiceGrpc;
import com.moego.idl.service.business_customer.v1.ListPetEvaluationRequest;
import com.moego.idl.service.offering.v1.EvaluationServiceGrpc;
import com.moego.idl.service.offering.v1.GetEvaluationListWithEvaluationIdsRequest;
import com.moego.idl.service.offering.v1.GetEvaluationRequest;
import com.moego.idl.service.offering.v1.GetServiceListByIdsRequest;
import com.moego.idl.service.offering.v1.ServiceManagementServiceGrpc;
import com.moego.lib.common.exception.ExceptionUtil;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

/**
 * <AUTHOR>
 * @since 2025/5/12
 */
@Component
@RequiredArgsConstructor
public class EvaluationHelper {

    private final EvaluationServiceGrpc.EvaluationServiceBlockingStub evaluationStub;
    private final BusinessPetEvaluationServiceGrpc.BusinessPetEvaluationServiceBlockingStub petEvaluationStub;
    private final ServiceManagementServiceGrpc.ServiceManagementServiceBlockingStub serviceStub;

    /**
     * Get evaluation by id, throw exception if not found
     *
     * @param evaluationId evaluation id
     * @return evaluation model
     */
    public EvaluationModel mustGetEvaluation(long evaluationId) {
        var resp = evaluationStub.getEvaluation(
                GetEvaluationRequest.newBuilder().setId(evaluationId).build());
        if (!resp.hasEvaluationModel()) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "Evaluation not found: " + evaluationId);
        }
        return resp.getEvaluationModel();
    }

    /**
     * Get evaluations by ids.
     *
     * @param evaluationIds evaluation ids
     * @return evaluation id -> evaluation
     */
    public Map<Long, EvaluationBriefView> listEvaluation(Collection<Long> evaluationIds) {
        var resp =
                evaluationStub.getEvaluationListWithEvaluationIds(GetEvaluationListWithEvaluationIdsRequest.newBuilder()
                        .addAllEvaluationIds(Set.copyOf(evaluationIds))
                        .build());
        return resp.getEvaluationsList().stream()
                .collect(Collectors.toMap(EvaluationBriefView::getId, Function.identity(), (a, b) -> a));
    }

    /**
     * Get pets missing evaluations for online booking.
     *
     * @param companyId  company id
     * @param petIds     pet ids
     * @param serviceIds service ids
     * @return petId -> serviceId -> evaluation
     */
    public Map<Long, Map<Long, EvaluationBriefView>> listMissingEvaluations(
            long companyId, Collection<Long> petIds, Collection<? extends Number> serviceIds) {
        if (ObjectUtils.isEmpty(petIds) || ObjectUtils.isEmpty(serviceIds)) {
            return Map.of();
        }

        // 1. 获取所有 service 信息，找出需要 evaluation 的 service 和对应的 evaluation id
        var serviceIdToEvaluationId = getRequiredEvaluationByServiceIds(serviceIds);
        if (serviceIdToEvaluationId.isEmpty()) {
            return Map.of();
        }

        // 2. 获取所有 evaluation 信息
        var evaluationIdToEvaluation = getEvaluationsByIds(serviceIdToEvaluationId.values());
        if (evaluationIdToEvaluation.isEmpty()) {
            return Map.of();
        }

        // 3. 获取 pet 已有的 evaluation 记录
        var petIdToPassedEvaluations = getPetEvaluations(companyId, petIds);

        // 4. 计算每个 pet 对应每个 service 缺失的 evaluations
        var result = new HashMap<Long, Map<Long, EvaluationBriefView>>();
        for (var petId : petIds) {
            // 获取 pet 已有的 evaluation ids
            var passedEvaluationIds = petIdToPassedEvaluations.getOrDefault(petId, List.of()).stream()
                    .map(PetEvaluationModel::getEvaluationId)
                    .collect(Collectors.toSet());

            // 计算每个 service 缺失的 evaluation
            var serviceIdToMissingEvaluation = new HashMap<Long, EvaluationBriefView>();
            for (var entry : serviceIdToEvaluationId.entrySet()) {
                var serviceId = entry.getKey();
                var evaluationId = entry.getValue();

                // 如果 pet 没有这个 evaluation，则添加到缺失列表
                if (!passedEvaluationIds.contains(evaluationId) && evaluationIdToEvaluation.containsKey(evaluationId)) {
                    serviceIdToMissingEvaluation.put(serviceId, evaluationIdToEvaluation.get(evaluationId));
                }
            }

            if (!serviceIdToMissingEvaluation.isEmpty()) {
                result.put(petId, serviceIdToMissingEvaluation);
            }
        }

        return result;
    }

    /**
     * List all evaluations that are required for online booking.
     *
     * @param serviceIds service ids
     * @return serviceId -> evaluation
     */
    public Map<Long, EvaluationBriefView> listRequiredEvaluation(Collection<? extends Number> serviceIds) {
        var serviceIdToEvaluationId = getRequiredEvaluationByServiceIds(
                serviceIds.stream().map(Number::longValue).toList());
        if (serviceIdToEvaluationId.isEmpty()) {
            return Map.of();
        }

        var evaluationIdToEvaluation = getEvaluationsByIds(serviceIdToEvaluationId.values());

        return serviceIdToEvaluationId.entrySet().stream()
                .filter(e -> evaluationIdToEvaluation.containsKey(e.getValue()))
                .collect(Collectors.toMap(Map.Entry::getKey, e -> evaluationIdToEvaluation.get(e.getValue())));
    }

    private Map<Long, Long> getRequiredEvaluationByServiceIds(Collection<? extends Number> serviceIds) {
        var ids = serviceIds.stream().map(Number::longValue).distinct().toList();

        if (CollectionUtils.isEmpty(ids)) {
            return Map.of();
        }

        // 获取 service 信息
        var services = serviceStub
                .getServiceListByIds(GetServiceListByIdsRequest.newBuilder()
                        .addAllServiceIds(ids)
                        .build())
                .getServicesList();

        // 过滤出需要 evaluation 的 service
        return services.stream()
                .filter(e -> e.getIsEvaluationRequired() && e.getIsEvaluationRequiredForOb())
                .filter(service -> service.getEvaluationId() > 0)
                .collect(Collectors.toMap(ServiceBriefView::getId, ServiceBriefView::getEvaluationId, (a, b) -> a));
    }

    private Map<Long, EvaluationBriefView> getEvaluationsByIds(Collection<Long> evaluationIds) {
        if (CollectionUtils.isEmpty(evaluationIds)) {
            return Map.of();
        }

        return evaluationStub
                .getEvaluationListWithEvaluationIds(GetEvaluationListWithEvaluationIdsRequest.newBuilder()
                        .addAllEvaluationIds(Set.copyOf(evaluationIds))
                        .build())
                .getEvaluationsList()
                .stream()
                .collect(Collectors.toMap(EvaluationBriefView::getId, Function.identity(), (a, b) -> a));
    }

    private Map<Long, List<PetEvaluationModel>> getPetEvaluations(long companyId, Collection<Long> petIds) {
        if (CollectionUtils.isEmpty(petIds)) {
            return Map.of();
        }

        var response = petEvaluationStub.listPetEvaluation(ListPetEvaluationRequest.newBuilder()
                .setTenant(Tenant.newBuilder().setCompanyId(companyId).build())
                .addAllPetIds(Set.copyOf(petIds))
                .build());

        return response.getPetEvaluationsList().stream()
                .filter(e -> e.getEvaluationStatus() == EvaluationStatus.PASS)
                .collect(Collectors.groupingBy(PetEvaluationModel::getPetId));
    }
}
