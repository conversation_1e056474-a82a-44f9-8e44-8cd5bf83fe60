package com.moego.client.api.v1.grooming.mapper;

import com.moego.idl.models.online_booking.v1.AcceptClientType;
import com.moego.idl.models.online_booking.v1.AvailabilityType;
import com.moego.idl.models.online_booking.v1.BookingRangeEndType;
import com.moego.idl.models.online_booking.v1.BusinessOBConfigModelBookingView;
import com.moego.idl.models.online_booking.v1.BusinessOBConfigModelClientListView;
import com.moego.idl.models.online_booking.v1.BusinessOBConfigModelClientView;
import com.moego.idl.models.online_booking.v1.PaymentType;
import com.moego.idl.models.online_booking.v1.PrepayDepositType;
import com.moego.idl.models.online_booking.v1.PrepayType;
import com.moego.idl.models.online_booking.v1.SelectedPetServiceDef;
import com.moego.idl.models.online_booking.v1.TimeSlotFormat;
import com.moego.idl.models.payment.v1.BookOnlineDepositModelClientView;
import com.moego.server.grooming.dto.BookOnlineDTO;
import com.moego.server.grooming.dto.BookOnlineDepositDTO;
import com.moego.server.grooming.dto.ob.BookOnlinePaymentBaseSettingDTO;
import com.moego.server.grooming.params.BookOnlinePetParams;
import java.util.List;
import java.util.Objects;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants;
import org.mapstruct.MappingTarget;
import org.mapstruct.Mappings;
import org.mapstruct.Named;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 * @since 2023/9/25
 */
@Mapper(
        componentModel = MappingConstants.ComponentModel.SPRING,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        unmappedSourcePolicy = ReportingPolicy.WARN,
        unmappedTargetPolicy = ReportingPolicy.WARN,
        uses = {BaseMapper.class},
        imports = {
            AvailabilityType.class,
            TimeSlotFormat.class,
            BookingRangeEndType.class,
            PaymentType.class,
            PrepayType.class,
            PrepayDepositType.class,
            AcceptClientType.class,
            Objects.class,
        })
public interface BookOnlineMapper {
    List<BusinessOBConfigModelClientListView> dtoToView(List<BookOnlineDTO> dtoList);

    @Mappings({
        @Mapping(
                target = "availableTimeType",
                expression = "java(AvailabilityType.forNumber(dto.getAvailableTimeType()))"),
        @Mapping(target = "timeslotFormat", expression = "java(TimeSlotFormat.forNumber(dto.getTimeslotFormat()))"),
        @Mapping(
                target = "bySlotTimeslotFormat",
                expression = "java(TimeSlotFormat.forNumber(dto.getBySlotTimeslotFormat()))"),
    })
    BusinessOBConfigModelClientListView dtoToView(BookOnlineDTO dto);

    @Mappings({
        @Mapping(target = "isEnable", source = "isEnable", qualifiedByName = "switchToBool"),
        @Mapping(
                target = "availableTimeType",
                expression = "java(AvailabilityType.forNumber(dto.getAvailableTimeType()))"),
        @Mapping(target = "timeslotFormat", expression = "java(TimeSlotFormat.forNumber(dto.getTimeslotFormat()))"),
        @Mapping(
                target = "bySlotTimeslotFormat",
                expression = "java(TimeSlotFormat.forNumber(dto.getBySlotTimeslotFormat()))"),
        @Mapping(target = "acceptClientType", expression = "java(AcceptClientType.forNumber(dto.getAcceptClient()))"),
    })
    BusinessOBConfigModelClientView dtoToClientView(BookOnlineDTO dto);

    @Mapping(target = "paymentType", source = "guid", qualifiedByName = "guidToPaymentType")
    @Mapping(target = "prepayType", source = "guid", qualifiedByName = "guidToPrepayType")
    @Mapping(
            target = "prepayDepositAmount",
            expression =
                    "java(Objects.equals(guidToPaymentType(dto.getGuid()), PaymentType.PAYMENT_TYPE_PREPAY) ? dto.getAmount().subtract(dto.getConvenienceFee()).doubleValue() : 0.0)")
    @Mapping(
            target = "prepayPaidAmount",
            expression =
                    "java(Objects.equals(guidToPaymentType(dto.getGuid()), PaymentType.PAYMENT_TYPE_PREPAY) ? dto.getAmount().add(dto.getBookingFee()).doubleValue() : 0.0)")
    BookOnlineDepositModelClientView dtoToView(BookOnlineDepositDTO dto);

    @Named("guidToPaymentType")
    default PaymentType guidToPaymentType(String guid) {
        if (!StringUtils.hasText(guid)) {
            return PaymentType.PAYMENT_TYPE_DISABLE;
        }
        return guid.startsWith("preauth_") ? PaymentType.PAYMENT_TYPE_PRE_AUTH : PaymentType.PAYMENT_TYPE_PREPAY;
    }

    @Named("guidToPrepayType")
    default PrepayType guidToPrepayType(String guid) {
        return guid.startsWith("de_") ? PrepayType.PREPAY_TYPE_DEPOSIT : PrepayType.PREPAY_TYPE_FULL_AMOUNT;
    }

    @Mappings({
        @Mapping(
                target = "availableTimeType",
                expression = "java(AvailabilityType.forNumber(dto.getAvailableTimeType()))"),
        @Mapping(target = "timeslotFormat", expression = "java(TimeSlotFormat.forNumber(dto.getTimeslotFormat()))"),
        @Mapping(
                target = "bySlotTimeslotFormat",
                expression = "java(TimeSlotFormat.forNumber(dto.getBySlotTimeslotFormat()))"),
        @Mapping(
                target = "bookingRangeEndType",
                expression = "java(BookingRangeEndType.forNumber(dto.getBookingRangeEndType()))"),
        @Mapping(target = "isEnable", source = "isEnable", qualifiedByName = "switchToBool"),
        @Mapping(target = "isDisplayStaffSelectionPage", source = "displayStaffSelectionPage"),
        @Mapping(target = "paymentType", expression = "java(PaymentType.forNumber(dto.getEnableNoShowFee()))"),
        @Mapping(target = "cofPolicy", source = "cancellationPolicy"),
        @Mapping(target = "prepayType", expression = "java(PrepayType.forNumber(dto.getPrepayType()))"),
        @Mapping(target = "isPrepayTipEnable", qualifiedByName = "switchToBool", source = "prepayTipEnable"),
        @Mapping(target = "prepayDepositType", expression = "java(PrepayDepositType.forNumber(dto.getDepositType()))"),
        @Mapping(target = "isPreAuthTipEnable", qualifiedByName = "switchToBool", source = "preAuthTipEnable"),
        @Mapping(
                target = "isAllowedSimplifySubmit",
                qualifiedByName = "switchToBool",
                source = "allowedSimplifySubmit"),
        @Mapping(target = "isNeedAddress", qualifiedByName = "switchToBool", source = "isNeedAddress"),
        @Mapping(target = "isCheckExistingClient", qualifiedByName = "switchToBool", source = "isCheckExistingClient"),
    })
    BusinessOBConfigModelBookingView dtoToBookingView(BookOnlineDTO dto);

    List<BookOnlinePetParams> defToParam(List<SelectedPetServiceDef> defList);

    BookOnlinePetParams defToParam(SelectedPetServiceDef def);

    @Mapping(target = "enableNoShowFee", source = "paymentType")
    void mergeGroupToSetting(BookOnlinePaymentBaseSettingDTO groupSetting, @MappingTarget BookOnlineDTO setting);
}
