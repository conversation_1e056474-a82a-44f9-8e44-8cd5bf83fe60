/*
 * @since 2024-06-18 09:50:09
 * <AUTHOR> <<EMAIL>>
 */

package com.moego.client.api.v1.membership.converter;

import com.moego.client.api.v1.shared.util.OperationUtil;
import com.moego.client.api.v1.shared.util.PaginationConverter;
import com.moego.idl.client.membership.v1.BuyMembershipParams;
import com.moego.idl.client.membership.v1.BuyMembershipResult;
import com.moego.idl.client.membership.v1.CreateSellLinkForAppParams;
import com.moego.idl.client.membership.v1.CreateSellLinkForAppResult;
import com.moego.idl.client.membership.v1.CreateSellLinkParams;
import com.moego.idl.client.membership.v1.CreateSellLinkResult;
import com.moego.idl.client.membership.v1.GetBuyResultParams;
import com.moego.idl.client.membership.v1.GetBuyResultResult;
import com.moego.idl.client.membership.v1.GetSellLinkPublicInfoParams;
import com.moego.idl.client.membership.v1.GetSellLinkPublicInfoResult;
import com.moego.idl.client.membership.v1.ListMembershipsParams;
import com.moego.idl.client.membership.v1.ListMembershipsResult;
import com.moego.idl.client.membership.v1.ListSubscriptionsForAppParams;
import com.moego.idl.client.membership.v1.ListSubscriptionsForAppResult;
import com.moego.idl.client.membership.v1.ListSubscriptionsParams;
import com.moego.idl.client.membership.v1.ListSubscriptionsResult;
import com.moego.idl.models.business_customer.v1.BusinessCustomerCreateDef;
import com.moego.idl.models.business_customer.v1.BusinessCustomerWithAdditionalInfoCreateDef;
import com.moego.idl.models.membership.v1.SellLinkCreateDef;
import com.moego.idl.models.membership.v1.SellLinkModel;
import com.moego.idl.models.membership.v1.SubscriptionCreateDef;
import com.moego.idl.models.membership.v1.SubscriptionModel;
import com.moego.idl.models.organization.v1.Tenant;
import com.moego.idl.service.business_customer.v1.CreateCustomerWithAdditionalInfoRequest;
import com.moego.idl.service.business_customer.v1.GetCustomerRequest;
import com.moego.idl.service.business_customer.v1.GetCustomerResponse;
import com.moego.idl.service.membership.v1.CreateSellLinkRequest;
import com.moego.idl.service.membership.v1.CreateSellLinkResponse;
import com.moego.idl.service.membership.v1.CreateSubscriptionRequest;
import com.moego.idl.service.membership.v1.CreateSubscriptionResponse;
import com.moego.idl.service.membership.v1.GetMembershipRequest;
import com.moego.idl.service.membership.v1.GetMembershipResponse;
import com.moego.idl.service.membership.v1.GetSellLinkRequest;
import com.moego.idl.service.membership.v1.GetSellLinkResponse;
import com.moego.idl.service.membership.v1.GetSubscriptionRequest;
import com.moego.idl.service.membership.v1.GetSubscriptionResponse;
import com.moego.idl.service.membership.v1.ListMembershipsRequest;
import com.moego.idl.service.membership.v1.ListMembershipsResponse;
import com.moego.idl.service.membership.v1.ListSubscriptionsRequest;
import com.moego.idl.service.membership.v1.ListSubscriptionsResponse;
import com.moego.idl.service.organization.v1.GetCompanyPreferenceSettingRequest;
import com.moego.idl.service.organization.v1.GetCompanyPreferenceSettingResponse;
import com.moego.idl.service.organization.v1.GetLocationDetailRequest;
import com.moego.idl.service.organization.v1.GetLocationDetailResponse;
import com.moego.idl.service.organization.v1.GetTaxRuleRequest;
import com.moego.idl.service.organization.v1.GetTaxRuleResponse;
import com.moego.lib.common.auth.AuthContext;
import com.moego.server.grooming.dto.ob.OBBusinessDTO;
import com.moego.server.payment.dto.CardDTO;
import com.moego.server.payment.dto.UsBankAccountDTO;
import java.util.List;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;
import org.springframework.beans.factory.annotation.Autowired;

@Mapper(
        componentModel = MappingConstants.ComponentModel.SPRING,
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        unmappedSourcePolicy = ReportingPolicy.IGNORE,
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        imports = {AuthContext.class, OperationUtil.class, SubscriptionModel.class, PaginationConverter.class})
public abstract class MembershipConverter {
    @Autowired
    protected PaginationConverter paginationConverter;

    public long businessId() {
        return AuthContext.get().businessId();
    }

    public abstract GetSellLinkRequest getSellLinkRequest(GetSellLinkPublicInfoParams params);

    @Mapping(target = "companyId", source = "sellLink.companyId")
    public abstract GetCompanyPreferenceSettingRequest getCompanyPreferenceSettingRequest(
            GetSellLinkResponse sellLinkOutput);

    @Mapping(target = "id", source = "sellLink.businessId")
    public abstract GetLocationDetailRequest getLocationDetailRequest(GetSellLinkResponse sellLinkOutput);

    @Mapping(target = "id", source = "sellLink.membershipId")
    public abstract GetMembershipRequest getMembershipRequest(GetSellLinkResponse sellLinkOutput);

    @Mapping(target = "tenant", ignore = true)
    @Mapping(target = "id", source = "sellLink.customerId")
    public abstract GetCustomerRequest getCustomerRequest(GetSellLinkResponse sellLinkOutput);

    public abstract Tenant tenant(Long companyId);

    @Mapping(target = "id", source = "membership.taxId")
    @Mapping(target = "tenant", source = "membership.companyId")
    public abstract GetTaxRuleRequest getTaxRuleRequest(GetMembershipResponse output);

    @Mapping(target = "customerMembership.customerId", source = "response.sellLink.customerId")
    @Mapping(target = "customerMembership.membershipId", source = "response.sellLink.membershipId")
    @Mapping(target = "companyId", source = "response.sellLink.companyId")
    @Mapping(target = "silent", constant = "true")
    public abstract GetSubscriptionRequest getSubscriptionRequest(
            GetSellLinkResponse response, List<SubscriptionModel.Status> statusIn);

    public GetSubscriptionRequest getNonCancelledSubscriptionRequest(GetSellLinkResponse sellLinkOutput) {
        return getSubscriptionRequest(
                sellLinkOutput, List.of(SubscriptionModel.Status.PENDING, SubscriptionModel.Status.ACTIVE));
    }

    @Mapping(target = "externalCardId", source = "externalCardId")
    public abstract SubscriptionCreateDef subscriptionCreateDef(
            BuyMembershipParams params, SellLinkModel sellLink, String externalCardId);

    @Mapping(target = "companyPreference", source = "companyOutput.preferenceSetting")
    @Mapping(target = "business", source = "businessOutput.location")
    @Mapping(target = "tax", source = "taxOutput.rule")
    public abstract GetSellLinkPublicInfoResult getSellLinkResult(
            GetSellLinkResponse sellLinkOutput,
            GetCompanyPreferenceSettingResponse companyOutput,
            GetLocationDetailResponse businessOutput,
            GetMembershipResponse membershipOutput,
            GetTaxRuleResponse taxOutput,
            GetCustomerResponse customerOutput,
            List<CardDTO> customerCards,
            List<UsBankAccountDTO> usBankAccounts,
            GetSubscriptionResponse subscriptionOutput);

    public abstract GetSellLinkRequest getSellLinkRequest(BuyMembershipParams request);

    @Mapping(target = "operation", expression = "java(OperationUtil.customerOperation(sellLink.getCustomerId()))")
    @Mapping(target = "sellLinkId", source = "sellLink.id")
    @Mapping(target = "businessId", source = "sellLink.businessId")
    @Mapping(target = "companyId", source = "sellLink.companyId")
    @Mapping(target = "sellOperatorStaffId", source = "sellLink.operatorStaffId")
    @Mapping(target = "subscriptionDef", expression = "java(subscriptionCreateDef(params, sellLink, externalCardId))")
    public abstract CreateSubscriptionRequest createSubscriptionRequest(
            BuyMembershipParams params, SellLinkModel sellLink, String externalCardId);

    public abstract BuyMembershipResult buyMembershipResult(CreateSubscriptionResponse createOutput);

    public abstract GetSellLinkRequest getSellLinkRequest(GetBuyResultParams request);

    public abstract GetBuyResultResult getBuyResult(GetSubscriptionResponse subscriptionOutput);

    @Mapping(target = "statusIn", source = "params.status")
    @Mapping(target = "pagination", defaultExpression = "java(paginationConverter.request())")
    @Mapping(target = "onlyOnlineBookingEnabled", constant = "true")
    public abstract ListMembershipsRequest listMembershipsRequest(Long companyId, ListMembershipsParams params);

    public abstract ListMembershipsResult listMembershipsResult(ListMembershipsResponse response);

    @Mapping(target = "operation", expression = "java(OperationUtil.customerOperation(customerId))")
    @Mapping(target = "sellLinkDef", expression = "java(sellLinkCreateDef(params, customerId))")
    public abstract CreateSellLinkRequest createSellLinkRequest(
            OBBusinessDTO dto, CreateSellLinkParams params, Long customerId);

    public abstract SellLinkCreateDef sellLinkCreateDef(CreateSellLinkParams params, Long customerId);

    public abstract CreateSellLinkResult createSellLinkResult(CreateSellLinkResponse response);

    @Mapping(target = "tenant", source = "companyId")
    @Mapping(
            target = "customerWithAdditionalInfo",
            expression =
                    "java(updatePreferredBusinessId(customerWithPetInfo.getCustomerWithAdditionalInfo(), preferredBusinessId))")
    public abstract CreateCustomerWithAdditionalInfoRequest createCustomerRequest(
            Long companyId, Long preferredBusinessId, CreateSellLinkParams.CustomerWithPetInfo customerWithPetInfo);

    @Mapping(
            target = "customer",
            expression = "java(updatePreferredBusinessId(def.getCustomer(), preferredBusinessId))")
    protected abstract BusinessCustomerWithAdditionalInfoCreateDef updatePreferredBusinessId(
            BusinessCustomerWithAdditionalInfoCreateDef def, Long preferredBusinessId);

    @Mapping(target = "preferredBusinessId", source = "preferredBusinessId")
    protected abstract BusinessCustomerCreateDef updatePreferredBusinessId(
            BusinessCustomerCreateDef def, Long preferredBusinessId);

    @Mapping(target = "tenant", source = "companyId")
    @Mapping(target = "customerIdIn", source = "customerId")
    @Mapping(target = "pagination", defaultExpression = "java(paginationConverter.request())")
    public abstract ListSubscriptionsRequest listSubscriptionsRequest(
            Long companyId, Long customerId, ListSubscriptionsParams params);

    @Mapping(target = "membershipSubscriptions", source = "result.membershipSubscriptions")
    @Mapping(target = "pagination", source = "result.pagination")
    public abstract ListSubscriptionsResult listSubscriptionsResult(ListSubscriptionsResponse response);

    @Mapping(target = "tenant", source = "companyId")
    @Mapping(target = "customerIdIn", source = "customerId")
    @Mapping(target = "pagination", defaultExpression = "java(paginationConverter.request())")
    public abstract ListSubscriptionsRequest listSubscriptionsRequest(
            Long companyId, Long customerId, ListSubscriptionsForAppParams params);

    @Mapping(target = "membershipSubscriptions", source = "result.membershipSubscriptions")
    @Mapping(target = "pagination", source = "result.pagination")
    public abstract ListSubscriptionsForAppResult listSubscriptionsForAppResult(ListSubscriptionsResponse response);

    @Mapping(target = "tenant", source = "companyId")
    @Mapping(
            target = "customerWithAdditionalInfo",
            expression =
                    "java(updatePreferredBusinessId(customerWithPetInfo.getCustomerWithAdditionalInfo(), preferredBusinessId))")
    public abstract CreateCustomerWithAdditionalInfoRequest createCustomerForAppRequest(
            Long companyId,
            Long preferredBusinessId,
            CreateSellLinkForAppParams.CustomerWithPetInfo customerWithPetInfo);

    @Mapping(target = "operation", expression = "java(OperationUtil.customerOperation(customerId))")
    @Mapping(target = "sellLinkDef", expression = "java(sellLinkCreateForAppDef(params, customerId))")
    public abstract CreateSellLinkRequest createSellLinkForAppRequest(
            CreateSellLinkForAppParams params, Long customerId);

    public abstract SellLinkCreateDef sellLinkCreateForAppDef(CreateSellLinkForAppParams params, Long customerId);

    public abstract CreateSellLinkForAppResult createSellLinkForAppResult(CreateSellLinkResponse response);

    public long customerId() {
        return AuthContext.get().customerId();
    }

    public int statusToSort(SubscriptionModel.Status status) {
        return switch (status) {
            case ACTIVE -> 1;
            case PENDING -> 2;
            case CANCELLED -> 3;
            default -> 4;
        };
    }
}
