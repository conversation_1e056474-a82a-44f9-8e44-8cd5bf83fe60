package com.moego.client.api.v1.online_booking.converter;

import com.google.type.Date;
import com.moego.idl.models.business_customer.v1.BusinessPetVaccineRecordModel;
import com.moego.idl.models.business_customer.v1.Source;
import com.moego.idl.models.business_customer.v1.VerifyStatus;
import com.moego.server.customer.dto.VaccineBindingRecordDto;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 * @since 2024/4/2
 */
@Mapper(
        componentModel = MappingConstants.ComponentModel.SPRING,
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        unmappedSourcePolicy = ReportingPolicy.IGNORE)
public interface BusinessPetVaccineConverter {

    List<BusinessPetVaccineRecordModel> dto2Model(List<VaccineBindingRecordDto> dtoList);

    @Mapping(target = "id", source = "vaccineBindingId")
    @Mapping(
            target = "expirationDate",
            source = "expirationDate",
            conditionExpression = "java(toDate(dto.getExpirationDate()) != null)")
    BusinessPetVaccineRecordModel dto2Model(VaccineBindingRecordDto dto);

    default Date toDate(String date) {
        try {
            var localDate = LocalDate.parse(date, DateTimeFormatter.ISO_LOCAL_DATE);
            return Date.newBuilder()
                    .setYear(localDate.getYear())
                    .setMonth(localDate.getMonthValue())
                    .setDay(localDate.getDayOfMonth())
                    .build();
        } catch (Exception e) {
            return null;
        }
    }

    default Source toSource(Byte value) {
        return Source.forNumber(value);
    }

    default VerifyStatus toVerifyStatus(Byte value) {
        return VerifyStatus.forNumber(value);
    }
}
