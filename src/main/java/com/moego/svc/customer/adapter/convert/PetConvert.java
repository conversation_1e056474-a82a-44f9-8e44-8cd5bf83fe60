package com.moego.svc.customer.adapter.convert;

import com.moego.idl.models.customer.v1.CustomerPetModel;
import com.moego.idl.models.customer.v1.PetGender;
import com.moego.idl.models.customer.v1.PetType;
import com.moego.idl.service.customer.v1.InitPetInputPet;
import com.moego.idl.service.customer.v1.PetInput;
import com.moego.server.customer.dto.CustomerPetDetailDTO;
import com.moego.svc.customer.model.dto.PetDTO;
import com.moego.svc.customer.repository.entity.Pet;
import java.util.List;
import java.util.Objects;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface PetConvert {
    PetConvert INSTANCE = Mappers.getMapper(PetConvert.class);

    PetDTO poToDto(Pet pet);

    List<PetDTO> poToDto(List<Pet> petList);

    Pet dtoToPo(PetDTO petDTO);

    PetDTO dtoToDto(CustomerPetDetailDTO customerPetDetailDTO);

    @Mapping(target = "petTypeMetadataId", source = "petTypeId")
    PetDTO initInputToDto(InitPetInputPet initPetInputPet);

    @Mapping(target = "petType", source = "petTypeMetadataId", qualifiedByName = "toPetType")
    @Mapping(target = "breedMix", qualifiedByName = "toBreedMixBoolean")
    @Mapping(target = "gender", qualifiedByName = "toGender")
    CustomerPetModel dtoToModel(PetDTO petDTO);

    @Named("toPetType")
    default PetType toPetType(Integer petType) {
        return PetType.forNumber(petType);
    }

    @Named("toBreedMixBoolean")
    default boolean toBreedMixBoolean(Integer breedMix) {
        return Objects.equals(breedMix, 1);
    }

    @Named("toGender")
    default PetGender toGender(Integer gender) {
        return PetGender.forNumber(gender);
    }

    @Mapping(target = "petTypeMetadataId", source = "petType", qualifiedByName = "toPetTypeInteger")
    @Mapping(target = "breedMix", source = "breedMix", qualifiedByName = "toBreedMixInteger")
    @Mapping(target = "gender", source = "gender", qualifiedByName = "toGenderInteger")
    PetDTO inputToDto(PetInput input);

    @Named("toPetTypeInteger")
    default Integer toPetTypeInteger(PetType petType) {
        return petType.getNumber();
    }

    @Named("toBreedMixInteger")
    default Integer toBreedMixInteger(boolean breedMix) {
        return breedMix ? 1 : 0;
    }

    @Named("toGenderInteger")
    default Integer toGenderInteger(PetGender gender) {
        return gender.getNumber();
    }

    @Mapping(target = "id", source = "petId")
    InitPetInputPet dtoToInput(CustomerPetDetailDTO dto);
}
