package com.moego.svc.order.service.impl;

import com.moego.svc.order.repository.entity.Order;
import com.moego.svc.order.repository.mapper.OrderMapper;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class OrderQueryService {

    private final OrderMapper orderMapper;

    public List<Order> getOrderIdsBySourceIdsAndType(Long businessId, List<Long> sourceIds, String sourceType) {
        return orderMapper.selectBySourceTypeAndSourceIds(businessId, sourceType, sourceIds, false);
    }
}
