package com.moego.svc.order.service;

import com.moego.idl.service.order.v1.EditStaffCommissionParams;
import com.moego.svc.order.model.params.SaveSplitTipsRecordParams;

public interface EditStaffTipsService {

    /**
     * 关单后的操作，只管理edit staff
     * @param request EditStaffCommissionParams
     */
    void saveEditStaff(EditStaffCommissionParams request);

    /**
     * 关单后的操作，只管理edit tips
     *
     * @param splitTipsRecordParams SaveSplitTipsRecordParams
     */
    void saveEditTips(SaveSplitTipsRecordParams splitTipsRecordParams);

    /**
     * 新增edit staff 和 edit tips 记录
     * 创建订单的时候需要调用，支付金额变动/pet detail被修改的时候都需要调用
     * 只有关单前的操作
     *
     * @param orderId order id
     */
    void saveEditStaffAndEditTips(Long orderId);
}
