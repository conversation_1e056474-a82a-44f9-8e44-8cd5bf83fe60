package com.moego.svc.order.service.impl;

import com.moego.common.enums.FeatureConst;
import com.moego.common.enums.InvoiceStatusEnum;
import com.moego.common.enums.order.DiscountType;
import com.moego.common.enums.order.LineApplyType;
import com.moego.common.enums.order.OrderItemType;
import com.moego.common.utils.CommonUtil;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.grooming.v1.AppointmentSource;
import com.moego.idl.models.marketing.v1.DiscountCodeModel;
import com.moego.idl.models.marketing.v1.DiscountCodeType;
import com.moego.idl.models.marketing.v1.Item;
import com.moego.idl.models.marketing.v1.RedeemType;
import com.moego.idl.models.order.v1.OrderSourceType;
import com.moego.idl.service.marketing.v1.AutoApplyDiscountCodeInput;
import com.moego.idl.service.marketing.v1.AutoApplyDiscountCodeOutput;
import com.moego.idl.service.marketing.v1.DeleteDiscountCodeLogInput;
import com.moego.idl.service.marketing.v1.DiscountCodeServiceGrpc;
import com.moego.idl.service.marketing.v1.DiscountCodeUsage;
import com.moego.idl.service.marketing.v1.GetAvailableDiscountListForExistingInvoiceInput;
import com.moego.idl.service.marketing.v1.GetAvailableDiscountListInput;
import com.moego.idl.service.marketing.v1.GetAvailableDiscountListOutput;
import com.moego.idl.service.marketing.v1.UseDiscountCodeInput;
import com.moego.idl.utils.v2.PaginationRequest;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.lib.featureflag.FeatureFlagApi;
import com.moego.lib.featureflag.FeatureFlagContext;
import com.moego.lib.featureflag.features.FeatureFlags;
import com.moego.server.grooming.client.IGroomingAppointmentClient;
import com.moego.server.grooming.dto.AppointmentDTO;
import com.moego.server.payment.api.IPaymentPlanService;
import com.moego.svc.order.convert.DiscountCodeConverter;
import com.moego.svc.order.model.bo.OrderBO;
import com.moego.svc.order.model.bo.OrderLineItemBO;
import com.moego.svc.order.model.dto.DiscountCodeDTO;
import com.moego.svc.order.model.dto.DiscountCodeUsageDTO;
import com.moego.svc.order.model.dto.OrderDetailDTO;
import com.moego.svc.order.model.params.GetOrderParams;
import com.moego.svc.order.model.params.UpdateOrderParams;
import com.moego.svc.order.model.params.base.OrderLineDiscountParams;
import com.moego.svc.order.repository.entity.OrderDetail;
import com.moego.svc.order.repository.entity.OrderLineDiscount;
import com.moego.svc.order.repository.entity.OrderLineItem;
import com.moego.svc.order.service.OrderLineDiscountService;
import com.moego.svc.order.service.OrderService;
import com.moego.svc.order.utils.OrderEnumUtil;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

@Slf4j
@Service
@RequiredArgsConstructor
public class OrderDiscountCodeService {

    private final OrderService orderService;
    private final OrderLineDiscountService orderLineDiscountService;
    private final IPaymentPlanService iPaymentPlanClient;
    private final IGroomingAppointmentClient iGroomingAppointmentClient;
    private final DiscountCodeServiceGrpc.DiscountCodeServiceBlockingStub discountCodeClient;
    private final FeatureFlagApi featureFlagApi;

    public GetAvailableDiscountListOutput getAvailableDiscountList(
            PaginationRequest paginationRequest, Long invoiceId, String codeName, Long businessId, Long companyId) {
        GetOrderParams getOrderParams = new GetOrderParams().setId(invoiceId).setBusinessId(businessId);
        OrderDetailDTO orderDetail = orderService.getOrderDetail(getOrderParams);
        if (Objects.isNull(orderDetail)) {
            return GetAvailableDiscountListOutput.newBuilder().build();
        }

        if (orderDetail.getOrder().getTotalAmount().compareTo(BigDecimal.ZERO) <= 0) {
            return GetAvailableDiscountListOutput.newBuilder().build();
        }

        boolean discountCodeEnable = checkDiscountCodeAvailable(businessId)
                && OrderSourceType.APPOINTMENT
                        .name()
                        .equalsIgnoreCase(orderDetail.getOrder().getSourceType());
        if (!discountCodeEnable) {
            return GetAvailableDiscountListOutput.newBuilder().build();
        }

        List<Long> usedDiscountCodeIdList = List.of();
        if (!CollectionUtils.isEmpty(orderDetail.getLineDiscounts())) {
            usedDiscountCodeIdList = orderDetail.getLineDiscounts().stream()
                    .filter(lineDiscount -> !Boolean.TRUE.equals(lineDiscount.getIsDeleted()))
                    .map(OrderLineDiscount::getDiscountCodeId)
                    .filter(discountCodeId -> Objects.nonNull(discountCodeId) && discountCodeId > 0)
                    .distinct()
                    .toList();
        }

        orderDetail.getLineItems().forEach(item -> {
            Integer purchasedQuantity = item.getPurchasedQuantity() != null ? item.getPurchasedQuantity() : 0;
            BigDecimal itemSubTotal =
                    item.getUnitPrice().multiply(BigDecimal.valueOf(item.getQuantity() - purchasedQuantity));
            item.setSubTotalAmount(itemSubTotal);
        });

        List<Item> itemList = orderDetail.getLineItems().stream()
                .filter(discount -> !Boolean.TRUE.equals(discount.getIsDeleted()))
                .map(lineItem -> Item.newBuilder()
                        .setObjectId(lineItem.getObjectId())
                        .setType(lineItem.getType())
                        .setSubTotalAmount(lineItem.getSubTotalAmount().doubleValue())
                        .build())
                .toList();

        GetAvailableDiscountListInput.Builder builder = GetAvailableDiscountListInput.newBuilder();
        builder.setPagination(paginationRequest)
                .setBusinessId(orderDetail.getOrder().getBusinessId())
                .setCompanyId(companyId)
                .setCustomerId(orderDetail.getOrder().getCustomerId())
                .setCodeName(codeName)
                .addAllItems(itemList)
                .addAllUsedDiscountCodeIds(usedDiscountCodeIdList)
                .setSourceId(orderDetail.getOrder().getSourceId());

        return discountCodeClient.getAvailableDiscountList(builder.build());
    }

    private List<DiscountCodeModel> getAvailableDiscountListForExistingInvoice(
            OrderDetail orderDetail, String appointmentDate) {
        Map<Boolean, Set<Long>> discountCodeIdMap = Stream.concat(
                        orderDetail.getOrder().getLineDiscounts().stream(),
                        orderDetail.getLineItems().stream()
                                .filter(lineItem -> !Boolean.TRUE.equals(lineItem.getIsDeleted()))
                                .map(OrderLineItemBO::getLineDiscounts)
                                .filter(Objects::nonNull)
                                .flatMap(Collection::stream))
                .filter(lineDiscount ->
                        !Boolean.TRUE.equals(lineDiscount.getIsDeleted()) && 0 != lineDiscount.getDiscountCodeId())
                .collect(Collectors.groupingBy(
                        item -> Objects.isNull(item.getId()),
                        Collectors.mapping(OrderLineDiscount::getDiscountCodeId, Collectors.toSet())));
        List<Long> willUseDiscountCodeIdList =
                discountCodeIdMap.getOrDefault(Boolean.TRUE, Set.of()).stream().toList();
        List<Long> usedDiscountCodeIdList =
                discountCodeIdMap.getOrDefault(Boolean.FALSE, Set.of()).stream().toList();

        orderDetail.getLineItems().forEach(item -> {
            Integer purchasedQuantity = item.getPurchasedQuantity() != null ? item.getPurchasedQuantity() : 0;
            BigDecimal itemSubTotal =
                    item.getUnitPrice().multiply(BigDecimal.valueOf(item.getQuantity() - purchasedQuantity));
            item.setSubTotalAmount(itemSubTotal);
        });

        List<Item> itemList = orderDetail.getLineItems().stream()
                .filter(discount -> !Boolean.TRUE.equals(discount.getIsDeleted()))
                .map(lineItem -> Item.newBuilder()
                        .setObjectId(lineItem.getObjectId())
                        .setType(lineItem.getType())
                        .setSubTotalAmount(lineItem.getSubTotalAmount().doubleValue())
                        .build())
                .toList();

        GetAvailableDiscountListForExistingInvoiceInput.Builder builder =
                GetAvailableDiscountListForExistingInvoiceInput.newBuilder();
        builder.setBusinessId(orderDetail.getOrder().getBusinessId())
                .setCustomerId(orderDetail.getOrder().getCustomerId())
                .addAllItems(itemList)
                .addAllWillUseDiscountCodeIds(willUseDiscountCodeIdList)
                .addAllUsedDiscountCodeIds(usedDiscountCodeIdList);
        if (StringUtils.hasText(appointmentDate)) {
            builder.setAppointmentDate(appointmentDate);
        }

        return discountCodeClient
                .getAvailableDiscountListForExistingInvoice(builder.build())
                .getDiscountCodeModelsList();
    }

    public void autoApplyDiscountCode(OrderDetail orderDetail) {
        Long businessId = orderDetail.getOrder().getBusinessId();
        boolean canAutoApplyDiscountCode = checkDiscountCodeAvailable(businessId)
                && OrderSourceType.APPOINTMENT
                        .name()
                        .equalsIgnoreCase(orderDetail.getOrder().getSourceType())
                && !AppointmentSource.APPOINTMENT_SOURCE_OB.equals(
                        orderDetail.getOrder().getSource());
        if (!canAutoApplyDiscountCode) {
            return;
        }

        orderDetail.getLineItems().forEach(item -> {
            Integer purchasedQuantity = item.getPurchasedQuantity() != null ? item.getPurchasedQuantity() : 0;
            BigDecimal itemSubTotal =
                    item.getUnitPrice().multiply(BigDecimal.valueOf(item.getQuantity() - purchasedQuantity));
            item.setSubTotalAmount(itemSubTotal);
        });

        List<Item> itemList = orderDetail.getLineItems().stream()
                .filter(lineItem -> !Boolean.TRUE.equals(lineItem.getIsDeleted()))
                .map(lineItem -> Item.newBuilder()
                        .setObjectId(lineItem.getObjectId())
                        .setType(lineItem.getType())
                        .setSubTotalAmount(lineItem.getSubTotalAmount().doubleValue())
                        .build())
                .toList();
        AutoApplyDiscountCodeInput.Builder builder = AutoApplyDiscountCodeInput.newBuilder();
        builder.setBusinessId(orderDetail.getOrder().getBusinessId())
                .setCustomerId(orderDetail.getOrder().getCustomerId())
                .setSourceId(orderDetail.getOrder().getSourceId())
                .setOrderId(orderDetail.getOrder().getId())
                .setStaffId(orderDetail.getOrder().getCreateBy())
                .addAllItems(itemList);
        String appointmentDate = getAppointmentDate(orderDetail);
        if (StringUtils.hasText(appointmentDate)) {
            builder.setAppointmentDate(appointmentDate);
        }

        AutoApplyDiscountCodeOutput autoApplyDiscountCodeOutput =
                discountCodeClient.autoApplyDiscountCode(builder.build());
        if (autoApplyDiscountCodeOutput.getSuccess()) {
            DiscountCodeModel discountCode = autoApplyDiscountCodeOutput.getDiscountCodeModel();
            //            fillDiscountCodeEach(orderDetail, DiscountCodeConverter.INSTANCE.toDTO(discountCode));
            UpdateOrderParams updateOrderParams = new UpdateOrderParams();
            updateOrderParams.setOrderId(orderDetail.getOrder().getId());
            OrderLineDiscountParams orderLineDiscountParams = new OrderLineDiscountParams();
            orderLineDiscountParams
                    .setOrderId(orderDetail.getOrder().getId())
                    .setApplyBy(orderDetail.getOrder().getCreateBy())
                    .setBusinessId(businessId)
                    .setDiscountCodeId(discountCode.getId());
            updateOrderParams.setLineDiscounts(List.of(orderLineDiscountParams));
            orderService.updateOrderIncr(updateOrderParams);
        }
    }

    private void fillDiscountCodeEach(OrderDetail orderDetail, DiscountCodeDTO discountCode) {
        Long businessId = orderDetail.getOrder().getBusinessId();
        Long discountCodeId = discountCode.getId();
        long applyBy = Objects.nonNull(orderDetail.getOrder().getUpdateBy())
                ? orderDetail.getOrder().getUpdateBy()
                : 0;
        DiscountCodeType discountCodeType = discountCode.getType();
        BigDecimal discountCodeAmount = discountCode.getAmount();

        OrderLineDiscount discountParams = new OrderLineDiscount();
        discountParams.setBusinessId(businessId);
        discountParams.setDiscountCodeId(discountCodeId);
        discountParams.setApplyBy(applyBy);
        discountParams.setApplySequence(discountCode.getApplySequence());
        if (Objects.equals(discountCodeType, DiscountCodeType.DISCOUNT_CODE_TYPE_PERCENTAGE)) {
            discountParams.setDiscountRate(discountCodeAmount);
            discountParams.setDiscountType(DiscountType.PERCENTAGE.getType());
        } else if (Objects.equals(discountCodeType, DiscountCodeType.DISCOUNT_CODE_TYPE_FIXED_AMOUNT)) {
            discountParams.setDiscountAmount(discountCodeAmount);
            discountParams.setDiscountType(DiscountType.AMOUNT.getType());
        } else if (Objects.equals(discountCodeType, DiscountCodeType.DISCOUNT_CODE_TYPE_CREDIT)) {
            discountParams.setDiscountAmount(discountCodeAmount);
            discountParams.setDiscountType(DiscountType.CREDIT.getType());
        }

        // 根据白名单提前设置 item discount 并进行逻辑短路
        if (isLineItemDiscountEnable(orderDetail.getOrder()) && CommonUtil.isNormal(discountCode.getOrderItemId())) {
            discountParams.setApplyType(LineApplyType.TYPE_ITEM.getType());
            orderDetail.getLineItems().stream()
                    .filter(item -> Objects.equals(OrderItemType.ITEM_TYPE_SERVICE.getType(), item.getType())
                            || Objects.equals(OrderItemType.ITEM_TYPE_PRODUCT.getType(), item.getType()))
                    .filter(item -> !Boolean.TRUE.equals(item.getIsDeleted()))
                    .filter(item -> Objects.equals(item.getId(), discountCode.getOrderItemId()))
                    .forEach(item -> {
                        OrderLineDiscount params = new OrderLineDiscount();
                        BeanUtils.copyProperties(discountParams, params);
                        item.getLineDiscounts().add(params);
                    });
            return;
        }

        // 无限制
        boolean noLimitation = Objects.equals(Boolean.TRUE, discountCode.getAllowedAllThing())
                || (Objects.equals(Boolean.TRUE, discountCode.getAllowedAllServices())
                        && Objects.equals(Boolean.TRUE, discountCode.getAllowedAllProducts()));
        if (noLimitation) {
            discountParams.setApplyType(LineApplyType.TYPE_ALL.getType());
            orderDetail.getOrder().getLineDiscounts().add(discountParams);
            return;
        }

        // 仅限 service 和 addon 使用
        boolean onlyAllowAllServicesAndAddOns = Objects.equals(Boolean.TRUE, discountCode.getAllowedAllServices())
                && !Objects.equals(Boolean.TRUE, discountCode.getAllowedAllProducts())
                && CollectionUtils.isEmpty(discountCode.getProductIds());
        if (onlyAllowAllServicesAndAddOns) {
            boolean alreadyHasDiscount = orderDetail.getOrder().getLineDiscounts().stream()
                    .filter(lineDiscount -> !Boolean.TRUE.equals(lineDiscount.getIsDeleted()))
                    .anyMatch(lineDiscount ->
                            Objects.equals(lineDiscount.getApplyType(), LineApplyType.TYPE_SERVICE.getType())
                                    && !Objects.equals(lineDiscount.getDiscountCodeId(), discountCodeId));
            if (alreadyHasDiscount) {
                return;
            }
            discountParams.setApplyType(LineApplyType.TYPE_SERVICE.getType());
            orderDetail.getOrder().getLineDiscounts().add(discountParams);
            return;
        }

        // 仅限 product 使用
        boolean onlyAllowAllProducts = Objects.equals(Boolean.TRUE, discountCode.getAllowedAllProducts())
                && !Objects.equals(Boolean.TRUE, discountCode.getAllowedAllServices())
                && CollectionUtils.isEmpty(discountCode.getServiceIds())
                && CollectionUtils.isEmpty(discountCode.getAddOnIds());
        if (onlyAllowAllProducts) {
            boolean alreadyHasDiscount = orderDetail.getOrder().getLineDiscounts().stream()
                    .filter(lineDiscount -> !Boolean.TRUE.equals(lineDiscount.getIsDeleted()))
                    .anyMatch(lineDiscount ->
                            Objects.equals(lineDiscount.getApplyType(), LineApplyType.TYPE_PRODUCT.getType())
                                    && !Objects.equals(lineDiscount.getDiscountCodeId(), discountCodeId));
            if (alreadyHasDiscount) {
                return;
            }
            discountParams.setApplyType(LineApplyType.TYPE_PRODUCT.getType());
            orderDetail.getOrder().getLineDiscounts().add(discountParams);
            return;
        }

        // 指定 order item id 使用
        if (CommonUtil.isNormal(discountCode.getOrderItemId())) {
            discountParams.setApplyType(LineApplyType.TYPE_ITEM.getType());
            orderDetail.getLineItems().stream()
                    .filter(item -> Objects.equals(OrderItemType.ITEM_TYPE_SERVICE.getType(), item.getType())
                            || Objects.equals(OrderItemType.ITEM_TYPE_PRODUCT.getType(), item.getType()))
                    .filter(item -> !Boolean.TRUE.equals(item.getIsDeleted()))
                    .filter(item -> Objects.equals(item.getId(), discountCode.getOrderItemId()))
                    .forEach(item -> {
                        OrderLineDiscount params = new OrderLineDiscount();
                        BeanUtils.copyProperties(discountParams, params);
                        item.getLineDiscounts().add(params);
                    });
            return;
        }

        // 其他情况
        if (CollectionUtils.isEmpty(discountCode.getServiceIds())
                && CollectionUtils.isEmpty(discountCode.getAddOnIds())
                && CollectionUtils.isEmpty(discountCode.getProductIds())) {
            return;
        }
        List<Long> serviceAndAddOnIdList = Stream.of(discountCode.getServiceIds(), discountCode.getAddOnIds())
                .flatMap(List::stream)
                .toList();
        List<OrderLineItemBO> eligibleLineItems = orderDetail.getLineItems().stream()
                .filter(item -> Objects.equals(OrderItemType.ITEM_TYPE_SERVICE.getType(), item.getType())
                        || Objects.equals(OrderItemType.ITEM_TYPE_PRODUCT.getType(), item.getType()))
                .filter(item -> !Boolean.TRUE.equals(item.getIsDeleted()))
                .filter(item -> {
                    // item 为 service 类型
                    if (Objects.equals(OrderItemType.ITEM_TYPE_SERVICE.getType(), item.getType())) {
                        // discount 允许所有 service 使用或者 item 在 discount 允许的 service、addon 列表中
                        return Objects.equals(
                                Boolean.TRUE,
                                discountCode.getAllowedAllServices()
                                        || serviceAndAddOnIdList.contains(item.getObjectId()));
                    }
                    // item 为 product 类型
                    if (Objects.equals(OrderItemType.ITEM_TYPE_PRODUCT.getType(), item.getType())) {
                        // discount 允许所有 product 使用或者 item 在 discount 允许的 product 列表中
                        return Objects.equals(Boolean.TRUE, discountCode.getAllowedAllProducts())
                                || discountCode.getProductIds().contains(item.getObjectId());
                    }
                    return false;
                })
                .filter(item -> CollectionUtils.isEmpty(item.getLineDiscounts()))
                .filter(item -> !Objects.equals(item.getQuantity(), item.getPurchasedQuantity()))
                .toList();
        discountParams.setApplyType(LineApplyType.TYPE_ITEM.getType());
        calculateDiscountAmount(discountCodeType, discountParams, discountCodeAmount, eligibleLineItems);
    }

    private static void calculateDiscountAmount(
            DiscountCodeType discountCodeType,
            OrderLineDiscount params,
            BigDecimal discountCodeAmount,
            List<OrderLineItemBO> eligibleLineItems) {
        if (Objects.equals(discountCodeType, DiscountCodeType.DISCOUNT_CODE_TYPE_PERCENTAGE)) {
            params.setDiscountRate(discountCodeAmount);
            params.setDiscountType(DiscountType.PERCENTAGE.getType());
            eligibleLineItems.forEach(item -> {
                OrderLineDiscount discountParams = new OrderLineDiscount();
                BeanUtils.copyProperties(params, discountParams);
                item.getLineDiscounts().add(discountParams);
            });
        } else if (Objects.equals(discountCodeType, DiscountCodeType.DISCOUNT_CODE_TYPE_FIXED_AMOUNT)
                || Objects.equals(discountCodeType, DiscountCodeType.DISCOUNT_CODE_TYPE_CREDIT)) {
            BigDecimal allSubTotal = eligibleLineItems.stream()
                    .map(OrderLineItem::getSubTotalAmount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            if (allSubTotal.compareTo(BigDecimal.ZERO) == 0) {
                return;
            }

            params.setDiscountType(DiscountType.AMOUNT.getType());
            Function<BigDecimal, BigDecimal> countFunction =
                    x -> discountCodeAmount.multiply(x).divide(allSubTotal, RoundingMode.HALF_UP);
            BigDecimal currentDiscountAmount = discountCodeAmount;
            for (int i = 0; i < eligibleLineItems.size(); i++) {
                OrderLineDiscount discountParams = new OrderLineDiscount();
                BeanUtils.copyProperties(params, discountParams);
                if (i == eligibleLineItems.size() - 1) {
                    discountParams.setDiscountAmount(currentDiscountAmount);
                } else {
                    discountParams.setDiscountAmount(
                            countFunction.apply(eligibleLineItems.get(i).getSubTotalAmount()));
                    currentDiscountAmount = currentDiscountAmount.subtract(discountParams.getDiscountAmount());
                }
                eligibleLineItems.get(i).getLineDiscounts().add(discountParams);
            }
        }
    }

    public void fillDiscountCode(OrderDetail orderDetail) {
        if (Objects.equals(
                InvoiceStatusEnum.INVOICE_STATUS_REMOVED, orderDetail.getOrder().getStatus())) {
            // clean discount code
            Stream.concat(
                            orderDetail.getOrder().getLineDiscounts().stream(),
                            orderDetail.getLineItems().stream()
                                    .filter(lineItem -> !Boolean.TRUE.equals(lineItem.getIsDeleted()))
                                    .map(OrderLineItemBO::getLineDiscounts)
                                    .filter(Objects::nonNull)
                                    .flatMap(Collection::stream))
                    .filter(discount ->
                            !Boolean.TRUE.equals(discount.getIsDeleted()) && 0 != discount.getDiscountCodeId())
                    .forEach(discount -> discount.setIsDeleted(true));
            return;
        }

        List<Long> discountCodeIdList = Stream.concat(
                        orderDetail.getOrder().getLineDiscounts().stream(),
                        orderDetail.getLineItems().stream()
                                .filter(lineItem -> !Boolean.TRUE.equals(lineItem.getIsDeleted()))
                                .map(OrderLineItemBO::getLineDiscounts)
                                .filter(Objects::nonNull)
                                .flatMap(Collection::stream))
                .filter(discount -> !Boolean.TRUE.equals(discount.getIsDeleted()) && 0 != discount.getDiscountCodeId())
                .map(OrderLineDiscount::getDiscountCodeId)
                .distinct()
                .toList();

        Map<Long, DiscountCodeDTO> discountCodeModelMap;
        if (!CollectionUtils.isEmpty(discountCodeIdList)) {
            discountCodeModelMap =
                    getAvailableDiscountListForExistingInvoice(orderDetail, getAppointmentDate(orderDetail)).stream()
                            .collect(Collectors.toMap(
                                    DiscountCodeModel::getId, DiscountCodeConverter.INSTANCE::toDTO, (o1, o2) -> o1));
        } else {
            discountCodeModelMap = Map.of();
        }

        // construct discount code list, only support these apply type: all, service, product
        List<DiscountCodeDTO> discountCodeList = Stream.concat(
                        orderDetail.getOrder().getLineDiscounts().stream(),
                        orderDetail.getLineItems().stream()
                                .filter(lineItem -> !Boolean.TRUE.equals(lineItem.getIsDeleted()))
                                .map(OrderLineItemBO::getLineDiscounts)
                                .filter(Objects::nonNull)
                                .flatMap(Collection::stream))
                .filter(discount -> !Boolean.TRUE.equals(discount.getIsDeleted()))
                .map(orderLineDiscount -> {
                    Long discountCodeId = orderLineDiscount.getDiscountCodeId();
                    if (Objects.equals(0L, discountCodeId)) {
                        DiscountCodeDTO discountCodeDTO = new DiscountCodeDTO();
                        discountCodeDTO.setId(0L);
                        discountCodeDTO.setAmount(orderLineDiscount.getDiscountAmount());
                        if (DiscountType.AMOUNT.getType().equals(orderLineDiscount.getDiscountType())) {
                            discountCodeDTO.setType(DiscountCodeType.DISCOUNT_CODE_TYPE_FIXED_AMOUNT);
                            discountCodeDTO.setAmount(orderLineDiscount.getDiscountAmount());
                        } else if (DiscountType.CREDIT.getType().equals(orderLineDiscount.getDiscountType())) {
                            discountCodeDTO.setType(DiscountCodeType.DISCOUNT_CODE_TYPE_CREDIT);
                            discountCodeDTO.setAmount(orderLineDiscount.getDiscountAmount());
                        } else if (DiscountType.PERCENTAGE.getType().equals(orderLineDiscount.getDiscountType())) {
                            discountCodeDTO.setType(DiscountCodeType.DISCOUNT_CODE_TYPE_PERCENTAGE);
                            discountCodeDTO.setAmount(orderLineDiscount.getDiscountRate());
                        }
                        if (LineApplyType.TYPE_ALL.getType().equals(orderLineDiscount.getApplyType())) {
                            discountCodeDTO.setAllowedAllThing(true);
                        } else if (LineApplyType.TYPE_SERVICE.getType().equals(orderLineDiscount.getApplyType())) {
                            discountCodeDTO.setAllowedAllServices(true);
                            discountCodeDTO.setAllowedAllProducts(false);
                        } else if (LineApplyType.TYPE_PRODUCT.getType().equals(orderLineDiscount.getApplyType())) {
                            discountCodeDTO.setAllowedAllProducts(true);
                            discountCodeDTO.setAllowedAllServices(false);
                        } else if (LineApplyType.TYPE_ITEM.getType().equals(orderLineDiscount.getApplyType())) {
                            discountCodeDTO.setOrderItemId(orderLineDiscount.getOrderItemId());
                        }
                        discountCodeDTO.setApplySequence(orderLineDiscount.getApplySequence());
                        return discountCodeDTO;
                    }
                    // 由于这里可能会出现一个 discount apply 到多个 item 的情况
                    // 这里必须深拷贝一下 discountCodeDTO，防止 item id 赋值的时候被覆盖
                    DiscountCodeDTO discountCodeDTO =
                            DiscountCodeConverter.INSTANCE.copy(discountCodeModelMap.get(discountCodeId));
                    if (Objects.nonNull(discountCodeDTO)) {
                        discountCodeDTO.setApplySequence(orderLineDiscount.getApplySequence());
                    }
                    if (isLineItemDiscountEnable(orderDetail.getOrder())
                            && LineApplyType.TYPE_ITEM.getType().equals(orderLineDiscount.getApplyType())
                            && CommonUtil.isNormal(orderLineDiscount.getOrderItemId())) {
                        discountCodeDTO.setOrderItemId(orderLineDiscount.getOrderItemId());
                    }
                    return discountCodeDTO;
                })
                .filter(Objects::nonNull)
                .toList();

        // clean all discount
        if (!CollectionUtils.isEmpty(orderDetail.getOrder().getLineDiscounts())) {
            orderDetail.getOrder().getLineDiscounts().forEach(lineDiscount -> lineDiscount.setIsDeleted(true));
        } else {
            orderDetail.getOrder().setLineDiscounts(new ArrayList<>());
        }
        orderDetail.getOrder().setDiscountCodeUsageList(new ArrayList<>());
        orderDetail.getLineItems().forEach(lineItem -> {
            if (!CollectionUtils.isEmpty(lineItem.getLineDiscounts())) {
                lineItem.getLineDiscounts().forEach(lineDiscount -> lineDiscount.setIsDeleted(true));
            } else {
                lineItem.setLineDiscounts(new ArrayList<>());
            }
            lineItem.setDiscountCodeUsageList(new ArrayList<>());
        });

        if (CollectionUtils.isEmpty(discountCodeList)) {
            return;
        }

        discountCodeList.forEach(discountCode -> fillDiscountCodeEach(orderDetail, discountCode));
    }

    private boolean isLineItemDiscountEnable(OrderBO order) {
        return featureFlagApi.isOn(
                FeatureFlags.ENTERPRISE_DISCOUNT_REDEEM_LINEITEM,
                FeatureFlagContext.builder()
                        .company(order.getCompanyId())
                        .enterprise(order.getEnterpriseId())
                        .build());
    }

    private String getAppointmentDate(OrderDetail orderDetail) {
        String appointmentDate = Strings.EMPTY;
        if (OrderSourceType.APPOINTMENT
                .name()
                .equalsIgnoreCase(orderDetail.getOrder().getSourceType())) {
            AppointmentDTO appointment = iGroomingAppointmentClient.getAppointmentById(
                    Math.toIntExact(orderDetail.getOrder().getSourceId()));
            if (Objects.nonNull(appointment) && StringUtils.hasText(appointment.getAppointmentDate())) {
                appointmentDate = appointment.getAppointmentDate();
            }
        }
        return appointmentDate;
    }

    public void saveDiscountCodeUsage(OrderDetail orderDetail) {
        boolean hasUsedDiscountCode = Stream.concat(
                        Optional.ofNullable(orderDetail.getOrder().getDiscountCodeUsageList())
                                .orElseGet(Collections::emptyList)
                                .stream(),
                        Optional.ofNullable(orderDetail.getLineItems()).orElseGet(Collections::emptyList).stream()
                                .map(OrderLineItemBO::getDiscountCodeUsageList)
                                .filter(Objects::nonNull)
                                .flatMap(Collection::stream))
                .map(DiscountCodeUsageDTO::getDiscountCodeId)
                .anyMatch(codeId -> codeId > 0);
        if (!hasUsedDiscountCode) {
            // delete discount code log
            discountCodeClient.deleteDiscountCodeLog(DeleteDiscountCodeLogInput.newBuilder()
                    .setRedeemId(orderDetail.getOrder().getSourceId())
                    .build());
            return;
        }

        Map<Long, DiscountCodeUsage> map = new HashMap<>(8);
        Stream.concat(
                        orderDetail.getOrder().getDiscountCodeUsageList().stream(),
                        orderDetail.getLineItems().stream()
                                .map(OrderLineItemBO::getDiscountCodeUsageList)
                                .filter(Objects::nonNull)
                                .flatMap(Collection::stream))
                .filter(usage -> usage.getDiscountCodeId() > 0)
                .forEach(usageDTO -> {
                    Long discountCodeId = usageDTO.getDiscountCodeId();
                    if (map.containsKey(discountCodeId)) {
                        DiscountCodeUsage discountCodeUsage = map.get(discountCodeId);
                        BigDecimal subTotalAmount = BigDecimal.valueOf(discountCodeUsage.getDiscountSalesAmount());
                        DiscountCodeUsage newDiscountCodeUsage = discountCodeUsage.toBuilder()
                                .addObjectIds(usageDTO.getObjectId())
                                .setDiscountSalesAmount(subTotalAmount
                                        .add(usageDTO.getDiscountSalesAmount())
                                        .doubleValue())
                                .build();
                        map.put(discountCodeId, newDiscountCodeUsage);
                        return;
                    }
                    DiscountCodeUsage codeUsage = DiscountCodeUsage.newBuilder()
                            .setDiscountCodeId(discountCodeId)
                            .addObjectIds(usageDTO.getObjectId())
                            .setDiscountSalesAmount(
                                    usageDTO.getDiscountSalesAmount().doubleValue())
                            .setType(usageDTO.getType())
                            .build();
                    map.put(discountCodeId, codeUsage);
                });

        UseDiscountCodeInput discountCodeInput = UseDiscountCodeInput.newBuilder()
                .setBusinessId(Math.toIntExact(orderDetail.getOrder().getBusinessId()))
                .setCustomerId(Math.toIntExact(orderDetail.getOrder().getCustomerId()))
                .setSourceId(Math.toIntExact(orderDetail.getOrder().getSourceId()))
                .setOrderId(Math.toIntExact(orderDetail.getOrder().getId()))
                .setStaffId(Math.toIntExact(orderDetail.getOrder().getUpdateBy()))
                .setRedeemType(getRedeemType(orderDetail))
                .addAllDiscountCodeUsages(map.values())
                .setInvoiceSales(orderDetail
                        .getOrder()
                        .getSubTotalAmount()
                        .subtract(orderDetail.getOrder().getDiscountAmount())
                        .doubleValue())
                .build();
        discountCodeClient.useDiscountCode(discountCodeInput);
    }

    private static RedeemType getRedeemType(OrderDetail orderDetail) {
        return switch (getOrderSourceType(orderDetail)) {
            case APPOINTMENT -> AppointmentSource.APPOINTMENT_SOURCE_OB.equals(
                            orderDetail.getOrder().getSource())
                    ? RedeemType.REDEEM_TYPE_ONLINE_BOOKING
                    : RedeemType.REDEEM_TYPE_APPOINTMENT;
            case PRODUCT -> RedeemType.REDEEM_TYPE_PRODUCT;
            case PACKAGE -> RedeemType.REDEEM_TYPE_PACKAGE;
            case BOOKING_REQUEST -> RedeemType.REDEEM_TYPE_ONLINE_BOOKING;
            default -> throw ExceptionUtil.bizException(
                    Code.CODE_PARAMS_ERROR,
                    "Not support source type: " + orderDetail.getOrder().getSourceType());
        };
    }

    private static OrderSourceType getOrderSourceType(OrderDetail orderDetail) {
        try {
            return OrderEnumUtil.convert2EnumSourceType(orderDetail.getOrder().getSourceType());
        } catch (IllegalArgumentException ignored) {
            return OrderSourceType.UNRECOGNIZED;
        }
    }

    public boolean checkDiscountCodeAvailable(Long businessId) {
        return Boolean.TRUE.equals(
                iPaymentPlanClient.checkFeatureCodeIsEnableByBid(businessId.intValue(), FeatureConst.FC_DISCOUNT_CODE));
    }

    public void migrateDiscountCodeId(Map<Long, Long> discountCodeIdMap) {
        orderLineDiscountService.migrateDiscountCodeIds(discountCodeIdMap);
    }
}
