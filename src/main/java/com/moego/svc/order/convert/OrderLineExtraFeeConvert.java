package com.moego.svc.order.convert;

import com.moego.idl.models.order.v1.OrderLineExtraFeeModel;
import com.moego.svc.order.model.params.base.OrderLineExtraFeeParams;
import com.moego.svc.order.repository.entity.OrderLineExtraFee;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;
import org.springframework.util.CollectionUtils;

@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        unmappedTargetPolicy = ReportingPolicy.WARN,
        unmappedSourcePolicy = ReportingPolicy.WARN)
public interface OrderLineExtraFeeConvert {

    OrderLineExtraFeeConvert INSTANCE = Mappers.getMapper(OrderLineExtraFeeConvert.class);

    OrderLineExtraFee paramToBean(OrderLineExtraFeeParams params);

    List<OrderLineExtraFee> paramToBeans(List<OrderLineExtraFeeParams> paramsList);

    static List<OrderLineExtraFeeParams> modelToParams(List<OrderLineExtraFeeModel> lineExtraFeeModels) {
        if (lineExtraFeeModels.size() == 0) return Collections.emptyList();
        List<OrderLineExtraFeeParams> lineExtraFeeParams = new ArrayList<>();
        for (OrderLineExtraFeeModel model : lineExtraFeeModels) {
            OrderLineExtraFeeParams lineExtraFee = new OrderLineExtraFeeParams()
                    .setId(model.hasId() ? model.getId() : null)
                    .setBusinessId(model.getBusinessId())
                    .setOrderId(model.getOrderId())
                    .setOrderItemId(model.hasOrderItemId() ? model.getOrderItemId() : null)
                    .setApplyType(model.getApplyType())
                    .setIsDeleted(model.hasIsDeleted() && model.getIsDeleted())
                    .setFeeType(model.getFeeType())
                    .setAmount(BigDecimal.valueOf(model.getAmount()))
                    .setName(model.getName())
                    .setDescription(model.getDescription())
                    .setCollectType(model.getCollectType())
                    .setApplyBy(model.getApplyBy())
                    .setApplySequence(model.hasApplySequence() ? model.getApplySequence() : null)
                    .setCreateTime(model.hasCreateTime() ? new Date(model.getCreateTime() * 1000) : null)
                    .setUpdateTime(model.hasUpdateTime() ? new Date(model.getUpdateTime() * 1000) : null);
            lineExtraFeeParams.add(lineExtraFee);
        }
        return lineExtraFeeParams;
    }

    static List<OrderLineExtraFeeModel> beanToModels(List<OrderLineExtraFee> lineExtraFees) {
        if (CollectionUtils.isEmpty(lineExtraFees)) return Collections.emptyList();
        List<OrderLineExtraFeeModel> lineExtraFeeModels = new ArrayList<>();
        for (OrderLineExtraFee entity : lineExtraFees) {
            OrderLineExtraFeeModel lineDiscountModel = OrderLineExtraFeeModel.newBuilder()
                    .setId(entity.getId())
                    .setBusinessId(entity.getBusinessId())
                    .setOrderId(entity.getOrderId())
                    .setOrderItemId(entity.getOrderItemId())
                    .setApplyType(entity.getApplyType())
                    .setIsDeleted(entity.getIsDeleted())
                    .setFeeType(entity.getFeeType())
                    .setAmount(entity.getAmount().doubleValue())
                    .setName(entity.getName())
                    .setDescription(entity.getDescription())
                    .setCollectType(entity.getCollectType())
                    .setApplyBy(entity.getApplyBy())
                    .setApplySequence(entity.getApplySequence())
                    .setCreateTime(entity.getCreateTime().getTime() / 1000)
                    .setUpdateTime(entity.getUpdateTime().getTime() / 1000)
                    .build();
            lineExtraFeeModels.add(lineDiscountModel);
        }
        return lineExtraFeeModels;
    }
}
