package com.moego.svc.order.model.params;

import java.util.List;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

@Getter
@Setter
@Accessors(chain = true)
public class GetCustomerItemsParams {

    private Long customerId;
    private Long businessId;
    private List<String> types;
    private List<Long> objectIds;
    private Long sinceTime;
    private Integer count;

    private Integer lineItemTypes;
}
