package com.moego.svc.organization.mapper.base;

import com.moego.svc.organization.entity.MoeBusinessPaymentMethod;
import com.moego.svc.organization.entity.MoeBusinessPaymentMethodExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface BasePaymentMethodMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business_payment_method
     *
     * @mbg.generated
     */
    long countByExample(MoeBusinessPaymentMethodExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business_payment_method
     *
     * @mbg.generated
     */
    int deleteByExample(MoeBusinessPaymentMethodExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business_payment_method
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business_payment_method
     *
     * @mbg.generated
     */
    int insert(MoeBusinessPaymentMethod row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business_payment_method
     *
     * @mbg.generated
     */
    int insertSelective(MoeBusinessPaymentMethod row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business_payment_method
     *
     * @mbg.generated
     */
    List<MoeBusinessPaymentMethod> selectByExample(MoeBusinessPaymentMethodExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business_payment_method
     *
     * @mbg.generated
     */
    MoeBusinessPaymentMethod selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business_payment_method
     *
     * @mbg.generated
     */
    int updateByExampleSelective(
            @Param("row") MoeBusinessPaymentMethod row, @Param("example") MoeBusinessPaymentMethodExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business_payment_method
     *
     * @mbg.generated
     */
    int updateByExample(
            @Param("row") MoeBusinessPaymentMethod row, @Param("example") MoeBusinessPaymentMethodExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business_payment_method
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(MoeBusinessPaymentMethod row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business_payment_method
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(MoeBusinessPaymentMethod row);
}
