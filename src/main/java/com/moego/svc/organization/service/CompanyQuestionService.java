package com.moego.svc.organization.service;

import com.moego.svc.organization.entity.MoeQuestionRecord;
import com.moego.svc.organization.enums.CompanyQuestionConst;
import com.moego.svc.organization.mapper.MoeQuestionRecordMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class CompanyQuestionService {
    private final MoeQuestionRecordMapper moeQuestionRecordMapper;

    public void initCompantQuestion(Long companyId) {
        try {
            moeQuestionRecordMapper.insertSelective(MoeQuestionRecord.builder()
                    .companyId(companyId)
                    .isRequired(CompanyQuestionConst.IS_REQUIRED_TURE)
                    .build());
        } catch (DuplicateKeyException ignored) {
        }
    }

    public void initCompanyFromEnterpriseQuestion(Long companyId) {
        try {
            moeQuestionRecordMapper.insertSelective(MoeQuestionRecord.builder()
                    .companyId(companyId)
                    .isRequired(CompanyQuestionConst.IS_REQUIRED_FALSE)
                    .build());
        } catch (DuplicateKeyException ignored) {
        }
    }

    /**
     * 查询是否填写过 question
     *
     * @param companyId
     * @return
     */
    public Boolean quesyIsFillQuestoin(Long companyId) {
        var record = queryByCompanyId(companyId);
        if (record != null) {
            return !CompanyQuestionConst.IS_REQUIRED_TURE.equals(record.getIsRequired());
        }
        return true;
    }

    public MoeQuestionRecord queryByCompanyId(Long companyId) {
        return moeQuestionRecordMapper.selectByCompanyId(companyId);
    }

    public void saveQuestionRecord(Long companyId, MoeQuestionRecord saveRecord) {
        var record = moeQuestionRecordMapper.selectByCompanyId(companyId);
        if (record != null) {
            saveRecord.setId(record.getId());
            saveRecord.setIsRequired(CompanyQuestionConst.IS_REQUIRED_FALSE);
            moeQuestionRecordMapper.updateByPrimaryKeySelective(saveRecord);
        }
    }
}
