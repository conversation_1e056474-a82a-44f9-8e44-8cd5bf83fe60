package com.moego.svc.organization.service;

import static com.moego.svc.organization.enums.DataSourceConst.READER;

import com.moego.common.utils.CommonUtil;
import com.moego.common.utils.DateUtil;
import com.moego.svc.organization.dto.BusinessCountDTO;
import com.moego.svc.organization.entity.MoeVan;
import com.moego.svc.organization.entity.MoeVanExample;
import com.moego.svc.organization.entity.MoeVanStaff;
import com.moego.svc.organization.entity.MoeVanStaffExample;
import com.moego.svc.organization.enums.VanStaffStatus;
import com.moego.svc.organization.enums.VanStatus;
import com.moego.svc.organization.mapper.MoeStaffMapper;
import com.moego.svc.organization.mapper.MoeVanMapper;
import com.moego.svc.organization.mapper.MoeVanStaffMapper;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

@Service
@RequiredArgsConstructor
public class VanService {
    private final MoeStaffMapper moeStaffMapper;
    private final MoeVanMapper moeVanMapper;
    private final MoeVanStaffMapper moeVanStaffMapper;

    public Map<Integer, Integer> getVanCountByBusinessIdList(List<Long> businessIdList) {
        List<BusinessCountDTO> businessCountDtoList = moeVanMapper
                .useDataSource(READER)
                .selectUsedVanByBidList(
                        businessIdList.stream().map(Long::intValue).collect(Collectors.toList()));
        return businessCountDtoList.stream()
                .collect(Collectors.toMap(BusinessCountDTO::getBusinessId, BusinessCountDTO::getCount));
    }

    /**
     * 根据 staff id 列表获取员工所属的车辆信息
     *
     * @param staffIds staffIds
     * @return key-staffId, value-MoeVan
     */
    public Map<Integer, MoeVan> getVanMapByStaffIds(Long companyId, List<Integer> staffIds) {
        if (CollectionUtils.isEmpty(staffIds)) {
            return Map.of();
        }
        MoeVanStaffExample vanStaffExample = new MoeVanStaffExample();
        vanStaffExample
                .createCriteria()
                .andCompanyIdEqualTo(companyId) // todo 待加 companyId + staffId 的索引
                .andStaffIdIn(staffIds)
                .andStatusEqualTo(VanStaffStatus.NORMAL.getValue());
        List<MoeVanStaff> vanStaffRelations = moeVanStaffMapper.selectByExample(vanStaffExample);
        if (vanStaffRelations.isEmpty()) {
            return Map.of();
        }
        List<Integer> vanIds =
                vanStaffRelations.stream().map(MoeVanStaff::getVanId).toList();

        MoeVanExample vanExample = new MoeVanExample();
        vanExample.createCriteria().andIdIn(vanIds).andIsDeletedEqualTo(VanStatus.NORMAL.getValue());
        Map<Integer, MoeVan> vanMap = moeVanMapper.selectByExampleWithBLOBs(vanExample).stream()
                .collect(Collectors.toMap(MoeVan::getId, Function.identity()));

        return vanStaffRelations.stream()
                .filter(relation -> vanMap.containsKey(relation.getVanId()))
                .collect(Collectors.toMap(
                        MoeVanStaff::getStaffId, relation -> vanMap.get(relation.getVanId()), (v1, v2) -> v1));
    }

    public void unbindStaffVanRelation(Long staffId, List<Long> businessIds) {
        if (CollectionUtils.isEmpty(businessIds)) {
            return;
        }

        MoeVanStaffExample vanStaffExample = new MoeVanStaffExample();
        vanStaffExample
                .createCriteria()
                .andBusinessIdIn(businessIds.stream().map(Long::intValue).collect(Collectors.toList()))
                .andStaffIdEqualTo(staffId.intValue())
                .andStatusEqualTo(VanStaffStatus.NORMAL.getValue());
        List<MoeVanStaff> vanStaffRelations = moeVanStaffMapper.selectByExample(vanStaffExample);
        if (vanStaffRelations.isEmpty()) {
            return;
        }

        MoeVanStaffExample updateExample = new MoeVanStaffExample();
        updateExample
                .createCriteria()
                .andIdIn(vanStaffRelations.stream().map(MoeVanStaff::getId).toList());

        Long nowTime = DateUtil.get10Timestamp();
        MoeVanStaff updateRecord = MoeVanStaff.builder()
                .status(VanStaffStatus.HISTORY.getValue())
                .endTime(nowTime)
                .updateTime(nowTime)
                .build();
        moeVanStaffMapper.updateByExampleSelective(updateRecord, updateExample);
    }

    public Map<Long, List<MoeVan>> getVanListByMultiCompanyId(List<Long> companyIdList) {
        if (CollectionUtils.isEmpty(companyIdList)) {
            return Map.of();
        }
        List<MoeVan> vanList = moeVanMapper.selectAllVanByCompanyIdList(companyIdList);
        // 将 van list 转化为 Map，key 是 companyId,value
        return vanList.stream().collect(Collectors.groupingBy(MoeVan::getCompanyId));
    }

    public Map<Long, Long> getAssignedVanForStaffId(List<Long> staffIdList) {
        if (CollectionUtils.isEmpty(staffIdList)) {
            return Map.of();
        }
        List<MoeVanStaff> vanStaffList = moeVanStaffMapper.selectByStaffIdsList(staffIdList);
        return vanStaffList.stream()
                .collect(Collectors.toMap(
                        staffVan -> (long) staffVan.getStaffId(),
                        staffVan -> (long) staffVan.getVanId(),
                        (existingValue, newValue) -> existingValue));
    }

    public void forceAssignStaffToVan(Long staffId, Long vanId) {
        // force remove
        var staff = moeStaffMapper.selectByPrimaryKey(staffId.intValue());
        var vanStaffRecord = MoeVanStaff.builder()
                .status(VanStaffStatus.HISTORY.getValue())
                .updateTime(CommonUtil.get10Timestamp())
                .build();
        MoeVanStaffExample updateExample = new MoeVanStaffExample();
        updateExample
                .createCriteria()
                .andCompanyIdEqualTo(staff.getCompanyId().longValue())
                .andStaffIdEqualTo(staffId.intValue())
                .andStatusEqualTo(VanStaffStatus.NORMAL.getValue());
        moeVanStaffMapper.updateByExampleSelective(vanStaffRecord, updateExample);
        if (vanId == null || vanId == 0) {
            return;
        }
        // insert
        var van = moeVanMapper.selectByPrimaryKey(vanId.intValue());
        long nowTime = CommonUtil.get10Timestamp();
        moeVanStaffMapper.insertSelective(MoeVanStaff.builder()
                .businessId(van.getBusinessId())
                .vanId(vanId.intValue())
                .staffId(staffId.intValue())
                .status(VanStaffStatus.NORMAL.getValue())
                .startTime(nowTime)
                .createTime(nowTime)
                .updateTime(nowTime)
                .companyId(staff.getCompanyId().longValue())
                .build());
    }
}
