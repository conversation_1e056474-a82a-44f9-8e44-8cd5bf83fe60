package com.moego.svc.organization.service;

import com.moego.idl.models.organization.v1.StaffAccessByLocationDef;
import com.moego.idl.models.organization.v1.StaffAccessListDef;
import com.moego.idl.models.organization.v1.StaffShowOnCalendarDef;
import com.moego.lib.common.util.JsonUtil;
import com.moego.svc.organization.dto.staff.StaffAccessDTO;
import com.moego.svc.organization.entity.MoeStaffAccess;
import com.moego.svc.organization.entity.MoeStaffAccessExample;
import com.moego.svc.organization.mapper.MoeStaffAccessMapper;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

@Service
@RequiredArgsConstructor
public class StaffAccessService {

    private final MoeStaffAccessMapper staffAccessMapper;
    private final BusinessService businessService;

    /**
     * 返回 staffId 在各个 location 可访问的 staff 列表，不包含自己
     */
    public List<StaffAccessDTO> getAccessStaffIds(
            Long staffId, List<Long> businessIds, Map<Long, List<Long>> locationStaffIdsMap) {
        List<MoeStaffAccess> staffAccessList = getStaffAccessList(staffId, businessIds);
        if (CollectionUtils.isEmpty(staffAccessList)) {
            return List.of();
        }
        return staffAccessList.stream()
                .map(staffAccess -> new StaffAccessDTO(
                        staffAccess.getLocationId(),
                        getAccessStaffIds(staffAccess, locationStaffIdsMap),
                        staffAccess.getAccessLocationAllStaffs()))
                .toList();
    }

    /**
     * 返回 staffId 在各个 location 可访问的 staff 列表映射，不包含自己
     */
    public Map<Long, List<Long>> getAccessStaffIdsMap(
            Long staffId, List<Long> businessIds, Map<Long, List<Long>> locationStaffIdsMap) {
        var locationToStaffIds = getStaffAccessList(staffId, businessIds).stream()
                .collect(Collectors.toMap(
                        MoeStaffAccess::getLocationId,
                        staffAccess -> getAccessStaffIds(staffAccess, locationStaffIdsMap)));
        // 按 location 返回，避免有些 location 下没初始化记录，没返回数据
        return businessIds.stream()
                .collect(Collectors.toMap(bid -> bid, bid -> locationToStaffIds.getOrDefault(bid, new ArrayList<>())));
    }

    /**
     *
     * @param staffId staffId
     * @return 当前staffId要展示的location列表
     */
    public List<Long> getAccessStaffLocations(Long staffId) {
        return staffAccessMapper.selectAccessStaffShownLocations(staffId);
    }

    private List<Long> getAccessStaffIds(MoeStaffAccess staffAccess, Map<Long, List<Long>> locationStaffIdsMap) {
        List<Long> accessStaffIds;
        if (staffAccess.getAccessLocationAllStaffs()) {
            accessStaffIds = locationStaffIdsMap.getOrDefault(staffAccess.getLocationId(), new ArrayList<>());
        } else {
            // 过滤删除的 staff
            accessStaffIds = JsonUtil.toList(staffAccess.getAccessStaffIds(), Long.class);
            accessStaffIds.retainAll(locationStaffIdsMap.getOrDefault(staffAccess.getLocationId(), new ArrayList<>()));
        }
        // access 列表移除自己
        accessStaffIds.remove(staffAccess.getStaffId());
        return accessStaffIds;
    }

    @Transactional
    public void saveAccessStaff(
            Long staffId,
            Long companyId,
            Long tokenStaffId,
            StaffAccessListDef accessList,
            StaffShowOnCalendarDef staffLocationList) {
        List<StaffAccessByLocationDef> accessStaffIdsByLocation = accessList.getStaffIdsByLocationList();
        List<Long> staffShownLocationList = staffLocationList.getLocationIdsList();
        if (CollectionUtils.isEmpty(accessStaffIdsByLocation) && CollectionUtils.isEmpty(staffShownLocationList)) {
            return;
        }
        // 查询company下的所有location，不在的认为是非法输入，过滤掉
        List<Long> LocationIdsUnderCompany = businessService.getLocationIdListByCompanyId(companyId);
        List<Long> realLocationList = staffShownLocationList.stream()
                .distinct()
                .filter(LocationIdsUnderCompany::contains)
                .toList();

        // 查新表记录
        Map<Long, MoeStaffAccess> staffAccessMap = getStaffAccessList(staffId, null).stream()
                .collect(Collectors.toMap(MoeStaffAccess::getLocationId, Function.identity()));
        Map<Long, MoeStaffAccess> updateRecordsMap = new HashMap<>();
        Map<Long, MoeStaffAccess> newRecordsMap = new HashMap<>();
        for (StaffAccessByLocationDef staffAccess : accessList.getStaffIdsByLocationList()) {
            Long locationId = staffAccess.getLocationId();
            if (staffAccessMap.containsKey(locationId)) {
                MoeStaffAccess updateRecord = staffAccessMap.get(locationId);
                updateRecord.setAccessLocationAllStaffs(staffAccess.getAccessLocationAllStaffs());
                updateRecord.setAccessStaffIds(JsonUtil.toJson(staffAccess.getStaffIdsList()));
                updateRecord.setUpdatedBy(tokenStaffId);
                updateRecordsMap.put(locationId, updateRecord);
            } else {
                MoeStaffAccess newRecord = MoeStaffAccess.builder()
                        .staffId(staffId)
                        .companyId(companyId)
                        .locationId(staffAccess.getLocationId())
                        .accessLocationAllStaffs(staffAccess.getAccessLocationAllStaffs())
                        .accessStaffIds(JsonUtil.toJson(staffAccess.getStaffIdsList()))
                        .updatedBy(tokenStaffId)
                        .build();
                newRecordsMap.put(locationId, newRecord);
            }
        }
        /**
         * 遍历要更新所有的location，判断是否在newRecordsMap、updateRecordsMap或者staffAccessMap中， 如果没有记录则插入一条，有的话更新
         * 要把db中的记录中的staff_id 下的记录都更新
         * eg 一开始传入的locationId 为 1,2,3,4,5
         *      这个时候数据库中有 1,2,3,4,5
         *    下次传入的locationId 为 1,2,3,4
         *      则遍历 1,2,3,4，会少掉5，所以要遍历这个map,把前端传进来的id设置为true，记录中其他的设置为false
         */

        // staffAccessMap 表示数据库中已有的记录
        staffAccessMap.forEach((locationId, value) -> {
            if (updateRecordsMap.containsKey(locationId)) {
                /**
                 * case 1 编辑了StaffAccessByLocationDef中的access_staff_ids,还没更新记录中是否展示的状态
                 */
                MoeStaffAccess updateRecord = updateRecordsMap.get(locationId);
                updateRecord.setIsShownOnCalendar(realLocationList.contains(locationId));
            } else if (newRecordsMap.containsKey(locationId)) {
                /**
                 * case 2 新增了StaffAccessByLocationDef中的access_staff_ids,还没更新记录中是否展示calendar的状态
                 */
                MoeStaffAccess newRecord = newRecordsMap.get(locationId);
                newRecord.setIsShownOnCalendar(realLocationList.contains(locationId));
            } else {
                /**
                 * case 3   在 staffAccessMap 中有记录，但是updateRecordsMap和newRecordsMap没有，
                 *          说明是单纯修改location是否展示记录，不涉及access_staff_ids之类的修改，往updateMap新增一条记录
                 */
                MoeStaffAccess updateRecord = staffAccessMap.get(locationId);
                updateRecord.setIsShownOnCalendar(realLocationList.contains(locationId));
                updateRecord.setUpdatedBy(tokenStaffId);
                updateRecordsMap.put(locationId, updateRecord);
            }
        });

        // 前端传入的locationIds,在数据库中没有的，且在insertMap中没有的，说明是新插入的一条记录
        /**
         * 还有的情况是，前端传入的locationIds，staffAccessMap记录中没有，此时发生在create staff 的时候
         */
        for (Long locationId : realLocationList) {
            /**
             * case 1 :没有在数据库中，但是在待更新的map中，同时也在前端传入的列表中，修改状态位为true，这种case出现在create staff 的时候，勾选上了access staff calendar，此时还没插入到数据库中的时候
             */
            if (newRecordsMap.containsKey(locationId)) {
                MoeStaffAccess newRecord = newRecordsMap.get(locationId);
                newRecord.setIsShownOnCalendar(true);
                newRecordsMap.put(locationId, newRecord);
            }
            /**
             * case 2 :数据库中中没有这个记录，同时待插入的map中也没有这个记录，手动插入一条记录
             */
            if (!staffAccessMap.containsKey(locationId) && !newRecordsMap.containsKey(locationId)) {
                MoeStaffAccess newRecord = MoeStaffAccess.builder()
                        .staffId(staffId)
                        .companyId(companyId)
                        .locationId(locationId)
                        .isShownOnCalendar(true)
                        .updatedBy(tokenStaffId)
                        .build();
                newRecordsMap.put(locationId, newRecord);
            }
        }

        if (!CollectionUtils.isEmpty(newRecordsMap)) {
            staffAccessMapper.batchInsertSelective(
                    newRecordsMap.values().stream().toList());
        }
        if (!CollectionUtils.isEmpty(updateRecordsMap)) {
            staffAccessMapper.batchUpdateByPrimaryKeySelective(
                    updateRecordsMap.values().stream().toList());
        }
    }

    public MoeStaffAccess getStaffAccess(Long staffId, Long locationId) {
        MoeStaffAccessExample example = new MoeStaffAccessExample();
        example.createCriteria().andStaffIdEqualTo(staffId).andLocationIdEqualTo(locationId);
        List<MoeStaffAccess> staffAccessList = staffAccessMapper.selectByExampleWithBLOBs(example);
        if (CollectionUtils.isEmpty(staffAccessList)) {
            return null;
        }
        return staffAccessList.get(0);
    }

    public List<MoeStaffAccess> getStaffAccessList(Long staffId, List<Long> businessIds) {
        MoeStaffAccessExample example = new MoeStaffAccessExample();
        example.createCriteria().andStaffIdEqualTo(staffId);
        if (!CollectionUtils.isEmpty(businessIds)) {
            example.getOredCriteria().get(0).andLocationIdIn(businessIds);
        }
        return staffAccessMapper.selectByExampleWithBLOBs(example);
    }

    @Transactional
    public void migrateStaffAccess(Integer fromStaffId, Integer toId) {
        MoeStaffAccessExample fromExample = new MoeStaffAccessExample();
        fromExample.createCriteria().andStaffIdEqualTo(fromStaffId.longValue());
        List<MoeStaffAccess> fromStaffAccessList = staffAccessMapper.selectByExampleWithBLOBs(fromExample);
        if (CollectionUtils.isEmpty(fromStaffAccessList)) {
            return;
        }
        MoeStaffAccessExample toExample = new MoeStaffAccessExample();
        toExample.createCriteria().andStaffIdEqualTo(toId.longValue());
        List<MoeStaffAccess> toStaffAccessList = staffAccessMapper.selectByExampleWithBLOBs(toExample);
        Map<Long, MoeStaffAccess> toStaffAccessMap = toStaffAccessList.stream()
                .collect(Collectors.toMap(MoeStaffAccess::getLocationId, Function.identity()));

        List<MoeStaffAccess> saveRecords = new ArrayList<>();
        for (MoeStaffAccess staffAccess : fromStaffAccessList) {
            if (toStaffAccessMap.containsKey(staffAccess.getLocationId())) {
                // 合并两个
                MoeStaffAccess toStaffAccess = toStaffAccessMap.get(staffAccess.getLocationId());
                boolean accessLocationAllStaff =
                        toStaffAccess.getAccessLocationAllStaffs() || staffAccess.getAccessLocationAllStaffs();
                List<Long> accessStaffIds = Stream.of(
                                JsonUtil.toList(toStaffAccess.getAccessStaffIds(), Long.class),
                                JsonUtil.toList(staffAccess.getAccessStaffIds(), Long.class))
                        .flatMap(Collection::stream)
                        .distinct()
                        .toList();
                toStaffAccess.setAccessLocationAllStaffs(accessLocationAllStaff);
                toStaffAccess.setAccessStaffIds(JsonUtil.toJson(accessStaffIds));
                saveRecords.add(toStaffAccess);
            } else {
                staffAccess.setStaffId(toId.longValue());
                saveRecords.add(staffAccess);
            }
        }
        batchSaveRecordsForMigrate(saveRecords);
        // access staff list 的替换
        List<MoeStaffAccess> staffAccessList = staffAccessMapper.selectAccessStaffIdContains(fromStaffId.longValue());
        if (CollectionUtils.isEmpty(staffAccessList)) {
            return;
        }
        List<MoeStaffAccess> updateRecords = new ArrayList<>();
        for (MoeStaffAccess staffAccess : staffAccessList) {
            List<Long> accessStaffIds = JsonUtil.toList(staffAccess.getAccessStaffIds(), Long.class);
            if (!accessStaffIds.contains(fromStaffId.longValue())) {
                continue;
            }
            accessStaffIds.remove(fromStaffId.longValue());
            if (!accessStaffIds.contains(toId.longValue()) && staffAccess.getStaffId() != toId.longValue()) {
                accessStaffIds.add(toId.longValue());
            }
            MoeStaffAccess updateRecord = MoeStaffAccess.builder()
                    .id(staffAccess.getId())
                    .accessStaffIds(JsonUtil.toJson(accessStaffIds))
                    .build();
            updateRecords.add(updateRecord);
        }
        if (!CollectionUtils.isEmpty(updateRecords)) {
            staffAccessMapper.batchUpdateByPrimaryKeySelective(updateRecords);
        }
    }

    public void updateStaffAccessAllForMigrate(Integer staffId, Integer locationId, Integer companyId) {
        MoeStaffAccess staffAccess = getStaffAccess(staffId.longValue(), locationId.longValue());
        if (staffAccess == null) {
            staffAccess = MoeStaffAccess.builder()
                    .staffId(staffId.longValue())
                    .companyId(companyId.longValue())
                    .locationId(locationId.longValue())
                    .accessLocationAllStaffs(true)
                    .build();
            staffAccessMapper.insertSelective(staffAccess);
        } else {
            MoeStaffAccess updateRecord = MoeStaffAccess.builder()
                    .id(staffAccess.getId())
                    .accessLocationAllStaffs(true)
                    .build();
            staffAccessMapper.updateByPrimaryKeySelective(updateRecord);
        }
    }

    @Transactional
    public void batchSaveRecordsForMigrate(List<MoeStaffAccess> staffAccessList) {
        List<MoeStaffAccess> newRecords = new ArrayList<>();
        List<MoeStaffAccess> updateRecords = new ArrayList<>();
        for (MoeStaffAccess staffAccess : staffAccessList) {
            if (staffAccess.getId() == null) {
                newRecords.add(staffAccess);
            } else {
                updateRecords.add(staffAccess);
            }
        }
        if (!CollectionUtils.isEmpty(newRecords)) {
            staffAccessMapper.batchInsertSelective(newRecords);
        }
        if (!CollectionUtils.isEmpty(updateRecords)) {
            staffAccessMapper.batchUpdateByPrimaryKeySelective(updateRecords);
        }
    }
}
