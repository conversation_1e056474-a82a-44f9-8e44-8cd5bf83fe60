package com.moego.svc.business.customer.service;

import com.moego.idl.models.business_customer.v1.BusinessPetMedicalInfoUpdateDef;
import com.moego.idl.models.organization.v1.Tenant;
import com.moego.svc.business.customer.converter.PetMedicalInfoConverter;
import com.moego.svc.business.customer.repo.PetRepo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class PetMedicalInfoService {

    private final PetMedicalInfoConverter petMedicalInfoConverter;

    private final PetRepo petRepo;

    public void updatePetMedicalInfo(Tenant tenant, long petId, BusinessPetMedicalInfoUpdateDef def) {
        var param = petMedicalInfoConverter.toUpdateParam(tenant, petId, def);
        petRepo.updatePetMedicalInfo(param);
    }
}
