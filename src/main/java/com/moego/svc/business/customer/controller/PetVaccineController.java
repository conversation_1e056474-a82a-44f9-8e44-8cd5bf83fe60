package com.moego.svc.business.customer.controller;

import com.moego.idl.service.business_customer.v1.BusinessPetVaccineServiceGrpc;
import com.moego.idl.service.business_customer.v1.CreatePetVaccineRequest;
import com.moego.idl.service.business_customer.v1.CreatePetVaccineResponse;
import com.moego.idl.service.business_customer.v1.DeletePetVaccineRequest;
import com.moego.idl.service.business_customer.v1.DeletePetVaccineResponse;
import com.moego.idl.service.business_customer.v1.GetPetVaccineRequest;
import com.moego.idl.service.business_customer.v1.GetPetVaccineResponse;
import com.moego.idl.service.business_customer.v1.ListPetVaccineRequest;
import com.moego.idl.service.business_customer.v1.ListPetVaccineResponse;
import com.moego.idl.service.business_customer.v1.ListPetVaccineTemplateRequest;
import com.moego.idl.service.business_customer.v1.ListPetVaccineTemplateResponse;
import com.moego.idl.service.business_customer.v1.SortPetVaccineRequest;
import com.moego.idl.service.business_customer.v1.SortPetVaccineResponse;
import com.moego.idl.service.business_customer.v1.UpdatePetVaccineRequest;
import com.moego.idl.service.business_customer.v1.UpdatePetVaccineResponse;
import com.moego.lib.common.grpc.server.GrpcService;
import com.moego.svc.business.customer.converter.PetVaccineConverter;
import com.moego.svc.business.customer.service.PetVaccineService;
import com.moego.svc.business.customer.utils.TenantUtils;
import io.grpc.stub.StreamObserver;
import lombok.RequiredArgsConstructor;

@GrpcService
@RequiredArgsConstructor
public class PetVaccineController extends BusinessPetVaccineServiceGrpc.BusinessPetVaccineServiceImplBase {

    private final PetVaccineConverter petVaccineConverter;

    private final PetVaccineService petVaccineService;

    @Override
    public void getPetVaccine(GetPetVaccineRequest request, StreamObserver<GetPetVaccineResponse> responseObserver) {

        var id = request.getId();
        var tenant = TenantUtils.of(request.getCompanyId());

        var vaccine = petVaccineService.getPetVaccine(tenant, id);

        var vaccineModel = petVaccineConverter.toModel(vaccine);
        var response =
                GetPetVaccineResponse.newBuilder().setVaccine(vaccineModel).build();

        responseObserver.onNext(response);
        responseObserver.onCompleted();
    }

    @Override
    public void listPetVaccineTemplate(
            ListPetVaccineTemplateRequest request, StreamObserver<ListPetVaccineTemplateResponse> responseObserver) {

        var vaccines = petVaccineService.listPetVaccine(TenantUtils.PLATFORM);

        var vaccineModels = petVaccineConverter.toModels(vaccines);
        var response = ListPetVaccineTemplateResponse.newBuilder()
                .addAllVaccines(vaccineModels)
                .build();

        responseObserver.onNext(response);
        responseObserver.onCompleted();
    }

    @Override
    public void listPetVaccine(ListPetVaccineRequest request, StreamObserver<ListPetVaccineResponse> responseObserver) {

        var tenant = TenantUtils.of(request.getCompanyId());

        var vaccines = petVaccineService.listPetVaccine(tenant);

        var vaccineModels = petVaccineConverter.toModels(vaccines);
        var response = ListPetVaccineResponse.newBuilder()
                .addAllVaccines(vaccineModels)
                .build();

        responseObserver.onNext(response);
        responseObserver.onCompleted();
    }

    @Override
    public void createPetVaccine(
            CreatePetVaccineRequest request, StreamObserver<CreatePetVaccineResponse> responseObserver) {

        var vaccine = petVaccineConverter.createRecord(request);
        vaccine = petVaccineService.createPetVaccine(vaccine);

        var vaccineModel = petVaccineConverter.toModel(vaccine);
        var response =
                CreatePetVaccineResponse.newBuilder().setVaccine(vaccineModel).build();

        responseObserver.onNext(response);
        responseObserver.onCompleted();
    }

    @Override
    public void updatePetVaccine(
            UpdatePetVaccineRequest request, StreamObserver<UpdatePetVaccineResponse> responseObserver) {

        var id = request.getId();
        var tenant = TenantUtils.of(request.getCompanyId());

        var vaccine = petVaccineService.getPetVaccine(tenant, id);
        petVaccineConverter.updateRecord(request, vaccine);
        petVaccineService.updatePetVaccine(vaccine);

        responseObserver.onNext(UpdatePetVaccineResponse.getDefaultInstance());
        responseObserver.onCompleted();
    }

    @Override
    public void sortPetVaccine(SortPetVaccineRequest request, StreamObserver<SortPetVaccineResponse> responseObserver) {

        var ids = request.getIdsList();
        var tenant = TenantUtils.of(request.getCompanyId());

        petVaccineService.sortPetVaccine(tenant, ids);

        responseObserver.onNext(SortPetVaccineResponse.getDefaultInstance());
        responseObserver.onCompleted();
    }

    @Override
    public void deletePetVaccine(
            DeletePetVaccineRequest request, StreamObserver<DeletePetVaccineResponse> responseObserver) {

        var tenant = TenantUtils.of(request.getCompanyId());

        petVaccineService.deletePetVaccine(tenant, request.getId());

        responseObserver.onNext(DeletePetVaccineResponse.getDefaultInstance());
        responseObserver.onCompleted();
    }
}
