package com.moego.svc.business.customer.params;

import com.moego.idl.models.organization.v1.Tenant;
import com.moego.svc.business.customer.enums.CustomerSource;
import java.time.LocalDateTime;
import java.util.List;
import lombok.Data;

@Data
public class CustomerCreateParam {
    private Tenant tenant;

    private Long preferredBusinessId;
    private Long accountId;

    private CustomerSource source;
    private String phoneNumber;
    private String email;

    private String firstName;
    private String lastName;
    private String avatarPath;
    private String clientColor;

    private Long referralSourceId;
    private List<Long> tagIds;

    private String externalId;
    private Long createdAt;
    private Long updatedAt;
    private Long createdBy;
    private Long updatedBy;
    private LocalDateTime birthday;
    private String e164PhoneNumber;
    private String referralSourceDesc;
}
