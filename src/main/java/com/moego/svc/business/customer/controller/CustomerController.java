package com.moego.svc.business.customer.controller;

import static com.moego.idl.service.business_customer.v1.BusinessCustomerServiceGrpc.BusinessCustomerServiceImplBase;

import com.moego.idl.models.business_customer.v1.BusinessCustomerAppointmentPreferenceUpdateDef;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.organization.v1.Tenant;
import com.moego.idl.service.business_customer.v1.BatchGetCustomerInfoRequest;
import com.moego.idl.service.business_customer.v1.BatchGetCustomerInfoResponse;
import com.moego.idl.service.business_customer.v1.BatchGetCustomerRequest;
import com.moego.idl.service.business_customer.v1.BatchGetCustomerResponse;
import com.moego.idl.service.business_customer.v1.CheckIdentifierRequest;
import com.moego.idl.service.business_customer.v1.CheckIdentifierResponse;
import com.moego.idl.service.business_customer.v1.CreateCustomerWithAdditionalInfoRequest;
import com.moego.idl.service.business_customer.v1.CreateCustomerWithAdditionalInfoResponse;
import com.moego.idl.service.business_customer.v1.GetCustomerInfoRequest;
import com.moego.idl.service.business_customer.v1.GetCustomerInfoResponse;
import com.moego.idl.service.business_customer.v1.GetCustomerRequest;
import com.moego.idl.service.business_customer.v1.GetCustomerResponse;
import com.moego.idl.service.business_customer.v1.ListCustomersRequest;
import com.moego.idl.service.business_customer.v1.ListCustomersResponse;
import com.moego.idl.service.business_customer.v1.UpdateCustomerPreferredBusinessRequest;
import com.moego.idl.service.business_customer.v1.UpdateCustomerPreferredBusinessResponse;
import com.moego.idl.service.business_customer.v1.UpdateCustomerRequest;
import com.moego.idl.service.business_customer.v1.UpdateCustomerResponse;
import com.moego.idl.utils.v2.PaginationResponse;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.lib.common.grpc.server.GrpcService;
import com.moego.lib.common.thread.ThreadPool;
import com.moego.svc.business.customer.converter.CustomerAddressConverter;
import com.moego.svc.business.customer.converter.CustomerConverter;
import com.moego.svc.business.customer.enums.DefaultPreferredFrequencyType;
import com.moego.svc.business.customer.enums.option.GetOption;
import com.moego.svc.business.customer.event.producer.CustomerEventProducer;
import com.moego.svc.business.customer.repository.jooq.tables.records.MoeBusinessCustomerRecord;
import com.moego.svc.business.customer.service.CustomerAddressService;
import com.moego.svc.business.customer.service.CustomerContactService;
import com.moego.svc.business.customer.service.CustomerNoteService;
import com.moego.svc.business.customer.service.CustomerPreferenceService;
import com.moego.svc.business.customer.service.CustomerService;
import com.moego.svc.business.customer.service.CustomerServiceV2;
import com.moego.svc.business.customer.service.DefaultPreferredFrequencyService;
import com.moego.svc.business.customer.service.PetMedicalInfoService;
import com.moego.svc.business.customer.service.PetNoteService;
import com.moego.svc.business.customer.service.PetPreferenceService;
import com.moego.svc.business.customer.service.PetService;
import com.moego.svc.business.customer.service.PetVaccineRecordService;
import com.moego.svc.business.customer.validator.RequestValidator;
import io.grpc.stub.StreamObserver;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.transaction.support.TransactionOperations;
import org.springframework.util.CollectionUtils;

@GrpcService
@RequiredArgsConstructor
public class CustomerController extends BusinessCustomerServiceImplBase {

    private final CustomerConverter customerConverter;
    private final CustomerAddressConverter customerAddressConverter;

    private final CustomerService customerService;
    private final CustomerServiceV2 customerServiceV2;
    private final CustomerContactService customerContactService;
    private final CustomerAddressService customerAddressService;
    private final CustomerPreferenceService customerPreferenceService;
    private final CustomerNoteService customerNoteService;
    private final PetService petService;
    private final PetPreferenceService petPreferenceService;
    private final PetMedicalInfoService petMedicalInfoService;
    private final PetVaccineRecordService petVaccineRecordService;
    private final PetNoteService petNoteService;
    private final DefaultPreferredFrequencyService defaultPreferredFrequencyService;
    private final CustomerEventProducer customerEventProducer;

    private final TransactionOperations transactionOperations;

    @Override
    public void getCustomer(GetCustomerRequest request, StreamObserver<GetCustomerResponse> responseObserver) {

        var tenant = request.hasTenant() ? request.getTenant() : null;
        // 兼容旧版本传入 companyId 的方式，后续应当移除 companyId 字段，统一用 tenant (TODO)
        if (tenant == null && request.getCompanyId() > 0) {
            tenant = Tenant.newBuilder().setCompanyId(request.getCompanyId()).build();
        }

        var customer =
                switch (request.getIdentifierCase()) {
                    case ID -> customerService.getCustomerById(tenant, request.getId(), true);
                    case CUSTOMER_CODE -> customerService.getCustomerByCustomerCode(tenant, request.getCustomerCode());
                    case EMAIL -> customerService.getCustomerByEmail(tenant, request.getEmail());
                    case PHONE_NUMBER -> customerService.getCustomerByPhoneNumber(tenant, request.getPhoneNumber());
                    default -> throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR);
                };

        var contact = customerContactService.getCustomerMainContact(null, customer.getId());

        var customerModel = customerConverter.toModel(customer, contact);
        var response =
                GetCustomerResponse.newBuilder().setCustomer(customerModel).build();

        responseObserver.onNext(response);
        responseObserver.onCompleted();
    }

    @Override
    public void getCustomerInfo(
            GetCustomerInfoRequest request, StreamObserver<GetCustomerInfoResponse> responseObserver) {

        var tenant = request.hasTenant() ? request.getTenant() : null;
        var id = request.getId();

        var customer =
                customerServiceV2.getCustomer(tenant, id, GetOption.INCLUDE_DELETED, GetOption.INCLUDE_CUSTOMER_TAG);
        var model = customerConverter.toInfoModel(customer);

        var response = GetCustomerInfoResponse.newBuilder().setCustomer(model).build();
        responseObserver.onNext(response);
        responseObserver.onCompleted();
    }

    @Override
    public void batchGetCustomer(
            BatchGetCustomerRequest request, StreamObserver<BatchGetCustomerResponse> responseObserver) {

        var tenant = request.hasTenant() ? request.getTenant() : null;
        // 兼容旧版本传入 companyId 的方式，后续应当移除 companyId 字段，统一用 tenant (TODO)
        if (tenant == null && request.getCompanyId() > 0) {
            tenant = Tenant.newBuilder().setCompanyId(request.getCompanyId()).build();
        }

        var customers = customerService.batchGetCustomerByIds(tenant, request.getIdsList());
        var contacts = customerContactService.batchGetCustomerMainContact(
                null, customers.stream().map(MoeBusinessCustomerRecord::getId).collect(Collectors.toSet()));

        var responseBuilder = BatchGetCustomerResponse.newBuilder();
        customers.forEach(customer -> {
            var contact = contacts.get(customer.getId());
            var customerModel = customerConverter.toModel(customer, contact);
            responseBuilder.addCustomers(customerModel);
        });

        responseObserver.onNext(responseBuilder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void batchGetCustomerInfo(
            BatchGetCustomerInfoRequest request, StreamObserver<BatchGetCustomerInfoResponse> responseObserver) {

        var tenant = request.hasTenant() ? request.getTenant() : null;
        var ids = request.getIdsList();

        var customers =
                customerServiceV2
                        .batchGetCustomer(tenant, ids, GetOption.INCLUDE_DELETED, GetOption.INCLUDE_CUSTOMER_TAG)
                        .stream()
                        .map(customerConverter::toInfoModel)
                        .toList();

        var response = BatchGetCustomerInfoResponse.newBuilder()
                .addAllCustomers(customers)
                .build();

        responseObserver.onNext(response);
        responseObserver.onCompleted();
    }

    @Override
    public void updateCustomerPreferredBusiness(
            UpdateCustomerPreferredBusinessRequest request,
            StreamObserver<UpdateCustomerPreferredBusinessResponse> responseObserver) {

        var tenant = request.hasTenant() ? request.getTenant() : null;
        // 兼容旧版本传入 companyId 的方式，后续应当移除 companyId 字段，统一用 tenant (TODO)
        if (tenant == null && request.getCompanyId() > 0) {
            tenant = Tenant.newBuilder().setCompanyId(request.getCompanyId()).build();
        }

        var customer = customerService.getCustomerById(tenant, request.getCustomerId(), false);

        if (request.getPreferredBusinessId() != customer.getBusinessId().longValue()) {
            customerConverter.updateRecord(request, customer);
            customerService.updatePreferredBusiness(customer);
        }

        responseObserver.onNext(UpdateCustomerPreferredBusinessResponse.getDefaultInstance());
        responseObserver.onCompleted();
    }

    @Override
    public void listCustomers(ListCustomersRequest request, StreamObserver<ListCustomersResponse> responseObserver) {
        var customers =
                customerService.listCustomers(request.getTenant(), request.getPagination(), request.getFilter());
        var total = customerService.countCustomers(request.getTenant(), request.getFilter());
        var contacts = customerContactService.batchGetCustomerMainContact(
                request.getTenant(),
                customers.stream().map(MoeBusinessCustomerRecord::getId).collect(Collectors.toSet()));
        responseObserver.onNext(ListCustomersResponse.newBuilder()
                .addAllCustomers(customers.stream()
                        .map(c -> {
                            var contact = contacts.get(c.getId());
                            return customerConverter.toModel(c, contact);
                        })
                        .toList())
                .setPagination(PaginationResponse.newBuilder()
                        .setPageNum(
                                request.getPagination().getPageNum() > 0
                                        ? request.getPagination().getPageNum()
                                        : 1)
                        .setPageSize(request.getPagination().getPageSize())
                        .setTotal(total)
                        .build())
                .build());
        responseObserver.onCompleted();
    }

    @Override
    public void createCustomerWithAdditionalInfo(
            CreateCustomerWithAdditionalInfoRequest request,
            StreamObserver<CreateCustomerWithAdditionalInfoResponse> responseObserver) {

        RequestValidator.validateRequest(request);

        var tenant = request.getTenant();
        var createdBy = request.hasCreatedBy() ? request.getCreatedBy() : null;
        var customerWithAdditionalInfo = request.getCustomerWithAdditionalInfo();
        var petsWithAdditionalInfo = request.getPetsWithAdditionalInfoList();

        var id = transactionOperations.execute((status) -> {
            // create customer
            var customerId =
                    customerServiceV2.createCustomer(tenant, createdBy, customerWithAdditionalInfo.getCustomer());

            // update communication preference
            if (customerWithAdditionalInfo.hasCommunicationPreference()) {
                customerPreferenceService.updateCommunicationPreference(
                        tenant, customerId, customerWithAdditionalInfo.getCommunicationPreference());
            }

            // update appointment preference
            var appointmentPreference = customerWithAdditionalInfo.hasAppointmentPreference()
                    ? customerWithAdditionalInfo.getAppointmentPreference()
                    : BusinessCustomerAppointmentPreferenceUpdateDef.getDefaultInstance();
            // 如果没有设置 grooming frequency，则使用默认值
            if (!appointmentPreference.hasPreferredGroomingFrequency()) {
                var defaultFrequency = defaultPreferredFrequencyService.getDefaultPreferredFrequency(
                        tenant, DefaultPreferredFrequencyType.GROOMING);
                appointmentPreference = appointmentPreference.toBuilder()
                        .setPreferredGroomingFrequency(defaultFrequency)
                        .build();
            }
            customerPreferenceService.updateAppointmentPreference(tenant, customerId, appointmentPreference);

            // upsert payment preference
            if (customerWithAdditionalInfo.hasPaymentPreference()) {
                customerPreferenceService.upsertPaymentPreference(
                        tenant, customerId, customerWithAdditionalInfo.getPaymentPreference());
            }

            var customer = customerServiceV2.getCustomer(tenant, customerId);
            var tenantForAddress = Tenant.newBuilder()
                    .setCompanyId(customer.getCompanyId())
                    .setBusinessId(customer.getPreferredBusinessId())
                    .build();

            // add primary address
            if (customerWithAdditionalInfo.hasPrimaryAddress()) {
                var address = customerAddressConverter.createRecord(
                        tenantForAddress, customerId, customerWithAdditionalInfo.getPrimaryAddress());
                customerAddressService.createCustomerAddress(address);
            }

            // add additional addresses
            if (!CollectionUtils.isEmpty(customerWithAdditionalInfo.getAdditionalAddressesList())) {
                customerWithAdditionalInfo
                        .getAdditionalAddressesList()
                        .forEach(def -> customerAddressConverter.createRecord(tenantForAddress, customerId, def));
            }

            // add customer notes
            if (!CollectionUtils.isEmpty(customerWithAdditionalInfo.getNotesList())) {
                customerNoteService.batchCreateCustomerNote(
                        customerId, createdBy, customerWithAdditionalInfo.getNotesList());
            }

            if (!CollectionUtils.isEmpty(petsWithAdditionalInfo)) {
                petsWithAdditionalInfo.forEach(petWithAdditionalInfo -> {
                    // create pet
                    var petId = petService.createPet(customer, petWithAdditionalInfo.getPet());

                    // update pet preference
                    if (petWithAdditionalInfo.hasPreference()) {
                        petPreferenceService.updatePreference(tenant, petId, petWithAdditionalInfo.getPreference());
                    }

                    // update medical info
                    if (petWithAdditionalInfo.hasMedicalInfo()) {
                        petMedicalInfoService.updatePetMedicalInfo(
                                tenant, petId, petWithAdditionalInfo.getMedicalInfo());
                    }

                    // create vaccine records
                    if (!CollectionUtils.isEmpty(petWithAdditionalInfo.getVaccineRecordsList())) {
                        // 新创建的 pet 不会有历史疫苗记录，所以不需要去重
                        petVaccineRecordService.batchCreateVaccineRecord(
                                petId, petWithAdditionalInfo.getVaccineRecordsList(), false);
                    }

                    // create pet notes
                    if (!CollectionUtils.isEmpty(petWithAdditionalInfo.getNotesList())) {
                        petNoteService.batchCreatePetNote(petId, createdBy, petWithAdditionalInfo.getNotesList());
                    }
                });
            }

            return customerId;
        });

        // should not happen
        if (id == null) {
            throw ExceptionUtil.bizException(Code.CODE_SERVER_ERROR);
        }

        // send event
        ThreadPool.execute(() -> customerEventProducer.sendCustomerCreatedEvent(tenant, id));

        var response = CreateCustomerWithAdditionalInfoResponse.newBuilder()
                .setCustomerId(id)
                .build();
        responseObserver.onNext(response);
        responseObserver.onCompleted();
    }

    @Override
    public void updateCustomer(UpdateCustomerRequest request, StreamObserver<UpdateCustomerResponse> responseObserver) {

        var tenant = request.hasTenant() ? request.getTenant() : null;
        var updatedBy = request.hasUpdatedBy() ? request.getUpdatedBy() : null;

        customerServiceV2.updateCustomer(tenant, updatedBy, request.getId(), request.getCustomer());

        responseObserver.onNext(UpdateCustomerResponse.getDefaultInstance());
        responseObserver.onCompleted();
    }

    @Override
    public void checkIdentifier(
            CheckIdentifierRequest request, StreamObserver<CheckIdentifierResponse> responseObserver) {

        var tenant = request.getTenant();
        var customerIds =
                switch (request.getIdentifierCase()) {
                    case PHONE_NUMBER -> customerServiceV2.listCustomerIdsByPhoneNumber(
                            tenant, request.getPhoneNumber());
                    case EMAIL -> customerServiceV2.listCustomerIdsByEmail(tenant, request.getEmail());
                    default -> throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR);
                };

        var exist = !CollectionUtils.isEmpty(customerIds);
        var multiple = exist && customerIds.size() > 1;
        var firstCustomerId = exist ? customerIds.get(0) : 0L;

        var response = CheckIdentifierResponse.newBuilder()
                .setExist(exist)
                .setMultiple(multiple)
                .setCustomerId(firstCustomerId)
                .build();

        responseObserver.onNext(response);
        responseObserver.onCompleted();
    }
}
