package com.moego.svc.business.customer.service;

import static com.moego.idl.models.errors.v1.Code.CODE_PARAMS_ERROR;
import static com.moego.svc.business.customer.repository.jooq.Tables.MOE_PET_HAIR_LENGTH;

import com.moego.idl.models.organization.v1.Tenant;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.svc.business.customer.enums.StatusEnum;
import com.moego.svc.business.customer.repository.jooq.tables.records.MoePetHairLengthRecord;
import com.moego.svc.business.customer.utils.SortUtils;
import com.moego.svc.business.customer.utils.TenantUtils;
import java.time.Instant;
import java.util.List;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jooq.Condition;
import org.jooq.DSLContext;
import org.jooq.OrderField;
import org.jooq.impl.DSL;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Service
@RequiredArgsConstructor
public class PetCoatTypeService {

    private final DSLContext dsl;

    /**
     * Order by `moe_pet_hair_length.sort desc, moe_pet_hair_length.id asc`
     */
    private static final OrderField<?>[] ORDER_FIELDS =
            new OrderField[] {MOE_PET_HAIR_LENGTH.SORT.desc(), MOE_PET_HAIR_LENGTH.ID.asc()};

    public Condition tenantCondition(Tenant tenant) {
        return MOE_PET_HAIR_LENGTH.COMPANY_ID.eq(tenant.getCompanyId());
    }

    /**
     * Get a pet coat type.
     * <br>
     * SQL:
     * ```
     * select {all fields} from moe_pet_hair_length
     * where id = ? and {tenant condition};
     * ```
     *
     * @param id     pet coat type id
     * @param tenant tenant
     * @return pet coat type
     */
    public MoePetHairLengthRecord getPetCoatType(long id, Tenant tenant) {
        var conditions = DSL.and(MOE_PET_HAIR_LENGTH.ID.eq((int) id), tenantCondition(tenant));
        var coatType = dsl.fetchOne(MOE_PET_HAIR_LENGTH, conditions);
        if (coatType == null) {
            // TODO: add new error code
            throw ExceptionUtil.bizException(CODE_PARAMS_ERROR, "Coat type not found");
        }
        return coatType;
    }

    /**
     * List pet coat types by tenant.
     * <br>
     * SQL:
     * ```
     * select {all fields} from moe_pet_hair_length
     * where {tenant condition} and status = 1
     * order by sort desc, id asc;
     * ```
     *
     * @param tenant tenant
     * @return pet coat type list, or empty list if not found
     */
    public List<MoePetHairLengthRecord> listPetCoatType(Tenant tenant) {
        return dsl.selectFrom(MOE_PET_HAIR_LENGTH)
                .where(tenantCondition(tenant))
                .and(MOE_PET_HAIR_LENGTH.STATUS.eq(StatusEnum.NORMAL.getValue()))
                .orderBy(ORDER_FIELDS)
                .fetch();
    }

    /**
     * Create a pet coat type.
     * If the name is already used in the tenant, throw exception.
     * The sort value will be set to the id by default, which means the coat type will be displayed last.
     *
     * @param record pet coat type to create
     * @return created pet coat type
     */
    @Transactional
    public MoePetHairLengthRecord createPetCoatType(MoePetHairLengthRecord record) {
        checkNameUsed(record);
        record.insert();
        record.setSort(record.getId()).update();
        record.refresh();
        return record;
    }

    /**
     * Update a pet coat type.
     * If the name is already used in the tenant, throw exception.
     *
     * @param record pet coat type to update
     */
    public void updatePetCoatType(MoePetHairLengthRecord record) {
        if (!record.changed()) {
            log.warn("pet coat type id {} is not changed", record.getId());
            return;
        }
        if (record.changed(MOE_PET_HAIR_LENGTH.NAME)) {
            checkNameUsed(record);
        }
        record.update();
    }

    /**
     * Sort pet coat types according to sortedIds.
     * The pet coat types will be sorted according to the order of `sortedIds`. If there are pet coat types of the tenant
     * whose ids are not included in `sortedIds`, they will be sorted to the end. If an id in `sortedIds` does not exist
     * or does not belong to the tenant, it will be ignored.
     * <br>
     * SQL:
     * ```
     * -- for each pet coat type:
     * update moe_pet_hair_length set sort = ?, update_time = ? where id = ?;
     * ```
     *
     * @param sortedIds sorted pet coat type ids
     * @param tenant    tenant
     */
    @Transactional
    public void sortPetCoatType(List<Long> sortedIds, Tenant tenant) {
        var sortMap = SortUtils.toSortMap(sortedIds);
        var coatTypes = listPetCoatType(tenant);

        // id in the front of sortedIds has a greater sort value, which means it will be displayed first.
        // If id is not in sortedIds, it will be displayed last.
        var now = Instant.now().getEpochSecond();
        for (var coatType : coatTypes) {
            var sort = sortMap.getOrDefault(coatType.getId().longValue(), 0);
            if (!Objects.equals(coatType.getSort(), sort)) {
                coatType.setSort(sort).setUpdateTime(now);
            }
        }

        dsl.batchUpdate(coatTypes).execute();
    }

    /**
     * Delete a pet coat type.
     * If the pet coat type is already deleted, won't do update.
     * <br>
     * SQL:
     * ```
     * update moe_pet_hair_length set status = 2, update_time = ? where id = ? and status = 1 and {tenant condition}
     * ```
     *
     * @param coatTypeId pet coat type id
     * @param tenant     tenant
     * @return true if the pet coat type is deleted, false if not found
     */
    public boolean deletePetCoatType(long coatTypeId, Tenant tenant) {
        return dsl.update(MOE_PET_HAIR_LENGTH)
                        .set(MOE_PET_HAIR_LENGTH.STATUS, StatusEnum.DELETED.getValue())
                        .set(MOE_PET_HAIR_LENGTH.UPDATE_TIME, Instant.now().getEpochSecond())
                        .where(MOE_PET_HAIR_LENGTH.ID.eq((int) coatTypeId))
                        .and(MOE_PET_HAIR_LENGTH.STATUS.eq(StatusEnum.NORMAL.getValue()))
                        .and(Objects.isNull(tenant) ? DSL.noCondition() : tenantCondition(tenant))
                        .execute()
                > 0;
    }

    /**
     * Check if the pet coat name is already used in the tenant.
     * <br>
     * SQL:
     * ```
     * select exists(
     *     select 1
     *     from moe_pet_hair_length
     *     where {tenant condition} and status = 1 and name = ? [and id != ?]
     * );
     * ```
     *
     * @param record record of pet coat type
     */
    private void checkNameUsed(MoePetHairLengthRecord record) {
        var tenant = TenantUtils.of(record.getCompanyId());

        var tenantCondition = tenantCondition(tenant);
        var nameEq = MOE_PET_HAIR_LENGTH.NAME.eq(record.getName());
        var idNe = MOE_PET_HAIR_LENGTH.ID.ne(record.getId());
        var statusCondition = MOE_PET_HAIR_LENGTH.STATUS.eq(StatusEnum.NORMAL.getValue());

        var conditions = record.getId() != null
                ? DSL.and(tenantCondition, statusCondition, nameEq, idNe)
                : DSL.and(tenantCondition, statusCondition, nameEq);

        var exist = dsl.fetchExists(MOE_PET_HAIR_LENGTH, conditions);
        if (exist) {
            // TODO: add new error code
            throw ExceptionUtil.bizException(CODE_PARAMS_ERROR, "Coat type name is already in use");
        }
    }
}
