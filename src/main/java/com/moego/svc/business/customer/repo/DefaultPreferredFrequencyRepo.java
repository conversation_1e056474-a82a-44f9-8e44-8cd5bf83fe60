package com.moego.svc.business.customer.repo;

import static com.moego.svc.business.customer.repository.jooq.Tables.MOE_DEFAULT_PREFERRED_FREQUENCY;

import com.moego.idl.models.organization.v1.Tenant;
import com.moego.idl.utils.v1.TimePeriod;
import com.moego.svc.business.customer.enums.DefaultPreferredFrequencyType;
import com.moego.svc.business.customer.repository.jooq.Keys;
import com.moego.svc.business.customer.repository.jooq.tables.records.MoeDefaultPreferredFrequencyRecord;
import java.time.LocalDateTime;
import lombok.RequiredArgsConstructor;
import org.jooq.DSLContext;
import org.jooq.impl.DSL;
import org.springframework.stereotype.Component;

/**
 * SQL for moe_default_preferred_frequency
 */
@Component
@RequiredArgsConstructor
public class DefaultPreferredFrequencyRepo {

    private final DSLContext dsl;

    public MoeDefaultPreferredFrequencyRecord getDefaultPreferredFrequency(
            Tenant tenant, DefaultPreferredFrequencyType frequencyType) {
        return dsl.selectFrom(MOE_DEFAULT_PREFERRED_FREQUENCY)
                .where(MOE_DEFAULT_PREFERRED_FREQUENCY.COMPANY_ID.eq(tenant.getCompanyId()))
                .and(frequencyType.getCondition())
                .fetchOne();
    }

    public void upsertDefaultPreferredFrequency(
            Tenant tenant, DefaultPreferredFrequencyType frequencyType, TimePeriod frequency) {

        var type = frequencyType.getValue();
        var period = frequency.getPeriodValue();
        var value = frequency.getValue();
        var now = LocalDateTime.now();

        // 先尝试插入
        dsl.insertInto(MOE_DEFAULT_PREFERRED_FREQUENCY)
                .set(MOE_DEFAULT_PREFERRED_FREQUENCY.COMPANY_ID, tenant.getCompanyId())
                // business_id 是废弃字段, 统一设置为 0
                .set(MOE_DEFAULT_PREFERRED_FREQUENCY.BUSINESS_ID, 0L)
                .set(MOE_DEFAULT_PREFERRED_FREQUENCY.FREQUENCY_TYPE, type)
                .set(MOE_DEFAULT_PREFERRED_FREQUENCY.CALENDAR_PERIOD, period)
                .set(MOE_DEFAULT_PREFERRED_FREQUENCY.VALUE, value)
                .set(MOE_DEFAULT_PREFERRED_FREQUENCY.CREATED_AT, now)
                .set(MOE_DEFAULT_PREFERRED_FREQUENCY.UPDATED_AT, now)
                // 当有冲突时，更新
                .onConflict(Keys.KEY_MOE_DEFAULT_PREFERRED_FREQUENCY_UK_COMPANY_ID.getFields())
                .doUpdate()
                // updated_at 只有在有变更时才更新
                .set(
                        MOE_DEFAULT_PREFERRED_FREQUENCY.UPDATED_AT,
                        DSL.when(
                                        // 任意一个字段有变更，就更新 updated_at
                                        DSL.or(
                                                MOE_DEFAULT_PREFERRED_FREQUENCY.FREQUENCY_TYPE.ne(type),
                                                MOE_DEFAULT_PREFERRED_FREQUENCY.CALENDAR_PERIOD.ne(period),
                                                MOE_DEFAULT_PREFERRED_FREQUENCY.VALUE.ne(value)),
                                        now)
                                // 否则保持原值
                                .otherwise(MOE_DEFAULT_PREFERRED_FREQUENCY.UPDATED_AT))
                .set(MOE_DEFAULT_PREFERRED_FREQUENCY.FREQUENCY_TYPE, type)
                .set(MOE_DEFAULT_PREFERRED_FREQUENCY.CALENDAR_PERIOD, period)
                .set(MOE_DEFAULT_PREFERRED_FREQUENCY.VALUE, value)
                .execute();
    }
}
