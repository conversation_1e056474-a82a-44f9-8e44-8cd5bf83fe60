package com.moego.svc.business.customer.repo;

import static com.moego.svc.business.customer.repository.jooq.Tables.PET_MERGE_MAPPING;

import com.moego.svc.business.customer.repository.jooq.tables.records.PetMergeMappingRecord;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.jooq.DSLContext;
import org.springframework.stereotype.Component;

/**
 * SQL for pet_merge_mapping
 */
@Component
@RequiredArgsConstructor
public class PetMergeMappingRepo {

    private final DSLContext dsl;

    public void batchCreate(List<PetMergeMappingRecord> records) {
        dsl.insertInto(PET_MERGE_MAPPING)
                .columns(PET_MERGE_MAPPING.fields())
                .valuesOfRecords(records)
                .execute();
    }

    public List<PetMergeMappingRecord> list(long processId) {
        return dsl.selectFrom(PET_MERGE_MAPPING)
                .where(PET_MERGE_MAPPING.TARGET_CUSTOMER_ID.eq(processId))
                .orderBy(PET_MERGE_MAPPING.ID)
                .fetch();
    }
}
