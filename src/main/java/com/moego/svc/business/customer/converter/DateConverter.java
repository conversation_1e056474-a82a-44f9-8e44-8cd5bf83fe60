package com.moego.svc.business.customer.converter;

import com.google.type.Date;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;

@Mapper(
        componentModel = MappingConstants.ComponentModel.SPRING,
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        unmappedTargetPolicy = ReportingPolicy.WARN,
        unmappedSourcePolicy = ReportingPolicy.WARN)
public interface DateConverter {
    /**
     * yyyy-MM-dd 格式的日期字符串转换为 google.type.Date 类型。只做类型转换，不做格式校验和日期合法性校验。
     *
     * @param str yyyy-MM-dd 格式的日期字符串
     * @return google.type.Date
     */
    default Date toDate(String str) {
        String[] split = str.split("-");
        return Date.newBuilder()
                .setYear(Integer.parseInt(split[0]))
                .setMonth(Integer.parseInt(split[1]))
                .setDay(Integer.parseInt(split[2]))
                .build();
    }

    default String toFormatDateString(Date date) {
        return String.format("%d-%02d-%02d", date.getYear(), date.getMonth(), date.getDay());
    }
}
