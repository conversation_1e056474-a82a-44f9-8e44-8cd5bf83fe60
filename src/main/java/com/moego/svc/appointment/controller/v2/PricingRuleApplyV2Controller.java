package com.moego.svc.appointment.controller.v2;

import com.moego.idl.service.appointment.v2.ApplyPricingRuleRequest;
import com.moego.idl.service.appointment.v2.ApplyPricingRuleResponse;
import com.moego.idl.service.appointment.v2.ListPricingRuleApplyLogRequest;
import com.moego.idl.service.appointment.v2.ListPricingRuleApplyLogResponse;
import com.moego.idl.service.appointment.v2.PricingRuleApplyServiceGrpc;
import com.moego.idl.service.appointment.v2.UpdateUpcomingAppointmentUsingPricingRuleRequest;
import com.moego.idl.service.appointment.v2.UpdateUpcomingAppointmentUsingPricingRuleResponse;
import com.moego.idl.service.organization.v1.DateTimeServiceGrpc;
import com.moego.idl.service.organization.v1.GetCompanyCurrentDayAndTimeRequest;
import com.moego.lib.common.grpc.server.GrpcService;
import com.moego.svc.appointment.converter.PricingRuleRecordConverter;
import com.moego.svc.appointment.domain.PricingRuleRecordApplyLog;
import com.moego.svc.appointment.service.AppointmentCompositeService;
import com.moego.svc.appointment.service.AppointmentService;
import com.moego.svc.appointment.service.PricingRuleRecordApplyService;
import io.grpc.stub.StreamObserver;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.util.CollectionUtils;

@GrpcService
@RequiredArgsConstructor
public class PricingRuleApplyV2Controller extends PricingRuleApplyServiceGrpc.PricingRuleApplyServiceImplBase {

    private final PricingRuleRecordApplyService pricingRuleRecordApplyService;
    private final AppointmentService appointmentService;
    private final AppointmentCompositeService appointmentCompositeService;
    private final DateTimeServiceGrpc.DateTimeServiceBlockingStub dateTimeService;

    @Override
    public void listPricingRuleApplyLog(
            final ListPricingRuleApplyLogRequest request,
            final StreamObserver<ListPricingRuleApplyLogResponse> responseObserver) {
        List<PricingRuleRecordApplyLog> applyLogByAppointmentId = pricingRuleRecordApplyService.getApplyLog(
                request.getCompanyId(), request.getSourceId(), request.getSourceType());

        responseObserver.onNext(com.moego.idl.service.appointment.v2.ListPricingRuleApplyLogResponse.newBuilder()
                .addAllPricingRuleApplyLogs(PricingRuleRecordConverter.INSTANCE.entityToModel(applyLogByAppointmentId))
                .build());
        responseObserver.onCompleted();
    }

    @Override
    public void updateUpcomingAppointmentUsingPricingRule(
            final UpdateUpcomingAppointmentUsingPricingRuleRequest request,
            final StreamObserver<UpdateUpcomingAppointmentUsingPricingRuleResponse> responseObserver) {
        long companyId = request.getCompanyId();
        List<Long> serviceIds = request.getServiceIdsList();
        if (CollectionUtils.isEmpty(serviceIds)) {
            responseObserver.onNext(
                    com.moego.idl.service.appointment.v2.UpdateUpcomingAppointmentUsingPricingRuleResponse
                            .getDefaultInstance());
            responseObserver.onCompleted();
            return;
        }

        var currentDayAndTime =
                dateTimeService.getCompanyCurrentDayAndTime(GetCompanyCurrentDayAndTimeRequest.newBuilder()
                        .setCompanyId(request.getCompanyId())
                        .build());

        List<Integer> appointmentIds = appointmentService.listNotStartedAppointmentWithPetService(
                companyId,
                null,
                serviceIds.stream().map(Math::toIntExact).toList(),
                null,
                currentDayAndTime.getCurrentDate());
        if (CollectionUtils.isEmpty(appointmentIds)) {
            responseObserver.onNext(
                    com.moego.idl.service.appointment.v2.UpdateUpcomingAppointmentUsingPricingRuleResponse
                            .getDefaultInstance());
            responseObserver.onCompleted();
            return;
        }
        appointmentIds.forEach(appointmentId ->
                appointmentCompositeService.updatePetDetails(request.getCompanyId(), appointmentId.longValue()));

        responseObserver.onNext(
                com.moego.idl.service.appointment.v2.UpdateUpcomingAppointmentUsingPricingRuleResponse.newBuilder()
                        .setAffectedAppointmentCount(appointmentIds.size())
                        .build());
        responseObserver.onCompleted();
    }

    @Override
    public void applyPricingRule(
            final ApplyPricingRuleRequest request, final StreamObserver<ApplyPricingRuleResponse> responseObserver) {
        responseObserver.onNext(com.moego.idl.service.appointment.v2.ApplyPricingRuleResponse.newBuilder()
                .addAllPetDetails(pricingRuleRecordApplyService.applyPricingRule(
                        request.getSourceId(),
                        request.getSourceType(),
                        request.getCompanyId(),
                        request.getBusinessId(),
                        request.getPetDetailsList()))
                .build());
        responseObserver.onCompleted();
    }
}
