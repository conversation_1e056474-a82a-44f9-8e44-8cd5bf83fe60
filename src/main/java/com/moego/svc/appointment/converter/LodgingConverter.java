package com.moego.svc.appointment.converter;

import com.moego.idl.models.appointment.v1.LodgingAssignPetDetailInfo;
import com.moego.idl.models.appointment.v1.LodgingAssignPetEvaluationInfo;
import com.moego.idl.models.appointment.v1.PetDetailDateType;
import com.moego.svc.appointment.domain.BoardingSplitLodging;
import com.moego.svc.appointment.domain.EvaluationServiceDetail;
import com.moego.svc.appointment.domain.MoeGroomingPetDetail;
import com.moego.svc.appointment.utils.PetDetailUtil;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import org.springframework.util.CollectionUtils;

@Mapper
public interface LodgingConverter {
    LodgingConverter INSTANCE = Mappers.getMapper(LodgingConverter.class);

    default LodgingAssignPetDetailInfo buildLodgingAssignPetDetailInfo(
            MoeGroomingPetDetail petDetail, Map<Integer, PetDetailDateType> dateTypeMap) {
        LodgingAssignPetDetailInfo.Builder builder = LodgingAssignPetDetailInfo.newBuilder()
                .setIsSplitLodging(false)
                .setId(petDetail.getId())
                .setPetId(petDetail.getPetId())
                .setStartDate(petDetail.getStartDate())
                .setStartTime(petDetail.getStartTime().intValue())
                .setEndDate(petDetail.getEndDate())
                .setEndTime(petDetail.getEndTime().intValue())
                .setServiceItemTypeValue(petDetail.getServiceItemType())
                .addAllSpecificDates(PetDetailUtil.getSpecificDates(petDetail));
        PetDetailDateType dateType = dateTypeMap.get(petDetail.getId());
        if (dateType != null) {
            builder.setDateType(dateType);
        }
        return builder.build();
    }

    default LodgingAssignPetDetailInfo buildLodgingAssignPetDetailInfo(
            MoeGroomingPetDetail petDetail, BoardingSplitLodging boardingSplitLodging, PetDetailDateType dateType) {
        LodgingAssignPetDetailInfo.Builder builder = LodgingAssignPetDetailInfo.newBuilder()
                .setIsSplitLodging(true)
                .setId(boardingSplitLodging.getPetDetailId())
                .setPetId(boardingSplitLodging.getPetId().intValue())
                .setStartDate(
                        boardingSplitLodging.getStartDateTime().toLocalDate().toString())
                .setStartTime(boardingSplitLodging.getStartDateTime().getHour() * 60
                        + boardingSplitLodging.getStartDateTime().getMinute())
                .setEndDate(boardingSplitLodging.getEndDateTime().toLocalDate().toString())
                .setEndTime(boardingSplitLodging.getEndDateTime().getHour() * 60
                        + boardingSplitLodging.getEndDateTime().getMinute())
                .setServiceItemTypeValue(petDetail.getServiceItemType())
                .addAllSpecificDates(PetDetailUtil.getSpecificDates(petDetail));
        if (dateType != null) {
            builder.setDateType(dateType);
        }
        return builder.build();
    }

    default List<LodgingAssignPetDetailInfo> buildLodgingAssignPetDetailInfos(
            MoeGroomingPetDetail petDetail,
            Map<Integer, PetDetailDateType> dateTypeMap,
            final List<BoardingSplitLodging> boardingSplitLodgings,
            final Long lodgingId) {
        var currentBoardingSplitLodgings = boardingSplitLodgings.stream()
                .filter(boardingSplitLodging -> Objects.equals(
                                boardingSplitLodging.getPetDetailId(),
                                petDetail.getId().longValue())
                        && Objects.equals(boardingSplitLodging.getLodgingId(), lodgingId))
                .toList();
        if (CollectionUtils.isEmpty(currentBoardingSplitLodgings)) {
            return List.of(buildLodgingAssignPetDetailInfo(petDetail, dateTypeMap));
        }
        return currentBoardingSplitLodgings.stream()
                .map(boardingSplitLodging -> buildLodgingAssignPetDetailInfo(
                        petDetail, boardingSplitLodging, dateTypeMap.get(petDetail.getId())))
                .toList();
    }

    default LodgingAssignPetEvaluationInfo buildLodgingAssignPetEvaluationInfo(
            EvaluationServiceDetail evaluationDetail) {
        LodgingAssignPetEvaluationInfo.Builder builder = LodgingAssignPetEvaluationInfo.newBuilder()
                .setId(evaluationDetail.getId())
                .setPetId(evaluationDetail.getPetId().intValue())
                .setStartDate(evaluationDetail.getStartDate().toString())
                .setStartTime(evaluationDetail.getStartTime())
                .setEndDate(evaluationDetail.getEndDate().toString())
                .setEndTime(evaluationDetail.getEndTime());
        return builder.build();
    }
}
