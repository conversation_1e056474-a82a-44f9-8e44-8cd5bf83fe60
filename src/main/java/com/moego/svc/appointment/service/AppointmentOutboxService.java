package com.moego.svc.appointment.service;

import static com.moego.svc.appointment.mapper.mysql.AppointmentOutboxDynamicSqlSupport.appointmentOutbox;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;
import static org.mybatis.dynamic.sql.SqlBuilder.isGreaterThanOrEqualTo;
import static org.mybatis.dynamic.sql.SqlBuilder.isLessThan;

import com.moego.idl.models.appointment.v1.OutboxSendStatus;
import com.moego.svc.appointment.domain.AppointmentOutbox;
import com.moego.svc.appointment.mapper.mysql.AppointmentOutboxMapper;
import java.time.LocalDateTime;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class AppointmentOutboxService {

    private final AppointmentOutboxMapper moeAppointmentOutboxMapper;

    public List<AppointmentOutbox> listPendingRecords(LocalDateTime startGte, LocalDateTime endLt) {
        return moeAppointmentOutboxMapper.select(
                c -> c.where(appointmentOutbox.status, isEqualTo(OutboxSendStatus.SEND_PENDING))
                        .and(appointmentOutbox.createdAt, isGreaterThanOrEqualTo(startGte))
                        .and(appointmentOutbox.createdAt, isLessThan(endLt)));
    }

    public void updateStatus(Long id, OutboxSendStatus status) {
        AppointmentOutbox rs = new AppointmentOutbox();
        rs.setId(id);
        rs.setStatus(status);
        moeAppointmentOutboxMapper.updateByPrimaryKeySelective(rs);
    }
}
