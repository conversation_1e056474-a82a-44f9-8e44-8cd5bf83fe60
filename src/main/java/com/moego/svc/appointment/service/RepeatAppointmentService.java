package com.moego.svc.appointment.service;

import com.moego.idl.models.appointment.v1.RepeatAppointmentModifyScope;
import com.moego.svc.appointment.domain.MoeGroomingAppointment;
import java.util.List;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2024/9/13
 */
@Service
@RequiredArgsConstructor
public class RepeatAppointmentService {

    private final AppointmentService appointmentService;

    /**
     * 根据 repeat id 和 scope 获取重复预约并过滤掉当前 repeat id 的预约
     *
     * @param appointmentId                repeat appointment id
     * @param repeatAppointmentModifyScope scope
     * @return repeat appointment list
     */
    public List<MoeGroomingAppointment> listRepeatAppointment(
            Long appointmentId, RepeatAppointmentModifyScope repeatAppointmentModifyScope) {
        MoeGroomingAppointment appointment = appointmentService.mustGet(appointmentId);
        List<MoeGroomingAppointment> repeats =
                switch (repeatAppointmentModifyScope) {
                    case THIS_AND_FOLLOWING -> appointmentService.listAfterRepeatAppointment(
                            appointment.getBusinessId(), appointment.getRepeatId(), appointment.getAppointmentDate());
                    case ALL -> appointmentService.listRepeatAppointment(
                            appointment.getBusinessId(), appointment.getRepeatId());
                    default -> List.of();
                };
        return repeats.stream()
                .filter(repeatAppointment -> !Objects.equals(repeatAppointment.getId(), appointment.getId()))
                .toList();
    }
}
