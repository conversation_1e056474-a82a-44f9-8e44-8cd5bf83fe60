package com.moego.svc.appointment.mapper.mysql;

import jakarta.annotation.Generated;
import java.sql.JDBCType;
import org.mybatis.dynamic.sql.AliasableSqlTable;
import org.mybatis.dynamic.sql.SqlColumn;

public final class AppointmentPetScheduleSettingDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: appointment_pet_schedule_setting")
    public static final AppointmentPetScheduleSetting appointmentPetScheduleSetting = new AppointmentPetScheduleSetting();

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: appointment_pet_schedule_setting.id")
    public static final SqlColumn<Long> id = appointmentPetScheduleSetting.id;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: appointment_pet_schedule_setting.company_id")
    public static final SqlColumn<Long> companyId = appointmentPetScheduleSetting.companyId;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: appointment_pet_schedule_setting.appointment_id")
    public static final SqlColumn<Long> appointmentId = appointmentPetScheduleSetting.appointmentId;

    /**
     * Database Column Remarks:
     *   1-feeding, 2-medication
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: appointment_pet_schedule_setting.schedule_type")
    public static final SqlColumn<Integer> scheduleType = appointmentPetScheduleSetting.scheduleType;

    /**
     * Database Column Remarks:
     *   appointment_pet_feeding.id, appointment_pet_medication.id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: appointment_pet_schedule_setting.schedule_id")
    public static final SqlColumn<Long> scheduleId = appointmentPetScheduleSetting.scheduleId;

    /**
     * Database Column Remarks:
     *   Scheduled time, unit in minutes. 09:00 AM = 540, 09:30 AM = 570 etc.
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: appointment_pet_schedule_setting.schedule_time")
    public static final SqlColumn<Integer> scheduleTime = appointmentPetScheduleSetting.scheduleTime;

    /**
     * Database Column Remarks:
     *   Schedule extra information, such as schedule alias name etc.
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: appointment_pet_schedule_setting.schedule_extra_json")
    public static final SqlColumn<String> scheduleExtraJson = appointmentPetScheduleSetting.scheduleExtraJson;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: appointment_pet_schedule_setting")
    public static final class AppointmentPetScheduleSetting extends AliasableSqlTable<AppointmentPetScheduleSetting> {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<Long> companyId = column("company_id", JDBCType.BIGINT);

        public final SqlColumn<Long> appointmentId = column("appointment_id", JDBCType.BIGINT);

        public final SqlColumn<Integer> scheduleType = column("schedule_type", JDBCType.INTEGER);

        public final SqlColumn<Long> scheduleId = column("schedule_id", JDBCType.BIGINT);

        public final SqlColumn<Integer> scheduleTime = column("schedule_time", JDBCType.INTEGER);

        public final SqlColumn<String> scheduleExtraJson = column("schedule_extra_json", JDBCType.LONGVARCHAR);

        public AppointmentPetScheduleSetting() {
            super("appointment_pet_schedule_setting", AppointmentPetScheduleSetting::new);
        }
    }
}