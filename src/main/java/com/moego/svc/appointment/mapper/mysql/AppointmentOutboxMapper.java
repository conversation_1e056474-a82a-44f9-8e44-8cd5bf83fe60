package com.moego.svc.appointment.mapper.mysql;

import static com.moego.svc.appointment.mapper.mysql.AppointmentOutboxDynamicSqlSupport.*;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;

import com.moego.lib.common.autoconfigure.datasource.DynamicDataSource;
import com.moego.svc.appointment.domain.AppointmentOutbox;
import com.moego.svc.appointment.mapper.typehandler.EventTypeHandler;
import com.moego.svc.appointment.mapper.typehandler.OutboxSendStatusHandler;
import jakarta.annotation.Generated;
import java.util.List;
import java.util.Optional;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectKey;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.delete.DeleteDSLCompleter;
import org.mybatis.dynamic.sql.insert.render.InsertStatementProvider;
import org.mybatis.dynamic.sql.select.CountDSLCompleter;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSL;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.update.UpdateModel;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.CommonCountMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonDeleteMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonSelectMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonUpdateMapper;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

@Mapper
public interface AppointmentOutboxMapper extends CommonSelectMapper, CommonCountMapper, CommonDeleteMapper, CommonUpdateMapper, DynamicDataSource<AppointmentOutboxMapper> {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: appointment_outbox")
    BasicColumn[] selectList = BasicColumn.columnList(id, topic, eventId, eventTime, eventKey, eventType, status, createdAt, eventDetail);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: appointment_outbox")
    @InsertProvider(type=SqlProviderAdapter.class, method="insert")
    @SelectKey(statement="SELECT LAST_INSERT_ID()", keyProperty="row.id", before=false, resultType=Long.class)
    int insert(InsertStatementProvider<AppointmentOutbox> insertStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: appointment_outbox")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @Results(id="AppointmentOutboxResult", value = {
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="topic", property="topic", jdbcType=JdbcType.VARCHAR),
        @Result(column="event_id", property="eventId", jdbcType=JdbcType.VARCHAR),
        @Result(column="event_time", property="eventTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="event_key", property="eventKey", jdbcType=JdbcType.VARCHAR),
        @Result(column="event_type", property="eventType", typeHandler=EventTypeHandler.class, jdbcType=JdbcType.INTEGER),
        @Result(column="status", property="status", typeHandler=OutboxSendStatusHandler.class, jdbcType=JdbcType.TINYINT),
        @Result(column="created_at", property="createdAt", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="event_detail", property="eventDetail", jdbcType=JdbcType.LONGVARCHAR)
    })
    List<AppointmentOutbox> selectMany(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: appointment_outbox")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @ResultMap("AppointmentOutboxResult")
    Optional<AppointmentOutbox> selectOne(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: appointment_outbox")
    default long count(CountDSLCompleter completer) {
        return MyBatis3Utils.countFrom(this::count, appointmentOutbox, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: appointment_outbox")
    default int delete(DeleteDSLCompleter completer) {
        return MyBatis3Utils.deleteFrom(this::delete, appointmentOutbox, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: appointment_outbox")
    default int deleteByPrimaryKey(Long id_) {
        return delete(c -> 
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: appointment_outbox")
    default int insertSelective(AppointmentOutbox row) {
        return MyBatis3Utils.insert(this::insert, row, appointmentOutbox, c ->
            c.map(topic).toPropertyWhenPresent("topic", row::getTopic)
            .map(eventId).toPropertyWhenPresent("eventId", row::getEventId)
            .map(eventTime).toPropertyWhenPresent("eventTime", row::getEventTime)
            .map(eventKey).toPropertyWhenPresent("eventKey", row::getEventKey)
            .map(eventType).toPropertyWhenPresent("eventType", row::getEventType)
            .map(status).toPropertyWhenPresent("status", row::getStatus)
            .map(createdAt).toPropertyWhenPresent("createdAt", row::getCreatedAt)
            .map(eventDetail).toPropertyWhenPresent("eventDetail", row::getEventDetail)
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: appointment_outbox")
    default Optional<AppointmentOutbox> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, appointmentOutbox, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: appointment_outbox")
    default List<AppointmentOutbox> select(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectList(this::selectMany, selectList, appointmentOutbox, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: appointment_outbox")
    default List<AppointmentOutbox> selectDistinct(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectDistinct(this::selectMany, selectList, appointmentOutbox, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: appointment_outbox")
    default Optional<AppointmentOutbox> selectByPrimaryKey(Long id_) {
        return selectOne(c ->
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: appointment_outbox")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, appointmentOutbox, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: appointment_outbox")
    static UpdateDSL<UpdateModel> updateAllColumns(AppointmentOutbox row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(topic).equalTo(row::getTopic)
                .set(eventId).equalTo(row::getEventId)
                .set(eventTime).equalTo(row::getEventTime)
                .set(eventKey).equalTo(row::getEventKey)
                .set(eventType).equalTo(row::getEventType)
                .set(status).equalTo(row::getStatus)
                .set(createdAt).equalTo(row::getCreatedAt)
                .set(eventDetail).equalTo(row::getEventDetail);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: appointment_outbox")
    static UpdateDSL<UpdateModel> updateSelectiveColumns(AppointmentOutbox row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(topic).equalToWhenPresent(row::getTopic)
                .set(eventId).equalToWhenPresent(row::getEventId)
                .set(eventTime).equalToWhenPresent(row::getEventTime)
                .set(eventKey).equalToWhenPresent(row::getEventKey)
                .set(eventType).equalToWhenPresent(row::getEventType)
                .set(status).equalToWhenPresent(row::getStatus)
                .set(createdAt).equalToWhenPresent(row::getCreatedAt)
                .set(eventDetail).equalToWhenPresent(row::getEventDetail);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: appointment_outbox")
    default int updateByPrimaryKeySelective(AppointmentOutbox row) {
        return update(c ->
            c.set(topic).equalToWhenPresent(row::getTopic)
            .set(eventId).equalToWhenPresent(row::getEventId)
            .set(eventTime).equalToWhenPresent(row::getEventTime)
            .set(eventKey).equalToWhenPresent(row::getEventKey)
            .set(eventType).equalToWhenPresent(row::getEventType)
            .set(status).equalToWhenPresent(row::getStatus)
            .set(createdAt).equalToWhenPresent(row::getCreatedAt)
            .set(eventDetail).equalToWhenPresent(row::getEventDetail)
            .where(id, isEqualTo(row::getId))
        );
    }
}