package com.moego.svc.appointment.listener.event;

import com.moego.idl.models.appointment.v1.RepeatAppointmentModifyScope;
import java.io.Serial;
import java.io.Serializable;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.springframework.context.ApplicationEvent;

/**
 * <AUTHOR>
 * @since 2024/2/5
 */
@Getter
@Setter
@Accessors(chain = true)
public class DeleteAppointmentPetDetailEvent extends ApplicationEvent implements Serializable {

    @Serial
    private static final long serialVersionUID = -4812079215542318592L;

    private Long companyId;
    private Long businessId;
    private Long customerId;
    private Long staffId;
    private Long appointmentId;
    private Long petId;
    private RepeatAppointmentModifyScope repeatAppointmentModifyScope;

    public DeleteAppointmentPetDetailEvent(Object source) {
        super(source);
    }
}
