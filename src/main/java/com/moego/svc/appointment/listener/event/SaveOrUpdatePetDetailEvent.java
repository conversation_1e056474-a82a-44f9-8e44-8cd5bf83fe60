package com.moego.svc.appointment.listener.event;

import com.moego.idl.models.appointment.v1.PetDetailDef;
import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.springframework.context.ApplicationEvent;

/**
 * <AUTHOR>
 * @since 2024/2/5
 */
@Getter
@Setter
@Accessors(chain = true)
public class SaveOrUpdatePetDetailEvent extends ApplicationEvent implements Serializable {

    @Serial
    private static final long serialVersionUID = -4812079215542318592L;

    private Long companyId;
    private Long businessId;
    private Long customerId;
    private Long appointmentId;
    private Integer tokenStaffId;
    private List<PetDetailDef> petDetailDefs;

    private LocalDateTime beforeAppointmentDateTime;
    private List<Integer> beforeStaffIds;

    private boolean isRepeatEvent;

    public SaveOrUpdatePetDetailEvent(Object source) {
        super(source);
    }
}
