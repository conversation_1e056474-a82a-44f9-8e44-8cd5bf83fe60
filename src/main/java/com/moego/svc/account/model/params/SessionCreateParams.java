package com.moego.svc.account.model.params;

import java.time.Duration;
import java.util.Map;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class SessionCreateParams {

    private long accountId;

    private String ip;

    private String userAgent;

    private String deviceId;

    private String refererLink;

    private long refererSessionId;

    private String impersonator;

    private Boolean renewable;

    private Duration maxAge;

    private String source;

    private Map<String, Object> sessionData;
}
