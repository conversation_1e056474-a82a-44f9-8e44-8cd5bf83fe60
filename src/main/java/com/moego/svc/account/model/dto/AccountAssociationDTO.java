package com.moego.svc.account.model.dto;

import com.moego.idl.models.account.v1.AccountAssociationPlatform;
import java.time.OffsetDateTime;
import java.util.Map;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class AccountAssociationDTO {

    private long id;

    private long accountId;

    private AccountAssociationPlatform platform;

    private String platformAccountId;

    private boolean visible;

    private Map<String, Object> platformData;

    private OffsetDateTime createdAt;

    private OffsetDateTime updatedAt;

    private OffsetDateTime deletedAt;
}
