package com.moego.svc.account.adapter.grpc.convertor;

import com.moego.idl.service.account.v1.SearchAccountForAdminRequest;
import com.moego.svc.account.model.params.AccountSearchParams;
import java.util.HashSet;
import java.util.Set;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;
import org.springframework.util.CollectionUtils;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
@Deprecated
public interface AccountSearchRequestConvertor {
    AccountSearchRequestConvertor INSTANCE = Mappers.getMapper(AccountSearchRequestConvertor.class);

    default AccountSearchParams fromSearchRequest(SearchAccountForAdminRequest request) {
        AccountSearchParams params = new AccountSearchParams();

        var page = request.getPage();
        params.setPageNo(page.getPageNo());
        params.setPageSize(page.getPageSize());

        if (request.hasSourceCondition()) {
            // source equal
            var condition = request.getSourceCondition();
            params.setSource(condition.getEq());
        }

        if (request.hasEmailCondition()) {
            // email like
            var condition = request.getEmailCondition();
            if (condition.hasLike()) {
                params.setEmailLike(condition.getLike());
            }
            // email suffix not like
            if (condition.hasSuffixNotLike()) {
                params.setEmailSuffixNotLike(condition.getSuffixNotLike());
            }
        }

        if (request.hasNameCondition()) {
            // name like
            var condition = request.getNameCondition();
            if (condition.hasLike()) {
                params.setNameLike(condition.getLike());
            }
        }

        if (request.hasAccountIdCondition()) {
            Set<Long> accountIdList = new HashSet<>();
            // account id in / equal
            var condition = request.getAccountIdCondition();
            if (!CollectionUtils.isEmpty(condition.getInList())) {
                accountIdList.addAll(condition.getInList());
            }
            if (condition.hasEq()) {
                accountIdList.add(condition.getEq());
            }
            params.setAccountIds(accountIdList);
        }

        return params;
    }
}
