package com.moego.svc.account.adapter.grpc.convertor;

import com.moego.idl.service.account.v1.CreateSessionArchiveTaskRequest;
import com.moego.svc.account.model.params.SessionArchiveTaskCreateParams;
import com.moego.svc.account.utils.TimeUtils;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;

@Mapper(
        componentModel = MappingConstants.ComponentModel.SPRING,
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        unmappedSourcePolicy = ReportingPolicy.IGNORE,
        uses = {TimeUtils.class})
public interface SessionArchiveTaskRequestConvertor {

    SessionArchiveTaskCreateParams fromCreateRequest(CreateSessionArchiveTaskRequest request);
}
