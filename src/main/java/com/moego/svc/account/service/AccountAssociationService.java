package com.moego.svc.account.service;

import static com.moego.svc.account.repository.jooq.Tables.ACCOUNT_ASSOCIATION;

import com.moego.idl.models.account.v1.AccountAssociationPlatform;
import com.moego.idl.models.errors.v1.Code;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.svc.account.model.dto.AccountAssociationDTO;
import com.moego.svc.account.model.params.AccountAssociationCreateParams;
import com.moego.svc.account.repository.convertor.AccountAssociationEntityConvertor;
import java.time.OffsetDateTime;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jooq.DSLContext;
import org.jooq.impl.DSL;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Service
@RequiredArgsConstructor
public class AccountAssociationService {

    private final DSLContext dsl;

    private final AccountAssociationEntityConvertor accountAssociationEntityConvertor;

    /**
     * 给某个 MoeGo account 增加一条第三方账号的绑定记录。对于同一个第三方平台，MoeGo account 可以绑定多个第三方账号。
     *
     * @param createParams createParams
     */
    public void addAccountAssociation(AccountAssociationCreateParams createParams) {
        var association = accountAssociationEntityConvertor.toRecord(createParams);

        var affectRows = dsl.insertInto(ACCOUNT_ASSOCIATION)
                .set(association)
                // 条件唯一索引冲突：(platform, platform_account_id) where deleted_at is null
                .onConflict(ACCOUNT_ASSOCIATION.PLATFORM, ACCOUNT_ASSOCIATION.PLATFORM_ACCOUNT_ID)
                .where(ACCOUNT_ASSOCIATION.DELETED_AT.isNull())
                // 只有当 account id 一致时，upsert 以下字段，否则不修改任何数据
                .doUpdate()
                .set(ACCOUNT_ASSOCIATION.PLATFORM_DATA, association.getPlatformData())
                .set(ACCOUNT_ASSOCIATION.VISIBLE, association.getVisible())
                .set(ACCOUNT_ASSOCIATION.UPDATED_AT, OffsetDateTime.now())
                .where(ACCOUNT_ASSOCIATION.ACCOUNT_ID.eq(createParams.getAccountId()))
                .execute();

        // insert / upsert 成功
        if (affectRows > 0) {
            return;
        }

        // affectRows = 0，说明唯一索引冲突时，account id 不一致导致没有执行 upsert 逻辑，
        // 即该第三方平台账号关联了其他 MoeGo 账号
        throw ExceptionUtil.bizException(
                Code.CODE_PLATFORM_ACCOUNT_ALREADY_ASSOCIATED,
                "This 3rd party platform account has already been associated with another MoeGo account.");
    }

    /**
     * 重新绑定 MoeGo account 和第三方账号。如果 MoeGo account 有绑定该第三方平台的账号（可能有多个），
     * 则删除原有绑定，并替换成新的绑定。
     *
     * @param createParams createParams
     */
    @Transactional
    public void resetAccountAssociation(AccountAssociationCreateParams createParams) {
        deleteAccountAssociationByAccount(createParams.getAccountId(), createParams.getPlatform());
        addAccountAssociation(createParams);
    }

    /**
     * 查询某个 MoeGo account 绑定的所有第三方账号。对于同一个第三方平台，MoeGo account 可以绑定多个第三方账号。
     *
     * @param accountId MoeGo account id
     * @param platform platform, optional, 如果不传则查询所有平台，否则只查询指定平台
     * @return List<AccountAssociationDTO>
     */
    public List<AccountAssociationDTO> listAssociationByAccount(long accountId, AccountAssociationPlatform platform) {
        return dsl.selectFrom(ACCOUNT_ASSOCIATION)
                .where(ACCOUNT_ASSOCIATION.ACCOUNT_ID.eq(accountId))
                .and(platform != null ? ACCOUNT_ASSOCIATION.PLATFORM.eq(platform.name()) : DSL.noCondition())
                .and(ACCOUNT_ASSOCIATION.DELETED_AT.isNull())
                .fetch()
                .map(accountAssociationEntityConvertor::fromRecord);
    }

    /**
     * 获取某个 MoeGo account 绑定的某个第三方平台的账号。对于同一个第三方平台，MoeGo account 可以绑定多个第三方账号，
     * 这个方法只返回最新的一个。如果需要查出同一个第三方平台下绑定的所有账号，可以使用 listAssociationByAccount 方法。
     *
     * @param accountId MoeGo account id
     * @param platform platform
     * @return AccountAssociationDTO
     */
    public AccountAssociationDTO getNewestAssociationByAccount(long accountId, AccountAssociationPlatform platform) {
        var association = dsl.selectFrom(ACCOUNT_ASSOCIATION)
                .where(ACCOUNT_ASSOCIATION.ACCOUNT_ID.eq(accountId))
                .and(ACCOUNT_ASSOCIATION.PLATFORM.eq(platform.name()))
                .and(ACCOUNT_ASSOCIATION.DELETED_AT.isNull())
                .orderBy(ACCOUNT_ASSOCIATION.CREATED_AT.desc())
                .limit(1)
                .fetchOne();
        return accountAssociationEntityConvertor.fromRecord(association);
    }

    /**
     * 根据第三方账号查询绑定的 MoeGo account
     *
     * @param platformAccountId platform account id
     * @param platform platform
     * @return AccountAssociationDTO
     */
    public AccountAssociationDTO getAccountAssociationByPlatformAccount(
            String platformAccountId, AccountAssociationPlatform platform) {
        var association = dsl.selectFrom(ACCOUNT_ASSOCIATION)
                .where(ACCOUNT_ASSOCIATION.PLATFORM_ACCOUNT_ID.eq(platformAccountId))
                .and(ACCOUNT_ASSOCIATION.PLATFORM.eq(platform.name()))
                .and(ACCOUNT_ASSOCIATION.DELETED_AT.isNull())
                .fetchOne();
        return accountAssociationEntityConvertor.fromRecord(association);
    }

    /**
     * 删除某个 MoeGo account 绑定的第三方账号
     *
     * @param accountId MoeGo account id
     * @param platform platform, optional, 如果不传则删除在所有平台绑定的第三方账号
     */
    public void deleteAccountAssociationByAccount(long accountId, AccountAssociationPlatform platform) {
        dsl.update(ACCOUNT_ASSOCIATION)
                .set(ACCOUNT_ASSOCIATION.DELETED_AT, OffsetDateTime.now())
                .where(ACCOUNT_ASSOCIATION.ACCOUNT_ID.eq(accountId))
                .and(platform != null ? ACCOUNT_ASSOCIATION.PLATFORM.eq(platform.name()) : DSL.noCondition())
                .and(ACCOUNT_ASSOCIATION.DELETED_AT.isNull())
                .execute();
    }
}
