package com.moego.svc.online.booking.mapstruct;

import com.moego.idl.models.online_booking.v1.AutomationConditionDef;
import com.moego.idl.models.online_booking.v1.AutomationSettingModel;
import com.moego.lib.common.util.JsonUtil;
import com.moego.svc.online.booking.entity.AutomationSetting;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;
import org.springframework.util.StringUtils;

@Mapper(
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        uses = {TimestampConverter.class, EnumConverter.class})
public interface AutomationConverter {

    AutomationConverter INSTANCE = Mappers.getMapper(AutomationConverter.class);

    @Mapping(target = "autoAcceptCondition", source = "autoAcceptCondition", qualifiedByName = "mapAutoAcceptCondition")
    @Mapping(target = "createdAt", source = "createdAt", qualifiedByName = "dateToTimestamp")
    @Mapping(target = "updatedAt", source = "updatedAt", qualifiedByName = "dateToTimestamp")
    AutomationSettingModel toModel(AutomationSetting entity);

    @Named("mapAutoAcceptCondition")
    static AutomationConditionDef mapAutoAcceptCondition(String autoAcceptCondition) {
        if (StringUtils.hasText(autoAcceptCondition)) {
            return JsonUtil.toBean(autoAcceptCondition, AutomationConditionDef.class);
        }
        return AutomationConditionDef.getDefaultInstance();
    }
}
