package com.moego.svc.online.booking.mapstruct;

import com.moego.idl.models.online_booking.v1.BoardingServiceDetailModel;
import com.moego.idl.service.online_booking.v1.CreateBoardingServiceDetailRequest;
import com.moego.idl.service.online_booking.v1.UpdateBoardingServiceDetailRequest;
import com.moego.svc.online.booking.entity.BoardingServiceDetail;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

@Mapper(
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface BoardingServiceDetailConverter {
    BoardingServiceDetailConverter INSTANCE = Mappers.getMapper(BoardingServiceDetailConverter.class);

    BoardingServiceDetailModel entityToModel(BoardingServiceDetail entity);

    BoardingServiceDetail createRequestToEntity(CreateBoardingServiceDetailRequest createRequest);

    default BoardingServiceDetail updateRequestToEntity(UpdateBoardingServiceDetailRequest updateRequest) {
        var detail = new BoardingServiceDetail();
        detail.setId(updateRequest.getId());
        if (updateRequest.hasStartDate()) {
            detail.setStartDate(updateRequest.getStartDate());
        }
        if (updateRequest.hasStartTime()) {
            detail.setStartTime(updateRequest.getStartTime());
        }
        if (updateRequest.hasEndDate()) {
            detail.setEndDate(updateRequest.getEndDate());
        }
        if (updateRequest.hasEndTime()) {
            detail.setEndTime(updateRequest.getEndTime());
        }
        return detail;
    }

    /*
     * Do NOT use any of the methods below,
     * their purpose is to perform mutual conversions between Protobuf and Java value types.
     */

    default com.google.protobuf.Timestamp dateToPBTimestamp(java.util.Date date) {
        return com.google.protobuf.util.Timestamps.fromDate(date);
    }

    default java.util.Date pbTimestampToDate(com.google.protobuf.Timestamp timestamp) {
        return new java.util.Date(com.google.protobuf.util.Timestamps.toMillis(timestamp));
    }

    default int pbEnumToInt(com.google.protobuf.ProtocolMessageEnum enumValue) {
        return enumValue.getNumber();
    }
}
