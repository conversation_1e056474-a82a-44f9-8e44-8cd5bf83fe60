package com.moego.svc.online.booking.consumer;

import com.moego.common.enums.ServiceItemEnum;
import com.moego.idl.models.event_bus.v1.EventData;
import com.moego.lib.event_bus.consumer.AbstractConsumer;
import com.moego.lib.event_bus.event.EventRecord;
import com.moego.server.grooming.api.IGroomingAppointmentService;
import com.moego.svc.online.booking.helper.WaitlistRedisHelper;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
public class AppointmentEventBusConsumer extends AbstractConsumer<EventData> {

    private static final String EVENT_TOPIC = "moego.erp.appointment";
    private final WaitlistRedisHelper waitlistRedisHelper;
    private final IGroomingAppointmentService iGroomingAppointmentService;

    @Override
    public String topicName() {
        return EVENT_TOPIC;
    }

    @Override
    public void consume(EventRecord<EventData> event) {
        switch (event.detail().getEventCase()) {
            case APPOINTMENT_CANCELED_EVENT -> {
                var canceledEvent = event.detail().getAppointmentCanceledEvent();
                setWaitlistCheck(
                        event.detail().getTenant().getCompanyId(),
                        event.detail().getTenant().getBusinessId(),
                        canceledEvent.getId());
            }
            case APPOINTMENT_UPDATED_EVENT -> {
                var updatedEvent = event.detail().getAppointmentUpdatedEvent();
                setWaitlistCheck(
                        event.detail().getTenant().getCompanyId(),
                        event.detail().getTenant().getBusinessId(),
                        updatedEvent.getId());
            }
            case APPOINTMENT_DELETED_EVENT -> {
                var deletedEvent = event.detail().getAppointmentDeletedEvent();
                setWaitlistCheck(
                        event.detail().getTenant().getCompanyId(),
                        event.detail().getTenant().getBusinessId(),
                        deletedEvent.getId());
            }
            default -> {}
        }
    }

    private void setWaitlistCheck(long companyId, long businessId, Long apptId) {
        // 过滤掉 only grooming 的 appt
        var appt = iGroomingAppointmentService.getAppointmentById(apptId.intValue());
        if (appt != null && !Objects.equals(appt.getServiceTypeInclude(), ServiceItemEnum.GROOMING.getBitValue())) {
            waitlistRedisHelper.setWaitlistCheckFlag(companyId, businessId);
        }
    }
}
