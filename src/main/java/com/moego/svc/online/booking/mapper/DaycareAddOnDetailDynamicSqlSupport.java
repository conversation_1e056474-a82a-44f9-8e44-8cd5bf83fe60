package com.moego.svc.online.booking.mapper;

import jakarta.annotation.Generated;
import java.math.BigDecimal;
import java.sql.JDBCType;
import java.util.Date;
import org.mybatis.dynamic.sql.AliasableSqlTable;
import org.mybatis.dynamic.sql.SqlColumn;

public final class DaycareAddOnDetailDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: daycare_add_on_detail")
    public static final DaycareAddOnDetail daycareAddOnDetail = new DaycareAddOnDetail();

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daycare_add_on_detail.id")
    public static final SqlColumn<Long> id = daycareAddOnDetail.id;

    /**
     * Database Column Remarks:
     *   The id of booking request
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daycare_add_on_detail.booking_request_id")
    public static final SqlColumn<Long> bookingRequestId = daycareAddOnDetail.bookingRequestId;

    /**
     * Database Column Remarks:
     *   The id of daycare service detail, associated with the current add-on
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daycare_add_on_detail.service_detail_id")
    public static final SqlColumn<Long> serviceDetailId = daycareAddOnDetail.serviceDetailId;

    /**
     * Database Column Remarks:
     *   The id of pet, associated with the current add-on
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daycare_add_on_detail.pet_id")
    public static final SqlColumn<Long> petId = daycareAddOnDetail.petId;

    /**
     * Database Column Remarks:
     *   The id of current add-on service
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daycare_add_on_detail.add_on_id")
    public static final SqlColumn<Long> addOnId = daycareAddOnDetail.addOnId;

    /**
     * Database Column Remarks:
     *   The specific dates of add-on service
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daycare_add_on_detail.specific_dates")
    public static final SqlColumn<String> specificDates = daycareAddOnDetail.specificDates;

    /**
     * Database Column Remarks:
     *   The flag to indicate if the add-on service is everyday
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daycare_add_on_detail.is_everyday")
    public static final SqlColumn<Boolean> isEveryday = daycareAddOnDetail.isEveryday;

    /**
     * Database Column Remarks:
     *   The price of current add-on service
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daycare_add_on_detail.service_price")
    public static final SqlColumn<BigDecimal> servicePrice = daycareAddOnDetail.servicePrice;

    /**
     * Database Column Remarks:
     *   The id of tax, associated with the current add-on
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daycare_add_on_detail.tax_id")
    public static final SqlColumn<Long> taxId = daycareAddOnDetail.taxId;

    /**
     * Database Column Remarks:
     *   The duration of current add-on service
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daycare_add_on_detail.duration")
    public static final SqlColumn<Integer> duration = daycareAddOnDetail.duration;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daycare_add_on_detail.created_at")
    public static final SqlColumn<Date> createdAt = daycareAddOnDetail.createdAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daycare_add_on_detail.updated_at")
    public static final SqlColumn<Date> updatedAt = daycareAddOnDetail.updatedAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daycare_add_on_detail.deleted_at")
    public static final SqlColumn<Date> deletedAt = daycareAddOnDetail.deletedAt;

    /**
     * Database Column Remarks:
     *   Number of times per day this add-on is performed
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daycare_add_on_detail.quantity_per_day")
    public static final SqlColumn<Integer> quantityPerDay = daycareAddOnDetail.quantityPerDay;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: daycare_add_on_detail")
    public static final class DaycareAddOnDetail extends AliasableSqlTable<DaycareAddOnDetail> {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<Long> bookingRequestId = column("booking_request_id", JDBCType.BIGINT);

        public final SqlColumn<Long> serviceDetailId = column("service_detail_id", JDBCType.BIGINT);

        public final SqlColumn<Long> petId = column("pet_id", JDBCType.BIGINT);

        public final SqlColumn<Long> addOnId = column("add_on_id", JDBCType.BIGINT);

        public final SqlColumn<String> specificDates = column("specific_dates", JDBCType.OTHER, "com.moego.svc.online.booking.typehandler.StringToJsonbTypeHandler");

        public final SqlColumn<Boolean> isEveryday = column("is_everyday", JDBCType.BIT);

        public final SqlColumn<BigDecimal> servicePrice = column("service_price", JDBCType.NUMERIC);

        public final SqlColumn<Long> taxId = column("tax_id", JDBCType.BIGINT);

        public final SqlColumn<Integer> duration = column("duration", JDBCType.INTEGER);

        public final SqlColumn<Date> createdAt = column("created_at", JDBCType.TIMESTAMP);

        public final SqlColumn<Date> updatedAt = column("updated_at", JDBCType.TIMESTAMP);

        public final SqlColumn<Date> deletedAt = column("deleted_at", JDBCType.TIMESTAMP);

        public final SqlColumn<Integer> quantityPerDay = column("quantity_per_day", JDBCType.INTEGER);

        public DaycareAddOnDetail() {
            super("daycare_add_on_detail", DaycareAddOnDetail::new);
        }
    }
}