package com.moego.svc.online.booking.service;

import static com.moego.common.utils.CommonUtil.isNormal;
import static com.moego.lib.common.exception.ExceptionUtil.bizException;
import static com.moego.svc.online.booking.mapper.BoardingServiceWaitlistDynamicSqlSupport.boardingServiceWaitlist;
import static com.moego.svc.online.booking.mapper.DaycareServiceWaitlistDynamicSqlSupport.daycareServiceWaitlist;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;
import static org.mybatis.dynamic.sql.SqlBuilder.isNull;

import com.moego.common.dto.notificationDto.NotificationExtraWaitlistAvailableDto;
import com.moego.common.enums.ServiceItemEnum;
import com.moego.idl.models.appointment.v1.LodgingAssignInfo;
import com.moego.idl.models.business_customer.v1.BusinessCustomerPetInfoModel;
import com.moego.idl.models.business_customer.v1.BusinessPetSizeModel;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.offering.v1.LodgingTypeModel;
import com.moego.idl.models.offering.v1.LodgingUnitModel;
import com.moego.idl.models.offering.v1.ServiceBriefView;
import com.moego.idl.models.online_booking.v1.BookingRequestModel;
import com.moego.idl.models.online_booking.v1.BookingRequestStatus;
import com.moego.idl.models.online_booking.v1.PetToLodgingDef;
import com.moego.idl.service.online_booking.v1.WaitlistExtra;
import com.moego.lib.common.util.JsonUtil;
import com.moego.server.message.api.INotificationService;
import com.moego.server.message.params.notification.NotificationWaitlistAvailableParams;
import com.moego.svc.online.booking.entity.BoardingServiceDetail;
import com.moego.svc.online.booking.entity.BoardingServiceWaitlist;
import com.moego.svc.online.booking.entity.DaycareServiceDetail;
import com.moego.svc.online.booking.entity.DaycareServiceWaitlist;
import com.moego.svc.online.booking.mapper.BoardingServiceWaitlistMapper;
import com.moego.svc.online.booking.mapper.DaycareServiceWaitlistMapper;
import com.moego.svc.online.booking.utils.ProtobufUtil;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Nullable;
import lombok.RequiredArgsConstructor;
import org.mybatis.dynamic.sql.SqlBuilder;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

@Service
@RequiredArgsConstructor
public class WaitlistService {

    private final BookingRequestService bookingRequestService;

    private final DaycareServiceDetailService daycareServiceDetailService;
    private final BoardingServiceDetailService boardingServiceDetailService;

    private final DaycareServiceWaitlistMapper daycareServiceWaitlistMapper;
    private final BoardingServiceWaitlistMapper boardingServiceWaitlistMapper;
    private final AutoAssignService autoAssignService;
    private final LodgingService lodgingService;
    private final ServiceService serviceService;

    private final INotificationService notificationApi;

    /**
     * Insert a BoardingServiceWaitlist.
     *
     * @param entity BoardingServiceWaitlist
     * @return insert BoardingServiceWaitlist id
     */
    public long insertBoardingServiceWaitlist(BoardingServiceWaitlist entity) {

        if (!isNormal(entity.getBookingRequestId())) {
            throw bizException(Code.CODE_PARAMS_ERROR, "bookingRequestId is required");
        }
        if (!isNormal(entity.getServiceDetailId())) {
            throw bizException(Code.CODE_PARAMS_ERROR, "serviceDetailId is required");
        }
        if (entity.getStartDate() == null) {
            throw bizException(Code.CODE_PARAMS_ERROR, "startDate is required");
        }
        if (entity.getEndDate() == null) {
            throw bizException(Code.CODE_PARAMS_ERROR, "endDate is required");
        }

        boardingServiceWaitlistMapper.insertSelective(entity);

        return entity.getId();
    }

    /**
     * Insert a DaycareServiceWaitlist.
     *
     * @param entity DaycareServiceWaitlist
     * @return insert DaycareServiceWaitlist id
     */
    public long insertDaycareServiceWaitlist(DaycareServiceWaitlist entity) {

        if (!isNormal(entity.getBookingRequestId())) {
            throw bizException(Code.CODE_PARAMS_ERROR, "bookingRequestId is required");
        }
        if (!isNormal(entity.getServiceDetailId())) {
            throw bizException(Code.CODE_PARAMS_ERROR, "serviceDetailId is required");
        }
        if (ObjectUtils.isEmpty(entity.getSpecificDates())) {
            throw bizException(Code.CODE_PARAMS_ERROR, "specificDates is required");
        }

        daycareServiceWaitlistMapper.insertSelective(entity);

        return entity.getId();
    }

    private void updateDaycareWaitlistRecord(DaycareServiceWaitlist existingRecord, List<LocalDate> dates) {
        var daycareWaitlist = new DaycareServiceWaitlist();
        daycareWaitlist.setId(existingRecord.getId());
        daycareWaitlist.setSpecificDates(dates);
        daycareWaitlist.setUpdatedAt(LocalDateTime.now());
        daycareServiceWaitlistMapper.updateByPrimaryKeySelective(daycareWaitlist);
    }

    public void updateDaycareWaitlist(
            long bookingRequestId, long serviceDetailId, List<com.google.type.Date> specificDatesList) {
        if (serviceDetailId <= 0) {
            throw bizException(Code.CODE_PARAMS_ERROR, "serviceDetailId is required");
        }
        if (CollectionUtils.isEmpty(specificDatesList)) {
            throw bizException(Code.CODE_PARAMS_ERROR, "specificDatesList is required");
        }

        // 转换日期格式
        List<LocalDate> specificDates = specificDatesList.stream()
                .map(date -> LocalDate.of(date.getYear(), date.getMonth(), date.getDay()))
                .collect(Collectors.toList());

        // 查询现有记录
        List<DaycareServiceWaitlist> existingRecords = daycareServiceWaitlistMapper.select(
                c -> c.where(daycareServiceWaitlist.bookingRequestId, isEqualTo(bookingRequestId))
                        .and(daycareServiceWaitlist.serviceDetailId, isEqualTo(serviceDetailId))
                        .and(daycareServiceWaitlist.deletedAt, isNull()));

        if (CollectionUtils.isEmpty(existingRecords)) {
            var daycareServiceWaitlist = new DaycareServiceWaitlist();
            daycareServiceWaitlist.setBookingRequestId(bookingRequestId);
            daycareServiceWaitlist.setServiceDetailId(serviceDetailId);
            daycareServiceWaitlist.setSpecificDates(specificDates);
            insertDaycareServiceWaitlist(daycareServiceWaitlist);
        } else {
            updateDaycareWaitlistRecord(existingRecords.get(0), specificDates);
        }
    }

    private void updateBoardingWaitlistRecord(
            BoardingServiceWaitlist existingRecord, LocalDate startDate, LocalDate endDate) {
        var boardingWaitlist = new BoardingServiceWaitlist();
        boardingWaitlist.setId(existingRecord.getId());
        boardingWaitlist.setServiceDetailId(existingRecord.getServiceDetailId());
        boardingWaitlist.setStartDate(startDate);
        boardingWaitlist.setEndDate(endDate);
        boardingWaitlist.setUpdatedAt(LocalDateTime.now());
        boardingServiceWaitlistMapper.updateByPrimaryKeySelective(boardingWaitlist);
    }

    public void updateWaitlistByStartDateEndDate(long bookingRequestId, String startDate, String endDate) {
        var waitlist = bookingRequestService.get(bookingRequestId);
        if (waitlist == null || waitlist.getStatus() != BookingRequestStatus.WAIT_LIST) {
            return;
        }

        // boarding waitlist save
        var boardingDetails = boardingServiceDetailService.listByBookingRequestId(bookingRequestId);
        if (!CollectionUtils.isEmpty(boardingDetails)) {
            boardingDetails.forEach(boarding -> {
                var saveBean = new BoardingServiceDetail();
                saveBean.setId(boarding.getId());
                saveBean.setStartDate(startDate);
                saveBean.setEndDate(endDate);
                boardingServiceDetailService.update(saveBean);
            });
        }
        var boardingWaitlists = boardingListByBookingRequestId(Collections.singletonList(bookingRequestId));
        if (!CollectionUtils.isEmpty(boardingWaitlists)) {
            boardingWaitlists.forEach(boardingWaitlist -> updateBoardingWaitlistRecord(
                    boardingWaitlist, LocalDate.parse(startDate), LocalDate.parse(endDate)));
        }

        // daycare waitlist save
        var daycareDetails = boardingServiceDetailService.listByBookingRequestId(bookingRequestId);
        if (!CollectionUtils.isEmpty(daycareDetails)) {
            daycareDetails.forEach(daycare -> {
                var saveBean = new DaycareServiceDetail();
                saveBean.setId(daycare.getId());
                saveBean.setSpecificDates(JsonUtil.toJson(Collections.singletonList(startDate)));
                daycareServiceDetailService.update(saveBean);
            });
        }
        var daycareWaitlists = daycareListByBookingRequestId(Collections.singletonList(bookingRequestId));
        if (!CollectionUtils.isEmpty(daycareWaitlists)) {
            daycareWaitlists.forEach(daycareWaitlist -> updateDaycareWaitlistRecord(
                    daycareWaitlist, Collections.singletonList(LocalDate.parse(startDate))));
        }
    }

    public void updateBoardingWaitlist(
            long bookingRequestId, long serviceDetailId, LocalDate startDate, LocalDate endDate) {
        if (serviceDetailId <= 0) {
            throw bizException(Code.CODE_PARAMS_ERROR, "serviceDetailId is required");
        }
        if (startDate == null) {
            throw bizException(Code.CODE_PARAMS_ERROR, "startDate is required");
        }
        if (endDate == null) {
            throw bizException(Code.CODE_PARAMS_ERROR, "endDate is required");
        }

        // 查询现有记录
        List<BoardingServiceWaitlist> existingRecords = boardingServiceWaitlistMapper.select(
                c -> c.where(boardingServiceWaitlist.serviceDetailId, isEqualTo(serviceDetailId))
                        .and(boardingServiceWaitlist.deletedAt, isNull()));

        if (CollectionUtils.isEmpty(existingRecords)) {
            var boardingServiceWaitlist = new BoardingServiceWaitlist();
            boardingServiceWaitlist.setBookingRequestId(bookingRequestId);
            boardingServiceWaitlist.setServiceDetailId(serviceDetailId);
            boardingServiceWaitlist.setStartDate(startDate);
            boardingServiceWaitlist.setEndDate(endDate);
            insertBoardingServiceWaitlist(boardingServiceWaitlist);
        } else {
            updateBoardingWaitlistRecord(existingRecords.get(0), startDate, endDate);
        }
    }

    public Map<Long, DaycareServiceWaitlist> daycareMapByServiceDetailIdWithBookingRquestId(
            List<Long> bookingRequestIds) {
        if (CollectionUtils.isEmpty(bookingRequestIds)) {
            return Map.of();
        }
        return daycareListByBookingRequestId(bookingRequestIds).stream()
                .collect(
                        Collectors.toMap(DaycareServiceWaitlist::getServiceDetailId, Function.identity(), (a, b) -> b));
    }

    public Map<Long, BoardingServiceWaitlist> boardingMapByServiceDetailIdWithBookingRquestId(
            List<Long> bookingRequestIds) {
        if (CollectionUtils.isEmpty(bookingRequestIds)) {
            return Map.of();
        }
        return boardingListByBookingRequestId((bookingRequestIds)).stream()
                .collect(Collectors.toMap(
                        BoardingServiceWaitlist::getServiceDetailId, Function.identity(), (a, b) -> b));
    }

    private List<DaycareServiceWaitlist> daycareListByBookingRequestId(List<Long> bookingRequestIds) {
        if (CollectionUtils.isEmpty(bookingRequestIds)) {
            return List.of();
        }
        return daycareServiceWaitlistMapper.select(
                c -> c.where(daycareServiceWaitlist.bookingRequestId, SqlBuilder.isIn(bookingRequestIds))
                        .and(daycareServiceWaitlist.deletedAt, isNull()));
    }

    private List<BoardingServiceWaitlist> boardingListByBookingRequestId(List<Long> bookingRequestIds) {
        if (CollectionUtils.isEmpty(bookingRequestIds)) {
            return List.of();
        }
        return boardingServiceWaitlistMapper.select(
                c -> c.where(boardingServiceWaitlist.bookingRequestId, SqlBuilder.isIn(bookingRequestIds))
                        .and(boardingServiceWaitlist.deletedAt, isNull()));
    }

    public List<WaitlistExtra> generateWaitlistExtras(List<BookingRequestModel> waitlists) {
        if (CollectionUtils.isEmpty(waitlists)) {
            return List.of();
        }
        long companyId = waitlists.get(0).getCompanyId();
        long businessId = waitlists.get(0).getBusinessId();

        // 获取 pet size list
        List<BusinessPetSizeModel> petSizeList = autoAssignService.getPetSizeList(companyId);
        // 获取 lodging 信息
        List<LodgingUnitModel> lodgingUnitList = lodgingService.getLodgingUnit(companyId, businessId);
        List<LodgingTypeModel> lodgingTypeList = lodgingService.getLodgingTypeByUnits(lodgingUnitList);
        // 获取 pet 数据
        Set<Long> petIds = waitlists.stream()
                .flatMap(waitlistModel -> PetDetailService.getPetIds(waitlistModel).stream())
                .collect(Collectors.toSet());
        Map<Long, BusinessCustomerPetInfoModel> petMap =
                autoAssignService.getPetMap(companyId, petIds.stream().toList());
        // 获取 service 数据
        Set<Long> serviceIds = waitlists.stream()
                .flatMap(waitlistModel -> PetDetailService.getServiceIds(waitlistModel).stream())
                .collect(Collectors.toSet());
        Map<Long, ServiceBriefView> serviceMap =
                serviceService.getServiceMap(companyId, serviceIds.stream().toList());

        List<WaitlistExtra> extraList = new ArrayList<>();
        for (BookingRequestModel waitlistModel : waitlists) {
            if (!StringUtils.hasText(waitlistModel.getStartDate())
                    || !StringUtils.hasText(waitlistModel.getEndDate())) {
                extraList.add(WaitlistExtra.newBuilder()
                        .setId(waitlistModel.getId())
                        .setIsAvailable(false)
                        .setIsExpired(false)
                        .build());
                continue;
            }
            // 获取 lodging 使用信息
            List<LodgingAssignInfo> assignInfoList = lodgingService.getLodgingAssignInfo(
                    companyId, businessId, waitlistModel.getStartDate(), waitlistModel.getEndDate());
            List<PetToLodgingDef> petToLodgingDefs = AutoAssignService.autoAssign(
                    waitlistModel, lodgingTypeList, lodgingUnitList, serviceMap, petSizeList, petMap, assignInfoList);

            extraList.add(WaitlistExtra.newBuilder()
                    .setId(waitlistModel.getId())
                    .setIsAvailable(!CollectionUtils.isEmpty(petToLodgingDefs))
                    .setIsExpired(isExpired(waitlistModel.getStartDate()))
                    .build());
        }
        return extraList;
    }

    /**
     * 检查 startDate 是否大于当前时间的 3 个月
     */
    private boolean isExpired(String date) {
        LocalDate startDate = LocalDate.parse(date);
        LocalDate threeMonthsAgo = LocalDate.now().minusMonths(3);
        return startDate.isBefore(threeMonthsAgo);
    }

    public void sendNotification(
            long businessId, @Nullable BookingRequestModel bookingRequestModel, @Nullable Long groomingWaitlistId) {
        NotificationWaitlistAvailableParams param = new NotificationWaitlistAvailableParams();
        param.setBusinessId((int) businessId);
        var webPushDto = new NotificationExtraWaitlistAvailableDto();
        if (bookingRequestModel != null) {
            for (BookingRequestModel.Service service : bookingRequestModel.getServicesList()) {
                if (service.hasBoarding()) {
                    var waitlist = service.getBoarding().getWaitlist();
                    webPushDto.setStartDate(
                            ProtobufUtil.toLocalDate(waitlist.getStartDate()).toString());
                    webPushDto.setEndDate(
                            ProtobufUtil.toLocalDate(waitlist.getEndDate()).toString());

                    webPushDto.setServiceItemType(ServiceItemEnum.BOARDING);
                    break;
                }
                if (service.hasDaycare()) {
                    var waitlist = service.getDaycare().getWaitlist();
                    webPushDto.setStartDate(ProtobufUtil.toLocalDate(waitlist.getSpecificDates(0))
                            .toString());
                    webPushDto.setEndDate(webPushDto.getStartDate());
                    webPushDto.setServiceItemType(ServiceItemEnum.DAYCARE);
                    break;
                }
            }
        }
        if (groomingWaitlistId != null && groomingWaitlistId > 0) {
            webPushDto.setServiceItemType(ServiceItemEnum.GROOMING);
        }
        param.setWebPushDto(webPushDto);
        param.setIsNotifyBusinessAllStaff(true);
        notificationApi.sendNotificationWaitlistAvailable(param);
    }
}
