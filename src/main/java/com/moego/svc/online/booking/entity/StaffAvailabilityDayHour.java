package com.moego.svc.online.booking.entity;

import jakarta.annotation.Generated;
import java.util.Date;
import java.util.List;

/**
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table staff_availability_day_hour
 */
public class StaffAvailabilityDayHour {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: staff_availability_day_hour.id")
    private Long id;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: staff_availability_day_hour.day_type")
    private Integer dayType;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: staff_availability_day_hour.day_id")
    private Long dayId;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: staff_availability_day_hour.start_time")
    private Integer startTime;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: staff_availability_day_hour.end_time")
    private Integer endTime;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: staff_availability_day_hour.capacity")
    private Integer capacity;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: staff_availability_day_hour.limit_ids")
    private List<Long> limitIds;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: staff_availability_day_hour.created_at")
    private Date createdAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: staff_availability_day_hour.updated_at")
    private Date updatedAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: staff_availability_day_hour.id")
    public Long getId() {
        return id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: staff_availability_day_hour.id")
    public void setId(Long id) {
        this.id = id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: staff_availability_day_hour.day_type")
    public Integer getDayType() {
        return dayType;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: staff_availability_day_hour.day_type")
    public void setDayType(Integer dayType) {
        this.dayType = dayType;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: staff_availability_day_hour.day_id")
    public Long getDayId() {
        return dayId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: staff_availability_day_hour.day_id")
    public void setDayId(Long dayId) {
        this.dayId = dayId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: staff_availability_day_hour.start_time")
    public Integer getStartTime() {
        return startTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: staff_availability_day_hour.start_time")
    public void setStartTime(Integer startTime) {
        this.startTime = startTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: staff_availability_day_hour.end_time")
    public Integer getEndTime() {
        return endTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: staff_availability_day_hour.end_time")
    public void setEndTime(Integer endTime) {
        this.endTime = endTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: staff_availability_day_hour.capacity")
    public Integer getCapacity() {
        return capacity;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: staff_availability_day_hour.capacity")
    public void setCapacity(Integer capacity) {
        this.capacity = capacity;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: staff_availability_day_hour.limit_ids")
    public List<Long> getLimitIds() {
        return limitIds;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: staff_availability_day_hour.limit_ids")
    public void setLimitIds(List<Long> limitIds) {
        this.limitIds = limitIds;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: staff_availability_day_hour.created_at")
    public Date getCreatedAt() {
        return createdAt;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: staff_availability_day_hour.created_at")
    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: staff_availability_day_hour.updated_at")
    public Date getUpdatedAt() {
        return updatedAt;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: staff_availability_day_hour.updated_at")
    public void setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
    }

    @Override
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: staff_availability_day_hour")
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", dayType=").append(dayType);
        sb.append(", dayId=").append(dayId);
        sb.append(", startTime=").append(startTime);
        sb.append(", endTime=").append(endTime);
        sb.append(", capacity=").append(capacity);
        sb.append(", limitIds=").append(limitIds);
        sb.append(", createdAt=").append(createdAt);
        sb.append(", updatedAt=").append(updatedAt);
        sb.append("]");
        return sb.toString();
    }

    @Override
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: staff_availability_day_hour")
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        StaffAvailabilityDayHour other = (StaffAvailabilityDayHour) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getDayType() == null ? other.getDayType() == null : this.getDayType().equals(other.getDayType()))
            && (this.getDayId() == null ? other.getDayId() == null : this.getDayId().equals(other.getDayId()))
            && (this.getStartTime() == null ? other.getStartTime() == null : this.getStartTime().equals(other.getStartTime()))
            && (this.getEndTime() == null ? other.getEndTime() == null : this.getEndTime().equals(other.getEndTime()))
            && (this.getCapacity() == null ? other.getCapacity() == null : this.getCapacity().equals(other.getCapacity()))
            && (this.getLimitIds() == null ? other.getLimitIds() == null : this.getLimitIds().equals(other.getLimitIds()))
            && (this.getCreatedAt() == null ? other.getCreatedAt() == null : this.getCreatedAt().equals(other.getCreatedAt()))
            && (this.getUpdatedAt() == null ? other.getUpdatedAt() == null : this.getUpdatedAt().equals(other.getUpdatedAt()));
    }

    @Override
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: staff_availability_day_hour")
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getDayType() == null) ? 0 : getDayType().hashCode());
        result = prime * result + ((getDayId() == null) ? 0 : getDayId().hashCode());
        result = prime * result + ((getStartTime() == null) ? 0 : getStartTime().hashCode());
        result = prime * result + ((getEndTime() == null) ? 0 : getEndTime().hashCode());
        result = prime * result + ((getCapacity() == null) ? 0 : getCapacity().hashCode());
        result = prime * result + ((getLimitIds() == null) ? 0 : getLimitIds().hashCode());
        result = prime * result + ((getCreatedAt() == null) ? 0 : getCreatedAt().hashCode());
        result = prime * result + ((getUpdatedAt() == null) ? 0 : getUpdatedAt().hashCode());
        return result;
    }
}