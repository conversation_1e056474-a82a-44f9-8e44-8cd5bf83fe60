package com.moego.svc.online.booking.server;

import static com.moego.common.enums.ServiceItemEnum.getMainServiceItemType;
import static com.moego.common.utils.CommonUtil.isNormal;
import static com.moego.lib.common.exception.ExceptionUtil.bizException;
import static com.moego.svc.online.booking.utils.PricingRuleUtils.getUsingPricingRuleService;
import static java.lang.Math.toIntExact;
import static java.util.Comparator.comparing;
import static java.util.Comparator.naturalOrder;
import static java.util.Comparator.nullsFirst;
import static java.util.Comparator.nullsLast;
import static java.util.function.Function.identity;
import static java.util.function.Predicate.not;
import static java.util.stream.Collectors.groupingBy;
import static java.util.stream.Collectors.mapping;
import static java.util.stream.Collectors.toList;
import static java.util.stream.Collectors.toMap;
import static org.springframework.util.CollectionUtils.firstElement;
import static org.springframework.util.CollectionUtils.isEmpty;
import static org.springframework.util.CollectionUtils.lastElement;

import com.google.protobuf.Int64Value;
import com.google.protobuf.util.Timestamps;
import com.google.type.Date;
import com.moego.common.dto.notificationDto.NotificationExtraOBReqestDto;
import com.moego.common.enums.AppointmentEventEnum;
import com.moego.common.enums.DepositPaymentTypeEnum;
import com.moego.common.enums.ServiceItemEnum;
import com.moego.common.enums.order.LineApplyType;
import com.moego.common.enums.order.OrderItemType;
import com.moego.common.params.CustomerIdsParams;
import com.moego.common.utils.CommonUtil;
import com.moego.common.utils.Pagination;
import com.moego.idl.models.appointment.v1.AppointmentCreateForOnlineBookingDef;
import com.moego.idl.models.appointment.v1.AppointmentModel;
import com.moego.idl.models.appointment.v1.AppointmentNoteCreateDef;
import com.moego.idl.models.appointment.v1.AppointmentNoteType;
import com.moego.idl.models.appointment.v1.AppointmentSource;
import com.moego.idl.models.appointment.v1.AppointmentStatus;
import com.moego.idl.models.appointment.v1.LodgingAssignInfo;
import com.moego.idl.models.appointment.v1.PetDetailDateType;
import com.moego.idl.models.appointment.v1.PetDetailDef;
import com.moego.idl.models.appointment.v1.SelectedEvaluationDef;
import com.moego.idl.models.appointment.v2.PricingRuleApplySourceType;
import com.moego.idl.models.business_customer.v1.BusinessCustomerInfoModel;
import com.moego.idl.models.business_customer.v1.BusinessCustomerPetInfoModel;
import com.moego.idl.models.business_customer.v1.BusinessPetSizeModel;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.offering.v1.CustomizedServiceView;
import com.moego.idl.models.offering.v1.EvaluationBriefView;
import com.moego.idl.models.offering.v1.EvaluationModel;
import com.moego.idl.models.offering.v1.LodgingTypeModel;
import com.moego.idl.models.offering.v1.LodgingUnitModel;
import com.moego.idl.models.offering.v1.ServiceBriefView;
import com.moego.idl.models.offering.v1.ServiceCategoryModel;
import com.moego.idl.models.offering.v1.ServiceItemType;
import com.moego.idl.models.offering.v1.ServiceModel;
import com.moego.idl.models.offering.v1.ServiceOverrideType;
import com.moego.idl.models.offering.v1.ServicePriceUnit;
import com.moego.idl.models.offering.v1.ServiceType;
import com.moego.idl.models.offering.v2.PetDetailCalculateDef;
import com.moego.idl.models.offering.v2.PetDetailCalculateResultDef;
import com.moego.idl.models.online_booking.v1.AcceptClientType;
import com.moego.idl.models.online_booking.v1.AutomationConditionDef;
import com.moego.idl.models.online_booking.v1.BoardingAddOnDetailModel;
import com.moego.idl.models.online_booking.v1.BoardingServiceDetailModel;
import com.moego.idl.models.online_booking.v1.BookingRequestAssociatedModel;
import com.moego.idl.models.online_booking.v1.BookingRequestModel;
import com.moego.idl.models.online_booking.v1.BookingRequestStatus;
import com.moego.idl.models.online_booking.v1.DaycareAddOnDetailModel;
import com.moego.idl.models.online_booking.v1.DaycareServiceDetailModel;
import com.moego.idl.models.online_booking.v1.EvaluationTestDetailModel;
import com.moego.idl.models.online_booking.v1.GroomingAddOnDetailModel;
import com.moego.idl.models.online_booking.v1.GroomingServiceDetailModel;
import com.moego.idl.models.online_booking.v1.PetToLodgingDef;
import com.moego.idl.models.online_booking.v1.ProfileUpdateCondition;
import com.moego.idl.models.online_booking.v1.VaccineStatusCondition;
import com.moego.idl.models.order.v1.OrderLineItemModel;
import com.moego.idl.models.order.v1.OrderLineTaxModel;
import com.moego.idl.models.order.v1.OrderModel;
import com.moego.idl.models.order.v1.OrderSourceType;
import com.moego.idl.models.order.v1.OrderStatus;
import com.moego.idl.models.organization.v1.TaxRuleModel;
import com.moego.idl.service.appointment.v1.AppointmentServiceGrpc;
import com.moego.idl.service.appointment.v1.CreateAppointmentForOnlineBookingRequest;
import com.moego.idl.service.appointment.v1.GetAppointmentRequest;
import com.moego.idl.service.appointment.v2.ApplyPricingRuleRequest;
import com.moego.idl.service.appointment.v2.PricingRuleApplyServiceGrpc;
import com.moego.idl.service.offering.v1.BatchGetCustomizedServiceRequest;
import com.moego.idl.service.offering.v1.BatchGetCustomizedServiceResponse.ServiceWithCustomizedInfo;
import com.moego.idl.service.offering.v1.CustomizedServiceQueryCondition;
import com.moego.idl.service.offering.v1.EvaluationServiceGrpc;
import com.moego.idl.service.offering.v1.GetEvaluationRequest;
import com.moego.idl.service.offering.v1.GetEvaluationResponse;
import com.moego.idl.service.offering.v1.GetServiceListByIdsRequest;
import com.moego.idl.service.offering.v1.GetServiceListRequest;
import com.moego.idl.service.offering.v1.ServiceManagementServiceGrpc;
import com.moego.idl.service.offering.v2.CalculatePricingRuleRequest;
import com.moego.idl.service.offering.v2.PricingRuleServiceGrpc;
import com.moego.idl.service.online_booking.v1.AcceptBookingRequestRequest;
import com.moego.idl.service.online_booking.v1.AcceptBookingRequestResponse;
import com.moego.idl.service.online_booking.v1.AcceptBookingRequestV2Request;
import com.moego.idl.service.online_booking.v1.AcceptBookingRequestV2Response;
import com.moego.idl.service.online_booking.v1.AutoAssignRequest;
import com.moego.idl.service.online_booking.v1.AutoAssignResponse;
import com.moego.idl.service.online_booking.v1.BookingRequestServiceGrpc;
import com.moego.idl.service.online_booking.v1.CheckWaitlistAvailableTaskRequest;
import com.moego.idl.service.online_booking.v1.CheckWaitlistAvailableTaskResponse;
import com.moego.idl.service.online_booking.v1.CountBookingRequestByFilterRequest;
import com.moego.idl.service.online_booking.v1.CountBookingRequestByFilterResponse;
import com.moego.idl.service.online_booking.v1.CountBookingRequestsRequest;
import com.moego.idl.service.online_booking.v1.CountBookingRequestsResponse;
import com.moego.idl.service.online_booking.v1.CreateBoardingAddOnDetailRequest;
import com.moego.idl.service.online_booking.v1.CreateBoardingServiceDetailRequest;
import com.moego.idl.service.online_booking.v1.CreateBookingRequestRequest;
import com.moego.idl.service.online_booking.v1.CreateDaycareAddOnDetailRequest;
import com.moego.idl.service.online_booking.v1.CreateDaycareServiceDetailRequest;
import com.moego.idl.service.online_booking.v1.CreateEvaluationTestDetailRequest;
import com.moego.idl.service.online_booking.v1.CreateGroomingOnlyRequest;
import com.moego.idl.service.online_booking.v1.CreateGroomingOnlyResponse;
import com.moego.idl.service.online_booking.v1.DeclineBookingRequestRequest;
import com.moego.idl.service.online_booking.v1.DeclineBookingRequestResponse;
import com.moego.idl.service.online_booking.v1.GetAutoAssignRequest;
import com.moego.idl.service.online_booking.v1.GetAutoAssignResponse;
import com.moego.idl.service.online_booking.v1.GetBookingRequestRequest;
import com.moego.idl.service.online_booking.v1.GetBookingRequestResponse;
import com.moego.idl.service.online_booking.v1.ListBookingRequestIdRequest;
import com.moego.idl.service.online_booking.v1.ListBookingRequestIdResponse;
import com.moego.idl.service.online_booking.v1.ListBookingRequestsRequest;
import com.moego.idl.service.online_booking.v1.ListBookingRequestsResponse;
import com.moego.idl.service.online_booking.v1.ListWaitlistsRequest;
import com.moego.idl.service.online_booking.v1.ListWaitlistsResponse;
import com.moego.idl.service.online_booking.v1.MoveBookingRequestToWaitlistRequest;
import com.moego.idl.service.online_booking.v1.MoveBookingRequestToWaitlistResponse;
import com.moego.idl.service.online_booking.v1.PreviewBookingRequestPricingRequest;
import com.moego.idl.service.online_booking.v1.PreviewBookingRequestPricingResponse;
import com.moego.idl.service.online_booking.v1.ReplaceBookingRequestRequest;
import com.moego.idl.service.online_booking.v1.ReplaceBookingRequestResponse;
import com.moego.idl.service.online_booking.v1.RetryFailedEventsRequest;
import com.moego.idl.service.online_booking.v1.RetryFailedEventsResponse;
import com.moego.idl.service.online_booking.v1.SyncBookingRequestFromAppointmentRequest;
import com.moego.idl.service.online_booking.v1.SyncBookingRequestFromAppointmentResponse;
import com.moego.idl.service.online_booking.v1.TriggerBookingRequestAutoAcceptedRequest;
import com.moego.idl.service.online_booking.v1.TriggerBookingRequestAutoAcceptedResponse;
import com.moego.idl.service.online_booking.v1.UpdateBookingRequestRequest;
import com.moego.idl.service.online_booking.v1.UpdateBookingRequestResponse;
import com.moego.idl.service.online_booking.v1.UpdateBookingRequestStatusRequest;
import com.moego.idl.service.online_booking.v1.UpdateBookingRequestStatusResponse;
import com.moego.idl.service.online_booking.v1.WaitlistExtra;
import com.moego.idl.service.order.v1.CreateOrderRequest;
import com.moego.idl.service.order.v1.OrderServiceGrpc;
import com.moego.idl.service.organization.v1.BatchGetTaxRuleRequest;
import com.moego.idl.service.organization.v1.TaxRuleServiceGrpc;
import com.moego.idl.utils.v2.OrderBy;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.grpc.server.GrpcService;
import com.moego.lib.common.thread.ThreadPool;
import com.moego.lib.common.util.JsonUtil;
import com.moego.server.customer.api.ICustomerProfileRequestService;
import com.moego.server.customer.dto.CustomerProfileRequestDTO;
import com.moego.server.grooming.api.IBookOnlineDepositService;
import com.moego.server.grooming.api.IBookOnlineQuestionService;
import com.moego.server.grooming.api.IOBService;
import com.moego.server.grooming.api.IOrderDecouplingFlowMarkerService;
import com.moego.server.grooming.dto.BookOnlineDepositDTO;
import com.moego.server.grooming.dto.ob.BookOnlineQuestionSaveDTO;
import com.moego.server.grooming.dto.ob.OBRequestSyncDTO;
import com.moego.server.grooming.params.ob.BookingRequestEventParams;
import com.moego.server.message.api.INotificationService;
import com.moego.server.message.params.OnlineBookWaitingNotifyParams;
import com.moego.server.message.params.notification.ob.NotificationOBRequestReceivedParams;
import com.moego.server.payment.api.IPaymentPreAuthService;
import com.moego.server.payment.api.IPaymentRefundService;
import com.moego.server.payment.api.IPaymentStripeService;
import com.moego.server.payment.params.AppointmentEventParams;
import com.moego.server.payment.params.CreateRefundByPaymentIdParams;
import com.moego.svc.online.booking.client.OrganizationClient;
import com.moego.svc.online.booking.dto.BookingRequestFilterDTO;
import com.moego.svc.online.booking.entity.BoardingAddOnDetail;
import com.moego.svc.online.booking.entity.BoardingServiceDetail;
import com.moego.svc.online.booking.entity.BoardingServiceWaitlist;
import com.moego.svc.online.booking.entity.BookingRequest;
import com.moego.svc.online.booking.entity.BookingRequestAppointmentMapping;
import com.moego.svc.online.booking.entity.DaycareAddOnDetail;
import com.moego.svc.online.booking.entity.DaycareServiceDetail;
import com.moego.svc.online.booking.entity.DaycareServiceWaitlist;
import com.moego.svc.online.booking.entity.EvaluationTestDetail;
import com.moego.svc.online.booking.entity.GroomingServiceDetail;
import com.moego.svc.online.booking.eventbus.OnlineBookingProducer;
import com.moego.svc.online.booking.helper.CustomerHelper;
import com.moego.svc.online.booking.helper.OfferingHelper;
import com.moego.svc.online.booking.helper.OrderHelper;
import com.moego.svc.online.booking.helper.OrganizationHelper;
import com.moego.svc.online.booking.helper.ServiceHelper;
import com.moego.svc.online.booking.helper.VaccineHelper;
import com.moego.svc.online.booking.helper.WaitlistRedisHelper;
import com.moego.svc.online.booking.listener.SyncBookingRequestListener;
import com.moego.svc.online.booking.mapstruct.AutoAssignConverter;
import com.moego.svc.online.booking.mapstruct.AutomationConverter;
import com.moego.svc.online.booking.mapstruct.BoardingAddOnDetailConverter;
import com.moego.svc.online.booking.mapstruct.BoardingAutoAssignConverter;
import com.moego.svc.online.booking.mapstruct.BoardingServiceDetailConverter;
import com.moego.svc.online.booking.mapstruct.BoardingServiceWaitlistConverter;
import com.moego.svc.online.booking.mapstruct.BookingRequestConverter;
import com.moego.svc.online.booking.mapstruct.BookingRequestNoteConverter;
import com.moego.svc.online.booking.mapstruct.DaycareAddOnDetailConverter;
import com.moego.svc.online.booking.mapstruct.DaycareServiceDetailConverter;
import com.moego.svc.online.booking.mapstruct.DaycareServiceWaitlistConverter;
import com.moego.svc.online.booking.mapstruct.DogWalkingServiceDetailConverter;
import com.moego.svc.online.booking.mapstruct.EvaluationTestDetailConverter;
import com.moego.svc.online.booking.mapstruct.FeedingConverter;
import com.moego.svc.online.booking.mapstruct.GroomingAddOnDetailConverter;
import com.moego.svc.online.booking.mapstruct.GroomingAutoAssignConverter;
import com.moego.svc.online.booking.mapstruct.GroomingServiceDetailConverter;
import com.moego.svc.online.booking.mapstruct.GroupClassServiceDetailConverter;
import com.moego.svc.online.booking.mapstruct.MedicationConverter;
import com.moego.svc.online.booking.mapstruct.PageConverter;
import com.moego.svc.online.booking.service.AutoAssignService;
import com.moego.svc.online.booking.service.AutomationSettingService;
import com.moego.svc.online.booking.service.BoardingAddOnDetailService;
import com.moego.svc.online.booking.service.BoardingAutoAssignService;
import com.moego.svc.online.booking.service.BoardingServiceDetailService;
import com.moego.svc.online.booking.service.BookingRequestAppointmentMappingService;
import com.moego.svc.online.booking.service.BookingRequestModifyService;
import com.moego.svc.online.booking.service.BookingRequestNoteService;
import com.moego.svc.online.booking.service.BookingRequestService;
import com.moego.svc.online.booking.service.DaycareAddOnDetailService;
import com.moego.svc.online.booking.service.DaycareServiceDetailService;
import com.moego.svc.online.booking.service.DogWalkingServiceDetailService;
import com.moego.svc.online.booking.service.EvaluationTestDetailService;
import com.moego.svc.online.booking.service.FeedingMedicationService;
import com.moego.svc.online.booking.service.FeedingService;
import com.moego.svc.online.booking.service.FulfillmentService;
import com.moego.svc.online.booking.service.GroomingAddOnDetailService;
import com.moego.svc.online.booking.service.GroomingAutoAssignService;
import com.moego.svc.online.booking.service.GroomingServiceDetailService;
import com.moego.svc.online.booking.service.GroupClassServiceDetailService;
import com.moego.svc.online.booking.service.LodgingService;
import com.moego.svc.online.booking.service.MedicationService;
import com.moego.svc.online.booking.service.MembershipService;
import com.moego.svc.online.booking.service.PetDetailService;
import com.moego.svc.online.booking.service.ServiceService;
import com.moego.svc.online.booking.service.WaitlistService;
import com.moego.svc.online.booking.utils.PricingRuleUtils;
import com.moego.svc.online.booking.utils.PricingRuleUtils.FulfillmentLineItem;
import com.moego.svc.online.booking.utils.ProtobufUtil;
import io.grpc.stub.StreamObserver;
import jakarta.annotation.Nullable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.IdentityHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.TreeMap;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.util.Pair;
import org.springframework.transaction.support.TransactionOperations;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 */
@Slf4j
@GrpcService
@RequiredArgsConstructor
public class BookingRequestServer extends BookingRequestServiceGrpc.BookingRequestServiceImplBase {

    private final BookingRequestService bookingRequestService;
    private final WaitlistService waitlistService;
    private final WaitlistRedisHelper waitlistRedisHelper;
    private final BookingRequestNoteService bookingRequestNoteService;
    private final DaycareServiceDetailService daycareServiceDetailService;
    private final BoardingServiceDetailService boardingServiceDetailService;
    private final EvaluationTestDetailService evaluationTestDetailService;
    private final DaycareAddOnDetailService daycareAddOnDetailService;
    private final BoardingAddOnDetailService boardingAddOnDetailService;
    private final FeedingService feedingService;
    private final MedicationService medicationService;
    private final DogWalkingServiceDetailService dogWalkingServiceDetailService;
    private final GroomingServiceDetailService groomingServiceDetailService;
    private final GroomingAddOnDetailService groomingAddOnDetailService;
    private final TransactionOperations transaction;
    private final GroomingAutoAssignService groomingAutoAssignService;
    private final BoardingAutoAssignService boardingAutoAssignService;
    private final SyncBookingRequestListener syncBookingRequestListener;
    private final StringRedisTemplate stringRedisTemplate;
    private final ServiceManagementServiceGrpc.ServiceManagementServiceBlockingStub serviceStub;
    private final BookingRequestModifyService bookingRequestModifyService;
    private final FeedingMedicationService feedingMedicationService;
    private final AutoAssignService autoAssignService;
    private final ServiceService serviceService;
    private final LodgingService lodgingService;
    private final PetDetailService petDetailService;
    private final OrganizationClient organizationClient;
    private final OnlineBookingProducer onlineBookingProducer;
    private final IBookOnlineDepositService depositApi;
    private final IPaymentRefundService refundApi;
    private final BookingRequestAppointmentMappingService bookingRequestAppointmentMappingService;
    private final IPaymentStripeService paymentStripeApi;
    private final AppointmentServiceGrpc.AppointmentServiceBlockingStub appointmentStub;
    private final OrderHelper orderHelper;
    private final IPaymentPreAuthService preAuthApi;
    private final OrganizationHelper organizationHelper;
    private final ServiceHelper serviceHelper;
    private final VaccineHelper vaccineHelper;
    private final IOBService obApi;
    private final AutomationSettingService automationSettingService;
    private final CustomerHelper customerHelper;
    private final INotificationService notificationApi;
    private final ICustomerProfileRequestService profileRequestApi;
    private final IBookOnlineQuestionService bookOnlineQuestionApi;
    private final IOrderDecouplingFlowMarkerService orderDecouplingFlowMarkerServiceApi;
    private final MembershipService membershipService;
    private final TaxRuleServiceGrpc.TaxRuleServiceBlockingStub taxRuleStub;
    private final EvaluationServiceGrpc.EvaluationServiceBlockingStub evaluationStub;
    private final OrderServiceGrpc.OrderServiceBlockingStub orderStub;
    private final PricingRuleApplyServiceGrpc.PricingRuleApplyServiceBlockingStub pricingRuleApplyStub;
    private final GroupClassServiceDetailService groupClassServiceDetailService;
    private final FulfillmentService fulfillmentService;
    private final OfferingHelper offeringHelper;
    private final PricingRuleServiceGrpc.PricingRuleServiceBlockingStub pricingRuleStub;

    @Override
    public void createBookingRequest(CreateBookingRequestRequest request, StreamObserver<Int64Value> responseObserver) {

        // 1. 创建 BookingRequest
        long id = createBookingRequest(request);
        onlineBookingProducer.pushOnlineBookingSubmittedEvent(id);
        if (request.getNeedCreateOrder()) {
            createOrder(request, id);
        }
        responseObserver.onNext(Int64Value.of(id));
        responseObserver.onCompleted();
    }

    private long createBookingRequest(CreateBookingRequestRequest request) {

        Long id = transaction.execute(status -> {
            var bookingRequestId = bookingRequestService.insert(toBookingRequest(request));

            request.getServicesList().forEach(service -> addService(service, bookingRequestId));
            if (request.hasComment() && StringUtils.hasText(request.getComment())) {
                bookingRequestNoteService.saveBookingRequestNote(
                        request.getCompanyId(), request.getCustomerId(), bookingRequestId, request.getComment());
            }

            return bookingRequestId;
        });

        if (id == null) {
            throw bizException(Code.CODE_SERVER_ERROR, "Return id should not be null");
        }

        // 这个操作需要异步并行查询 service detail 数据，需要在事务外执行
        updateBookingRequestDateTime(id);

        return id;
    }

    /*private*/ void updateBookingRequestDateTime(long bookingRequestId) {

        var bookingRequest = mustGetBookingRequest(null, bookingRequestId);

        var services = listService(bookingRequest);

        var mainServiceItemType = getMainServiceItemType(bookingRequest.getServiceTypeInclude());
        switch (mainServiceItemType) {
            case BOARDING -> updateBoardingDateTime(bookingRequest, services);
            case DAYCARE -> updateDaycareDateTime(bookingRequest, services);
            case GROOMING -> updateGroomingDateTime(bookingRequest, services);
            case EVALUATION -> updateEvaluationDateTime(bookingRequest, services);
            case DOG_WALKING -> updateDogWalkingDateTime(bookingRequest, services);
            case GROUP_CLASS -> updateGroupClassDateTime(bookingRequest, services);
            default -> throw bizException(Code.CODE_PARAMS_ERROR, "Unsupported service type: " + mainServiceItemType);
        }
    }

    private void updateGroupClassDateTime(BookingRequest bookingRequest, List<BookingRequestModel.Service> services) {
        var datePoints = services.stream()
                .filter(BookingRequestModel.Service::hasGroupClass)
                .map(BookingRequestModel.Service::getGroupClass)
                .map(BookingRequestModel.GroupClassService::getService)
                .flatMap(e -> e.getSpecificDatesList().stream()
                        .flatMap(date ->
                                Stream.of(new DatePoint(date, e.getStartTime()), new DatePoint(date, e.getEndTime()))))
                .toList();
        if (datePoints.isEmpty()) {
            return;
        }

        var min = DatePoint.min(datePoints);
        var max = DatePoint.max(datePoints);
        var updateBean = new BookingRequest();
        updateBean.setId(bookingRequest.getId());
        updateBean.setStartDate(min.date());
        updateBean.setStartTime(min.time());
        updateBean.setEndDate(max.date());
        updateBean.setEndTime(max.time());
        bookingRequestService.update(updateBean);
    }

    private void updateDogWalkingDateTime(BookingRequest bookingRequest, List<BookingRequestModel.Service> services) {
        var datePoints = services.stream()
                .filter(BookingRequestModel.Service::hasDogWalking)
                .map(BookingRequestModel.Service::getDogWalking)
                .map(BookingRequestModel.DogWalkingService::getService)
                .flatMap(e -> {
                    var list = new ArrayList<DatePoint>();
                    if (StringUtils.hasText(e.getStartDate())) {
                        list.add(new DatePoint(e.getStartDate(), e.getStartTime()));
                    }
                    if (StringUtils.hasText(e.getEndDate())) {
                        list.add(new DatePoint(e.getEndDate(), e.getEndTime()));
                    }
                    return list.stream();
                })
                .toList();
        if (datePoints.isEmpty()) {
            return;
        }

        var min = DatePoint.min(datePoints);
        var max = DatePoint.max(datePoints);
        var updateBean = new BookingRequest();
        updateBean.setId(bookingRequest.getId());
        updateBean.setStartDate(min.date());
        updateBean.setStartTime(min.time());
        updateBean.setEndDate(max.date());
        updateBean.setEndTime(max.time());
        bookingRequestService.update(updateBean);
    }

    private void updateEvaluationDateTime(BookingRequest bookingRequest, List<BookingRequestModel.Service> services) {
        var datePoints = services.stream()
                .filter(BookingRequestModel.Service::hasEvaluation)
                .map(BookingRequestModel.Service::getEvaluation)
                .map(BookingRequestModel.EvaluationService::getService)
                .flatMap(e -> {
                    var list = new ArrayList<DatePoint>();
                    if (StringUtils.hasText(e.getStartDate())) {
                        list.add(new DatePoint(e.getStartDate(), e.getStartTime()));
                    }
                    if (StringUtils.hasText(e.getEndDate())) {
                        list.add(new DatePoint(e.getEndDate(), e.getEndTime()));
                    }
                    return list.stream();
                })
                .toList();
        if (datePoints.isEmpty()) {
            return;
        }

        var min = DatePoint.min(datePoints);
        var max = DatePoint.max(datePoints);
        var updateBean = new BookingRequest();
        updateBean.setId(bookingRequest.getId());
        updateBean.setStartDate(min.date());
        updateBean.setStartTime(min.time());
        updateBean.setEndDate(max.date());
        updateBean.setEndTime(max.time());
        bookingRequestService.update(updateBean);
    }

    private void updateGroomingDateTime(BookingRequest bookingRequest, List<BookingRequestModel.Service> services) {
        var datePoints = services.stream()
                .filter(BookingRequestModel.Service::hasGrooming)
                .map(BookingRequestModel.Service::getGrooming)
                .map(BookingRequestModel.GroomingService::getService)
                .flatMap(e -> {
                    var list = new ArrayList<DatePoint>();
                    if (StringUtils.hasText(e.getStartDate())) {
                        list.add(new DatePoint(e.getStartDate(), e.hasStartTime() ? e.getStartTime() : null));
                    }
                    if (StringUtils.hasText(e.getEndDate())) {
                        list.add(new DatePoint(e.getEndDate(), e.hasEndTime() ? e.getEndTime() : null));
                    }
                    return list.stream();
                })
                .toList();
        if (datePoints.isEmpty()) {
            return;
        }

        var min = DatePoint.min(datePoints);
        var max = DatePoint.max(datePoints);
        var updateBean = new BookingRequest();
        updateBean.setId(bookingRequest.getId());
        updateBean.setStartDate(min.date());
        updateBean.setStartTime(min.time());
        updateBean.setEndDate(max.date());
        updateBean.setEndTime(max.time());
        bookingRequestService.update(updateBean);
    }

    private void updateDaycareDateTime(BookingRequest bookingRequest, List<BookingRequestModel.Service> services) {
        var datePoints = services.stream()
                .filter(BookingRequestModel.Service::hasDaycare)
                .map(BookingRequestModel.Service::getDaycare)
                .map(BookingRequestModel.DaycareService::getService)
                .flatMap(e -> e.getSpecificDatesList().stream()
                        .flatMap(date ->
                                Stream.of(new DatePoint(date, e.getStartTime()), new DatePoint(date, e.getEndTime()))))
                .toList();
        if (datePoints.isEmpty()) {
            return;
        }

        var min = DatePoint.min(datePoints);
        var max = DatePoint.max(datePoints);

        var updateBean = new BookingRequest();
        updateBean.setId(bookingRequest.getId());
        updateBean.setStartDate(min.date());
        updateBean.setStartTime(min.time());
        updateBean.setEndDate(max.date());
        updateBean.setEndTime(max.time());
        bookingRequestService.update(updateBean);
    }

    private void updateBoardingDateTime(BookingRequest bookingRequest, List<BookingRequestModel.Service> services) {
        var datePoints = services.stream()
                .filter(BookingRequestModel.Service::hasBoarding)
                .map(BookingRequestModel.Service::getBoarding)
                .map(BookingRequestModel.BoardingService::getService)
                .flatMap(e -> {
                    var list = new ArrayList<DatePoint>();
                    if (StringUtils.hasText(e.getStartDate())) {
                        list.add(new DatePoint(e.getStartDate(), e.getStartTime()));
                    }
                    if (StringUtils.hasText(e.getEndDate())) {
                        list.add(new DatePoint(e.getEndDate(), e.getEndTime()));
                    }
                    return list.stream();
                })
                .toList();
        if (datePoints.isEmpty()) {
            return;
        }

        var min = DatePoint.min(datePoints);
        var max = DatePoint.max(datePoints);

        var updateBean = new BookingRequest();
        updateBean.setId(bookingRequest.getId());
        updateBean.setStartDate(min.date());
        updateBean.setStartTime(min.time());
        updateBean.setEndDate(max.date());
        updateBean.setEndTime(max.time());
        bookingRequestService.update(updateBean);
    }

    @Override
    public void createGroomingOnly(
            CreateGroomingOnlyRequest request, StreamObserver<CreateGroomingOnlyResponse> responseObserver) {
        super.createGroomingOnly(request, responseObserver);
    }

    @Override
    public void getBookingRequest(
            GetBookingRequestRequest request, StreamObserver<GetBookingRequestResponse> responseObserver) {
        BookingRequest bookingRequest = bookingRequestService.get(request.getId());
        if (bookingRequest == null) {
            responseObserver.onNext(GetBookingRequestResponse.getDefaultInstance());
            responseObserver.onCompleted();
            return;
        }

        Set<BookingRequestAssociatedModel> models = new HashSet<>(request.getAssociatedModelsList());
        Map<Long, List<BookingRequestModel.Service>> servicesMap = listServices(List.of(bookingRequest), models);

        var bookingRequestModelBuilder = BookingRequestConverter.INSTANCE.entityToModel(bookingRequest).toBuilder();

        var note = bookingRequestNoteService.getBookingRequestNoteByBookingRequestId(
                bookingRequest.getCompanyId(), request.getId());
        if (note != null) {
            bookingRequestModelBuilder.setComment(BookingRequestNoteConverter.INSTANCE.entityToModel(note));
        }

        responseObserver.onNext(GetBookingRequestResponse.newBuilder()
                .setBookingRequest(bookingRequestModelBuilder
                        .addAllServices(servicesMap.getOrDefault(bookingRequest.getId(), List.of()))
                        .build())
                .build());

        responseObserver.onCompleted();
    }

    /**
     * Get grooming services by booking request ids.
     *
     * @param bookingRequests booking requests
     * @param models          associated models
     * @return bookingRequestId -> grooming services
     */
    private Map<Long, List<BookingRequestModel.Service>> buildGroomingServices(
            List<BookingRequest> bookingRequests, Set<BookingRequestAssociatedModel> models) {
        if (isEmpty(bookingRequests)) {
            return Map.of();
        }

        List<Long> bookingRequestIds =
                bookingRequests.stream().map(BookingRequest::getId).toList();
        Map<Long, Map<Long, BookingRequestModel.GroomingService.Builder>> builderMap = new HashMap<>();
        if (models.contains(BookingRequestAssociatedModel.SERVICE)) {
            var serviceList = groomingServiceDetailService.listByBookingRequestId(bookingRequestIds);

            var customizedServiceList = listCustomizedService(serviceList, bookingRequests);

            for (var groomingServiceDetail : serviceList) {
                var serviceDetail = GroomingServiceDetailConverter.INSTANCE.entityToModel(groomingServiceDetail);
                var overrideServiceDetail = buildOverrideServiceDetail(serviceDetail, customizedServiceList);
                builderMap
                        .computeIfAbsent(overrideServiceDetail.getBookingRequestId(), id -> new HashMap<>())
                        .computeIfAbsent(
                                overrideServiceDetail.getId(), id -> BookingRequestModel.GroomingService.newBuilder())
                        .setService(overrideServiceDetail);
            }
        }
        if (models.contains(BookingRequestAssociatedModel.ADD_ON)) {
            groomingAddOnDetailService
                    .listByBookingRequestId(bookingRequestIds)
                    .forEach((bookingRequestId, serviceAddOnsMap) ->
                            serviceAddOnsMap.forEach((serviceDetailId, addOns) -> builderMap
                                    .computeIfAbsent(bookingRequestId, id -> new HashMap<>())
                                    .computeIfAbsent(
                                            serviceDetailId, id -> BookingRequestModel.GroomingService.newBuilder())
                                    .addAllAddons(addOns.stream()
                                            .map(GroomingAddOnDetailConverter.INSTANCE::entityToModel)
                                            .toList())));
        }
        if (models.contains(BookingRequestAssociatedModel.AUTO_ASSIGN)) {
            groomingAutoAssignService
                    .listByBookingRequestId(bookingRequestIds)
                    .forEach((bookingRequestId, autoAssign) -> {
                        var serviceBuilder = builderMap.get(bookingRequestId);
                        if (!isEmpty(serviceBuilder)) {
                            serviceBuilder.values().stream()
                                    .findFirst()
                                    .ifPresent(builder -> builder.setAutoAssign(
                                            GroomingAutoAssignConverter.INSTANCE.entityToModel(autoAssign)));
                        } else {
                            builderMap
                                    .computeIfAbsent(bookingRequestId, id -> new HashMap<>())
                                    .computeIfAbsent(
                                            autoAssign.getId(), id -> BookingRequestModel.GroomingService.newBuilder())
                                    .setAutoAssign(GroomingAutoAssignConverter.INSTANCE.entityToModel(autoAssign));
                        }
                    });
        }
        return builderMap.entrySet().stream()
                .collect(Collectors.toMap(Map.Entry::getKey, entry -> entry.getValue().values().stream()
                        .map(BookingRequestModel.GroomingService.Builder::build)
                        .map(service -> BookingRequestModel.Service.newBuilder()
                                .setGrooming(service)
                                .build())
                        .toList()));
    }

    private List<ServiceWithCustomizedInfo> listCustomizedService(
            List<GroomingServiceDetail> serviceList, List<BookingRequest> bookingRequests) {

        var bookingRequestIdToBookingRequest =
                bookingRequests.stream().collect(Collectors.toMap(BookingRequest::getId, e -> e, (o, n) -> o));

        var companyIdToBookingRequestIdToServiceList = serviceList.stream()
                .collect(groupingBy(
                        service -> bookingRequestIdToBookingRequest
                                .get(service.getBookingRequestId())
                                .getCompanyId(),
                        groupingBy(GroomingServiceDetail::getBookingRequestId)));

        return companyIdToBookingRequestIdToServiceList.entrySet().stream()
                .map(entry -> {
                    var companyId = entry.getKey();
                    var bookingRequestIdToServiceList = entry.getValue();

                    var builder = BatchGetCustomizedServiceRequest.newBuilder().setCompanyId(companyId);

                    bookingRequestIdToServiceList.forEach((bookingRequestId, ServiceDetailList) -> {
                        var businessId = bookingRequestIdToBookingRequest
                                .get(bookingRequestId)
                                .getBusinessId();
                        var conditions = buildCustomizedServiceQueryConditionList(businessId, ServiceDetailList);
                        conditions.forEach(builder::addQueryConditionList);
                    });

                    return serviceStub
                            .batchGetCustomizedService(builder.build())
                            .getCustomizedServiceListList();
                })
                .flatMap(List::stream)
                .toList();
    }

    private static GroomingServiceDetailModel buildOverrideServiceDetail(
            GroomingServiceDetailModel serviceDetail, List<ServiceWithCustomizedInfo> customizedServiceList) {

        var builder = serviceDetail.toBuilder();

        var customizedService = findCustomizedService(
                customizedServiceList,
                serviceDetail.getServiceId(),
                serviceDetail.getPetId(),
                serviceDetail.getStaffId());
        if (customizedService != null) {
            builder.setPriceOverrideType(customizedService.getPriceOverrideType());
            builder.setDurationOverrideType(customizedService.getDurationOverrideType());
        }

        return builder.build();
    }

    private static List<CustomizedServiceQueryCondition> buildCustomizedServiceQueryConditionList(
            long businessId, List<GroomingServiceDetail> groomingServiceDetailList) {

        var condList = new ArrayList<CustomizedServiceQueryCondition>();
        for (var groomingServiceDetail : groomingServiceDetailList) {
            var cb = CustomizedServiceQueryCondition.newBuilder();
            cb.setServiceId(groomingServiceDetail.getServiceId());
            cb.setBusinessId(businessId);
            if (isNormal(groomingServiceDetail.getPetId())) {
                cb.setPetId(groomingServiceDetail.getPetId());
            }
            if (isNormal(groomingServiceDetail.getStaffId())) {
                cb.setStaffId(groomingServiceDetail.getStaffId());
            }
            condList.add(cb.build());
        }

        return condList;
    }

    @Nullable
    private static CustomizedServiceView findCustomizedService(
            List<ServiceWithCustomizedInfo> customizedServiceList, long serviceId, long petId, long staffId) {
        return customizedServiceList.stream()
                .filter(e -> {
                    var cond = e.getQueryCondition();
                    return serviceId == cond.getServiceId()
                            && (!isNormal(petId) && !isNormal(cond.getPetId())
                                    || isNormal(petId) && petId == cond.getPetId())
                            && (!isNormal(staffId) && !isNormal(cond.getStaffId())
                                    || isNormal(staffId) && staffId == cond.getStaffId());
                })
                .findFirst()
                .map(ServiceWithCustomizedInfo::getCustomizedService)
                .orElse(null);
    }

    private Map<Long, List<BookingRequestModel.Service>> buildBoardingServices(
            List<Long> bookingRequestIds, Set<BookingRequestAssociatedModel> models) {
        Map<Long, Map<Long, BookingRequestModel.BoardingService.Builder>> builderMap = new HashMap<>();
        if (models.contains(BookingRequestAssociatedModel.SERVICE)) {
            var boardingWaitlistByServiceIdId =
                    waitlistService.boardingMapByServiceDetailIdWithBookingRquestId(bookingRequestIds);
            boardingServiceDetailService.listByBookingRequestId(bookingRequestIds).stream()
                    .map(BoardingServiceDetailConverter.INSTANCE::entityToModel)
                    .forEach(serviceDetail -> {
                        var builder = builderMap
                                .computeIfAbsent(serviceDetail.getBookingRequestId(), id -> new HashMap<>())
                                .computeIfAbsent(
                                        serviceDetail.getId(), id -> BookingRequestModel.BoardingService.newBuilder());
                        builder.setService(serviceDetail);
                        if (boardingWaitlistByServiceIdId.containsKey(serviceDetail.getId())) {
                            builder.setWaitlist(BoardingServiceWaitlistConverter.INSTANCE.entityToModel(
                                    boardingWaitlistByServiceIdId.get(serviceDetail.getId())));
                        }
                    });
        }
        if (models.contains(BookingRequestAssociatedModel.ADD_ON)) {
            boardingAddOnDetailService
                    .listByBookingRequestId(bookingRequestIds)
                    .forEach((bookingRequestId, serviceAddOnsMap) ->
                            serviceAddOnsMap.forEach((serviceDetailId, addOns) -> builderMap
                                    .computeIfAbsent(bookingRequestId, id -> new HashMap<>())
                                    .computeIfAbsent(
                                            serviceDetailId, id -> BookingRequestModel.BoardingService.newBuilder())
                                    .addAllAddons(addOns.stream()
                                            .map(BoardingAddOnDetailConverter.INSTANCE::entityToModel)
                                            .toList())));
        }
        if (models.contains(BookingRequestAssociatedModel.FEEDING)) {
            feedingService
                    .listByBookingRequestId(bookingRequestIds, ServiceItemType.BOARDING_VALUE)
                    .forEach((bookingRequestId, bookingRequestFeedings) ->
                            bookingRequestFeedings.forEach((serviceDetailId, feedings) -> {
                                var detail = builderMap
                                        .computeIfAbsent(bookingRequestId, id -> new HashMap<>())
                                        .computeIfAbsent(
                                                serviceDetailId,
                                                id -> BookingRequestModel.BoardingService.newBuilder());
                                detail.addAllFeedings(FeedingConverter.INSTANCE.entityToModel(feedings));
                                if (!isEmpty(feedings)) {
                                    detail.setFeeding(FeedingConverter.INSTANCE.entityToModel(feedings.get(0)));
                                }
                            }));
        }
        if (models.contains(BookingRequestAssociatedModel.MEDICATION)) {
            medicationService
                    .listByBookingRequestId(bookingRequestIds, ServiceItemType.BOARDING_VALUE)
                    .forEach((bookingRequestId, bookingRequestMedications) -> {
                        bookingRequestMedications.forEach((serviceDetailId, medications) -> {
                            var detail = builderMap
                                    .computeIfAbsent(bookingRequestId, id -> new HashMap<>())
                                    .computeIfAbsent(
                                            serviceDetailId, id -> BookingRequestModel.BoardingService.newBuilder());
                            detail.addAllMedications(MedicationConverter.INSTANCE.entityToModel(medications));
                            if (!isEmpty(medications)) {
                                detail.setMedication(MedicationConverter.INSTANCE.entityToModel(medications.get(0)));
                            }
                        });
                    });
        }
        if (models.contains(BookingRequestAssociatedModel.AUTO_ASSIGN)) {
            boardingAutoAssignService
                    .listByBookingRequestId(bookingRequestIds)
                    .forEach((bookingRequestId, autoAssign) -> builderMap
                            .computeIfAbsent(bookingRequestId, id -> new HashMap<>())
                            .computeIfAbsent(
                                    autoAssign.getBoardingServiceDetailId(),
                                    id -> BookingRequestModel.BoardingService.newBuilder())
                            .setAutoAssign(BoardingAutoAssignConverter.INSTANCE.entityToModel(autoAssign)));
        }

        return builderMap.entrySet().stream()
                .collect(Collectors.toMap(Map.Entry::getKey, entry -> entry.getValue().values().stream()
                        .map(BookingRequestModel.BoardingService.Builder::build)
                        .map(service -> BookingRequestModel.Service.newBuilder()
                                .setBoarding(service)
                                .build())
                        .toList()));
    }

    private Map<Long, List<BookingRequestModel.Service>> buildDaycareServices(
            List<Long> bookingRequestIds, Set<BookingRequestAssociatedModel> models) {
        Map<Long, Map<Long, BookingRequestModel.DaycareService.Builder>> builderMap = new HashMap<>();
        if (models.contains(BookingRequestAssociatedModel.SERVICE)) {
            var daycareWaitlistByServiceIdId =
                    waitlistService.daycareMapByServiceDetailIdWithBookingRquestId(bookingRequestIds);
            daycareServiceDetailService.listByBookingRequestId(bookingRequestIds).stream()
                    .map(DaycareServiceDetailConverter.INSTANCE::entityToModel)
                    .forEach(serviceDetail -> {
                        var builder = builderMap
                                .computeIfAbsent(serviceDetail.getBookingRequestId(), id -> new HashMap<>())
                                .computeIfAbsent(
                                        serviceDetail.getId(), id -> BookingRequestModel.DaycareService.newBuilder());
                        builder.setService(serviceDetail);
                        if (daycareWaitlistByServiceIdId.containsKey(serviceDetail.getId())) {
                            builder.setWaitlist(DaycareServiceWaitlistConverter.INSTANCE.entityToModel(
                                    daycareWaitlistByServiceIdId.get(serviceDetail.getId())));
                        }
                    });
        }
        if (models.contains(BookingRequestAssociatedModel.ADD_ON)) {
            daycareAddOnDetailService
                    .listByBookingRequestId(bookingRequestIds)
                    .forEach((bookingRequestId, serviceAddOnsMap) ->
                            serviceAddOnsMap.forEach((serviceDetailId, addOns) -> builderMap
                                    .computeIfAbsent(bookingRequestId, id -> new HashMap<>())
                                    .computeIfAbsent(
                                            serviceDetailId, id -> BookingRequestModel.DaycareService.newBuilder())
                                    .addAllAddons(addOns.stream()
                                            .map(DaycareAddOnDetailConverter.INSTANCE::entityToModel)
                                            .toList())));
        }
        if (models.contains(BookingRequestAssociatedModel.FEEDING)) {
            feedingService
                    .listByBookingRequestId(bookingRequestIds, ServiceItemType.DAYCARE_VALUE)
                    .forEach((bookingRequestId, bookingRequestFeedings) ->
                            bookingRequestFeedings.forEach((serviceDetailId, feedings) -> {
                                var detail = builderMap
                                        .computeIfAbsent(bookingRequestId, id -> new HashMap<>())
                                        .computeIfAbsent(
                                                serviceDetailId, id -> BookingRequestModel.DaycareService.newBuilder());
                                detail.addAllFeedings(FeedingConverter.INSTANCE.entityToModel(feedings));
                                if (!isEmpty(feedings)) {
                                    detail.setFeeding(FeedingConverter.INSTANCE.entityToModel(feedings.get(0)));
                                }
                            }));
        }
        if (models.contains(BookingRequestAssociatedModel.MEDICATION)) {
            medicationService
                    .listByBookingRequestId(bookingRequestIds, ServiceItemType.DAYCARE_VALUE)
                    .forEach((bookingRequestId, bookingRequestMedications) -> {
                        bookingRequestMedications.forEach((serviceDetailId, medications) -> {
                            var detail = builderMap
                                    .computeIfAbsent(bookingRequestId, id -> new HashMap<>())
                                    .computeIfAbsent(
                                            serviceDetailId, id -> BookingRequestModel.DaycareService.newBuilder());

                            detail.addAllMedications(MedicationConverter.INSTANCE.entityToModel(medications));
                            if (!isEmpty(medications)) {
                                detail.setMedication(MedicationConverter.INSTANCE.entityToModel(medications.get(0)));
                            }
                        });
                    });
        }
        return builderMap.entrySet().stream()
                .collect(Collectors.toMap(Map.Entry::getKey, entry -> entry.getValue().values().stream()
                        .map(BookingRequestModel.DaycareService.Builder::build)
                        .map(service -> BookingRequestModel.Service.newBuilder()
                                .setDaycare(service)
                                .build())
                        .toList()));
    }

    private Map<Long, List<BookingRequestModel.Service>> buildEvaluationServices(
            List<Long> bookingRequestIds, Set<BookingRequestAssociatedModel> models) {
        Map<Long, Map<Long, BookingRequestModel.EvaluationService.Builder>> builderMap = new HashMap<>();
        if (models.contains(BookingRequestAssociatedModel.SERVICE)) {
            evaluationTestDetailService.listByBookingRequestId(bookingRequestIds).stream()
                    .map(EvaluationTestDetailConverter.INSTANCE::entityToModel)
                    .forEach(serviceDetail -> builderMap
                            .computeIfAbsent(serviceDetail.getBookingRequestId(), id -> new HashMap<>())
                            .computeIfAbsent(
                                    serviceDetail.getId(), id -> BookingRequestModel.EvaluationService.newBuilder())
                            .setService(serviceDetail));
        }
        return builderMap.entrySet().stream()
                .collect(Collectors.toMap(Map.Entry::getKey, entry -> entry.getValue().values().stream()
                        .map(BookingRequestModel.EvaluationService.Builder::build)
                        .map(service -> BookingRequestModel.Service.newBuilder()
                                .setEvaluation(service)
                                .build())
                        .toList()));
    }

    private Map<Long, List<BookingRequestModel.Service>> buildDogWalkingServices(
            List<Long> bookingRequestIds, Set<BookingRequestAssociatedModel> models) {
        if (isEmpty(bookingRequestIds)) {
            return Map.of();
        }

        var builderMap = new HashMap<Long, Map<Long, BookingRequestModel.DogWalkingService.Builder>>();
        if (models.contains(BookingRequestAssociatedModel.SERVICE)) {
            dogWalkingServiceDetailService
                    .listByBookingRequestId(bookingRequestIds)
                    .forEach(service -> {
                        var model = DogWalkingServiceDetailConverter.INSTANCE.entityToModel(service);
                        builderMap
                                .computeIfAbsent(model.getBookingRequestId(), id -> new HashMap<>())
                                .computeIfAbsent(
                                        model.getId(), id -> BookingRequestModel.DogWalkingService.newBuilder())
                                .setService(model);
                    });
        }

        return builderMap.entrySet().stream()
                .collect(Collectors.toMap(Map.Entry::getKey, entry -> entry.getValue().values().stream()
                        .map(BookingRequestModel.DogWalkingService.Builder::build)
                        .map(service -> BookingRequestModel.Service.newBuilder()
                                .setDogWalking(service)
                                .build())
                        .toList()));
    }

    private Map<Long, List<BookingRequestModel.Service>> buildGroupClassServices(
            List<Long> bookingRequestIds, Set<BookingRequestAssociatedModel> models) {
        if (isEmpty(bookingRequestIds)) {
            return Map.of();
        }

        var builderMap = new HashMap<Long, Map<Long, BookingRequestModel.GroupClassService.Builder>>();
        if (models.contains(BookingRequestAssociatedModel.SERVICE)) {
            groupClassServiceDetailService
                    .listByBookingRequestId(bookingRequestIds)
                    .forEach(service -> {
                        var model = GroupClassServiceDetailConverter.INSTANCE.entityToModel(service);
                        builderMap
                                .computeIfAbsent(model.getBookingRequestId(), id -> new HashMap<>())
                                .computeIfAbsent(
                                        model.getId(), id -> BookingRequestModel.GroupClassService.newBuilder())
                                .setService(model);
                    });
        }

        return builderMap.entrySet().stream()
                .collect(Collectors.toMap(Map.Entry::getKey, entry -> entry.getValue().values().stream()
                        .map(BookingRequestModel.GroupClassService.Builder::build)
                        .map(service -> BookingRequestModel.Service.newBuilder()
                                .setGroupClass(service)
                                .build())
                        .toList()));
    }

    /**
     * List services by booking request ids.
     *
     * @param bookingRequests booking requests
     * @param models          associated models
     * @return bookingRequestId -> list of services
     */
    private Map<Long, List<BookingRequestModel.Service>> listServices(
            List<BookingRequest> bookingRequests, Set<BookingRequestAssociatedModel> models) {
        if (isEmpty(bookingRequests) || isEmpty(models)) {
            return Map.of();
        }
        List<Long> bookingRequestIds =
                bookingRequests.stream().map(BookingRequest::getId).toList();
        List<ServiceItemEnum> serviceItems = ServiceItemEnum.convertBitValues(bookingRequests.stream()
                .map(BookingRequest::getServiceTypeInclude)
                .toList());

        var futures = serviceItems.stream()
                .map(serviceItem -> switch (serviceItem) {
                    case GROOMING -> CompletableFuture.supplyAsync(
                            () -> buildGroomingServices(bookingRequests, models));
                    case BOARDING -> CompletableFuture.supplyAsync(
                            () -> buildBoardingServices(bookingRequestIds, models));
                    case DAYCARE -> CompletableFuture.supplyAsync(
                            () -> buildDaycareServices(bookingRequestIds, models));
                    case EVALUATION -> CompletableFuture.supplyAsync(
                            () -> buildEvaluationServices(bookingRequestIds, models));
                    case DOG_WALKING -> CompletableFuture.supplyAsync(
                            () -> buildDogWalkingServices(bookingRequestIds, models));
                    case GROUP_CLASS -> CompletableFuture.supplyAsync(
                            () -> buildGroupClassServices(bookingRequestIds, models));
                })
                .toList();
        CompletableFuture.allOf(futures.toArray(CompletableFuture[]::new)).join();

        return futures.stream()
                .map(CompletableFuture::join)
                .flatMap(map -> map.entrySet().stream())
                .collect(Collectors.groupingBy(
                        Map.Entry::getKey,
                        Collectors.mapping(
                                Map.Entry::getValue, Collectors.flatMapping(List::stream, Collectors.toList()))));
    }

    private String toDateString(Date date) {
        return LocalDate.of(date.getYear(), date.getMonth(), date.getDay()).toString();
    }

    @Override
    public void listBookingRequests(
            ListBookingRequestsRequest request, StreamObserver<ListBookingRequestsResponse> responseObserver) {
        BookingRequestFilterDTO filter = convertBookingRequestToFilter(request);
        // 判断是否内存排序
        boolean isQueryOnlyAvailable = request.hasIsWaitlistAvailable() && request.getIsWaitlistAvailable();
        boolean isOrderPriceDesc = request.hasOrderPriceDesc() && request.getOrderPriceDesc();

        Pagination pagination = PageConverter.INSTANCE.toPagination(request.getPagination());
        if (isQueryOnlyAvailable || isOrderPriceDesc) {
            pagination = Pagination.ALL;
        }

        // query booking request models
        Pair<List<BookingRequest>, Pagination> pair = bookingRequestService.listByBusinessFilter(filter, pagination);
        Set<BookingRequestAssociatedModel> models = new HashSet<>(request.getAssociatedModelsList());
        Map<Long, List<BookingRequestModel.Service>> servicesMap = listServices(pair.getFirst(), models);
        List<BookingRequestModel> bookingRequestModelList = pair.getFirst().stream()
                .map(bookingRequest -> BookingRequestConverter.INSTANCE.entityToModel(bookingRequest).toBuilder()
                        .addAllServices(servicesMap.getOrDefault(bookingRequest.getId(), List.of()))
                        .build())
                .collect(Collectors.toCollection(ArrayList::new));
        pagination = pair.getSecond();
        if (!request.getStatusesList().contains(BookingRequestStatus.WAIT_LIST)) {
            responseObserver.onNext(ListBookingRequestsResponse.newBuilder()
                    .addAllBookingRequests(bookingRequestModelList)
                    .addAllWaitlistExtras(List.of())
                    .setPagination(PageConverter.INSTANCE.toResponse(pagination))
                    .build());
            responseObserver.onCompleted();
            return;
        }

        // query isAvailable
        var waitlistExtras = waitlistService.generateWaitlistExtras(bookingRequestModelList);

        // filter bookingRequestModelList to waitlist available list
        if (isQueryOnlyAvailable) {
            var availableWaitlistIds = waitlistExtras.stream()
                    .filter(WaitlistExtra::getIsAvailable)
                    .map(WaitlistExtra::getId)
                    .toList();
            bookingRequestModelList = bookingRequestModelList.stream()
                    .filter(waitlist -> availableWaitlistIds.contains(waitlist.getId()))
                    .collect(Collectors.toCollection(ArrayList::new));
        }
        // order by list
        if (isOrderPriceDesc) {
            bookingRequestModelList.sort(
                    Comparator.comparingDouble((BookingRequestModel brm) -> brm.getServicesList().stream()
                                    .mapToDouble(s -> {
                                        double sum = 0;
                                        if (s.hasDaycare()) {
                                            sum += s.getDaycare().getService().getServicePrice();
                                        }
                                        if (s.hasBoarding()) {
                                            sum += s.getBoarding().getService().getServicePrice();
                                        }
                                        return sum;
                                    })
                                    .sum())
                            .reversed());
        }
        // 根据分页值手动分页
        if (isQueryOnlyAvailable || isOrderPriceDesc) {
            var paginationOrigin = PageConverter.INSTANCE.toPagination(request.getPagination());
            var pageNum = paginationOrigin.pageNum();
            var pageSize = paginationOrigin.pageSize();
            int total = bookingRequestModelList.size();
            int fromIndex = Math.min((pageNum - 1) * pageSize, total);
            int toIndex = Math.min(fromIndex + pageSize, total);
            bookingRequestModelList = bookingRequestModelList.subList(fromIndex, toIndex);
            pagination = pagination.toBuilder()
                    .pageNum(pageNum)
                    .pageSize(pageSize)
                    .total(total)
                    .build();
        }
        // query comment
        bookingRequestModelList = bookingRequestNoteService.fillWaitlistModelComments(bookingRequestModelList);

        responseObserver.onNext(ListBookingRequestsResponse.newBuilder()
                .addAllBookingRequests(bookingRequestModelList)
                .addAllWaitlistExtras(waitlistExtras)
                .setPagination(PageConverter.INSTANCE.toResponse(pagination))
                .build());
        responseObserver.onCompleted();
    }

    private BookingRequestFilterDTO convertBookingRequestToFilter(ListBookingRequestsRequest request) {
        var filter = new BookingRequestFilterDTO()
                .setBusinessIds(Stream.concat(Stream.of(request.getBusinessId()), request.getBusinessIdsList().stream())
                        .filter(id -> id != 0)
                        .toList())
                .setStatuses(request.getStatusesList().stream()
                        .map(BookingRequestStatus::getNumber)
                        .toList())
                .setStartDate(request.hasStartDate() ? toDateString(request.getStartDate()) : null)
                .setEndDate(request.hasEndDate() ? toDateString(request.getEndDate()) : null)
                .setLatestEndDate(request.hasLatestEndDate() ? toDateString(request.getLatestEndDate()) : null)
                .setOrderBys(request.getOrderBysList())
                .setServiceItems(request.getServiceItemsList())
                .setServiceTypeIncludes(request.getServiceTypeIncludesList())
                .setCompanyId(request.hasCompanyId() ? request.getCompanyId() : null)
                .setCustomerIds(request.getCustomerIdList())
                .setAppointmentIds(request.getAppointmentIdsList())
                .setPaymentStatuses(request.getPaymentStatusesList())
                .setIsWaitlistExpired(request.hasIsWaitlistExpired() && request.getIsWaitlistExpired());
        if (request.hasSource() || request.getSourcesCount() > 0) {
            Set<BookingRequestModel.Source> sources = new HashSet<>(request.getSourcesList());
            if (request.hasSource()) {
                sources.add(request.getSource());
            }
            filter.setSources(sources.stream().toList());
        }
        return filter;
    }

    @Override
    public void listWaitlists(ListWaitlistsRequest request, StreamObserver<ListWaitlistsResponse> responseObserver) {
        var filter = convertBookingRequestToFilter(BookingRequestConverter.INSTANCE.convertRequest(request));
        // 判断是否内存排序
        boolean isQueryOnlyAvailable = request.hasIsWaitlistAvailable() && request.getIsWaitlistAvailable();
        boolean isOrderPriceDesc = request.hasOrderPriceDesc() && request.getOrderPriceDesc();

        Pagination pagination = PageConverter.INSTANCE.toPagination(request.getPagination());
        if (isQueryOnlyAvailable || isOrderPriceDesc) {
            pagination = Pagination.ALL;
        }

        // query booking request models
        Pair<List<BookingRequest>, Pagination> pair = bookingRequestService.listByBusinessFilter(filter, pagination);
        Set<BookingRequestAssociatedModel> models = new HashSet<>(request.getAssociatedModelsList());
        Map<Long, List<BookingRequestModel.Service>> servicesMap = listServices(pair.getFirst(), models);
        List<BookingRequestModel> bookingRequestModelList = pair.getFirst().stream()
                .map(bookingRequest -> BookingRequestConverter.INSTANCE.entityToModel(bookingRequest).toBuilder()
                        .addAllServices(servicesMap.getOrDefault(bookingRequest.getId(), List.of()))
                        .build())
                .collect(Collectors.toCollection(ArrayList::new));
        pagination = pair.getSecond();

        // query isAvailable
        var waitlistExtras = waitlistService.generateWaitlistExtras(bookingRequestModelList);

        // filter bookingRequestModelList to waitlist available list
        if (isQueryOnlyAvailable) {
            var availableWaitlistIds = waitlistExtras.stream()
                    .filter(WaitlistExtra::getIsAvailable)
                    .map(WaitlistExtra::getId)
                    .toList();
            bookingRequestModelList = bookingRequestModelList.stream()
                    .filter(waitlist -> availableWaitlistIds.contains(waitlist.getId()))
                    .collect(Collectors.toCollection(ArrayList::new));
        }
        // order by list
        if (isOrderPriceDesc) {
            bookingRequestModelList.sort(
                    Comparator.comparingDouble((BookingRequestModel brm) -> brm.getServicesList().stream()
                                    .mapToDouble(s -> {
                                        double sum = 0;
                                        if (s.hasDaycare()) {
                                            sum += s.getDaycare().getService().getServicePrice();
                                        }
                                        if (s.hasBoarding()) {
                                            sum += s.getBoarding().getService().getServicePrice();
                                        }
                                        return sum;
                                    })
                                    .sum())
                            .reversed());
        }
        // 根据分页值手动分页
        if (isQueryOnlyAvailable || isOrderPriceDesc) {
            var paginationOrigin = PageConverter.INSTANCE.toPagination(request.getPagination());
            var pageNum = paginationOrigin.pageNum();
            var pageSize = paginationOrigin.pageSize();
            int total = bookingRequestModelList.size();
            int fromIndex = Math.min((pageNum - 1) * pageSize, total);
            int toIndex = Math.min(fromIndex + pageSize, total);
            bookingRequestModelList = bookingRequestModelList.subList(fromIndex, toIndex);
            pagination = pagination.toBuilder()
                    .pageNum(pageNum)
                    .pageSize(pageSize)
                    .total(total)
                    .build();
        }
        // query comment
        bookingRequestModelList = bookingRequestNoteService.fillWaitlistModelComments(bookingRequestModelList);

        responseObserver.onNext(ListWaitlistsResponse.newBuilder()
                .addAllBookingRequests(bookingRequestModelList)
                .addAllWaitlistExtras(waitlistExtras)
                .setPagination(PageConverter.INSTANCE.toResponse(pagination))
                .build());
        responseObserver.onCompleted();
    }

    private void addService(CreateBookingRequestRequest.Service service, long bookingRequestId) {
        switch (service.getServiceCase()) {
            case GROOMING -> addGroomingService(service.getGrooming(), bookingRequestId);
            case BOARDING -> addBoardingService(service.getBoarding(), bookingRequestId);
            case DAYCARE -> addDaycareService(service.getDaycare(), bookingRequestId);
            case EVALUATION -> addEvaluationService(service.getEvaluation(), bookingRequestId);
            case DOG_WALKING -> addDogWalkingService(service.getDogWalking(), bookingRequestId);
            case GROUP_CLASS -> addGroupClassService(service.getGroupClass(), bookingRequestId);
            default -> throw bizException(Code.CODE_PARAMS_ERROR, "Invalid service type: " + service.getServiceCase());
        }
    }

    private void addGroupClassService(CreateBookingRequestRequest.GroupClassService groupClass, long bookingRequestId) {
        var serviceDetail = GroupClassServiceDetailConverter.INSTANCE.createRequestToEntity(groupClass.getService());
        serviceDetail.setBookingRequestId(bookingRequestId);
        groupClassServiceDetailService.insert(serviceDetail);
    }

    private void addDogWalkingService(CreateBookingRequestRequest.DogWalkingService dogWalking, long bookingRequestId) {
        var dogWalkingServiceDetail =
                DogWalkingServiceDetailConverter.INSTANCE.createRequestToEntity(dogWalking.getService());
        dogWalkingServiceDetail.setBookingRequestId(bookingRequestId);
        dogWalkingServiceDetailService.insert(dogWalkingServiceDetail);
    }

    private void addGroomingService(CreateBookingRequestRequest.GroomingService grooming, long bookingRequestId) {
        GroomingServiceDetail groomingServiceDetail =
                GroomingServiceDetailConverter.INSTANCE.createRequestToEntity(grooming.getService());
        groomingServiceDetail.setBookingRequestId(bookingRequestId);
        groomingServiceDetailService.insert(groomingServiceDetail);

        for (var addon : grooming.getAddonsV2List()) {
            var groomingAddOnDetail = GroomingAddOnDetailConverter.INSTANCE.createRequestToEntity(addon);
            groomingAddOnDetail.setBookingRequestId(bookingRequestId);
            groomingAddOnDetail.setServiceDetailId(groomingServiceDetail.getId());
            groomingAddOnDetailService.insert(groomingAddOnDetail);
        }
    }

    private void addEvaluationService(CreateBookingRequestRequest.EvaluationService evaluation, long bookingRequestId) {
        EvaluationTestDetail evaluationTestDetail =
                EvaluationTestDetailConverter.INSTANCE.createRequestToEntity(evaluation.getService());
        evaluationTestDetail.setBookingRequestId(bookingRequestId);
        evaluationTestDetailService.insert(evaluationTestDetail);
    }

    private void addDaycareService(CreateBookingRequestRequest.DaycareService daycare, long bookingRequestId) {
        long serviceDetailId = daycareServiceDetailService.insert(buildDaycareServiceDetail(daycare, bookingRequestId));

        daycare.getAddonsList().forEach(addon -> {
            DaycareAddOnDetail daycareAddOnDetail = DaycareAddOnDetailConverter.INSTANCE.createRequestToEntity(addon);
            daycareAddOnDetail.setBookingRequestId(bookingRequestId);
            daycareAddOnDetail.setServiceDetailId(serviceDetailId);
            daycareAddOnDetailService.insert(daycareAddOnDetail);
        });

        var feedings = new ArrayList<>(daycare.getFeedingsList());
        var medications = new ArrayList<>(daycare.getMedicationsList());
        if (daycare.hasFeeding()) {
            feedings.add(daycare.getFeeding());
        }
        if (daycare.hasMedication()) {
            medications.add(daycare.getMedication());
        }
        feedingService.insertMultiple(FeedingConverter.INSTANCE.createRequestToEntity(
                feedings, bookingRequestId, serviceDetailId, ServiceItemType.DAYCARE));
        medicationService.insertMultiple(MedicationConverter.INSTANCE.createRequestToEntity(
                medications, bookingRequestId, serviceDetailId, ServiceItemType.DAYCARE));

        if (daycare.hasWaitlist()) {
            var waitlist = daycare.getWaitlist();
            var daycareServiceWaitlist = new DaycareServiceWaitlist();
            daycareServiceWaitlist.setBookingRequestId(bookingRequestId);
            daycareServiceWaitlist.setServiceDetailId(serviceDetailId);
            daycareServiceWaitlist.setSpecificDates(waitlist.getSpecificDatesList().stream()
                    .map(ProtobufUtil::toLocalDate)
                    .toList());
            waitlistService.insertDaycareServiceWaitlist(daycareServiceWaitlist);
        }
    }

    private static DaycareServiceDetail buildDaycareServiceDetail(
            CreateBookingRequestRequest.DaycareService daycare, long bookingRequestId) {
        DaycareServiceDetail daycareServiceDetail =
                DaycareServiceDetailConverter.INSTANCE.createRequestToEntity(daycare.getService());
        daycareServiceDetail.setBookingRequestId(bookingRequestId);

        if (daycare.hasWaitlist()) {
            // 当 service 为 waitlist 时，使用 waitlist 的 specificDates
            // 有一些逻辑依赖（比如创建 order）service 的 specificDates，所以这里要保证它们不为空
            var dates = daycare.getWaitlist().getSpecificDatesList().stream()
                    .map(ProtobufUtil::toLocalDate)
                    .toList();
            daycareServiceDetail.setSpecificDates(JsonUtil.toJson(dates));
        }

        return daycareServiceDetail;
    }

    private void addBoardingService(CreateBookingRequestRequest.BoardingService boarding, long bookingRequestId) {
        long serviceDetailId =
                boardingServiceDetailService.insert(buildBoardingServiceDetail(boarding, bookingRequestId));

        boarding.getAddonsList().forEach(addon -> {
            BoardingAddOnDetail boardingAddOnDetail =
                    BoardingAddOnDetailConverter.INSTANCE.createRequestToEntity(addon);
            boardingAddOnDetail.setBookingRequestId(bookingRequestId);
            boardingAddOnDetail.setServiceDetailId(serviceDetailId);
            boardingAddOnDetailService.insert(boardingAddOnDetail);
        });

        var feedings = new ArrayList<>(boarding.getFeedingsList());
        var medications = new ArrayList<>(boarding.getMedicationsList());
        if (boarding.hasFeeding()) {
            feedings.add(boarding.getFeeding());
        }
        if (boarding.hasMedication()) {
            medications.add(boarding.getMedication());
        }
        feedingService.insertMultiple(FeedingConverter.INSTANCE.createRequestToEntity(
                feedings, bookingRequestId, serviceDetailId, ServiceItemType.BOARDING));
        medicationService.insertMultiple(MedicationConverter.INSTANCE.createRequestToEntity(
                medications, bookingRequestId, serviceDetailId, ServiceItemType.BOARDING));

        if (boarding.hasWaitlist()) {
            var waitlist = boarding.getWaitlist();
            var boardingServiceWaitlist = new BoardingServiceWaitlist();
            boardingServiceWaitlist.setBookingRequestId(bookingRequestId);
            boardingServiceWaitlist.setServiceDetailId(serviceDetailId);
            boardingServiceWaitlist.setStartDate(ProtobufUtil.toLocalDate(waitlist.getStartDate()));
            boardingServiceWaitlist.setEndDate(ProtobufUtil.toLocalDate(waitlist.getEndDate()));
            waitlistService.insertBoardingServiceWaitlist(boardingServiceWaitlist);
        }
    }

    private static BoardingServiceDetail buildBoardingServiceDetail(
            CreateBookingRequestRequest.BoardingService boarding, long bookingRequestId) {
        BoardingServiceDetail boardingServiceDetail =
                BoardingServiceDetailConverter.INSTANCE.createRequestToEntity(boarding.getService());
        boardingServiceDetail.setBookingRequestId(bookingRequestId);

        if (boarding.hasWaitlist()) {
            // 当 service 为 waitlist 时，使用 waitlist 的 startDate 和 endDate
            // 有一些逻辑依赖（比如创建 order）service 的 startDate 和 endDate，所以这里要保证它们不为空
            var waitlist = boarding.getWaitlist();
            boardingServiceDetail.setStartDate(
                    ProtobufUtil.toLocalDate(waitlist.getStartDate()).toString());
            boardingServiceDetail.setEndDate(
                    ProtobufUtil.toLocalDate(waitlist.getEndDate()).toString());
        }

        return boardingServiceDetail;
    }

    private static BookingRequest toBookingRequest(CreateBookingRequestRequest request) {
        BookingRequest entity = BookingRequestConverter.INSTANCE.createRequestToEntity(request);

        if (hasWaitlist(request)) {
            entity.setStatus(BookingRequestStatus.WAIT_LIST);
        }

        return entity;
    }

    private static boolean hasWaitlist(CreateBookingRequestRequest request) {
        return request.getServicesList().stream().anyMatch(e -> switch (e.getServiceCase()) {
            case BOARDING -> e.getBoarding().hasWaitlist();
            case DAYCARE -> e.getDaycare().hasWaitlist();
                // Add other service types here
            default -> false;
        });
    }

    private static void setDateAndTime(BookingRequest entity, CreateBookingRequestRequest request) {
        List<DatePoint> starts = new ArrayList<>();
        List<DatePoint> ends = new ArrayList<>();
        addDatePoints(request, starts, ends);

        DatePoint min = DatePoint.min(starts);
        DatePoint max = DatePoint.max(ends);

        entity.setStartDate(min.date());
        entity.setStartTime(min.time());
        entity.setEndDate(max.date());
        entity.setEndTime(max.time());
    }

    private static void addDatePoints(
            CreateBookingRequestRequest request, List<DatePoint> starts, List<DatePoint> ends) {
        for (CreateBookingRequestRequest.Service service : request.getServicesList()) {
            switch (service.getServiceCase()) {
                case GROOMING -> {
                    var grooming = service.getGrooming().getService();

                    String startDate = grooming.getStartDate();
                    int startTime = grooming.getStartTime();

                    LocalDateTime ldt = LocalDateTime.of(
                                    LocalDate.parse(startDate), LocalTime.ofSecondOfDay(startTime * 60L))
                            .plusMinutes(grooming.getServiceTime());

                    starts.add(new DatePoint(startDate, startTime));
                    ends.add(new DatePoint(
                            ldt.toLocalDate().toString(), ldt.toLocalTime().toSecondOfDay() / 60));
                }
                case BOARDING -> {
                    CreateBoardingServiceDetailRequest boarding =
                            service.getBoarding().getService();
                    String startDate = boarding.getStartDate();
                    String endDate = boarding.getEndDate();
                    Integer startTime = boarding.getStartTime();
                    Integer endTime = boarding.getEndTime();
                    starts.add(new DatePoint(startDate, startTime));
                    ends.add(new DatePoint(endDate, endTime));
                }
                case DAYCARE -> {
                    CreateDaycareServiceDetailRequest daycare =
                            service.getDaycare().getService();

                    List<String> dates =
                            daycare.getSpecificDatesList().stream().sorted().toList();
                    String startDate = firstElement(dates);
                    String endDate = lastElement(dates);
                    Integer startTime = daycare.getStartTime();
                    Integer endTime = daycare.getEndTime();
                    starts.add(new DatePoint(startDate, startTime));
                    ends.add(new DatePoint(endDate, endTime));
                }
                case EVALUATION -> {
                    CreateEvaluationTestDetailRequest evaluation =
                            service.getEvaluation().getService();

                    String startDate = evaluation.getStartDate();
                    int startTime = evaluation.getStartTime();

                    LocalDateTime ldt = LocalDateTime.of(
                                    LocalDate.parse(startDate), LocalTime.ofSecondOfDay(startTime * 60L))
                            .plusMinutes(evaluation.getDuration());

                    starts.add(new DatePoint(startDate, startTime));
                    ends.add(new DatePoint(
                            ldt.toLocalDate().toString(), ldt.toLocalTime().toSecondOfDay() / 60));
                }
                case DOG_WALKING -> {
                    var dogWalking = service.getDogWalking().getService();

                    var startDate = dogWalking.getStartDate();
                    var startTime = dogWalking.getStartTime();

                    var ldt = LocalDateTime.of(LocalDate.parse(startDate), LocalTime.ofSecondOfDay(startTime * 60L))
                            .plusMinutes(dogWalking.getServiceTime());

                    starts.add(new DatePoint(startDate, startTime));
                    ends.add(new DatePoint(
                            ldt.toLocalDate().toString(), ldt.toLocalTime().toSecondOfDay() / 60));
                }
                case GROUP_CLASS -> {
                    var groupClass = service.getGroupClass().getService();

                    List<String> dates =
                            groupClass.getSpecificDatesList().stream().sorted().toList();
                    String startDate = firstElement(dates);
                    String endDate = lastElement(dates);
                    Integer startTime = groupClass.getStartTime();
                    Integer endTime = groupClass.getEndTime();
                    starts.add(new DatePoint(startDate, startTime));
                    ends.add(new DatePoint(endDate, endTime));
                }
                default -> {
                    // no-op
                }
            }
        }
    }

    private record DatePoint(String date, Integer time) {
        public static DatePoint max(List<DatePoint> datePoints) {
            return datePoints.stream()
                    .max(comparing((DatePoint a) -> a.date, nullsFirst(naturalOrder()))
                            .thenComparing(a -> a.time, nullsFirst(naturalOrder())))
                    .orElseThrow(() -> bizException(Code.CODE_PARAMS_ERROR, "Can't find max date time"));
        }

        public static DatePoint min(List<DatePoint> datePoints) {
            return datePoints.stream()
                    .min(comparing((DatePoint a) -> a.date, nullsLast(naturalOrder()))
                            .thenComparing(a -> a.time, nullsLast(naturalOrder())))
                    .orElseThrow(() -> bizException(Code.CODE_PARAMS_ERROR, "Can't find min date time"));
        }
    }

    @Override
    public void updateBookingRequestStatus(
            UpdateBookingRequestStatusRequest request,
            StreamObserver<UpdateBookingRequestStatusResponse> responseObserver) {
        int affectedRows = bookingRequestService.updateStatus(request.getId(), request.getStatus());

        responseObserver.onNext(UpdateBookingRequestStatusResponse.newBuilder()
                .setUpdatedResult(affectedRows != 0)
                .build());
        responseObserver.onCompleted();
    }

    @Override
    public void updateBookingRequest(
            UpdateBookingRequestRequest request, StreamObserver<UpdateBookingRequestResponse> responseObserver) {

        transaction.execute(status -> {
            var bookingRequest = BookingRequestConverter.INSTANCE.updateRequestToEntity(request);
            bookingRequestService.update(bookingRequest);

            request.getServicesList().forEach(s -> updateService(bookingRequest.getId(), s));

            if (request.hasComment()) {
                var bookingRequestBean = bookingRequestService.get(request.getId());
                if (bookingRequestBean != null) {
                    bookingRequestNoteService.saveBookingRequestNote(
                            bookingRequestBean.getCompanyId(),
                            bookingRequestBean.getCustomerId(),
                            request.getId(),
                            request.getComment());
                }
            }
            return bookingRequest.getId();
        });

        if (!isEmpty(request.getServicesList())) {
            refreshBookingOnlineDateTime(request.getId());
        }
        responseObserver.onNext(UpdateBookingRequestResponse.getDefaultInstance());
        responseObserver.onCompleted();
    }

    @Override
    public void replaceBookingRequest(
            ReplaceBookingRequestRequest request, StreamObserver<ReplaceBookingRequestResponse> responseObserver) {

        transaction.execute(status -> {
            var bookingRequest = BookingRequestConverter.INSTANCE.replaceRequestToEntity(request);
            bookingRequestService.update(bookingRequest);

            replaceSaveService(request.getId(), request.getServicesList());

            if (StringUtils.hasText(request.getStartDate())) {
                waitlistService.updateWaitlistByStartDateEndDate(
                        request.getId(), request.getStartDate(), request.getEndDate());
            }

            if (request.hasComment()) {
                var bookingRequestBean = bookingRequestService.get(request.getId());
                if (bookingRequestBean != null) {
                    bookingRequestNoteService.saveBookingRequestNote(
                            bookingRequestBean.getCompanyId(),
                            bookingRequestBean.getCustomerId(),
                            request.getId(),
                            request.getComment());
                }
            }
            return bookingRequest.getId();
        });

        ThreadPool.execute(() -> {
            if (!isEmpty(request.getServicesList())) {
                refreshBookingOnlineDateTime(request.getId());
            }
        });

        responseObserver.onNext(ReplaceBookingRequestResponse.getDefaultInstance());
        responseObserver.onCompleted();
    }

    private void replaceSaveService(long bookingRequestId, List<ReplaceBookingRequestRequest.Service> serviceList) {
        if (isEmpty(serviceList)) {
            return;
        }
        List<ReplaceBookingRequestRequest.BoardingService> boardingServiceList = new ArrayList<>();
        List<ReplaceBookingRequestRequest.DaycareService> daycareServicesList = new ArrayList<>();
        // boarding && daycare
        for (var service : serviceList) {
            if (service.hasBoarding()) {
                boardingServiceList.add(service.getBoarding());
            }
            if (service.hasDaycare()) {
                daycareServicesList.add(service.getDaycare());
            }
        }
        if (isEmpty(boardingServiceList) && isEmpty(daycareServicesList)) {
            throw bizException(Code.CODE_PARAMS_ERROR, "Please select at least one service: boarding or daycare.");
        }

        replaceBoardingService(bookingRequestId, boardingServiceList);
        replaceDaycareService(bookingRequestId, daycareServicesList);
    }

    private static String getPetServiceKey(long petId, long serviceId) {
        return petId + "_" + serviceId;
    }

    private void replaceBoardingService(
            long bookingRequestId, List<ReplaceBookingRequestRequest.BoardingService> newBoardingServiceList) {
        // 获取当前 booking request 的 boarding service detail
        var existedBoardingServiceDetails = boardingServiceDetailService.listByBookingRequestId(bookingRequestId);
        if (isEmpty(newBoardingServiceList) && isEmpty(existedBoardingServiceDetails)) {
            return;
        }
        // key 等于 petid+serviceid
        Map<String, BoardingServiceDetail> existedBoardingServiceDetailMap = existedBoardingServiceDetails.stream()
                .collect(Collectors.toMap(e -> getPetServiceKey(e.getPetId(), e.getServiceId()), Function.identity()));

        // 遍历新的服务列表
        for (ReplaceBookingRequestRequest.BoardingService newService : newBoardingServiceList) {
            String key = getPetServiceKey(
                    newService.getService().getPetId(), newService.getService().getServiceId());
            BoardingServiceDetail existedService = existedBoardingServiceDetailMap.get(key);
            // convert to bean
            BoardingServiceDetail boardingServiceDetail =
                    BoardingServiceDetailConverter.INSTANCE.createRequestToEntity(newService.getService());
            Long serviceDetailId;
            if (existedService == null) {
                // 新增服务
                boardingServiceDetail.setBookingRequestId(bookingRequestId);
                boardingServiceDetailService.insert(boardingServiceDetail);
                serviceDetailId = boardingServiceDetail.getId();
            } else {
                // 更新服务
                boardingServiceDetail.setId(existedService.getId());
                boardingServiceDetailService.update(boardingServiceDetail);
                serviceDetailId = existedService.getId();
                // 从已存在map中移除，剩下的就是要删除的
                existedBoardingServiceDetailMap.remove(key);
            }
            // 有 waitlist 就保存
            if (newService.hasWaitlist()) {
                var waitlist = newService.getWaitlist();
                waitlistService.updateBoardingWaitlist(
                        bookingRequestId,
                        serviceDetailId,
                        ProtobufUtil.toLocalDate(waitlist.getStartDate()),
                        ProtobufUtil.toLocalDate(waitlist.getEndDate()));
            }
        }
        existedBoardingServiceDetailMap.values().stream()
                .mapToLong(BoardingServiceDetail::getId)
                .forEach(boardingServiceDetailService::delete);
    }

    private void replaceDaycareService(
            long bookingRequestId, List<ReplaceBookingRequestRequest.DaycareService> daycareServicesList) {
        // 获取当前 booking request 的 daycare service detail
        var existedDaycareServiceDetails = daycareServiceDetailService.listByBookingRequestId(bookingRequestId);
        if (isEmpty(daycareServicesList) && isEmpty(existedDaycareServiceDetails)) {
            return;
        }
        // key 等于 petid+serviceid
        Map<String, DaycareServiceDetail> existedDaycareServiceDetailMap = existedDaycareServiceDetails.stream()
                .collect(Collectors.toMap(e -> getPetServiceKey(e.getPetId(), e.getServiceId()), Function.identity()));

        // 遍历新的服务列表
        for (ReplaceBookingRequestRequest.DaycareService newService : daycareServicesList) {
            String key = getPetServiceKey(
                    newService.getService().getPetId(), newService.getService().getServiceId());
            DaycareServiceDetail existedService = existedDaycareServiceDetailMap.get(key);
            // convert to bean
            var daycareServiceDetail =
                    DaycareServiceDetailConverter.INSTANCE.createRequestToEntity(newService.getService());
            Long serviceDetailId;
            if (existedService == null) {
                // 新增服务
                daycareServiceDetail.setBookingRequestId(bookingRequestId);
                daycareServiceDetailService.insert(daycareServiceDetail);
                serviceDetailId = daycareServiceDetail.getId();
            } else {
                // 更新服务
                daycareServiceDetail.setId(existedService.getId());
                daycareServiceDetailService.update(daycareServiceDetail);
                serviceDetailId = existedService.getId();
                // 从已存在map中移除，剩下的就是要删除的
                existedDaycareServiceDetailMap.remove(key);
            }
            // 有 waitlist 就保存
            if (newService.hasWaitlist()) {
                waitlistService.updateDaycareWaitlist(
                        bookingRequestId,
                        serviceDetailId,
                        newService.getWaitlist().getSpecificDatesList());
            }
        }
        existedDaycareServiceDetailMap.values().stream()
                .mapToLong(DaycareServiceDetail::getId)
                .forEach(daycareServiceDetailService::delete);
    }

    private void updateService(Long bookingRequestId, UpdateBookingRequestRequest.Service service) {
        switch (service.getServiceCase()) {
            case BOARDING -> updateBoardingService(bookingRequestId, service.getBoarding());
            case DAYCARE -> updateDaycareService(bookingRequestId, service.getDaycare());
            case GROOMING -> updateGroomingService(service.getGrooming());
            default -> throw bizException(Code.CODE_PARAMS_ERROR, "Invalid service type: " + service.getServiceCase());
        }
    }

    private void updateGroomingService(UpdateBookingRequestRequest.GroomingService grooming) {
        var groomingServiceDetail =
                GroomingServiceDetailConverter.INSTANCE.updateRequestToEntity(grooming.getService());
        groomingServiceDetailService.update(groomingServiceDetail);

        grooming.getAddonsList().forEach(addon -> {
            var groomingAddOnDetail = GroomingAddOnDetailConverter.INSTANCE.updateRequestToEntity(addon);
            groomingAddOnDetailService.update(groomingAddOnDetail);
        });

        if (grooming.hasAutoAssign()) {
            var groomingAutoAssign =
                    GroomingAutoAssignConverter.INSTANCE.upsertRequestToEntity(grooming.getAutoAssign());
            // TODO 未来支持多个 auto assign 记录时，需要根据 service detail id 更新
            groomingAutoAssign.setGroomingServiceDetailId(0L);
            groomingAutoAssignService.upsertByGroomingServiceDetailId(groomingAutoAssign);
        }
    }

    private void updateBoardingService(Long bookingRequestId, UpdateBookingRequestRequest.BoardingService boarding) {
        var serviceDetailToUpdate =
                BoardingServiceDetailConverter.INSTANCE.updateRequestToEntity(boarding.getService());
        boardingServiceDetailService.update(serviceDetailToUpdate);
        boarding.getAddonsList().forEach(addon -> {
            var addOnDetailToUpdate = BoardingAddOnDetailConverter.INSTANCE.updateRequestToEntity(addon);
            boardingAddOnDetailService.update(addOnDetailToUpdate);
        });

        long serviceDetailId = boarding.getService().getId();
        if (boarding.hasFeedingsUpsert()) {
            var toDelIds = feedingService.listByBookingRequestId(bookingRequestId).stream()
                    .filter(k -> k.getServiceDetailId().equals(serviceDetailId))
                    .map(k -> k.getId())
                    .toList();
            feedingService.delete(toDelIds);
            feedingService.insertMultiple(FeedingConverter.INSTANCE.createRequestToEntity(
                    boarding.getFeedingsUpsert().getValuesList(),
                    bookingRequestId,
                    serviceDetailId,
                    ServiceItemType.BOARDING));
        }
        if (boarding.hasMedicationsUpsert()) {
            var toDelIds = medicationService.listByBookingRequestId(bookingRequestId).stream()
                    .filter(k -> k.getServiceDetailId().equals(serviceDetailId))
                    .map(k -> k.getId())
                    .toList();
            medicationService.delete(toDelIds);
            medicationService.insertMultiple(MedicationConverter.INSTANCE.createRequestToEntity(
                    boarding.getMedicationsUpsert().getValuesList(),
                    bookingRequestId,
                    serviceDetailId,
                    ServiceItemType.BOARDING));
        }
        if (boarding.hasWaitlist()) {
            waitlistService.updateBoardingWaitlist(
                    bookingRequestId,
                    serviceDetailId,
                    ProtobufUtil.toLocalDate(boarding.getWaitlist().getStartDate()),
                    ProtobufUtil.toLocalDate(boarding.getWaitlist().getEndDate()));
        }
    }

    private void updateDaycareService(Long bookingRequestId, UpdateBookingRequestRequest.DaycareService daycare) {
        var serviceDetailToUpdate = DaycareServiceDetailConverter.INSTANCE.updateRequestToEntity(daycare.getService());
        daycareServiceDetailService.update(serviceDetailToUpdate);
        daycare.getAddonsList().forEach(addon -> {
            var addOnDetailToUpdate = DaycareAddOnDetailConverter.INSTANCE.updateRequestToEntity(addon);
            daycareAddOnDetailService.update(addOnDetailToUpdate);
        });

        long serviceDetailId = daycare.getService().getId();
        if (daycare.hasFeedingsUpsert()) {
            var toDelIds = feedingService.listByBookingRequestId(bookingRequestId).stream()
                    .filter(k -> k.getServiceDetailId().equals(serviceDetailId))
                    .map(k -> k.getId())
                    .toList();
            feedingService.delete(toDelIds);
            feedingService.insertMultiple(FeedingConverter.INSTANCE.createRequestToEntity(
                    daycare.getFeedingsUpsert().getValuesList(),
                    bookingRequestId,
                    serviceDetailId,
                    ServiceItemType.DAYCARE));
        }
        if (daycare.hasMedicationsUpsert()) {
            var toDelIds = medicationService.listByBookingRequestId(bookingRequestId).stream()
                    .filter(k -> k.getServiceDetailId().equals(serviceDetailId))
                    .map(k -> k.getId())
                    .toList();
            medicationService.delete(toDelIds);
            medicationService.insertMultiple(MedicationConverter.INSTANCE.createRequestToEntity(
                    daycare.getMedicationsUpsert().getValuesList(),
                    bookingRequestId,
                    serviceDetailId,
                    ServiceItemType.DAYCARE));
        }
        if (daycare.hasWaitlist()) {
            waitlistService.updateDaycareWaitlist(
                    bookingRequestId,
                    serviceDetailId,
                    daycare.getWaitlist().getSpecificDates().getDatesList());
        }
    }

    @Override
    public void retryFailedEvents(
            RetryFailedEventsRequest request, StreamObserver<RetryFailedEventsResponse> responseObserver) {
        List<String> pops = stringRedisTemplate.opsForSet().pop(OBRequestSyncDTO.FAILED_KEY, 100);
        if (!isEmpty(pops)) {
            log.info("Retry failed events: {}", pops);
            pops.forEach(pop -> {
                try {
                    BookingRequestEventParams params = JsonUtil.toBean(pop, BookingRequestEventParams.class);
                    syncBookingRequestListener.processEvent(params);
                } catch (Exception e) {
                    log.error("Failed to process failed event: {}", pop, e);
                    stringRedisTemplate.opsForSet().add(OBRequestSyncDTO.FAILED_KEY, pop);
                }
            });
        }

        responseObserver.onNext(RetryFailedEventsResponse.getDefaultInstance());
        responseObserver.onCompleted();
    }

    @Override
    public void getAutoAssign(GetAutoAssignRequest request, StreamObserver<GetAutoAssignResponse> responseObserver) {
        responseObserver.onNext(getAutoAssignResponse(request));
        responseObserver.onCompleted();
    }

    private GetAutoAssignResponse getAutoAssignResponse(GetAutoAssignRequest request) {
        long companyId = request.getCompanyId();

        BookingRequest bookingRequest = bookingRequestService.get(request.getId());
        if (Objects.isNull(bookingRequest) || !Objects.equals(companyId, bookingRequest.getCompanyId())) {
            throw bizException(Code.CODE_PARAMS_ERROR, "Booking request not found");
        }

        Map<Long, List<BookingRequestModel.Service>> servicesMap = listServices(
                List.of(bookingRequest),
                Set.of(
                        BookingRequestAssociatedModel.SERVICE,
                        BookingRequestAssociatedModel.ADD_ON,
                        BookingRequestAssociatedModel.FEEDING,
                        BookingRequestAssociatedModel.MEDICATION));

        BookingRequestModel bookingRequestModel =
                BookingRequestConverter.INSTANCE.entityToModel(bookingRequest).toBuilder()
                        .addAllServices(servicesMap.getOrDefault(bookingRequest.getId(), List.of()))
                        .build();

        var result = GetAutoAssignResponse.newBuilder();
        doLodgingAssign(bookingRequestModel, result);
        doStaffAssign(bookingRequestModel, result);

        return result.build();
    }

    /**
     * auto assign lodging. Support boarding only
     */
    void doLodgingAssign(BookingRequestModel bookingRequestModel, GetAutoAssignResponse.Builder result) {
        // 获取待分配的 pet service 信息
        List<BoardingServiceDetailModel> boardingServiceDetails =
                PetDetailService.getBoardingServiceDetails(bookingRequestModel);
        List<DaycareServiceDetailModel> daycareServiceDetails =
                PetDetailService.getDaycareServiceDetails(bookingRequestModel);
        // 没有待分配的信息，直接返回
        if (isEmpty(daycareServiceDetails) && isEmpty(boardingServiceDetails)) {
            return;
        }

        long companyId = bookingRequestModel.getCompanyId();
        long businessId = bookingRequestModel.getBusinessId();

        // 获取 pet、service 信息，用于 lodging available 判断
        Map<Long, BusinessCustomerPetInfoModel> petMap =
                autoAssignService.getPetMap(companyId, PetDetailService.getPetIds(bookingRequestModel));
        List<BusinessPetSizeModel> petSizeList = autoAssignService.getPetSizeList(companyId);
        Map<Long, ServiceBriefView> serviceMap =
                serviceService.getServiceMap(companyId, PetDetailService.getServiceIds(bookingRequestModel));

        // 获取 lodging 信息
        List<LodgingUnitModel> lodgingUnitList = lodgingService.getLodgingUnit(companyId, businessId);
        List<LodgingTypeModel> lodgingTypeList = lodgingService.getLodgingTypeByUnits(lodgingUnitList);
        Map<Long, LodgingUnitModel> lodgingUnitMap =
                lodgingUnitList.stream().collect(Collectors.toMap(LodgingUnitModel::getId, Function.identity()));
        Map<Long, LodgingTypeModel> lodgingTypeMap =
                lodgingTypeList.stream().collect(Collectors.toMap(LodgingTypeModel::getId, Function.identity()));

        // 获取 lodging 使用信息
        List<LodgingAssignInfo> assignInfoList = lodgingService.getLodgingAssignInfo(
                companyId, businessId, bookingRequestModel.getStartDate(), bookingRequestModel.getEndDate());

        List<PetToLodgingDef> petLodgings = AutoAssignService.autoAssign(
                bookingRequestModel, lodgingTypeList, lodgingUnitList, serviceMap, petSizeList, petMap, assignInfoList);

        result.addAllBoardingAssignRequires(AutoAssignConverter.INSTANCE.buildAssignRequire(
                        PetDetailService.getBoardingServiceDetails(bookingRequestModel),
                        PetDetailService.getDaycareServiceDetails(bookingRequestModel)))
                .addAllPetToLodgings(petLodgings)
                .addAllLodgings(AutoAssignConverter.INSTANCE.buildAutoAssignLodgingDetail(
                        petLodgings.stream()
                                .map(PetToLodgingDef::getLodgingUnitId)
                                .distinct()
                                .toList(),
                        lodgingUnitMap,
                        lodgingTypeMap));
    }

    /**
     * auto assign staff. Support evaluation only
     */
    void doStaffAssign(BookingRequestModel bookingRequestModel, GetAutoAssignResponse.Builder result) {
        // 获取待分配的 pet service 信息
        var evaluationServiceDetails = PetDetailService.getEvaluationServiceDetails(bookingRequestModel);
        // 没有待分配的信息，直接返回
        if (isEmpty(evaluationServiceDetails)) {
            return;
        }

        long companyId = bookingRequestModel.getCompanyId();
        long businessId = bookingRequestModel.getBusinessId();

        // 1. 获取 business 可用的 staff
        var staffs = organizationHelper.listStaffForBusiness(companyId, businessId, true);

        // 2. 获取 evaluation 详情
        var evaluations = serviceHelper.getEvaluationByIds(evaluationServiceDetails.stream()
                .map(EvaluationTestDetailModel::getEvaluationId)
                .toList());

        result.addAllEvaluationAssignRequires(
                        AutoAssignConverter.INSTANCE.evaluationToAssignRequire(evaluationServiceDetails, evaluations))
                .addAllEvaluationPetToStaffs(
                        AutoAssignService.autoAssignToStaff(evaluationServiceDetails, evaluations, staffs));
    }

    @Override
    public void acceptBookingRequest(
            AcceptBookingRequestRequest request, StreamObserver<AcceptBookingRequestResponse> responseObserver) {
        responseObserver.onNext(getAcceptBookingRequestResponse(request));
        responseObserver.onCompleted();
    }

    private AcceptBookingRequestResponse getAcceptBookingRequestResponse(AcceptBookingRequestRequest request) {
        Long companyId = request.getCompanyId();
        Long businessId = request.getBusinessId();
        Long staffId = request.hasStaffId() ? request.getStaffId() : 0;

        BookingRequest bookingRequest = bookingRequestService.get(request.getId());
        if (Objects.isNull(bookingRequest) || !Objects.equals(companyId, bookingRequest.getCompanyId())) {
            throw bizException(Code.CODE_PARAMS_ERROR, "Booking request not found");
        }

        Map<Long, List<BookingRequestModel.Service>> servicesMap = listServices(
                List.of(bookingRequest),
                Set.of(
                        BookingRequestAssociatedModel.SERVICE,
                        BookingRequestAssociatedModel.ADD_ON,
                        BookingRequestAssociatedModel.FEEDING,
                        BookingRequestAssociatedModel.MEDICATION));

        BookingRequestModel bookingRequestModel =
                BookingRequestConverter.INSTANCE.entityToModel(bookingRequest).toBuilder()
                        .addAllServices(servicesMap.getOrDefault(bookingRequest.getId(), List.of()))
                        .build();

        // get last lodging unit id
        List<PetToLodgingDef> petToLodgingsList = request.getPetToLodgingsList();
        if (Objects.equals(ServiceItemEnum.DAYCARE, getMainServiceItemType(bookingRequest.getServiceTypeInclude()))) {
            // daycare service 优先 assign last lodging
            List<PetToLodgingDef> petToLastLodgingList =
                    getLastLodgingUnitId(servicesMap, companyId, businessId, bookingRequestModel.getCustomerId());
            if (!isEmpty(petToLastLodgingList)) {
                Map<Long, PetToLodgingDef> merged = new HashMap<>();
                Stream.concat(petToLodgingsList.stream(), petToLastLodgingList.stream())
                        .forEach(def -> merged.put(def.getPetId(), def));
                petToLodgingsList = new ArrayList<>(merged.values());
            }
        }

        List<Long> appointmentIds = bookingRequestModifyService.createAppointment(
                bookingRequestModel,
                companyId,
                businessId,
                staffId,
                AppointmentStatus.UNCONFIRMED,
                petToLodgingsList,
                request.getPetToStaffsList(),
                request.getEvaluationPetToStaffsList(),
                request.getPetToServicesList());

        // set booking request status
        int affectedRows = bookingRequestService.updateStatus(request.getId(), BookingRequestStatus.SCHEDULED);

        feedingMedicationService.syncPetDetailDef(companyId, bookingRequestModel);
        onlineBookingProducer.pushOnlineBookingAcceptedEvent(bookingRequest);

        afterBookingRequestAccepted(appointmentIds, bookingRequest);

        return AcceptBookingRequestResponse.newBuilder()
                .setResult(!isEmpty(appointmentIds) && affectedRows != 0)
                .setAppointmentId(appointmentIds.get(0))
                .build();
    }

    /*private*/ void afterBookingRequestAccepted(List<Long> appointmentIds, BookingRequest bookingRequest) {

        if (ObjectUtils.isEmpty(appointmentIds)) {
            return;
        }

        // 添加 booking request 和 appointment 的关联
        addBookingRequestAppointmentMappings(appointmentIds, bookingRequest.getId());

        try {

            // 将 appointment id 回写到 OB deposit，用于向后兼容
            updateOBDeposit(bookingRequest.getId(), appointmentIds);

            // 处理 payment
            // prepay: 直接扣款
            // preauth: 写入 PreAuthRecord 记录，capture 金额
            // NOTE: 这个操作依赖 addBookingRequestAppointmentMappings 这一步
            handlePayment(bookingRequest.getBusinessId(), appointmentIds.get(0), bookingRequest.getId());

        } catch (Exception e) {

            // 如果出现异常，需要删除 booking request 和 appointment 的关联记录
            deleteBookingRequestAppointmentMappings(bookingRequest.getId());

            throw e;
        }
    }

    private void handlePayment(long businessId, long appointmentId, long bookingRequestId) {

        var deposit = depositApi.getOBDepositByBookingRequestId(Math.toIntExact(businessId), bookingRequestId);
        if (deposit == null) {
            return;
        }

        if (Objects.equals(deposit.getDepositType(), DepositPaymentTypeEnum.PrePay)) {

            // Accept booking request 之后需要真正 capture payment
            paymentStripeApi.capturePaymentIntent(deposit.getPaymentId());

        } else if (Objects.equals(deposit.getDepositType(), DepositPaymentTypeEnum.PreAuth)) {
            var appointment = mustGetAppointment(appointmentId);
            // Accept booking request 之后需要写入 pre-auth record
            insertPreAuthRecord(appointment, deposit);
        }
    }

    private AppointmentModel mustGetAppointment(long appointmentId) {
        var resp = appointmentStub.getAppointment(GetAppointmentRequest.newBuilder()
                .setAppointmentId(appointmentId)
                .build());
        if (!resp.hasAppointment()) {
            throw bizException(Code.CODE_PARAMS_ERROR, "Appointment not found: " + appointmentId);
        }
        return resp.getAppointment();
    }

    private void insertPreAuthRecord(AppointmentModel appointment, BookOnlineDepositDTO deposit) {

        var preauthInfo = deposit.getPreauthInfo();
        if (preauthInfo == null) {
            return;
        }

        var order = orderHelper.getBySource(OrderSourceType.APPOINTMENT, appointment.getId());
        if (order == null) {
            throw bizException(Code.CODE_PARAMS_ERROR, "Order not found: " + appointment.getId());
        }

        var param = new AppointmentEventParams();
        param.setBusinessId(Math.toIntExact(appointment.getBusinessId()));
        param.setCompanyId(appointment.getCompanyId());
        param.setTicketId(Math.toIntExact(appointment.getId()));
        param.setInvoiceId(Math.toIntExact(order.getOrder().getId()));
        param.setPreAuthAmount(BigDecimal.valueOf(order.getOrder().getSubTotalAmount()));
        param.setPreAuthPaymentMethod(preauthInfo.getPaymentMethodId());
        param.setPreAuthCardNumber(preauthInfo.getCardNumber());
        param.setCustomerId(Math.toIntExact(appointment.getCustomerId()));
        param.setApptDateStr(appointment.getAppointmentDate());
        param.setApptTime(appointment.getAppointmentStartTime());
        param.setPreAuthStatus(true);
        param.setEvent(AppointmentEventEnum.CREATE_SINGLE.name());
        param.setAppointmentSource(AppointmentSource.ONLINE_BOOKING_VALUE);
        param.setReleasePreAuth(false);
        param.setTicketIds(List.of());

        // 依赖于 booking request 和 appointment 的关联关系
        // See com.moego.server.payment.service.PreAuthService.isBookingRequest
        preAuthApi.create(param);
    }

    private void updateOBDeposit(long bookingRequestId, List<Long> appointmentIds) {
        if (ObjectUtils.isEmpty(appointmentIds)) {
            return;
        }

        var deposit = depositApi.getOBDepositByBookingRequestId(null, bookingRequestId);
        if (deposit == null) {
            return;
        }

        // 只有 daycare 一个 booking request 可能会创建多个 appointment
        // 但是 daycare 只付了一天的钱，所以这里逻辑是闭环的：一个 OB deposit 对应一个 booking request 和一个 appointment
        var appointmentId = appointmentIds.get(0);

        depositApi.update(IBookOnlineDepositService.UpdateParam.builder()
                .id(deposit.getId())
                .groomingId(Math.toIntExact(appointmentId))
                .build());
    }

    private void deleteBookingRequestAppointmentMappings(Long bookingRequestId) {
        bookingRequestAppointmentMappingService.deleteByBookingRequestId(bookingRequestId);
    }

    private void addBookingRequestAppointmentMappings(List<Long> appointmentIds, Long bookingRequestId) {
        for (var appointmentId : appointmentIds) {
            var insertBean = new BookingRequestAppointmentMapping();
            insertBean.setBookingRequestId(bookingRequestId);
            insertBean.setAppointmentId(appointmentId);
            bookingRequestAppointmentMappingService.insert(insertBean);
        }
    }

    private List<PetToLodgingDef> getLastLodgingUnitId(
            Map<Long, List<BookingRequestModel.Service>> servicesMap,
            Long companyId,
            Long businessId,
            Long customerId) {
        Map<Long /* pet id */, Long /* service id */> petServiceMap = servicesMap.values().stream()
                .flatMap(List::stream)
                .filter(BookingRequestModel.Service::hasDaycare)
                .map(BookingRequestModel.Service::getDaycare)
                .map(BookingRequestModel.DaycareService::getService)
                .collect(Collectors.toMap(
                        DaycareServiceDetailModel::getPetId, DaycareServiceDetailModel::getServiceId, (a, b) -> b));

        List<PetToLodgingDef> petToLodgingsList = petDetailService
                .getLastLodgingUnitId(companyId, businessId, customerId, petServiceMap.keySet(), petServiceMap.values())
                .entrySet()
                .stream()
                .map(entry -> PetToLodgingDef.newBuilder()
                        .setPetId(entry.getKey())
                        .setLodgingUnitId(entry.getValue())
                        .build())
                .toList();

        Set<Long> lodgingUnitIds = petToLodgingsList.stream()
                .map(PetToLodgingDef::getLodgingUnitId)
                .collect(Collectors.toSet());

        Set<Long> existUnitIds = lodgingService.getLodgingUnitByUnitIds(companyId, businessId, lodgingUnitIds).stream()
                .map(LodgingUnitModel::getId)
                .collect(Collectors.toSet());

        return petToLodgingsList.stream()
                .filter(entry -> existUnitIds.contains(entry.getLodgingUnitId()))
                .toList();
    }

    @Override
    public void declineBookingRequest(
            DeclineBookingRequestRequest request, StreamObserver<DeclineBookingRequestResponse> responseObserver) {
        long companyId = request.getCompanyId();
        long businessId = request.getBusinessId();
        long staffId = request.hasStaffId() ? request.getStaffId() : 0;

        BookingRequest bookingRequest = bookingRequestService.get(request.getId());
        if (bookingRequest == null) {
            throw bizException(Code.CODE_PARAMS_ERROR, "Booking request not found");
        }

        Map<Long, List<BookingRequestModel.Service>> servicesMap = listServices(
                List.of(bookingRequest),
                Set.of(
                        BookingRequestAssociatedModel.SERVICE,
                        BookingRequestAssociatedModel.ADD_ON,
                        BookingRequestAssociatedModel.FEEDING,
                        BookingRequestAssociatedModel.MEDICATION));

        BookingRequestModel bookingRequestModel =
                BookingRequestConverter.INSTANCE.entityToModel(bookingRequest).toBuilder()
                        .addAllServices(servicesMap.getOrDefault(bookingRequest.getId(), List.of()))
                        .build();

        // create appointment with decline status
        List<Long> appointmentIds = bookingRequestModifyService.createAppointment(
                bookingRequestModel,
                companyId,
                businessId,
                staffId,
                AppointmentStatus.CANCELED,
                List.of(),
                List.of(),
                List.of(),
                List.of());

        // set booking request status
        int affectedRows = bookingRequestService.updateStatus(request.getId(), BookingRequestStatus.DECLINED);

        ThreadPool.execute(() -> refund(businessId, request.getId()));

        responseObserver.onNext(DeclineBookingRequestResponse.newBuilder()
                .setResult(!isEmpty(appointmentIds) && affectedRows != 0)
                .setAppointmentId(appointmentIds.get(0))
                .build());
        responseObserver.onCompleted();
    }

    @Override
    public void acceptBookingRequestV2(
            AcceptBookingRequestV2Request request, StreamObserver<AcceptBookingRequestV2Response> responseObserver) {

        var beforeStatus = bookingRequestService.mustGet(request.getId()).getStatus();

        int affectedRows = bookingRequestService.updateStatus(request.getId(), BookingRequestStatus.SCHEDULED);
        if (affectedRows == 0) {
            responseObserver.onNext(
                    AcceptBookingRequestV2Response.newBuilder().setResult(false).build());
            responseObserver.onCompleted();
            return;
        }

        List<Long> appointmentIds;
        try {
            var bookingRequest =
                    mustGetBookingRequest(request.hasCompanyId() ? request.getCompanyId() : null, request.getId());

            var serviceDetails = listService(bookingRequest);

            if (ServiceItemEnum.GROUP_CLASS.isIncludedIn(bookingRequest.getServiceTypeInclude())) {
                appointmentIds = fulfillmentService.createFulfillment(bookingRequest, serviceDetails);
            } else {
                appointmentIds = createAppointments(request, serviceDetails, bookingRequest);

                var bookingRequestModel = BookingRequestConverter.INSTANCE.entityToModel(bookingRequest).toBuilder()
                        .addAllServices(serviceDetails)
                        .build();

                feedingMedicationService.syncPetDetailDef(bookingRequest.getCompanyId(), bookingRequestModel);
                onlineBookingProducer.pushOnlineBookingAcceptedEvent(bookingRequest);
            }
            afterBookingRequestAccepted(appointmentIds, bookingRequest);
        } catch (Exception e) {

            // accept 失败需要回滚状态
            bookingRequestService.updateStatus(request.getId(), beforeStatus);

            throw e;
        }

        responseObserver.onNext(AcceptBookingRequestV2Response.newBuilder()
                .setResult(!isEmpty(appointmentIds))
                .setAppointmentId(appointmentIds.get(0))
                .build());
        responseObserver.onCompleted();
    }

    private BookingRequest mustGetBookingRequest(@Nullable Long companyId, long bookingRequestId) {
        var bookingRequest = bookingRequestService.mustGet(bookingRequestId);
        if (companyId != null && !Objects.equals(companyId, bookingRequest.getCompanyId())) {
            throw bizException(Code.CODE_PARAMS_ERROR, "Booking request not found: " + bookingRequestId);
        }
        return bookingRequest;
    }

    private BookingRequest mustGetBookingRequest(
            @Nullable Long companyId, @Nullable Long businessId, long bookingRequestId) {
        var bookingRequest = bookingRequestService.mustGet(bookingRequestId);
        if (companyId != null && !Objects.equals(companyId, bookingRequest.getCompanyId())) {
            throw bizException(Code.CODE_PARAMS_ERROR, "Booking request not found: " + bookingRequestId);
        }
        if (businessId != null && !Objects.equals(businessId, bookingRequest.getBusinessId())) {
            throw bizException(Code.CODE_PARAMS_ERROR, "Booking request not found: " + bookingRequestId);
        }
        return bookingRequest;
    }

    private BookingRequestModel mustGetBookingRequestModel(@Nullable Long companyId, long bookingRequestId) {
        var bookingRequest = bookingRequestService.mustGet(bookingRequestId);
        if (companyId != null && !Objects.equals(companyId, bookingRequest.getCompanyId())) {
            throw bizException(Code.CODE_PARAMS_ERROR, "Booking request not found: " + bookingRequestId);
        }
        Set<BookingRequestAssociatedModel> models =
                new HashSet<>(List.of(BookingRequestAssociatedModel.SERVICE, BookingRequestAssociatedModel.ADD_ON));
        Map<Long, List<BookingRequestModel.Service>> servicesMap = listServices(List.of(bookingRequest), models);
        return BookingRequestConverter.INSTANCE.entityToModel(bookingRequest).toBuilder()
                .addAllServices(servicesMap.getOrDefault(bookingRequest.getId(), List.of()))
                .build();
    }

    private List<Long> createAppointments(
            AcceptBookingRequestV2Request request,
            List<BookingRequestModel.Service> serviceDetails,
            BookingRequest bookingRequest) {

        var appointmentIds = new ArrayList<Long>();

        // 特别注意：boarding/daycare 里面有 evaluation，如果 evaluation 对应 service 的 is_evaluation_required_for_ob 为 true
        // 这类 evaluation 叫做 mandatory evaluation.
        // 在 accept booking request 时：
        // - mandatory evaluation 需要放在一起单独创建一个 appointment
        // - non-mandatory evaluation 则需要和对应的 boarding/daycare service 放在一起创建 appointment

        var mandatoryEvaluations = filterMandatoryEvaluation(serviceDetails);
        var normalServiceDetails = serviceDetails.stream()
                .filter(not(mandatoryEvaluations::contains))
                .toList();

        switch (getMainServiceItemType(bookingRequest.getServiceTypeInclude())) {
            case BOARDING -> {
                appointmentIds.add(createAppointmentForBoarding(request, normalServiceDetails, bookingRequest));

                if (!mandatoryEvaluations.isEmpty()) {
                    // 注意：新增的 evaluation (createEvaluationRequests) 应该和 boarding 一起创建
                    // 为 mandatory evaluation 创建 appointment 时需要去掉 createEvaluationRequests
                    var newRequest =
                            request.toBuilder().clearCreateEvaluationRequests().build();
                    appointmentIds.add(
                            createAppointmentForEvaluation(newRequest, mandatoryEvaluations, bookingRequest));
                }
            }
            case DAYCARE -> {
                // daycare 会按照 date 拆分为多个 appointments
                appointmentIds.addAll(createAppointmentsForDaycare(request, normalServiceDetails, bookingRequest));

                if (!mandatoryEvaluations.isEmpty()) {
                    // 注意：新增的 evaluation (createEvaluationRequests) 应该随着 daycare 一起创建
                    // 为 mandatory evaluation 创建 appointment 时需要去掉 createEvaluationRequests
                    var newRequest =
                            request.toBuilder().clearCreateEvaluationRequests().build();
                    appointmentIds.add(
                            createAppointmentForEvaluation(newRequest, mandatoryEvaluations, bookingRequest));
                }
            }
            case GROOMING -> {
                appointmentIds.add(createAppointmentForGrooming(request, serviceDetails, bookingRequest));
            }
            case EVALUATION -> {
                appointmentIds.add(createAppointmentForEvaluation(request, serviceDetails, bookingRequest));
            }
            default -> throw bizException(
                    Code.CODE_PARAMS_ERROR,
                    "Unsupported service type: " + getMainServiceItemType(bookingRequest.getServiceTypeInclude()));
        }

        return appointmentIds;
    }

    private long createAppointmentForEvaluation(
            AcceptBookingRequestV2Request request,
            List<BookingRequestModel.Service> serviceDetails,
            BookingRequest bookingRequest) {

        var petDetailDefs = new ArrayList<PetDetailDef>();

        // evaluation 会出现的 case 只有 evaluation
        petDetailDefs.addAll(buildPetDetailDefsForEvaluation(request, serviceDetails, null));

        var req = buildCreateAppointmentForOnlineBookingRequest(request, bookingRequest, petDetailDefs);

        return appointmentStub.createAppointmentForOnlineBooking(req).getAppointmentId();
    }

    private long createAppointmentForGrooming(
            AcceptBookingRequestV2Request request,
            List<BookingRequestModel.Service> serviceDetails,
            BookingRequest bookingRequest) {

        var petDetailDefs = new ArrayList<PetDetailDef>();

        // grooming 会出现的 case 只有 grooming
        petDetailDefs.addAll(buildPetDetailDefsForGrooming(request, serviceDetails, null));

        var req = buildCreateAppointmentForOnlineBookingRequest(request, bookingRequest, petDetailDefs);

        return appointmentStub.createAppointmentForOnlineBooking(req).getAppointmentId();
    }

    private long createAppointmentForBoarding(
            AcceptBookingRequestV2Request request,
            List<BookingRequestModel.Service> serviceDetails,
            BookingRequest bookingRequest) {

        var petDetailDefs = new ArrayList<PetDetailDef>();

        // boarding 会出现的 case 只有 boarding + grooming + evaluation
        petDetailDefs.addAll(buildPetDetailDefsForBoarding(request, serviceDetails, bookingRequest));
        petDetailDefs.addAll(buildPetDetailDefsForGrooming(request, serviceDetails, null));
        petDetailDefs.addAll(buildPetDetailDefsForEvaluation(request, serviceDetails, null));

        var req = buildCreateAppointmentForOnlineBookingRequest(request, bookingRequest, petDetailDefs);

        return appointmentStub.createAppointmentForOnlineBooking(req).getAppointmentId();
    }

    private List<BookingRequestModel.Service> filterMandatoryEvaluation(
            List<BookingRequestModel.Service> serviceDetails) {
        var serviceIds = serviceDetails.stream()
                .filter(BookingRequestModel.Service::hasEvaluation)
                .map(e -> e.getEvaluation().getService().getServiceId())
                .filter(CommonUtil::isNormal)
                .collect(Collectors.toSet());

        var serviceIdToService = serviceHelper.listService(serviceIds);

        return serviceDetails.stream()
                .filter(BookingRequestModel.Service::hasEvaluation)
                .filter(e -> isNormal(e.getEvaluation().getService().getServiceId()))
                .filter(e -> {
                    var service = serviceIdToService.get(
                            e.getEvaluation().getService().getServiceId());
                    return service != null
                            && service.getIsEvaluationRequired()
                            && service.getIsEvaluationRequiredForOb();
                })
                .toList();
    }

    private List<PetDetailDef> buildPetDetailDefsForEvaluation(
            AcceptBookingRequestV2Request request,
            List<BookingRequestModel.Service> serviceDetails,
            @Nullable LocalDate date // 只 build 指定日期的 evaluation
            ) {

        var petIdToEvaluationList = serviceDetails.stream()
                .filter(BookingRequestModel.Service::hasEvaluation)
                .collect(groupingBy(BookingRequestServer::getPetId));

        var result = new ArrayList<PetDetailDef>();

        for (var en : petIdToEvaluationList.entrySet()) {
            var petId = en.getKey();
            var evaluationServices = en.getValue();
            var petDetailBuilder = PetDetailDef.newBuilder().setPetId(petId);
            for (var evaluation : evaluationServices) {
                if (date == null
                        || Objects.equals(
                                evaluation.getEvaluation().getService().getStartDate(), date.toString())) {
                    processEvaluation(petDetailBuilder, evaluation.getEvaluation(), request);
                }
            }
            if (!petDetailBuilder.getEvaluationsList().isEmpty()) {
                result.add(petDetailBuilder.build());
            }
        }

        // 处理 create evaluation request
        var petIdToCreateEvaluationRequests = request.getCreateEvaluationRequestsList().stream()
                .collect(groupingBy(AcceptBookingRequestV2Request.CreateEvaluationRequest::getPetId));
        for (var en : petIdToCreateEvaluationRequests.entrySet()) {
            var petId = en.getKey();
            var createEvaluationRequests = en.getValue();
            var petDetailBuilder = PetDetailDef.newBuilder().setPetId(petId);
            for (var createEvaluationRequest : createEvaluationRequests) {
                if (date == null
                        || Objects.equals(ProtobufUtil.toLocalDate(createEvaluationRequest.getStartDate()), date)) {
                    petDetailBuilder.addEvaluations(buildSelectedEvaluationDef(createEvaluationRequest));
                }
            }
            if (!petDetailBuilder.getEvaluationsList().isEmpty()) {
                result.add(petDetailBuilder.build());
            }
        }

        return result;
    }

    private SelectedEvaluationDef buildSelectedEvaluationDef(
            AcceptBookingRequestV2Request.CreateEvaluationRequest createEvaluationRequest) {

        var evaluationBuilder = SelectedEvaluationDef.newBuilder();
        evaluationBuilder.setServiceId(createEvaluationRequest.getEvaluationId());
        evaluationBuilder.setStartDate(
                ProtobufUtil.toLocalDate(createEvaluationRequest.getStartDate()).toString());
        evaluationBuilder.setStartTime(createEvaluationRequest.getStartTime());
        if (createEvaluationRequest.hasStaffId()) {
            evaluationBuilder.setStaffId(createEvaluationRequest.getStaffId());
        }
        if (createEvaluationRequest.hasLodgingId()) {
            evaluationBuilder.setLodgingId(createEvaluationRequest.getLodgingId());
        }

        var evaluationBriefView = mustGetEvaluation(createEvaluationRequest.getEvaluationId());
        evaluationBuilder.setServiceTime(evaluationBriefView.getDuration());
        evaluationBuilder.setServicePrice(evaluationBriefView.getPrice());
        evaluationBuilder.setEndTime(evaluationBuilder.getStartTime() + evaluationBriefView.getDuration());

        return evaluationBuilder.build();
    }

    private static List<PetDetailDef> buildPetDetailDefsForGrooming(
            AcceptBookingRequestV2Request request,
            List<BookingRequestModel.Service> serviceDetails,
            @Nullable LocalDate date // 只 build 指定日期的 grooming
            ) {

        var petIdToGroomingList = serviceDetails.stream()
                .filter(BookingRequestModel.Service::hasGrooming)
                .collect(groupingBy(BookingRequestServer::getPetId));

        var result = new ArrayList<PetDetailDef>();

        for (var en : petIdToGroomingList.entrySet()) {
            var petId = en.getKey();
            var groomingServices = en.getValue();
            var petDetailBuilder = PetDetailDef.newBuilder().setPetId(petId);
            for (var grooming : groomingServices) {
                if (date == null
                        || Objects.equals(grooming.getGrooming().getService().getStartDate(), date.toString())) {
                    processGrooming(petDetailBuilder, grooming.getGrooming(), request);
                }
            }
            if (!petDetailBuilder.getServicesList().isEmpty()) {
                result.add(petDetailBuilder.build());
            }
        }

        return result;
    }

    private static List<PetDetailDef> buildPetDetailDefsForBoarding(
            AcceptBookingRequestV2Request request,
            List<BookingRequestModel.Service> serviceDetails,
            BookingRequest bookingRequest) {

        var petIdToBoardingList = serviceDetails.stream()
                .filter(BookingRequestModel.Service::hasBoarding)
                .collect(groupingBy(BookingRequestServer::getPetId));

        var result = new ArrayList<PetDetailDef>();

        for (var en : petIdToBoardingList.entrySet()) {
            var petId = en.getKey();
            var boardingServices = en.getValue();
            var petDetailBuilder = PetDetailDef.newBuilder().setPetId(petId);
            for (var boarding : boardingServices) {
                processBoarding(petDetailBuilder, boarding.getBoarding(), request, bookingRequest);
            }
            result.add(petDetailBuilder.build());
        }

        return result;
    }

    private List<Long> createAppointmentsForDaycare(
            AcceptBookingRequestV2Request request,
            List<BookingRequestModel.Service> serviceDetails,
            BookingRequest bookingRequest) {

        // 将相同日期不同 pet 的 daycare 服务合并到同一个 appointment
        var dateToDaycareList = serviceDetails.stream()
                .filter(BookingRequestModel.Service::hasDaycare)
                .flatMap(e -> e.getDaycare().getService().getSpecificDatesList().stream()
                        .map(it -> Map.entry(LocalDate.parse(it), e)))
                .collect(groupingBy(Map.Entry::getKey, TreeMap::new, mapping(Map.Entry::getValue, toList())));

        if (dateToDaycareList.isEmpty()) {
            return List.of();
        }

        var result = new ArrayList<Long>();
        var iterator = dateToDaycareList.entrySet().iterator();

        // 这里特别注意 prepay 的场景，daycare prepay 时只付第一天的钱
        // 为了避免并发问题，这里不能直接并发创建，而是要等第一天的 appointment 创建完成之后再创建后续的 appointment

        // 同步创建第一天的 appointment
        var firstEntry = iterator.next();
        var firstDate = firstEntry.getKey();
        var firstDaycareServices = firstEntry.getValue();
        var firstAppointmentId =
                createDaycareAppointment(request, serviceDetails, bookingRequest, firstDaycareServices, firstDate);
        result.add(firstAppointmentId);

        // 并发创建后续日期的 appointment
        var futures = new ArrayList<CompletableFuture<Long>>();
        while (iterator.hasNext()) {
            var entry = iterator.next();
            var date = entry.getKey();
            var daycareServices = entry.getValue();
            futures.add(CompletableFuture.supplyAsync(
                    () -> createDaycareAppointment(request, serviceDetails, bookingRequest, daycareServices, date),
                    ThreadPool.getSubmitExecutor()));
        }

        for (var future : futures) {
            result.add(future.join());
        }

        return result;
    }

    private long createDaycareAppointment(
            AcceptBookingRequestV2Request request,
            List<BookingRequestModel.Service> serviceDetails,
            BookingRequest bookingRequest,
            List<BookingRequestModel.Service> daycareServices,
            LocalDate date) {

        var petDetailDefs = new ArrayList<PetDetailDef>();

        // daycare 会出现的 case 只有 daycare + grooming + evaluation
        petDetailDefs.addAll(buildPetDetailDefsForDaycare(request, daycareServices, bookingRequest, date));
        petDetailDefs.addAll(buildPetDetailDefsForGrooming(request, serviceDetails, date));
        petDetailDefs.addAll(buildPetDetailDefsForEvaluation(request, serviceDetails, date));

        var req = buildCreateAppointmentForOnlineBookingRequest(request, bookingRequest, petDetailDefs);
        return appointmentStub.createAppointmentForOnlineBooking(req).getAppointmentId();
    }

    private static List<PetDetailDef> buildPetDetailDefsForDaycare(
            AcceptBookingRequestV2Request request,
            List<BookingRequestModel.Service> daycareServices,
            BookingRequest bookingRequest,
            LocalDate date) {

        var daycarePetDetailDefs = new ArrayList<PetDetailDef>();

        for (var daycareService : daycareServices) {
            daycarePetDetailDefs.add(
                    buildPetDetailDefForDaycare(daycareService.getDaycare(), request, bookingRequest, date));
        }

        return daycarePetDetailDefs;
    }

    private static CreateAppointmentForOnlineBookingRequest buildCreateAppointmentForOnlineBookingRequest(
            AcceptBookingRequestV2Request request, BookingRequest bookingRequest, List<PetDetailDef> petDetailDefs) {
        var builder = CreateAppointmentForOnlineBookingRequest.newBuilder();
        builder.setCompanyId(bookingRequest.getCompanyId());
        builder.setBusinessId(bookingRequest.getBusinessId());
        if (request.hasStaffId()) {
            builder.setStaffId(request.getStaffId());
        }
        builder.setBookingRequestId(bookingRequest.getId());
        builder.setBookingRequestIdentifier(bookingRequest.getId() + "." + bookingRequest.getStartDate());
        builder.setAppointment(buildAppointmentCreateForOnlineBookingDef(bookingRequest));
        if (StringUtils.hasText(bookingRequest.getAdditionalNote())) {
            builder.addNotes(buildAppointmentNoteCreateDef(bookingRequest));
        }

        builder.addAllPetDetails(petDetailDefs);

        return builder.build();
    }

    private List<BookingRequestModel.Service> listService(BookingRequest bookingRequest) {
        return listServices(
                        List.of(bookingRequest),
                        Set.of(
                                BookingRequestAssociatedModel.SERVICE,
                                BookingRequestAssociatedModel.ADD_ON,
                                BookingRequestAssociatedModel.FEEDING,
                                BookingRequestAssociatedModel.MEDICATION))
                .getOrDefault(bookingRequest.getId(), List.of());
    }

    private static long getPetId(BookingRequestModel.Service serviceDetail) {
        return switch (serviceDetail.getServiceCase()) {
            case GROOMING -> serviceDetail.getGrooming().getService().getPetId();
            case BOARDING -> serviceDetail.getBoarding().getService().getPetId();
            case DAYCARE -> serviceDetail.getDaycare().getService().getPetId();
            case EVALUATION -> serviceDetail.getEvaluation().getService().getPetId();
            case DOG_WALKING -> serviceDetail.getDogWalking().getService().getPetId();
            default -> 0L;
        };
    }

    private static void processGrooming(
            PetDetailDef.Builder petDetailBuilder,
            BookingRequestModel.GroomingService grooming,
            AcceptBookingRequestV2Request request) {
        var serviceBuilder =
                BookingRequestConverter.INSTANCE.groomingToSelectedServiceDef(grooming.getService()).toBuilder();

        var specifiedService = request.getGroomingServicesList().stream()
                .filter(e -> Objects.equals(e.getId(), grooming.getService().getId()))
                .findFirst()
                .orElse(null);
        if (specifiedService != null) {
            if (specifiedService.hasStaffId()) {
                serviceBuilder.setStaffId(specifiedService.getStaffId());
            }
            if (specifiedService.hasStartTime()) {
                serviceBuilder.setStartTime(specifiedService.getStartTime());
                serviceBuilder.setEndTime(
                        specifiedService.getStartTime() + grooming.getService().getServiceTime());
            }
        }

        petDetailBuilder.addServices(serviceBuilder.build());

        for (var addon : grooming.getAddonsList()) {
            var addonBuilder =
                    BookingRequestConverter.INSTANCE
                            .groomingAddOnToSelectedServiceAddOnDef(addon, addon.getStartDate())
                            .toBuilder();

            var specifiedAddon = request.getGroomingAddonsList().stream()
                    .filter(e -> Objects.equals(e.getId(), addon.getId()))
                    .findFirst()
                    .orElse(null);
            if (specifiedAddon != null) {
                if (specifiedAddon.hasStaffId()) {
                    addonBuilder.setStaffId(specifiedAddon.getStaffId());
                }
                if (specifiedAddon.hasStartTime()) {
                    addonBuilder.setStartTime(specifiedAddon.getStartTime());
                }
            }

            petDetailBuilder.addAddOns(addonBuilder.build());
        }
    }

    private static void processBoarding(
            PetDetailDef.Builder petDetailBuilder,
            BookingRequestModel.BoardingService boarding,
            AcceptBookingRequestV2Request request,
            BookingRequest bookingRequest) {
        var serviceBuilder = BookingRequestConverter.INSTANCE
                .boardingToSelectedServiceDef(
                        boarding.getService(),
                        boarding.getService().getLodgingId(),
                        boarding.getFeedingsList(),
                        boarding.getMedicationsList())
                .toBuilder();

        var specifiedService = request.getBoardingServicesList().stream()
                .filter(e -> Objects.equals(e.getId(), boarding.getService().getId()))
                .findFirst()
                .orElse(null);
        if (specifiedService != null) {
            if (specifiedService.hasLodgingId()) {
                serviceBuilder.setLodgingId(specifiedService.getLodgingId());
            }
        }

        petDetailBuilder.addServices(serviceBuilder.build());

        for (var addon : boarding.getAddonsList()) {
            var addonBuilder = BookingRequestConverter.INSTANCE
                    .boardingAddOnToSelectedServiceAddOnDef(
                            addon,
                            bookingRequest.getStartDate(),
                            bookingRequest.getStartTime(),
                            boarding.getService().getServiceId())
                    .toBuilder();

            var specifiedAddon = request.getBoardingAddonsList().stream()
                    .filter(e -> Objects.equals(e.getId(), addon.getId()))
                    .findFirst()
                    .orElse(null);
            if (specifiedAddon != null) {
                if (specifiedAddon.hasStartTime()) {
                    addonBuilder.setStartTime(specifiedAddon.getStartTime());
                }
                if (specifiedAddon.hasStaffId()) {
                    addonBuilder.setStaffId(specifiedAddon.getStaffId());
                    // 如果是 required staff addon，需要把 date type 设置为 DATE_POINT
                    // 和 com.moego.svc.online.booking.service.BookingRequestModifyService.createOneAppointment 逻辑保持一致
                    addonBuilder.setAddonDateType(PetDetailDateType.PET_DETAIL_DATE_DATE_POINT);
                    // 如果是 required staff addon，需要把 start date 设置为 addon 的 start date
                    // 和 com.moego.svc.online.booking.service.BookingRequestModifyService.createOneAppointment 逻辑保持一致
                    addonBuilder.setStartDate(getStartDate(addon));
                }
            }

            petDetailBuilder.addAddOns(addonBuilder.build());
        }
    }

    private static String getStartDate(BoardingAddOnDetailModel addon) {
        if (addon.hasStartDate()) {
            return ProtobufUtil.toLocalDate(addon.getStartDate()).toString();
        }
        var specificDates = addon.getSpecificDatesList();
        if (specificDates.isEmpty()) {
            throw bizException(
                    Code.CODE_PARAMS_ERROR, String.format("Can't get startDate for addon: %s", JsonUtil.toJson(addon)));
        }
        return specificDates.get(0);
    }

    private static PetDetailDef buildPetDetailDefForDaycare(
            BookingRequestModel.DaycareService daycare,
            AcceptBookingRequestV2Request request,
            BookingRequest bookingRequest,
            LocalDate date) {

        var petDetailBuilder =
                PetDetailDef.newBuilder().setPetId(daycare.getService().getPetId());

        var serviceBuilder = BookingRequestConverter.INSTANCE
                .daycareToSelectedServiceDef(
                        daycare.getService(),
                        date.toString(),
                        date.toString(),
                        0L,
                        daycare.getFeedingsList(),
                        daycare.getMedicationsList())
                .toBuilder()
                .clearSpecificDates()
                .addSpecificDates(date.toString());

        var specifiedService = request.getDaycareServicesList().stream()
                .filter(e -> Objects.equals(e.getId(), daycare.getService().getId()))
                .findFirst()
                .orElse(null);
        if (specifiedService != null) {
            if (specifiedService.hasLodgingId()) {
                serviceBuilder.setLodgingId(specifiedService.getLodgingId());
            }
        }

        petDetailBuilder.addServices(serviceBuilder.build());

        for (var addon : daycare.getAddonsList()) {

            if (!addon.getIsEveryday() && !addon.getSpecificDatesList().contains(date.toString())) {
                continue;
            }

            var addonBuilder = BookingRequestConverter.INSTANCE
                    .daycareAddOnToSelectedServiceAddOnDef(
                            addon,
                            date.toString(),
                            bookingRequest.getStartTime(),
                            daycare.getService().getServiceId(),
                            List.of(date.toString()))
                    .toBuilder();

            var specifiedAddon = request.getDaycareAddonsList().stream()
                    .filter(e -> Objects.equals(e.getId(), addon.getId()))
                    .findFirst()
                    .orElse(null);
            if (specifiedAddon != null) {
                if (specifiedAddon.hasStartTime()) {
                    addonBuilder.setStartTime(specifiedAddon.getStartTime());
                }
                if (specifiedAddon.hasStaffId()) {
                    addonBuilder.setStaffId(specifiedAddon.getStaffId());
                }
            }

            petDetailBuilder.addAddOns(addonBuilder.build());
        }

        return petDetailBuilder.build();
    }

    private void processEvaluation(
            PetDetailDef.Builder petDetailBuilder,
            BookingRequestModel.EvaluationService evaluation,
            AcceptBookingRequestV2Request request) {
        var evaluationBuilder =
                BookingRequestConverter.INSTANCE.evaluationToSelectedServiceDef(evaluation.getService()).toBuilder();

        var specifiedEvaluation = request.getEvaluationServicesList().stream()
                .filter(e -> Objects.equals(e.getId(), evaluation.getService().getId()))
                .findFirst()
                .orElse(null);
        if (specifiedEvaluation != null) {
            if (specifiedEvaluation.hasStaffId()) {
                evaluationBuilder.setStaffId(specifiedEvaluation.getStaffId());
            }
            if (specifiedEvaluation.hasEvaluationId()) {
                var evaluationModel = mustGetEvaluation(specifiedEvaluation.getEvaluationId());
                evaluationBuilder.setServiceId(evaluationModel.getId());
                evaluationBuilder.setServicePrice(evaluationModel.getPrice());
                evaluationBuilder.setServiceTime(evaluationModel.getDuration());
                evaluationBuilder.setEndTime(evaluationBuilder.getStartTime() + evaluationModel.getDuration());
            }
        }

        petDetailBuilder.addEvaluations(evaluationBuilder.build());
    }

    private EvaluationBriefView mustGetEvaluation(long evaluationId) {
        var evaluation = serviceHelper.getEvaluationByIds(List.of(evaluationId)).get(evaluationId);
        if (evaluation == null) {
            throw bizException(Code.CODE_PARAMS_ERROR, "Evaluation not found: " + evaluationId);
        }
        return evaluation;
    }

    private static AppointmentNoteCreateDef buildAppointmentNoteCreateDef(BookingRequest bookingRequest) {
        return AppointmentNoteCreateDef.newBuilder()
                .setNote(bookingRequest.getAdditionalNote())
                .setType(AppointmentNoteType.ADDITIONAL)
                .build();
    }

    private static AppointmentCreateForOnlineBookingDef buildAppointmentCreateForOnlineBookingDef(
            BookingRequest bookingRequest) {
        var appointmentBuilder = AppointmentCreateForOnlineBookingDef.newBuilder();
        appointmentBuilder.setCustomerId(bookingRequest.getCustomerId());
        appointmentBuilder.setStatus(AppointmentStatus.UNCONFIRMED);
        appointmentBuilder.setCreatedAt(Timestamps.fromDate(new java.util.Date()));
        return appointmentBuilder.build();
    }

    private void refund(long businessId, long bookingRequestId) {
        var deposit = depositApi.getOBDepositByBookingRequestId(Math.toIntExact(businessId), bookingRequestId);
        if (deposit == null) {
            return;
        }

        var params = new CreateRefundByPaymentIdParams();
        params.setPaymentId(deposit.getPaymentId());
        params.setReason("cancel for online booking decline");
        refundApi.createRefundByPaymentId(Math.toIntExact(businessId), params);
    }

    @Override
    public void countBookingRequests(
            CountBookingRequestsRequest request, StreamObserver<CountBookingRequestsResponse> responseObserver) {
        int count = bookingRequestService.countBookingRequest(
                BookingRequestConverter.INSTANCE.countRequestToFilterDTO(request));
        responseObserver.onNext(
                CountBookingRequestsResponse.newBuilder().setCount(count).build());
        responseObserver.onCompleted();
    }

    @Override
    public void listBookingRequestId(
            ListBookingRequestIdRequest request, StreamObserver<ListBookingRequestIdResponse> responseObserver) {
        var appointmentIdToBookingRequestId =
                bookingRequestAppointmentMappingService.listByAppointmentIds(request.getAppointmentIdsList()).stream()
                        .collect(toMap(
                                BookingRequestAppointmentMapping::getAppointmentId,
                                BookingRequestAppointmentMapping::getBookingRequestId,
                                (o, n) -> o));

        responseObserver.onNext(ListBookingRequestIdResponse.newBuilder()
                .putAllAppointmentIdToBookingRequestId(appointmentIdToBookingRequestId)
                .build());
        responseObserver.onCompleted();
    }

    @Override
    public void syncBookingRequestFromAppointment(
            SyncBookingRequestFromAppointmentRequest request,
            StreamObserver<SyncBookingRequestFromAppointmentResponse> responseObserver) {

        for (var appointmentId : request.getAppointmentIdList()) {
            var params = new BookingRequestEventParams();
            params.setAppointmentId(Math.toIntExact(appointmentId));
            params.setEvent(BookingRequestEventParams.BookingRequestEvent.SUBMITTED);
            syncBookingRequestListener.processEvent(params);
        }

        responseObserver.onNext(SyncBookingRequestFromAppointmentResponse.getDefaultInstance());
        responseObserver.onCompleted();
    }

    @Override
    public void triggerBookingRequestAutoAccepted(
            TriggerBookingRequestAutoAcceptedRequest request,
            StreamObserver<TriggerBookingRequestAutoAcceptedResponse> responseObserver) {
        // auto accept if possible
        var isAutoAccepted = autoAccept(request.getId());

        // sync C-end update to B-end
        if (isAutoAccepted) {
            ThreadPool.execute(() -> syncCustomerProfile(request.getId()));
        }

        // send notification
        ThreadPool.execute(() -> sendNotification(request.getId(), isAutoAccepted));

        responseObserver.onNext(TriggerBookingRequestAutoAcceptedResponse.newBuilder()
                .setIsAutoAccepted(isAutoAccepted)
                .build());
        responseObserver.onCompleted();
    }

    private void syncCustomerProfile(long bookingRequestId) {
        var bookingRequest = bookingRequestService.mustGet(bookingRequestId);
        var businessId = toIntExact(bookingRequest.getBusinessId());
        var customerId = toIntExact(bookingRequest.getCustomerId());

        var customerProfileRequest = profileRequestApi.getCustomerProfileRequest(businessId, customerId);
        if (customerProfileRequest == null) {
            return;
        }

        var existingPets = Optional.ofNullable(customerProfileRequest.getPets()).stream()
                .flatMap(Collection::stream)
                .filter(e -> isNormal(e.getPetId()))
                .toList();
        customerProfileRequest.setPets(existingPets);

        profileRequestApi.updateCustomerAndProfileRequest(customerProfileRequest, true);

        var dto = new BookOnlineQuestionSaveDTO();
        dto.setCompanyId(bookingRequest.getCompanyId());
        dto.setBusinessId(businessId);
        dto.setCustomerId(customerId);
        dto.setClientCustomQuestionMap(customerProfileRequest.getClient().getCustomQuestions());
        dto.setPetCustomQuestionMap(existingPets.stream()
                .collect(Collectors.toMap(
                        CustomerProfileRequestDTO.PetProfileDTO::getPetId,
                        pet -> Optional.ofNullable(pet.getCustomQuestions()).orElse(Map.of()))));
        bookOnlineQuestionApi.upsertCustomerQuestionSave(dto, true);
    }

    private boolean autoAccept(long bookingRequestId) {

        var bookingRequest = bookingRequestService.mustGet(bookingRequestId);
        if (bookingRequest.getStatus() != BookingRequestStatus.SUBMITTED) {
            return false;
        }

        // check service type, only boarding and daycare can be auto accepted
        int serviceTypeInclude = bookingRequest.getServiceTypeInclude();
        ServiceItemType serviceItemType = null;
        if (ServiceItemEnum.BOARDING.isIncludedIn(serviceTypeInclude)) {
            serviceItemType = ServiceItemType.BOARDING;
        } else if (ServiceItemEnum.DAYCARE.isIncludedIn(serviceTypeInclude)) {
            serviceItemType = ServiceItemType.DAYCARE;
        }
        if (Objects.isNull(serviceItemType)) {
            log.info("Auto accept failed, service type not match. bookingRequestId: {}", bookingRequestId);
            return false;
        }

        var bookingRequestIdToServices = listServices(
                List.of(bookingRequest),
                Set.of(
                        BookingRequestAssociatedModel.SERVICE,
                        BookingRequestAssociatedModel.ADD_ON,
                        BookingRequestAssociatedModel.FEEDING,
                        BookingRequestAssociatedModel.MEDICATION));

        var bookingRequestModel = BookingRequestConverter.INSTANCE.entityToModel(bookingRequest).toBuilder()
                .addAllServices(bookingRequestIdToServices.getOrDefault(bookingRequest.getId(), List.of()))
                .build();

        // 两种情况不能 auto accept：
        // 1. 有 require staff 的 addon，需要手动 staff
        // 2. grooming service 还没有 staff 或者 time
        boolean hasRequiredStaffAddon = hasRequiredStaffAddOn(bookingRequestModel, bookingRequest.getCompanyId());
        if (hasRequiredStaffAddon || hasIncompleteGroomingService(bookingRequestModel)) {
            return false;
        }

        Set<Long> obRequestPetIds = bookingRequestModel.getServicesList().stream()
                .map(BookingRequestServer::getPetId)
                .filter(CommonUtil::isNormal)
                .collect(Collectors.toSet());

        boolean canAutoAccept = canAutoAccept(bookingRequest, serviceItemType, obRequestPetIds);

        if (Boolean.FALSE.equals(canAutoAccept)) {
            return false;
        }

        // auto assign for boarding & daycare
        List<PetToLodgingDef> petToLodgingsList = List.of();
        if (ServiceItemEnum.BOARDING.isIncludedIn(serviceTypeInclude)
                || ServiceItemEnum.DAYCARE.isIncludedIn(serviceTypeInclude)) {
            GetAutoAssignResponse autoAssignResponse = getAutoAssignResponse(GetAutoAssignRequest.newBuilder()
                    .setId(bookingRequestId)
                    .setCompanyId(bookingRequest.getCompanyId())
                    .setBusinessId(bookingRequest.getBusinessId())
                    .build());
            petToLodgingsList = autoAssignResponse.getPetToLodgingsList();
            log.info("Auto assign result: {}", JsonUtil.toJson(autoAssignResponse));

            if (ObjectUtils.isEmpty(petToLodgingsList) && ServiceItemEnum.BOARDING.isIncludedIn(serviceTypeInclude)) {
                log.info("Auto assign failed, petToLodgingsList is empty. bookingRequestId: {}", bookingRequestId);
                return false;
            }

            Set<Long> petIds =
                    petToLodgingsList.stream().map(PetToLodgingDef::getPetId).collect(Collectors.toSet());
            boolean petHasNoLodging = bookingRequestModel.getServicesList().stream()
                    .filter(BookingRequestModel.Service::hasBoarding)
                    .map(service -> service.getBoarding().getService().getPetId())
                    .distinct()
                    .anyMatch(not(petIds::contains));
            if (petHasNoLodging) {
                log.info("Auto assign failed, some pet has no lodging room. bookingRequestId: {}", bookingRequestId);
                return false;
            }
        }

        // auto accept
        AcceptBookingRequestResponse acceptBookingRequestResponse =
                getAcceptBookingRequestResponse(AcceptBookingRequestRequest.newBuilder()
                        .setId(bookingRequestId)
                        .setCompanyId(bookingRequest.getCompanyId())
                        .setBusinessId(bookingRequest.getBusinessId())
                        .addAllPetToLodgings(petToLodgingsList)
                        .addAllPetToStaffs(List.of())
                        .build());
        if (!acceptBookingRequestResponse.getResult()) {
            log.info("Auto accept failed when scheduling this appointment. bookingRequestId: {}", bookingRequestId);
            return false;
        }

        log.info("Auto accept success. bookingRequestId: {}", bookingRequestId);
        return true;
    }

    private static boolean hasIncompleteGroomingService(BookingRequestModel bookingRequestModel) {
        var groomings = bookingRequestModel.getServicesList().stream()
                .filter(BookingRequestModel.Service::hasGrooming)
                .toList();

        for (var grooming : groomings) {
            var service = grooming.getGrooming().getService();
            if (!isNormal(service.getStaffId()) || !service.hasStartTime()) {
                return true;
            }
        }

        return false;
    }

    private void sendNotification(long bookingRequestId, boolean isAutoAccepted) {

        var bookingRequest = bookingRequestService.mustGet(bookingRequestId);

        // web 站内 + mobile notification
        sendOBReceivedNotification(bookingRequest);

        // 发送 'Online booking -> Notification -> When client submit booking request -> Business receive' 通知
        sendOBSubmittedNotification(bookingRequest, isAutoAccepted);
    }

    private void sendOBSubmittedNotification(BookingRequest bookingRequest, boolean isAutoAccepted) {
        OnlineBookWaitingNotifyParams onlineBookWaitingNotifyParams = new OnlineBookWaitingNotifyParams();
        onlineBookWaitingNotifyParams.setCompanyId(bookingRequest.getCompanyId());
        onlineBookWaitingNotifyParams.setBusinessId(toIntExact(bookingRequest.getBusinessId()));
        onlineBookWaitingNotifyParams.setBookingRequestId(bookingRequest.getId());
        if (isAutoAccepted) {
            onlineBookWaitingNotifyParams.setType(OnlineBookWaitingNotifyParams.TYPE_ACCEPT);
        } else {
            onlineBookWaitingNotifyParams.setType(OnlineBookWaitingNotifyParams.TYPE_SUBMIT);
        }
        notificationApi.bookOnlineNotify(onlineBookWaitingNotifyParams);
    }

    private void sendOBReceivedNotification(BookingRequest bookingRequest) {
        var customer = customerHelper.mustGetCustomer(bookingRequest.getCustomerId());

        NotificationExtraOBReqestDto extra = new NotificationExtraOBReqestDto();
        extra.setGroomingId(0);
        extra.setAppointmentDate(bookingRequest.getStartDate());
        extra.setAppointmentStartTime(bookingRequest.getStartTime());
        extra.setNoStartTime(false);
        extra.setAppointmentEndTime(bookingRequest.getEndTime());
        extra.setCustomerId(toIntExact(bookingRequest.getCustomerId()));
        extra.setCustomerFirstName(customer.getFirstName());
        extra.setCustomerLastName(customer.getLastName());
        extra.setStaffId(0);
        extra.setBookingRequestId(bookingRequest.getId());
        extra.setServices(List.of());

        NotificationOBRequestReceivedParams param = new NotificationOBRequestReceivedParams();
        param.setBusinessId(toIntExact(bookingRequest.getBusinessId()));
        param.setIsSendMobilePush(false); // 不给 mobile 发通知，mobile 还没有适配 BD
        param.setWebPushDto(extra);
        notificationApi.sendNotificationOBRequestReceived(param);
    }

    private boolean hasRequiredStaffAddOn(BookingRequestModel bookingRequest, long companyId) {
        List<Long> addOnIds = bookingRequest.getServicesList().stream()
                .flatMap(service -> switch (service.getServiceCase()) {
                    case GROOMING -> Optional.of(service.getGrooming().getAddonsList()).orElse(List.of()).stream()
                            .map(GroomingAddOnDetailModel::getAddOnId);
                    case BOARDING -> Optional.of(service.getBoarding().getAddonsList()).orElse(List.of()).stream()
                            .map(BoardingAddOnDetailModel::getAddOnId);
                    case DAYCARE -> Optional.of(service.getDaycare().getAddonsList()).orElse(List.of()).stream()
                            .map(DaycareAddOnDetailModel::getAddOnId);
                    default -> Stream.empty();
                })
                .distinct()
                .toList();

        if (isEmpty(addOnIds)) {
            return false;
        }

        boolean hasRequiredStaffAddOn = serviceStub
                .getServiceListByIds(GetServiceListByIdsRequest.newBuilder()
                        .setCompanyId(companyId)
                        .addAllServiceIds(addOnIds)
                        .build())
                .getServicesList()
                .stream()
                .anyMatch(service -> ServiceType.ADDON.equals(service.getType()) && service.getRequireDedicatedStaff());
        if (hasRequiredStaffAddOn) {
            log.info("Auto accept failed, add on required staff. bookingRequestId: {}", bookingRequest.getId());
            return true;
        }

        return false;
    }

    private boolean canAutoAccept(
            BookingRequest bookingRequest, ServiceItemType serviceItemType, Collection<Long> obRequestPetIds) {
        long companyId = bookingRequest.getCompanyId();
        long businessId = bookingRequest.getBusinessId();
        long customerId = bookingRequest.getCustomerId();

        var automationSetting = AutomationConverter.INSTANCE.toModel(
                automationSettingService.getAutomationSetting(companyId, businessId, serviceItemType));

        if (!automationSetting.getEnableAutoAccept()) {
            log.info("Auto accept failed, auto accept not enabled. bookingRequestId: {}", bookingRequest.getId());
            return false;
        }

        AutomationConditionDef autoAcceptCondition = automationSetting.getAutoAcceptCondition();
        AcceptClientType acceptClientType = autoAcceptCondition.getAcceptClientType();
        ProfileUpdateCondition profileUpdatesCondition = autoAcceptCondition.getProfileUpdateCondition();
        VaccineStatusCondition vaccineStatusCondition = autoAcceptCondition.getVaccineStatusCondition();

        if (AcceptClientType.ACCEPT_CLIENT_TYPE_BOTH.equals(acceptClientType)
                && ProfileUpdateCondition.PROFILE_UPDATE_CONDITION_ALL.equals(profileUpdatesCondition)
                && VaccineStatusCondition.VACCINE_STATUS_CONDITION_ALL.equals(vaccineStatusCondition)) {
            return true;
        }

        boolean canAutoAccept =
                switch (acceptClientType) {
                    case ACCEPT_CLIENT_TYPE_NEW -> {
                        boolean isNewCustomer = bookingRequest.getAttr().getIsNewVisitor();
                        if (!isNewCustomer) {
                            log.info(
                                    "Auto accept failed, customer is existing. bookingRequestId: {}",
                                    bookingRequest.getId());
                        }
                        yield isNewCustomer;
                    }
                    case ACCEPT_CLIENT_TYPE_EXISTING -> {
                        boolean isExistingCustomer = !bookingRequest.getAttr().getIsNewVisitor();
                        if (!isExistingCustomer) {
                            log.info(
                                    "Auto accept failed, customer is new. bookingRequestId: {}",
                                    bookingRequest.getId());
                        }
                        yield isExistingCustomer;
                    }
                    default -> true;
                };
        if (Boolean.FALSE.equals(canAutoAccept)) {
            return false;
        }

        var customerUpdateDTO = obApi.listCustomerHasRequestUpdate(
                        new CustomerIdsParams(toIntExact(businessId), List.of(toIntExact(customerId))))
                .get(toIntExact(customerId));
        canAutoAccept = switch (profileUpdatesCondition) {
            case PROFILE_UPDATE_CONDITION_WITHOUT_UPDATE -> {
                boolean noUpdate = !customerUpdateDTO.hasRequestUpdate();
                if (!noUpdate) {
                    log.info(
                            "Auto accept failed, profile has been updated. bookingRequestId: {}",
                            bookingRequest.getId());
                }
                yield noUpdate;
            }
            default -> true;};
        if (Boolean.FALSE.equals(canAutoAccept)) {
            return false;
        }

        List<CustomerProfileRequestDTO.PetProfileDTO> pets = customerUpdateDTO.mergedProfile().getPets().stream()
                .filter(pet -> obRequestPetIds.contains(pet.getPetId().longValue()))
                .toList();
        return switch (vaccineStatusCondition) {
            case VACCINE_STATUS_CONDITION_NO_MISSING_OR_EXPIRED -> {
                boolean notExpired = vaccineHelper.vaccineNotExpired(pets, bookingRequest.getEndDate());
                if (!notExpired) {
                    log.info("Auto accept failed, vaccine is expired. bookingRequestId: {}", bookingRequest.getId());
                    yield false;
                }
                boolean notMissing = vaccineHelper.vaccineNotMissing(pets, companyId, serviceItemType);
                if (!notMissing) {
                    log.info("Auto accept failed, vaccine is missing. bookingRequestId: {}", bookingRequest.getId());
                }
                yield notMissing;
            }
            default -> true;
        };
    }

    void refreshBookingOnlineDateTime(Long bookingRequestId) {
        var bookingRequest = bookingRequestService.mustGet(bookingRequestId);
        var services = listServices(List.of(bookingRequest), Set.of(BookingRequestAssociatedModel.SERVICE))
                .getOrDefault(bookingRequestId, List.of());
        var updateBean = reCalculateBookingRequestTime(services);
        updateBean.setId(bookingRequestId);
        bookingRequestService.update(updateBean);
    }

    static BookingRequest reCalculateBookingRequestTime(List<BookingRequestModel.Service> services) {
        List<DatePoint> starts = new ArrayList<>();
        List<DatePoint> ends = new ArrayList<>();
        for (var service : services) {
            switch (service.getServiceCase()) {
                case GROOMING -> {
                    var grooming = service.getGrooming().getService();
                    String startDate = grooming.getStartDate();
                    String endDate = grooming.getEndDate();
                    Integer startTime = grooming.getStartTime();
                    Integer endTime = grooming.getEndTime();
                    starts.add(new DatePoint(startDate, startTime));
                    ends.add(new DatePoint(endDate, endTime));
                }
                case BOARDING -> {
                    var boarding = service.getBoarding().getService();
                    String startDate = boarding.getStartDate();
                    String endDate = boarding.getEndDate();
                    Integer startTime = boarding.getStartTime();
                    Integer endTime = boarding.getEndTime();
                    starts.add(new DatePoint(startDate, startTime));
                    ends.add(new DatePoint(endDate, endTime));
                }
                case DAYCARE -> {
                    var daycare = service.getDaycare().getService();

                    List<String> dates =
                            daycare.getSpecificDatesList().stream().sorted().toList();
                    String startDate = firstElement(dates);
                    String endDate = lastElement(dates);
                    Integer startTime = daycare.getStartTime();
                    Integer endTime = daycare.getEndTime();
                    starts.add(new DatePoint(startDate, startTime));
                    ends.add(new DatePoint(endDate, endTime));
                }
                case EVALUATION -> {
                    var evaluation = service.getEvaluation().getService();

                    String startDate = evaluation.getStartDate();
                    String endDate = evaluation.getEndDate();
                    int startTime = evaluation.getStartTime();
                    Integer endTime = evaluation.getEndTime();

                    starts.add(new DatePoint(startDate, startTime));
                    ends.add(new DatePoint(endDate, endTime));
                }
                case DOG_WALKING -> {
                    var dogWalking = service.getDogWalking().getService();

                    var startDate = dogWalking.getStartDate();
                    String endDate = dogWalking.getEndDate();
                    var startTime = dogWalking.getStartTime();
                    Integer endTime = dogWalking.getEndTime();

                    starts.add(new DatePoint(startDate, startTime));
                    ends.add(new DatePoint(endDate, endTime));
                }
                default -> {
                    // no-op
                }
            }
        }
        DatePoint min = DatePoint.min(starts);
        DatePoint max = DatePoint.max(ends);

        var updateBean = new BookingRequest();
        updateBean.setStartDate(min.date());
        updateBean.setStartTime(min.time());
        updateBean.setEndDate(max.date());
        updateBean.setEndTime(max.time());
        return updateBean;
    }

    @Override
    public void autoAssign(AutoAssignRequest request, StreamObserver<AutoAssignResponse> responseObserver) {

        var bookingRequest = mustGetBookingRequest(
                request.hasCompanyId() ? request.getCompanyId() : null, request.getBookingRequestId());

        var serviceDetails = listService(bookingRequest);

        var bookingRequestModel = BookingRequestConverter.INSTANCE.entityToModel(bookingRequest).toBuilder()
                .addAllServices(serviceDetails)
                .build();

        var assignResult = assign(bookingRequestModel);

        responseObserver.onNext(buildAutoAssignResponse(assignResult, serviceDetails));
        responseObserver.onCompleted();
    }

    private static AutoAssignResponse buildAutoAssignResponse(
            GetAutoAssignResponse assignResult, List<BookingRequestModel.Service> serviceDetails) {
        var builder = AutoAssignResponse.newBuilder();

        var boardingServiceDetailIdToLodgingId = buildAssignResultForBoardingService(assignResult, serviceDetails);

        for (var en : boardingServiceDetailIdToLodgingId.entrySet()) {
            var boardingServiceDetailId = en.getKey();
            var lodgingId = en.getValue();

            var boardingBuilder = AutoAssignResponse.BoardingService.newBuilder();
            boardingBuilder.setId(boardingServiceDetailId);
            if (isNormal(lodgingId)) {
                boardingBuilder.setLodgingId(lodgingId);
            }

            builder.addBoardingServices(boardingBuilder.build());
        }

        var evaluationServiceDetailIdToStaffId = buildAssignResultForEvaluationService(assignResult, serviceDetails);

        for (var en : evaluationServiceDetailIdToStaffId.entrySet()) {
            var evaluationServiceDetailId = en.getKey();
            var staffId = en.getValue();

            var evaluationBuilder = AutoAssignResponse.EvaluationService.newBuilder();
            evaluationBuilder.setId(evaluationServiceDetailId);
            if (isNormal(staffId)) {
                evaluationBuilder.setStaffId(staffId);
            }

            builder.addEvaluationServices(evaluationBuilder.build());
        }

        return builder.build();
    }

    /**
     * @return evaluation_test_detail id -> staff id
     */
    private static Map<Long, Long> buildAssignResultForEvaluationService(
            GetAutoAssignResponse assignResult, List<BookingRequestModel.Service> serviceDetails) {
        // assignResult 是 pet id -> staff id
        // 由于一个 pet 不会存在多个 evaluation service，使用 pet id 可以找到对应的 evaluation_test_detail id
        return assignResult.getEvaluationPetToStaffsList().stream()
                .map(e -> {
                    var evaluationService = serviceDetails.stream()
                            .filter(BookingRequestModel.Service::hasEvaluation)
                            .map(it -> it.getEvaluation().getService())
                            .filter(it -> Objects.equals(it.getPetId(), e.getPetId()))
                            .findFirst()
                            .orElse(null);
                    return evaluationService != null ? Map.entry(evaluationService.getId(), e.getStaffId()) : null;
                })
                .filter(Objects::nonNull)
                .collect(toMap(Map.Entry::getKey, Map.Entry::getValue, (o, n) -> o));
    }

    /**
     * @return boarding_service_detail id -> lodging unit id
     */
    private static Map<Long, Long> buildAssignResultForBoardingService(
            GetAutoAssignResponse assignResult, List<BookingRequestModel.Service> serviceDetails) {
        // assignResult 是 pet id -> lodging id
        // 由于一个 pet 不会存在多个 boarding service，使用 pet id 可以找到对应的 boarding_service_detail id
        return assignResult.getPetToLodgingsList().stream()
                .map(e -> {
                    var boardingService = serviceDetails.stream()
                            .filter(BookingRequestModel.Service::hasBoarding)
                            .map(it -> it.getBoarding().getService())
                            .filter(it -> Objects.equals(it.getPetId(), e.getPetId()))
                            .findFirst()
                            .orElse(null);
                    return boardingService != null ? Map.entry(boardingService.getId(), e.getLodgingUnitId()) : null;
                })
                .filter(Objects::nonNull)
                .collect(toMap(Map.Entry::getKey, Map.Entry::getValue, (o, n) -> o));
    }

    private GetAutoAssignResponse assign(BookingRequestModel bookingRequestModel) {
        var builder = GetAutoAssignResponse.newBuilder();
        doLodgingAssign(bookingRequestModel, builder);
        doStaffAssign(bookingRequestModel, builder);
        return builder.build();
    }

    private long createOrder(CreateBookingRequestRequest req, long bookingRequestId) {

        var bookingRequest = mustGetBookingRequestModel(req.getCompanyId(), bookingRequestId);

        var orderId = doCreateOrder(
                bookingRequest, listCustomizedService(req.getCompanyId(), req.getBusinessId(), getPetServices(req)));

        // 标记 order 是通过新流程创建的，这样才能在 payment 回调里判断是否需要进行 auto accept 和 send notification 等逻辑
        orderDecouplingFlowMarkerServiceApi.insertOrderDecouplingFlowMarker(orderId);

        // membership info for obc submit boarding&daycare
        if (req.hasMembership()) {
            ThreadPool.execute(() -> membershipService.applyMemberships(
                    bookingRequest.getBusinessId(),
                    bookingRequest.getCompanyId(),
                    0L,
                    orderId,
                    req.getMembership().getMembershipIdsList()));
        }

        return orderId;
    }

    private Map<Long, ServiceModel> listService(BookingRequestModel bookingRequest) {

        var serviceIds = bookingRequest.getServicesList().stream()
                .map(service -> switch (service.getServiceCase()) {
                    case GROOMING -> service.getGrooming().getService().getServiceId();
                    case BOARDING -> service.getBoarding().getService().getServiceId();
                    case DAYCARE -> service.getDaycare().getService().getServiceId();
                    case DOG_WALKING -> service.getDogWalking().getService().getServiceId();
                    default -> null;
                })
                .filter(Objects::nonNull)
                .distinct()
                .toList();

        var addonIds = bookingRequest.getServicesList().stream()
                .map(service -> switch (service.getServiceCase()) {
                    case BOARDING -> service.getBoarding().getAddonsList().stream()
                            .map(BoardingAddOnDetailModel::getAddOnId)
                            .toList();
                    case DAYCARE -> service.getDaycare().getAddonsList().stream()
                            .map(DaycareAddOnDetailModel::getAddOnId)
                            .toList();
                    default -> List.<Long>of();
                })
                .flatMap(List::stream)
                .distinct()
                .toList();

        var allIds = new ArrayList<>(serviceIds);
        allIds.addAll(addonIds);

        return !allIds.isEmpty()
                ? serviceStub
                        .getServiceList(GetServiceListRequest.newBuilder()
                                .setTokenCompanyId(bookingRequest.getCompanyId())
                                .addBusinessIds(bookingRequest.getBusinessId())
                                .addAllServiceIds(allIds)
                                .build())
                        .getCategoryListList()
                        .stream()
                        .map(ServiceCategoryModel::getServicesList)
                        .flatMap(Collection::stream)
                        .collect(toMap(ServiceModel::getServiceId, identity(), (o, n) -> o))
                : Map.of();
    }

    private Map<Long, List<CreateBookingRequestRequest.Service>> getPetServices(CreateBookingRequestRequest req) {
        Map<Long, List<CreateBookingRequestRequest.Service>> petToServices = new HashMap<>();
        for (var service : req.getServicesList()) {
            if (service.hasGrooming()) {
                petToServices
                        .computeIfAbsent(service.getGrooming().getService().getPetId(), k -> new ArrayList<>())
                        .add(service);
            }
        }
        return petToServices;
    }

    private List<ServiceWithCustomizedInfo> listCustomizedService(
            Long companyId, Long businessId, Map<Long, List<CreateBookingRequestRequest.Service>> items) {

        var builder = BatchGetCustomizedServiceRequest.newBuilder();

        builder.setCompanyId(companyId);

        for (var petAndServiceList : items.entrySet()) {
            var petId = petAndServiceList.getKey();
            for (var service : petAndServiceList.getValue()) {
                var serviceId = getServiceId(service);
                if (!isNormal(serviceId)) {
                    continue;
                }

                var condBuilder = CustomizedServiceQueryCondition.newBuilder()
                        .setServiceId(serviceId)
                        .setBusinessId(businessId);
                if (isNormal(petId)) {
                    condBuilder.setPetId(petId);
                }
                var staffId = getStaffId(service);
                if (isNormal(staffId)) {
                    condBuilder.setStaffId(staffId);
                }
                builder.addQueryConditionList(condBuilder.build());
            }
        }

        for (var petAndAddonList : buildPetToAddonsMapForBoarding(items).entrySet()) {
            var petId = petAndAddonList.getKey();
            for (var addon : petAndAddonList.getValue()) {
                var condBuilder = CustomizedServiceQueryCondition.newBuilder()
                        .setServiceId(addon.getAddOnId())
                        .setBusinessId(businessId);
                if (isNormal(petId)) {
                    condBuilder.setPetId(petId);
                }
                builder.addQueryConditionList(condBuilder.build());
            }
        }

        for (var petAndAddonList : buildPetToAddonsMapForDaycare(items).entrySet()) {
            var petId = petAndAddonList.getKey();
            for (var addon : petAndAddonList.getValue()) {
                var condBuilder = CustomizedServiceQueryCondition.newBuilder()
                        .setServiceId(addon.getAddOnId())
                        .setBusinessId(businessId);
                if (isNormal(petId)) {
                    condBuilder.setPetId(petId);
                }
                builder.addQueryConditionList(condBuilder.build());
            }
        }

        if (builder.getQueryConditionListList().isEmpty()) {
            return List.of();
        }

        return serviceStub.batchGetCustomizedService(builder.build()).getCustomizedServiceListList();
    }

    private static IdentityHashMap<Long, List<CreateBoardingAddOnDetailRequest>> buildPetToAddonsMapForBoarding(
            Map<Long, List<CreateBookingRequestRequest.Service>> items) {

        var petToAddons = new IdentityHashMap<Long, List<CreateBoardingAddOnDetailRequest>>();
        for (var item : items.entrySet()) {
            for (var service : item.getValue()) {
                if (service.getServiceCase() == CreateBookingRequestRequest.Service.ServiceCase.BOARDING) {
                    petToAddons
                            .computeIfAbsent(item.getKey(), k -> new ArrayList<>())
                            .addAll(service.getBoarding().getAddonsList());
                }
            }
        }

        return petToAddons;
    }

    private static IdentityHashMap<Long, List<CreateDaycareAddOnDetailRequest>> buildPetToAddonsMapForDaycare(
            Map<Long, List<CreateBookingRequestRequest.Service>> items) {

        var petToAddons = new IdentityHashMap<Long, List<CreateDaycareAddOnDetailRequest>>();

        for (var item : items.entrySet()) {
            for (var service : item.getValue()) {
                if (service.getServiceCase() == CreateBookingRequestRequest.Service.ServiceCase.DAYCARE) {
                    petToAddons
                            .computeIfAbsent(item.getKey(), k -> new ArrayList<>())
                            .addAll(service.getDaycare().getAddonsList());
                }
            }
        }

        return petToAddons;
    }

    @Nullable
    private static Long getServiceId(CreateBookingRequestRequest.Service service) {
        // TODO(Freeman): 如果这个接口需要支持 grooming，需要修改这里
        return switch (service.getServiceCase()) {
            case BOARDING -> service.getBoarding().getService().getServiceId();
            case DAYCARE -> service.getDaycare().getService().getServiceId();
            default -> null;
        };
    }

    @Nullable
    private static Long getStaffId(CreateBookingRequestRequest.Service service) {
        // 暂时只支持 boarding 和 daycare，都不支持选择 staff
        return null;
    }

    private long doCreateOrder(
            BookingRequestModel bookingRequest, List<ServiceWithCustomizedInfo> customizedServiceList) {

        var serviceIdToService = listService(bookingRequest);
        var evaluationIdToEvaluation = listEvaluation(bookingRequest);
        var taxIdToTax = listTax(serviceIdToService.values());
        var customer = customerHelper.mustGetCustomer(bookingRequest.getCustomerId());
        var pricingRuleResults = calculatePricingRuleResults(bookingRequest, serviceIdToService, customizedServiceList);

        return orderStub
                .createOrder(CreateOrderRequest.newBuilder()
                        .setOrder(
                                buildOrderModel(bookingRequest, serviceIdToService, evaluationIdToEvaluation, customer))
                        .addAllLineItems(buildLineItemsForServiceDetails(
                                bookingRequest,
                                serviceIdToService,
                                evaluationIdToEvaluation,
                                taxIdToTax,
                                pricingRuleResults))
                        .build())
                .getId();
    }

    private Map<Long, TaxRuleModel> listTax(Collection<ServiceModel> services) {
        var taxIds = services.stream()
                .map(ServiceModel::getTaxId)
                .filter(CommonUtil::isNormal)
                .distinct()
                .toList();

        if (taxIds.isEmpty()) {
            return Map.of();
        }
        return taxRuleStub
                .batchGetTaxRule(
                        BatchGetTaxRuleRequest.newBuilder().addAllIds(taxIds).build())
                .getRulesList()
                .stream()
                .collect(Collectors.toMap(TaxRuleModel::getId, identity(), (o, n) -> o));
    }

    private Map<Long, EvaluationModel> listEvaluation(BookingRequestModel bookingRequest) {
        var evaluationIds = bookingRequest.getServicesList().stream()
                .map(service -> switch (service.getServiceCase()) {
                    case EVALUATION -> service.getEvaluation().getService().getEvaluationId();
                    default -> null;
                })
                .filter(Objects::nonNull)
                .distinct()
                .toList();

        return evaluationIds.stream()
                .map(evaluationId -> evaluationStub.getEvaluation(GetEvaluationRequest.newBuilder()
                        .setId(evaluationId)
                        .build())) // 一个 booking request 只会有一个 evaluation，没有提供批量查询接口
                .filter(GetEvaluationResponse::hasEvaluationModel)
                .map(GetEvaluationResponse::getEvaluationModel)
                .collect(toMap(EvaluationModel::getId, identity(), (o, n) -> o));
    }

    private static List<OrderLineItemModel> buildLineItemsForServiceDetails(
            BookingRequestModel bookingRequest,
            Map<Long, ServiceModel> serviceIdToService,
            Map<Long, EvaluationModel> evaluationIdToEvaluation,
            Map<Long, TaxRuleModel> taxIdToTax,
            List<PetDetailCalculateResultDef> pricingRuleResults) {

        var petDetails = bookingRequest.getServicesList();
        if (petDetails.isEmpty()) {
            return List.of();
        }

        var mainServiceItemType = ServiceItemEnum.getMainServiceItemType(bookingRequest.getServiceTypeInclude());

        var result =
                switch (mainServiceItemType) {
                    case BOARDING -> buildLineItemsForBoarding(
                            bookingRequest, serviceIdToService, taxIdToTax, pricingRuleResults);
                    case DAYCARE -> buildLineItemsForDaycare(
                            bookingRequest, serviceIdToService, taxIdToTax, pricingRuleResults);
                    case GROOMING -> buildLineItemsForGrooming(bookingRequest, serviceIdToService, taxIdToTax);
                    case EVALUATION -> buildLineItemsForEvaluation(bookingRequest, evaluationIdToEvaluation);
                    case DOG_WALKING -> buildLineItemsForDogWalking(bookingRequest, serviceIdToService, taxIdToTax);
                    default -> throw bizException(
                            Code.CODE_PARAMS_ERROR, "Unsupported service type: " + mainServiceItemType);
                };

        return mergeByPetIdAndServiceIdAndPrice(result);
    }

    private static OrderModel buildOrderModel(
            BookingRequestModel bookingRequest,
            Map<Long, ServiceModel> serviceIdToService,
            Map<Long, EvaluationModel> evaluationIdToEvaluation,
            BusinessCustomerInfoModel customer) {

        var desc = String.format("%s %s", customer.getFirstName(), customer.getLastName());
        var title = buildTitle(bookingRequest, serviceIdToService, evaluationIdToEvaluation);

        return OrderModel.newBuilder()
                .setCompanyId(bookingRequest.getCompanyId())
                .setBusinessId(bookingRequest.getBusinessId())
                .setCustomerId(bookingRequest.getCustomerId())
                .setSourceType(OrderSourceType.BOOKING_REQUEST.name().toLowerCase())
                .setStatus(OrderStatus.CREATED.getNumber())
                .setSource(com.moego.idl.models.grooming.v1.AppointmentSource.APPOINTMENT_SOURCE_OB)
                .setSourceId(bookingRequest.getId())
                .setDescription(desc)
                .setTitle(title)
                .setCreateBy(0)
                .setUpdateBy(0)
                .build();
    }

    private static List<OrderLineItemModel> buildLineItemsForDogWalking(
            BookingRequestModel bookingRequest,
            Map<Long, ServiceModel> serviceIdToService,
            Map<Long, TaxRuleModel> taxIdToTax) {

        var result = new ArrayList<OrderLineItemModel>();

        for (var dogWalkingService : getDogWalkingServices(bookingRequest)) {
            result.addAll(
                    buildLineItemForDogWalking(bookingRequest, serviceIdToService, taxIdToTax, dogWalkingService));
        }

        return result;
    }

    private static List<OrderLineItemModel> buildLineItemsForEvaluation(
            BookingRequestModel bookingRequest, Map<Long, EvaluationModel> evaluationIdToEvaluation) {

        var result = new ArrayList<OrderLineItemModel>();

        for (var evaluationService : getEvaluationServices(bookingRequest)) {
            result.addAll(buildLineItemForEvaluation(bookingRequest, evaluationIdToEvaluation, evaluationService));
        }

        return result;
    }

    private static List<OrderLineItemModel> buildLineItemsForGrooming(
            BookingRequestModel bookingRequest,
            Map<Long, ServiceModel> serviceIdToService,
            Map<Long, TaxRuleModel> taxIdToTax) {

        var result = new ArrayList<OrderLineItemModel>();

        for (var groomingService : getGroomingServices(bookingRequest)) {
            result.addAll(buildLineItemForGrooming(bookingRequest, serviceIdToService, taxIdToTax, groomingService));
        }

        return result;
    }

    private static List<OrderLineItemModel> buildLineItemsForDaycare(
            BookingRequestModel bookingRequest,
            Map<Long, ServiceModel> serviceIdToService,
            Map<Long, TaxRuleModel> taxIdToTax,
            List<PetDetailCalculateResultDef> pricingRuleResults) {

        var result = new ArrayList<OrderLineItemModel>();

        // 特别注意：daycare 只收第一天的钱

        String firstDate = getFirstDateForDaycare(bookingRequest);

        var daycareServices = getDaycareServices(bookingRequest).stream()
                .filter(e -> e.getService().getSpecificDatesList().contains(firstDate))
                .toList();

        for (var daycareService : daycareServices) {
            result.addAll(buildLineItemForDaycare(
                    bookingRequest, serviceIdToService, taxIdToTax, daycareService, pricingRuleResults));
        }

        var groomingServicesInFirstDate = getGroomingServices(bookingRequest).stream()
                .filter(e -> Objects.equals(e.getService().getStartDate(), firstDate))
                .toList();

        for (var groomingService : groomingServicesInFirstDate) {
            result.addAll(buildLineItemForGrooming(bookingRequest, serviceIdToService, taxIdToTax, groomingService));
        }

        return result;
    }

    private static String getFirstDateForDaycare(BookingRequestModel bookingRequest) {
        return getDaycareServices(bookingRequest).stream()
                .flatMap(e -> e.getService().getSpecificDatesList().stream())
                .sorted()
                .findFirst()
                .orElseThrow(
                        () -> bizException(Code.CODE_PARAMS_ERROR, "There is no specific date for daycare service"));
    }

    private static List<OrderLineItemModel> buildLineItemsForBoarding(
            BookingRequestModel bookingRequest,
            Map<Long, ServiceModel> serviceIdToService,
            Map<Long, TaxRuleModel> taxIdToTax,
            List<PetDetailCalculateResultDef> pricingRuleResults) {

        var result = new ArrayList<OrderLineItemModel>();

        for (var boardingService : getBoardingServices(bookingRequest)) {
            result.addAll(buildLineItemForBoarding(
                    bookingRequest, serviceIdToService, taxIdToTax, boardingService, pricingRuleResults));
        }

        for (var groomingService : getGroomingServices(bookingRequest)) {
            result.addAll(buildLineItemForGrooming(bookingRequest, serviceIdToService, taxIdToTax, groomingService));
        }

        return result;
    }

    private static List<BookingRequestModel.BoardingService> getBoardingServices(BookingRequestModel bookingRequest) {
        return bookingRequest.getServicesList().stream()
                .filter(BookingRequestModel.Service::hasBoarding)
                .map(BookingRequestModel.Service::getBoarding)
                .toList();
    }

    private static List<BookingRequestModel.DaycareService> getDaycareServices(BookingRequestModel bookingRequest) {
        return bookingRequest.getServicesList().stream()
                .filter(BookingRequestModel.Service::hasDaycare)
                .map(BookingRequestModel.Service::getDaycare)
                .toList();
    }

    private static List<BookingRequestModel.GroomingService> getGroomingServices(BookingRequestModel bookingRequest) {
        return bookingRequest.getServicesList().stream()
                .filter(BookingRequestModel.Service::hasGrooming)
                .map(BookingRequestModel.Service::getGrooming)
                .toList();
    }

    private static List<BookingRequestModel.EvaluationService> getEvaluationServices(
            BookingRequestModel bookingRequest) {
        return bookingRequest.getServicesList().stream()
                .filter(BookingRequestModel.Service::hasEvaluation)
                .map(BookingRequestModel.Service::getEvaluation)
                .toList();
    }

    private static List<BookingRequestModel.DogWalkingService> getDogWalkingServices(
            BookingRequestModel bookingRequest) {
        return bookingRequest.getServicesList().stream()
                .filter(BookingRequestModel.Service::hasDogWalking)
                .map(BookingRequestModel.Service::getDogWalking)
                .toList();
    }

    private static List<OrderLineItemModel> mergeByPetIdAndServiceIdAndPrice(List<OrderLineItemModel> lineItems) {
        var uniqKeyToLineItem = lineItems.stream()
                .collect(Collectors.groupingBy(
                        e -> e.getPetId() + ":" + e.getObjectId() + ":"
                                + e.getUnitPrice(), // pet id, serviceId 和 price 分组
                        Collectors.collectingAndThen(Collectors.toList(), list -> {
                            var builder = list.get(0).toBuilder();
                            builder.setQuantity(list.stream()
                                    .mapToInt(OrderLineItemModel::getQuantity)
                                    .sum());
                            return builder.build();
                        })));
        return List.copyOf(uniqKeyToLineItem.values());
    }

    private static List<OrderLineItemModel> buildLineItemForEvaluation(
            BookingRequestModel bookingRequest,
            Map<Long, EvaluationModel> evaluationIdToEvaluation,
            BookingRequestModel.EvaluationService evaluation) {

        var petDetail = evaluation.getService();
        var evaluationModel = Optional.ofNullable(evaluationIdToEvaluation.get(petDetail.getEvaluationId()))
                .orElseThrow(() ->
                        bizException(Code.CODE_PARAMS_ERROR, "Evaluation not found: " + petDetail.getEvaluationId()));

        var builder = OrderLineItemModel.newBuilder();
        builder.setBusinessId(bookingRequest.getBusinessId());
        builder.setObjectId(petDetail.getEvaluationId());
        builder.setType(OrderItemType.ITEM_TYPE_EVALUATION_SERVICE.getType());
        builder.setName(evaluationModel.getName());
        builder.setUnitPrice(petDetail.getServicePrice());
        builder.setQuantity(1);
        builder.setPetId(petDetail.getPetId());

        return List.of(builder.build());
    }

    private static List<OrderLineItemModel> buildLineItemForDaycare(
            BookingRequestModel bookingRequest,
            Map<Long, ServiceModel> serviceIdToService,
            Map<Long, TaxRuleModel> taxIdToTax,
            BookingRequestModel.DaycareService daycare,
            List<PetDetailCalculateResultDef> pricingRuleResults) {

        var petDetail = daycare.getService();
        var service = Optional.ofNullable(serviceIdToService.get(petDetail.getServiceId()))
                .orElseThrow(
                        () -> bizException(Code.CODE_PARAMS_ERROR, "Service not found: " + petDetail.getServiceId()));

        // NOTE: 这里有个特殊逻辑：daycare 为多天时，只计算第一天的价格
        // See https://moego.atlassian.net/browse/MER-1091
        var date =
                petDetail.getSpecificDatesList().stream().sorted().findFirst().orElse(null);

        if (date == null) {
            return List.of();
        }

        var result = new ArrayList<OrderLineItemModel>();

        // service
        var serviceBuilder = OrderLineItemModel.newBuilder();
        serviceBuilder.setBusinessId(bookingRequest.getBusinessId());
        serviceBuilder.setObjectId(petDetail.getServiceId());
        serviceBuilder.setType(OrderItemType.ITEM_TYPE_SERVICE.getType());
        serviceBuilder.setName(service.getName());
        serviceBuilder.setDescription(service.getDescription());
        serviceBuilder.setUnitPrice(getPrice(pricingRuleResults, petDetail, date));
        serviceBuilder.setQuantity(1);
        serviceBuilder.setPetId(petDetail.getPetId());

        var serviceTaxId = service.getTaxId();
        if (serviceTaxId > 0) {
            var tax = Optional.ofNullable(taxIdToTax.get(serviceTaxId))
                    .orElseThrow(() -> bizException(Code.CODE_PARAMS_ERROR, "Tax not found: " + serviceTaxId));
            serviceBuilder.addLineTaxes(buildOrderLineTax(bookingRequest, tax));
        }

        result.add(serviceBuilder.build());

        // addons
        for (var addon : daycare.getAddonsList()) {
            if (!addon.getIsEveryday() && !addon.getSpecificDatesList().contains(date)) { // 只算第一天的 addon
                continue;
            }

            var addonModel = Optional.ofNullable(serviceIdToService.get(addon.getAddOnId()))
                    .orElseGet(ServiceModel::getDefaultInstance);

            var builder = OrderLineItemModel.newBuilder();

            builder.setObjectId(addon.getAddOnId());
            builder.setType(OrderItemType.ITEM_TYPE_SERVICE.getType());
            builder.setName(addonModel.getName());
            builder.setDescription(addonModel.getDescription());
            builder.setUnitPrice(addon.getServicePrice());
            builder.setQuantity(addon.getQuantityPerDay());
            builder.setPetId(addon.getPetId());
            var taxId = addonModel.getTaxId();
            if (taxId > 0) {
                var tax = Optional.ofNullable(taxIdToTax.get(taxId))
                        .orElseThrow(() -> bizException(Code.CODE_PARAMS_ERROR, "Tax not found: " + taxId));
                builder.addLineTaxes(buildOrderLineTax(bookingRequest, tax));
            }

            result.add(builder.build());
        }

        return result;
    }

    private static Double getPrice(
            List<PetDetailCalculateResultDef> pricingRuleResults, DaycareServiceDetailModel petDetail, String date) {
        return pricingRuleResults.stream()
                .filter(e -> e.getPetId() == petDetail.getPetId()
                        && e.getServiceId() == petDetail.getServiceId()
                        && Objects.equals(e.getServiceDate(), date))
                .findFirst()
                .map(PetDetailCalculateResultDef::getAdjustedPrice)
                .orElseGet(petDetail::getServicePrice);
    }

    private static List<OrderLineItemModel> buildLineItemForBoarding(
            BookingRequestModel bookingRequest,
            Map<Long, ServiceModel> serviceIdToService,
            Map<Long, TaxRuleModel> taxIdToTax,
            BookingRequestModel.BoardingService boarding,
            List<PetDetailCalculateResultDef> pricingRuleResults) {

        var petDetail = boarding.getService();
        var service = Optional.ofNullable(serviceIdToService.get(petDetail.getServiceId()))
                .orElseThrow(
                        () -> bizException(Code.CODE_PARAMS_ERROR, "Service not found: " + petDetail.getServiceId()));

        var result = new ArrayList<OrderLineItemModel>();

        var start = LocalDate.parse(petDetail.getStartDate());
        var end = isCalculateByDay(service)
                ? LocalDate.parse(petDetail.getEndDate()).plusDays(1)
                : LocalDate.parse(petDetail.getEndDate());

        var dates = start.datesUntil(end).toList();

        for (var date : dates) {
            var price = pricingRuleResults.stream()
                    .filter(e -> e.getPetId() == petDetail.getPetId()
                            && e.getServiceId() == petDetail.getServiceId()
                            && Objects.equals(e.getServiceDate(), date.toString()))
                    .findFirst()
                    .map(PetDetailCalculateResultDef::getAdjustedPrice)
                    .orElseGet(petDetail::getServicePrice);

            var builder = OrderLineItemModel.newBuilder();

            builder.setObjectId(petDetail.getServiceId());
            builder.setType(OrderItemType.ITEM_TYPE_SERVICE.getType());
            builder.setName(service.getName());
            builder.setDescription(service.getDescription());
            builder.setUnitPrice(price);
            builder.setQuantity(1);
            builder.setPetId(petDetail.getPetId());

            var taxId = service.getTaxId();
            if (taxId > 0) {
                var tax = Optional.ofNullable(taxIdToTax.get(taxId))
                        .orElseThrow(() -> bizException(Code.CODE_PARAMS_ERROR, "Tax not found: " + taxId));
                builder.addLineTaxes(buildOrderLineTax(bookingRequest, tax));
            }

            result.add(builder.build());
        }

        for (var addon : boarding.getAddonsList()) {

            long days =
                    switch (addon.getDateType()) {
                        case PET_DETAIL_DATE_EVERYDAY -> LocalDate.parse(petDetail.getStartDate())
                                .datesUntil(LocalDate.parse(petDetail.getEndDate()))
                                .count();
                        case PET_DETAIL_DATE_EVERYDAY_INCLUDE_CHECKOUT_DAY -> LocalDate.parse(petDetail.getStartDate())
                                        .datesUntil(LocalDate.parse(petDetail.getEndDate()))
                                        .count()
                                + 1;
                        case PET_DETAIL_DATE_SPECIFIC_DATE -> addon.getSpecificDatesCount();
                        case PET_DETAIL_DATE_DATE_POINT -> 1;
                        default -> throw bizException(
                                Code.CODE_PARAMS_ERROR, "Invalid date type: " + addon.getDateType());
                    };

            var addonModel = Optional.ofNullable(serviceIdToService.get(addon.getAddOnId()))
                    .orElseGet(ServiceModel::getDefaultInstance);

            for (int i = 0; i < days; i++) {
                var builder = OrderLineItemModel.newBuilder();

                builder.setObjectId(addon.getAddOnId());
                builder.setType(OrderItemType.ITEM_TYPE_SERVICE.getType());
                builder.setName(addonModel.getName());
                builder.setDescription(addonModel.getDescription());
                builder.setUnitPrice(addon.getServicePrice());
                builder.setQuantity(addon.getQuantityPerDay());
                builder.setPetId(petDetail.getPetId());

                var taxId = addonModel.getTaxId();
                if (taxId > 0) {
                    var tax = Optional.ofNullable(taxIdToTax.get(taxId))
                            .orElseThrow(() -> bizException(Code.CODE_PARAMS_ERROR, "Tax not found: " + taxId));
                    builder.addLineTaxes(buildOrderLineTax(bookingRequest, tax));
                }

                result.add(builder.build());
            }
        }

        return result;
    }

    private static boolean isCalculateByDay(ServiceModel service) {
        return service.getServiceItemType() == ServiceItemType.DAYCARE
                || (service.getServiceItemType() == ServiceItemType.BOARDING
                        && service.getPriceUnit() == ServicePriceUnit.PER_DAY);
    }

    private static List<OrderLineItemModel> buildLineItemForGrooming(
            BookingRequestModel bookingRequest,
            Map<Long, ServiceModel> serviceIdToService,
            Map<Long, TaxRuleModel> taxIdToTax,
            BookingRequestModel.GroomingService grooming) {

        var petDetail = grooming.getService();
        var service = Optional.ofNullable(serviceIdToService.get(petDetail.getServiceId()))
                .orElseThrow(
                        () -> bizException(Code.CODE_PARAMS_ERROR, "Service not found: " + petDetail.getServiceId()));

        var builder = OrderLineItemModel.newBuilder();
        builder.setBusinessId(bookingRequest.getBusinessId());
        builder.setObjectId(petDetail.getServiceId());
        builder.setType(OrderItemType.ITEM_TYPE_SERVICE.getType());
        builder.setName(service.getName());
        builder.setDescription(service.getDescription());
        builder.setUnitPrice(petDetail.getServicePrice());
        builder.setQuantity(1);
        builder.setPetId(petDetail.getPetId());

        var taxId = service.getTaxId();
        if (taxId > 0) {
            var tax = Optional.ofNullable(taxIdToTax.get(taxId))
                    .orElseThrow(() -> bizException(Code.CODE_PARAMS_ERROR, "Tax not found: " + taxId));
            builder.addLineTaxes(buildOrderLineTax(bookingRequest, tax));
        }

        return List.of(builder.build());
    }

    private static List<OrderLineItemModel> buildLineItemForDogWalking(
            BookingRequestModel bookingRequest,
            Map<Long, ServiceModel> serviceIdToService,
            Map<Long, TaxRuleModel> taxIdToTax,
            BookingRequestModel.DogWalkingService dogWalking) {

        var petDetail = dogWalking.getService();
        var service = Optional.ofNullable(serviceIdToService.get(petDetail.getServiceId()))
                .orElseThrow(
                        () -> bizException(Code.CODE_PARAMS_ERROR, "Service not found: " + petDetail.getServiceId()));

        var builder = OrderLineItemModel.newBuilder();
        builder.setBusinessId(bookingRequest.getBusinessId());
        builder.setObjectId(petDetail.getServiceId());
        builder.setType(OrderItemType.ITEM_TYPE_SERVICE.getType());
        builder.setName(service.getName());
        builder.setDescription(service.getDescription());
        builder.setUnitPrice(petDetail.getServicePrice());
        builder.setQuantity(1);
        builder.setPetId(petDetail.getPetId());

        var taxId = service.getTaxId();
        if (taxId > 0) {
            var tax = Optional.ofNullable(taxIdToTax.get(taxId))
                    .orElseThrow(() -> bizException(Code.CODE_PARAMS_ERROR, "Tax not found: " + taxId));
            builder.addLineTaxes(buildOrderLineTax(bookingRequest, tax));
        }

        return List.of(builder.build());
    }

    private static OrderLineTaxModel buildOrderLineTax(BookingRequestModel bookingRequest, TaxRuleModel tax) {
        return OrderLineTaxModel.newBuilder()
                .setBusinessId(bookingRequest.getBusinessId())
                .setApplyType(LineApplyType.TYPE_ITEM.getType())
                .setTaxId(tax.getId())
                .setTaxRate(tax.getRate())
                .build();
    }

    private static String buildTitle(
            BookingRequestModel bookingRequest,
            Map<Long, ServiceModel> serviceIdToService,
            Map<Long, EvaluationModel> evaluationIdToEvaluation) {
        return bookingRequest.getServicesList().stream()
                .map(service -> switch (service.getServiceCase()) {
                    case GROOMING -> {
                        var serviceId = service.getGrooming().getService().getServiceId();
                        yield Optional.ofNullable(serviceIdToService.get(serviceId))
                                .map(ServiceModel::getName)
                                .orElse(null);
                    }
                    case BOARDING -> {
                        var serviceId = service.getBoarding().getService().getServiceId();
                        yield Optional.ofNullable(serviceIdToService.get(serviceId))
                                .map(ServiceModel::getName)
                                .orElse(null);
                    }
                    case DAYCARE -> {
                        var serviceId = service.getDaycare().getService().getServiceId();
                        yield Optional.ofNullable(serviceIdToService.get(serviceId))
                                .map(ServiceModel::getName)
                                .orElse(null);
                    }
                    case EVALUATION -> {
                        var evaluationId = service.getEvaluation().getService().getEvaluationId();
                        yield Optional.ofNullable(evaluationIdToEvaluation.get(evaluationId))
                                .map(EvaluationModel::getName)
                                .orElse(null);
                    }
                    case DOG_WALKING -> {
                        var serviceId = service.getDogWalking().getService().getServiceId();
                        yield Optional.ofNullable(serviceIdToService.get(serviceId))
                                .map(ServiceModel::getName)
                                .orElse(null);
                    }
                    default -> null;
                })
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.joining(", "));
    }

    private List<PetDetailCalculateResultDef> calculatePricingRuleResults(
            BookingRequestModel bookingRequest,
            Map<Long, ServiceModel> serviceIdToService,
            List<ServiceWithCustomizedInfo> customizedServiceList) {

        var petDetailCalculateDefs = new ArrayList<PetDetailCalculateDef>();
        for (var bookingRequestItem : bookingRequest.getServicesList()) {
            var defs =
                    switch (bookingRequestItem.getServiceCase()) {
                        case BOARDING -> {
                            BoardingServiceDetailModel boarding =
                                    bookingRequestItem.getBoarding().getService();
                            CustomizedServiceView customizedService = findCustomizedService(
                                    customizedServiceList, boarding.getServiceId(), boarding.getPetId(), 0);
                            if (Objects.nonNull(customizedService)
                                    && Objects.equals(
                                            ServiceOverrideType.CLIENT, customizedService.getPriceOverrideType())) {
                                yield List.<PetDetailCalculateDef>of();
                            }
                            yield buildPetDetailCalculateDefForBoarding(
                                    bookingRequestItem.getBoarding(), serviceIdToService);
                        }
                        case DAYCARE -> {
                            DaycareServiceDetailModel daycare =
                                    bookingRequestItem.getDaycare().getService();
                            CustomizedServiceView customizedService = findCustomizedService(
                                    customizedServiceList, daycare.getServiceId(), daycare.getPetId(), 0);
                            if (Objects.nonNull(customizedService)
                                    && Objects.equals(
                                            ServiceOverrideType.CLIENT, customizedService.getPriceOverrideType())) {
                                yield List.<PetDetailCalculateDef>of();
                            }
                            yield buildPetDetailCalculateDefForDaycare(bookingRequestItem.getDaycare());
                        }
                        default -> List.<PetDetailCalculateDef>of();
                    };
            petDetailCalculateDefs.addAll(defs);
        }

        if (petDetailCalculateDefs.isEmpty()) {
            return List.of();
        }

        return pricingRuleApplyStub
                .applyPricingRule(ApplyPricingRuleRequest.newBuilder()
                        .setCompanyId(bookingRequest.getCompanyId())
                        .setBusinessId(bookingRequest.getBusinessId())
                        .setSourceId(bookingRequest.getId())
                        .setSourceType(PricingRuleApplySourceType.SOURCE_TYPE_BOOKING_REQUEST)
                        .addAllPetDetails(petDetailCalculateDefs)
                        .build())
                .getPetDetailsList();
    }

    private static List<PetDetailCalculateDef> buildPetDetailCalculateDefForDaycare(
            BookingRequestModel.DaycareService daycare) {

        var service = daycare.getService();

        var result = new ArrayList<PetDetailCalculateDef>();

        for (var date : service.getSpecificDatesList()) {
            var builder = PetDetailCalculateDef.newBuilder();
            builder.setPetId(service.getPetId());
            builder.setServiceId(service.getServiceId());
            builder.setServicePrice(service.getServicePrice());
            builder.setServiceDate(date);
            result.add(builder.build());
        }

        return result;
    }

    private static List<PetDetailCalculateDef> buildPetDetailCalculateDefForBoarding(
            BookingRequestModel.BoardingService boarding, Map<Long, ServiceModel> serviceIdToService) {

        var petDetail = boarding.getService();
        var service = Optional.ofNullable(serviceIdToService.get(petDetail.getServiceId()))
                .orElseThrow(
                        () -> bizException(Code.CODE_PARAMS_ERROR, "Service not found: " + petDetail.getServiceId()));

        Stream<LocalDate> dates = isCalculateByDay(service)
                ? LocalDate.parse(petDetail.getStartDate())
                        .datesUntil(LocalDate.parse(petDetail.getEndDate()).plusDays(1))
                : LocalDate.parse(petDetail.getStartDate()).datesUntil(LocalDate.parse(petDetail.getEndDate()));

        return dates.map(date -> {
                    var builder = PetDetailCalculateDef.newBuilder();
                    builder.setPetId(petDetail.getPetId());
                    builder.setServiceId(petDetail.getServiceId());
                    builder.setServicePrice(petDetail.getServicePrice());
                    builder.setServiceDate(date.toString());
                    return builder.build();
                })
                .toList();
    }

    @Override
    public void checkWaitlistAvailableTask(
            CheckWaitlistAvailableTaskRequest request,
            StreamObserver<CheckWaitlistAvailableTaskResponse> responseObserver) {
        ThreadPool.execute(this::startCheckWaitlistAvailableTask);
        responseObserver.onNext(CheckWaitlistAvailableTaskResponse.getDefaultInstance());
        responseObserver.onCompleted();
    }

    @Override
    public void moveBookingRequestToWaitlist(
            MoveBookingRequestToWaitlistRequest request,
            StreamObserver<MoveBookingRequestToWaitlistResponse> responseObserver) {

        var bookingRequest = mustGetBookingRequest(
                request.hasCompanyId() ? request.getCompanyId() : null,
                request.hasBusinessId() ? request.getBusinessId() : null,
                request.getBookingRequestId());

        if (bookingRequest.getStatus() == BookingRequestStatus.WAIT_LIST) {
            responseObserver.onNext(MoveBookingRequestToWaitlistResponse.getDefaultInstance());
            responseObserver.onCompleted();
            return;
        }

        // 1. 在 service detail level 添加 waitlist 信息
        var mainServiceItemType = getMainServiceItemType(bookingRequest.getServiceTypeInclude());

        switch (mainServiceItemType) {
            case BOARDING -> addWaitlistForBoarding(bookingRequest);
            case DAYCARE -> addWaitlistForDaycare(bookingRequest);
            default -> throw bizException(Code.CODE_PARAMS_ERROR, mainServiceItemType + " not support waitlist");
        }

        // 2. 更新 booking request status
        var updateBean = new BookingRequest();
        updateBean.setId(bookingRequest.getId());
        updateBean.setStatus(BookingRequestStatus.WAIT_LIST);
        updateBean.setAttr(buildAttr(bookingRequest));
        bookingRequestService.update(updateBean);

        responseObserver.onNext(MoveBookingRequestToWaitlistResponse.getDefaultInstance());
        responseObserver.onCompleted();
    }

    private static BookingRequestModel.Attr buildAttr(BookingRequest bookingRequest) {
        var builder = bookingRequest.getAttr().toBuilder();
        var staffId = AuthContext.get().staffId();
        if (isNormal(staffId)) {
            builder.setCreatedByStaffId(staffId);
        }
        return builder.build();
    }

    private void addWaitlistForBoarding(BookingRequest bookingRequest) {
        var boardingServices = boardingServiceDetailService.listByBookingRequestId(bookingRequest.getId());

        for (var boardingServiceDetail : boardingServices) {
            if (!StringUtils.hasText(boardingServiceDetail.getStartDate())
                    || !StringUtils.hasText(boardingServiceDetail.getEndDate())) {
                continue;
            }

            var boardingServiceWaitlist = new BoardingServiceWaitlist();
            boardingServiceWaitlist.setBookingRequestId(boardingServiceDetail.getBookingRequestId());
            boardingServiceWaitlist.setServiceDetailId(boardingServiceDetail.getId());
            boardingServiceWaitlist.setStartDate(LocalDate.parse(boardingServiceDetail.getStartDate()));
            boardingServiceWaitlist.setEndDate(LocalDate.parse(boardingServiceDetail.getEndDate()));

            waitlistService.insertBoardingServiceWaitlist(boardingServiceWaitlist);
        }
    }

    private void addWaitlistForDaycare(BookingRequest bookingRequest) {
        var daycareServices = daycareServiceDetailService.listByBookingRequestId(bookingRequest.getId());

        for (var daycareServiceDetail : daycareServices) {
            if (!StringUtils.hasText(daycareServiceDetail.getSpecificDates())) {
                continue;
            }

            var daycareServiceWaitlist = new DaycareServiceWaitlist();
            daycareServiceWaitlist.setBookingRequestId(daycareServiceDetail.getBookingRequestId());
            daycareServiceWaitlist.setServiceDetailId(daycareServiceDetail.getId());
            daycareServiceWaitlist.setSpecificDates(
                    JsonUtil.toList(daycareServiceDetail.getSpecificDates(), LocalDate.class));

            waitlistService.insertDaycareServiceWaitlist(daycareServiceWaitlist);
        }
    }

    private void startCheckWaitlistAvailableTask() {
        List<Pair<Long, Long>> waitlistToCheck = waitlistRedisHelper.getAllWaitlistToCheck();
        log.info("checkWaitlistAvailability count {}", waitlistToCheck.size());

        for (Pair<Long, Long> ids : waitlistToCheck) {
            long companyId = ids.getFirst();
            long businessId = ids.getSecond();
            try {
                // 检查 bd waitlist available
                var bdTrueList = bdWaitlistAvailableCheck(companyId, businessId);
                if (!isEmpty(bdTrueList)) {
                    waitlistService.sendNotification(businessId, bdTrueList.get(0), null);
                }
            } catch (Exception e) {
                log.error("Failed to handle waitlist logic for companyId={}, businessId={}", companyId, businessId, e);
            }
        }
    }

    private List<BookingRequestModel> bdWaitlistAvailableCheck(long companyId, long businessId) {
        var timeZoneName = organizationClient.getCompanyTimeZoneName(companyId);
        ZoneId zoneId = ZoneId.of(timeZoneName);
        LocalDate nowDate = LocalDate.now(zoneId);

        String ORDER_BY_COLUMN = "startDate";
        // boarding daycare 检查
        BookingRequestFilterDTO filter = new BookingRequestFilterDTO()
                .setCompanyId(companyId)
                .setBusinessIds(Collections.singletonList(businessId))
                .setStartDate(nowDate.toString())
                .setStatuses((Collections.singletonList(BookingRequestStatus.WAIT_LIST_VALUE)))
                .setIsWaitlistExpired(false)
                .setOrderBys(Collections.singletonList(OrderBy.newBuilder()
                        .setFieldName(ORDER_BY_COLUMN)
                        .setAsc(false)
                        .build()));

        List<BookingRequestModel> tureList = new ArrayList<>();

        var pagination = Pagination.builder().pageSize(100).build();
        for (int pageNum = 1; pageNum < 100; pageNum++) {
            pagination = pagination.toBuilder().pageNum(pageNum).build();

            Pair<List<BookingRequest>, Pagination> pair =
                    bookingRequestService.listByBusinessFilter(filter, pagination);
            if (isEmpty(pair.getFirst())) {
                break;
            }
            Map<Long, List<BookingRequestModel.Service>> servicesMap =
                    listServices(pair.getFirst(), Collections.singleton(BookingRequestAssociatedModel.SERVICE));

            List<BookingRequestModel> bookingRequestModelList = pair.getFirst().stream()
                    .map(bookingRequest -> BookingRequestConverter.INSTANCE.entityToModel(bookingRequest).toBuilder()
                            .addAllServices(servicesMap.getOrDefault(bookingRequest.getId(), List.of()))
                            .build())
                    .toList();

            var waitlistExtras = waitlistService.generateWaitlistExtras(bookingRequestModelList);
            Map<Long, WaitlistExtra> extraMapById = waitlistExtras.stream()
                    .collect(Collectors.toMap(WaitlistExtra::getId, Function.identity(), (o, n) -> o));

            for (var bookingRequestModel : bookingRequestModelList) {
                var waitlistExtra = extraMapById.get(bookingRequestModel.getId());
                var isSendNotification = waitlistRedisHelper.sendCheckCompare(
                        waitlistExtra.getIsAvailable(), bookingRequestModel.getId(), null);
                if (isSendNotification) {
                    tureList.add(bookingRequestModel);
                }
            }
        }

        return tureList;
    }

    @Override
    public void previewBookingRequestPricing(
            PreviewBookingRequestPricingRequest request,
            StreamObserver<PreviewBookingRequestPricingResponse> responseObserver) {
        var customizedServices = listCustomizedServices(request);

        var serviceAmountsWithPricingRule = calculateServiceAmountsWithPricingRule(request, customizedServices);

        var addOnAmounts = PricingRuleUtils.convertAddOns(request.getPetServicesList(), customizedServices);

        var lineItems = Stream.concat(
                        PricingRuleUtils.processServiceLineItems(serviceAmountsWithPricingRule).stream(),
                        PricingRuleUtils.processSamePetServices(addOnAmounts).stream())
                .toList();

        responseObserver.onNext(PreviewBookingRequestPricingResponse.newBuilder()
                .addAllLineItems(lineItems)
                .build());
        responseObserver.onCompleted();
    }

    private List<FulfillmentLineItem> calculateServiceAmountsWithPricingRule(
            PreviewBookingRequestPricingRequest request, List<ServiceWithCustomizedInfo> customizedServices) {

        var serviceAmounts = PricingRuleUtils.convertServices(request.getPetServicesList(), customizedServices);

        var petDetails = listPetDetailWithPricingRuleApplied(request.getCompanyId(), serviceAmounts);
        if (petDetails.isEmpty()) {
            return serviceAmounts;
        }

        return getUsingPricingRuleService(serviceAmounts, petDetails);
    }

    private List<PetDetailCalculateResultDef> listPetDetailWithPricingRuleApplied(
            long companyId, List<FulfillmentLineItem> items) {

        var pricingRuleEligibleServices = items.stream()
                .filter(PricingRuleUtils::isPricingRuleEligible)
                .map(item -> PetDetailCalculateDef.newBuilder()
                        .setPetId(item.petId())
                        .setServiceId(item.service().getId())
                        .setServicePrice(item.price().doubleValue())
                        .setServiceDate(item.date())
                        .build())
                .toList();

        if (pricingRuleEligibleServices.isEmpty()) {
            return List.of();
        }

        return pricingRuleStub
                .calculatePricingRule(CalculatePricingRuleRequest.newBuilder()
                        .setCompanyId(companyId)
                        .addAllPetDetails(pricingRuleEligibleServices)
                        .build())
                .getPetDetailsList();
    }

    private List<ServiceWithCustomizedInfo> listCustomizedServices(PreviewBookingRequestPricingRequest request) {
        var conditions = request.getPetServicesList().stream()
                .flatMap(petServiceDetail -> petServiceDetail.getServiceDetailsList().stream()
                        .flatMap(service -> PricingRuleUtils.getQueryConditionStream(
                                request.getBusinessId(), service, petServiceDetail)))
                .toList();

        return offeringHelper.listCustomizedService(request.getCompanyId(), conditions);
    }

    @Override
    public void countBookingRequestByFilter(
            CountBookingRequestByFilterRequest request,
            StreamObserver<CountBookingRequestByFilterResponse> responseObserver) {
        // 获取 evaluation 是否在使用中
        var evaluationInUseMap = evaluationTestDetailService.countBookingRequestByFilter(
                request.getCompanyId(), request.getEvaluationIdsList());

        CountBookingRequestByFilterResponse.Builder responseBuilder =
                CountBookingRequestByFilterResponse.newBuilder().putAllEvaluationInUse(evaluationInUseMap);

        responseObserver.onNext(responseBuilder.build());
        responseObserver.onCompleted();
    }
}
