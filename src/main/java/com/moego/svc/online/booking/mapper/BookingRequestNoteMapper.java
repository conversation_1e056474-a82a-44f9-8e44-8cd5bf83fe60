package com.moego.svc.online.booking.mapper;

import static com.moego.svc.online.booking.mapper.BookingRequestNoteDynamicSqlSupport.*;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;

import com.moego.lib.common.autoconfigure.datasource.DynamicDataSource;
import com.moego.svc.online.booking.entity.BookingRequestNote;
import jakarta.annotation.Generated;
import java.util.Collection;
import java.util.List;
import java.util.Optional;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.delete.DeleteDSLCompleter;
import org.mybatis.dynamic.sql.insert.render.InsertStatementProvider;
import org.mybatis.dynamic.sql.select.CountDSLCompleter;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSL;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.update.UpdateModel;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.CommonCountMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonDeleteMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonSelectMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonUpdateMapper;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

@Mapper
public interface BookingRequestNoteMapper extends CommonSelectMapper, CommonCountMapper, CommonDeleteMapper, CommonUpdateMapper, DynamicDataSource<BookingRequestNoteMapper> {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_request_note")
    BasicColumn[] selectList = BasicColumn.columnList(id, companyId, customerId, bookingRequestId, note, createdAt, updatedAt, deletedAt);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_request_note")
    @InsertProvider(type=SqlProviderAdapter.class, method="insert")
    @Options(useGeneratedKeys=true,keyProperty="row.id")
    int insert(InsertStatementProvider<BookingRequestNote> insertStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_request_note")
    @InsertProvider(type=SqlProviderAdapter.class, method="insertMultipleWithGeneratedKeys")
    @Options(useGeneratedKeys=true,keyProperty="records.id")
    int insertMultiple(@Param("insertStatement") String insertStatement, @Param("records") List<BookingRequestNote> records);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_request_note")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @Results(id="BookingRequestNoteResult", value = {
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="company_id", property="companyId", jdbcType=JdbcType.BIGINT),
        @Result(column="customer_id", property="customerId", jdbcType=JdbcType.BIGINT),
        @Result(column="booking_request_id", property="bookingRequestId", jdbcType=JdbcType.BIGINT),
        @Result(column="note", property="note", jdbcType=JdbcType.VARCHAR),
        @Result(column="created_at", property="createdAt", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="updated_at", property="updatedAt", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="deleted_at", property="deletedAt", jdbcType=JdbcType.TIMESTAMP)
    })
    List<BookingRequestNote> selectMany(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_request_note")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @ResultMap("BookingRequestNoteResult")
    Optional<BookingRequestNote> selectOne(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_request_note")
    default long count(CountDSLCompleter completer) {
        return MyBatis3Utils.countFrom(this::count, bookingRequestNote, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_request_note")
    default int delete(DeleteDSLCompleter completer) {
        return MyBatis3Utils.deleteFrom(this::delete, bookingRequestNote, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_request_note")
    default int deleteByPrimaryKey(Long id_) {
        return delete(c -> 
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_request_note")
    default int insertMultiple(Collection<BookingRequestNote> records) {
        return MyBatis3Utils.insertMultipleWithGeneratedKeys(this::insertMultiple, records, bookingRequestNote, c ->
            c.map(companyId).toProperty("companyId")
            .map(customerId).toProperty("customerId")
            .map(bookingRequestId).toProperty("bookingRequestId")
            .map(note).toProperty("note")
            .map(createdAt).toProperty("createdAt")
            .map(updatedAt).toProperty("updatedAt")
            .map(deletedAt).toProperty("deletedAt")
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_request_note")
    default int insertSelective(BookingRequestNote row) {
        return MyBatis3Utils.insert(this::insert, row, bookingRequestNote, c ->
            c.map(companyId).toPropertyWhenPresent("companyId", row::getCompanyId)
            .map(customerId).toPropertyWhenPresent("customerId", row::getCustomerId)
            .map(bookingRequestId).toPropertyWhenPresent("bookingRequestId", row::getBookingRequestId)
            .map(note).toPropertyWhenPresent("note", row::getNote)
            .map(createdAt).toPropertyWhenPresent("createdAt", row::getCreatedAt)
            .map(updatedAt).toPropertyWhenPresent("updatedAt", row::getUpdatedAt)
            .map(deletedAt).toPropertyWhenPresent("deletedAt", row::getDeletedAt)
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_request_note")
    default Optional<BookingRequestNote> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, bookingRequestNote, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_request_note")
    default List<BookingRequestNote> select(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectList(this::selectMany, selectList, bookingRequestNote, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_request_note")
    default List<BookingRequestNote> selectDistinct(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectDistinct(this::selectMany, selectList, bookingRequestNote, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_request_note")
    default Optional<BookingRequestNote> selectByPrimaryKey(Long id_) {
        return selectOne(c ->
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_request_note")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, bookingRequestNote, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_request_note")
    static UpdateDSL<UpdateModel> updateAllColumns(BookingRequestNote row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(companyId).equalTo(row::getCompanyId)
                .set(customerId).equalTo(row::getCustomerId)
                .set(bookingRequestId).equalTo(row::getBookingRequestId)
                .set(note).equalTo(row::getNote)
                .set(createdAt).equalTo(row::getCreatedAt)
                .set(updatedAt).equalTo(row::getUpdatedAt)
                .set(deletedAt).equalTo(row::getDeletedAt);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_request_note")
    static UpdateDSL<UpdateModel> updateSelectiveColumns(BookingRequestNote row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(companyId).equalToWhenPresent(row::getCompanyId)
                .set(customerId).equalToWhenPresent(row::getCustomerId)
                .set(bookingRequestId).equalToWhenPresent(row::getBookingRequestId)
                .set(note).equalToWhenPresent(row::getNote)
                .set(createdAt).equalToWhenPresent(row::getCreatedAt)
                .set(updatedAt).equalToWhenPresent(row::getUpdatedAt)
                .set(deletedAt).equalToWhenPresent(row::getDeletedAt);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_request_note")
    default int updateByPrimaryKeySelective(BookingRequestNote row) {
        return update(c ->
            c.set(companyId).equalToWhenPresent(row::getCompanyId)
            .set(customerId).equalToWhenPresent(row::getCustomerId)
            .set(bookingRequestId).equalToWhenPresent(row::getBookingRequestId)
            .set(note).equalToWhenPresent(row::getNote)
            .set(createdAt).equalToWhenPresent(row::getCreatedAt)
            .set(updatedAt).equalToWhenPresent(row::getUpdatedAt)
            .set(deletedAt).equalToWhenPresent(row::getDeletedAt)
            .where(id, isEqualTo(row::getId))
        );
    }
}