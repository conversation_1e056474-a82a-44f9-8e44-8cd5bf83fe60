package com.moego.svc.online.booking.mapper;

import com.moego.idl.models.appointment.v1.PetDetailDateType;
import jakarta.annotation.Generated;
import java.math.BigDecimal;
import java.sql.JDBCType;
import java.time.LocalDate;
import java.util.Date;
import java.util.List;
import org.mybatis.dynamic.sql.AliasableSqlTable;
import org.mybatis.dynamic.sql.SqlColumn;

public final class BoardingAddOnDetailDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: boarding_add_on_detail")
    public static final BoardingAddOnDetail boardingAddOnDetail = new BoardingAddOnDetail();

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: boarding_add_on_detail.id")
    public static final SqlColumn<Long> id = boardingAddOnDetail.id;

    /**
     * Database Column Remarks:
     *   The id of booking request
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: boarding_add_on_detail.booking_request_id")
    public static final SqlColumn<Long> bookingRequestId = boardingAddOnDetail.bookingRequestId;

    /**
     * Database Column Remarks:
     *   The id of boarding service detail, associated with the current add-on
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: boarding_add_on_detail.service_detail_id")
    public static final SqlColumn<Long> serviceDetailId = boardingAddOnDetail.serviceDetailId;

    /**
     * Database Column Remarks:
     *   The id of pet, associated with the current add-on
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: boarding_add_on_detail.pet_id")
    public static final SqlColumn<Long> petId = boardingAddOnDetail.petId;

    /**
     * Database Column Remarks:
     *   The id of current add-on service
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: boarding_add_on_detail.add_on_id")
    public static final SqlColumn<Long> addOnId = boardingAddOnDetail.addOnId;

    /**
     * Database Column Remarks:
     *   The specific dates of the add-on service
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: boarding_add_on_detail.specific_dates")
    public static final SqlColumn<List<LocalDate>> specificDates = boardingAddOnDetail.specificDates;

    /**
     * Database Column Remarks:
     *   Whether the add-on service is everyday
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: boarding_add_on_detail.is_everyday")
    public static final SqlColumn<Boolean> isEveryday = boardingAddOnDetail.isEveryday;

    /**
     * Database Column Remarks:
     *   The price of current add-on service
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: boarding_add_on_detail.service_price")
    public static final SqlColumn<BigDecimal> servicePrice = boardingAddOnDetail.servicePrice;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: boarding_add_on_detail.tax_id")
    public static final SqlColumn<Long> taxId = boardingAddOnDetail.taxId;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: boarding_add_on_detail.duration")
    public static final SqlColumn<Integer> duration = boardingAddOnDetail.duration;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: boarding_add_on_detail.created_at")
    public static final SqlColumn<Date> createdAt = boardingAddOnDetail.createdAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: boarding_add_on_detail.updated_at")
    public static final SqlColumn<Date> updatedAt = boardingAddOnDetail.updatedAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: boarding_add_on_detail.deleted_at")
    public static final SqlColumn<Date> deletedAt = boardingAddOnDetail.deletedAt;

    /**
     * Database Column Remarks:
     *   Number of times per day this add-on is performed
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: boarding_add_on_detail.quantity_per_day")
    public static final SqlColumn<Integer> quantityPerDay = boardingAddOnDetail.quantityPerDay;

    /**
     * Database Column Remarks:
     *   date type, 1-every day except checkout day, 2-specific date, 3-date point, 4-everyday
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: boarding_add_on_detail.date_type")
    public static final SqlColumn<PetDetailDateType> dateType = boardingAddOnDetail.dateType;

    /**
     * Database Column Remarks:
     *   当 date_type 为 PET_DETAIL_DATE_SPECIFIC_DATE 时，使用该字段表示具体日期
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: boarding_add_on_detail.start_date")
    public static final SqlColumn<LocalDate> startDate = boardingAddOnDetail.startDate;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: boarding_add_on_detail")
    public static final class BoardingAddOnDetail extends AliasableSqlTable<BoardingAddOnDetail> {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<Long> bookingRequestId = column("booking_request_id", JDBCType.BIGINT);

        public final SqlColumn<Long> serviceDetailId = column("service_detail_id", JDBCType.BIGINT);

        public final SqlColumn<Long> petId = column("pet_id", JDBCType.BIGINT);

        public final SqlColumn<Long> addOnId = column("add_on_id", JDBCType.BIGINT);

        public final SqlColumn<List<LocalDate>> specificDates = column("specific_dates", JDBCType.OTHER, "com.moego.svc.online.booking.typehandler.BoardingAddOnDetailSpecificDatesTypeHandler");

        public final SqlColumn<Boolean> isEveryday = column("is_everyday", JDBCType.BIT);

        public final SqlColumn<BigDecimal> servicePrice = column("service_price", JDBCType.NUMERIC);

        public final SqlColumn<Long> taxId = column("tax_id", JDBCType.BIGINT);

        public final SqlColumn<Integer> duration = column("duration", JDBCType.INTEGER);

        public final SqlColumn<Date> createdAt = column("created_at", JDBCType.TIMESTAMP);

        public final SqlColumn<Date> updatedAt = column("updated_at", JDBCType.TIMESTAMP);

        public final SqlColumn<Date> deletedAt = column("deleted_at", JDBCType.TIMESTAMP);

        public final SqlColumn<Integer> quantityPerDay = column("quantity_per_day", JDBCType.INTEGER);

        public final SqlColumn<PetDetailDateType> dateType = column("date_type", JDBCType.INTEGER, "com.moego.svc.online.booking.typehandler.BoardingAddOnDetailDateTypeTypeHandler");

        public final SqlColumn<LocalDate> startDate = column("start_date", JDBCType.DATE);

        public BoardingAddOnDetail() {
            super("boarding_add_on_detail", BoardingAddOnDetail::new);
        }
    }
}