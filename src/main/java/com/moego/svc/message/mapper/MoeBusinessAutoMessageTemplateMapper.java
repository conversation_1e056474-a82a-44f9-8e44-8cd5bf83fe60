package com.moego.svc.message.mapper;

import com.moego.svc.message.mapperbean.MoeBusinessAutoMessageTemplate;
import java.util.List;

public interface MoeBusinessAutoMessageTemplateMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business_auto_message_template
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business_auto_message_template
     *
     * @mbg.generated
     */
    int insert(MoeBusinessAutoMessageTemplate record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business_auto_message_template
     *
     * @mbg.generated
     */
    int insertSelective(MoeBusinessAutoMessageTemplate record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business_auto_message_template
     *
     * @mbg.generated
     */
    MoeBusinessAutoMessageTemplate selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business_auto_message_template
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(MoeBusinessAutoMessageTemplate record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business_auto_message_template
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(MoeBusinessAutoMessageTemplate record);

    List<MoeBusinessAutoMessageTemplate> selectForTransfer(List<Integer> businessIds);
}
