package com.moego.svc.message.mapper;

import com.moego.svc.message.mapperbean.MoeScheduleMessage;
import com.moego.svc.message.mapperbean.MoeScheduleMessageExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface MoeScheduleMessageMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_schedule_message
     *
     * @mbg.generated
     */
    long countByExample(MoeScheduleMessageExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_schedule_message
     *
     * @mbg.generated
     */
    int deleteByExample(MoeScheduleMessageExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_schedule_message
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_schedule_message
     *
     * @mbg.generated
     */
    int insert(MoeScheduleMessage record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_schedule_message
     *
     * @mbg.generated
     */
    int insertSelective(MoeScheduleMessage record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_schedule_message
     *
     * @mbg.generated
     */
    List<MoeScheduleMessage> selectByExampleWithBLOBs(MoeScheduleMessageExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_schedule_message
     *
     * @mbg.generated
     */
    List<MoeScheduleMessage> selectByExample(MoeScheduleMessageExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_schedule_message
     *
     * @mbg.generated
     */
    MoeScheduleMessage selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_schedule_message
     *
     * @mbg.generated
     */
    int updateByExampleSelective(
            @Param("record") MoeScheduleMessage record, @Param("example") MoeScheduleMessageExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_schedule_message
     *
     * @mbg.generated
     */
    int updateByExampleWithBLOBs(
            @Param("record") MoeScheduleMessage record, @Param("example") MoeScheduleMessageExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_schedule_message
     *
     * @mbg.generated
     */
    int updateByExample(
            @Param("record") MoeScheduleMessage record, @Param("example") MoeScheduleMessageExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_schedule_message
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(MoeScheduleMessage record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_schedule_message
     *
     * @mbg.generated
     */
    int updateByPrimaryKeyWithBLOBs(MoeScheduleMessage record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_schedule_message
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(MoeScheduleMessage record);

    List<MoeScheduleMessage> listScheduledMessage(@Param("start") String start, @Param("end") String end);
}
