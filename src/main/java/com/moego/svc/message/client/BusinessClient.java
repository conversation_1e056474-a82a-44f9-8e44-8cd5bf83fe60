package com.moego.svc.message.client;

import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.organization.v1.LocationBriefView;
import com.moego.idl.service.organization.v1.BusinessServiceGrpc;
import com.moego.idl.service.organization.v1.GetLocationDetailRequest;
import com.moego.idl.service.organization.v1.GetLocationListRequest;
import com.moego.lib.common.exception.ExceptionUtil;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
@Slf4j
public class BusinessClient {

    private final BusinessServiceGrpc.BusinessServiceBlockingStub businessServiceBlockingStub;

    public List<LocationBriefView> getLocationList(Long companyId) {
        try {
            return businessServiceBlockingStub
                    .getLocationList(GetLocationListRequest.newBuilder()
                            .setTokenCompanyId(companyId)
                            .build())
                    .getLocationList();
        } catch (Exception e) {
            log.error("get location list with company_id [{}] error", companyId, e);
            throw ExceptionUtil.bizException(Code.CODE_SERVER_ERROR, "get location list error");
        }
    }

    public String getLocationName(Long companyId, Long locationId) {
        try {
            return businessServiceBlockingStub
                    .getLocationDetail(GetLocationDetailRequest.newBuilder()
                            .setId(locationId)
                            .setTokenCompanyId(companyId)
                            .build())
                    .getLocation()
                    .getName();
        } catch (Exception e) {
            log.error("get location detail with company_id [{}] and location_id [{}] error", companyId, locationId, e);
            throw ExceptionUtil.bizException(Code.CODE_SERVER_ERROR, "get location detail error");
        }
    }
}
