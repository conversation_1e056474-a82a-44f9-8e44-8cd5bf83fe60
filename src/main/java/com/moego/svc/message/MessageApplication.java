package com.moego.svc.message;

import java.time.ZoneOffset;
import java.util.TimeZone;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;

@EnableFeignClients({
    "com.moego.server.business.client",
    "com.moego.server.customer.client",
    "com.moego.server.grooming.client",
    "com.moego.server.message.client",
})
@SpringBootApplication(proxyBeanMethods = false)
@MapperScan("com.moego.svc.message.mapper")
public class MessageApplication {

    public static void main(String[] args) {
        TimeZone.setDefault(TimeZone.getTimeZone(ZoneOffset.UTC));

        SpringApplication.run(MessageApplication.class, args);
    }
}
