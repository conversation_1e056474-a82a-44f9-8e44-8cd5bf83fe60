package com.moego.api.v3.shared.helper;

import com.moego.idl.models.business_customer.v1.PetEvaluationModel;
import com.moego.idl.models.customer.v1.EvaluationStatus;
import com.moego.idl.models.offering.v1.EvaluationBriefView;
import com.moego.idl.models.offering.v1.ServiceBriefView;
import com.moego.idl.models.organization.v1.Tenant;
import com.moego.idl.service.business_customer.v1.BusinessPetEvaluationServiceGrpc;
import com.moego.idl.service.business_customer.v1.ListPetEvaluationRequest;
import com.moego.idl.service.offering.v1.EvaluationServiceGrpc;
import com.moego.idl.service.offering.v1.GetEvaluationListWithEvaluationIdsRequest;
import com.moego.idl.service.offering.v1.GetServiceListByIdsRequest;
import com.moego.idl.service.offering.v1.ServiceManagementServiceGrpc;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

/**
 * <AUTHOR>
 * @since 2025/5/12
 */
@Component
@RequiredArgsConstructor
public class EvaluationHelper {

    private final EvaluationServiceGrpc.EvaluationServiceBlockingStub evaluationStub;
    private final BusinessPetEvaluationServiceGrpc.BusinessPetEvaluationServiceBlockingStub petEvaluationStub;
    private final ServiceManagementServiceGrpc.ServiceManagementServiceBlockingStub serviceStub;

    /**
     * 获取 pets missing 的 evaluations
     *
     * @param companyId  company id
     * @param petIds     pet ids
     * @param serviceIds service ids
     * @return petId -> serviceId -> evaluation
     */
    public Map<Long, Map<Long, EvaluationBriefView>> listMissingEvaluations(
            long companyId, Collection<Long> petIds, Collection<Long> serviceIds) {
        if (ObjectUtils.isEmpty(petIds) || ObjectUtils.isEmpty(serviceIds)) {
            return Map.of();
        }

        // 1. 获取所有 service 信息，找出需要 evaluation 的 service 和对应的 evaluation id
        var serviceIdToEvaluationId = getServiceEvaluationIds(serviceIds);
        if (serviceIdToEvaluationId.isEmpty()) {
            return Map.of();
        }

        // 2. 获取所有 evaluation 信息
        var evaluationIdToEvaluation = getEvaluationsByIds(serviceIdToEvaluationId.values());
        if (evaluationIdToEvaluation.isEmpty()) {
            return Map.of();
        }

        // 3. 获取 pet 已有的 evaluation 记录
        var petIdToPassedEvaluations = getPetEvaluations(companyId, petIds);

        // 4. 计算每个 pet 对应每个 service 缺失的 evaluations
        var result = new HashMap<Long, Map<Long, EvaluationBriefView>>();
        for (var petId : petIds) {
            // 获取 pet 已有的 evaluation ids
            var passedEvaluationIds = petIdToPassedEvaluations.getOrDefault(petId, List.of()).stream()
                    .map(PetEvaluationModel::getEvaluationId)
                    .collect(Collectors.toSet());

            // 计算每个 service 缺失的 evaluation
            var serviceIdToMissingEvaluation = new HashMap<Long, EvaluationBriefView>();
            for (var entry : serviceIdToEvaluationId.entrySet()) {
                var serviceId = entry.getKey();
                var evaluationId = entry.getValue();

                // 如果 pet 没有这个 evaluation，则添加到缺失列表
                if (!passedEvaluationIds.contains(evaluationId) && evaluationIdToEvaluation.containsKey(evaluationId)) {
                    serviceIdToMissingEvaluation.put(serviceId, evaluationIdToEvaluation.get(evaluationId));
                }
            }

            if (!serviceIdToMissingEvaluation.isEmpty()) {
                result.put(petId, serviceIdToMissingEvaluation);
            }
        }

        return result;
    }

    /**
     * 获取 service 需要的 evaluation ids
     *
     * @param serviceIds service ids
     * @return serviceId -> evaluationId
     */
    private Map<Long, Long> getServiceEvaluationIds(Collection<Long> serviceIds) {
        if (CollectionUtils.isEmpty(serviceIds)) {
            return Map.of();
        }

        // 获取 service 信息
        var services = serviceStub
                .getServiceListByIds(GetServiceListByIdsRequest.newBuilder()
                        .addAllServiceIds(Set.copyOf(serviceIds))
                        .build())
                .getServicesList();

        // 过滤出需要 evaluation 的 service
        return services.stream()
                .filter(ServiceBriefView::getIsEvaluationRequired)
                .filter(service -> service.getEvaluationId() > 0)
                .collect(Collectors.toMap(ServiceBriefView::getId, ServiceBriefView::getEvaluationId, (a, b) -> a));
    }

    private Map<Long, EvaluationBriefView> getEvaluationsByIds(Collection<Long> evaluationIds) {
        if (CollectionUtils.isEmpty(evaluationIds)) {
            return Map.of();
        }

        return evaluationStub
                .getEvaluationListWithEvaluationIds(GetEvaluationListWithEvaluationIdsRequest.newBuilder()
                        .addAllEvaluationIds(Set.copyOf(evaluationIds))
                        .build())
                .getEvaluationsList()
                .stream()
                .collect(Collectors.toMap(EvaluationBriefView::getId, Function.identity(), (a, b) -> a));
    }

    private Map<Long, List<PetEvaluationModel>> getPetEvaluations(long companyId, Collection<Long> petIds) {
        if (ObjectUtils.isEmpty(petIds)) {
            return Map.of();
        }

        var response = petEvaluationStub.listPetEvaluation(ListPetEvaluationRequest.newBuilder()
                .setTenant(Tenant.newBuilder().setCompanyId(companyId).build())
                .addAllPetIds(Set.copyOf(petIds))
                .build());

        return response.getPetEvaluationsList().stream()
                .filter(e -> e.getEvaluationStatus() == EvaluationStatus.PASS)
                .collect(Collectors.groupingBy(PetEvaluationModel::getPetId));
    }

    /**
     * Get evaluations by ids.
     *
     * @param evaluationIds evaluation ids
     * @return evaluation id -> evaluation
     */
    public Map<Long, EvaluationBriefView> listEvaluation(Collection<Long> evaluationIds) {
        var resp =
                evaluationStub.getEvaluationListWithEvaluationIds(GetEvaluationListWithEvaluationIdsRequest.newBuilder()
                        .addAllEvaluationIds(Set.copyOf(evaluationIds))
                        .build());
        return resp.getEvaluationsList().stream()
                .collect(Collectors.toMap(EvaluationBriefView::getId, Function.identity(), (a, b) -> a));
    }
}
