/*
 * This file is generated by jOOQ.
 */
package com.moego.svc.business.customer.repository.jooq.tables;


import com.moego.svc.business.customer.repository.jooq.Indexes;
import com.moego.svc.business.customer.repository.jooq.Keys;
import com.moego.svc.business.customer.repository.jooq.MoeCustomer;
import com.moego.svc.business.customer.repository.jooq.tables.records.MoePetPetColorBindingRecord;

import java.util.Arrays;
import java.util.List;
import java.util.function.Function;

import org.jooq.Converter;
import org.jooq.Field;
import org.jooq.ForeignKey;
import org.jooq.Function5;
import org.jooq.Identity;
import org.jooq.Index;
import org.jooq.Name;
import org.jooq.Record;
import org.jooq.Records;
import org.jooq.Row5;
import org.jooq.Schema;
import org.jooq.SelectField;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class MoePetPetColorBinding extends TableImpl<MoePetPetColorBindingRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of
     * <code>moe_customer.moe_pet_pet_color_binding</code>
     */
    public static final MoePetPetColorBinding MOE_PET_PET_COLOR_BINDING = new MoePetPetColorBinding();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<MoePetPetColorBindingRecord> getRecordType() {
        return MoePetPetColorBindingRecord.class;
    }

    /**
     * The column <code>moe_customer.moe_pet_pet_color_binding.id</code>.
     */
    public final TableField<MoePetPetColorBindingRecord, Integer> ID = createField(DSL.name("id"), SQLDataType.INTEGER.nullable(false).identity(true), this, "");

    /**
     * The column <code>moe_customer.moe_pet_pet_color_binding.pet_id</code>.
     */
    public final TableField<MoePetPetColorBindingRecord, Integer> PET_ID = createField(DSL.name("pet_id"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "");

    /**
     * The column
     * <code>moe_customer.moe_pet_pet_color_binding.pet_color_id</code>.
     */
    public final TableField<MoePetPetColorBindingRecord, Integer> PET_COLOR_ID = createField(DSL.name("pet_color_id"), SQLDataType.INTEGER.nullable(false).defaultValue(DSL.inline("0", SQLDataType.INTEGER)), this, "");

    /**
     * The column <code>moe_customer.moe_pet_pet_color_binding.status</code>.
     * 1:有效 2:无效
     */
    public final TableField<MoePetPetColorBindingRecord, Integer> STATUS = createField(DSL.name("status"), SQLDataType.TINYINT.nullable(false).defaultValue(DSL.inline("1", SQLDataType.TINYINT)), this, "1:有效 2:无效", Converter.ofNullable(Byte.class, Integer.class, Integer::valueOf, Integer::byteValue));

    /**
     * The column
     * <code>moe_customer.moe_pet_pet_color_binding.create_time</code>.
     */
    public final TableField<MoePetPetColorBindingRecord, Long> CREATE_TIME = createField(DSL.name("create_time"), SQLDataType.BIGINT.nullable(false).defaultValue(DSL.inline("0", SQLDataType.BIGINT)), this, "");

    private MoePetPetColorBinding(Name alias, Table<MoePetPetColorBindingRecord> aliased) {
        this(alias, aliased, null);
    }

    private MoePetPetColorBinding(Name alias, Table<MoePetPetColorBindingRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, DSL.comment(""), TableOptions.table());
    }

    /**
     * Create an aliased <code>moe_customer.moe_pet_pet_color_binding</code>
     * table reference
     */
    public MoePetPetColorBinding(String alias) {
        this(DSL.name(alias), MOE_PET_PET_COLOR_BINDING);
    }

    /**
     * Create an aliased <code>moe_customer.moe_pet_pet_color_binding</code>
     * table reference
     */
    public MoePetPetColorBinding(Name alias) {
        this(alias, MOE_PET_PET_COLOR_BINDING);
    }

    /**
     * Create a <code>moe_customer.moe_pet_pet_color_binding</code> table
     * reference
     */
    public MoePetPetColorBinding() {
        this(DSL.name("moe_pet_pet_color_binding"), null);
    }

    public <O extends Record> MoePetPetColorBinding(Table<O> child, ForeignKey<O, MoePetPetColorBindingRecord> key) {
        super(child, key, MOE_PET_PET_COLOR_BINDING);
    }

    @Override
    public Schema getSchema() {
        return aliased() ? null : MoeCustomer.MOE_CUSTOMER;
    }

    @Override
    public List<Index> getIndexes() {
        return Arrays.asList(Indexes.MOE_PET_PET_COLOR_BINDING_IDX_PET_CODE_ID, Indexes.MOE_PET_PET_COLOR_BINDING_INDEX_PETID);
    }

    @Override
    public Identity<MoePetPetColorBindingRecord, Integer> getIdentity() {
        return (Identity<MoePetPetColorBindingRecord, Integer>) super.getIdentity();
    }

    @Override
    public UniqueKey<MoePetPetColorBindingRecord> getPrimaryKey() {
        return Keys.KEY_MOE_PET_PET_COLOR_BINDING_PRIMARY;
    }

    @Override
    public MoePetPetColorBinding as(String alias) {
        return new MoePetPetColorBinding(DSL.name(alias), this);
    }

    @Override
    public MoePetPetColorBinding as(Name alias) {
        return new MoePetPetColorBinding(alias, this);
    }

    @Override
    public MoePetPetColorBinding as(Table<?> alias) {
        return new MoePetPetColorBinding(alias.getQualifiedName(), this);
    }

    /**
     * Rename this table
     */
    @Override
    public MoePetPetColorBinding rename(String name) {
        return new MoePetPetColorBinding(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public MoePetPetColorBinding rename(Name name) {
        return new MoePetPetColorBinding(name, null);
    }

    /**
     * Rename this table
     */
    @Override
    public MoePetPetColorBinding rename(Table<?> name) {
        return new MoePetPetColorBinding(name.getQualifiedName(), null);
    }

    // -------------------------------------------------------------------------
    // Row5 type methods
    // -------------------------------------------------------------------------

    @Override
    public Row5<Integer, Integer, Integer, Integer, Long> fieldsRow() {
        return (Row5) super.fieldsRow();
    }

    /**
     * Convenience mapping calling {@link SelectField#convertFrom(Function)}.
     */
    public <U> SelectField<U> mapping(Function5<? super Integer, ? super Integer, ? super Integer, ? super Integer, ? super Long, ? extends U> from) {
        return convertFrom(Records.mapping(from));
    }

    /**
     * Convenience mapping calling {@link SelectField#convertFrom(Class,
     * Function)}.
     */
    public <U> SelectField<U> mapping(Class<U> toType, Function5<? super Integer, ? super Integer, ? super Integer, ? super Integer, ? super Long, ? extends U> from) {
        return convertFrom(toType, Records.mapping(from));
    }
}
