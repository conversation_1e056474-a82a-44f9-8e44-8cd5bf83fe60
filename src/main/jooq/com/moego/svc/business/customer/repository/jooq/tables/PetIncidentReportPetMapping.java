/*
 * This file is generated by jOOQ.
 */
package com.moego.svc.business.customer.repository.jooq.tables;


import com.moego.svc.business.customer.repository.jooq.Keys;
import com.moego.svc.business.customer.repository.jooq.MoeCustomer;
import com.moego.svc.business.customer.repository.jooq.tables.records.PetIncidentReportPetMappingRecord;

import java.util.Arrays;
import java.util.List;
import java.util.function.Function;

import org.jooq.Field;
import org.jooq.ForeignKey;
import org.jooq.Function6;
import org.jooq.Identity;
import org.jooq.JSON;
import org.jooq.Name;
import org.jooq.Record;
import org.jooq.Records;
import org.jooq.Row6;
import org.jooq.Schema;
import org.jooq.SelectField;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;


/**
 * pet incident report pet表, 记录incident pet列表
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class PetIncidentReportPetMapping extends TableImpl<PetIncidentReportPetMappingRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of
     * <code>moe_customer.pet_incident_report_pet_mapping</code>
     */
    public static final PetIncidentReportPetMapping PET_INCIDENT_REPORT_PET_MAPPING = new PetIncidentReportPetMapping();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<PetIncidentReportPetMappingRecord> getRecordType() {
        return PetIncidentReportPetMappingRecord.class;
    }

    /**
     * The column <code>moe_customer.pet_incident_report_pet_mapping.id</code>.
     */
    public final TableField<PetIncidentReportPetMappingRecord, Long> ID = createField(DSL.name("id"), SQLDataType.BIGINT.nullable(false).identity(true), this, "");

    /**
     * The column
     * <code>moe_customer.pet_incident_report_pet_mapping.company_id</code>.
     * company id
     */
    public final TableField<PetIncidentReportPetMappingRecord, Long> COMPANY_ID = createField(DSL.name("company_id"), SQLDataType.BIGINT.nullable(false).defaultValue(DSL.inline("0", SQLDataType.BIGINT)), this, "company id");

    /**
     * The column
     * <code>moe_customer.pet_incident_report_pet_mapping.business_id</code>.
     * business id
     */
    public final TableField<PetIncidentReportPetMappingRecord, Long> BUSINESS_ID = createField(DSL.name("business_id"), SQLDataType.BIGINT.nullable(false).defaultValue(DSL.inline("0", SQLDataType.BIGINT)), this, "business id");

    /**
     * The column
     * <code>moe_customer.pet_incident_report_pet_mapping.incident_id</code>.
     * incident id
     */
    public final TableField<PetIncidentReportPetMappingRecord, Long> INCIDENT_ID = createField(DSL.name("incident_id"), SQLDataType.BIGINT.nullable(false).defaultValue(DSL.inline("0", SQLDataType.BIGINT)), this, "incident id");

    /**
     * The column
     * <code>moe_customer.pet_incident_report_pet_mapping.pet_id</code>. pet id
     */
    public final TableField<PetIncidentReportPetMappingRecord, Long> PET_ID = createField(DSL.name("pet_id"), SQLDataType.BIGINT.nullable(false).defaultValue(DSL.inline("0", SQLDataType.BIGINT)), this, "pet id");

    /**
     * The column
     * <code>moe_customer.pet_incident_report_pet_mapping.vaccine_status</code>.
     * pet vaccine status 快照
     */
    public final TableField<PetIncidentReportPetMappingRecord, JSON> VACCINE_STATUS = createField(DSL.name("vaccine_status"), SQLDataType.JSON, this, "pet vaccine status 快照");

    private PetIncidentReportPetMapping(Name alias, Table<PetIncidentReportPetMappingRecord> aliased) {
        this(alias, aliased, null);
    }

    private PetIncidentReportPetMapping(Name alias, Table<PetIncidentReportPetMappingRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, DSL.comment("pet incident report pet表, 记录incident pet列表"), TableOptions.table());
    }

    /**
     * Create an aliased
     * <code>moe_customer.pet_incident_report_pet_mapping</code> table reference
     */
    public PetIncidentReportPetMapping(String alias) {
        this(DSL.name(alias), PET_INCIDENT_REPORT_PET_MAPPING);
    }

    /**
     * Create an aliased
     * <code>moe_customer.pet_incident_report_pet_mapping</code> table reference
     */
    public PetIncidentReportPetMapping(Name alias) {
        this(alias, PET_INCIDENT_REPORT_PET_MAPPING);
    }

    /**
     * Create a <code>moe_customer.pet_incident_report_pet_mapping</code> table
     * reference
     */
    public PetIncidentReportPetMapping() {
        this(DSL.name("pet_incident_report_pet_mapping"), null);
    }

    public <O extends Record> PetIncidentReportPetMapping(Table<O> child, ForeignKey<O, PetIncidentReportPetMappingRecord> key) {
        super(child, key, PET_INCIDENT_REPORT_PET_MAPPING);
    }

    @Override
    public Schema getSchema() {
        return aliased() ? null : MoeCustomer.MOE_CUSTOMER;
    }

    @Override
    public Identity<PetIncidentReportPetMappingRecord, Long> getIdentity() {
        return (Identity<PetIncidentReportPetMappingRecord, Long>) super.getIdentity();
    }

    @Override
    public UniqueKey<PetIncidentReportPetMappingRecord> getPrimaryKey() {
        return Keys.KEY_PET_INCIDENT_REPORT_PET_MAPPING_PRIMARY;
    }

    @Override
    public List<UniqueKey<PetIncidentReportPetMappingRecord>> getUniqueKeys() {
        return Arrays.asList(Keys.KEY_PET_INCIDENT_REPORT_PET_MAPPING_UK_COMPANY_PET_INCIDENT);
    }

    @Override
    public PetIncidentReportPetMapping as(String alias) {
        return new PetIncidentReportPetMapping(DSL.name(alias), this);
    }

    @Override
    public PetIncidentReportPetMapping as(Name alias) {
        return new PetIncidentReportPetMapping(alias, this);
    }

    @Override
    public PetIncidentReportPetMapping as(Table<?> alias) {
        return new PetIncidentReportPetMapping(alias.getQualifiedName(), this);
    }

    /**
     * Rename this table
     */
    @Override
    public PetIncidentReportPetMapping rename(String name) {
        return new PetIncidentReportPetMapping(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public PetIncidentReportPetMapping rename(Name name) {
        return new PetIncidentReportPetMapping(name, null);
    }

    /**
     * Rename this table
     */
    @Override
    public PetIncidentReportPetMapping rename(Table<?> name) {
        return new PetIncidentReportPetMapping(name.getQualifiedName(), null);
    }

    // -------------------------------------------------------------------------
    // Row6 type methods
    // -------------------------------------------------------------------------

    @Override
    public Row6<Long, Long, Long, Long, Long, JSON> fieldsRow() {
        return (Row6) super.fieldsRow();
    }

    /**
     * Convenience mapping calling {@link SelectField#convertFrom(Function)}.
     */
    public <U> SelectField<U> mapping(Function6<? super Long, ? super Long, ? super Long, ? super Long, ? super Long, ? super JSON, ? extends U> from) {
        return convertFrom(Records.mapping(from));
    }

    /**
     * Convenience mapping calling {@link SelectField#convertFrom(Class,
     * Function)}.
     */
    public <U> SelectField<U> mapping(Class<U> toType, Function6<? super Long, ? super Long, ? super Long, ? super Long, ? super Long, ? super JSON, ? extends U> from) {
        return convertFrom(toType, Records.mapping(from));
    }
}
