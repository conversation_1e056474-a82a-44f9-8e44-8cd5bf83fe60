/*
 * This file is generated by jOOQ.
 */
package com.moego.svc.business.customer.repository.jooq.tables.records;


import com.moego.svc.business.customer.repository.jooq.tables.MoeCustomerAccountBlackList;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record6;
import org.jooq.Row6;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class MoeCustomerAccountBlackListRecord extends UpdatableRecordImpl<MoeCustomerAccountBlackListRecord> implements Record6<Integer, Integer, String, Integer, Integer, Integer> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>moe_customer.moe_customer_account_black_list.id</code>.
     */
    public MoeCustomerAccountBlackListRecord setId(Integer value) {
        set(0, value);
        return this;
    }

    /**
     * Getter for <code>moe_customer.moe_customer_account_black_list.id</code>.
     */
    public Integer getId() {
        return (Integer) get(0);
    }

    /**
     * Setter for
     * <code>moe_customer.moe_customer_account_black_list.type</code>. 1-phone
     * number, 2-email
     */
    public MoeCustomerAccountBlackListRecord setType(Integer value) {
        set(1, value);
        return this;
    }

    /**
     * Getter for
     * <code>moe_customer.moe_customer_account_black_list.type</code>. 1-phone
     * number, 2-email
     */
    public Integer getType() {
        return (Integer) get(1);
    }

    /**
     * Setter for
     * <code>moe_customer.moe_customer_account_black_list.value</code>. string
     * of phone number or email
     */
    public MoeCustomerAccountBlackListRecord setValue(String value) {
        set(2, value);
        return this;
    }

    /**
     * Getter for
     * <code>moe_customer.moe_customer_account_black_list.value</code>. string
     * of phone number or email
     */
    public String getValue() {
        return (String) get(2);
    }

    /**
     * Setter for
     * <code>moe_customer.moe_customer_account_black_list.status</code>.
     * 1-normal, 2-deleted
     */
    public MoeCustomerAccountBlackListRecord setStatus(Integer value) {
        set(3, value);
        return this;
    }

    /**
     * Getter for
     * <code>moe_customer.moe_customer_account_black_list.status</code>.
     * 1-normal, 2-deleted
     */
    public Integer getStatus() {
        return (Integer) get(3);
    }

    /**
     * Setter for
     * <code>moe_customer.moe_customer_account_black_list.create_time</code>.
     */
    public MoeCustomerAccountBlackListRecord setCreateTime(Integer value) {
        set(4, value);
        return this;
    }

    /**
     * Getter for
     * <code>moe_customer.moe_customer_account_black_list.create_time</code>.
     */
    public Integer getCreateTime() {
        return (Integer) get(4);
    }

    /**
     * Setter for
     * <code>moe_customer.moe_customer_account_black_list.update_time</code>.
     */
    public MoeCustomerAccountBlackListRecord setUpdateTime(Integer value) {
        set(5, value);
        return this;
    }

    /**
     * Getter for
     * <code>moe_customer.moe_customer_account_black_list.update_time</code>.
     */
    public Integer getUpdateTime() {
        return (Integer) get(5);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<Integer> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record6 type implementation
    // -------------------------------------------------------------------------

    @Override
    public Row6<Integer, Integer, String, Integer, Integer, Integer> fieldsRow() {
        return (Row6) super.fieldsRow();
    }

    @Override
    public Row6<Integer, Integer, String, Integer, Integer, Integer> valuesRow() {
        return (Row6) super.valuesRow();
    }

    @Override
    public Field<Integer> field1() {
        return MoeCustomerAccountBlackList.MOE_CUSTOMER_ACCOUNT_BLACK_LIST.ID;
    }

    @Override
    public Field<Integer> field2() {
        return MoeCustomerAccountBlackList.MOE_CUSTOMER_ACCOUNT_BLACK_LIST.TYPE;
    }

    @Override
    public Field<String> field3() {
        return MoeCustomerAccountBlackList.MOE_CUSTOMER_ACCOUNT_BLACK_LIST.VALUE;
    }

    @Override
    public Field<Integer> field4() {
        return MoeCustomerAccountBlackList.MOE_CUSTOMER_ACCOUNT_BLACK_LIST.STATUS;
    }

    @Override
    public Field<Integer> field5() {
        return MoeCustomerAccountBlackList.MOE_CUSTOMER_ACCOUNT_BLACK_LIST.CREATE_TIME;
    }

    @Override
    public Field<Integer> field6() {
        return MoeCustomerAccountBlackList.MOE_CUSTOMER_ACCOUNT_BLACK_LIST.UPDATE_TIME;
    }

    @Override
    public Integer component1() {
        return getId();
    }

    @Override
    public Integer component2() {
        return getType();
    }

    @Override
    public String component3() {
        return getValue();
    }

    @Override
    public Integer component4() {
        return getStatus();
    }

    @Override
    public Integer component5() {
        return getCreateTime();
    }

    @Override
    public Integer component6() {
        return getUpdateTime();
    }

    @Override
    public Integer value1() {
        return getId();
    }

    @Override
    public Integer value2() {
        return getType();
    }

    @Override
    public String value3() {
        return getValue();
    }

    @Override
    public Integer value4() {
        return getStatus();
    }

    @Override
    public Integer value5() {
        return getCreateTime();
    }

    @Override
    public Integer value6() {
        return getUpdateTime();
    }

    @Override
    public MoeCustomerAccountBlackListRecord value1(Integer value) {
        setId(value);
        return this;
    }

    @Override
    public MoeCustomerAccountBlackListRecord value2(Integer value) {
        setType(value);
        return this;
    }

    @Override
    public MoeCustomerAccountBlackListRecord value3(String value) {
        setValue(value);
        return this;
    }

    @Override
    public MoeCustomerAccountBlackListRecord value4(Integer value) {
        setStatus(value);
        return this;
    }

    @Override
    public MoeCustomerAccountBlackListRecord value5(Integer value) {
        setCreateTime(value);
        return this;
    }

    @Override
    public MoeCustomerAccountBlackListRecord value6(Integer value) {
        setUpdateTime(value);
        return this;
    }

    @Override
    public MoeCustomerAccountBlackListRecord values(Integer value1, Integer value2, String value3, Integer value4, Integer value5, Integer value6) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        return this;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached MoeCustomerAccountBlackListRecord
     */
    public MoeCustomerAccountBlackListRecord() {
        super(MoeCustomerAccountBlackList.MOE_CUSTOMER_ACCOUNT_BLACK_LIST);
    }

    /**
     * Create a detached, initialised MoeCustomerAccountBlackListRecord
     */
    public MoeCustomerAccountBlackListRecord(Integer id, Integer type, String value, Integer status, Integer createTime, Integer updateTime) {
        super(MoeCustomerAccountBlackList.MOE_CUSTOMER_ACCOUNT_BLACK_LIST);

        setId(id);
        setType(type);
        setValue(value);
        setStatus(status);
        setCreateTime(createTime);
        setUpdateTime(updateTime);
        resetChangedOnNotNull();
    }
}
