/*
 * This file is generated by jOOQ.
 */
package com.moego.svc.business.customer.repository.jooq.tables.records;


import com.moego.svc.business.customer.repository.jooq.tables.CustomerCreationSetting;

import java.time.LocalDateTime;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record6;
import org.jooq.Row6;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class CustomerCreationSettingRecord extends UpdatableRecordImpl<CustomerCreationSettingRecord> implements Record6<Long, Long, Integer, Integer, LocalDateTime, LocalDateTime> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>moe_customer.customer_creation_setting.id</code>.
     */
    public CustomerCreationSettingRecord setId(Long value) {
        set(0, value);
        return this;
    }

    /**
     * Getter for <code>moe_customer.customer_creation_setting.id</code>.
     */
    public Long getId() {
        return (Long) get(0);
    }

    /**
     * Setter for
     * <code>moe_customer.customer_creation_setting.company_id</code>.
     */
    public CustomerCreationSettingRecord setCompanyId(Long value) {
        set(1, value);
        return this;
    }

    /**
     * Getter for
     * <code>moe_customer.customer_creation_setting.company_id</code>.
     */
    public Long getCompanyId() {
        return (Long) get(1);
    }

    /**
     * Setter for
     * <code>moe_customer.customer_creation_setting.enable_creation_from_sms</code>.
     */
    public CustomerCreationSettingRecord setEnableCreationFromSms(Integer value) {
        set(2, value);
        return this;
    }

    /**
     * Getter for
     * <code>moe_customer.customer_creation_setting.enable_creation_from_sms</code>.
     */
    public Integer getEnableCreationFromSms() {
        return (Integer) get(2);
    }

    /**
     * Setter for
     * <code>moe_customer.customer_creation_setting.enable_creation_from_call</code>.
     */
    public CustomerCreationSettingRecord setEnableCreationFromCall(Integer value) {
        set(3, value);
        return this;
    }

    /**
     * Getter for
     * <code>moe_customer.customer_creation_setting.enable_creation_from_call</code>.
     */
    public Integer getEnableCreationFromCall() {
        return (Integer) get(3);
    }

    /**
     * Setter for
     * <code>moe_customer.customer_creation_setting.created_at</code>.
     */
    public CustomerCreationSettingRecord setCreatedAt(LocalDateTime value) {
        set(4, value);
        return this;
    }

    /**
     * Getter for
     * <code>moe_customer.customer_creation_setting.created_at</code>.
     */
    public LocalDateTime getCreatedAt() {
        return (LocalDateTime) get(4);
    }

    /**
     * Setter for
     * <code>moe_customer.customer_creation_setting.updated_at</code>.
     */
    public CustomerCreationSettingRecord setUpdatedAt(LocalDateTime value) {
        set(5, value);
        return this;
    }

    /**
     * Getter for
     * <code>moe_customer.customer_creation_setting.updated_at</code>.
     */
    public LocalDateTime getUpdatedAt() {
        return (LocalDateTime) get(5);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<Long> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record6 type implementation
    // -------------------------------------------------------------------------

    @Override
    public Row6<Long, Long, Integer, Integer, LocalDateTime, LocalDateTime> fieldsRow() {
        return (Row6) super.fieldsRow();
    }

    @Override
    public Row6<Long, Long, Integer, Integer, LocalDateTime, LocalDateTime> valuesRow() {
        return (Row6) super.valuesRow();
    }

    @Override
    public Field<Long> field1() {
        return CustomerCreationSetting.CUSTOMER_CREATION_SETTING.ID;
    }

    @Override
    public Field<Long> field2() {
        return CustomerCreationSetting.CUSTOMER_CREATION_SETTING.COMPANY_ID;
    }

    @Override
    public Field<Integer> field3() {
        return CustomerCreationSetting.CUSTOMER_CREATION_SETTING.ENABLE_CREATION_FROM_SMS;
    }

    @Override
    public Field<Integer> field4() {
        return CustomerCreationSetting.CUSTOMER_CREATION_SETTING.ENABLE_CREATION_FROM_CALL;
    }

    @Override
    public Field<LocalDateTime> field5() {
        return CustomerCreationSetting.CUSTOMER_CREATION_SETTING.CREATED_AT;
    }

    @Override
    public Field<LocalDateTime> field6() {
        return CustomerCreationSetting.CUSTOMER_CREATION_SETTING.UPDATED_AT;
    }

    @Override
    public Long component1() {
        return getId();
    }

    @Override
    public Long component2() {
        return getCompanyId();
    }

    @Override
    public Integer component3() {
        return getEnableCreationFromSms();
    }

    @Override
    public Integer component4() {
        return getEnableCreationFromCall();
    }

    @Override
    public LocalDateTime component5() {
        return getCreatedAt();
    }

    @Override
    public LocalDateTime component6() {
        return getUpdatedAt();
    }

    @Override
    public Long value1() {
        return getId();
    }

    @Override
    public Long value2() {
        return getCompanyId();
    }

    @Override
    public Integer value3() {
        return getEnableCreationFromSms();
    }

    @Override
    public Integer value4() {
        return getEnableCreationFromCall();
    }

    @Override
    public LocalDateTime value5() {
        return getCreatedAt();
    }

    @Override
    public LocalDateTime value6() {
        return getUpdatedAt();
    }

    @Override
    public CustomerCreationSettingRecord value1(Long value) {
        setId(value);
        return this;
    }

    @Override
    public CustomerCreationSettingRecord value2(Long value) {
        setCompanyId(value);
        return this;
    }

    @Override
    public CustomerCreationSettingRecord value3(Integer value) {
        setEnableCreationFromSms(value);
        return this;
    }

    @Override
    public CustomerCreationSettingRecord value4(Integer value) {
        setEnableCreationFromCall(value);
        return this;
    }

    @Override
    public CustomerCreationSettingRecord value5(LocalDateTime value) {
        setCreatedAt(value);
        return this;
    }

    @Override
    public CustomerCreationSettingRecord value6(LocalDateTime value) {
        setUpdatedAt(value);
        return this;
    }

    @Override
    public CustomerCreationSettingRecord values(Long value1, Long value2, Integer value3, Integer value4, LocalDateTime value5, LocalDateTime value6) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        return this;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached CustomerCreationSettingRecord
     */
    public CustomerCreationSettingRecord() {
        super(CustomerCreationSetting.CUSTOMER_CREATION_SETTING);
    }

    /**
     * Create a detached, initialised CustomerCreationSettingRecord
     */
    public CustomerCreationSettingRecord(Long id, Long companyId, Integer enableCreationFromSms, Integer enableCreationFromCall, LocalDateTime createdAt, LocalDateTime updatedAt) {
        super(CustomerCreationSetting.CUSTOMER_CREATION_SETTING);

        setId(id);
        setCompanyId(companyId);
        setEnableCreationFromSms(enableCreationFromSms);
        setEnableCreationFromCall(enableCreationFromCall);
        setCreatedAt(createdAt);
        setUpdatedAt(updatedAt);
        resetChangedOnNotNull();
    }
}
