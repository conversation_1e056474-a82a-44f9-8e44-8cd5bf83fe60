/*
 * This file is generated by jOOQ.
 */
package com.moego.svc.account.repository.jooq.tables;


import com.moego.svc.account.repository.jooq.Indexes;
import com.moego.svc.account.repository.jooq.Keys;
import com.moego.svc.account.repository.jooq.Public;
import com.moego.svc.account.repository.jooq.tables.records.AccountImpersonateApprovalInstanceRecord;

import java.time.OffsetDateTime;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;

import org.jooq.Condition;
import org.jooq.Field;
import org.jooq.Index;
import org.jooq.Name;
import org.jooq.PlainSQL;
import org.jooq.QueryPart;
import org.jooq.SQL;
import org.jooq.Schema;
import org.jooq.Select;
import org.jooq.Stringly;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class AccountImpersonateApprovalInstance extends TableImpl<AccountImpersonateApprovalInstanceRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of
     * <code>public.account_impersonate_approval_instance</code>
     */
    public static final AccountImpersonateApprovalInstance ACCOUNT_IMPERSONATE_APPROVAL_INSTANCE = new AccountImpersonateApprovalInstance();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<AccountImpersonateApprovalInstanceRecord> getRecordType() {
        return AccountImpersonateApprovalInstanceRecord.class;
    }

    /**
     * The column
     * <code>public.account_impersonate_approval_instance.instance_code</code>.
     */
    public final TableField<AccountImpersonateApprovalInstanceRecord, String> INSTANCE_CODE = createField(DSL.name("instance_code"), SQLDataType.VARCHAR(64).nullable(false), this, "");

    /**
     * The column
     * <code>public.account_impersonate_approval_instance.impersonator</code>.
     */
    public final TableField<AccountImpersonateApprovalInstanceRecord, String> IMPERSONATOR = createField(DSL.name("impersonator"), SQLDataType.VARCHAR(255).nullable(false), this, "");

    /**
     * The column
     * <code>public.account_impersonate_approval_instance.source</code>.
     */
    public final TableField<AccountImpersonateApprovalInstanceRecord, String> SOURCE = createField(DSL.name("source"), SQLDataType.VARCHAR(50).nullable(false), this, "");

    /**
     * The column
     * <code>public.account_impersonate_approval_instance.target_account_id</code>.
     */
    public final TableField<AccountImpersonateApprovalInstanceRecord, Long> TARGET_ACCOUNT_ID = createField(DSL.name("target_account_id"), SQLDataType.BIGINT.nullable(false), this, "");

    /**
     * The column
     * <code>public.account_impersonate_approval_instance.target_account_email</code>.
     */
    public final TableField<AccountImpersonateApprovalInstanceRecord, String> TARGET_ACCOUNT_EMAIL = createField(DSL.name("target_account_email"), SQLDataType.VARCHAR(255).nullable(false), this, "");

    /**
     * The column
     * <code>public.account_impersonate_approval_instance.max_age</code>.
     */
    public final TableField<AccountImpersonateApprovalInstanceRecord, Long> MAX_AGE = createField(DSL.name("max_age"), SQLDataType.BIGINT.nullable(false), this, "");

    /**
     * The column
     * <code>public.account_impersonate_approval_instance.status</code>.
     */
    public final TableField<AccountImpersonateApprovalInstanceRecord, String> STATUS = createField(DSL.name("status"), SQLDataType.VARCHAR(20).nullable(false), this, "");

    /**
     * The column
     * <code>public.account_impersonate_approval_instance.created_at</code>.
     */
    public final TableField<AccountImpersonateApprovalInstanceRecord, OffsetDateTime> CREATED_AT = createField(DSL.name("created_at"), SQLDataType.TIMESTAMPWITHTIMEZONE(6).nullable(false), this, "");

    /**
     * The column
     * <code>public.account_impersonate_approval_instance.approved_at</code>.
     */
    public final TableField<AccountImpersonateApprovalInstanceRecord, OffsetDateTime> APPROVED_AT = createField(DSL.name("approved_at"), SQLDataType.TIMESTAMPWITHTIMEZONE(6), this, "");

    private AccountImpersonateApprovalInstance(Name alias, Table<AccountImpersonateApprovalInstanceRecord> aliased) {
        this(alias, aliased, (Field<?>[]) null, null);
    }

    private AccountImpersonateApprovalInstance(Name alias, Table<AccountImpersonateApprovalInstanceRecord> aliased, Field<?>[] parameters, Condition where) {
        super(alias, null, aliased, parameters, DSL.comment(""), TableOptions.table(), where);
    }

    /**
     * Create an aliased
     * <code>public.account_impersonate_approval_instance</code> table reference
     */
    public AccountImpersonateApprovalInstance(String alias) {
        this(DSL.name(alias), ACCOUNT_IMPERSONATE_APPROVAL_INSTANCE);
    }

    /**
     * Create an aliased
     * <code>public.account_impersonate_approval_instance</code> table reference
     */
    public AccountImpersonateApprovalInstance(Name alias) {
        this(alias, ACCOUNT_IMPERSONATE_APPROVAL_INSTANCE);
    }

    /**
     * Create a <code>public.account_impersonate_approval_instance</code> table
     * reference
     */
    public AccountImpersonateApprovalInstance() {
        this(DSL.name("account_impersonate_approval_instance"), null);
    }

    @Override
    public Schema getSchema() {
        return aliased() ? null : Public.PUBLIC;
    }

    @Override
    public List<Index> getIndexes() {
        return Arrays.asList(Indexes.ACCOUNT_IMPERSONATE_APPROVAL_INSTANCE_IDX_APPROVED, Indexes.ACCOUNT_IMPERSONATE_APPROVAL_INSTANCE_IDX_CREATED);
    }

    @Override
    public UniqueKey<AccountImpersonateApprovalInstanceRecord> getPrimaryKey() {
        return Keys.ACCOUNT_IMPERSONATE_APPROVAL_INSTANCE_PKEY;
    }

    @Override
    public AccountImpersonateApprovalInstance as(String alias) {
        return new AccountImpersonateApprovalInstance(DSL.name(alias), this);
    }

    @Override
    public AccountImpersonateApprovalInstance as(Name alias) {
        return new AccountImpersonateApprovalInstance(alias, this);
    }

    @Override
    public AccountImpersonateApprovalInstance as(Table<?> alias) {
        return new AccountImpersonateApprovalInstance(alias.getQualifiedName(), this);
    }

    /**
     * Rename this table
     */
    @Override
    public AccountImpersonateApprovalInstance rename(String name) {
        return new AccountImpersonateApprovalInstance(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public AccountImpersonateApprovalInstance rename(Name name) {
        return new AccountImpersonateApprovalInstance(name, null);
    }

    /**
     * Rename this table
     */
    @Override
    public AccountImpersonateApprovalInstance rename(Table<?> name) {
        return new AccountImpersonateApprovalInstance(name.getQualifiedName(), null);
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public AccountImpersonateApprovalInstance where(Condition condition) {
        return new AccountImpersonateApprovalInstance(getQualifiedName(), aliased() ? this : null, null, condition);
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public AccountImpersonateApprovalInstance where(Collection<? extends Condition> conditions) {
        return where(DSL.and(conditions));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public AccountImpersonateApprovalInstance where(Condition... conditions) {
        return where(DSL.and(conditions));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public AccountImpersonateApprovalInstance where(Field<Boolean> condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public AccountImpersonateApprovalInstance where(SQL condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public AccountImpersonateApprovalInstance where(@Stringly.SQL String condition) {
        return where(DSL.condition(condition));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public AccountImpersonateApprovalInstance where(@Stringly.SQL String condition, Object... binds) {
        return where(DSL.condition(condition, binds));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    @PlainSQL
    public AccountImpersonateApprovalInstance where(@Stringly.SQL String condition, QueryPart... parts) {
        return where(DSL.condition(condition, parts));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public AccountImpersonateApprovalInstance whereExists(Select<?> select) {
        return where(DSL.exists(select));
    }

    /**
     * Create an inline derived table from this table
     */
    @Override
    public AccountImpersonateApprovalInstance whereNotExists(Select<?> select) {
        return where(DSL.notExists(select));
    }
}
