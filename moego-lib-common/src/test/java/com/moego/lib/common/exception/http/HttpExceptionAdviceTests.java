package com.moego.lib.common.exception.http;

import static org.assertj.core.api.Assertions.assertThat;

import com.moego.lib.common.EchoSvcController;
import com.moego.model.echo.v1.EchoRequest;
import com.moego.svc.echo.v1.EchoGrpc;
import io.grpc.StatusRuntimeException;
import jakarta.servlet.http.HttpServletResponse;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.web.client.TestRestTemplate;
import org.springframework.boot.test.web.server.LocalServerPort;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * {@link HttpExceptionAdvice} tester.
 *
 * <AUTHOR>
 */
@SpringBootTest(
        classes = HttpExceptionAdviceTests.Cfg.class,
        webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT,
        properties = {
            "moego.grpc.client.in-process.name=HttpExceptionAdviceTests",
            "moego.grpc.server.in-process.name=HttpExceptionAdviceTests"
        })
class HttpExceptionAdviceTests {

    @Autowired
    EchoGrpc.EchoBlockingStub stub;

    @Autowired
    TestRestTemplate rest;

    @LocalServerPort
    int port;

    /**
     * {@link HttpExceptionAdvice#handleStatusRuntimeException(StatusRuntimeException, HttpServletResponse)}
     */
    @Test
    void handleStatusRuntimeException() {
        ResponseEntity<String> response = rest.getForEntity("http://localhost:" + port, String.class);

        assertThat(response.getStatusCodeValue()).isEqualTo(HttpStatus.OK.value());
        assertThat(response.getBody()).contains("INVALID_ARGUMENT");
    }

    @Configuration(proxyBeanMethods = false)
    @EnableAutoConfiguration
    @RestController
    @Import(EchoSvcController.class)
    static class Cfg {

        @Autowired
        EchoGrpc.EchoBlockingStub stub;

        @GetMapping
        public String get() {
            stub.echo(EchoRequest.newBuilder().setMessage("").build());
            return "OK";
        }
    }
}
