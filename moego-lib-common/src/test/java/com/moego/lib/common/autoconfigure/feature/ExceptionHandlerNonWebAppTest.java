package com.moego.lib.common.autoconfigure.feature;

import static org.assertj.core.api.Assertions.assertThat;

import com.moego.lib.common.exception.grpc.GrpcExceptionAdvice;
import com.moego.lib.common.exception.grpc.error.GrpcExceptionServerInterceptor;
import com.moego.lib.common.exception.http.FeignDecoderExceptionAdvice;
import com.moego.lib.common.exception.http.HttpExceptionAdvice;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.runner.ApplicationContextRunner;

/**
 * {@link ExceptionHandler} tester.
 */
public class ExceptionHandlerNonWebAppTest {

    private final ApplicationContextRunner runner =
            new ApplicationContextRunner().withUserConfiguration(ExceptionHandler.class);

    @Test
    public void testDefaultBehavior() {
        runner.run(context -> {
            assertThat(context).doesNotHaveBean(HttpExceptionAdvice.class);
            assertThat(context).doesNotHaveBean(FeignDecoderExceptionAdvice.class);
            assertThat(context).hasSingleBean(GrpcExceptionServerInterceptor.class);
            assertThat(context).hasSingleBean(GrpcExceptionAdvice.class);
        });
    }
}
