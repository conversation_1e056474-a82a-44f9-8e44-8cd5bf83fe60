INSERT INTO `moe_payment`.`moe_feature` (`id`, `name`, `code`, `allow_type`, `create_time`, `enable`, `quota`, `update_time`, `is_deleted`) VALUES (1, 'auto message', 'autoMessage', 2, '2022-09-06 09:41:20', 0, 0, '2022-09-06 09:41:20', 0), (2, 'three confirm reminder', 'confirmReminder', 2, '2022-09-06 09:41:20', 0, 0, '2022-09-06 09:41:20', 0), (3, 'pick up', 'pickUp', 2, '2022-09-06 09:41:20', 0, 0, '2022-09-06 09:41:20', 0), (4, 'smart scheduling', 'ss', 2, '2022-09-06 09:41:20', 0, 0, '2022-09-06 09:41:20', 0), (5, 'smart scheduling for repeat', 'ssForRepeat', 2, '2022-09-06 09:41:20', 0, 0, '2022-09-06 09:41:20', 0), (6, 'stripe', 'stripe', 2, '2022-09-06 09:41:20', 0, 0, '2022-09-06 09:41:20', 0), (7, 'square', 'square', 2, '2022-09-06 09:41:20', 0, 0, '2022-09-06 09:41:20', 0), (8, 'digital agreement', 'agreement', 2, '2022-09-06 09:41:20', 0, 0, '2022-09-06 09:41:20', 0), (9, 'intake form', 'intakeForm', 2, '2022-09-06 09:41:20', 0, 0, '2022-09-06 09:41:20', 0), (10, 'online booking', 'onlineBooking', 2, '2022-09-06 09:41:20', 0, 0, '2022-09-06 09:41:20', 0), (11, 'auto reply', 'autoReply', 2, '2022-09-06 09:41:20', 0, 0, '2022-09-06 09:41:20', 0), (12, 'two way', 'twoWay', 2, '2022-09-06 09:41:20', 0, 0, '2022-09-06 09:41:20', 0), (13, 'mass Text', 'massText', 2, '2022-09-06 09:41:20', 0, 0, '2022-09-06 09:41:20', 0), (14, 'reviewBooster', 'reviewBooster', 2, '2022-09-06 09:41:20', 0, 0, '2022-09-06 09:41:20', 0), (15, 'payroll', 'payroll', 2, '2022-09-06 09:41:20', 0, 0, '2022-09-06 09:41:20', 0), (16, 'payment', 'payment', 2, '2022-09-06 09:41:20', 0, 0, '2022-09-06 09:41:20', 0), (17, 'reportLeaderBoard', 'leaderBoard', 2, '2022-09-06 09:41:20', 0, 0, '2022-09-06 09:41:20', 0), (18, 'reportNormal', 'reportNormal', 2, '2022-09-06 09:41:20', 1, -1, '2022-09-06 09:41:20', 0), (19, 'eta', 'eta', 2, '2022-09-06 09:41:20', 0, 0, '2022-09-06 09:41:20', 0), (20, 'obByBreed', 'obByBreed', 2, '2022-09-06 09:41:20', 0, 0, '2022-09-06 09:41:20', 0), (21, 'nearby', 'nearby', 0, '2022-09-06 09:41:20', 0, 0, '2022-09-06 09:41:20', 0), (22, 'Certain area for certain days', 'cacd', 0, '2022-09-06 09:41:20', 0, 0, '2022-09-06 09:41:20', 0), (23, 'arrival window', 'arrivalWindow', 0, '2022-09-06 09:41:20', 0, 0, '2022-09-06 09:41:20', 0), (24, 'routeOptimization', 'routeOptimization', 0, '2022-09-06 09:41:20', 0, 0, '2022-09-06 09:41:20', 0), (25, 'birthdayReminder', 'birthdayReminder', 2, '2022-09-06 09:41:20', 0, 0, '2022-09-06 09:41:20', 0), (26, 'rebookReminder', 'rebookReminder', 2, '2022-09-06 09:41:20', 0, 0, '2022-09-06 09:41:20', 0), (27, 'repeatAppt', 'repeatAppt', 2, '2022-09-06 09:41:20', 0, 0, '2022-09-06 09:41:20', 0), (28, 'waitingList', 'waitingList', 2, '2022-09-06 09:41:20', 0, 0, '2022-09-06 09:41:20', 0), (29, 'google calendar', 'googleCalendar', 2, '2022-09-06 09:41:20', 0, 0, '2022-09-06 09:41:20', 0), (30, 'quickBook', 'quickBook', 2, '2022-09-06 09:41:20', 0, 0, '2022-09-06 09:41:20', 0), (31, 'stripe reader', 'stripeReader', 2, '2022-09-06 09:41:20', 0, 0, '2022-09-06 09:41:20', 0), (32, 'call forwarding', 'callForwarding', 2, '2022-09-06 09:41:20', 0, 0, '2022-09-06 09:41:20', 0), (33, 'map view', 'mapView', 0, '2022-09-06 09:41:20', 0, 0, '2022-09-06 09:41:20', 0), (34, 'obBySlot', 'obBySlot', 1, '2022-09-06 09:41:20', 0, 0, '2022-09-06 09:41:20', 0), (35, 'processFee', 'processFee', 2, '2022-09-06 09:41:20', 0, 0, '2022-09-06 09:41:20', 0), (36, 'obDeposit', 'obDeposit', 2, '2022-09-06 09:41:20', 0, 0, '2022-09-06 09:41:20', 0), (37, 'retail', 'retail', 2, '2022-09-06 09:41:20', 0, 0, '2022-09-06 09:41:20', 0), (38, 'package', 'package', 2, '2022-09-06 09:41:20', 0, 0, '2022-09-06 09:41:20', 0), (39, 'expiryReminder', 'expiryReminder', 2, '2022-09-06 09:41:20', 0, 0, '2022-09-06 09:41:20', 0), (40, 'unlimited auto message', 'unlimitedAuto', 2, '2022-09-06 09:41:20', 0, 0, '2022-09-28 01:41:02', 0), (41, 'mobile van database', 'vanCURD', 0, '2022-09-14 09:07:57', 0, 0, '2022-09-28 01:40:37', 0), (42, 'multi van management', 'multiVan', 0, '2022-09-14 09:08:26', 0, 0, '2022-09-28 01:40:43', 0);


INSERT INTO `moe_payment`.`moe_plan_feature_relation` (`id`, `level`, `code`, `allow_type`, `enable`, `quota`, `create_time`, `update_time`, `is_deleted`) VALUES  (1, 1001, 'autoMessage', 2, 1, -1, '2022-09-06 09:41:31', '2022-09-06 09:41:31', 0),(2, 1001, 'confirmReminder', 2, 1, -1, '2022-09-06 09:41:31', '2022-09-06 09:41:31', 0),(3, 1001, 'pickUp', 2, 0, 0, '2022-09-06 09:41:31', '2022-09-15 10:22:03', 0),(4, 1001, 'ss', 2, 1, -1, '2022-09-06 09:41:31', '2022-09-28 02:47:18', 0),(5, 1001, 'ssForRepeat', 2, 0, 0, '2022-09-06 09:41:31', '2022-09-06 09:41:31', 0),(6, 1001, 'stripe', 2, 1, -1, '2022-09-06 09:41:31', '2022-09-06 09:41:31', 0),(7, 1001, 'square', 2, 1, -1, '2022-09-06 09:41:31', '2022-09-06 09:41:31', 0),(8, 1001, 'agreement', 2, 1, -1, '2022-09-06 09:41:31', '2022-09-06 09:41:31', 0),(9, 1001, 'intakeForm', 2, 1, -1, '2022-09-06 09:41:31', '2022-09-06 09:41:31', 0),(10, 1001, 'onlineBooking', 2, 1, -1, '2022-09-06 09:41:31', '2022-09-06 09:41:31', 0),(11, 1001, 'autoReply', 2, 0, -1, '2022-09-06 09:41:31', '2022-09-28 02:51:02', 0),(12, 1001, 'twoWay', 2, 0, 0, '2022-09-06 09:41:31', '2022-09-06 09:41:31', 0),(13, 1001, 'massText', 2, 0, 0, '2022-09-06 09:41:31', '2022-09-06 09:41:31', 0),(14, 1001, 'reviewBooster', 2, 0, 0, '2022-09-06 09:41:31', '2022-09-06 09:41:31', 0),(15, 1001, 'payroll', 2, 0, 0, '2022-09-06 09:41:31', '2022-09-06 09:41:31', 0),(16, 1001, 'payment', 2, 1, -1, '2022-09-06 09:41:31', '2022-09-06 09:41:31', 0),(17, 1001, 'leaderBoard', 2, 0, 0, '2022-09-06 09:41:31', '2022-09-06 09:41:31', 0),(18, 1001, 'reportNormal', 2, 1, -1, '2022-09-06 09:41:31', '2022-09-06 09:41:31', 0),(19, 1001, 'eta', 2, 0, 0, '2022-09-06 09:41:31', '2022-09-06 09:41:31', 0),(20, 1001, 'obByBreed', 2, 0, 0, '2022-09-06 09:41:31', '2022-09-06 09:41:31', 0),(21, 1001, 'nearby', 0, 1, 0, '2022-09-06 09:41:31', '2022-09-22 06:35:44', 0),(22, 1001, 'cacd', 0, 0, 0, '2022-09-06 09:41:31', '2022-09-06 09:41:31', 0),(23, 1001, 'arrivalWindow', 0, 1, -1, '2022-09-06 09:41:31', '2022-09-06 09:41:31', 0),(24, 1001, 'routeOptimization', 0, 0, 0, '2022-09-06 09:41:31', '2022-09-06 09:41:31', 0),(25, 1001, 'birthdayReminder', 2, 0, -1, '2022-09-06 09:41:31', '2022-09-28 02:45:45', 0),(26, 1001, 'rebookReminder', 2, 0, 0, '2022-09-06 09:41:31', '2022-09-06 09:41:31', 0),(27, 1001, 'repeatAppt', 2, 1, -1, '2022-09-06 09:41:31', '2022-09-06 09:41:31', 0),(28, 1001, 'waitingList', 2, 0, 0, '2022-09-06 09:41:31', '2022-09-06 09:41:31', 0),(29, 1001, 'googleCalendar', 2, 1, -1, '2022-09-06 09:41:31', '2022-09-06 09:41:31', 0),(30, 1001, 'quickBook', 2, 1, -1, '2022-09-06 09:41:31', '2022-09-06 09:41:31', 0),(31, 1001, 'stripeReader', 2, 1, -1, '2022-09-06 09:41:31', '2022-09-06 09:41:31', 0),(32, 1001, 'callForwarding', 2, 0, 0, '2022-09-06 09:41:31', '2022-09-06 09:41:31', 0),(33, 1001, 'mapView', 0, 1, -1, '2022-09-06 09:41:31', '2022-09-06 09:41:31', 0),(34, 1001, 'obBySlot', 1, 0, -1, '2022-09-06 09:41:31', '2022-09-28 02:48:03', 0),(35, 1001, 'processFee', 2, 1, -1, '2022-09-06 09:41:31', '2022-09-06 09:41:31', 0),(36, 1001, 'obDeposit', 2, 1, -1, '2022-09-06 09:41:31', '2022-09-06 09:41:31', 0),(37, 1001, 'retail', 2, 1, -1, '2022-09-06 09:41:31', '2022-09-06 09:41:31', 0),(38, 1001, 'package', 2, 0, 0, '2022-09-06 09:41:31', '2022-09-06 09:41:31', 0),(39, 1001, 'expiryReminder', 2, 0, -1, '2022-09-06 09:41:31', '2022-09-28 02:50:34', 0),(40, 1001, 'unlimitedAuto', 2, 0, 0, '2022-09-06 09:41:31', '2022-09-06 09:41:31', 0),(41, 1101, 'autoMessage', 2, 1, -1, '2022-09-06 09:41:31', '2022-09-06 09:41:31', 0),(42, 1101, 'confirmReminder', 2, 1, -1, '2022-09-06 09:41:31', '2022-09-06 09:41:31', 0),(43, 1101, 'pickUp', 2, 1, -1, '2022-09-06 09:41:31', '2022-09-06 09:41:31', 0),(44, 1101, 'ss', 2, 1, -1, '2022-09-06 09:41:31', '2022-09-06 09:41:31', 0),(45, 1101, 'ssForRepeat', 2, 1, -1, '2022-09-06 09:41:31', '2022-09-06 09:41:31', 0),(46, 1101, 'stripe', 2, 1, -1, '2022-09-06 09:41:31', '2022-09-06 09:41:31', 0),(47, 1101, 'square', 2, 1, -1, '2022-09-06 09:41:31', '2022-09-06 09:41:31', 0),(48, 1101, 'agreement', 2, 1, -1, '2022-09-06 09:41:31', '2022-09-06 09:41:31', 0),(49, 1101, 'intakeForm', 2, 1, -1, '2022-09-06 09:41:31', '2022-09-06 09:41:31', 0),(50, 1101, 'onlineBooking', 2, 1, -1, '2022-09-06 09:41:31', '2022-09-06 09:41:31', 0),(51, 1101, 'autoReply', 2, 1, -1, '2022-09-06 09:41:31', '2022-09-06 09:41:31', 0),(52, 1101, 'twoWay', 2, 1, -1, '2022-09-06 09:41:31', '2022-09-06 09:41:31', 0),(53, 1101, 'massText', 2, 1, -1, '2022-09-06 09:41:31', '2022-09-06 09:41:31', 0),(54, 1101, 'reviewBooster', 2, 1, -1, '2022-09-06 09:41:31', '2022-09-06 09:41:31', 0),(55, 1101, 'payroll', 2, 1, -1, '2022-09-06 09:41:31', '2022-09-06 09:41:31', 0),(56, 1101, 'payment', 2, 1, -1, '2022-09-06 09:41:31', '2022-09-06 09:41:31', 0),(57, 1101, 'leaderBoard', 2, 0, 0, '2022-09-06 09:41:31', '2022-09-14 01:41:40', 0),(58, 1101, 'reportNormal', 2, 1, -1, '2022-09-06 09:41:31', '2022-09-06 09:41:31', 0),(59, 1101, 'eta', 2, 1, -1, '2022-09-06 09:41:31', '2022-09-06 09:41:31', 0),(60, 1101, 'obByBreed', 2, 1, -1, '2022-09-06 09:41:31', '2022-09-06 09:41:31', 0),(61, 1101, 'nearby', 0, 1, -1, '2022-09-06 09:41:31', '2022-09-06 09:41:31', 0),(62, 1101, 'cacd', 0, 1, -1, '2022-09-06 09:41:31', '2022-09-06 09:41:31', 0),(63, 1101, 'arrivalWindow', 0, 1, -1, '2022-09-06 09:41:31', '2022-09-06 09:41:31', 0),(64, 1101, 'routeOptimization', 0, 1, -1, '2022-09-06 09:41:31', '2022-09-06 09:41:31', 0),(65, 1101, 'birthdayReminder', 2, 1, -1, '2022-09-06 09:41:31', '2022-09-13 09:01:13', 0),(66, 1101, 'rebookReminder', 2, 1, -1, '2022-09-06 09:41:31', '2022-09-13 09:03:02', 0),(67, 1101, 'repeatAppt', 2, 1, -1, '2022-09-06 09:41:31', '2022-09-06 09:41:31', 0),(68, 1101, 'waitingList', 2, 1, -1, '2022-09-06 09:41:31', '2022-09-06 09:41:31', 0),(69, 1101, 'googleCalendar', 2, 1, -1, '2022-09-06 09:41:31', '2022-09-06 09:41:31', 0),(70, 1101, 'quickBook', 2, 1, -1, '2022-09-06 09:41:31', '2022-09-06 09:41:31', 0),(71, 1101, 'stripeReader', 2, 1, -1, '2022-09-06 09:41:31', '2022-09-06 09:41:31', 0),(72, 1101, 'callForwarding', 2, 1, -1, '2022-09-06 09:41:31', '2022-09-06 09:41:31', 0),(73, 1101, 'mapView', 0, 1, -1, '2022-09-06 09:41:31', '2022-09-06 09:41:31', 0),(74, 1101, 'obBySlot', 1, 1, -1, '2022-09-06 09:41:31', '2022-09-06 09:41:31', 0),(75, 1101, 'processFee', 2, 1, -1, '2022-09-06 09:41:31', '2022-09-06 09:41:31', 0),(76, 1101, 'obDeposit', 2, 1, -1, '2022-09-06 09:41:31', '2022-09-06 09:41:31', 0),(77, 1101, 'retail', 2, 1, -1, '2022-09-06 09:41:31', '2022-09-06 09:41:31', 0),(78, 1101, 'package', 2, 1, -1, '2022-09-06 09:41:31', '2022-09-06 09:41:31', 0),(79, 1101, 'expiryReminder', 2, 1, -1, '2022-09-06 09:41:31', '2022-09-13 09:03:06', 0),(80, 1101, 'unlimitedAuto', 2, 0, 0, '2022-09-06 09:41:31', '2022-09-13 09:03:51', 0),(81, 1201, 'autoMessage', 2, 1, -1, '2022-09-06 09:41:31', '2022-09-06 09:41:31', 0),(82, 1201, 'confirmReminder', 2, 1, -1, '2022-09-06 09:41:31', '2022-09-06 09:41:31', 0),(83, 1201, 'pickUp', 2, 1, -1, '2022-09-06 09:41:31', '2022-09-06 09:41:31', 0),(84, 1201, 'ss', 2, 1, -1, '2022-09-06 09:41:31', '2022-09-06 09:41:31', 0),(85, 1201, 'ssForRepeat', 2, 1, -1, '2022-09-06 09:41:31', '2022-09-06 09:41:31', 0),(86, 1201, 'stripe', 2, 1, -1, '2022-09-06 09:41:31', '2022-09-06 09:41:31', 0),(87, 1201, 'square', 2, 1, -1, '2022-09-06 09:41:31', '2022-09-06 09:41:31', 0),(88, 1201, 'agreement', 2, 1, -1, '2022-09-06 09:41:31', '2022-09-06 09:41:31', 0),(89, 1201, 'intakeForm', 2, 1, -1, '2022-09-06 09:41:31', '2022-09-06 09:41:31', 0),(90, 1201, 'onlineBooking', 2, 1, -1, '2022-09-06 09:41:31', '2022-09-06 09:41:31', 0),(91, 1201, 'autoReply', 2, 1, -1, '2022-09-06 09:41:31', '2022-09-13 09:02:43', 0),(92, 1201, 'twoWay', 2, 1, -1, '2022-09-06 09:41:31', '2022-09-06 09:41:31', 0),(93, 1201, 'massText', 2, 1, -1, '2022-09-06 09:41:31', '2022-09-06 09:41:31', 0),(94, 1201, 'reviewBooster', 2, 1, -1, '2022-09-06 09:41:31', '2022-09-06 09:41:31', 0),(95, 1201, 'payroll', 2, 1, -1, '2022-09-06 09:41:31', '2022-09-06 09:41:31', 0),(96, 1201, 'payment', 2, 1, -1, '2022-09-06 09:41:31', '2022-09-06 09:41:31', 0),(97, 1201, 'leaderBoard', 2, 1, -1, '2022-09-06 09:41:31', '2022-09-14 01:41:25', 0),(98, 1201, 'reportNormal', 2, 1, -1, '2022-09-06 09:41:31', '2022-09-06 09:41:31', 0),(99, 1201, 'eta', 2, 1, -1, '2022-09-06 09:41:31', '2022-09-06 09:41:31', 0),(100, 1201, 'obByBreed', 2, 1, -1, '2022-09-06 09:41:31', '2022-09-06 09:41:31', 0),(101, 1201, 'nearby', 0, 1, -1, '2022-09-06 09:41:31', '2022-09-06 09:41:31', 0),(102, 1201, 'cacd', 0, 1, -1, '2022-09-06 09:41:31', '2022-09-06 09:41:31', 0),(103, 1201, 'arrivalWindow', 0, 1, -1, '2022-09-06 09:41:31', '2022-09-06 09:41:31', 0),(104, 1201, 'routeOptimization', 0, 1, -1, '2022-09-06 09:41:31', '2022-09-06 09:41:31', 0),(105, 1201, 'birthdayReminder', 2, 1, -1, '2022-09-06 09:41:31', '2022-09-06 09:41:31', 0),(106, 1201, 'rebookReminder', 2, 1, -1, '2022-09-06 09:41:31', '2022-09-06 09:41:31', 0),(107, 1201, 'repeatAppt', 2, 1, -1, '2022-09-06 09:41:31', '2022-09-06 09:41:31', 0),(108, 1201, 'waitingList', 2, 1, -1, '2022-09-06 09:41:31', '2022-09-06 09:41:31', 0),(109, 1201, 'googleCalendar', 2, 1, -1, '2022-09-06 09:41:31', '2022-09-06 09:41:31', 0),(110, 1201, 'quickBook', 2, 1, -1, '2022-09-06 09:41:31', '2022-09-06 09:41:31', 0),(111, 1201, 'stripeReader', 2, 1, -1, '2022-09-06 09:41:31', '2022-09-06 09:41:31', 0),(112, 1201, 'callForwarding', 2, 1, -1, '2022-09-06 09:41:31', '2022-09-06 09:41:31', 0),(113, 1201, 'mapView', 0, 1, -1, '2022-09-06 09:41:31', '2022-09-06 09:41:31', 0),(114, 1201, 'obBySlot', 1, 1, -1, '2022-09-06 09:41:31', '2022-09-06 09:41:31', 0),(115, 1201, 'processFee', 2, 1, -1, '2022-09-06 09:41:31', '2022-09-06 09:41:31', 0),(116, 1201, 'obDeposit', 2, 1, -1, '2022-09-06 09:41:31', '2022-09-06 09:41:31', 0),(117, 1201, 'retail', 2, 1, -1, '2022-09-06 09:41:31', '2022-09-06 09:41:31', 0),(118, 1201, 'package', 2, 1, -1, '2022-09-06 09:41:31', '2022-09-06 09:41:31', 0),(119, 1201, 'expiryReminder', 2, 1, -1, '2022-09-06 09:41:31', '2022-09-06 09:41:31', 0),(120, 1201, 'unlimitedAuto', 2, 1, -1, '2022-09-06 09:41:31', '2022-09-06 09:41:31', 0),(121, 1001, 'vanCURD', 0, 1, -1, '2022-09-14 09:11:22', '2022-09-14 09:11:22', 0),(122, 1101, 'vanCURD', 0, 1, -1, '2022-09-14 09:11:42', '2022-09-14 09:11:42', 0),(123, 1201, 'vanCURD', 0, 1, -1, '2022-09-14 09:11:54', '2022-09-14 09:11:54', 0),(124, 1001, 'multiVan', 0, 0, 0, '2022-09-14 09:12:14', '2022-09-14 09:12:14', 0),(125, 1101, 'multiVan', 0, 1, -1, '2022-09-14 09:12:26', '2022-09-14 09:12:26', 0),(126, 1201, 'multiVan', 0, 1, -1, '2022-09-14 09:12:38', '2022-09-14 09:12:38', 0);
