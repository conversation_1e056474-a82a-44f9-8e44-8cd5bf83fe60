CREATE TABLE `moe_staff_override_area` (
   `id` int unsigned NOT NULL AUTO_INCREMENT,
   `business_id` int NOT NULL DEFAULT '0' COMMENT 'business id',
   `staff_id` int NOT NULL DEFAULT '0' COMMENT 'staff id',
   `override_date` varchar(10) COLLATE utf8mb4_unicode_520_ci NOT NULL COMMENT 'override date',
   `area_data` json NOT NULL DEFAULT (json_object()) COMMENT 'area data',
   `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'create time',
   `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'update time',
   `company_id` int NOT NULL DEFAULT '0' COMMENT 'company_id',
   PRIMARY KEY (`id`),
   UNIQUE KEY `index_staff_date` (`business_id`,`staff_id`,`override_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci COMMENT='staff area override table'