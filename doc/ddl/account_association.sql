drop table if exists account_association;
create table account_association
(
  id                    bigserial primary key,
  account_id            bigint                                                     not null,
  platform              text                                                       not null,
  platform_account_id   text                                                       not null,
  platform_data         text                       default ''                      not null,
  visible               boolean                                                    not null,
  created_at            timestamp with time zone   default statement_timestamp()   not null,
  updated_at            timestamp with time zone   default null,
  deleted_at            timestamp with time zone   default null
);

create index idx_account_association_account_id
  on account_association (account_id);

create unique index uk_account_association_platform_account_id
  on account_association (platform, platform_account_id)
  where deleted_at is null;

alter table account_association add constraint platform_length check (length(platform) < 100);
alter table account_association add constraint platform_account_id_length check (length(platform_account_id) < 100);
alter table account_association add constraint platform_data_length check (length(platform_data) < 4096);

comment on table account_association is 'Table: Third-party account association';
comment on column account_association.id is 'id, primary key';
comment on column account_association.account_id is 'account id';
comment on column account_association.platform is 'platform, such as google, apple, facebook';
comment on column account_association.platform_account_id is 'platform account id';
comment on column account_association.platform_data is 'platform data, stored as json string';
comment on column account_association.visible is 'visible, if true, the user can see this association';
comment on column account_association.created_at is 'created at';
comment on column account_association.updated_at is 'updated at, null means not updated';
comment on column account_association.deleted_at is 'updated at, null means not deleted';
comment on index idx_account_association_account_id is 'index for account id';
comment on index uk_account_association_platform_account_id is 'unique index for platform and platform account id where associations are not deleted';
