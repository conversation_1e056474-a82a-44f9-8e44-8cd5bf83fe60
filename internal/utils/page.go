package utils

import (
	"gorm.io/gorm"

	utilsV2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v2"
)

func BuildPageScopes(pagination *utilsV2.PaginationRequest) func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if pagination.GetPageNum() == 0 || pagination.GetPageSize() == 0 {
			return db
		}
		return db.Offset(int((pagination.GetPageNum() - 1) * pagination.GetPageSize())).Limit(int(pagination.GetPageSize()))
	}
}
