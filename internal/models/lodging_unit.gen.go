// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package models

import (
	"time"

	"gorm.io/gorm"
)

const TableNameLodgingUnit = "lodging_unit"

// LodgingUnit mapped from table <lodging_unit>
type LodgingUnit struct {
	ID            int64          `gorm:"column:id;primaryKey;autoIncrement:true" json:"id"`
	CompanyID     int64          `gorm:"column:company_id;not null" json:"company_id"`
	BusinessID    int64          `gorm:"column:business_id;not null" json:"business_id"`
	LodgingTypeID int64          `gorm:"column:lodging_type_id;not null;comment:lodging template type" json:"lodging_type_id"` // lodging template type
	Name          string         `gorm:"column:name;not null;comment:lodging name" json:"name"`                                // lodging name
	CreatedAt     *time.Time     `gorm:"column:created_at;not null;default:CURRENT_TIMESTAMP" json:"created_at"`
	CreatedBy     int64          `gorm:"column:created_by;not null" json:"created_by"`
	UpdatedAt     *time.Time     `gorm:"column:updated_at;not null;default:CURRENT_TIMESTAMP;autoUpdateTime:false" json:"updated_at"`
	UpdatedBy     int64          `gorm:"column:updated_by;not null" json:"updated_by"`
	DeletedAt     gorm.DeletedAt `gorm:"column:deleted_at" json:"deleted_at"`
	DeletedBy     int64          `gorm:"column:deleted_by;not null" json:"deleted_by"`
	CameraID      int64          `gorm:"column:camera_id;not null" json:"camera_id"`
	Sort          int32          `gorm:"column:sort;not null" json:"sort"`
}

// TableName LodgingUnit's table name
func (*LodgingUnit) TableName() string {
	return TableNameLodgingUnit
}
