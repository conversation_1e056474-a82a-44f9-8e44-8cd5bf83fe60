package models

import (
	"time"

	"github.com/shopspring/decimal"
	"gorm.io/gorm"

	offeringpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"
)

const TableNameGroupClassInstance = "group_class_instance"

type GroupClassInstance struct {
	ID           int64                                     `gorm:"column:id;primaryKey;autoIncrement:true"`
	CreatedAt    *time.Time                                `gorm:"column:created_at"`
	UpdatedAt    *time.Time                                `gorm:"column:updated_at"`
	DeletedAt    gorm.DeletedAt                            `gorm:"column:deleted_at"`
	CompanyID    int64                                     `gorm:"column:company_id"`
	BusinessID   int64                                     `gorm:"column:business_id"`
	ServiceID    int64                                     `gorm:"column:service_id"`
	Name         string                                    `gorm:"column:name"`
	StaffID      int64                                     `gorm:"column:staff_id"`
	Price        decimal.Decimal                           `gorm:"column:price"`
	TaxID        int64                                     `gorm:"column:tax_id"`
	CurrencyCode string                                    `gorm:"column:currency_code"`
	StartTime    *time.Time                                `gorm:"column:start_time"`
	TimeZone     string                                    `gorm:"column:time_zone"`
	Capacity     int64                                     `gorm:"column:capacity"`
	Occurrence   *offeringpb.GroupClassInstance_Occurrence `gorm:"column:occurrence;serializer:proto_json"`
	Status       offeringpb.GroupClassInstance_Status      `gorm:"column:status;serializer:proto_enum"`
}

func (*GroupClassInstance) TableName() string {
	return TableNameGroupClassInstance
}

type GroupClassInstanceCount struct {
	Status    offeringpb.GroupClassInstance_Status `gorm:"column:status;serializer:proto_enum"`
	ServiceID int64                                `gorm:"column:service_id"`
	Count     int64                                `gorm:"column:count"`
}
