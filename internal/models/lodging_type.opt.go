package models

import (
	"time"

	"github.com/lib/pq"
	"gorm.io/gorm"

	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"
)

type LodgingTypeWhereOpt struct {
	ID        *int64  `gorm:"column:id;"`
	IDIn      []int64 `gorm:"column:id;query_expr:in"`
	CompanyID *int64  `gorm:"column:company_id;"`
}

type LodgingTypeUpdateOpt struct {
	Description        *string             `gorm:"column:description;"`
	Name               *string             `gorm:"column:name;"`
	Photo              *[]string           `gorm:"column:photo;"`
	MaxPetNum          *int32              `gorm:"column:max_pet_num;"`
	UpdatedAt          *time.Time          `gorm:"column:updated_at;"`
	UpdatedBy          *int64              `gorm:"column:updated_by;"`
	DeletedAt          *gorm.DeletedAt     `gorm:"column:deleted_at;"`
	DeletedBy          *int64              `gorm:"column:deleted_by;"`
	AllowedPetSizeList *pq.Int64Array      `gorm:"column:allowed_pet_size_list;"`
	PetSizeFilter      *bool               `gorm:"column:pet_size_filter;"`
	Type               *v1.LodgingUnitType `gorm:"column:type;"`
	Sort               *int32              `gorm:"column:sort;"`
}
