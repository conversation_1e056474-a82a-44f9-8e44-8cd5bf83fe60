package _interface

import (
	"github.com/MoeGolibrary/moego-svc-offering/internal/do"
)

// goverter:converter
// goverter:name LodgingTypeConverter
// goverter:output:file ../impl/lodging_type_impl.go
// goverter:output:package impl
// goverter:useZeroValueOnPointerInconsistency
// goverter:matchIgnoreCase
// goverter:ignoreUnexported
// goverter:enum:unknown @ignore
type LodgingTypeConverter interface {
	// goverter:map Type Type
	ConvertUpdateByIdToUpdateOpt(source *do.LodgingTypeUpdateByIDOpt) *do.LodgingTypeUpdateOpt
}

//go:generate go run github.com/jmattheis/goverter/cmd/goverter gen ./
