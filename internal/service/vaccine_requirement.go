package service

import (
	"context"

	"google.golang.org/protobuf/proto"
	"gorm.io/gorm"

	offeringpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"
	offeringsvcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/offering/v1"
	utilsV2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v2"

	"github.com/MoeGolibrary/moego-svc-offering/internal/do"
	"github.com/MoeGolibrary/moego-svc-offering/internal/models"
	"github.com/MoeGolibrary/moego-svc-offering/internal/repository"
	"github.com/MoeGolibrary/moego-svc-offering/internal/resource"
)

type VaccineServiceRequirementService interface {
	// UpdateVaccineRequirementsForServiceItem updates vaccine requirements for a service item
	UpdateVaccineRequirementsForServiceItem(ctx context.Context, companyID int64, serviceItemType offeringpb.ServiceItemType, vaccineIDs []int64) error
	// UpdateServiceRequirementsForVaccine updates service requirements for a vaccine
	UpdateServiceRequirementsForVaccine(ctx context.Context, companyID int64, vaccineID int64, serviceItemTypes []offeringpb.ServiceItemType) error
	// ListVaccineRequirements lists vaccine requirements by filters
	ListVaccineRequirements(ctx context.Context, companyID int64, filters *offeringsvcpb.ListServiceVaccineRequirementsRequest_Filters, paginationRequest *utilsV2.PaginationRequest) (int64, []*do.VaccineRequirement, error)
}

type vaccineServiceRequirementService struct {
	vaccineRequirementRepository repository.VaccineRequirementRepository
	db                           *gorm.DB
}

func (v vaccineServiceRequirementService) ListVaccineRequirements(ctx context.Context, companyID int64, filters *offeringsvcpb.ListServiceVaccineRequirementsRequest_Filters, paginationRequest *utilsV2.PaginationRequest) (int64, []*do.VaccineRequirement, error) {
	whereOpt := &models.VaccineRequirementWhereOpt{
		CompanyId: companyID,
	}
	if filters != nil {
		if filters.ServiceItemTypes != nil {
			whereOpt.ServiceItemTypes = filters.GetServiceItemTypes()
		}
		if filters.VaccineIds != nil {
			whereOpt.VaccineIDs = filters.GetVaccineIds()
		}
	}
	total, records, err := v.vaccineRequirementRepository.ListVaccineRequirements(ctx, nil, whereOpt, paginationRequest)
	if err != nil {
		return 0, nil, err
	}

	vaccineRequirements := make([]*do.VaccineRequirement, 0, len(records))
	for _, record := range records {
		vaccineRequirements = append(vaccineRequirements, &do.VaccineRequirement{
			Id:              record.ID,
			ServiceItemType: record.ServiceItemType,
			VaccineID:       record.VaccineID,
		})
	}

	return total, vaccineRequirements, nil
}

func (v vaccineServiceRequirementService) UpdateVaccineRequirementsForServiceItem(ctx context.Context, companyID int64, serviceItemType offeringpb.ServiceItemType, vaccineIDs []int64) error {
	records := make([]*models.VaccineRequirement, 0, len(vaccineIDs))
	for _, vaccineID := range vaccineIDs {
		records = append(records, &models.VaccineRequirement{
			CompanyID:       companyID,
			ServiceItemType: serviceItemType,
			VaccineID:       vaccineID,
		})
	}

	return v.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		if err := v.vaccineRequirementRepository.RemoveVaccineRequirement(ctx, tx, &models.VaccineRequirementDeleteOpt{
			CompanyId:       companyID,
			ServiceItemType: serviceItemType.Enum(),
		}); err != nil {
			return err
		}

		if err := v.vaccineRequirementRepository.BatchCreateVaccineRequirement(ctx, tx, records); err != nil {
			return err
		}

		return nil
	})
}

func (v vaccineServiceRequirementService) UpdateServiceRequirementsForVaccine(ctx context.Context, companyID int64, vaccineID int64, serviceItemTypes []offeringpb.ServiceItemType) error {
	records := make([]*models.VaccineRequirement, 0, len(serviceItemTypes))
	for _, serviceItemType := range serviceItemTypes {
		records = append(records, &models.VaccineRequirement{
			CompanyID:       companyID,
			ServiceItemType: serviceItemType,
			VaccineID:       vaccineID,
		})
	}
	return v.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		if err := v.vaccineRequirementRepository.RemoveVaccineRequirement(ctx, tx, &models.VaccineRequirementDeleteOpt{
			CompanyId: companyID,
			VaccineID: proto.Int64(vaccineID),
		}); err != nil {
			return err
		}

		if err := v.vaccineRequirementRepository.BatchCreateVaccineRequirement(ctx, tx, records); err != nil {
			return err
		}

		return nil
	})
}

func NewVaccineServiceRequirementService(vaccineRequirementRepository repository.VaccineRequirementRepository) VaccineServiceRequirementService {
	return &vaccineServiceRequirementService{
		vaccineRequirementRepository: vaccineRequirementRepository,
		db:                           resource.GetOfferingDB(),
	}
}
