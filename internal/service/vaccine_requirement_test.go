package service

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"
	"google.golang.org/protobuf/proto"

	"github.com/MoeGolibrary/go-lib/gorm"
	offeringpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"
	offeringsvcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/offering/v1"
	utilsV2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v2"

	"github.com/MoeGolibrary/moego-svc-offering/internal/do"
	"github.com/MoeGolibrary/moego-svc-offering/internal/mocks"
	"github.com/MoeGolibrary/moego-svc-offering/internal/models"
)

func TestVaccineServiceRequirementService_ListVaccineRequirements(t *testing.T) {
	cid := int64(1)
	serviceItemTypes := []offeringpb.ServiceItemType{offeringpb.ServiceItemType_BOARDING, offeringpb.ServiceItemType_EVALUATION}
	vaccineIds := []int64{11, 22, 33}

	ctrl := gomock.NewController(t)
	mockVaccineRequirementRepo := mocks.NewMockVaccineRequirementRepository(ctrl)
	mockVaccineRequirementRepo.EXPECT().ListVaccineRequirements(context.Background(), nil, &models.VaccineRequirementWhereOpt{
		CompanyId:        cid,
		ServiceItemTypes: serviceItemTypes,
		VaccineIDs:       vaccineIds,
	}, &utilsV2.PaginationRequest{
		PageSize: proto.Int32(15),
		PageNum:  proto.Int32(1),
	}).Return(int64(20), []*models.VaccineRequirement{
		{
			ID:              1,
			ServiceItemType: offeringpb.ServiceItemType_BOARDING,
			VaccineID:       11,
		},
		{
			ID:              2,
			ServiceItemType: offeringpb.ServiceItemType_EVALUATION,
			VaccineID:       22,
		},
	}, nil)

	expected := []*do.VaccineRequirement{
		{
			Id:              1,
			ServiceItemType: offeringpb.ServiceItemType_BOARDING,
			VaccineID:       11,
		},
		{
			Id:              2,
			ServiceItemType: offeringpb.ServiceItemType_EVALUATION,
			VaccineID:       22,
		},
	}

	svc := NewVaccineServiceRequirementService(mockVaccineRequirementRepo)
	total, vaccineRequirements, err := svc.ListVaccineRequirements(context.Background(), cid, &offeringsvcpb.ListServiceVaccineRequirementsRequest_Filters{
		ServiceItemTypes: serviceItemTypes,
		VaccineIds:       vaccineIds,
	}, &utilsV2.PaginationRequest{
		PageSize: proto.Int32(15),
		PageNum:  proto.Int32(1),
	})
	assert.NoError(t, err)
	assert.Equal(t, int64(20), total)
	assert.Equal(t, expected, vaccineRequirements)
}

func TestVaccineServiceRequirementService_UpdateVaccineRequirementsForServiceItem(t *testing.T) {
	cid := int64(1)
	serviceItemType := offeringpb.ServiceItemType_BOARDING
	vaccineIds := []int64{11, 22, 33}

	mockDB, sqlMock := gorm.MockPostgres(t)
	sqlMock.ExpectBegin()
	sqlMock.ExpectCommit()

	ctrl := gomock.NewController(t)
	mockVaccineRequirementRepo := mocks.NewMockVaccineRequirementRepository(ctrl)
	mockVaccineRequirementRepo.EXPECT().RemoveVaccineRequirement(gomock.Any(), gomock.Any(), &models.VaccineRequirementDeleteOpt{
		CompanyId:       cid,
		ServiceItemType: serviceItemType.Enum(),
	}).Return(nil)
	mockVaccineRequirementRepo.EXPECT().BatchCreateVaccineRequirement(context.Background(), gomock.Any(), []*models.VaccineRequirement{
		{
			CompanyID:       cid,
			ServiceItemType: serviceItemType,
			VaccineID:       11,
		},
		{
			CompanyID:       cid,
			ServiceItemType: serviceItemType,
			VaccineID:       22,
		},
		{
			CompanyID:       cid,
			ServiceItemType: serviceItemType,
			VaccineID:       33,
		},
	}).Return(nil)

	svc := &vaccineServiceRequirementService{
		vaccineRequirementRepository: mockVaccineRequirementRepo,
		db:                           mockDB,
	}
	err := svc.UpdateVaccineRequirementsForServiceItem(context.Background(), cid, serviceItemType, vaccineIds)
	assert.NoError(t, err)
}

func TestVaccineServiceRequirementService_UpdateServiceRequirementsForVaccine(t *testing.T) {
	cid := int64(1)
	vaccineID := int64(11)
	serviceItemTypes := []offeringpb.ServiceItemType{offeringpb.ServiceItemType_BOARDING, offeringpb.ServiceItemType_EVALUATION}

	mockDB, sqlMock := gorm.MockPostgres(t)
	sqlMock.ExpectBegin()
	sqlMock.ExpectCommit()

	ctrl := gomock.NewController(t)
	mockVaccineRequirementRepo := mocks.NewMockVaccineRequirementRepository(ctrl)
	mockVaccineRequirementRepo.EXPECT().RemoveVaccineRequirement(gomock.Any(), gomock.Any(), &models.VaccineRequirementDeleteOpt{
		CompanyId: cid,
		VaccineID: proto.Int64(vaccineID),
	}).Return(nil)

	mockVaccineRequirementRepo.EXPECT().BatchCreateVaccineRequirement(context.Background(), gomock.Any(), []*models.VaccineRequirement{
		{
			CompanyID:       cid,
			ServiceItemType: offeringpb.ServiceItemType_BOARDING,
			VaccineID:       vaccineID,
		},
		{
			CompanyID:       cid,
			ServiceItemType: offeringpb.ServiceItemType_EVALUATION,
			VaccineID:       vaccineID,
		},
	}).Return(nil)

	svc := &vaccineServiceRequirementService{
		vaccineRequirementRepository: mockVaccineRequirementRepo,
		db:                           mockDB,
	}
	err := svc.UpdateServiceRequirementsForVaccine(context.Background(), cid, vaccineID, serviceItemTypes)
	assert.NoError(t, err)
}
