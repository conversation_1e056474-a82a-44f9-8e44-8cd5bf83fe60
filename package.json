{"name": "moego-api-metadata", "version": "1.0.0", "main": "index.js", "repository": "**************:MoeGolibrary/moego-api-metadata.git", "author": "junbao <<EMAIL>>", "private": true, "files": [], "license": "UNLICENSED", "dependencies": {"@commitlint/cli": "^17.0.3", "@commitlint/config-angular": "^17.0.3", "git-branch-is": "^4.0.0", "husky": "^8.0.1", "prettier": "^2.7.1", "prettier-plugin-java": "^1.6.2", "pretty-quick": "^3.1.3"}, "scripts": {"prepare": "husky install"}, "devDependencies": {"@prettier/plugin-xml": "^2.2.0"}}