// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/client/grooming/v1/appointment_api.proto

package groomingapipb

import (
	v16 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/agreement/v1"
	v11 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/business/v1"
	v14 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/business_customer/v1"
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/grooming/v1"
	v13 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/message/v1"
	v12 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/online_booking/v1"
	v15 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/payment/v1"
	v2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v2"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// appointment list request
type AppointmentListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// appointment type
	Type v1.AppointmentType `protobuf:"varint,1,opt,name=type,proto3,enum=moego.models.grooming.v1.AppointmentType" json:"type,omitempty"`
	// pagination
	Pagination *v2.PaginationRequest `protobuf:"bytes,2,opt,name=pagination,proto3,oneof" json:"pagination,omitempty"`
	// sort by
	Sorts []*v1.AppointmentSortDef `protobuf:"bytes,3,rep,name=sorts,proto3" json:"sorts,omitempty"`
}

func (x *AppointmentListRequest) Reset() {
	*x = AppointmentListRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_grooming_v1_appointment_api_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AppointmentListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AppointmentListRequest) ProtoMessage() {}

func (x *AppointmentListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_grooming_v1_appointment_api_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AppointmentListRequest.ProtoReflect.Descriptor instead.
func (*AppointmentListRequest) Descriptor() ([]byte, []int) {
	return file_moego_client_grooming_v1_appointment_api_proto_rawDescGZIP(), []int{0}
}

func (x *AppointmentListRequest) GetType() v1.AppointmentType {
	if x != nil {
		return x.Type
	}
	return v1.AppointmentType(0)
}

func (x *AppointmentListRequest) GetPagination() *v2.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *AppointmentListRequest) GetSorts() []*v1.AppointmentSortDef {
	if x != nil {
		return x.Sorts
	}
	return nil
}

// appointment list response
type AppointmentListResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// appointment list view
	Appointments []*v1.AppointmentModelClientListView `protobuf:"bytes,1,rep,name=appointments,proto3" json:"appointments,omitempty"`
	// business view
	Businesses []*v11.BusinessModelClientListView `protobuf:"bytes,2,rep,name=businesses,proto3" json:"businesses,omitempty"`
	// online booking config
	ObConfigs []*v12.BusinessOBConfigModelClientListView `protobuf:"bytes,3,rep,name=ob_configs,json=obConfigs,proto3" json:"ob_configs,omitempty"`
	// pagination
	Pagination *v2.PaginationResponse `protobuf:"bytes,4,opt,name=pagination,proto3,oneof" json:"pagination,omitempty"`
	// business mobile arrival window view
	ArrivalWindows []*v11.ArrivalWindowModelPublicView `protobuf:"bytes,5,rep,name=arrival_windows,json=arrivalWindows,proto3" json:"arrival_windows,omitempty"`
}

func (x *AppointmentListResponse) Reset() {
	*x = AppointmentListResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_grooming_v1_appointment_api_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AppointmentListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AppointmentListResponse) ProtoMessage() {}

func (x *AppointmentListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_grooming_v1_appointment_api_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AppointmentListResponse.ProtoReflect.Descriptor instead.
func (*AppointmentListResponse) Descriptor() ([]byte, []int) {
	return file_moego_client_grooming_v1_appointment_api_proto_rawDescGZIP(), []int{1}
}

func (x *AppointmentListResponse) GetAppointments() []*v1.AppointmentModelClientListView {
	if x != nil {
		return x.Appointments
	}
	return nil
}

func (x *AppointmentListResponse) GetBusinesses() []*v11.BusinessModelClientListView {
	if x != nil {
		return x.Businesses
	}
	return nil
}

func (x *AppointmentListResponse) GetObConfigs() []*v12.BusinessOBConfigModelClientListView {
	if x != nil {
		return x.ObConfigs
	}
	return nil
}

func (x *AppointmentListResponse) GetPagination() *v2.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *AppointmentListResponse) GetArrivalWindows() []*v11.ArrivalWindowModelPublicView {
	if x != nil {
		return x.ArrivalWindows
	}
	return nil
}

// appointment detail request
type AppointmentDetailRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// appointment id
	AppointmentId int64 `protobuf:"varint,1,opt,name=appointment_id,json=appointmentId,proto3" json:"appointment_id,omitempty"`
}

func (x *AppointmentDetailRequest) Reset() {
	*x = AppointmentDetailRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_grooming_v1_appointment_api_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AppointmentDetailRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AppointmentDetailRequest) ProtoMessage() {}

func (x *AppointmentDetailRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_grooming_v1_appointment_api_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AppointmentDetailRequest.ProtoReflect.Descriptor instead.
func (*AppointmentDetailRequest) Descriptor() ([]byte, []int) {
	return file_moego_client_grooming_v1_appointment_api_proto_rawDescGZIP(), []int{2}
}

func (x *AppointmentDetailRequest) GetAppointmentId() int64 {
	if x != nil {
		return x.AppointmentId
	}
	return 0
}

// appointment detail response
type AppointmentDetailResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// appointment detail view
	Appointment *v1.AppointmentModelClientView `protobuf:"bytes,1,opt,name=appointment,proto3" json:"appointment,omitempty"`
	// pet detail view
	AppointmentItems []*v1.PetDetailModelClientView `protobuf:"bytes,2,rep,name=appointment_items,json=appointmentItems,proto3" json:"appointment_items,omitempty"`
	// business view
	Business *v11.BusinessModelClientView `protobuf:"bytes,3,opt,name=business,proto3" json:"business,omitempty"`
	// business twilio number
	TwilioNumber *v13.BusinessTwilioNumberView `protobuf:"bytes,4,opt,name=twilio_number,json=twilioNumber,proto3" json:"twilio_number,omitempty"`
	// service view
	Services []*v1.ServiceModelClientView `protobuf:"bytes,5,rep,name=services,proto3" json:"services,omitempty"`
	// business pet view
	BusinessPets []*v14.BusinessCustomerPetModelClientView `protobuf:"bytes,6,rep,name=business_pets,json=businessPets,proto3" json:"business_pets,omitempty"`
	// staff view
	Staffs []*v11.StaffModelClientView `protobuf:"bytes,7,rep,name=staffs,proto3" json:"staffs,omitempty"`
	// payment method view
	PaymentMethod *v15.PaymentMethodModelPublicView `protobuf:"bytes,8,opt,name=payment_method,json=paymentMethod,proto3" json:"payment_method,omitempty"`
	// ob deposit view
	ObDeposit *v15.BookOnlineDepositModelClientView `protobuf:"bytes,9,opt,name=ob_deposit,json=obDeposit,proto3" json:"ob_deposit,omitempty"`
	// ob config view
	ObConfig *v12.BusinessOBConfigModelClientView `protobuf:"bytes,10,opt,name=ob_config,json=obConfig,proto3" json:"ob_config,omitempty"`
	// business customer view
	Customer *v14.BusinessCustomerModelClientView `protobuf:"bytes,11,opt,name=customer,proto3" json:"customer,omitempty"`
	// business customer primary address view
	PrimaryAddress *v14.BusinessCustomerAddressModelClientView `protobuf:"bytes,12,opt,name=primary_address,json=primaryAddress,proto3" json:"primary_address,omitempty"`
	// business mobile arrival window view
	ArrivalWindow *v11.ArrivalWindowModelPublicView `protobuf:"bytes,13,opt,name=arrival_window,json=arrivalWindow,proto3" json:"arrival_window,omitempty"`
	// signed agreements
	SignedAgreements []*v16.AgreementRecordSimpleView `protobuf:"bytes,14,rep,name=signed_agreements,json=signedAgreements,proto3" json:"signed_agreements,omitempty"`
}

func (x *AppointmentDetailResponse) Reset() {
	*x = AppointmentDetailResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_grooming_v1_appointment_api_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AppointmentDetailResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AppointmentDetailResponse) ProtoMessage() {}

func (x *AppointmentDetailResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_grooming_v1_appointment_api_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AppointmentDetailResponse.ProtoReflect.Descriptor instead.
func (*AppointmentDetailResponse) Descriptor() ([]byte, []int) {
	return file_moego_client_grooming_v1_appointment_api_proto_rawDescGZIP(), []int{3}
}

func (x *AppointmentDetailResponse) GetAppointment() *v1.AppointmentModelClientView {
	if x != nil {
		return x.Appointment
	}
	return nil
}

func (x *AppointmentDetailResponse) GetAppointmentItems() []*v1.PetDetailModelClientView {
	if x != nil {
		return x.AppointmentItems
	}
	return nil
}

func (x *AppointmentDetailResponse) GetBusiness() *v11.BusinessModelClientView {
	if x != nil {
		return x.Business
	}
	return nil
}

func (x *AppointmentDetailResponse) GetTwilioNumber() *v13.BusinessTwilioNumberView {
	if x != nil {
		return x.TwilioNumber
	}
	return nil
}

func (x *AppointmentDetailResponse) GetServices() []*v1.ServiceModelClientView {
	if x != nil {
		return x.Services
	}
	return nil
}

func (x *AppointmentDetailResponse) GetBusinessPets() []*v14.BusinessCustomerPetModelClientView {
	if x != nil {
		return x.BusinessPets
	}
	return nil
}

func (x *AppointmentDetailResponse) GetStaffs() []*v11.StaffModelClientView {
	if x != nil {
		return x.Staffs
	}
	return nil
}

func (x *AppointmentDetailResponse) GetPaymentMethod() *v15.PaymentMethodModelPublicView {
	if x != nil {
		return x.PaymentMethod
	}
	return nil
}

func (x *AppointmentDetailResponse) GetObDeposit() *v15.BookOnlineDepositModelClientView {
	if x != nil {
		return x.ObDeposit
	}
	return nil
}

func (x *AppointmentDetailResponse) GetObConfig() *v12.BusinessOBConfigModelClientView {
	if x != nil {
		return x.ObConfig
	}
	return nil
}

func (x *AppointmentDetailResponse) GetCustomer() *v14.BusinessCustomerModelClientView {
	if x != nil {
		return x.Customer
	}
	return nil
}

func (x *AppointmentDetailResponse) GetPrimaryAddress() *v14.BusinessCustomerAddressModelClientView {
	if x != nil {
		return x.PrimaryAddress
	}
	return nil
}

func (x *AppointmentDetailResponse) GetArrivalWindow() *v11.ArrivalWindowModelPublicView {
	if x != nil {
		return x.ArrivalWindow
	}
	return nil
}

func (x *AppointmentDetailResponse) GetSignedAgreements() []*v16.AgreementRecordSimpleView {
	if x != nil {
		return x.SignedAgreements
	}
	return nil
}

// create booking request
type CreateBookingRequestRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// selected business id
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// pet selected service list
	SelectedPetServices []*v12.SelectedPetServiceDef `protobuf:"bytes,2,rep,name=selected_pet_services,json=selectedPetServices,proto3" json:"selected_pet_services,omitempty"`
	// selected staff id
	StaffId int64 `protobuf:"varint,3,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
	// selected appointment date
	AppointmentDate string `protobuf:"bytes,4,opt,name=appointment_date,json=appointmentDate,proto3" json:"appointment_date,omitempty"`
	// selected appointment start time, in minutes
	AppointmentStartTime int32 `protobuf:"varint,5,opt,name=appointment_start_time,json=appointmentStartTime,proto3" json:"appointment_start_time,omitempty"`
	// additional note
	AdditionalNote string `protobuf:"bytes,7,opt,name=additional_note,json=additionalNote,proto3" json:"additional_note,omitempty"`
	// sign agreements
	SignAgreements []*v16.AgreementRecordDef `protobuf:"bytes,6,rep,name=sign_agreements,json=signAgreements,proto3" json:"sign_agreements,omitempty"`
	// payment
	//
	// Types that are assignable to Payment:
	//
	//	*CreateBookingRequestRequest_CardOnFile
	//	*CreateBookingRequestRequest_Prepay
	//	*CreateBookingRequestRequest_PreAuth
	Payment isCreateBookingRequestRequest_Payment `protobuf_oneof:"payment"`
}

func (x *CreateBookingRequestRequest) Reset() {
	*x = CreateBookingRequestRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_grooming_v1_appointment_api_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateBookingRequestRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateBookingRequestRequest) ProtoMessage() {}

func (x *CreateBookingRequestRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_grooming_v1_appointment_api_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateBookingRequestRequest.ProtoReflect.Descriptor instead.
func (*CreateBookingRequestRequest) Descriptor() ([]byte, []int) {
	return file_moego_client_grooming_v1_appointment_api_proto_rawDescGZIP(), []int{4}
}

func (x *CreateBookingRequestRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *CreateBookingRequestRequest) GetSelectedPetServices() []*v12.SelectedPetServiceDef {
	if x != nil {
		return x.SelectedPetServices
	}
	return nil
}

func (x *CreateBookingRequestRequest) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

func (x *CreateBookingRequestRequest) GetAppointmentDate() string {
	if x != nil {
		return x.AppointmentDate
	}
	return ""
}

func (x *CreateBookingRequestRequest) GetAppointmentStartTime() int32 {
	if x != nil {
		return x.AppointmentStartTime
	}
	return 0
}

func (x *CreateBookingRequestRequest) GetAdditionalNote() string {
	if x != nil {
		return x.AdditionalNote
	}
	return ""
}

func (x *CreateBookingRequestRequest) GetSignAgreements() []*v16.AgreementRecordDef {
	if x != nil {
		return x.SignAgreements
	}
	return nil
}

func (m *CreateBookingRequestRequest) GetPayment() isCreateBookingRequestRequest_Payment {
	if m != nil {
		return m.Payment
	}
	return nil
}

func (x *CreateBookingRequestRequest) GetCardOnFile() *v12.CardOnFileDef {
	if x, ok := x.GetPayment().(*CreateBookingRequestRequest_CardOnFile); ok {
		return x.CardOnFile
	}
	return nil
}

func (x *CreateBookingRequestRequest) GetPrepay() *v12.PrepayDef {
	if x, ok := x.GetPayment().(*CreateBookingRequestRequest_Prepay); ok {
		return x.Prepay
	}
	return nil
}

func (x *CreateBookingRequestRequest) GetPreAuth() *v12.PreAuthDef {
	if x, ok := x.GetPayment().(*CreateBookingRequestRequest_PreAuth); ok {
		return x.PreAuth
	}
	return nil
}

type isCreateBookingRequestRequest_Payment interface {
	isCreateBookingRequestRequest_Payment()
}

type CreateBookingRequestRequest_CardOnFile struct {
	// card on file
	CardOnFile *v12.CardOnFileDef `protobuf:"bytes,11,opt,name=card_on_file,json=cardOnFile,proto3,oneof"`
}

type CreateBookingRequestRequest_Prepay struct {
	// prepay
	Prepay *v12.PrepayDef `protobuf:"bytes,12,opt,name=prepay,proto3,oneof"`
}

type CreateBookingRequestRequest_PreAuth struct {
	// pre-auth
	PreAuth *v12.PreAuthDef `protobuf:"bytes,13,opt,name=pre_auth,json=preAuth,proto3,oneof"`
}

func (*CreateBookingRequestRequest_CardOnFile) isCreateBookingRequestRequest_Payment() {}

func (*CreateBookingRequestRequest_Prepay) isCreateBookingRequestRequest_Payment() {}

func (*CreateBookingRequestRequest_PreAuth) isCreateBookingRequestRequest_Payment() {}

// booking response
type CreateBookingRequestResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// auto accept flag
	IsAutoAccept bool `protobuf:"varint,1,opt,name=is_auto_accept,json=isAutoAccept,proto3" json:"is_auto_accept,omitempty"`
}

func (x *CreateBookingRequestResponse) Reset() {
	*x = CreateBookingRequestResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_grooming_v1_appointment_api_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateBookingRequestResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateBookingRequestResponse) ProtoMessage() {}

func (x *CreateBookingRequestResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_grooming_v1_appointment_api_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateBookingRequestResponse.ProtoReflect.Descriptor instead.
func (*CreateBookingRequestResponse) Descriptor() ([]byte, []int) {
	return file_moego_client_grooming_v1_appointment_api_proto_rawDescGZIP(), []int{5}
}

func (x *CreateBookingRequestResponse) GetIsAutoAccept() bool {
	if x != nil {
		return x.IsAutoAccept
	}
	return false
}

// pre submit online booking request response
type PreSubmitBookingRequestResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// result
	Result bool `protobuf:"varint,1,opt,name=result,proto3" json:"result,omitempty"`
}

func (x *PreSubmitBookingRequestResponse) Reset() {
	*x = PreSubmitBookingRequestResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_grooming_v1_appointment_api_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PreSubmitBookingRequestResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PreSubmitBookingRequestResponse) ProtoMessage() {}

func (x *PreSubmitBookingRequestResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_grooming_v1_appointment_api_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PreSubmitBookingRequestResponse.ProtoReflect.Descriptor instead.
func (*PreSubmitBookingRequestResponse) Descriptor() ([]byte, []int) {
	return file_moego_client_grooming_v1_appointment_api_proto_rawDescGZIP(), []int{6}
}

func (x *PreSubmitBookingRequestResponse) GetResult() bool {
	if x != nil {
		return x.Result
	}
	return false
}

// update booking request request
type UpdateBookingRequestRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// booking request
	//
	// Types that are assignable to BookingRequest:
	//
	//	*UpdateBookingRequestRequest_Reschedule
	//	*UpdateBookingRequestRequest_Cancel
	BookingRequest isUpdateBookingRequestRequest_BookingRequest `protobuf_oneof:"booking_request"`
}

func (x *UpdateBookingRequestRequest) Reset() {
	*x = UpdateBookingRequestRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_grooming_v1_appointment_api_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateBookingRequestRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateBookingRequestRequest) ProtoMessage() {}

func (x *UpdateBookingRequestRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_grooming_v1_appointment_api_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateBookingRequestRequest.ProtoReflect.Descriptor instead.
func (*UpdateBookingRequestRequest) Descriptor() ([]byte, []int) {
	return file_moego_client_grooming_v1_appointment_api_proto_rawDescGZIP(), []int{7}
}

func (m *UpdateBookingRequestRequest) GetBookingRequest() isUpdateBookingRequestRequest_BookingRequest {
	if m != nil {
		return m.BookingRequest
	}
	return nil
}

func (x *UpdateBookingRequestRequest) GetReschedule() *RescheduleBookingRequest {
	if x, ok := x.GetBookingRequest().(*UpdateBookingRequestRequest_Reschedule); ok {
		return x.Reschedule
	}
	return nil
}

func (x *UpdateBookingRequestRequest) GetCancel() *CancelBookingRequest {
	if x, ok := x.GetBookingRequest().(*UpdateBookingRequestRequest_Cancel); ok {
		return x.Cancel
	}
	return nil
}

type isUpdateBookingRequestRequest_BookingRequest interface {
	isUpdateBookingRequestRequest_BookingRequest()
}

type UpdateBookingRequestRequest_Reschedule struct {
	// reschedule booking request
	Reschedule *RescheduleBookingRequest `protobuf:"bytes,1,opt,name=reschedule,proto3,oneof"`
}

type UpdateBookingRequestRequest_Cancel struct {
	// cancel booking request
	Cancel *CancelBookingRequest `protobuf:"bytes,2,opt,name=cancel,proto3,oneof"`
}

func (*UpdateBookingRequestRequest_Reschedule) isUpdateBookingRequestRequest_BookingRequest() {}

func (*UpdateBookingRequestRequest_Cancel) isUpdateBookingRequestRequest_BookingRequest() {}

// reschedule booking request
type RescheduleBookingRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// appointment id
	AppointmentId int64 `protobuf:"varint,1,opt,name=appointment_id,json=appointmentId,proto3" json:"appointment_id,omitempty"`
	// selected staff id
	StaffId int64 `protobuf:"varint,2,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
	// selected appointment date
	AppointmentDate string `protobuf:"bytes,3,opt,name=appointment_date,json=appointmentDate,proto3" json:"appointment_date,omitempty"`
	// selected appointment start time, in minutes
	AppointmentStartTime int32 `protobuf:"varint,4,opt,name=appointment_start_time,json=appointmentStartTime,proto3" json:"appointment_start_time,omitempty"`
}

func (x *RescheduleBookingRequest) Reset() {
	*x = RescheduleBookingRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_grooming_v1_appointment_api_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RescheduleBookingRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RescheduleBookingRequest) ProtoMessage() {}

func (x *RescheduleBookingRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_grooming_v1_appointment_api_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RescheduleBookingRequest.ProtoReflect.Descriptor instead.
func (*RescheduleBookingRequest) Descriptor() ([]byte, []int) {
	return file_moego_client_grooming_v1_appointment_api_proto_rawDescGZIP(), []int{8}
}

func (x *RescheduleBookingRequest) GetAppointmentId() int64 {
	if x != nil {
		return x.AppointmentId
	}
	return 0
}

func (x *RescheduleBookingRequest) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

func (x *RescheduleBookingRequest) GetAppointmentDate() string {
	if x != nil {
		return x.AppointmentDate
	}
	return ""
}

func (x *RescheduleBookingRequest) GetAppointmentStartTime() int32 {
	if x != nil {
		return x.AppointmentStartTime
	}
	return 0
}

// cancel booking request
type CancelBookingRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// appointment id
	AppointmentId int64 `protobuf:"varint,1,opt,name=appointment_id,json=appointmentId,proto3" json:"appointment_id,omitempty"`
}

func (x *CancelBookingRequest) Reset() {
	*x = CancelBookingRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_grooming_v1_appointment_api_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CancelBookingRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CancelBookingRequest) ProtoMessage() {}

func (x *CancelBookingRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_grooming_v1_appointment_api_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CancelBookingRequest.ProtoReflect.Descriptor instead.
func (*CancelBookingRequest) Descriptor() ([]byte, []int) {
	return file_moego_client_grooming_v1_appointment_api_proto_rawDescGZIP(), []int{9}
}

func (x *CancelBookingRequest) GetAppointmentId() int64 {
	if x != nil {
		return x.AppointmentId
	}
	return 0
}

// update booking request response
type UpdateBookingRequestResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// result
	Result bool `protobuf:"varint,1,opt,name=result,proto3" json:"result,omitempty"`
	// auto accept flag
	IsAutoAccept bool `protobuf:"varint,2,opt,name=is_auto_accept,json=isAutoAccept,proto3" json:"is_auto_accept,omitempty"`
}

func (x *UpdateBookingRequestResponse) Reset() {
	*x = UpdateBookingRequestResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_grooming_v1_appointment_api_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateBookingRequestResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateBookingRequestResponse) ProtoMessage() {}

func (x *UpdateBookingRequestResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_grooming_v1_appointment_api_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateBookingRequestResponse.ProtoReflect.Descriptor instead.
func (*UpdateBookingRequestResponse) Descriptor() ([]byte, []int) {
	return file_moego_client_grooming_v1_appointment_api_proto_rawDescGZIP(), []int{10}
}

func (x *UpdateBookingRequestResponse) GetResult() bool {
	if x != nil {
		return x.Result
	}
	return false
}

func (x *UpdateBookingRequestResponse) GetIsAutoAccept() bool {
	if x != nil {
		return x.IsAutoAccept
	}
	return false
}

// update appointment request
type UpdateAppointmentRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// appointment id
	AppointmentId int64 `protobuf:"varint,1,opt,name=appointment_id,json=appointmentId,proto3" json:"appointment_id,omitempty"`
	// update status
	Status v1.AppointmentStatus `protobuf:"varint,2,opt,name=status,proto3,enum=moego.models.grooming.v1.AppointmentStatus" json:"status,omitempty"`
}

func (x *UpdateAppointmentRequest) Reset() {
	*x = UpdateAppointmentRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_grooming_v1_appointment_api_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateAppointmentRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateAppointmentRequest) ProtoMessage() {}

func (x *UpdateAppointmentRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_grooming_v1_appointment_api_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateAppointmentRequest.ProtoReflect.Descriptor instead.
func (*UpdateAppointmentRequest) Descriptor() ([]byte, []int) {
	return file_moego_client_grooming_v1_appointment_api_proto_rawDescGZIP(), []int{11}
}

func (x *UpdateAppointmentRequest) GetAppointmentId() int64 {
	if x != nil {
		return x.AppointmentId
	}
	return 0
}

func (x *UpdateAppointmentRequest) GetStatus() v1.AppointmentStatus {
	if x != nil {
		return x.Status
	}
	return v1.AppointmentStatus(0)
}

// update appointment response
type UpdateAppointmentResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// result
	Result bool `protobuf:"varint,1,opt,name=result,proto3" json:"result,omitempty"`
}

func (x *UpdateAppointmentResponse) Reset() {
	*x = UpdateAppointmentResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_grooming_v1_appointment_api_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateAppointmentResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateAppointmentResponse) ProtoMessage() {}

func (x *UpdateAppointmentResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_grooming_v1_appointment_api_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateAppointmentResponse.ProtoReflect.Descriptor instead.
func (*UpdateAppointmentResponse) Descriptor() ([]byte, []int) {
	return file_moego_client_grooming_v1_appointment_api_proto_rawDescGZIP(), []int{12}
}

func (x *UpdateAppointmentResponse) GetResult() bool {
	if x != nil {
		return x.Result
	}
	return false
}

// get today appointment list response
type GetTodayAppointmentListResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// appointment list
	Appointments []*AppointmentDetailResponse `protobuf:"bytes,1,rep,name=appointments,proto3" json:"appointments,omitempty"`
}

func (x *GetTodayAppointmentListResponse) Reset() {
	*x = GetTodayAppointmentListResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_grooming_v1_appointment_api_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTodayAppointmentListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTodayAppointmentListResponse) ProtoMessage() {}

func (x *GetTodayAppointmentListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_grooming_v1_appointment_api_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTodayAppointmentListResponse.ProtoReflect.Descriptor instead.
func (*GetTodayAppointmentListResponse) Descriptor() ([]byte, []int) {
	return file_moego_client_grooming_v1_appointment_api_proto_rawDescGZIP(), []int{13}
}

func (x *GetTodayAppointmentListResponse) GetAppointments() []*AppointmentDetailResponse {
	if x != nil {
		return x.Appointments
	}
	return nil
}

var File_moego_client_grooming_v1_appointment_api_proto protoreflect.FileDescriptor

var file_moego_client_grooming_v1_appointment_api_proto_rawDesc = []byte{
	0x0a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2f, 0x67,
	0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x70, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x12, 0x18, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x67,
	0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x1a, 0x1b, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x65, 0x6d, 0x70, 0x74,
	0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x35, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2f,
	0x76, 0x31, 0x2f, 0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x72, 0x65, 0x63,
	0x6f, 0x72, 0x64, 0x5f, 0x64, 0x65, 0x66, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x37,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x61, 0x67, 0x72,
	0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x67, 0x72, 0x65, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x5f, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x34, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x2f, 0x76,
	0x31, 0x2f, 0x61, 0x72, 0x72, 0x69, 0x76, 0x61, 0x6c, 0x5f, 0x77, 0x69, 0x6e, 0x64, 0x6f, 0x77,
	0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x62, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2b, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x62, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x48, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f, 0x62, 0x75,
	0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f,
	0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x40, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x44, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x70, 0x65, 0x74, 0x5f,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2f, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x67, 0x72, 0x6f, 0x6f, 0x6d,
	0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x5f, 0x64, 0x65, 0x66, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x30, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x67, 0x72, 0x6f, 0x6f,
	0x6d, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x31, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x67, 0x72,
	0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x30, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2f, 0x67, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x70, 0x65, 0x74,
	0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2d, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2f, 0x67, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x33, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2f, 0x76, 0x31, 0x2f, 0x62, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x74, 0x77, 0x69, 0x6c, 0x69, 0x6f, 0x5f, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x3e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f,
	0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x5f, 0x6f, 0x62, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x32, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f,
	0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x73, 0x5f, 0x64, 0x65, 0x66, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x32, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x6e, 0x6c, 0x69, 0x6e,
	0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x65, 0x6c,
	0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x64, 0x65, 0x66, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x2c, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x70,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x64, 0x65, 0x70, 0x6f, 0x73, 0x69,
	0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x33,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x70, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f,
	0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x28, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x75, 0x74, 0x69, 0x6c, 0x73,
	0x2f, 0x76, 0x32, 0x2f, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76,
	0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xfc, 0x01, 0x0a, 0x16, 0x41, 0x70, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x47, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x67,
	0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x70, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x82,
	0x01, 0x02, 0x10, 0x01, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x46, 0x0a, 0x0a, 0x70, 0x61,
	0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e,
	0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x48, 0x00, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x88,
	0x01, 0x01, 0x12, 0x42, 0x0a, 0x05, 0x73, 0x6f, 0x72, 0x74, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x2c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x67, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x70, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x6f, 0x72, 0x74, 0x44, 0x65, 0x66, 0x52,
	0x05, 0x73, 0x6f, 0x72, 0x74, 0x73, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x70, 0x61, 0x67, 0x69, 0x6e,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0xeb, 0x03, 0x0a, 0x17, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x5c, 0x0a, 0x0c, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x38, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x67, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x2e,
	0x76, 0x31, 0x2e, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x6f,
	0x64, 0x65, 0x6c, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x56, 0x69, 0x65,
	0x77, 0x52, 0x0c, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x12,
	0x55, 0x0a, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x65, 0x73, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x42,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x43, 0x6c, 0x69, 0x65,
	0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x56, 0x69, 0x65, 0x77, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x65, 0x73, 0x12, 0x62, 0x0a, 0x0a, 0x6f, 0x62, 0x5f, 0x63, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x43, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65,
	0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x4f, 0x42, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x4d, 0x6f, 0x64, 0x65,
	0x6c, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x56, 0x69, 0x65, 0x77, 0x52,
	0x09, 0x6f, 0x62, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x73, 0x12, 0x47, 0x0a, 0x0a, 0x70, 0x61,
	0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e,
	0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x48, 0x00, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x88, 0x01, 0x01, 0x12, 0x5f, 0x0a, 0x0f, 0x61, 0x72, 0x72, 0x69, 0x76, 0x61, 0x6c, 0x5f, 0x77,
	0x69, 0x6e, 0x64, 0x6f, 0x77, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x36, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x72, 0x72, 0x69, 0x76, 0x61, 0x6c, 0x57,
	0x69, 0x6e, 0x64, 0x6f, 0x77, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x63,
	0x56, 0x69, 0x65, 0x77, 0x52, 0x0e, 0x61, 0x72, 0x72, 0x69, 0x76, 0x61, 0x6c, 0x57, 0x69, 0x6e,
	0x64, 0x6f, 0x77, 0x73, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x22, 0x4a, 0x0a, 0x18, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x2e, 0x0a, 0x0e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00,
	0x52, 0x0d, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x22,
	0xa9, 0x0a, 0x0a, 0x19, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x56, 0x0a,
	0x0b, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x67, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x70,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x43, 0x6c,
	0x69, 0x65, 0x6e, 0x74, 0x56, 0x69, 0x65, 0x77, 0x52, 0x0b, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x5f, 0x0a, 0x11, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x32, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x67, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x65, 0x74, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74,
	0x56, 0x69, 0x65, 0x77, 0x52, 0x10, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x12, 0x4d, 0x0a, 0x08, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x2e, 0x76, 0x31, 0x2e, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x4d, 0x6f, 0x64, 0x65,
	0x6c, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x56, 0x69, 0x65, 0x77, 0x52, 0x08, 0x62, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x12, 0x56, 0x0a, 0x0d, 0x74, 0x77, 0x69, 0x6c, 0x69, 0x6f, 0x5f,
	0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x31, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x54,
	0x77, 0x69, 0x6c, 0x69, 0x6f, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x56, 0x69, 0x65, 0x77, 0x52,
	0x0c, 0x74, 0x77, 0x69, 0x6c, 0x69, 0x6f, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x4c, 0x0a,
	0x08, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x30, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x67,
	0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x56, 0x69, 0x65,
	0x77, 0x52, 0x08, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x12, 0x6a, 0x0a, 0x0d, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x70, 0x65, 0x74, 0x73, 0x18, 0x06, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x45, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x43,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x50, 0x65, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x43,
	0x6c, 0x69, 0x65, 0x6e, 0x74, 0x56, 0x69, 0x65, 0x77, 0x52, 0x0c, 0x62, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x50, 0x65, 0x74, 0x73, 0x12, 0x46, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x66, 0x66,
	0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x2e,
	0x76, 0x31, 0x2e, 0x53, 0x74, 0x61, 0x66, 0x66, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x43, 0x6c, 0x69,
	0x65, 0x6e, 0x74, 0x56, 0x69, 0x65, 0x77, 0x52, 0x06, 0x73, 0x74, 0x61, 0x66, 0x66, 0x73, 0x12,
	0x5c, 0x0a, 0x0e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x6d, 0x65, 0x74, 0x68, 0x6f,
	0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76,
	0x31, 0x2e, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x4d,
	0x6f, 0x64, 0x65, 0x6c, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x56, 0x69, 0x65, 0x77, 0x52, 0x0d,
	0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x12, 0x58, 0x0a,
	0x0a, 0x6f, 0x62, 0x5f, 0x64, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x39, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x6f, 0x6f, 0x6b,
	0x4f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x4d, 0x6f, 0x64,
	0x65, 0x6c, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x56, 0x69, 0x65, 0x77, 0x52, 0x09, 0x6f, 0x62,
	0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x12, 0x5c, 0x0a, 0x09, 0x6f, 0x62, 0x5f, 0x63, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3f, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65,
	0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x4f, 0x42, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x4d, 0x6f, 0x64, 0x65,
	0x6c, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x56, 0x69, 0x65, 0x77, 0x52, 0x08, 0x6f, 0x62, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x5e, 0x0a, 0x08, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x42, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f,
	0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x4d, 0x6f, 0x64, 0x65,
	0x6c, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x56, 0x69, 0x65, 0x77, 0x52, 0x08, 0x63, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x12, 0x72, 0x0a, 0x0f, 0x70, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79,
	0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x49,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62, 0x75,
	0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e,
	0x76, 0x31, 0x2e, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x43, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x43,
	0x6c, 0x69, 0x65, 0x6e, 0x74, 0x56, 0x69, 0x65, 0x77, 0x52, 0x0e, 0x70, 0x72, 0x69, 0x6d, 0x61,
	0x72, 0x79, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x5d, 0x0a, 0x0e, 0x61, 0x72, 0x72,
	0x69, 0x76, 0x61, 0x6c, 0x5f, 0x77, 0x69, 0x6e, 0x64, 0x6f, 0x77, 0x18, 0x0d, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x36, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x72, 0x72,
	0x69, 0x76, 0x61, 0x6c, 0x57, 0x69, 0x6e, 0x64, 0x6f, 0x77, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x50,
	0x75, 0x62, 0x6c, 0x69, 0x63, 0x56, 0x69, 0x65, 0x77, 0x52, 0x0d, 0x61, 0x72, 0x72, 0x69, 0x76,
	0x61, 0x6c, 0x57, 0x69, 0x6e, 0x64, 0x6f, 0x77, 0x12, 0x61, 0x0a, 0x11, 0x73, 0x69, 0x67, 0x6e,
	0x65, 0x64, 0x5f, 0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x0e, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e,
	0x41, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x53,
	0x69, 0x6d, 0x70, 0x6c, 0x65, 0x56, 0x69, 0x65, 0x77, 0x52, 0x10, 0x73, 0x69, 0x67, 0x6e, 0x65,
	0x64, 0x41, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x22, 0xce, 0x05, 0x0a, 0x1b,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x28, 0x0a, 0x0b, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x69, 0x0a, 0x15, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65,
	0x64, 0x5f, 0x70, 0x65, 0x74, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69,
	0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x50, 0x65,
	0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x44, 0x65, 0x66, 0x52, 0x13, 0x73, 0x65, 0x6c,
	0x65, 0x63, 0x74, 0x65, 0x64, 0x50, 0x65, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73,
	0x12, 0x19, 0x0a, 0x08, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x07, 0x73, 0x74, 0x61, 0x66, 0x66, 0x49, 0x64, 0x12, 0x45, 0x0a, 0x10, 0x61,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x1a, 0xfa, 0x42, 0x17, 0x72, 0x15, 0x32, 0x13, 0x5e, 0x5c,
	0x64, 0x7b, 0x34, 0x7d, 0x2d, 0x5c, 0x64, 0x7b, 0x32, 0x7d, 0x2d, 0x5c, 0x64, 0x7b, 0x32, 0x7d,
	0x24, 0x52, 0x0f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x61,
	0x74, 0x65, 0x12, 0x40, 0x0a, 0x16, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x05, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x1a, 0x05, 0x18, 0xa0, 0x0b, 0x28, 0x00, 0x52, 0x14,
	0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x72, 0x74,
	0x54, 0x69, 0x6d, 0x65, 0x12, 0x32, 0x0a, 0x0f, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x61, 0x6c, 0x5f, 0x6e, 0x6f, 0x74, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa,
	0x42, 0x06, 0x72, 0x04, 0x18, 0xff, 0xff, 0x03, 0x52, 0x0e, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x61, 0x6c, 0x4e, 0x6f, 0x74, 0x65, 0x12, 0x56, 0x0a, 0x0f, 0x73, 0x69, 0x67, 0x6e,
	0x5f, 0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x67,
	0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x44, 0x65, 0x66,
	0x52, 0x0e, 0x73, 0x69, 0x67, 0x6e, 0x41, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73,
	0x12, 0x51, 0x0a, 0x0c, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x6f, 0x6e, 0x5f, 0x66, 0x69, 0x6c, 0x65,
	0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f,
	0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x61, 0x72, 0x64, 0x4f, 0x6e, 0x46, 0x69,
	0x6c, 0x65, 0x44, 0x65, 0x66, 0x48, 0x00, 0x52, 0x0a, 0x63, 0x61, 0x72, 0x64, 0x4f, 0x6e, 0x46,
	0x69, 0x6c, 0x65, 0x12, 0x43, 0x0a, 0x06, 0x70, 0x72, 0x65, 0x70, 0x61, 0x79, 0x18, 0x0c, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e,
	0x67, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72, 0x65, 0x70, 0x61, 0x79, 0x44, 0x65, 0x66, 0x48, 0x00,
	0x52, 0x06, 0x70, 0x72, 0x65, 0x70, 0x61, 0x79, 0x12, 0x47, 0x0a, 0x08, 0x70, 0x72, 0x65, 0x5f,
	0x61, 0x75, 0x74, 0x68, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65,
	0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72, 0x65, 0x41,
	0x75, 0x74, 0x68, 0x44, 0x65, 0x66, 0x48, 0x00, 0x52, 0x07, 0x70, 0x72, 0x65, 0x41, 0x75, 0x74,
	0x68, 0x42, 0x09, 0x0a, 0x07, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x22, 0x44, 0x0a, 0x1c,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x24, 0x0a, 0x0e,
	0x69, 0x73, 0x5f, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x61, 0x63, 0x63, 0x65, 0x70, 0x74, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x0c, 0x69, 0x73, 0x41, 0x75, 0x74, 0x6f, 0x41, 0x63, 0x63, 0x65,
	0x70, 0x74, 0x22, 0x39, 0x0a, 0x1f, 0x50, 0x72, 0x65, 0x53, 0x75, 0x62, 0x6d, 0x69, 0x74, 0x42,
	0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0xd5, 0x01,
	0x0a, 0x1b, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x54, 0x0a,
	0x0a, 0x72, 0x65, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x32, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74,
	0x2e, 0x67, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x73,
	0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x00, 0x52, 0x0a, 0x72, 0x65, 0x73, 0x63, 0x68, 0x65, 0x64,
	0x75, 0x6c, 0x65, 0x12, 0x48, 0x0a, 0x06, 0x63, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65,
	0x6e, 0x74, 0x2e, 0x67, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x43,
	0x61, 0x6e, 0x63, 0x65, 0x6c, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x48, 0x00, 0x52, 0x06, 0x63, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x42, 0x16, 0x0a,
	0x0f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x03, 0xf8, 0x42, 0x01, 0x22, 0xee, 0x01, 0x0a, 0x18, 0x52, 0x65, 0x73, 0x63, 0x68, 0x65,
	0x64, 0x75, 0x6c, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x2e, 0x0a, 0x0e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22,
	0x02, 0x20, 0x00, 0x52, 0x0d, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x73, 0x74, 0x61, 0x66, 0x66, 0x49, 0x64, 0x12, 0x45, 0x0a,
	0x10, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x64, 0x61, 0x74,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x1a, 0xfa, 0x42, 0x17, 0x72, 0x15, 0x32, 0x13,
	0x5e, 0x5c, 0x64, 0x7b, 0x34, 0x7d, 0x2d, 0x5c, 0x64, 0x7b, 0x32, 0x7d, 0x2d, 0x5c, 0x64, 0x7b,
	0x32, 0x7d, 0x24, 0x52, 0x0f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x44, 0x61, 0x74, 0x65, 0x12, 0x40, 0x0a, 0x16, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x05, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x1a, 0x05, 0x18, 0xa0, 0x0b, 0x28, 0x00,
	0x52, 0x14, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61,
	0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x22, 0x46, 0x0a, 0x14, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c,
	0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2e,
	0x0a, 0x0e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52,
	0x0d, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x22, 0x5c,
	0x0a, 0x1c, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x16,
	0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06,
	0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x24, 0x0a, 0x0e, 0x69, 0x73, 0x5f, 0x61, 0x75, 0x74,
	0x6f, 0x5f, 0x61, 0x63, 0x63, 0x65, 0x70, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0c,
	0x69, 0x73, 0x41, 0x75, 0x74, 0x6f, 0x41, 0x63, 0x63, 0x65, 0x70, 0x74, 0x22, 0x8f, 0x01, 0x0a,
	0x18, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2e, 0x0a, 0x0e, 0x61, 0x70, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0d, 0x61, 0x70, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x43, 0x0a, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2b, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x67, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e,
	0x67, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x33,
	0x0a, 0x19, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x72,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x72, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x22, 0x7a, 0x0a, 0x1f, 0x47, 0x65, 0x74, 0x54, 0x6f, 0x64, 0x61, 0x79, 0x41,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x57, 0x0a, 0x0c, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x72, 0x6f, 0x6f,
	0x6d, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x52, 0x0c, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x32,
	0xfa, 0x08, 0x0a, 0x12, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x79, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x41, 0x70, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x30, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x72, 0x6f, 0x6f,
	0x6d, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x31,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x72,
	0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x7f, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x32, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e,
	0x67, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x33, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x72, 0x6f,
	0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x90, 0x01, 0x0a, 0x17, 0x50, 0x72, 0x65, 0x53, 0x75, 0x62, 0x6d, 0x69, 0x74,
	0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x35,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x72,
	0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x39, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c,
	0x69, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31,
	0x2e, 0x50, 0x72, 0x65, 0x53, 0x75, 0x62, 0x6d, 0x69, 0x74, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e,
	0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x22, 0x03, 0x88, 0x02, 0x01, 0x12, 0x8a, 0x01, 0x0a, 0x14, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x35,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x72,
	0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x36, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c,
	0x69, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31,
	0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x03, 0x88,
	0x02, 0x01, 0x12, 0x85, 0x01, 0x0a, 0x14, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x42, 0x6f, 0x6f,
	0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x35, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x72, 0x6f, 0x6f, 0x6d,
	0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x42, 0x6f, 0x6f,
	0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x36, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e,
	0x74, 0x2e, 0x67, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x7c, 0x0a, 0x11, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x12,
	0x32, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x67,
	0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65,
	0x6e, 0x74, 0x2e, 0x67, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x69, 0x0a, 0x1a, 0x47, 0x65, 0x74, 0x4c,
	0x61, 0x73, 0x74, 0x46, 0x69, 0x6e, 0x69, 0x73, 0x68, 0x65, 0x64, 0x41, 0x70, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x1a, 0x33,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x72,
	0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x69, 0x0a, 0x1a, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x78, 0x74, 0x55, 0x70,
	0x63, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x12, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x1a, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e,
	0x67, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x6c,
	0x0a, 0x17, 0x47, 0x65, 0x74, 0x54, 0x6f, 0x64, 0x61, 0x79, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74,
	0x79, 0x1a, 0x39, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74,
	0x2e, 0x67, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74,
	0x54, 0x6f, 0x64, 0x61, 0x79, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x81, 0x01, 0x0a,
	0x20, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x63,
	0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x67, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x2e, 0x76,
	0x31, 0x50, 0x01, 0x5a, 0x5b, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f,
	0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f,
	0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2f, 0x67, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x2f,
	0x76, 0x31, 0x3b, 0x67, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x61, 0x70, 0x69, 0x70, 0x62,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_client_grooming_v1_appointment_api_proto_rawDescOnce sync.Once
	file_moego_client_grooming_v1_appointment_api_proto_rawDescData = file_moego_client_grooming_v1_appointment_api_proto_rawDesc
)

func file_moego_client_grooming_v1_appointment_api_proto_rawDescGZIP() []byte {
	file_moego_client_grooming_v1_appointment_api_proto_rawDescOnce.Do(func() {
		file_moego_client_grooming_v1_appointment_api_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_client_grooming_v1_appointment_api_proto_rawDescData)
	})
	return file_moego_client_grooming_v1_appointment_api_proto_rawDescData
}

var file_moego_client_grooming_v1_appointment_api_proto_msgTypes = make([]protoimpl.MessageInfo, 14)
var file_moego_client_grooming_v1_appointment_api_proto_goTypes = []interface{}{
	(*AppointmentListRequest)(nil),                     // 0: moego.client.grooming.v1.AppointmentListRequest
	(*AppointmentListResponse)(nil),                    // 1: moego.client.grooming.v1.AppointmentListResponse
	(*AppointmentDetailRequest)(nil),                   // 2: moego.client.grooming.v1.AppointmentDetailRequest
	(*AppointmentDetailResponse)(nil),                  // 3: moego.client.grooming.v1.AppointmentDetailResponse
	(*CreateBookingRequestRequest)(nil),                // 4: moego.client.grooming.v1.CreateBookingRequestRequest
	(*CreateBookingRequestResponse)(nil),               // 5: moego.client.grooming.v1.CreateBookingRequestResponse
	(*PreSubmitBookingRequestResponse)(nil),            // 6: moego.client.grooming.v1.PreSubmitBookingRequestResponse
	(*UpdateBookingRequestRequest)(nil),                // 7: moego.client.grooming.v1.UpdateBookingRequestRequest
	(*RescheduleBookingRequest)(nil),                   // 8: moego.client.grooming.v1.RescheduleBookingRequest
	(*CancelBookingRequest)(nil),                       // 9: moego.client.grooming.v1.CancelBookingRequest
	(*UpdateBookingRequestResponse)(nil),               // 10: moego.client.grooming.v1.UpdateBookingRequestResponse
	(*UpdateAppointmentRequest)(nil),                   // 11: moego.client.grooming.v1.UpdateAppointmentRequest
	(*UpdateAppointmentResponse)(nil),                  // 12: moego.client.grooming.v1.UpdateAppointmentResponse
	(*GetTodayAppointmentListResponse)(nil),            // 13: moego.client.grooming.v1.GetTodayAppointmentListResponse
	(v1.AppointmentType)(0),                            // 14: moego.models.grooming.v1.AppointmentType
	(*v2.PaginationRequest)(nil),                       // 15: moego.utils.v2.PaginationRequest
	(*v1.AppointmentSortDef)(nil),                      // 16: moego.models.grooming.v1.AppointmentSortDef
	(*v1.AppointmentModelClientListView)(nil),          // 17: moego.models.grooming.v1.AppointmentModelClientListView
	(*v11.BusinessModelClientListView)(nil),            // 18: moego.models.business.v1.BusinessModelClientListView
	(*v12.BusinessOBConfigModelClientListView)(nil),    // 19: moego.models.online_booking.v1.BusinessOBConfigModelClientListView
	(*v2.PaginationResponse)(nil),                      // 20: moego.utils.v2.PaginationResponse
	(*v11.ArrivalWindowModelPublicView)(nil),           // 21: moego.models.business.v1.ArrivalWindowModelPublicView
	(*v1.AppointmentModelClientView)(nil),              // 22: moego.models.grooming.v1.AppointmentModelClientView
	(*v1.PetDetailModelClientView)(nil),                // 23: moego.models.grooming.v1.PetDetailModelClientView
	(*v11.BusinessModelClientView)(nil),                // 24: moego.models.business.v1.BusinessModelClientView
	(*v13.BusinessTwilioNumberView)(nil),               // 25: moego.models.message.v1.BusinessTwilioNumberView
	(*v1.ServiceModelClientView)(nil),                  // 26: moego.models.grooming.v1.ServiceModelClientView
	(*v14.BusinessCustomerPetModelClientView)(nil),     // 27: moego.models.business_customer.v1.BusinessCustomerPetModelClientView
	(*v11.StaffModelClientView)(nil),                   // 28: moego.models.business.v1.StaffModelClientView
	(*v15.PaymentMethodModelPublicView)(nil),           // 29: moego.models.payment.v1.PaymentMethodModelPublicView
	(*v15.BookOnlineDepositModelClientView)(nil),       // 30: moego.models.payment.v1.BookOnlineDepositModelClientView
	(*v12.BusinessOBConfigModelClientView)(nil),        // 31: moego.models.online_booking.v1.BusinessOBConfigModelClientView
	(*v14.BusinessCustomerModelClientView)(nil),        // 32: moego.models.business_customer.v1.BusinessCustomerModelClientView
	(*v14.BusinessCustomerAddressModelClientView)(nil), // 33: moego.models.business_customer.v1.BusinessCustomerAddressModelClientView
	(*v16.AgreementRecordSimpleView)(nil),              // 34: moego.models.agreement.v1.AgreementRecordSimpleView
	(*v12.SelectedPetServiceDef)(nil),                  // 35: moego.models.online_booking.v1.SelectedPetServiceDef
	(*v16.AgreementRecordDef)(nil),                     // 36: moego.models.agreement.v1.AgreementRecordDef
	(*v12.CardOnFileDef)(nil),                          // 37: moego.models.online_booking.v1.CardOnFileDef
	(*v12.PrepayDef)(nil),                              // 38: moego.models.online_booking.v1.PrepayDef
	(*v12.PreAuthDef)(nil),                             // 39: moego.models.online_booking.v1.PreAuthDef
	(v1.AppointmentStatus)(0),                          // 40: moego.models.grooming.v1.AppointmentStatus
	(*emptypb.Empty)(nil),                              // 41: google.protobuf.Empty
}
var file_moego_client_grooming_v1_appointment_api_proto_depIdxs = []int32{
	14, // 0: moego.client.grooming.v1.AppointmentListRequest.type:type_name -> moego.models.grooming.v1.AppointmentType
	15, // 1: moego.client.grooming.v1.AppointmentListRequest.pagination:type_name -> moego.utils.v2.PaginationRequest
	16, // 2: moego.client.grooming.v1.AppointmentListRequest.sorts:type_name -> moego.models.grooming.v1.AppointmentSortDef
	17, // 3: moego.client.grooming.v1.AppointmentListResponse.appointments:type_name -> moego.models.grooming.v1.AppointmentModelClientListView
	18, // 4: moego.client.grooming.v1.AppointmentListResponse.businesses:type_name -> moego.models.business.v1.BusinessModelClientListView
	19, // 5: moego.client.grooming.v1.AppointmentListResponse.ob_configs:type_name -> moego.models.online_booking.v1.BusinessOBConfigModelClientListView
	20, // 6: moego.client.grooming.v1.AppointmentListResponse.pagination:type_name -> moego.utils.v2.PaginationResponse
	21, // 7: moego.client.grooming.v1.AppointmentListResponse.arrival_windows:type_name -> moego.models.business.v1.ArrivalWindowModelPublicView
	22, // 8: moego.client.grooming.v1.AppointmentDetailResponse.appointment:type_name -> moego.models.grooming.v1.AppointmentModelClientView
	23, // 9: moego.client.grooming.v1.AppointmentDetailResponse.appointment_items:type_name -> moego.models.grooming.v1.PetDetailModelClientView
	24, // 10: moego.client.grooming.v1.AppointmentDetailResponse.business:type_name -> moego.models.business.v1.BusinessModelClientView
	25, // 11: moego.client.grooming.v1.AppointmentDetailResponse.twilio_number:type_name -> moego.models.message.v1.BusinessTwilioNumberView
	26, // 12: moego.client.grooming.v1.AppointmentDetailResponse.services:type_name -> moego.models.grooming.v1.ServiceModelClientView
	27, // 13: moego.client.grooming.v1.AppointmentDetailResponse.business_pets:type_name -> moego.models.business_customer.v1.BusinessCustomerPetModelClientView
	28, // 14: moego.client.grooming.v1.AppointmentDetailResponse.staffs:type_name -> moego.models.business.v1.StaffModelClientView
	29, // 15: moego.client.grooming.v1.AppointmentDetailResponse.payment_method:type_name -> moego.models.payment.v1.PaymentMethodModelPublicView
	30, // 16: moego.client.grooming.v1.AppointmentDetailResponse.ob_deposit:type_name -> moego.models.payment.v1.BookOnlineDepositModelClientView
	31, // 17: moego.client.grooming.v1.AppointmentDetailResponse.ob_config:type_name -> moego.models.online_booking.v1.BusinessOBConfigModelClientView
	32, // 18: moego.client.grooming.v1.AppointmentDetailResponse.customer:type_name -> moego.models.business_customer.v1.BusinessCustomerModelClientView
	33, // 19: moego.client.grooming.v1.AppointmentDetailResponse.primary_address:type_name -> moego.models.business_customer.v1.BusinessCustomerAddressModelClientView
	21, // 20: moego.client.grooming.v1.AppointmentDetailResponse.arrival_window:type_name -> moego.models.business.v1.ArrivalWindowModelPublicView
	34, // 21: moego.client.grooming.v1.AppointmentDetailResponse.signed_agreements:type_name -> moego.models.agreement.v1.AgreementRecordSimpleView
	35, // 22: moego.client.grooming.v1.CreateBookingRequestRequest.selected_pet_services:type_name -> moego.models.online_booking.v1.SelectedPetServiceDef
	36, // 23: moego.client.grooming.v1.CreateBookingRequestRequest.sign_agreements:type_name -> moego.models.agreement.v1.AgreementRecordDef
	37, // 24: moego.client.grooming.v1.CreateBookingRequestRequest.card_on_file:type_name -> moego.models.online_booking.v1.CardOnFileDef
	38, // 25: moego.client.grooming.v1.CreateBookingRequestRequest.prepay:type_name -> moego.models.online_booking.v1.PrepayDef
	39, // 26: moego.client.grooming.v1.CreateBookingRequestRequest.pre_auth:type_name -> moego.models.online_booking.v1.PreAuthDef
	8,  // 27: moego.client.grooming.v1.UpdateBookingRequestRequest.reschedule:type_name -> moego.client.grooming.v1.RescheduleBookingRequest
	9,  // 28: moego.client.grooming.v1.UpdateBookingRequestRequest.cancel:type_name -> moego.client.grooming.v1.CancelBookingRequest
	40, // 29: moego.client.grooming.v1.UpdateAppointmentRequest.status:type_name -> moego.models.grooming.v1.AppointmentStatus
	3,  // 30: moego.client.grooming.v1.GetTodayAppointmentListResponse.appointments:type_name -> moego.client.grooming.v1.AppointmentDetailResponse
	0,  // 31: moego.client.grooming.v1.AppointmentService.GetAppointmentList:input_type -> moego.client.grooming.v1.AppointmentListRequest
	2,  // 32: moego.client.grooming.v1.AppointmentService.GetAppointmentDetail:input_type -> moego.client.grooming.v1.AppointmentDetailRequest
	4,  // 33: moego.client.grooming.v1.AppointmentService.PreSubmitBookingRequest:input_type -> moego.client.grooming.v1.CreateBookingRequestRequest
	4,  // 34: moego.client.grooming.v1.AppointmentService.CreateBookingRequest:input_type -> moego.client.grooming.v1.CreateBookingRequestRequest
	7,  // 35: moego.client.grooming.v1.AppointmentService.UpdateBookingRequest:input_type -> moego.client.grooming.v1.UpdateBookingRequestRequest
	11, // 36: moego.client.grooming.v1.AppointmentService.UpdateAppointment:input_type -> moego.client.grooming.v1.UpdateAppointmentRequest
	41, // 37: moego.client.grooming.v1.AppointmentService.GetLastFinishedAppointment:input_type -> google.protobuf.Empty
	41, // 38: moego.client.grooming.v1.AppointmentService.GetNextUpcomingAppointment:input_type -> google.protobuf.Empty
	41, // 39: moego.client.grooming.v1.AppointmentService.GetTodayAppointmentList:input_type -> google.protobuf.Empty
	1,  // 40: moego.client.grooming.v1.AppointmentService.GetAppointmentList:output_type -> moego.client.grooming.v1.AppointmentListResponse
	3,  // 41: moego.client.grooming.v1.AppointmentService.GetAppointmentDetail:output_type -> moego.client.grooming.v1.AppointmentDetailResponse
	6,  // 42: moego.client.grooming.v1.AppointmentService.PreSubmitBookingRequest:output_type -> moego.client.grooming.v1.PreSubmitBookingRequestResponse
	5,  // 43: moego.client.grooming.v1.AppointmentService.CreateBookingRequest:output_type -> moego.client.grooming.v1.CreateBookingRequestResponse
	10, // 44: moego.client.grooming.v1.AppointmentService.UpdateBookingRequest:output_type -> moego.client.grooming.v1.UpdateBookingRequestResponse
	12, // 45: moego.client.grooming.v1.AppointmentService.UpdateAppointment:output_type -> moego.client.grooming.v1.UpdateAppointmentResponse
	3,  // 46: moego.client.grooming.v1.AppointmentService.GetLastFinishedAppointment:output_type -> moego.client.grooming.v1.AppointmentDetailResponse
	3,  // 47: moego.client.grooming.v1.AppointmentService.GetNextUpcomingAppointment:output_type -> moego.client.grooming.v1.AppointmentDetailResponse
	13, // 48: moego.client.grooming.v1.AppointmentService.GetTodayAppointmentList:output_type -> moego.client.grooming.v1.GetTodayAppointmentListResponse
	40, // [40:49] is the sub-list for method output_type
	31, // [31:40] is the sub-list for method input_type
	31, // [31:31] is the sub-list for extension type_name
	31, // [31:31] is the sub-list for extension extendee
	0,  // [0:31] is the sub-list for field type_name
}

func init() { file_moego_client_grooming_v1_appointment_api_proto_init() }
func file_moego_client_grooming_v1_appointment_api_proto_init() {
	if File_moego_client_grooming_v1_appointment_api_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_client_grooming_v1_appointment_api_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AppointmentListRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_grooming_v1_appointment_api_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AppointmentListResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_grooming_v1_appointment_api_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AppointmentDetailRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_grooming_v1_appointment_api_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AppointmentDetailResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_grooming_v1_appointment_api_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateBookingRequestRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_grooming_v1_appointment_api_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateBookingRequestResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_grooming_v1_appointment_api_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PreSubmitBookingRequestResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_grooming_v1_appointment_api_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateBookingRequestRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_grooming_v1_appointment_api_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RescheduleBookingRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_grooming_v1_appointment_api_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CancelBookingRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_grooming_v1_appointment_api_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateBookingRequestResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_grooming_v1_appointment_api_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateAppointmentRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_grooming_v1_appointment_api_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateAppointmentResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_grooming_v1_appointment_api_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTodayAppointmentListResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_client_grooming_v1_appointment_api_proto_msgTypes[0].OneofWrappers = []interface{}{}
	file_moego_client_grooming_v1_appointment_api_proto_msgTypes[1].OneofWrappers = []interface{}{}
	file_moego_client_grooming_v1_appointment_api_proto_msgTypes[4].OneofWrappers = []interface{}{
		(*CreateBookingRequestRequest_CardOnFile)(nil),
		(*CreateBookingRequestRequest_Prepay)(nil),
		(*CreateBookingRequestRequest_PreAuth)(nil),
	}
	file_moego_client_grooming_v1_appointment_api_proto_msgTypes[7].OneofWrappers = []interface{}{
		(*UpdateBookingRequestRequest_Reschedule)(nil),
		(*UpdateBookingRequestRequest_Cancel)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_client_grooming_v1_appointment_api_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   14,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_client_grooming_v1_appointment_api_proto_goTypes,
		DependencyIndexes: file_moego_client_grooming_v1_appointment_api_proto_depIdxs,
		MessageInfos:      file_moego_client_grooming_v1_appointment_api_proto_msgTypes,
	}.Build()
	File_moego_client_grooming_v1_appointment_api_proto = out.File
	file_moego_client_grooming_v1_appointment_api_proto_rawDesc = nil
	file_moego_client_grooming_v1_appointment_api_proto_goTypes = nil
	file_moego_client_grooming_v1_appointment_api_proto_depIdxs = nil
}
