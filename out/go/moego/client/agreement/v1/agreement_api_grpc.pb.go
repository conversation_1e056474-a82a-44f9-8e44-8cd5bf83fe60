// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/client/agreement/v1/agreement_api.proto

package agreementapipb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// AgreementServiceClient is the client API for AgreementService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type AgreementServiceClient interface {
	// get agreement list
	GetOnlineBookingRequiredAgreementList(ctx context.Context, in *GetOnlineBookingRequiredAgreementListRequest, opts ...grpc.CallOption) (*GetOnlineBookingRequiredAgreementListResponse, error)
}

type agreementServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewAgreementServiceClient(cc grpc.ClientConnInterface) AgreementServiceClient {
	return &agreementServiceClient{cc}
}

func (c *agreementServiceClient) GetOnlineBookingRequiredAgreementList(ctx context.Context, in *GetOnlineBookingRequiredAgreementListRequest, opts ...grpc.CallOption) (*GetOnlineBookingRequiredAgreementListResponse, error) {
	out := new(GetOnlineBookingRequiredAgreementListResponse)
	err := c.cc.Invoke(ctx, "/moego.client.agreement.v1.AgreementService/GetOnlineBookingRequiredAgreementList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AgreementServiceServer is the server API for AgreementService service.
// All implementations must embed UnimplementedAgreementServiceServer
// for forward compatibility
type AgreementServiceServer interface {
	// get agreement list
	GetOnlineBookingRequiredAgreementList(context.Context, *GetOnlineBookingRequiredAgreementListRequest) (*GetOnlineBookingRequiredAgreementListResponse, error)
	mustEmbedUnimplementedAgreementServiceServer()
}

// UnimplementedAgreementServiceServer must be embedded to have forward compatible implementations.
type UnimplementedAgreementServiceServer struct {
}

func (UnimplementedAgreementServiceServer) GetOnlineBookingRequiredAgreementList(context.Context, *GetOnlineBookingRequiredAgreementListRequest) (*GetOnlineBookingRequiredAgreementListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetOnlineBookingRequiredAgreementList not implemented")
}
func (UnimplementedAgreementServiceServer) mustEmbedUnimplementedAgreementServiceServer() {}

// UnsafeAgreementServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to AgreementServiceServer will
// result in compilation errors.
type UnsafeAgreementServiceServer interface {
	mustEmbedUnimplementedAgreementServiceServer()
}

func RegisterAgreementServiceServer(s grpc.ServiceRegistrar, srv AgreementServiceServer) {
	s.RegisterService(&AgreementService_ServiceDesc, srv)
}

func _AgreementService_GetOnlineBookingRequiredAgreementList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetOnlineBookingRequiredAgreementListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AgreementServiceServer).GetOnlineBookingRequiredAgreementList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.client.agreement.v1.AgreementService/GetOnlineBookingRequiredAgreementList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AgreementServiceServer).GetOnlineBookingRequiredAgreementList(ctx, req.(*GetOnlineBookingRequiredAgreementListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// AgreementService_ServiceDesc is the grpc.ServiceDesc for AgreementService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var AgreementService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.client.agreement.v1.AgreementService",
	HandlerType: (*AgreementServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetOnlineBookingRequiredAgreementList",
			Handler:    _AgreementService_GetOnlineBookingRequiredAgreementList_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/client/agreement/v1/agreement_api.proto",
}
