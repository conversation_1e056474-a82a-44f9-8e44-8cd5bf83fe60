// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/client/agreement/v1/agreement_record_api.proto

package agreementapipb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// AgreementRecordServiceClient is the client API for AgreementRecordService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type AgreementRecordServiceClient interface {
	// get agreement record list for customer
	GetRecordList(ctx context.Context, in *GetRecordListParams, opts ...grpc.CallOption) (*GetRecordListResult, error)
}

type agreementRecordServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewAgreementRecordServiceClient(cc grpc.ClientConnInterface) AgreementRecordServiceClient {
	return &agreementRecordServiceClient{cc}
}

func (c *agreementRecordServiceClient) GetRecordList(ctx context.Context, in *GetRecordListParams, opts ...grpc.CallOption) (*GetRecordListResult, error) {
	out := new(GetRecordListResult)
	err := c.cc.Invoke(ctx, "/moego.client.agreement.v1.AgreementRecordService/GetRecordList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AgreementRecordServiceServer is the server API for AgreementRecordService service.
// All implementations must embed UnimplementedAgreementRecordServiceServer
// for forward compatibility
type AgreementRecordServiceServer interface {
	// get agreement record list for customer
	GetRecordList(context.Context, *GetRecordListParams) (*GetRecordListResult, error)
	mustEmbedUnimplementedAgreementRecordServiceServer()
}

// UnimplementedAgreementRecordServiceServer must be embedded to have forward compatible implementations.
type UnimplementedAgreementRecordServiceServer struct {
}

func (UnimplementedAgreementRecordServiceServer) GetRecordList(context.Context, *GetRecordListParams) (*GetRecordListResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetRecordList not implemented")
}
func (UnimplementedAgreementRecordServiceServer) mustEmbedUnimplementedAgreementRecordServiceServer() {
}

// UnsafeAgreementRecordServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to AgreementRecordServiceServer will
// result in compilation errors.
type UnsafeAgreementRecordServiceServer interface {
	mustEmbedUnimplementedAgreementRecordServiceServer()
}

func RegisterAgreementRecordServiceServer(s grpc.ServiceRegistrar, srv AgreementRecordServiceServer) {
	s.RegisterService(&AgreementRecordService_ServiceDesc, srv)
}

func _AgreementRecordService_GetRecordList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRecordListParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AgreementRecordServiceServer).GetRecordList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.client.agreement.v1.AgreementRecordService/GetRecordList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AgreementRecordServiceServer).GetRecordList(ctx, req.(*GetRecordListParams))
	}
	return interceptor(ctx, in, info, handler)
}

// AgreementRecordService_ServiceDesc is the grpc.ServiceDesc for AgreementRecordService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var AgreementRecordService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.client.agreement.v1.AgreementRecordService",
	HandlerType: (*AgreementRecordServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetRecordList",
			Handler:    _AgreementRecordService_GetRecordList_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/client/agreement/v1/agreement_record_api.proto",
}
