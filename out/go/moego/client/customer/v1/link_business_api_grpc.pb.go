// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/client/customer/v1/link_business_api.proto

package customerapipb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// LinkBusinessServiceClient is the client API for LinkBusinessService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type LinkBusinessServiceClient interface {
	// get link business list
	GetLinkBusinessList(ctx context.Context, in *GetLinkBusinessListParams, opts ...grpc.CallOption) (*GetLinkBusinessListResult, error)
	// link business
	LinkBusiness(ctx context.Context, in *LinkBusinessParams, opts ...grpc.CallOption) (*LinkBusinessResult, error)
	// check business invitation code and phone number
	GetInvitationBusinessInfo(ctx context.Context, in *GetInvitationBusinessInfoParams, opts ...grpc.CallOption) (*GetInvitationBusinessInfoResult, error)
	// get phone number profile
	GetCustomerProfile(ctx context.Context, in *GetCustomerProfileParams, opts ...grpc.CallOption) (*GetCustomerProfileResult, error)
	// send verification code
	SendVerificationCode(ctx context.Context, in *SendVerificationCodeParams, opts ...grpc.CallOption) (*SendVerificationCodeResult, error)
	// check verification code
	CheckVerificationCode(ctx context.Context, in *CheckVerificationCodeParams, opts ...grpc.CallOption) (*CheckVerificationCodeResult, error)
}

type linkBusinessServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewLinkBusinessServiceClient(cc grpc.ClientConnInterface) LinkBusinessServiceClient {
	return &linkBusinessServiceClient{cc}
}

func (c *linkBusinessServiceClient) GetLinkBusinessList(ctx context.Context, in *GetLinkBusinessListParams, opts ...grpc.CallOption) (*GetLinkBusinessListResult, error) {
	out := new(GetLinkBusinessListResult)
	err := c.cc.Invoke(ctx, "/moego.client.customer.v1.LinkBusinessService/GetLinkBusinessList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *linkBusinessServiceClient) LinkBusiness(ctx context.Context, in *LinkBusinessParams, opts ...grpc.CallOption) (*LinkBusinessResult, error) {
	out := new(LinkBusinessResult)
	err := c.cc.Invoke(ctx, "/moego.client.customer.v1.LinkBusinessService/LinkBusiness", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *linkBusinessServiceClient) GetInvitationBusinessInfo(ctx context.Context, in *GetInvitationBusinessInfoParams, opts ...grpc.CallOption) (*GetInvitationBusinessInfoResult, error) {
	out := new(GetInvitationBusinessInfoResult)
	err := c.cc.Invoke(ctx, "/moego.client.customer.v1.LinkBusinessService/GetInvitationBusinessInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *linkBusinessServiceClient) GetCustomerProfile(ctx context.Context, in *GetCustomerProfileParams, opts ...grpc.CallOption) (*GetCustomerProfileResult, error) {
	out := new(GetCustomerProfileResult)
	err := c.cc.Invoke(ctx, "/moego.client.customer.v1.LinkBusinessService/GetCustomerProfile", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *linkBusinessServiceClient) SendVerificationCode(ctx context.Context, in *SendVerificationCodeParams, opts ...grpc.CallOption) (*SendVerificationCodeResult, error) {
	out := new(SendVerificationCodeResult)
	err := c.cc.Invoke(ctx, "/moego.client.customer.v1.LinkBusinessService/SendVerificationCode", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *linkBusinessServiceClient) CheckVerificationCode(ctx context.Context, in *CheckVerificationCodeParams, opts ...grpc.CallOption) (*CheckVerificationCodeResult, error) {
	out := new(CheckVerificationCodeResult)
	err := c.cc.Invoke(ctx, "/moego.client.customer.v1.LinkBusinessService/CheckVerificationCode", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// LinkBusinessServiceServer is the server API for LinkBusinessService service.
// All implementations must embed UnimplementedLinkBusinessServiceServer
// for forward compatibility
type LinkBusinessServiceServer interface {
	// get link business list
	GetLinkBusinessList(context.Context, *GetLinkBusinessListParams) (*GetLinkBusinessListResult, error)
	// link business
	LinkBusiness(context.Context, *LinkBusinessParams) (*LinkBusinessResult, error)
	// check business invitation code and phone number
	GetInvitationBusinessInfo(context.Context, *GetInvitationBusinessInfoParams) (*GetInvitationBusinessInfoResult, error)
	// get phone number profile
	GetCustomerProfile(context.Context, *GetCustomerProfileParams) (*GetCustomerProfileResult, error)
	// send verification code
	SendVerificationCode(context.Context, *SendVerificationCodeParams) (*SendVerificationCodeResult, error)
	// check verification code
	CheckVerificationCode(context.Context, *CheckVerificationCodeParams) (*CheckVerificationCodeResult, error)
	mustEmbedUnimplementedLinkBusinessServiceServer()
}

// UnimplementedLinkBusinessServiceServer must be embedded to have forward compatible implementations.
type UnimplementedLinkBusinessServiceServer struct {
}

func (UnimplementedLinkBusinessServiceServer) GetLinkBusinessList(context.Context, *GetLinkBusinessListParams) (*GetLinkBusinessListResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetLinkBusinessList not implemented")
}
func (UnimplementedLinkBusinessServiceServer) LinkBusiness(context.Context, *LinkBusinessParams) (*LinkBusinessResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method LinkBusiness not implemented")
}
func (UnimplementedLinkBusinessServiceServer) GetInvitationBusinessInfo(context.Context, *GetInvitationBusinessInfoParams) (*GetInvitationBusinessInfoResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetInvitationBusinessInfo not implemented")
}
func (UnimplementedLinkBusinessServiceServer) GetCustomerProfile(context.Context, *GetCustomerProfileParams) (*GetCustomerProfileResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCustomerProfile not implemented")
}
func (UnimplementedLinkBusinessServiceServer) SendVerificationCode(context.Context, *SendVerificationCodeParams) (*SendVerificationCodeResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendVerificationCode not implemented")
}
func (UnimplementedLinkBusinessServiceServer) CheckVerificationCode(context.Context, *CheckVerificationCodeParams) (*CheckVerificationCodeResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckVerificationCode not implemented")
}
func (UnimplementedLinkBusinessServiceServer) mustEmbedUnimplementedLinkBusinessServiceServer() {}

// UnsafeLinkBusinessServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to LinkBusinessServiceServer will
// result in compilation errors.
type UnsafeLinkBusinessServiceServer interface {
	mustEmbedUnimplementedLinkBusinessServiceServer()
}

func RegisterLinkBusinessServiceServer(s grpc.ServiceRegistrar, srv LinkBusinessServiceServer) {
	s.RegisterService(&LinkBusinessService_ServiceDesc, srv)
}

func _LinkBusinessService_GetLinkBusinessList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLinkBusinessListParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LinkBusinessServiceServer).GetLinkBusinessList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.client.customer.v1.LinkBusinessService/GetLinkBusinessList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LinkBusinessServiceServer).GetLinkBusinessList(ctx, req.(*GetLinkBusinessListParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _LinkBusinessService_LinkBusiness_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LinkBusinessParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LinkBusinessServiceServer).LinkBusiness(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.client.customer.v1.LinkBusinessService/LinkBusiness",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LinkBusinessServiceServer).LinkBusiness(ctx, req.(*LinkBusinessParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _LinkBusinessService_GetInvitationBusinessInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetInvitationBusinessInfoParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LinkBusinessServiceServer).GetInvitationBusinessInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.client.customer.v1.LinkBusinessService/GetInvitationBusinessInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LinkBusinessServiceServer).GetInvitationBusinessInfo(ctx, req.(*GetInvitationBusinessInfoParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _LinkBusinessService_GetCustomerProfile_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCustomerProfileParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LinkBusinessServiceServer).GetCustomerProfile(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.client.customer.v1.LinkBusinessService/GetCustomerProfile",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LinkBusinessServiceServer).GetCustomerProfile(ctx, req.(*GetCustomerProfileParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _LinkBusinessService_SendVerificationCode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SendVerificationCodeParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LinkBusinessServiceServer).SendVerificationCode(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.client.customer.v1.LinkBusinessService/SendVerificationCode",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LinkBusinessServiceServer).SendVerificationCode(ctx, req.(*SendVerificationCodeParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _LinkBusinessService_CheckVerificationCode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckVerificationCodeParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LinkBusinessServiceServer).CheckVerificationCode(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.client.customer.v1.LinkBusinessService/CheckVerificationCode",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LinkBusinessServiceServer).CheckVerificationCode(ctx, req.(*CheckVerificationCodeParams))
	}
	return interceptor(ctx, in, info, handler)
}

// LinkBusinessService_ServiceDesc is the grpc.ServiceDesc for LinkBusinessService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var LinkBusinessService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.client.customer.v1.LinkBusinessService",
	HandlerType: (*LinkBusinessServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetLinkBusinessList",
			Handler:    _LinkBusinessService_GetLinkBusinessList_Handler,
		},
		{
			MethodName: "LinkBusiness",
			Handler:    _LinkBusinessService_LinkBusiness_Handler,
		},
		{
			MethodName: "GetInvitationBusinessInfo",
			Handler:    _LinkBusinessService_GetInvitationBusinessInfo_Handler,
		},
		{
			MethodName: "GetCustomerProfile",
			Handler:    _LinkBusinessService_GetCustomerProfile_Handler,
		},
		{
			MethodName: "SendVerificationCode",
			Handler:    _LinkBusinessService_SendVerificationCode_Handler,
		},
		{
			MethodName: "CheckVerificationCode",
			Handler:    _LinkBusinessService_CheckVerificationCode_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/client/customer/v1/link_business_api.proto",
}
