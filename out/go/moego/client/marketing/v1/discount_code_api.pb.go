// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/client/marketing/v1/discount_code_api.proto

package marketingapipb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/marketing/v1"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// get business discount code config params
type GetDiscountCodeConfigParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// selected business id
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
}

func (x *GetDiscountCodeConfigParams) Reset() {
	*x = GetDiscountCodeConfigParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_marketing_v1_discount_code_api_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetDiscountCodeConfigParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDiscountCodeConfigParams) ProtoMessage() {}

func (x *GetDiscountCodeConfigParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_marketing_v1_discount_code_api_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDiscountCodeConfigParams.ProtoReflect.Descriptor instead.
func (*GetDiscountCodeConfigParams) Descriptor() ([]byte, []int) {
	return file_moego_client_marketing_v1_discount_code_api_proto_rawDescGZIP(), []int{0}
}

func (x *GetDiscountCodeConfigParams) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

// get business discount code config result
type GetDiscountCodeConfigResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business has valid discount code
	HasValidDiscountCode bool `protobuf:"varint,1,opt,name=has_valid_discount_code,json=hasValidDiscountCode,proto3" json:"has_valid_discount_code,omitempty"`
}

func (x *GetDiscountCodeConfigResult) Reset() {
	*x = GetDiscountCodeConfigResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_marketing_v1_discount_code_api_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetDiscountCodeConfigResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDiscountCodeConfigResult) ProtoMessage() {}

func (x *GetDiscountCodeConfigResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_marketing_v1_discount_code_api_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDiscountCodeConfigResult.ProtoReflect.Descriptor instead.
func (*GetDiscountCodeConfigResult) Descriptor() ([]byte, []int) {
	return file_moego_client_marketing_v1_discount_code_api_proto_rawDescGZIP(), []int{1}
}

func (x *GetDiscountCodeConfigResult) GetHasValidDiscountCode() bool {
	if x != nil {
		return x.HasValidDiscountCode
	}
	return false
}

// check discount code params
type CheckDiscountCodeParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// selected business id
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// discount code
	DiscountCode string `protobuf:"bytes,2,opt,name=discount_code,json=discountCode,proto3" json:"discount_code,omitempty"`
	// service ids, include the ID collection of services and add-ons
	ServiceIds []int64 `protobuf:"varint,3,rep,packed,name=service_ids,json=serviceIds,proto3" json:"service_ids,omitempty"`
	// appointment date, used to verify the specific appointment datetime within the validity period of Discount Code
	AppointmentDate *string `protobuf:"bytes,4,opt,name=appointment_date,json=appointmentDate,proto3,oneof" json:"appointment_date,omitempty"`
}

func (x *CheckDiscountCodeParams) Reset() {
	*x = CheckDiscountCodeParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_marketing_v1_discount_code_api_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckDiscountCodeParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckDiscountCodeParams) ProtoMessage() {}

func (x *CheckDiscountCodeParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_marketing_v1_discount_code_api_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckDiscountCodeParams.ProtoReflect.Descriptor instead.
func (*CheckDiscountCodeParams) Descriptor() ([]byte, []int) {
	return file_moego_client_marketing_v1_discount_code_api_proto_rawDescGZIP(), []int{2}
}

func (x *CheckDiscountCodeParams) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *CheckDiscountCodeParams) GetDiscountCode() string {
	if x != nil {
		return x.DiscountCode
	}
	return ""
}

func (x *CheckDiscountCodeParams) GetServiceIds() []int64 {
	if x != nil {
		return x.ServiceIds
	}
	return nil
}

func (x *CheckDiscountCodeParams) GetAppointmentDate() string {
	if x != nil && x.AppointmentDate != nil {
		return *x.AppointmentDate
	}
	return ""
}

// check discount code result
type CheckDiscountCodeResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// discount code detail info
	DiscountCode *v1.DiscountCodeModelOnlineBookingView `protobuf:"bytes,1,opt,name=discount_code,json=discountCode,proto3" json:"discount_code,omitempty"`
}

func (x *CheckDiscountCodeResult) Reset() {
	*x = CheckDiscountCodeResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_marketing_v1_discount_code_api_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckDiscountCodeResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckDiscountCodeResult) ProtoMessage() {}

func (x *CheckDiscountCodeResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_marketing_v1_discount_code_api_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckDiscountCodeResult.ProtoReflect.Descriptor instead.
func (*CheckDiscountCodeResult) Descriptor() ([]byte, []int) {
	return file_moego_client_marketing_v1_discount_code_api_proto_rawDescGZIP(), []int{3}
}

func (x *CheckDiscountCodeResult) GetDiscountCode() *v1.DiscountCodeModelOnlineBookingView {
	if x != nil {
		return x.DiscountCode
	}
	return nil
}

var File_moego_client_marketing_v1_discount_code_api_proto protoreflect.FileDescriptor

var file_moego_client_marketing_v1_discount_code_api_proto_rawDesc = []byte{
	0x0a, 0x31, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2f, 0x6d,
	0x61, 0x72, 0x6b, 0x65, 0x74, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x64, 0x69, 0x73, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x19, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e,
	0x74, 0x2e, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x1a, 0x34,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6d, 0x61, 0x72,
	0x6b, 0x65, 0x74, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76,
	0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x47, 0x0a,
	0x1b, 0x47, 0x65, 0x74, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6f, 0x64, 0x65,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x28, 0x0a, 0x0b,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x22, 0x54, 0x0a, 0x1b, 0x47, 0x65, 0x74, 0x44, 0x69, 0x73,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x35, 0x0a, 0x17, 0x68, 0x61, 0x73, 0x5f, 0x76, 0x61, 0x6c,
	0x69, 0x64, 0x5f, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x64, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x14, 0x68, 0x61, 0x73, 0x56, 0x61, 0x6c, 0x69, 0x64,
	0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x22, 0x83, 0x02, 0x0a,
	0x17, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6f,
	0x64, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x28, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x49, 0x64, 0x12, 0x2c, 0x0a, 0x0d, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x63,
	0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02,
	0x18, 0x14, 0x52, 0x0c, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6f, 0x64, 0x65,
	0x12, 0x2f, 0x0a, 0x0b, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x73, 0x18,
	0x03, 0x20, 0x03, 0x28, 0x03, 0x42, 0x0e, 0xfa, 0x42, 0x0b, 0x92, 0x01, 0x08, 0x08, 0x01, 0x22,
	0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64,
	0x73, 0x12, 0x4a, 0x0a, 0x10, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x1a, 0xfa, 0x42, 0x17,
	0x72, 0x15, 0x32, 0x13, 0x5e, 0x5c, 0x64, 0x7b, 0x34, 0x7d, 0x2d, 0x5c, 0x64, 0x7b, 0x32, 0x7d,
	0x2d, 0x5c, 0x64, 0x7b, 0x32, 0x7d, 0x24, 0x48, 0x00, 0x52, 0x0f, 0x61, 0x70, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x61, 0x74, 0x65, 0x88, 0x01, 0x01, 0x42, 0x13, 0x0a,
	0x11, 0x5f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x64, 0x61,
	0x74, 0x65, 0x22, 0x7d, 0x0a, 0x17, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x44, 0x69, 0x73, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x62, 0x0a,
	0x0d, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x3d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31,
	0x2e, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x4d, 0x6f, 0x64,
	0x65, 0x6c, 0x4f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x56,
	0x69, 0x65, 0x77, 0x52, 0x0c, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6f, 0x64,
	0x65, 0x32, 0x9c, 0x02, 0x0a, 0x13, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6f,
	0x64, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x87, 0x01, 0x0a, 0x15, 0x47, 0x65,
	0x74, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x12, 0x36, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65,
	0x6e, 0x74, 0x2e, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e,
	0x47, 0x65, 0x74, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x36, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x61, 0x72, 0x6b, 0x65,
	0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x44, 0x69, 0x73, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x12, 0x7b, 0x0a, 0x11, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x44, 0x69, 0x73, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x32, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x69, 0x6e,
	0x67, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x32, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x61, 0x72, 0x6b,
	0x65, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x44, 0x69,
	0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x42, 0x84, 0x01, 0x0a, 0x21, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69,
	0x64, 0x6c, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74,
	0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x5d, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62,
	0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72,
	0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69,
	0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2f, 0x6d, 0x61, 0x72, 0x6b,
	0x65, 0x74, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x3b, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x69,
	0x6e, 0x67, 0x61, 0x70, 0x69, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_client_marketing_v1_discount_code_api_proto_rawDescOnce sync.Once
	file_moego_client_marketing_v1_discount_code_api_proto_rawDescData = file_moego_client_marketing_v1_discount_code_api_proto_rawDesc
)

func file_moego_client_marketing_v1_discount_code_api_proto_rawDescGZIP() []byte {
	file_moego_client_marketing_v1_discount_code_api_proto_rawDescOnce.Do(func() {
		file_moego_client_marketing_v1_discount_code_api_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_client_marketing_v1_discount_code_api_proto_rawDescData)
	})
	return file_moego_client_marketing_v1_discount_code_api_proto_rawDescData
}

var file_moego_client_marketing_v1_discount_code_api_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_moego_client_marketing_v1_discount_code_api_proto_goTypes = []interface{}{
	(*GetDiscountCodeConfigParams)(nil),           // 0: moego.client.marketing.v1.GetDiscountCodeConfigParams
	(*GetDiscountCodeConfigResult)(nil),           // 1: moego.client.marketing.v1.GetDiscountCodeConfigResult
	(*CheckDiscountCodeParams)(nil),               // 2: moego.client.marketing.v1.CheckDiscountCodeParams
	(*CheckDiscountCodeResult)(nil),               // 3: moego.client.marketing.v1.CheckDiscountCodeResult
	(*v1.DiscountCodeModelOnlineBookingView)(nil), // 4: moego.models.marketing.v1.DiscountCodeModelOnlineBookingView
}
var file_moego_client_marketing_v1_discount_code_api_proto_depIdxs = []int32{
	4, // 0: moego.client.marketing.v1.CheckDiscountCodeResult.discount_code:type_name -> moego.models.marketing.v1.DiscountCodeModelOnlineBookingView
	0, // 1: moego.client.marketing.v1.DiscountCodeService.GetDiscountCodeConfig:input_type -> moego.client.marketing.v1.GetDiscountCodeConfigParams
	2, // 2: moego.client.marketing.v1.DiscountCodeService.CheckDiscountCode:input_type -> moego.client.marketing.v1.CheckDiscountCodeParams
	1, // 3: moego.client.marketing.v1.DiscountCodeService.GetDiscountCodeConfig:output_type -> moego.client.marketing.v1.GetDiscountCodeConfigResult
	3, // 4: moego.client.marketing.v1.DiscountCodeService.CheckDiscountCode:output_type -> moego.client.marketing.v1.CheckDiscountCodeResult
	3, // [3:5] is the sub-list for method output_type
	1, // [1:3] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_moego_client_marketing_v1_discount_code_api_proto_init() }
func file_moego_client_marketing_v1_discount_code_api_proto_init() {
	if File_moego_client_marketing_v1_discount_code_api_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_client_marketing_v1_discount_code_api_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetDiscountCodeConfigParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_marketing_v1_discount_code_api_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetDiscountCodeConfigResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_marketing_v1_discount_code_api_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckDiscountCodeParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_marketing_v1_discount_code_api_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckDiscountCodeResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_client_marketing_v1_discount_code_api_proto_msgTypes[2].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_client_marketing_v1_discount_code_api_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_client_marketing_v1_discount_code_api_proto_goTypes,
		DependencyIndexes: file_moego_client_marketing_v1_discount_code_api_proto_depIdxs,
		MessageInfos:      file_moego_client_marketing_v1_discount_code_api_proto_msgTypes,
	}.Build()
	File_moego_client_marketing_v1_discount_code_api_proto = out.File
	file_moego_client_marketing_v1_discount_code_api_proto_rawDesc = nil
	file_moego_client_marketing_v1_discount_code_api_proto_goTypes = nil
	file_moego_client_marketing_v1_discount_code_api_proto_depIdxs = nil
}
