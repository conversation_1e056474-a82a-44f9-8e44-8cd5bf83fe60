// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/admin/appointment/v1/appointment_admin.proto

package appointmentapipb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// AppointmentAdminServiceClient is the client API for AppointmentAdminService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type AppointmentAdminServiceClient interface {
	// query appointments list
	QueryAppointmentList(ctx context.Context, in *QueryAppointmentListParams, opts ...grpc.CallOption) (*QueryAppointmentListResult, error)
	// delete appointment
	DeleteAppointment(ctx context.Context, in *DeleteAppointmentParams, opts ...grpc.CallOption) (*DeleteAppointmentResult, error)
}

type appointmentAdminServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewAppointmentAdminServiceClient(cc grpc.ClientConnInterface) AppointmentAdminServiceClient {
	return &appointmentAdminServiceClient{cc}
}

func (c *appointmentAdminServiceClient) QueryAppointmentList(ctx context.Context, in *QueryAppointmentListParams, opts ...grpc.CallOption) (*QueryAppointmentListResult, error) {
	out := new(QueryAppointmentListResult)
	err := c.cc.Invoke(ctx, "/moego.admin.appointment.v1.AppointmentAdminService/QueryAppointmentList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appointmentAdminServiceClient) DeleteAppointment(ctx context.Context, in *DeleteAppointmentParams, opts ...grpc.CallOption) (*DeleteAppointmentResult, error) {
	out := new(DeleteAppointmentResult)
	err := c.cc.Invoke(ctx, "/moego.admin.appointment.v1.AppointmentAdminService/DeleteAppointment", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AppointmentAdminServiceServer is the server API for AppointmentAdminService service.
// All implementations must embed UnimplementedAppointmentAdminServiceServer
// for forward compatibility
type AppointmentAdminServiceServer interface {
	// query appointments list
	QueryAppointmentList(context.Context, *QueryAppointmentListParams) (*QueryAppointmentListResult, error)
	// delete appointment
	DeleteAppointment(context.Context, *DeleteAppointmentParams) (*DeleteAppointmentResult, error)
	mustEmbedUnimplementedAppointmentAdminServiceServer()
}

// UnimplementedAppointmentAdminServiceServer must be embedded to have forward compatible implementations.
type UnimplementedAppointmentAdminServiceServer struct {
}

func (UnimplementedAppointmentAdminServiceServer) QueryAppointmentList(context.Context, *QueryAppointmentListParams) (*QueryAppointmentListResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryAppointmentList not implemented")
}
func (UnimplementedAppointmentAdminServiceServer) DeleteAppointment(context.Context, *DeleteAppointmentParams) (*DeleteAppointmentResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteAppointment not implemented")
}
func (UnimplementedAppointmentAdminServiceServer) mustEmbedUnimplementedAppointmentAdminServiceServer() {
}

// UnsafeAppointmentAdminServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to AppointmentAdminServiceServer will
// result in compilation errors.
type UnsafeAppointmentAdminServiceServer interface {
	mustEmbedUnimplementedAppointmentAdminServiceServer()
}

func RegisterAppointmentAdminServiceServer(s grpc.ServiceRegistrar, srv AppointmentAdminServiceServer) {
	s.RegisterService(&AppointmentAdminService_ServiceDesc, srv)
}

func _AppointmentAdminService_QueryAppointmentList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryAppointmentListParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppointmentAdminServiceServer).QueryAppointmentList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.admin.appointment.v1.AppointmentAdminService/QueryAppointmentList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppointmentAdminServiceServer).QueryAppointmentList(ctx, req.(*QueryAppointmentListParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _AppointmentAdminService_DeleteAppointment_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteAppointmentParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppointmentAdminServiceServer).DeleteAppointment(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.admin.appointment.v1.AppointmentAdminService/DeleteAppointment",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppointmentAdminServiceServer).DeleteAppointment(ctx, req.(*DeleteAppointmentParams))
	}
	return interceptor(ctx, in, info, handler)
}

// AppointmentAdminService_ServiceDesc is the grpc.ServiceDesc for AppointmentAdminService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var AppointmentAdminService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.admin.appointment.v1.AppointmentAdminService",
	HandlerType: (*AppointmentAdminServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "QueryAppointmentList",
			Handler:    _AppointmentAdminService_QueryAppointmentList_Handler,
		},
		{
			MethodName: "DeleteAppointment",
			Handler:    _AppointmentAdminService_DeleteAppointment_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/admin/appointment/v1/appointment_admin.proto",
}
