// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/admin/pay_ops/v1/payout_whitlist_control.proto

package payopsapipb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// PayoutWhitelistControlServiceClient is the client API for PayoutWhitelistControlService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type PayoutWhitelistControlServiceClient interface {
	// get payout whitelist control list
	GetCompanyFeatureRelationList(ctx context.Context, in *CompanyFeatureRelationGetParams, opts ...grpc.CallOption) (*CompanyFeatureRelationListResult, error)
	// create payout whitelist control
	CreateCompanyFeatureRelation(ctx context.Context, in *CreateCompanyFeatureRelationParams, opts ...grpc.CallOption) (*CreateCompanyFeatureRelationResult, error)
	// delete payout whitelist control
	DeletedCompanyFeatureRelation(ctx context.Context, in *DeletedCompanyFeatureRelationParams, opts ...grpc.CallOption) (*DeletedCompanyFeatureRelationResult, error)
}

type payoutWhitelistControlServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewPayoutWhitelistControlServiceClient(cc grpc.ClientConnInterface) PayoutWhitelistControlServiceClient {
	return &payoutWhitelistControlServiceClient{cc}
}

func (c *payoutWhitelistControlServiceClient) GetCompanyFeatureRelationList(ctx context.Context, in *CompanyFeatureRelationGetParams, opts ...grpc.CallOption) (*CompanyFeatureRelationListResult, error) {
	out := new(CompanyFeatureRelationListResult)
	err := c.cc.Invoke(ctx, "/moego.admin.pay_ops.v1.PayoutWhitelistControlService/GetCompanyFeatureRelationList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *payoutWhitelistControlServiceClient) CreateCompanyFeatureRelation(ctx context.Context, in *CreateCompanyFeatureRelationParams, opts ...grpc.CallOption) (*CreateCompanyFeatureRelationResult, error) {
	out := new(CreateCompanyFeatureRelationResult)
	err := c.cc.Invoke(ctx, "/moego.admin.pay_ops.v1.PayoutWhitelistControlService/CreateCompanyFeatureRelation", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *payoutWhitelistControlServiceClient) DeletedCompanyFeatureRelation(ctx context.Context, in *DeletedCompanyFeatureRelationParams, opts ...grpc.CallOption) (*DeletedCompanyFeatureRelationResult, error) {
	out := new(DeletedCompanyFeatureRelationResult)
	err := c.cc.Invoke(ctx, "/moego.admin.pay_ops.v1.PayoutWhitelistControlService/DeletedCompanyFeatureRelation", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// PayoutWhitelistControlServiceServer is the server API for PayoutWhitelistControlService service.
// All implementations must embed UnimplementedPayoutWhitelistControlServiceServer
// for forward compatibility
type PayoutWhitelistControlServiceServer interface {
	// get payout whitelist control list
	GetCompanyFeatureRelationList(context.Context, *CompanyFeatureRelationGetParams) (*CompanyFeatureRelationListResult, error)
	// create payout whitelist control
	CreateCompanyFeatureRelation(context.Context, *CreateCompanyFeatureRelationParams) (*CreateCompanyFeatureRelationResult, error)
	// delete payout whitelist control
	DeletedCompanyFeatureRelation(context.Context, *DeletedCompanyFeatureRelationParams) (*DeletedCompanyFeatureRelationResult, error)
	mustEmbedUnimplementedPayoutWhitelistControlServiceServer()
}

// UnimplementedPayoutWhitelistControlServiceServer must be embedded to have forward compatible implementations.
type UnimplementedPayoutWhitelistControlServiceServer struct {
}

func (UnimplementedPayoutWhitelistControlServiceServer) GetCompanyFeatureRelationList(context.Context, *CompanyFeatureRelationGetParams) (*CompanyFeatureRelationListResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCompanyFeatureRelationList not implemented")
}
func (UnimplementedPayoutWhitelistControlServiceServer) CreateCompanyFeatureRelation(context.Context, *CreateCompanyFeatureRelationParams) (*CreateCompanyFeatureRelationResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateCompanyFeatureRelation not implemented")
}
func (UnimplementedPayoutWhitelistControlServiceServer) DeletedCompanyFeatureRelation(context.Context, *DeletedCompanyFeatureRelationParams) (*DeletedCompanyFeatureRelationResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeletedCompanyFeatureRelation not implemented")
}
func (UnimplementedPayoutWhitelistControlServiceServer) mustEmbedUnimplementedPayoutWhitelistControlServiceServer() {
}

// UnsafePayoutWhitelistControlServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to PayoutWhitelistControlServiceServer will
// result in compilation errors.
type UnsafePayoutWhitelistControlServiceServer interface {
	mustEmbedUnimplementedPayoutWhitelistControlServiceServer()
}

func RegisterPayoutWhitelistControlServiceServer(s grpc.ServiceRegistrar, srv PayoutWhitelistControlServiceServer) {
	s.RegisterService(&PayoutWhitelistControlService_ServiceDesc, srv)
}

func _PayoutWhitelistControlService_GetCompanyFeatureRelationList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CompanyFeatureRelationGetParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PayoutWhitelistControlServiceServer).GetCompanyFeatureRelationList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.admin.pay_ops.v1.PayoutWhitelistControlService/GetCompanyFeatureRelationList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PayoutWhitelistControlServiceServer).GetCompanyFeatureRelationList(ctx, req.(*CompanyFeatureRelationGetParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _PayoutWhitelistControlService_CreateCompanyFeatureRelation_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateCompanyFeatureRelationParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PayoutWhitelistControlServiceServer).CreateCompanyFeatureRelation(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.admin.pay_ops.v1.PayoutWhitelistControlService/CreateCompanyFeatureRelation",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PayoutWhitelistControlServiceServer).CreateCompanyFeatureRelation(ctx, req.(*CreateCompanyFeatureRelationParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _PayoutWhitelistControlService_DeletedCompanyFeatureRelation_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeletedCompanyFeatureRelationParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PayoutWhitelistControlServiceServer).DeletedCompanyFeatureRelation(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.admin.pay_ops.v1.PayoutWhitelistControlService/DeletedCompanyFeatureRelation",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PayoutWhitelistControlServiceServer).DeletedCompanyFeatureRelation(ctx, req.(*DeletedCompanyFeatureRelationParams))
	}
	return interceptor(ctx, in, info, handler)
}

// PayoutWhitelistControlService_ServiceDesc is the grpc.ServiceDesc for PayoutWhitelistControlService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var PayoutWhitelistControlService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.admin.pay_ops.v1.PayoutWhitelistControlService",
	HandlerType: (*PayoutWhitelistControlServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetCompanyFeatureRelationList",
			Handler:    _PayoutWhitelistControlService_GetCompanyFeatureRelationList_Handler,
		},
		{
			MethodName: "CreateCompanyFeatureRelation",
			Handler:    _PayoutWhitelistControlService_CreateCompanyFeatureRelation_Handler,
		},
		{
			MethodName: "DeletedCompanyFeatureRelation",
			Handler:    _PayoutWhitelistControlService_DeletedCompanyFeatureRelation_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/admin/pay_ops/v1/payout_whitlist_control.proto",
}
