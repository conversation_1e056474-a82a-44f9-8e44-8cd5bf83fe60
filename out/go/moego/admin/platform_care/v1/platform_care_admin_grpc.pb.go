// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/admin/platform_care/v1/platform_care_admin.proto

package platformcareapipb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// PlatformCareServiceClient is the client API for PlatformCareService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type PlatformCareServiceClient interface {
	// create platform care link
	CreatePlatformCareLink(ctx context.Context, in *CreatePlatformCareLinkParams, opts ...grpc.CallOption) (*CreatePlatformCareLinkResult, error)
	// get platform care record
	GetPlatformCareRecordByCode(ctx context.Context, in *GetPlatformCareRecordByCodeParams, opts ...grpc.CallOption) (*GetPlatformCareRecordByCodeResult, error)
	// add platform care record
	AddPlatformCareRecord(ctx context.Context, in *AddPlatformCareRecordParams, opts ...grpc.CallOption) (*AddPlatformCareRecordResult, error)
	// get platform care record list
	GetPlatformCareRecordList(ctx context.Context, in *GetPlatformCareRecordListParams, opts ...grpc.CallOption) (*GetPlatformCareRecordListResult, error)
	// update platform care record
	UpdatePlatformCareRecord(ctx context.Context, in *UpdatePlatformCareRecordParams, opts ...grpc.CallOption) (*UpdatePlatformCareRecordResult, error)
	// delete platform care record
	DeletePlatformCareRecord(ctx context.Context, in *DeletePlatformCareRecordParams, opts ...grpc.CallOption) (*DeletePlatformCareRecordResult, error)
}

type platformCareServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewPlatformCareServiceClient(cc grpc.ClientConnInterface) PlatformCareServiceClient {
	return &platformCareServiceClient{cc}
}

func (c *platformCareServiceClient) CreatePlatformCareLink(ctx context.Context, in *CreatePlatformCareLinkParams, opts ...grpc.CallOption) (*CreatePlatformCareLinkResult, error) {
	out := new(CreatePlatformCareLinkResult)
	err := c.cc.Invoke(ctx, "/moego.admin.platform_care.v1.PlatformCareService/CreatePlatformCareLink", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *platformCareServiceClient) GetPlatformCareRecordByCode(ctx context.Context, in *GetPlatformCareRecordByCodeParams, opts ...grpc.CallOption) (*GetPlatformCareRecordByCodeResult, error) {
	out := new(GetPlatformCareRecordByCodeResult)
	err := c.cc.Invoke(ctx, "/moego.admin.platform_care.v1.PlatformCareService/GetPlatformCareRecordByCode", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *platformCareServiceClient) AddPlatformCareRecord(ctx context.Context, in *AddPlatformCareRecordParams, opts ...grpc.CallOption) (*AddPlatformCareRecordResult, error) {
	out := new(AddPlatformCareRecordResult)
	err := c.cc.Invoke(ctx, "/moego.admin.platform_care.v1.PlatformCareService/AddPlatformCareRecord", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *platformCareServiceClient) GetPlatformCareRecordList(ctx context.Context, in *GetPlatformCareRecordListParams, opts ...grpc.CallOption) (*GetPlatformCareRecordListResult, error) {
	out := new(GetPlatformCareRecordListResult)
	err := c.cc.Invoke(ctx, "/moego.admin.platform_care.v1.PlatformCareService/GetPlatformCareRecordList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *platformCareServiceClient) UpdatePlatformCareRecord(ctx context.Context, in *UpdatePlatformCareRecordParams, opts ...grpc.CallOption) (*UpdatePlatformCareRecordResult, error) {
	out := new(UpdatePlatformCareRecordResult)
	err := c.cc.Invoke(ctx, "/moego.admin.platform_care.v1.PlatformCareService/UpdatePlatformCareRecord", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *platformCareServiceClient) DeletePlatformCareRecord(ctx context.Context, in *DeletePlatformCareRecordParams, opts ...grpc.CallOption) (*DeletePlatformCareRecordResult, error) {
	out := new(DeletePlatformCareRecordResult)
	err := c.cc.Invoke(ctx, "/moego.admin.platform_care.v1.PlatformCareService/DeletePlatformCareRecord", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// PlatformCareServiceServer is the server API for PlatformCareService service.
// All implementations must embed UnimplementedPlatformCareServiceServer
// for forward compatibility
type PlatformCareServiceServer interface {
	// create platform care link
	CreatePlatformCareLink(context.Context, *CreatePlatformCareLinkParams) (*CreatePlatformCareLinkResult, error)
	// get platform care record
	GetPlatformCareRecordByCode(context.Context, *GetPlatformCareRecordByCodeParams) (*GetPlatformCareRecordByCodeResult, error)
	// add platform care record
	AddPlatformCareRecord(context.Context, *AddPlatformCareRecordParams) (*AddPlatformCareRecordResult, error)
	// get platform care record list
	GetPlatformCareRecordList(context.Context, *GetPlatformCareRecordListParams) (*GetPlatformCareRecordListResult, error)
	// update platform care record
	UpdatePlatformCareRecord(context.Context, *UpdatePlatformCareRecordParams) (*UpdatePlatformCareRecordResult, error)
	// delete platform care record
	DeletePlatformCareRecord(context.Context, *DeletePlatformCareRecordParams) (*DeletePlatformCareRecordResult, error)
	mustEmbedUnimplementedPlatformCareServiceServer()
}

// UnimplementedPlatformCareServiceServer must be embedded to have forward compatible implementations.
type UnimplementedPlatformCareServiceServer struct {
}

func (UnimplementedPlatformCareServiceServer) CreatePlatformCareLink(context.Context, *CreatePlatformCareLinkParams) (*CreatePlatformCareLinkResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreatePlatformCareLink not implemented")
}
func (UnimplementedPlatformCareServiceServer) GetPlatformCareRecordByCode(context.Context, *GetPlatformCareRecordByCodeParams) (*GetPlatformCareRecordByCodeResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPlatformCareRecordByCode not implemented")
}
func (UnimplementedPlatformCareServiceServer) AddPlatformCareRecord(context.Context, *AddPlatformCareRecordParams) (*AddPlatformCareRecordResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddPlatformCareRecord not implemented")
}
func (UnimplementedPlatformCareServiceServer) GetPlatformCareRecordList(context.Context, *GetPlatformCareRecordListParams) (*GetPlatformCareRecordListResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPlatformCareRecordList not implemented")
}
func (UnimplementedPlatformCareServiceServer) UpdatePlatformCareRecord(context.Context, *UpdatePlatformCareRecordParams) (*UpdatePlatformCareRecordResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdatePlatformCareRecord not implemented")
}
func (UnimplementedPlatformCareServiceServer) DeletePlatformCareRecord(context.Context, *DeletePlatformCareRecordParams) (*DeletePlatformCareRecordResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeletePlatformCareRecord not implemented")
}
func (UnimplementedPlatformCareServiceServer) mustEmbedUnimplementedPlatformCareServiceServer() {}

// UnsafePlatformCareServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to PlatformCareServiceServer will
// result in compilation errors.
type UnsafePlatformCareServiceServer interface {
	mustEmbedUnimplementedPlatformCareServiceServer()
}

func RegisterPlatformCareServiceServer(s grpc.ServiceRegistrar, srv PlatformCareServiceServer) {
	s.RegisterService(&PlatformCareService_ServiceDesc, srv)
}

func _PlatformCareService_CreatePlatformCareLink_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreatePlatformCareLinkParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PlatformCareServiceServer).CreatePlatformCareLink(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.admin.platform_care.v1.PlatformCareService/CreatePlatformCareLink",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PlatformCareServiceServer).CreatePlatformCareLink(ctx, req.(*CreatePlatformCareLinkParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _PlatformCareService_GetPlatformCareRecordByCode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPlatformCareRecordByCodeParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PlatformCareServiceServer).GetPlatformCareRecordByCode(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.admin.platform_care.v1.PlatformCareService/GetPlatformCareRecordByCode",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PlatformCareServiceServer).GetPlatformCareRecordByCode(ctx, req.(*GetPlatformCareRecordByCodeParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _PlatformCareService_AddPlatformCareRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddPlatformCareRecordParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PlatformCareServiceServer).AddPlatformCareRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.admin.platform_care.v1.PlatformCareService/AddPlatformCareRecord",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PlatformCareServiceServer).AddPlatformCareRecord(ctx, req.(*AddPlatformCareRecordParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _PlatformCareService_GetPlatformCareRecordList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPlatformCareRecordListParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PlatformCareServiceServer).GetPlatformCareRecordList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.admin.platform_care.v1.PlatformCareService/GetPlatformCareRecordList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PlatformCareServiceServer).GetPlatformCareRecordList(ctx, req.(*GetPlatformCareRecordListParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _PlatformCareService_UpdatePlatformCareRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdatePlatformCareRecordParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PlatformCareServiceServer).UpdatePlatformCareRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.admin.platform_care.v1.PlatformCareService/UpdatePlatformCareRecord",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PlatformCareServiceServer).UpdatePlatformCareRecord(ctx, req.(*UpdatePlatformCareRecordParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _PlatformCareService_DeletePlatformCareRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeletePlatformCareRecordParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PlatformCareServiceServer).DeletePlatformCareRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.admin.platform_care.v1.PlatformCareService/DeletePlatformCareRecord",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PlatformCareServiceServer).DeletePlatformCareRecord(ctx, req.(*DeletePlatformCareRecordParams))
	}
	return interceptor(ctx, in, info, handler)
}

// PlatformCareService_ServiceDesc is the grpc.ServiceDesc for PlatformCareService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var PlatformCareService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.admin.platform_care.v1.PlatformCareService",
	HandlerType: (*PlatformCareServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreatePlatformCareLink",
			Handler:    _PlatformCareService_CreatePlatformCareLink_Handler,
		},
		{
			MethodName: "GetPlatformCareRecordByCode",
			Handler:    _PlatformCareService_GetPlatformCareRecordByCode_Handler,
		},
		{
			MethodName: "AddPlatformCareRecord",
			Handler:    _PlatformCareService_AddPlatformCareRecord_Handler,
		},
		{
			MethodName: "GetPlatformCareRecordList",
			Handler:    _PlatformCareService_GetPlatformCareRecordList_Handler,
		},
		{
			MethodName: "UpdatePlatformCareRecord",
			Handler:    _PlatformCareService_UpdatePlatformCareRecord_Handler,
		},
		{
			MethodName: "DeletePlatformCareRecord",
			Handler:    _PlatformCareService_DeletePlatformCareRecord_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/admin/platform_care/v1/platform_care_admin.proto",
}
