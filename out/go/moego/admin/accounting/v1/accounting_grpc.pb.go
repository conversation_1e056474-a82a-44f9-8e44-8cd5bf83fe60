// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/admin/accounting/v1/accounting.proto

package accountingapipb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// AccountingServiceClient is the client API for AccountingService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type AccountingServiceClient interface {
	// compensate
	Compensate(ctx context.Context, in *CompensateRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
}

type accountingServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewAccountingServiceClient(cc grpc.ClientConnInterface) AccountingServiceClient {
	return &accountingServiceClient{cc}
}

func (c *accountingServiceClient) Compensate(ctx context.Context, in *CompensateRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, "/moego.admin.accounting.v1.AccountingService/Compensate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AccountingServiceServer is the server API for AccountingService service.
// All implementations must embed UnimplementedAccountingServiceServer
// for forward compatibility
type AccountingServiceServer interface {
	// compensate
	Compensate(context.Context, *CompensateRequest) (*emptypb.Empty, error)
	mustEmbedUnimplementedAccountingServiceServer()
}

// UnimplementedAccountingServiceServer must be embedded to have forward compatible implementations.
type UnimplementedAccountingServiceServer struct {
}

func (UnimplementedAccountingServiceServer) Compensate(context.Context, *CompensateRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Compensate not implemented")
}
func (UnimplementedAccountingServiceServer) mustEmbedUnimplementedAccountingServiceServer() {}

// UnsafeAccountingServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to AccountingServiceServer will
// result in compilation errors.
type UnsafeAccountingServiceServer interface {
	mustEmbedUnimplementedAccountingServiceServer()
}

func RegisterAccountingServiceServer(s grpc.ServiceRegistrar, srv AccountingServiceServer) {
	s.RegisterService(&AccountingService_ServiceDesc, srv)
}

func _AccountingService_Compensate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CompensateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccountingServiceServer).Compensate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.admin.accounting.v1.AccountingService/Compensate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccountingServiceServer).Compensate(ctx, req.(*CompensateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// AccountingService_ServiceDesc is the grpc.ServiceDesc for AccountingService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var AccountingService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.admin.accounting.v1.AccountingService",
	HandlerType: (*AccountingServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Compensate",
			Handler:    _AccountingService_Compensate_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/admin/accounting/v1/accounting.proto",
}
