// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/admin/common/v1/common_admin.proto

package commonapipb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// CommonServiceClient is the client API for CommonService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type CommonServiceClient interface {
	// describe enums
	DescribeEnums(ctx context.Context, in *DescribeEnumsParams, opts ...grpc.CallOption) (*DescribeEnumsResult, error)
}

type commonServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewCommonServiceClient(cc grpc.ClientConnInterface) CommonServiceClient {
	return &commonServiceClient{cc}
}

func (c *commonServiceClient) DescribeEnums(ctx context.Context, in *DescribeEnumsParams, opts ...grpc.CallOption) (*DescribeEnumsResult, error) {
	out := new(DescribeEnumsResult)
	err := c.cc.Invoke(ctx, "/moego.admin.common.v1.CommonService/DescribeEnums", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// CommonServiceServer is the server API for CommonService service.
// All implementations must embed UnimplementedCommonServiceServer
// for forward compatibility
type CommonServiceServer interface {
	// describe enums
	DescribeEnums(context.Context, *DescribeEnumsParams) (*DescribeEnumsResult, error)
	mustEmbedUnimplementedCommonServiceServer()
}

// UnimplementedCommonServiceServer must be embedded to have forward compatible implementations.
type UnimplementedCommonServiceServer struct {
}

func (UnimplementedCommonServiceServer) DescribeEnums(context.Context, *DescribeEnumsParams) (*DescribeEnumsResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DescribeEnums not implemented")
}
func (UnimplementedCommonServiceServer) mustEmbedUnimplementedCommonServiceServer() {}

// UnsafeCommonServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to CommonServiceServer will
// result in compilation errors.
type UnsafeCommonServiceServer interface {
	mustEmbedUnimplementedCommonServiceServer()
}

func RegisterCommonServiceServer(s grpc.ServiceRegistrar, srv CommonServiceServer) {
	s.RegisterService(&CommonService_ServiceDesc, srv)
}

func _CommonService_DescribeEnums_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DescribeEnumsParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CommonServiceServer).DescribeEnums(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.admin.common.v1.CommonService/DescribeEnums",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CommonServiceServer).DescribeEnums(ctx, req.(*DescribeEnumsParams))
	}
	return interceptor(ctx, in, info, handler)
}

// CommonService_ServiceDesc is the grpc.ServiceDesc for CommonService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var CommonService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.admin.common.v1.CommonService",
	HandlerType: (*CommonServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "DescribeEnums",
			Handler:    _CommonService_DescribeEnums_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/admin/common/v1/common_admin.proto",
}
