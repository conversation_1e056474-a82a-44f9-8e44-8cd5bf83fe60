// @since 2025-04-07 15:32:56
// <AUTHOR> <<EMAIL>>

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/fulfillment/v1/group_class_detail_models.proto

package fulfillmentpb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// The group class status
type GroupClassDetailModel_Status int32

const (
	// Unspecified
	GroupClassDetailModel_STATUS_UNSPECIFIED GroupClassDetailModel_Status = 0
	// Not started
	GroupClassDetailModel_NOT_STARTED GroupClassDetailModel_Status = 1
	// In progress
	GroupClassDetailModel_IN_PROGRESS GroupClassDetailModel_Status = 2
	// Completed
	GroupClassDetailModel_COMPLETED GroupClassDetailModel_Status = 3
)

// Enum value maps for GroupClassDetailModel_Status.
var (
	GroupClassDetailModel_Status_name = map[int32]string{
		0: "STATUS_UNSPECIFIED",
		1: "NOT_STARTED",
		2: "IN_PROGRESS",
		3: "COMPLETED",
	}
	GroupClassDetailModel_Status_value = map[string]int32{
		"STATUS_UNSPECIFIED": 0,
		"NOT_STARTED":        1,
		"IN_PROGRESS":        2,
		"COMPLETED":          3,
	}
)

func (x GroupClassDetailModel_Status) Enum() *GroupClassDetailModel_Status {
	p := new(GroupClassDetailModel_Status)
	*p = x
	return p
}

func (x GroupClassDetailModel_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GroupClassDetailModel_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_fulfillment_v1_group_class_detail_models_proto_enumTypes[0].Descriptor()
}

func (GroupClassDetailModel_Status) Type() protoreflect.EnumType {
	return &file_moego_models_fulfillment_v1_group_class_detail_models_proto_enumTypes[0]
}

func (x GroupClassDetailModel_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GroupClassDetailModel_Status.Descriptor instead.
func (GroupClassDetailModel_Status) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_fulfillment_v1_group_class_detail_models_proto_rawDescGZIP(), []int{0, 0}
}

// The GroupClassDetail model
type GroupClassDetailModel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The unique identifier of the model
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// The fulfillment id
	FulfillmentId int64 `protobuf:"varint,2,opt,name=fulfillment_id,json=fulfillmentId,proto3" json:"fulfillment_id,omitempty"`
	// The pet id
	PetId int64 `protobuf:"varint,3,opt,name=pet_id,json=petId,proto3" json:"pet_id,omitempty"`
	// The group class id, service id
	GroupClassId int64 `protobuf:"varint,4,opt,name=group_class_id,json=groupClassId,proto3" json:"group_class_id,omitempty"`
	// The group class instance id
	GroupClassInstanceId int64 `protobuf:"varint,5,opt,name=group_class_instance_id,json=groupClassInstanceId,proto3" json:"group_class_instance_id,omitempty"`
	// The group class status
	Status GroupClassDetailModel_Status `protobuf:"varint,7,opt,name=status,proto3,enum=moego.models.fulfillment.v1.GroupClassDetailModel_Status" json:"status,omitempty"`
	// the create time
	CreatedAt *timestamppb.Timestamp `protobuf:"bytes,13,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	// the update time
	UpdatedAt *timestamppb.Timestamp `protobuf:"bytes,14,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	// the delete time, non-null means is deleted
	DeletedAt *timestamppb.Timestamp `protobuf:"bytes,15,opt,name=deleted_at,json=deletedAt,proto3,oneof" json:"deleted_at,omitempty"`
}

func (x *GroupClassDetailModel) Reset() {
	*x = GroupClassDetailModel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_fulfillment_v1_group_class_detail_models_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GroupClassDetailModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GroupClassDetailModel) ProtoMessage() {}

func (x *GroupClassDetailModel) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_fulfillment_v1_group_class_detail_models_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GroupClassDetailModel.ProtoReflect.Descriptor instead.
func (*GroupClassDetailModel) Descriptor() ([]byte, []int) {
	return file_moego_models_fulfillment_v1_group_class_detail_models_proto_rawDescGZIP(), []int{0}
}

func (x *GroupClassDetailModel) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *GroupClassDetailModel) GetFulfillmentId() int64 {
	if x != nil {
		return x.FulfillmentId
	}
	return 0
}

func (x *GroupClassDetailModel) GetPetId() int64 {
	if x != nil {
		return x.PetId
	}
	return 0
}

func (x *GroupClassDetailModel) GetGroupClassId() int64 {
	if x != nil {
		return x.GroupClassId
	}
	return 0
}

func (x *GroupClassDetailModel) GetGroupClassInstanceId() int64 {
	if x != nil {
		return x.GroupClassInstanceId
	}
	return 0
}

func (x *GroupClassDetailModel) GetStatus() GroupClassDetailModel_Status {
	if x != nil {
		return x.Status
	}
	return GroupClassDetailModel_STATUS_UNSPECIFIED
}

func (x *GroupClassDetailModel) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *GroupClassDetailModel) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *GroupClassDetailModel) GetDeletedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.DeletedAt
	}
	return nil
}

var File_moego_models_fulfillment_v1_group_class_detail_models_proto protoreflect.FileDescriptor

var file_moego_models_fulfillment_v1_group_class_detail_models_proto_rawDesc = []byte{
	0x0a, 0x3b, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x66,
	0x75, 0x6c, 0x66, 0x69, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x67, 0x72,
	0x6f, 0x75, 0x70, 0x5f, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1b, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x66, 0x75, 0x6c, 0x66,
	0x69, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xad, 0x04, 0x0a, 0x15,
	0x47, 0x72, 0x6f, 0x75, 0x70, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x25, 0x0a, 0x0e, 0x66, 0x75, 0x6c, 0x66, 0x69, 0x6c, 0x6c,
	0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x66,
	0x75, 0x6c, 0x66, 0x69, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x15, 0x0a, 0x06,
	0x70, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x70, 0x65,
	0x74, 0x49, 0x64, 0x12, 0x24, 0x0a, 0x0e, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x63, 0x6c, 0x61,
	0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x67, 0x72, 0x6f,
	0x75, 0x70, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x49, 0x64, 0x12, 0x35, 0x0a, 0x17, 0x67, 0x72, 0x6f,
	0x75, 0x70, 0x5f, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x5f, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63,
	0x65, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x14, 0x67, 0x72, 0x6f, 0x75,
	0x70, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x49, 0x64,
	0x12, 0x51, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x39, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x66, 0x75, 0x6c, 0x66, 0x69, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47,
	0x72, 0x6f, 0x75, 0x70, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x4d,
	0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61,
	0x74, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39,
	0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0e, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09,
	0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x3e, 0x0a, 0x0a, 0x64, 0x65, 0x6c,
	0x65, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x48, 0x00, 0x52, 0x09, 0x64, 0x65, 0x6c,
	0x65, 0x74, 0x65, 0x64, 0x41, 0x74, 0x88, 0x01, 0x01, 0x22, 0x51, 0x0a, 0x06, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x16, 0x0a, 0x12, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e,
	0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0f, 0x0a, 0x0b, 0x4e,
	0x4f, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x52, 0x54, 0x45, 0x44, 0x10, 0x01, 0x12, 0x0f, 0x0a, 0x0b,
	0x49, 0x4e, 0x5f, 0x50, 0x52, 0x4f, 0x47, 0x52, 0x45, 0x53, 0x53, 0x10, 0x02, 0x12, 0x0d, 0x0a,
	0x09, 0x43, 0x4f, 0x4d, 0x50, 0x4c, 0x45, 0x54, 0x45, 0x44, 0x10, 0x03, 0x42, 0x0d, 0x0a, 0x0b,
	0x5f, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x42, 0x87, 0x01, 0x0a, 0x23,
	0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x66, 0x75, 0x6c, 0x66, 0x69, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x5e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f,
	0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x66, 0x75, 0x6c, 0x66, 0x69, 0x6c, 0x6c,
	0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x3b, 0x66, 0x75, 0x6c, 0x66, 0x69, 0x6c, 0x6c, 0x6d,
	0x65, 0x6e, 0x74, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_fulfillment_v1_group_class_detail_models_proto_rawDescOnce sync.Once
	file_moego_models_fulfillment_v1_group_class_detail_models_proto_rawDescData = file_moego_models_fulfillment_v1_group_class_detail_models_proto_rawDesc
)

func file_moego_models_fulfillment_v1_group_class_detail_models_proto_rawDescGZIP() []byte {
	file_moego_models_fulfillment_v1_group_class_detail_models_proto_rawDescOnce.Do(func() {
		file_moego_models_fulfillment_v1_group_class_detail_models_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_fulfillment_v1_group_class_detail_models_proto_rawDescData)
	})
	return file_moego_models_fulfillment_v1_group_class_detail_models_proto_rawDescData
}

var file_moego_models_fulfillment_v1_group_class_detail_models_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_moego_models_fulfillment_v1_group_class_detail_models_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_moego_models_fulfillment_v1_group_class_detail_models_proto_goTypes = []interface{}{
	(GroupClassDetailModel_Status)(0), // 0: moego.models.fulfillment.v1.GroupClassDetailModel.Status
	(*GroupClassDetailModel)(nil),     // 1: moego.models.fulfillment.v1.GroupClassDetailModel
	(*timestamppb.Timestamp)(nil),     // 2: google.protobuf.Timestamp
}
var file_moego_models_fulfillment_v1_group_class_detail_models_proto_depIdxs = []int32{
	0, // 0: moego.models.fulfillment.v1.GroupClassDetailModel.status:type_name -> moego.models.fulfillment.v1.GroupClassDetailModel.Status
	2, // 1: moego.models.fulfillment.v1.GroupClassDetailModel.created_at:type_name -> google.protobuf.Timestamp
	2, // 2: moego.models.fulfillment.v1.GroupClassDetailModel.updated_at:type_name -> google.protobuf.Timestamp
	2, // 3: moego.models.fulfillment.v1.GroupClassDetailModel.deleted_at:type_name -> google.protobuf.Timestamp
	4, // [4:4] is the sub-list for method output_type
	4, // [4:4] is the sub-list for method input_type
	4, // [4:4] is the sub-list for extension type_name
	4, // [4:4] is the sub-list for extension extendee
	0, // [0:4] is the sub-list for field type_name
}

func init() { file_moego_models_fulfillment_v1_group_class_detail_models_proto_init() }
func file_moego_models_fulfillment_v1_group_class_detail_models_proto_init() {
	if File_moego_models_fulfillment_v1_group_class_detail_models_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_models_fulfillment_v1_group_class_detail_models_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GroupClassDetailModel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_models_fulfillment_v1_group_class_detail_models_proto_msgTypes[0].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_fulfillment_v1_group_class_detail_models_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_fulfillment_v1_group_class_detail_models_proto_goTypes,
		DependencyIndexes: file_moego_models_fulfillment_v1_group_class_detail_models_proto_depIdxs,
		EnumInfos:         file_moego_models_fulfillment_v1_group_class_detail_models_proto_enumTypes,
		MessageInfos:      file_moego_models_fulfillment_v1_group_class_detail_models_proto_msgTypes,
	}.Build()
	File_moego_models_fulfillment_v1_group_class_detail_models_proto = out.File
	file_moego_models_fulfillment_v1_group_class_detail_models_proto_rawDesc = nil
	file_moego_models_fulfillment_v1_group_class_detail_models_proto_goTypes = nil
	file_moego_models_fulfillment_v1_group_class_detail_models_proto_depIdxs = nil
}
