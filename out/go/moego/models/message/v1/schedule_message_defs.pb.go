// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/message/v1/schedule_message_defs.proto

package messagepb

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Custom content in schedule message model
type ScheduleMessageCustomDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// custom content, the message manually entered by the user in the input box
	Content string `protobuf:"bytes,1,opt,name=content,proto3" json:"content,omitempty"`
	// receipt customer's contact id. If not specified, it will be sent to the primary phone by default.
	ReceiptContactId *int64 `protobuf:"varint,2,opt,name=receipt_contact_id,json=receiptContactId,proto3,oneof" json:"receipt_contact_id,omitempty"`
}

func (x *ScheduleMessageCustomDef) Reset() {
	*x = ScheduleMessageCustomDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_message_v1_schedule_message_defs_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ScheduleMessageCustomDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ScheduleMessageCustomDef) ProtoMessage() {}

func (x *ScheduleMessageCustomDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_message_v1_schedule_message_defs_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ScheduleMessageCustomDef.ProtoReflect.Descriptor instead.
func (*ScheduleMessageCustomDef) Descriptor() ([]byte, []int) {
	return file_moego_models_message_v1_schedule_message_defs_proto_rawDescGZIP(), []int{0}
}

func (x *ScheduleMessageCustomDef) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *ScheduleMessageCustomDef) GetReceiptContactId() int64 {
	if x != nil && x.ReceiptContactId != nil {
		return *x.ReceiptContactId
	}
	return 0
}

var File_moego_models_message_v1_schedule_message_defs_proto protoreflect.FileDescriptor

var file_moego_models_message_v1_schedule_message_defs_proto_rawDesc = []byte{
	0x0a, 0x33, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75,
	0x6c, 0x65, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x5f, 0x64, 0x65, 0x66, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x17, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x76, 0x31, 0x1a, 0x17,
	0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x91, 0x01, 0x0a, 0x18, 0x53, 0x63, 0x68, 0x65,
	0x64, 0x75, 0x6c, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x43, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x44, 0x65, 0x66, 0x12, 0x22, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x72, 0x03, 0x18, 0x80, 0x10, 0x52,
	0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x3a, 0x0a, 0x12, 0x72, 0x65, 0x63, 0x65,
	0x69, 0x70, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x48, 0x00, 0x52,
	0x10, 0x72, 0x65, 0x63, 0x65, 0x69, 0x70, 0x74, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x49,
	0x64, 0x88, 0x01, 0x01, 0x42, 0x15, 0x0a, 0x13, 0x5f, 0x72, 0x65, 0x63, 0x65, 0x69, 0x70, 0x74,
	0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x42, 0x7b, 0x0a, 0x1f, 0x63,
	0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x76, 0x31, 0x50, 0x01,
	0x5a, 0x56, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65,
	0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d,
	0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f,
	0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2f, 0x76, 0x31, 0x3b, 0x6d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_message_v1_schedule_message_defs_proto_rawDescOnce sync.Once
	file_moego_models_message_v1_schedule_message_defs_proto_rawDescData = file_moego_models_message_v1_schedule_message_defs_proto_rawDesc
)

func file_moego_models_message_v1_schedule_message_defs_proto_rawDescGZIP() []byte {
	file_moego_models_message_v1_schedule_message_defs_proto_rawDescOnce.Do(func() {
		file_moego_models_message_v1_schedule_message_defs_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_message_v1_schedule_message_defs_proto_rawDescData)
	})
	return file_moego_models_message_v1_schedule_message_defs_proto_rawDescData
}

var file_moego_models_message_v1_schedule_message_defs_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_moego_models_message_v1_schedule_message_defs_proto_goTypes = []interface{}{
	(*ScheduleMessageCustomDef)(nil), // 0: moego.models.message.v1.ScheduleMessageCustomDef
}
var file_moego_models_message_v1_schedule_message_defs_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_moego_models_message_v1_schedule_message_defs_proto_init() }
func file_moego_models_message_v1_schedule_message_defs_proto_init() {
	if File_moego_models_message_v1_schedule_message_defs_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_models_message_v1_schedule_message_defs_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ScheduleMessageCustomDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_models_message_v1_schedule_message_defs_proto_msgTypes[0].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_message_v1_schedule_message_defs_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_message_v1_schedule_message_defs_proto_goTypes,
		DependencyIndexes: file_moego_models_message_v1_schedule_message_defs_proto_depIdxs,
		MessageInfos:      file_moego_models_message_v1_schedule_message_defs_proto_msgTypes,
	}.Build()
	File_moego_models_message_v1_schedule_message_defs_proto = out.File
	file_moego_models_message_v1_schedule_message_defs_proto_rawDesc = nil
	file_moego_models_message_v1_schedule_message_defs_proto_goTypes = nil
	file_moego_models_message_v1_schedule_message_defs_proto_depIdxs = nil
}
