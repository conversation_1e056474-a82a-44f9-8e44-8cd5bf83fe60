// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/payment/v1/event_models.proto

package paymentpb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// payout event model
type PayoutEventModel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 事件类型
	EventType PayoutEventType `protobuf:"varint,1,opt,name=event_type,json=eventType,proto3,enum=moego.models.payment.v1.PayoutEventType" json:"event_type,omitempty"`
	// payout 模型详情
	PayoutModel *PayoutModel `protobuf:"bytes,2,opt,name=payout_model,json=payoutModel,proto3" json:"payout_model,omitempty"`
}

func (x *PayoutEventModel) Reset() {
	*x = PayoutEventModel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_payment_v1_event_models_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PayoutEventModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PayoutEventModel) ProtoMessage() {}

func (x *PayoutEventModel) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_payment_v1_event_models_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PayoutEventModel.ProtoReflect.Descriptor instead.
func (*PayoutEventModel) Descriptor() ([]byte, []int) {
	return file_moego_models_payment_v1_event_models_proto_rawDescGZIP(), []int{0}
}

func (x *PayoutEventModel) GetEventType() PayoutEventType {
	if x != nil {
		return x.EventType
	}
	return PayoutEventType_EVENT_TYPE_UNSPECIFIED
}

func (x *PayoutEventModel) GetPayoutModel() *PayoutModel {
	if x != nil {
		return x.PayoutModel
	}
	return nil
}

var File_moego_models_payment_v1_event_models_proto protoreflect.FileDescriptor

var file_moego_models_payment_v1_event_models_proto_rawDesc = []byte{
	0x0a, 0x2a, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x70,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x5f,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x17, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x76, 0x31, 0x1a, 0x29, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x65,
	0x76, 0x65, 0x6e, 0x74, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x2b, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x70,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x70, 0x61, 0x79, 0x6f, 0x75, 0x74,
	0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xa4, 0x01,
	0x0a, 0x10, 0x50, 0x61, 0x79, 0x6f, 0x75, 0x74, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x4d, 0x6f, 0x64,
	0x65, 0x6c, 0x12, 0x47, 0x0a, 0x0a, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x28, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31,
	0x2e, 0x50, 0x61, 0x79, 0x6f, 0x75, 0x74, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65,
	0x52, 0x09, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x47, 0x0a, 0x0c, 0x70,
	0x61, 0x79, 0x6f, 0x75, 0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x24, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x61, 0x79, 0x6f,
	0x75, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x0b, 0x70, 0x61, 0x79, 0x6f, 0x75, 0x74, 0x4d,
	0x6f, 0x64, 0x65, 0x6c, 0x42, 0x7b, 0x0a, 0x1f, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x56, 0x67, 0x69, 0x74, 0x68, 0x75,
	0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61,
	0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66,
	0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x70, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x3b, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x70,
	0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_payment_v1_event_models_proto_rawDescOnce sync.Once
	file_moego_models_payment_v1_event_models_proto_rawDescData = file_moego_models_payment_v1_event_models_proto_rawDesc
)

func file_moego_models_payment_v1_event_models_proto_rawDescGZIP() []byte {
	file_moego_models_payment_v1_event_models_proto_rawDescOnce.Do(func() {
		file_moego_models_payment_v1_event_models_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_payment_v1_event_models_proto_rawDescData)
	})
	return file_moego_models_payment_v1_event_models_proto_rawDescData
}

var file_moego_models_payment_v1_event_models_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_moego_models_payment_v1_event_models_proto_goTypes = []interface{}{
	(*PayoutEventModel)(nil), // 0: moego.models.payment.v1.PayoutEventModel
	(PayoutEventType)(0),     // 1: moego.models.payment.v1.PayoutEventType
	(*PayoutModel)(nil),      // 2: moego.models.payment.v1.PayoutModel
}
var file_moego_models_payment_v1_event_models_proto_depIdxs = []int32{
	1, // 0: moego.models.payment.v1.PayoutEventModel.event_type:type_name -> moego.models.payment.v1.PayoutEventType
	2, // 1: moego.models.payment.v1.PayoutEventModel.payout_model:type_name -> moego.models.payment.v1.PayoutModel
	2, // [2:2] is the sub-list for method output_type
	2, // [2:2] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_moego_models_payment_v1_event_models_proto_init() }
func file_moego_models_payment_v1_event_models_proto_init() {
	if File_moego_models_payment_v1_event_models_proto != nil {
		return
	}
	file_moego_models_payment_v1_event_enums_proto_init()
	file_moego_models_payment_v1_payout_models_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_moego_models_payment_v1_event_models_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PayoutEventModel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_payment_v1_event_models_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_payment_v1_event_models_proto_goTypes,
		DependencyIndexes: file_moego_models_payment_v1_event_models_proto_depIdxs,
		MessageInfos:      file_moego_models_payment_v1_event_models_proto_msgTypes,
	}.Build()
	File_moego_models_payment_v1_event_models_proto = out.File
	file_moego_models_payment_v1_event_models_proto_rawDesc = nil
	file_moego_models_payment_v1_event_models_proto_goTypes = nil
	file_moego_models_payment_v1_event_models_proto_depIdxs = nil
}
