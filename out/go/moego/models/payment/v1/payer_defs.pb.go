// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/payment/v1/payer_defs.proto

package paymentpb

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// payer
type PayerDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// payer_type
	PayerType PayerType `protobuf:"varint,1,opt,name=payer_type,json=payerType,proto3,enum=moego.models.payment.v1.PayerType" json:"payer_type,omitempty"`
	// payer_id
	PayerId int64 `protobuf:"varint,2,opt,name=payer_id,json=payerId,proto3" json:"payer_id,omitempty"`
}

func (x *PayerDef) Reset() {
	*x = PayerDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_payment_v1_payer_defs_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PayerDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PayerDef) ProtoMessage() {}

func (x *PayerDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_payment_v1_payer_defs_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PayerDef.ProtoReflect.Descriptor instead.
func (*PayerDef) Descriptor() ([]byte, []int) {
	return file_moego_models_payment_v1_payer_defs_proto_rawDescGZIP(), []int{0}
}

func (x *PayerDef) GetPayerType() PayerType {
	if x != nil {
		return x.PayerType
	}
	return PayerType_PAYER_TYPE_UNSPECIFIED
}

func (x *PayerDef) GetPayerId() int64 {
	if x != nil {
		return x.PayerId
	}
	return 0
}

// bill payer
type BillPayerSettingDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// bill type
	BillType BillType `protobuf:"varint,1,opt,name=bill_type,json=billType,proto3,enum=moego.models.payment.v1.BillType" json:"bill_type,omitempty"`
	// payer
	Payer *PayerDef `protobuf:"bytes,2,opt,name=payer,proto3" json:"payer,omitempty"`
}

func (x *BillPayerSettingDef) Reset() {
	*x = BillPayerSettingDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_payment_v1_payer_defs_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BillPayerSettingDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BillPayerSettingDef) ProtoMessage() {}

func (x *BillPayerSettingDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_payment_v1_payer_defs_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BillPayerSettingDef.ProtoReflect.Descriptor instead.
func (*BillPayerSettingDef) Descriptor() ([]byte, []int) {
	return file_moego_models_payment_v1_payer_defs_proto_rawDescGZIP(), []int{1}
}

func (x *BillPayerSettingDef) GetBillType() BillType {
	if x != nil {
		return x.BillType
	}
	return BillType_BILLING_TYPE_UNSPECIFIED
}

func (x *BillPayerSettingDef) GetPayer() *PayerDef {
	if x != nil {
		return x.Payer
	}
	return nil
}

var File_moego_models_payment_v1_payer_defs_proto protoreflect.FileDescriptor

var file_moego_models_payment_v1_payer_defs_proto_rawDesc = []byte{
	0x0a, 0x28, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x70,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x70, 0x61, 0x79, 0x65, 0x72, 0x5f,
	0x64, 0x65, 0x66, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x17, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x76, 0x31, 0x1a, 0x29, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x70, 0x61, 0x79,
	0x65, 0x72, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17,
	0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x74, 0x0a, 0x08, 0x50, 0x61, 0x79, 0x65, 0x72,
	0x44, 0x65, 0x66, 0x12, 0x4d, 0x0a, 0x0a, 0x70, 0x61, 0x79, 0x65, 0x72, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x22, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76,
	0x31, 0x2e, 0x50, 0x61, 0x79, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x42, 0x0a, 0xfa, 0x42, 0x07,
	0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x52, 0x09, 0x70, 0x61, 0x79, 0x65, 0x72, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x70, 0x61, 0x79, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x70, 0x61, 0x79, 0x65, 0x72, 0x49, 0x64, 0x22, 0x9a, 0x01,
	0x0a, 0x13, 0x42, 0x69, 0x6c, 0x6c, 0x50, 0x61, 0x79, 0x65, 0x72, 0x53, 0x65, 0x74, 0x74, 0x69,
	0x6e, 0x67, 0x44, 0x65, 0x66, 0x12, 0x4a, 0x0a, 0x09, 0x62, 0x69, 0x6c, 0x6c, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x21, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x76, 0x31, 0x2e, 0x42, 0x69, 0x6c, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x42, 0x0a, 0xfa, 0x42, 0x07,
	0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x52, 0x08, 0x62, 0x69, 0x6c, 0x6c, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x37, 0x0a, 0x05, 0x70, 0x61, 0x79, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x21, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x61, 0x79, 0x65, 0x72,
	0x44, 0x65, 0x66, 0x52, 0x05, 0x70, 0x61, 0x79, 0x65, 0x72, 0x42, 0x7b, 0x0a, 0x1f, 0x63, 0x6f,
	0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a,
	0x56, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47,
	0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61,
	0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f,
	0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x3b, 0x70, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_payment_v1_payer_defs_proto_rawDescOnce sync.Once
	file_moego_models_payment_v1_payer_defs_proto_rawDescData = file_moego_models_payment_v1_payer_defs_proto_rawDesc
)

func file_moego_models_payment_v1_payer_defs_proto_rawDescGZIP() []byte {
	file_moego_models_payment_v1_payer_defs_proto_rawDescOnce.Do(func() {
		file_moego_models_payment_v1_payer_defs_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_payment_v1_payer_defs_proto_rawDescData)
	})
	return file_moego_models_payment_v1_payer_defs_proto_rawDescData
}

var file_moego_models_payment_v1_payer_defs_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_moego_models_payment_v1_payer_defs_proto_goTypes = []interface{}{
	(*PayerDef)(nil),            // 0: moego.models.payment.v1.PayerDef
	(*BillPayerSettingDef)(nil), // 1: moego.models.payment.v1.BillPayerSettingDef
	(PayerType)(0),              // 2: moego.models.payment.v1.PayerType
	(BillType)(0),               // 3: moego.models.payment.v1.BillType
}
var file_moego_models_payment_v1_payer_defs_proto_depIdxs = []int32{
	2, // 0: moego.models.payment.v1.PayerDef.payer_type:type_name -> moego.models.payment.v1.PayerType
	3, // 1: moego.models.payment.v1.BillPayerSettingDef.bill_type:type_name -> moego.models.payment.v1.BillType
	0, // 2: moego.models.payment.v1.BillPayerSettingDef.payer:type_name -> moego.models.payment.v1.PayerDef
	3, // [3:3] is the sub-list for method output_type
	3, // [3:3] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_moego_models_payment_v1_payer_defs_proto_init() }
func file_moego_models_payment_v1_payer_defs_proto_init() {
	if File_moego_models_payment_v1_payer_defs_proto != nil {
		return
	}
	file_moego_models_payment_v1_payer_enums_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_moego_models_payment_v1_payer_defs_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PayerDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_payment_v1_payer_defs_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BillPayerSettingDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_payment_v1_payer_defs_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_payment_v1_payer_defs_proto_goTypes,
		DependencyIndexes: file_moego_models_payment_v1_payer_defs_proto_depIdxs,
		MessageInfos:      file_moego_models_payment_v1_payer_defs_proto_msgTypes,
	}.Build()
	File_moego_models_payment_v1_payer_defs_proto = out.File
	file_moego_models_payment_v1_payer_defs_proto_rawDesc = nil
	file_moego_models_payment_v1_payer_defs_proto_goTypes = nil
	file_moego_models_payment_v1_payer_defs_proto_depIdxs = nil
}
