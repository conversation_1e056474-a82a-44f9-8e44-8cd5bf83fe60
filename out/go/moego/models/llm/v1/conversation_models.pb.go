// @since 2023-07-02 15:06:52
// <AUTHOR> <<EMAIL>>

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/llm/v1/conversation_models.proto

package llmpb

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// The Conversation model
type ConversationCompletionModel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id, no value currently
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// provider platform
	Provider string `protobuf:"bytes,2,opt,name=provider,proto3" json:"provider,omitempty"`
	// model name
	Model string `protobuf:"bytes,3,opt,name=model,proto3" json:"model,omitempty"`
	// external id
	ExternalId string `protobuf:"bytes,4,opt,name=external_id,json=externalId,proto3" json:"external_id,omitempty"`
	// usage info
	Usage *UsageDef `protobuf:"bytes,5,opt,name=usage,proto3" json:"usage,omitempty"`
	// choices, must be 1 element
	Choices []string `protobuf:"bytes,6,rep,name=choices,proto3" json:"choices,omitempty"`
}

func (x *ConversationCompletionModel) Reset() {
	*x = ConversationCompletionModel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_llm_v1_conversation_models_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ConversationCompletionModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConversationCompletionModel) ProtoMessage() {}

func (x *ConversationCompletionModel) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_llm_v1_conversation_models_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConversationCompletionModel.ProtoReflect.Descriptor instead.
func (*ConversationCompletionModel) Descriptor() ([]byte, []int) {
	return file_moego_models_llm_v1_conversation_models_proto_rawDescGZIP(), []int{0}
}

func (x *ConversationCompletionModel) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ConversationCompletionModel) GetProvider() string {
	if x != nil {
		return x.Provider
	}
	return ""
}

func (x *ConversationCompletionModel) GetModel() string {
	if x != nil {
		return x.Model
	}
	return ""
}

func (x *ConversationCompletionModel) GetExternalId() string {
	if x != nil {
		return x.ExternalId
	}
	return ""
}

func (x *ConversationCompletionModel) GetUsage() *UsageDef {
	if x != nil {
		return x.Usage
	}
	return nil
}

func (x *ConversationCompletionModel) GetChoices() []string {
	if x != nil {
		return x.Choices
	}
	return nil
}

var File_moego_models_llm_v1_conversation_models_proto protoreflect.FileDescriptor

var file_moego_models_llm_v1_conversation_models_proto_rawDesc = []byte{
	0x0a, 0x2d, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6c,
	0x6c, 0x6d, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12,
	0x13, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6c, 0x6c,
	0x6d, 0x2e, 0x76, 0x31, 0x1a, 0x24, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2f, 0x6c, 0x6c, 0x6d, 0x2f, 0x76, 0x31, 0x2f, 0x75, 0x73, 0x61, 0x67, 0x65, 0x5f,
	0x64, 0x65, 0x66, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69,
	0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x22, 0xe4, 0x01, 0x0a, 0x1b, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x6f,
	0x64, 0x65, 0x6c, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x08, 0x00, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1a, 0x0a, 0x08,
	0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x12, 0x14, 0x0a, 0x05, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x1f,
	0x0a, 0x0b, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x49, 0x64, 0x12,
	0x33, 0x0a, 0x05, 0x75, 0x73, 0x61, 0x67, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6c, 0x6c,
	0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x73, 0x61, 0x67, 0x65, 0x44, 0x65, 0x66, 0x52, 0x05, 0x75,
	0x73, 0x61, 0x67, 0x65, 0x12, 0x24, 0x0a, 0x07, 0x63, 0x68, 0x6f, 0x69, 0x63, 0x65, 0x73, 0x18,
	0x06, 0x20, 0x03, 0x28, 0x09, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x92, 0x01, 0x04, 0x08, 0x01, 0x10,
	0x01, 0x52, 0x07, 0x63, 0x68, 0x6f, 0x69, 0x63, 0x65, 0x73, 0x42, 0x6f, 0x0a, 0x1b, 0x63, 0x6f,
	0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x6c, 0x6c, 0x6d, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x4e, 0x67, 0x69, 0x74,
	0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62,
	0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64,
	0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67,
	0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6c,
	0x6c, 0x6d, 0x2f, 0x76, 0x31, 0x3b, 0x6c, 0x6c, 0x6d, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x33,
}

var (
	file_moego_models_llm_v1_conversation_models_proto_rawDescOnce sync.Once
	file_moego_models_llm_v1_conversation_models_proto_rawDescData = file_moego_models_llm_v1_conversation_models_proto_rawDesc
)

func file_moego_models_llm_v1_conversation_models_proto_rawDescGZIP() []byte {
	file_moego_models_llm_v1_conversation_models_proto_rawDescOnce.Do(func() {
		file_moego_models_llm_v1_conversation_models_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_llm_v1_conversation_models_proto_rawDescData)
	})
	return file_moego_models_llm_v1_conversation_models_proto_rawDescData
}

var file_moego_models_llm_v1_conversation_models_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_moego_models_llm_v1_conversation_models_proto_goTypes = []interface{}{
	(*ConversationCompletionModel)(nil), // 0: moego.models.llm.v1.ConversationCompletionModel
	(*UsageDef)(nil),                    // 1: moego.models.llm.v1.UsageDef
}
var file_moego_models_llm_v1_conversation_models_proto_depIdxs = []int32{
	1, // 0: moego.models.llm.v1.ConversationCompletionModel.usage:type_name -> moego.models.llm.v1.UsageDef
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_moego_models_llm_v1_conversation_models_proto_init() }
func file_moego_models_llm_v1_conversation_models_proto_init() {
	if File_moego_models_llm_v1_conversation_models_proto != nil {
		return
	}
	file_moego_models_llm_v1_usage_defs_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_moego_models_llm_v1_conversation_models_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ConversationCompletionModel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_llm_v1_conversation_models_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_llm_v1_conversation_models_proto_goTypes,
		DependencyIndexes: file_moego_models_llm_v1_conversation_models_proto_depIdxs,
		MessageInfos:      file_moego_models_llm_v1_conversation_models_proto_msgTypes,
	}.Build()
	File_moego_models_llm_v1_conversation_models_proto = out.File
	file_moego_models_llm_v1_conversation_models_proto_rawDesc = nil
	file_moego_models_llm_v1_conversation_models_proto_goTypes = nil
	file_moego_models_llm_v1_conversation_models_proto_depIdxs = nil
}
