// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/organization/v1/company_defs.proto

package organizationpb

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// company preference setting definition
type UpdateCompanyPreferenceSettingDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// currency code
	CurrencyCode string `protobuf:"bytes,1,opt,name=currency_code,json=currencyCode,proto3" json:"currency_code,omitempty"`
	// currency symbol
	CurrencySymbol string `protobuf:"bytes,2,opt,name=currency_symbol,json=currencySymbol,proto3" json:"currency_symbol,omitempty"`
	// date format
	DateFormatType DateFormat `protobuf:"varint,3,opt,name=date_format_type,json=dateFormatType,proto3,enum=moego.models.organization.v1.DateFormat" json:"date_format_type,omitempty"`
	// time format
	TimeFormatType TimeFormat `protobuf:"varint,4,opt,name=time_format_type,json=timeFormatType,proto3,enum=moego.models.organization.v1.TimeFormat" json:"time_format_type,omitempty"`
	// unit of weight
	UnitOfWeightType WeightUnit `protobuf:"varint,5,opt,name=unit_of_weight_type,json=unitOfWeightType,proto3,enum=moego.models.organization.v1.WeightUnit" json:"unit_of_weight_type,omitempty"`
	// unit of distance
	UnitOfDistanceType DistanceUnit `protobuf:"varint,6,opt,name=unit_of_distance_type,json=unitOfDistanceType,proto3,enum=moego.models.organization.v1.DistanceUnit" json:"unit_of_distance_type,omitempty"`
	// whether the notification sound is on
	NotificationSoundEnable bool `protobuf:"varint,7,opt,name=notification_sound_enable,json=notificationSoundEnable,proto3" json:"notification_sound_enable,omitempty"`
	// timezone
	TimeZone *TimeZone `protobuf:"bytes,8,opt,name=time_zone,json=timeZone,proto3" json:"time_zone,omitempty"`
}

func (x *UpdateCompanyPreferenceSettingDef) Reset() {
	*x = UpdateCompanyPreferenceSettingDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_organization_v1_company_defs_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateCompanyPreferenceSettingDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateCompanyPreferenceSettingDef) ProtoMessage() {}

func (x *UpdateCompanyPreferenceSettingDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_organization_v1_company_defs_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateCompanyPreferenceSettingDef.ProtoReflect.Descriptor instead.
func (*UpdateCompanyPreferenceSettingDef) Descriptor() ([]byte, []int) {
	return file_moego_models_organization_v1_company_defs_proto_rawDescGZIP(), []int{0}
}

func (x *UpdateCompanyPreferenceSettingDef) GetCurrencyCode() string {
	if x != nil {
		return x.CurrencyCode
	}
	return ""
}

func (x *UpdateCompanyPreferenceSettingDef) GetCurrencySymbol() string {
	if x != nil {
		return x.CurrencySymbol
	}
	return ""
}

func (x *UpdateCompanyPreferenceSettingDef) GetDateFormatType() DateFormat {
	if x != nil {
		return x.DateFormatType
	}
	return DateFormat_DATE_FORMAT_UNSPECIFIED
}

func (x *UpdateCompanyPreferenceSettingDef) GetTimeFormatType() TimeFormat {
	if x != nil {
		return x.TimeFormatType
	}
	return TimeFormat_TIME_FORMAT_UNSPECIFIED
}

func (x *UpdateCompanyPreferenceSettingDef) GetUnitOfWeightType() WeightUnit {
	if x != nil {
		return x.UnitOfWeightType
	}
	return WeightUnit_WEIGHT_UNIT_UNSPECIFIED
}

func (x *UpdateCompanyPreferenceSettingDef) GetUnitOfDistanceType() DistanceUnit {
	if x != nil {
		return x.UnitOfDistanceType
	}
	return DistanceUnit_DISTANCE_UNIT_UNSPECIFIED
}

func (x *UpdateCompanyPreferenceSettingDef) GetNotificationSoundEnable() bool {
	if x != nil {
		return x.NotificationSoundEnable
	}
	return false
}

func (x *UpdateCompanyPreferenceSettingDef) GetTimeZone() *TimeZone {
	if x != nil {
		return x.TimeZone
	}
	return nil
}

// company extra info
type CompanyExtraInfoDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// unread notification count
	UnreadNotificationCount int32 `protobuf:"varint,1,opt,name=unread_notification_count,json=unreadNotificationCount,proto3" json:"unread_notification_count,omitempty"`
}

func (x *CompanyExtraInfoDef) Reset() {
	*x = CompanyExtraInfoDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_organization_v1_company_defs_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CompanyExtraInfoDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CompanyExtraInfoDef) ProtoMessage() {}

func (x *CompanyExtraInfoDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_organization_v1_company_defs_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CompanyExtraInfoDef.ProtoReflect.Descriptor instead.
func (*CompanyExtraInfoDef) Descriptor() ([]byte, []int) {
	return file_moego_models_organization_v1_company_defs_proto_rawDescGZIP(), []int{1}
}

func (x *CompanyExtraInfoDef) GetUnreadNotificationCount() int32 {
	if x != nil {
		return x.UnreadNotificationCount
	}
	return 0
}

var File_moego_models_organization_v1_company_defs_proto protoreflect.FileDescriptor

var file_moego_models_organization_v1_company_defs_proto_rawDesc = []byte{
	0x0a, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f,
	0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x63,
	0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x64, 0x65, 0x66, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x1c, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x1a,
	0x30, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x72,
	0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x6f,
	0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x2c, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f,
	0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x2f,
	0x74, 0x69, 0x6d, 0x65, 0x5f, 0x7a, 0x6f, 0x6e, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61,
	0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xe5, 0x04, 0x0a, 0x21, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x50, 0x72, 0x65, 0x66, 0x65, 0x72,
	0x65, 0x6e, 0x63, 0x65, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x44, 0x65, 0x66, 0x12, 0x2d,
	0x0a, 0x0d, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x72, 0x03, 0x98, 0x01, 0x03, 0x52,
	0x0c, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x30, 0x0a,
	0x0f, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x5f, 0x73, 0x79, 0x6d, 0x62, 0x6f, 0x6c,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52,
	0x0e, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x53, 0x79, 0x6d, 0x62, 0x6f, 0x6c, 0x12,
	0x52, 0x0a, 0x10, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x28, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x46, 0x6f, 0x72,
	0x6d, 0x61, 0x74, 0x52, 0x0e, 0x64, 0x61, 0x74, 0x65, 0x46, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x52, 0x0a, 0x10, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x66, 0x6f, 0x72, 0x6d,
	0x61, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x28, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67,
	0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x69, 0x6d,
	0x65, 0x46, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x52, 0x0e, 0x74, 0x69, 0x6d, 0x65, 0x46, 0x6f, 0x72,
	0x6d, 0x61, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x57, 0x0a, 0x13, 0x75, 0x6e, 0x69, 0x74, 0x5f,
	0x6f, 0x66, 0x5f, 0x77, 0x65, 0x69, 0x67, 0x68, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x28, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x76, 0x31, 0x2e, 0x57, 0x65, 0x69, 0x67, 0x68, 0x74, 0x55, 0x6e, 0x69, 0x74, 0x52, 0x10,
	0x75, 0x6e, 0x69, 0x74, 0x4f, 0x66, 0x57, 0x65, 0x69, 0x67, 0x68, 0x74, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x5d, 0x0a, 0x15, 0x75, 0x6e, 0x69, 0x74, 0x5f, 0x6f, 0x66, 0x5f, 0x64, 0x69, 0x73, 0x74,
	0x61, 0x6e, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f,
	0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x44,
	0x69, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x55, 0x6e, 0x69, 0x74, 0x52, 0x12, 0x75, 0x6e, 0x69,
	0x74, 0x4f, 0x66, 0x44, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x3a, 0x0a, 0x19, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x73, 0x6f, 0x75, 0x6e, 0x64, 0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x17, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x53, 0x6f, 0x75, 0x6e, 0x64, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x43, 0x0a, 0x09, 0x74,
	0x69, 0x6d, 0x65, 0x5f, 0x7a, 0x6f, 0x6e, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72,
	0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x69,
	0x6d, 0x65, 0x5a, 0x6f, 0x6e, 0x65, 0x52, 0x08, 0x74, 0x69, 0x6d, 0x65, 0x5a, 0x6f, 0x6e, 0x65,
	0x22, 0x51, 0x0a, 0x13, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x45, 0x78, 0x74, 0x72, 0x61,
	0x49, 0x6e, 0x66, 0x6f, 0x44, 0x65, 0x66, 0x12, 0x3a, 0x0a, 0x19, 0x75, 0x6e, 0x72, 0x65, 0x61,
	0x64, 0x5f, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x17, 0x75, 0x6e, 0x72, 0x65,
	0x61, 0x64, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f,
	0x75, 0x6e, 0x74, 0x42, 0x8a, 0x01, 0x0a, 0x24, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67,
	0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x60,
	0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f,
	0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70,
	0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75,
	0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2f, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76,
	0x31, 0x3b, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x70, 0x62,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_organization_v1_company_defs_proto_rawDescOnce sync.Once
	file_moego_models_organization_v1_company_defs_proto_rawDescData = file_moego_models_organization_v1_company_defs_proto_rawDesc
)

func file_moego_models_organization_v1_company_defs_proto_rawDescGZIP() []byte {
	file_moego_models_organization_v1_company_defs_proto_rawDescOnce.Do(func() {
		file_moego_models_organization_v1_company_defs_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_organization_v1_company_defs_proto_rawDescData)
	})
	return file_moego_models_organization_v1_company_defs_proto_rawDescData
}

var file_moego_models_organization_v1_company_defs_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_moego_models_organization_v1_company_defs_proto_goTypes = []interface{}{
	(*UpdateCompanyPreferenceSettingDef)(nil), // 0: moego.models.organization.v1.UpdateCompanyPreferenceSettingDef
	(*CompanyExtraInfoDef)(nil),               // 1: moego.models.organization.v1.CompanyExtraInfoDef
	(DateFormat)(0),                           // 2: moego.models.organization.v1.DateFormat
	(TimeFormat)(0),                           // 3: moego.models.organization.v1.TimeFormat
	(WeightUnit)(0),                           // 4: moego.models.organization.v1.WeightUnit
	(DistanceUnit)(0),                         // 5: moego.models.organization.v1.DistanceUnit
	(*TimeZone)(nil),                          // 6: moego.models.organization.v1.TimeZone
}
var file_moego_models_organization_v1_company_defs_proto_depIdxs = []int32{
	2, // 0: moego.models.organization.v1.UpdateCompanyPreferenceSettingDef.date_format_type:type_name -> moego.models.organization.v1.DateFormat
	3, // 1: moego.models.organization.v1.UpdateCompanyPreferenceSettingDef.time_format_type:type_name -> moego.models.organization.v1.TimeFormat
	4, // 2: moego.models.organization.v1.UpdateCompanyPreferenceSettingDef.unit_of_weight_type:type_name -> moego.models.organization.v1.WeightUnit
	5, // 3: moego.models.organization.v1.UpdateCompanyPreferenceSettingDef.unit_of_distance_type:type_name -> moego.models.organization.v1.DistanceUnit
	6, // 4: moego.models.organization.v1.UpdateCompanyPreferenceSettingDef.time_zone:type_name -> moego.models.organization.v1.TimeZone
	5, // [5:5] is the sub-list for method output_type
	5, // [5:5] is the sub-list for method input_type
	5, // [5:5] is the sub-list for extension type_name
	5, // [5:5] is the sub-list for extension extendee
	0, // [0:5] is the sub-list for field type_name
}

func init() { file_moego_models_organization_v1_company_defs_proto_init() }
func file_moego_models_organization_v1_company_defs_proto_init() {
	if File_moego_models_organization_v1_company_defs_proto != nil {
		return
	}
	file_moego_models_organization_v1_company_enums_proto_init()
	file_moego_models_organization_v1_time_zone_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_moego_models_organization_v1_company_defs_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateCompanyPreferenceSettingDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_organization_v1_company_defs_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CompanyExtraInfoDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_organization_v1_company_defs_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_organization_v1_company_defs_proto_goTypes,
		DependencyIndexes: file_moego_models_organization_v1_company_defs_proto_depIdxs,
		MessageInfos:      file_moego_models_organization_v1_company_defs_proto_msgTypes,
	}.Build()
	File_moego_models_organization_v1_company_defs_proto = out.File
	file_moego_models_organization_v1_company_defs_proto_rawDesc = nil
	file_moego_models_organization_v1_company_defs_proto_goTypes = nil
	file_moego_models_organization_v1_company_defs_proto_depIdxs = nil
}
