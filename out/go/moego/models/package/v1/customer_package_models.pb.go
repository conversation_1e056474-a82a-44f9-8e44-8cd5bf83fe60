// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/package/v1/customer_package_models.proto

package packagepb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// The customer package model, indicate the package sold to the customer
type CustomerPackageModel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// package id
	PackageId int64 `protobuf:"varint,2,opt,name=package_id,json=packageId,proto3" json:"package_id,omitempty"`
	// customer id
	CustomerId int64 `protobuf:"varint,3,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
	// package name
	PackageName string `protobuf:"bytes,4,opt,name=package_name,json=packageName,proto3" json:"package_name,omitempty"`
	// package description
	PackageDescription string `protobuf:"bytes,5,opt,name=package_description,json=packageDescription,proto3" json:"package_description,omitempty"`
	// package price
	PackagePrice float64 `protobuf:"fixed64,6,opt,name=package_price,json=packagePrice,proto3" json:"package_price,omitempty"`
	// purchase time
	PurchaseTime *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=purchase_time,json=purchaseTime,proto3" json:"purchase_time,omitempty"`
	// start time, the time when the package is valid
	StartTime *timestamppb.Timestamp `protobuf:"bytes,8,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	// end time, the time when the package is expired
	EndTime *timestamppb.Timestamp `protobuf:"bytes,9,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	// items
	RemainingItems []*RemainingItem `protobuf:"bytes,10,rep,name=remaining_items,json=remainingItems,proto3" json:"remaining_items,omitempty"`
	// business id
	BusinessId int64 `protobuf:"varint,11,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
}

func (x *CustomerPackageModel) Reset() {
	*x = CustomerPackageModel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_package_v1_customer_package_models_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CustomerPackageModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerPackageModel) ProtoMessage() {}

func (x *CustomerPackageModel) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_package_v1_customer_package_models_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerPackageModel.ProtoReflect.Descriptor instead.
func (*CustomerPackageModel) Descriptor() ([]byte, []int) {
	return file_moego_models_package_v1_customer_package_models_proto_rawDescGZIP(), []int{0}
}

func (x *CustomerPackageModel) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *CustomerPackageModel) GetPackageId() int64 {
	if x != nil {
		return x.PackageId
	}
	return 0
}

func (x *CustomerPackageModel) GetCustomerId() int64 {
	if x != nil {
		return x.CustomerId
	}
	return 0
}

func (x *CustomerPackageModel) GetPackageName() string {
	if x != nil {
		return x.PackageName
	}
	return ""
}

func (x *CustomerPackageModel) GetPackageDescription() string {
	if x != nil {
		return x.PackageDescription
	}
	return ""
}

func (x *CustomerPackageModel) GetPackagePrice() float64 {
	if x != nil {
		return x.PackagePrice
	}
	return 0
}

func (x *CustomerPackageModel) GetPurchaseTime() *timestamppb.Timestamp {
	if x != nil {
		return x.PurchaseTime
	}
	return nil
}

func (x *CustomerPackageModel) GetStartTime() *timestamppb.Timestamp {
	if x != nil {
		return x.StartTime
	}
	return nil
}

func (x *CustomerPackageModel) GetEndTime() *timestamppb.Timestamp {
	if x != nil {
		return x.EndTime
	}
	return nil
}

func (x *CustomerPackageModel) GetRemainingItems() []*RemainingItem {
	if x != nil {
		return x.RemainingItems
	}
	return nil
}

func (x *CustomerPackageModel) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

// remaining items
type RemainingItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// services
	ServiceIds []int64 `protobuf:"varint,1,rep,packed,name=service_ids,json=serviceIds,proto3" json:"service_ids,omitempty"`
	// quantity
	TotalQuantity int32 `protobuf:"varint,2,opt,name=total_quantity,json=totalQuantity,proto3" json:"total_quantity,omitempty"`
	// remaining quantity
	RemainingQuantity int32 `protobuf:"varint,3,opt,name=remaining_quantity,json=remainingQuantity,proto3" json:"remaining_quantity,omitempty"`
}

func (x *RemainingItem) Reset() {
	*x = RemainingItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_package_v1_customer_package_models_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RemainingItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RemainingItem) ProtoMessage() {}

func (x *RemainingItem) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_package_v1_customer_package_models_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RemainingItem.ProtoReflect.Descriptor instead.
func (*RemainingItem) Descriptor() ([]byte, []int) {
	return file_moego_models_package_v1_customer_package_models_proto_rawDescGZIP(), []int{1}
}

func (x *RemainingItem) GetServiceIds() []int64 {
	if x != nil {
		return x.ServiceIds
	}
	return nil
}

func (x *RemainingItem) GetTotalQuantity() int32 {
	if x != nil {
		return x.TotalQuantity
	}
	return 0
}

func (x *RemainingItem) GetRemainingQuantity() int32 {
	if x != nil {
		return x.RemainingQuantity
	}
	return 0
}

var File_moego_models_package_v1_customer_package_models_proto protoreflect.FileDescriptor

var file_moego_models_package_v1_customer_package_models_proto_rawDesc = []byte{
	0x0a, 0x35, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x70,
	0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x65, 0x72, 0x5f, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x17, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x2e, 0x76, 0x31,
	0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x22, 0x84, 0x04, 0x0a, 0x14, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x50, 0x61,
	0x63, 0x6b, 0x61, 0x67, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x61,
	0x63, 0x6b, 0x61, 0x67, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09,
	0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a,
	0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x70, 0x61,
	0x63, 0x6b, 0x61, 0x67, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2f, 0x0a,
	0x13, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x5f, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x70, 0x61, 0x63, 0x6b,
	0x61, 0x67, 0x65, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x23,
	0x0a, 0x0d, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0c, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x50, 0x72,
	0x69, 0x63, 0x65, 0x12, 0x3f, 0x0a, 0x0d, 0x70, 0x75, 0x72, 0x63, 0x68, 0x61, 0x73, 0x65, 0x5f,
	0x74, 0x69, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0c, 0x70, 0x75, 0x72, 0x63, 0x68, 0x61, 0x73, 0x65,
	0x54, 0x69, 0x6d, 0x65, 0x12, 0x39, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69,
	0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12,
	0x35, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x07, 0x65,
	0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x4f, 0x0a, 0x0f, 0x72, 0x65, 0x6d, 0x61, 0x69, 0x6e,
	0x69, 0x6e, 0x67, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x26, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70,
	0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x6d, 0x61, 0x69, 0x6e,
	0x69, 0x6e, 0x67, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x0e, 0x72, 0x65, 0x6d, 0x61, 0x69, 0x6e, 0x69,
	0x6e, 0x67, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x62, 0x75,
	0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x22, 0x86, 0x01, 0x0a, 0x0d, 0x52, 0x65, 0x6d,
	0x61, 0x69, 0x6e, 0x69, 0x6e, 0x67, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x52,
	0x0a, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x73, 0x12, 0x25, 0x0a, 0x0e, 0x74,
	0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x71, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x0d, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x51, 0x75, 0x61, 0x6e, 0x74, 0x69,
	0x74, 0x79, 0x12, 0x2d, 0x0a, 0x12, 0x72, 0x65, 0x6d, 0x61, 0x69, 0x6e, 0x69, 0x6e, 0x67, 0x5f,
	0x71, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x11,
	0x72, 0x65, 0x6d, 0x61, 0x69, 0x6e, 0x69, 0x6e, 0x67, 0x51, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x74,
	0x79, 0x42, 0x77, 0x0a, 0x1b, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69,
	0x64, 0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x6b, 0x67, 0x2e, 0x76, 0x31,
	0x50, 0x01, 0x5a, 0x56, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d,
	0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x2f, 0x76, 0x31,
	0x3b, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x33,
}

var (
	file_moego_models_package_v1_customer_package_models_proto_rawDescOnce sync.Once
	file_moego_models_package_v1_customer_package_models_proto_rawDescData = file_moego_models_package_v1_customer_package_models_proto_rawDesc
)

func file_moego_models_package_v1_customer_package_models_proto_rawDescGZIP() []byte {
	file_moego_models_package_v1_customer_package_models_proto_rawDescOnce.Do(func() {
		file_moego_models_package_v1_customer_package_models_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_package_v1_customer_package_models_proto_rawDescData)
	})
	return file_moego_models_package_v1_customer_package_models_proto_rawDescData
}

var file_moego_models_package_v1_customer_package_models_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_moego_models_package_v1_customer_package_models_proto_goTypes = []interface{}{
	(*CustomerPackageModel)(nil),  // 0: moego.models.package.v1.CustomerPackageModel
	(*RemainingItem)(nil),         // 1: moego.models.package.v1.RemainingItem
	(*timestamppb.Timestamp)(nil), // 2: google.protobuf.Timestamp
}
var file_moego_models_package_v1_customer_package_models_proto_depIdxs = []int32{
	2, // 0: moego.models.package.v1.CustomerPackageModel.purchase_time:type_name -> google.protobuf.Timestamp
	2, // 1: moego.models.package.v1.CustomerPackageModel.start_time:type_name -> google.protobuf.Timestamp
	2, // 2: moego.models.package.v1.CustomerPackageModel.end_time:type_name -> google.protobuf.Timestamp
	1, // 3: moego.models.package.v1.CustomerPackageModel.remaining_items:type_name -> moego.models.package.v1.RemainingItem
	4, // [4:4] is the sub-list for method output_type
	4, // [4:4] is the sub-list for method input_type
	4, // [4:4] is the sub-list for extension type_name
	4, // [4:4] is the sub-list for extension extendee
	0, // [0:4] is the sub-list for field type_name
}

func init() { file_moego_models_package_v1_customer_package_models_proto_init() }
func file_moego_models_package_v1_customer_package_models_proto_init() {
	if File_moego_models_package_v1_customer_package_models_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_models_package_v1_customer_package_models_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CustomerPackageModel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_package_v1_customer_package_models_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RemainingItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_package_v1_customer_package_models_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_package_v1_customer_package_models_proto_goTypes,
		DependencyIndexes: file_moego_models_package_v1_customer_package_models_proto_depIdxs,
		MessageInfos:      file_moego_models_package_v1_customer_package_models_proto_msgTypes,
	}.Build()
	File_moego_models_package_v1_customer_package_models_proto = out.File
	file_moego_models_package_v1_customer_package_models_proto_rawDesc = nil
	file_moego_models_package_v1_customer_package_models_proto_goTypes = nil
	file_moego_models_package_v1_customer_package_models_proto_depIdxs = nil
}
