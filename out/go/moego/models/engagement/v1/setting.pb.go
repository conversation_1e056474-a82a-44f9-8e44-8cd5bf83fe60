// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/engagement/v1/setting.proto

package engagementpb

import (
	v11 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/organization/v1"
	v12 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/permission/v1"
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// device type
type DeviceType int32

const (
	// unspecified
	DeviceType_DEVICE_TYPE_UNSPECIFIED DeviceType = 0
	// web
	DeviceType_DEVICE_TYPE_WEB DeviceType = 1
	// mobile
	DeviceType_DEVICE_TYPE_MOBILE_APP DeviceType = 2
)

// Enum value maps for DeviceType.
var (
	DeviceType_name = map[int32]string{
		0: "DEVICE_TYPE_UNSPECIFIED",
		1: "DEVICE_TYPE_WEB",
		2: "DEVICE_TYPE_MOBILE_APP",
	}
	DeviceType_value = map[string]int32{
		"DEVICE_TYPE_UNSPECIFIED": 0,
		"DEVICE_TYPE_WEB":         1,
		"DEVICE_TYPE_MOBILE_APP":  2,
	}
)

func (x DeviceType) Enum() *DeviceType {
	p := new(DeviceType)
	*p = x
	return p
}

func (x DeviceType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DeviceType) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_engagement_v1_setting_proto_enumTypes[0].Descriptor()
}

func (DeviceType) Type() protoreflect.EnumType {
	return &file_moego_models_engagement_v1_setting_proto_enumTypes[0]
}

func (x DeviceType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DeviceType.Descriptor instead.
func (DeviceType) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_engagement_v1_setting_proto_rawDescGZIP(), []int{0}
}

// dispatching type
type Setting_DispatchingType int32

const (
	// TYPE_UNSPECIFIED
	Setting_DISPATCH_TYPE_UNSPECIFIED Setting_DispatchingType = 0
	// ALL_AT_ONCE
	Setting_ALL_AT_ONCE Setting_DispatchingType = 1
	// WEB_CALL_FIRST
	Setting_WEB_CALL_FIRST Setting_DispatchingType = 2
	// MOBILE_CALL_FIRST
	Setting_MOBILE_CALL_FIRST Setting_DispatchingType = 3
	// MOBILE_ONLY
	Setting_MOBILE_ONLY Setting_DispatchingType = 4
	// WEB_ONLY
	Setting_WEB_ONLY Setting_DispatchingType = 5
)

// Enum value maps for Setting_DispatchingType.
var (
	Setting_DispatchingType_name = map[int32]string{
		0: "DISPATCH_TYPE_UNSPECIFIED",
		1: "ALL_AT_ONCE",
		2: "WEB_CALL_FIRST",
		3: "MOBILE_CALL_FIRST",
		4: "MOBILE_ONLY",
		5: "WEB_ONLY",
	}
	Setting_DispatchingType_value = map[string]int32{
		"DISPATCH_TYPE_UNSPECIFIED": 0,
		"ALL_AT_ONCE":               1,
		"WEB_CALL_FIRST":            2,
		"MOBILE_CALL_FIRST":         3,
		"MOBILE_ONLY":               4,
		"WEB_ONLY":                  5,
	}
)

func (x Setting_DispatchingType) Enum() *Setting_DispatchingType {
	p := new(Setting_DispatchingType)
	*p = x
	return p
}

func (x Setting_DispatchingType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Setting_DispatchingType) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_engagement_v1_setting_proto_enumTypes[1].Descriptor()
}

func (Setting_DispatchingType) Type() protoreflect.EnumType {
	return &file_moego_models_engagement_v1_setting_proto_enumTypes[1]
}

func (x Setting_DispatchingType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Setting_DispatchingType.Descriptor instead.
func (Setting_DispatchingType) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_engagement_v1_setting_proto_rawDescGZIP(), []int{0, 0}
}

// calling setting
type Setting struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// dispatching type
	DispatchingType Setting_DispatchingType `protobuf:"varint,1,opt,name=dispatching_type,json=dispatchingType,proto3,enum=moego.models.engagement.v1.Setting_DispatchingType" json:"dispatching_type,omitempty"`
	// business hours range
	BusinessHoursRange *v1.TimeOfDayInterval `protobuf:"bytes,2,opt,name=business_hours_range,json=businessHoursRange,proto3" json:"business_hours_range,omitempty"`
	// auto recording
	AutoRecording bool `protobuf:"varint,3,opt,name=auto_recording,json=autoRecording,proto3" json:"auto_recording,omitempty"`
	// phone number
	PhoneNumber string `protobuf:"bytes,4,opt,name=phone_number,json=phoneNumber,proto3" json:"phone_number,omitempty"`
	// voice mail
	VoiceMail string `protobuf:"bytes,5,opt,name=voice_mail,json=voiceMail,proto3" json:"voice_mail,omitempty"`
	// 是否启用 engagement center
	EnableMoegoCall bool `protobuf:"varint,6,opt,name=enable_moego_call,json=enableMoegoCall,proto3" json:"enable_moego_call,omitempty"`
	// 不启用 engagement center 时，incoming 电话转拨的号码
	CallForwardingNumber string `protobuf:"bytes,7,opt,name=call_forwarding_number,json=callForwardingNumber,proto3" json:"call_forwarding_number,omitempty"`
	// 是否启用 follow up 短信
	EnableFollowUpSms bool `protobuf:"varint,8,opt,name=enable_follow_up_sms,json=enableFollowUpSms,proto3" json:"enable_follow_up_sms,omitempty"`
	// follow up 短信内容
	FollowUpSmsContent string `protobuf:"bytes,9,opt,name=follow_up_sms_content,json=followUpSmsContent,proto3" json:"follow_up_sms_content,omitempty"`
	// call ring duration(秒)
	CallRingDuration int32 `protobuf:"varint,10,opt,name=call_ring_duration,json=callRingDuration,proto3" json:"call_ring_duration,omitempty"`
}

func (x *Setting) Reset() {
	*x = Setting{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_engagement_v1_setting_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Setting) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Setting) ProtoMessage() {}

func (x *Setting) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_engagement_v1_setting_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Setting.ProtoReflect.Descriptor instead.
func (*Setting) Descriptor() ([]byte, []int) {
	return file_moego_models_engagement_v1_setting_proto_rawDescGZIP(), []int{0}
}

func (x *Setting) GetDispatchingType() Setting_DispatchingType {
	if x != nil {
		return x.DispatchingType
	}
	return Setting_DISPATCH_TYPE_UNSPECIFIED
}

func (x *Setting) GetBusinessHoursRange() *v1.TimeOfDayInterval {
	if x != nil {
		return x.BusinessHoursRange
	}
	return nil
}

func (x *Setting) GetAutoRecording() bool {
	if x != nil {
		return x.AutoRecording
	}
	return false
}

func (x *Setting) GetPhoneNumber() string {
	if x != nil {
		return x.PhoneNumber
	}
	return ""
}

func (x *Setting) GetVoiceMail() string {
	if x != nil {
		return x.VoiceMail
	}
	return ""
}

func (x *Setting) GetEnableMoegoCall() bool {
	if x != nil {
		return x.EnableMoegoCall
	}
	return false
}

func (x *Setting) GetCallForwardingNumber() string {
	if x != nil {
		return x.CallForwardingNumber
	}
	return ""
}

func (x *Setting) GetEnableFollowUpSms() bool {
	if x != nil {
		return x.EnableFollowUpSms
	}
	return false
}

func (x *Setting) GetFollowUpSmsContent() string {
	if x != nil {
		return x.FollowUpSmsContent
	}
	return ""
}

func (x *Setting) GetCallRingDuration() int32 {
	if x != nil {
		return x.CallRingDuration
	}
	return 0
}

// seat
type SeatsSetting struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// seats
	Seats []*Seat `protobuf:"bytes,1,rep,name=seats,proto3" json:"seats,omitempty"`
	// capacity
	Capacity int64 `protobuf:"varint,2,opt,name=capacity,proto3" json:"capacity,omitempty"`
}

func (x *SeatsSetting) Reset() {
	*x = SeatsSetting{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_engagement_v1_setting_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SeatsSetting) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SeatsSetting) ProtoMessage() {}

func (x *SeatsSetting) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_engagement_v1_setting_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SeatsSetting.ProtoReflect.Descriptor instead.
func (*SeatsSetting) Descriptor() ([]byte, []int) {
	return file_moego_models_engagement_v1_setting_proto_rawDescGZIP(), []int{1}
}

func (x *SeatsSetting) GetSeats() []*Seat {
	if x != nil {
		return x.Seats
	}
	return nil
}

func (x *SeatsSetting) GetCapacity() int64 {
	if x != nil {
		return x.Capacity
	}
	return 0
}

// seat
type Seat struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// staff
	Staff *v11.StaffModel `protobuf:"bytes,1,opt,name=staff,proto3" json:"staff,omitempty"`
	// deletable
	Deletable bool `protobuf:"varint,2,opt,name=deletable,proto3" json:"deletable,omitempty"`
}

func (x *Seat) Reset() {
	*x = Seat{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_engagement_v1_setting_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Seat) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Seat) ProtoMessage() {}

func (x *Seat) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_engagement_v1_setting_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Seat.ProtoReflect.Descriptor instead.
func (*Seat) Descriptor() ([]byte, []int) {
	return file_moego_models_engagement_v1_setting_proto_rawDescGZIP(), []int{2}
}

func (x *Seat) GetStaff() *v11.StaffModel {
	if x != nil {
		return x.Staff
	}
	return nil
}

func (x *Seat) GetDeletable() bool {
	if x != nil {
		return x.Deletable
	}
	return false
}

// tmp seat
type TmpSeat struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// staff
	Staff *v11.StaffModel `protobuf:"bytes,1,opt,name=staff,proto3" json:"staff,omitempty"`
	// role
	Role *v12.RoleModel `protobuf:"bytes,2,opt,name=role,proto3" json:"role,omitempty"`
	// device type
	DeviceTypes []DeviceType `protobuf:"varint,3,rep,packed,name=device_types,json=deviceTypes,proto3,enum=moego.models.engagement.v1.DeviceType" json:"device_types,omitempty"`
}

func (x *TmpSeat) Reset() {
	*x = TmpSeat{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_engagement_v1_setting_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TmpSeat) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TmpSeat) ProtoMessage() {}

func (x *TmpSeat) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_engagement_v1_setting_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TmpSeat.ProtoReflect.Descriptor instead.
func (*TmpSeat) Descriptor() ([]byte, []int) {
	return file_moego_models_engagement_v1_setting_proto_rawDescGZIP(), []int{3}
}

func (x *TmpSeat) GetStaff() *v11.StaffModel {
	if x != nil {
		return x.Staff
	}
	return nil
}

func (x *TmpSeat) GetRole() *v12.RoleModel {
	if x != nil {
		return x.Role
	}
	return nil
}

func (x *TmpSeat) GetDeviceTypes() []DeviceType {
	if x != nil {
		return x.DeviceTypes
	}
	return nil
}

var File_moego_models_engagement_v1_setting_proto protoreflect.FileDescriptor

var file_moego_models_engagement_v1_setting_proto_rawDesc = []byte{
	0x0a, 0x28, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x65,
	0x6e, 0x67, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x65, 0x74,
	0x74, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1a, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x67, 0x61, 0x67, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x1a, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x32, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e,
	0x2f, 0x76, 0x31, 0x2f, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x29, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2f, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x74, 0x69, 0x6d, 0x65,
	0x5f, 0x6f, 0x66, 0x5f, 0x64, 0x61, 0x79, 0x5f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xa9, 0x05, 0x0a, 0x07, 0x53, 0x65, 0x74, 0x74, 0x69,
	0x6e, 0x67, 0x12, 0x5e, 0x0a, 0x10, 0x64, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x69, 0x6e,
	0x67, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x33, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x67, 0x61,
	0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e,
	0x67, 0x2e, 0x44, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70,
	0x65, 0x52, 0x0f, 0x64, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x69, 0x6e, 0x67, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x53, 0x0a, 0x14, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x68,
	0x6f, 0x75, 0x72, 0x73, 0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x21, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76,
	0x31, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x4f, 0x66, 0x44, 0x61, 0x79, 0x49, 0x6e, 0x74, 0x65, 0x72,
	0x76, 0x61, 0x6c, 0x52, 0x12, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x48, 0x6f, 0x75,
	0x72, 0x73, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x61, 0x75, 0x74, 0x6f, 0x5f,
	0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x0d, 0x61, 0x75, 0x74, 0x6f, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x12, 0x21,
	0x0a, 0x0c, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65,
	0x72, 0x12, 0x1d, 0x0a, 0x0a, 0x76, 0x6f, 0x69, 0x63, 0x65, 0x5f, 0x6d, 0x61, 0x69, 0x6c, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x76, 0x6f, 0x69, 0x63, 0x65, 0x4d, 0x61, 0x69, 0x6c,
	0x12, 0x2a, 0x0a, 0x11, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x5f, 0x63, 0x61, 0x6c, 0x6c, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0f, 0x65, 0x6e, 0x61,
	0x62, 0x6c, 0x65, 0x4d, 0x6f, 0x65, 0x67, 0x6f, 0x43, 0x61, 0x6c, 0x6c, 0x12, 0x34, 0x0a, 0x16,
	0x63, 0x61, 0x6c, 0x6c, 0x5f, 0x66, 0x6f, 0x72, 0x77, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x5f,
	0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x14, 0x63, 0x61,
	0x6c, 0x6c, 0x46, 0x6f, 0x72, 0x77, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x4e, 0x75, 0x6d, 0x62,
	0x65, 0x72, 0x12, 0x2f, 0x0a, 0x14, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x66, 0x6f, 0x6c,
	0x6c, 0x6f, 0x77, 0x5f, 0x75, 0x70, 0x5f, 0x73, 0x6d, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x11, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x46, 0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x55, 0x70,
	0x53, 0x6d, 0x73, 0x12, 0x31, 0x0a, 0x15, 0x66, 0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x5f, 0x75, 0x70,
	0x5f, 0x73, 0x6d, 0x73, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x12, 0x66, 0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x55, 0x70, 0x53, 0x6d, 0x73, 0x43,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x2c, 0x0a, 0x12, 0x63, 0x61, 0x6c, 0x6c, 0x5f, 0x72,
	0x69, 0x6e, 0x67, 0x5f, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0a, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x10, 0x63, 0x61, 0x6c, 0x6c, 0x52, 0x69, 0x6e, 0x67, 0x44, 0x75, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x22, 0x8b, 0x01, 0x0a, 0x0f, 0x44, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63,
	0x68, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1d, 0x0a, 0x19, 0x44, 0x49, 0x53, 0x50,
	0x41, 0x54, 0x43, 0x48, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43,
	0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0f, 0x0a, 0x0b, 0x41, 0x4c, 0x4c, 0x5f, 0x41,
	0x54, 0x5f, 0x4f, 0x4e, 0x43, 0x45, 0x10, 0x01, 0x12, 0x12, 0x0a, 0x0e, 0x57, 0x45, 0x42, 0x5f,
	0x43, 0x41, 0x4c, 0x4c, 0x5f, 0x46, 0x49, 0x52, 0x53, 0x54, 0x10, 0x02, 0x12, 0x15, 0x0a, 0x11,
	0x4d, 0x4f, 0x42, 0x49, 0x4c, 0x45, 0x5f, 0x43, 0x41, 0x4c, 0x4c, 0x5f, 0x46, 0x49, 0x52, 0x53,
	0x54, 0x10, 0x03, 0x12, 0x0f, 0x0a, 0x0b, 0x4d, 0x4f, 0x42, 0x49, 0x4c, 0x45, 0x5f, 0x4f, 0x4e,
	0x4c, 0x59, 0x10, 0x04, 0x12, 0x0c, 0x0a, 0x08, 0x57, 0x45, 0x42, 0x5f, 0x4f, 0x4e, 0x4c, 0x59,
	0x10, 0x05, 0x22, 0x62, 0x0a, 0x0c, 0x53, 0x65, 0x61, 0x74, 0x73, 0x53, 0x65, 0x74, 0x74, 0x69,
	0x6e, 0x67, 0x12, 0x36, 0x0a, 0x05, 0x73, 0x65, 0x61, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x20, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x65, 0x6e, 0x67, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x53,
	0x65, 0x61, 0x74, 0x52, 0x05, 0x73, 0x65, 0x61, 0x74, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x61,
	0x70, 0x61, 0x63, 0x69, 0x74, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x63, 0x61,
	0x70, 0x61, 0x63, 0x69, 0x74, 0x79, 0x22, 0x64, 0x0a, 0x04, 0x53, 0x65, 0x61, 0x74, 0x12, 0x3e,
	0x0a, 0x05, 0x73, 0x74, 0x61, 0x66, 0x66, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67,
	0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x61,
	0x66, 0x66, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x05, 0x73, 0x74, 0x61, 0x66, 0x66, 0x12, 0x1c,
	0x0a, 0x09, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x09, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x22, 0xcf, 0x01, 0x0a,
	0x07, 0x54, 0x6d, 0x70, 0x53, 0x65, 0x61, 0x74, 0x12, 0x3e, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x66,
	0x66, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x61, 0x66, 0x66, 0x4d, 0x6f, 0x64, 0x65,
	0x6c, 0x52, 0x05, 0x73, 0x74, 0x61, 0x66, 0x66, 0x12, 0x39, 0x0a, 0x04, 0x72, 0x6f, 0x6c, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e,
	0x2e, 0x76, 0x31, 0x2e, 0x52, 0x6f, 0x6c, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x04, 0x72,
	0x6f, 0x6c, 0x65, 0x12, 0x49, 0x0a, 0x0c, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x26, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x67, 0x61, 0x67, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x54, 0x79, 0x70,
	0x65, 0x52, 0x0b, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x73, 0x2a, 0x5a,
	0x0a, 0x0a, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1b, 0x0a, 0x17,
	0x44, 0x45, 0x56, 0x49, 0x43, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50,
	0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x13, 0x0a, 0x0f, 0x44, 0x45, 0x56,
	0x49, 0x43, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x57, 0x45, 0x42, 0x10, 0x01, 0x12, 0x1a,
	0x0a, 0x16, 0x44, 0x45, 0x56, 0x49, 0x43, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4d, 0x4f,
	0x42, 0x49, 0x4c, 0x45, 0x5f, 0x41, 0x50, 0x50, 0x10, 0x02, 0x42, 0x84, 0x01, 0x0a, 0x22, 0x63,
	0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x67, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76,
	0x31, 0x50, 0x01, 0x5a, 0x5c, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f,
	0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x65, 0x6e, 0x67, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e,
	0x74, 0x2f, 0x76, 0x31, 0x3b, 0x65, 0x6e, 0x67, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x70,
	0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_engagement_v1_setting_proto_rawDescOnce sync.Once
	file_moego_models_engagement_v1_setting_proto_rawDescData = file_moego_models_engagement_v1_setting_proto_rawDesc
)

func file_moego_models_engagement_v1_setting_proto_rawDescGZIP() []byte {
	file_moego_models_engagement_v1_setting_proto_rawDescOnce.Do(func() {
		file_moego_models_engagement_v1_setting_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_engagement_v1_setting_proto_rawDescData)
	})
	return file_moego_models_engagement_v1_setting_proto_rawDescData
}

var file_moego_models_engagement_v1_setting_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_moego_models_engagement_v1_setting_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_moego_models_engagement_v1_setting_proto_goTypes = []interface{}{
	(DeviceType)(0),              // 0: moego.models.engagement.v1.DeviceType
	(Setting_DispatchingType)(0), // 1: moego.models.engagement.v1.Setting.DispatchingType
	(*Setting)(nil),              // 2: moego.models.engagement.v1.Setting
	(*SeatsSetting)(nil),         // 3: moego.models.engagement.v1.SeatsSetting
	(*Seat)(nil),                 // 4: moego.models.engagement.v1.Seat
	(*TmpSeat)(nil),              // 5: moego.models.engagement.v1.TmpSeat
	(*v1.TimeOfDayInterval)(nil), // 6: moego.utils.v1.TimeOfDayInterval
	(*v11.StaffModel)(nil),       // 7: moego.models.organization.v1.StaffModel
	(*v12.RoleModel)(nil),        // 8: moego.models.permission.v1.RoleModel
}
var file_moego_models_engagement_v1_setting_proto_depIdxs = []int32{
	1, // 0: moego.models.engagement.v1.Setting.dispatching_type:type_name -> moego.models.engagement.v1.Setting.DispatchingType
	6, // 1: moego.models.engagement.v1.Setting.business_hours_range:type_name -> moego.utils.v1.TimeOfDayInterval
	4, // 2: moego.models.engagement.v1.SeatsSetting.seats:type_name -> moego.models.engagement.v1.Seat
	7, // 3: moego.models.engagement.v1.Seat.staff:type_name -> moego.models.organization.v1.StaffModel
	7, // 4: moego.models.engagement.v1.TmpSeat.staff:type_name -> moego.models.organization.v1.StaffModel
	8, // 5: moego.models.engagement.v1.TmpSeat.role:type_name -> moego.models.permission.v1.RoleModel
	0, // 6: moego.models.engagement.v1.TmpSeat.device_types:type_name -> moego.models.engagement.v1.DeviceType
	7, // [7:7] is the sub-list for method output_type
	7, // [7:7] is the sub-list for method input_type
	7, // [7:7] is the sub-list for extension type_name
	7, // [7:7] is the sub-list for extension extendee
	0, // [0:7] is the sub-list for field type_name
}

func init() { file_moego_models_engagement_v1_setting_proto_init() }
func file_moego_models_engagement_v1_setting_proto_init() {
	if File_moego_models_engagement_v1_setting_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_models_engagement_v1_setting_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Setting); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_engagement_v1_setting_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SeatsSetting); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_engagement_v1_setting_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Seat); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_engagement_v1_setting_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TmpSeat); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_engagement_v1_setting_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_engagement_v1_setting_proto_goTypes,
		DependencyIndexes: file_moego_models_engagement_v1_setting_proto_depIdxs,
		EnumInfos:         file_moego_models_engagement_v1_setting_proto_enumTypes,
		MessageInfos:      file_moego_models_engagement_v1_setting_proto_msgTypes,
	}.Build()
	File_moego_models_engagement_v1_setting_proto = out.File
	file_moego_models_engagement_v1_setting_proto_rawDesc = nil
	file_moego_models_engagement_v1_setting_proto_goTypes = nil
	file_moego_models_engagement_v1_setting_proto_depIdxs = nil
}
