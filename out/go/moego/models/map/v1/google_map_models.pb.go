// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/map/v1/google_map_models.proto

package mappb

import (
	viewport "google.golang.org/genproto/googleapis/geo/type/viewport"
	latlng "google.golang.org/genproto/googleapis/type/latlng"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// GooglePlace
// https://developers.google.com/maps/documentation/places/web-service/reference/rest/v1/places#resource:-place
type GooglePlace struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The unique identifier of a place.
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// This Place's resource name, in `places/{place_id}` format.  Can be used to
	// look up the Place.
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// A set of type tags for this result. For example, "political" and
	// "locality". For the complete list of possible values, see Table A and Table
	// B at
	// https://developers.google.com/maps/documentation/places/web-service/place-types
	Types []string `protobuf:"bytes,3,rep,name=types,proto3" json:"types,omitempty"`
	// A full, human-readable address for this place.
	FormattedAddress string `protobuf:"bytes,4,opt,name=formatted_address,json=formattedAddress,proto3" json:"formatted_address,omitempty"`
	// A short, human-readable address for this place.
	ShortFormattedAddress string `protobuf:"bytes,5,opt,name=short_formatted_address,json=shortFormattedAddress,proto3" json:"short_formatted_address,omitempty"`
	// Repeated components for each locality level.
	// Note the following facts about the address_components[] array:
	// - The array of address components may contain more components than the
	// formatted_address.
	// - The array does not necessarily include all the political entities that
	// contain an address, apart from those included in the formatted_address. To
	// retrieve all the political entities that contain a specific address, you
	// should use reverse geocoding, passing the latitude/longitude of the address
	// as a parameter to the request.
	// - The format of the response is not guaranteed to remain the same between
	// requests. In particular, the number of address_components varies based on
	// the address requested and can change over time for the same address. A
	// component can change position in the array. The type of the component can
	// change. A particular component may be missing in a later response.
	AddressComponents []*GooglePlace_AddressComponent `protobuf:"bytes,6,rep,name=address_components,json=addressComponents,proto3" json:"address_components,omitempty"`
	// Plus code of the place location lat/long.
	PlusCode *GooglePlace_PlusCode `protobuf:"bytes,7,opt,name=plus_code,json=plusCode,proto3" json:"plus_code,omitempty"`
	// The position of this place.
	Location *latlng.LatLng `protobuf:"bytes,8,opt,name=location,proto3" json:"location,omitempty"`
	// A viewport suitable for displaying the place on an average-sized map.
	Viewport *viewport.Viewport `protobuf:"bytes,9,opt,name=viewport,proto3" json:"viewport,omitempty"`
	// Information (including references) about photos of this place. A maximum of
	// 10 photos can be returned.
	Photos []*Photo `protobuf:"bytes,10,rep,name=photos,proto3" json:"photos,omitempty"`
	// The place's address in adr microformat: http://microformats.org/wiki/adr.
	AdrFormatAddress string `protobuf:"bytes,11,opt,name=adr_format_address,json=adrFormatAddress,proto3" json:"adr_format_address,omitempty"`
	// A set of data provider that must be shown with this result.
	Attributions []*GooglePlace_Attribution `protobuf:"bytes,12,rep,name=attributions,proto3" json:"attributions,omitempty"`
}

func (x *GooglePlace) Reset() {
	*x = GooglePlace{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_map_v1_google_map_models_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GooglePlace) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GooglePlace) ProtoMessage() {}

func (x *GooglePlace) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_map_v1_google_map_models_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GooglePlace.ProtoReflect.Descriptor instead.
func (*GooglePlace) Descriptor() ([]byte, []int) {
	return file_moego_models_map_v1_google_map_models_proto_rawDescGZIP(), []int{0}
}

func (x *GooglePlace) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *GooglePlace) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *GooglePlace) GetTypes() []string {
	if x != nil {
		return x.Types
	}
	return nil
}

func (x *GooglePlace) GetFormattedAddress() string {
	if x != nil {
		return x.FormattedAddress
	}
	return ""
}

func (x *GooglePlace) GetShortFormattedAddress() string {
	if x != nil {
		return x.ShortFormattedAddress
	}
	return ""
}

func (x *GooglePlace) GetAddressComponents() []*GooglePlace_AddressComponent {
	if x != nil {
		return x.AddressComponents
	}
	return nil
}

func (x *GooglePlace) GetPlusCode() *GooglePlace_PlusCode {
	if x != nil {
		return x.PlusCode
	}
	return nil
}

func (x *GooglePlace) GetLocation() *latlng.LatLng {
	if x != nil {
		return x.Location
	}
	return nil
}

func (x *GooglePlace) GetViewport() *viewport.Viewport {
	if x != nil {
		return x.Viewport
	}
	return nil
}

func (x *GooglePlace) GetPhotos() []*Photo {
	if x != nil {
		return x.Photos
	}
	return nil
}

func (x *GooglePlace) GetAdrFormatAddress() string {
	if x != nil {
		return x.AdrFormatAddress
	}
	return ""
}

func (x *GooglePlace) GetAttributions() []*GooglePlace_Attribution {
	if x != nil {
		return x.Attributions
	}
	return nil
}

// Information about a photo of a place.
type Photo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Identifier. A reference representing this place photo which may be used to
	// look up this place photo again (also called the API "resource" name:
	// `places/{place_id}/photos/{photo}`).
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// The maximum available width, in pixels.
	WidthPx int32 `protobuf:"varint,2,opt,name=width_px,json=widthPx,proto3" json:"width_px,omitempty"`
	// The maximum available height, in pixels.
	HeightPx int32 `protobuf:"varint,3,opt,name=height_px,json=heightPx,proto3" json:"height_px,omitempty"`
	// This photo's authors.
	AuthorAttributions []*AuthorAttribution `protobuf:"bytes,4,rep,name=author_attributions,json=authorAttributions,proto3" json:"author_attributions,omitempty"`
}

func (x *Photo) Reset() {
	*x = Photo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_map_v1_google_map_models_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Photo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Photo) ProtoMessage() {}

func (x *Photo) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_map_v1_google_map_models_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Photo.ProtoReflect.Descriptor instead.
func (*Photo) Descriptor() ([]byte, []int) {
	return file_moego_models_map_v1_google_map_models_proto_rawDescGZIP(), []int{1}
}

func (x *Photo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Photo) GetWidthPx() int32 {
	if x != nil {
		return x.WidthPx
	}
	return 0
}

func (x *Photo) GetHeightPx() int32 {
	if x != nil {
		return x.HeightPx
	}
	return 0
}

func (x *Photo) GetAuthorAttributions() []*AuthorAttribution {
	if x != nil {
		return x.AuthorAttributions
	}
	return nil
}

// Information about the author of the UGC data. Used in
// [Photo][google.maps.places.v1.Photo], and
// [Review][google.maps.places.v1.Review].
type AuthorAttribution struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Name of the author of the [Photo][google.maps.places.v1.Photo] or
	// [Review][google.maps.places.v1.Review].
	DisplayName string `protobuf:"bytes,1,opt,name=display_name,json=displayName,proto3" json:"display_name,omitempty"`
	// URI of the author of the [Photo][google.maps.places.v1.Photo] or
	// [Review][google.maps.places.v1.Review].
	Uri string `protobuf:"bytes,2,opt,name=uri,proto3" json:"uri,omitempty"`
	// Profile photo URI of the author of the
	// [Photo][google.maps.places.v1.Photo] or
	// [Review][google.maps.places.v1.Review].
	PhotoUri string `protobuf:"bytes,3,opt,name=photo_uri,json=photoUri,proto3" json:"photo_uri,omitempty"`
}

func (x *AuthorAttribution) Reset() {
	*x = AuthorAttribution{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_map_v1_google_map_models_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AuthorAttribution) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AuthorAttribution) ProtoMessage() {}

func (x *AuthorAttribution) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_map_v1_google_map_models_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AuthorAttribution.ProtoReflect.Descriptor instead.
func (*AuthorAttribution) Descriptor() ([]byte, []int) {
	return file_moego_models_map_v1_google_map_models_proto_rawDescGZIP(), []int{2}
}

func (x *AuthorAttribution) GetDisplayName() string {
	if x != nil {
		return x.DisplayName
	}
	return ""
}

func (x *AuthorAttribution) GetUri() string {
	if x != nil {
		return x.Uri
	}
	return ""
}

func (x *AuthorAttribution) GetPhotoUri() string {
	if x != nil {
		return x.PhotoUri
	}
	return ""
}

// The structured components that form the formatted address, if this
// information is available.
type GooglePlace_AddressComponent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The full text description or name of the address component. For example,
	// an address component for the country Australia may have a long_name of
	// "Australia".
	LongText string `protobuf:"bytes,1,opt,name=long_text,json=longText,proto3" json:"long_text,omitempty"`
	// An abbreviated textual name for the address component, if available. For
	// example, an address component for the country of Australia may have a
	// short_name of "AU".
	ShortText string `protobuf:"bytes,2,opt,name=short_text,json=shortText,proto3" json:"short_text,omitempty"`
	// An array indicating the type(s) of the address component.
	Types []string `protobuf:"bytes,3,rep,name=types,proto3" json:"types,omitempty"`
	// The language used to format this components, in CLDR notation.
	LanguageCode string `protobuf:"bytes,4,opt,name=language_code,json=languageCode,proto3" json:"language_code,omitempty"`
}

func (x *GooglePlace_AddressComponent) Reset() {
	*x = GooglePlace_AddressComponent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_map_v1_google_map_models_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GooglePlace_AddressComponent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GooglePlace_AddressComponent) ProtoMessage() {}

func (x *GooglePlace_AddressComponent) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_map_v1_google_map_models_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GooglePlace_AddressComponent.ProtoReflect.Descriptor instead.
func (*GooglePlace_AddressComponent) Descriptor() ([]byte, []int) {
	return file_moego_models_map_v1_google_map_models_proto_rawDescGZIP(), []int{0, 0}
}

func (x *GooglePlace_AddressComponent) GetLongText() string {
	if x != nil {
		return x.LongText
	}
	return ""
}

func (x *GooglePlace_AddressComponent) GetShortText() string {
	if x != nil {
		return x.ShortText
	}
	return ""
}

func (x *GooglePlace_AddressComponent) GetTypes() []string {
	if x != nil {
		return x.Types
	}
	return nil
}

func (x *GooglePlace_AddressComponent) GetLanguageCode() string {
	if x != nil {
		return x.LanguageCode
	}
	return ""
}

// Plus code (http://plus.codes) is a location reference with two formats:
// global code defining a 14mx14m (1/8000th of a degree) or smaller rectangle,
// and compound code, replacing the prefix with a reference location.
type GooglePlace_PlusCode struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Place's global (full) code, such as "9FWM33GV+HQ", representing an
	// 1/8000 by 1/8000 degree area (~14 by 14 meters).
	GlobalCode string `protobuf:"bytes,1,opt,name=global_code,json=globalCode,proto3" json:"global_code,omitempty"`
	// Place's compound code, such as "33GV+HQ, Ramberg, Norway", containing
	// the suffix of the global code and replacing the prefix with a formatted
	// name of a reference entity.
	CompoundCode string `protobuf:"bytes,2,opt,name=compound_code,json=compoundCode,proto3" json:"compound_code,omitempty"`
}

func (x *GooglePlace_PlusCode) Reset() {
	*x = GooglePlace_PlusCode{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_map_v1_google_map_models_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GooglePlace_PlusCode) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GooglePlace_PlusCode) ProtoMessage() {}

func (x *GooglePlace_PlusCode) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_map_v1_google_map_models_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GooglePlace_PlusCode.ProtoReflect.Descriptor instead.
func (*GooglePlace_PlusCode) Descriptor() ([]byte, []int) {
	return file_moego_models_map_v1_google_map_models_proto_rawDescGZIP(), []int{0, 1}
}

func (x *GooglePlace_PlusCode) GetGlobalCode() string {
	if x != nil {
		return x.GlobalCode
	}
	return ""
}

func (x *GooglePlace_PlusCode) GetCompoundCode() string {
	if x != nil {
		return x.CompoundCode
	}
	return ""
}

// Information about data providers of this place.
type GooglePlace_Attribution struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Name of the Place's data provider.
	Provider string `protobuf:"bytes,1,opt,name=provider,proto3" json:"provider,omitempty"`
	// URI to the Place's data provider.
	ProviderUri string `protobuf:"bytes,2,opt,name=provider_uri,json=providerUri,proto3" json:"provider_uri,omitempty"`
}

func (x *GooglePlace_Attribution) Reset() {
	*x = GooglePlace_Attribution{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_map_v1_google_map_models_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GooglePlace_Attribution) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GooglePlace_Attribution) ProtoMessage() {}

func (x *GooglePlace_Attribution) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_map_v1_google_map_models_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GooglePlace_Attribution.ProtoReflect.Descriptor instead.
func (*GooglePlace_Attribution) Descriptor() ([]byte, []int) {
	return file_moego_models_map_v1_google_map_models_proto_rawDescGZIP(), []int{0, 2}
}

func (x *GooglePlace_Attribution) GetProvider() string {
	if x != nil {
		return x.Provider
	}
	return ""
}

func (x *GooglePlace_Attribution) GetProviderUri() string {
	if x != nil {
		return x.ProviderUri
	}
	return ""
}

var File_moego_models_map_v1_google_map_models_proto protoreflect.FileDescriptor

var file_moego_models_map_v1_google_map_models_proto_rawDesc = []byte{
	0x0a, 0x2b, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6d,
	0x61, 0x70, 0x2f, 0x76, 0x31, 0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x5f, 0x6d, 0x61, 0x70,
	0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x13, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x61, 0x70, 0x2e,
	0x76, 0x31, 0x1a, 0x1e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x67, 0x65, 0x6f, 0x2f, 0x74,
	0x79, 0x70, 0x65, 0x2f, 0x76, 0x69, 0x65, 0x77, 0x70, 0x6f, 0x72, 0x74, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x18, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f,
	0x6c, 0x61, 0x74, 0x6c, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x9e, 0x07, 0x0a,
	0x0b, 0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x50, 0x6c, 0x61, 0x63, 0x65, 0x12, 0x0e, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x12, 0x14, 0x0a, 0x05, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x05, 0x74, 0x79, 0x70, 0x65, 0x73, 0x12, 0x2b, 0x0a, 0x11, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74,
	0x74, 0x65, 0x64, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x10, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x74, 0x65, 0x64, 0x41, 0x64, 0x64, 0x72,
	0x65, 0x73, 0x73, 0x12, 0x36, 0x0a, 0x17, 0x73, 0x68, 0x6f, 0x72, 0x74, 0x5f, 0x66, 0x6f, 0x72,
	0x6d, 0x61, 0x74, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x15, 0x73, 0x68, 0x6f, 0x72, 0x74, 0x46, 0x6f, 0x72, 0x6d, 0x61,
	0x74, 0x74, 0x65, 0x64, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x60, 0x0a, 0x12, 0x61,
	0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74,
	0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x61, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x50, 0x6c, 0x61, 0x63, 0x65, 0x2e, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73,
	0x73, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52, 0x11, 0x61, 0x64, 0x64, 0x72,
	0x65, 0x73, 0x73, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x46, 0x0a,
	0x09, 0x70, 0x6c, 0x75, 0x73, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x6d, 0x61, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x50, 0x6c, 0x61,
	0x63, 0x65, 0x2e, 0x50, 0x6c, 0x75, 0x73, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x08, 0x70, 0x6c, 0x75,
	0x73, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x2f, 0x0a, 0x08, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4c, 0x61, 0x74, 0x4c, 0x6e, 0x67, 0x52, 0x08, 0x6c, 0x6f,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x35, 0x0a, 0x08, 0x76, 0x69, 0x65, 0x77, 0x70, 0x6f,
	0x72, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x67, 0x65, 0x6f, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x56, 0x69, 0x65, 0x77, 0x70,
	0x6f, 0x72, 0x74, 0x52, 0x08, 0x76, 0x69, 0x65, 0x77, 0x70, 0x6f, 0x72, 0x74, 0x12, 0x32, 0x0a,
	0x06, 0x70, 0x68, 0x6f, 0x74, 0x6f, 0x73, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x61, 0x70,
	0x2e, 0x76, 0x31, 0x2e, 0x50, 0x68, 0x6f, 0x74, 0x6f, 0x52, 0x06, 0x70, 0x68, 0x6f, 0x74, 0x6f,
	0x73, 0x12, 0x2c, 0x0a, 0x12, 0x61, 0x64, 0x72, 0x5f, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x5f,
	0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x61,
	0x64, 0x72, 0x46, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12,
	0x50, 0x0a, 0x0c, 0x61, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18,
	0x0c, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x61, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x50, 0x6c, 0x61, 0x63, 0x65, 0x2e, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x0c, 0x61, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x1a, 0x89, 0x01, 0x0a, 0x10, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x43, 0x6f, 0x6d,
	0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x6c, 0x6f, 0x6e, 0x67, 0x5f, 0x74,
	0x65, 0x78, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6c, 0x6f, 0x6e, 0x67, 0x54,
	0x65, 0x78, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x68, 0x6f, 0x72, 0x74, 0x5f, 0x74, 0x65, 0x78,
	0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x68, 0x6f, 0x72, 0x74, 0x54, 0x65,
	0x78, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28,
	0x09, 0x52, 0x05, 0x74, 0x79, 0x70, 0x65, 0x73, 0x12, 0x23, 0x0a, 0x0d, 0x6c, 0x61, 0x6e, 0x67,
	0x75, 0x61, 0x67, 0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0c, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x1a, 0x50, 0x0a,
	0x08, 0x50, 0x6c, 0x75, 0x73, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x67, 0x6c, 0x6f,
	0x62, 0x61, 0x6c, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a,
	0x67, 0x6c, 0x6f, 0x62, 0x61, 0x6c, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x63, 0x6f,
	0x6d, 0x70, 0x6f, 0x75, 0x6e, 0x64, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0c, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x75, 0x6e, 0x64, 0x43, 0x6f, 0x64, 0x65, 0x1a,
	0x4c, 0x0a, 0x0b, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1a,
	0x0a, 0x08, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x12, 0x21, 0x0a, 0x0c, 0x70, 0x72,
	0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x5f, 0x75, 0x72, 0x69, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x55, 0x72, 0x69, 0x22, 0xac, 0x01,
	0x0a, 0x05, 0x50, 0x68, 0x6f, 0x74, 0x6f, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x77,
	0x69, 0x64, 0x74, 0x68, 0x5f, 0x70, 0x78, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x77,
	0x69, 0x64, 0x74, 0x68, 0x50, 0x78, 0x12, 0x1b, 0x0a, 0x09, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74,
	0x5f, 0x70, 0x78, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x68, 0x65, 0x69, 0x67, 0x68,
	0x74, 0x50, 0x78, 0x12, 0x57, 0x0a, 0x13, 0x61, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x5f, 0x61, 0x74,
	0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x26, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x6d, 0x61, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x41, 0x74, 0x74,
	0x72, 0x69, 0x62, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x12, 0x61, 0x75, 0x74, 0x68, 0x6f, 0x72,
	0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0x65, 0x0a, 0x11,
	0x41, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x21, 0x0a, 0x0c, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x72, 0x69, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x75, 0x72, 0x69, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x68, 0x6f, 0x74, 0x6f, 0x5f,
	0x75, 0x72, 0x69, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x68, 0x6f, 0x74, 0x6f,
	0x55, 0x72, 0x69, 0x42, 0x6f, 0x0a, 0x1b, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x61, 0x70, 0x2e,
	0x76, 0x31, 0x50, 0x01, 0x5a, 0x4e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d,
	0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6d, 0x61, 0x70, 0x2f, 0x76, 0x31, 0x3b, 0x6d,
	0x61, 0x70, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_map_v1_google_map_models_proto_rawDescOnce sync.Once
	file_moego_models_map_v1_google_map_models_proto_rawDescData = file_moego_models_map_v1_google_map_models_proto_rawDesc
)

func file_moego_models_map_v1_google_map_models_proto_rawDescGZIP() []byte {
	file_moego_models_map_v1_google_map_models_proto_rawDescOnce.Do(func() {
		file_moego_models_map_v1_google_map_models_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_map_v1_google_map_models_proto_rawDescData)
	})
	return file_moego_models_map_v1_google_map_models_proto_rawDescData
}

var file_moego_models_map_v1_google_map_models_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_moego_models_map_v1_google_map_models_proto_goTypes = []interface{}{
	(*GooglePlace)(nil),                  // 0: moego.models.map.v1.GooglePlace
	(*Photo)(nil),                        // 1: moego.models.map.v1.Photo
	(*AuthorAttribution)(nil),            // 2: moego.models.map.v1.AuthorAttribution
	(*GooglePlace_AddressComponent)(nil), // 3: moego.models.map.v1.GooglePlace.AddressComponent
	(*GooglePlace_PlusCode)(nil),         // 4: moego.models.map.v1.GooglePlace.PlusCode
	(*GooglePlace_Attribution)(nil),      // 5: moego.models.map.v1.GooglePlace.Attribution
	(*latlng.LatLng)(nil),                // 6: google.type.LatLng
	(*viewport.Viewport)(nil),            // 7: google.geo.type.Viewport
}
var file_moego_models_map_v1_google_map_models_proto_depIdxs = []int32{
	3, // 0: moego.models.map.v1.GooglePlace.address_components:type_name -> moego.models.map.v1.GooglePlace.AddressComponent
	4, // 1: moego.models.map.v1.GooglePlace.plus_code:type_name -> moego.models.map.v1.GooglePlace.PlusCode
	6, // 2: moego.models.map.v1.GooglePlace.location:type_name -> google.type.LatLng
	7, // 3: moego.models.map.v1.GooglePlace.viewport:type_name -> google.geo.type.Viewport
	1, // 4: moego.models.map.v1.GooglePlace.photos:type_name -> moego.models.map.v1.Photo
	5, // 5: moego.models.map.v1.GooglePlace.attributions:type_name -> moego.models.map.v1.GooglePlace.Attribution
	2, // 6: moego.models.map.v1.Photo.author_attributions:type_name -> moego.models.map.v1.AuthorAttribution
	7, // [7:7] is the sub-list for method output_type
	7, // [7:7] is the sub-list for method input_type
	7, // [7:7] is the sub-list for extension type_name
	7, // [7:7] is the sub-list for extension extendee
	0, // [0:7] is the sub-list for field type_name
}

func init() { file_moego_models_map_v1_google_map_models_proto_init() }
func file_moego_models_map_v1_google_map_models_proto_init() {
	if File_moego_models_map_v1_google_map_models_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_models_map_v1_google_map_models_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GooglePlace); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_map_v1_google_map_models_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Photo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_map_v1_google_map_models_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AuthorAttribution); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_map_v1_google_map_models_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GooglePlace_AddressComponent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_map_v1_google_map_models_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GooglePlace_PlusCode); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_map_v1_google_map_models_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GooglePlace_Attribution); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_map_v1_google_map_models_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_map_v1_google_map_models_proto_goTypes,
		DependencyIndexes: file_moego_models_map_v1_google_map_models_proto_depIdxs,
		MessageInfos:      file_moego_models_map_v1_google_map_models_proto_msgTypes,
	}.Build()
	File_moego_models_map_v1_google_map_models_proto = out.File
	file_moego_models_map_v1_google_map_models_proto_rawDesc = nil
	file_moego_models_map_v1_google_map_models_proto_goTypes = nil
	file_moego_models_map_v1_google_map_models_proto_depIdxs = nil
}
