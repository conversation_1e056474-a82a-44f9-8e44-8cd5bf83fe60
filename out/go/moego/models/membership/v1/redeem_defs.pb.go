// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/membership/v1/redeem_defs.proto

package membershippb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Benefit redeem definition
type BenefitRedeemDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the benefit id
	BenefitId int64 `protobuf:"varint,1,opt,name=benefit_id,json=benefitId,proto3" json:"benefit_id,omitempty"`
	// the quantity, discount 没有数量限制
	Amount *int32 `protobuf:"varint,2,opt,name=amount,proto3,oneof" json:"amount,omitempty"`
	// feature item id
	//
	// Types that are assignable to FeatureItemId:
	//
	//	*BenefitRedeemDef_AddOnId
	//	*BenefitRedeemDef_ServiceId
	//	*BenefitRedeemDef_Boarding
	//	*BenefitRedeemDef_ProductId
	FeatureItemId isBenefitRedeemDef_FeatureItemId `protobuf_oneof:"feature_item_id"`
}

func (x *BenefitRedeemDef) Reset() {
	*x = BenefitRedeemDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_membership_v1_redeem_defs_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BenefitRedeemDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BenefitRedeemDef) ProtoMessage() {}

func (x *BenefitRedeemDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_membership_v1_redeem_defs_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BenefitRedeemDef.ProtoReflect.Descriptor instead.
func (*BenefitRedeemDef) Descriptor() ([]byte, []int) {
	return file_moego_models_membership_v1_redeem_defs_proto_rawDescGZIP(), []int{0}
}

func (x *BenefitRedeemDef) GetBenefitId() int64 {
	if x != nil {
		return x.BenefitId
	}
	return 0
}

func (x *BenefitRedeemDef) GetAmount() int32 {
	if x != nil && x.Amount != nil {
		return *x.Amount
	}
	return 0
}

func (m *BenefitRedeemDef) GetFeatureItemId() isBenefitRedeemDef_FeatureItemId {
	if m != nil {
		return m.FeatureItemId
	}
	return nil
}

func (x *BenefitRedeemDef) GetAddOnId() int64 {
	if x, ok := x.GetFeatureItemId().(*BenefitRedeemDef_AddOnId); ok {
		return x.AddOnId
	}
	return 0
}

func (x *BenefitRedeemDef) GetServiceId() int64 {
	if x, ok := x.GetFeatureItemId().(*BenefitRedeemDef_ServiceId); ok {
		return x.ServiceId
	}
	return 0
}

func (x *BenefitRedeemDef) GetBoarding() *BoardingRedeemScenarioItem {
	if x, ok := x.GetFeatureItemId().(*BenefitRedeemDef_Boarding); ok {
		return x.Boarding
	}
	return nil
}

func (x *BenefitRedeemDef) GetProductId() int64 {
	if x, ok := x.GetFeatureItemId().(*BenefitRedeemDef_ProductId); ok {
		return x.ProductId
	}
	return 0
}

type isBenefitRedeemDef_FeatureItemId interface {
	isBenefitRedeemDef_FeatureItemId()
}

type BenefitRedeemDef_AddOnId struct {
	// add-on
	AddOnId int64 `protobuf:"varint,3,opt,name=add_on_id,json=addOnId,proto3,oneof"`
}

type BenefitRedeemDef_ServiceId struct {
	// service
	ServiceId int64 `protobuf:"varint,4,opt,name=service_id,json=serviceId,proto3,oneof"`
}

type BenefitRedeemDef_Boarding struct {
	// boarding
	Boarding *BoardingRedeemScenarioItem `protobuf:"bytes,5,opt,name=boarding,proto3,oneof"`
}

type BenefitRedeemDef_ProductId struct {
	// product
	ProductId int64 `protobuf:"varint,6,opt,name=product_id,json=productId,proto3,oneof"`
}

func (*BenefitRedeemDef_AddOnId) isBenefitRedeemDef_FeatureItemId() {}

func (*BenefitRedeemDef_ServiceId) isBenefitRedeemDef_FeatureItemId() {}

func (*BenefitRedeemDef_Boarding) isBenefitRedeemDef_FeatureItemId() {}

func (*BenefitRedeemDef_ProductId) isBenefitRedeemDef_FeatureItemId() {}

var File_moego_models_membership_v1_redeem_defs_proto protoreflect.FileDescriptor

var file_moego_models_membership_v1_redeem_defs_proto_rawDesc = []byte{
	0x0a, 0x2c, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6d,
	0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x2f, 0x76, 0x31, 0x2f, 0x72, 0x65, 0x64,
	0x65, 0x65, 0x6d, 0x5f, 0x64, 0x65, 0x66, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1a,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x6d,
	0x62, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x2e, 0x76, 0x31, 0x1a, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73,
	0x68, 0x69, 0x70, 0x2f, 0x76, 0x31, 0x2f, 0x72, 0x65, 0x64, 0x65, 0x65, 0x6d, 0x5f, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xa2, 0x02, 0x0a, 0x10, 0x42,
	0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x52, 0x65, 0x64, 0x65, 0x65, 0x6d, 0x44, 0x65, 0x66, 0x12,
	0x1d, 0x0a, 0x0a, 0x62, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x09, 0x62, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x49, 0x64, 0x12, 0x1b,
	0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x48, 0x01,
	0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x88, 0x01, 0x01, 0x12, 0x1c, 0x0a, 0x09, 0x61,
	0x64, 0x64, 0x5f, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x48, 0x00,
	0x52, 0x07, 0x61, 0x64, 0x64, 0x4f, 0x6e, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0a, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x48, 0x00, 0x52,
	0x09, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x12, 0x54, 0x0a, 0x08, 0x62, 0x6f,
	0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x36, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x6d, 0x62,
	0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x6f, 0x61, 0x72, 0x64, 0x69,
	0x6e, 0x67, 0x52, 0x65, 0x64, 0x65, 0x65, 0x6d, 0x53, 0x63, 0x65, 0x6e, 0x61, 0x72, 0x69, 0x6f,
	0x49, 0x74, 0x65, 0x6d, 0x48, 0x00, 0x52, 0x08, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67,
	0x12, 0x1f, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x03, 0x48, 0x00, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x49,
	0x64, 0x42, 0x11, 0x0a, 0x0f, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x5f, 0x69, 0x74, 0x65,
	0x6d, 0x5f, 0x69, 0x64, 0x42, 0x09, 0x0a, 0x07, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x42,
	0x84, 0x01, 0x0a, 0x22, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64,
	0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73,
	0x68, 0x69, 0x70, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x5c, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62,
	0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72,
	0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69,
	0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6d, 0x65, 0x6d, 0x62,
	0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x2f, 0x76, 0x31, 0x3b, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72,
	0x73, 0x68, 0x69, 0x70, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_membership_v1_redeem_defs_proto_rawDescOnce sync.Once
	file_moego_models_membership_v1_redeem_defs_proto_rawDescData = file_moego_models_membership_v1_redeem_defs_proto_rawDesc
)

func file_moego_models_membership_v1_redeem_defs_proto_rawDescGZIP() []byte {
	file_moego_models_membership_v1_redeem_defs_proto_rawDescOnce.Do(func() {
		file_moego_models_membership_v1_redeem_defs_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_membership_v1_redeem_defs_proto_rawDescData)
	})
	return file_moego_models_membership_v1_redeem_defs_proto_rawDescData
}

var file_moego_models_membership_v1_redeem_defs_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_moego_models_membership_v1_redeem_defs_proto_goTypes = []interface{}{
	(*BenefitRedeemDef)(nil),           // 0: moego.models.membership.v1.BenefitRedeemDef
	(*BoardingRedeemScenarioItem)(nil), // 1: moego.models.membership.v1.BoardingRedeemScenarioItem
}
var file_moego_models_membership_v1_redeem_defs_proto_depIdxs = []int32{
	1, // 0: moego.models.membership.v1.BenefitRedeemDef.boarding:type_name -> moego.models.membership.v1.BoardingRedeemScenarioItem
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_moego_models_membership_v1_redeem_defs_proto_init() }
func file_moego_models_membership_v1_redeem_defs_proto_init() {
	if File_moego_models_membership_v1_redeem_defs_proto != nil {
		return
	}
	file_moego_models_membership_v1_redeem_models_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_moego_models_membership_v1_redeem_defs_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BenefitRedeemDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_models_membership_v1_redeem_defs_proto_msgTypes[0].OneofWrappers = []interface{}{
		(*BenefitRedeemDef_AddOnId)(nil),
		(*BenefitRedeemDef_ServiceId)(nil),
		(*BenefitRedeemDef_Boarding)(nil),
		(*BenefitRedeemDef_ProductId)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_membership_v1_redeem_defs_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_membership_v1_redeem_defs_proto_goTypes,
		DependencyIndexes: file_moego_models_membership_v1_redeem_defs_proto_depIdxs,
		MessageInfos:      file_moego_models_membership_v1_redeem_defs_proto_msgTypes,
	}.Build()
	File_moego_models_membership_v1_redeem_defs_proto = out.File
	file_moego_models_membership_v1_redeem_defs_proto_rawDesc = nil
	file_moego_models_membership_v1_redeem_defs_proto_goTypes = nil
	file_moego_models_membership_v1_redeem_defs_proto_depIdxs = nil
}
