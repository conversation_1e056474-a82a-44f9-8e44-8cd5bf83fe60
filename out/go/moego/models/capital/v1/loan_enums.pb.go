// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/capital/v1/loan_enums.proto

package capitalpb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Loan channel
type LoanChannel int32

const (
	// Unspecified
	LoanChannel_LOAN_CHANNEL_UNSPECIFIED LoanChannel = 0
	// Stripe
	LoanChannel_STRIPE LoanChannel = 1
	// Ka<PERSON><PERSON>nel_KANMON LoanChannel = 2
)

// Enum value maps for LoanChannel.
var (
	LoanChannel_name = map[int32]string{
		0: "LOAN_CHANNEL_UNSPECIFIED",
		1: "STRIPE",
		2: "KANMON",
	}
	LoanChannel_value = map[string]int32{
		"LOAN_CHANNEL_UNSPECIFIED": 0,
		"STRIPE":                   1,
		"KANMON":                   2,
	}
)

func (x LoanChannel) Enum() *LoanChannel {
	p := new(LoanChannel)
	*p = x
	return p
}

func (x LoanChannel) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (LoanChannel) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_capital_v1_loan_enums_proto_enumTypes[0].Descriptor()
}

func (LoanChannel) Type() protoreflect.EnumType {
	return &file_moego_models_capital_v1_loan_enums_proto_enumTypes[0]
}

func (x LoanChannel) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use LoanChannel.Descriptor instead.
func (LoanChannel) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_capital_v1_loan_enums_proto_rawDescGZIP(), []int{0}
}

// The type of a loan offer
type LoanOfferType int32

const (
	// Unspecified
	LoanOfferType_LOAN_OFFER_TYPE_UNSPECIFIED LoanOfferType = 0
	// MCA: Merchant Cash Advance
	LoanOfferType_MCA LoanOfferType = 1
	// Term Loan
	LoanOfferType_TERM_LOAN LoanOfferType = 2
)

// Enum value maps for LoanOfferType.
var (
	LoanOfferType_name = map[int32]string{
		0: "LOAN_OFFER_TYPE_UNSPECIFIED",
		1: "MCA",
		2: "TERM_LOAN",
	}
	LoanOfferType_value = map[string]int32{
		"LOAN_OFFER_TYPE_UNSPECIFIED": 0,
		"MCA":                         1,
		"TERM_LOAN":                   2,
	}
)

func (x LoanOfferType) Enum() *LoanOfferType {
	p := new(LoanOfferType)
	*p = x
	return p
}

func (x LoanOfferType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (LoanOfferType) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_capital_v1_loan_enums_proto_enumTypes[1].Descriptor()
}

func (LoanOfferType) Type() protoreflect.EnumType {
	return &file_moego_models_capital_v1_loan_enums_proto_enumTypes[1]
}

func (x LoanOfferType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use LoanOfferType.Descriptor instead.
func (LoanOfferType) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_capital_v1_loan_enums_proto_rawDescGZIP(), []int{1}
}

// The product type of a loan
type LoanProductType int32

const (
	// Unspecified
	LoanProductType_LOAN_PRODUCT_TYPE_UNSPECIFIED LoanProductType = 0
	// Standard loan
	LoanProductType_STANDARD LoanProductType = 1
	// Refill loan
	LoanProductType_REFILL LoanProductType = 2
)

// Enum value maps for LoanProductType.
var (
	LoanProductType_name = map[int32]string{
		0: "LOAN_PRODUCT_TYPE_UNSPECIFIED",
		1: "STANDARD",
		2: "REFILL",
	}
	LoanProductType_value = map[string]int32{
		"LOAN_PRODUCT_TYPE_UNSPECIFIED": 0,
		"STANDARD":                      1,
		"REFILL":                        2,
	}
)

func (x LoanProductType) Enum() *LoanProductType {
	p := new(LoanProductType)
	*p = x
	return p
}

func (x LoanProductType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (LoanProductType) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_capital_v1_loan_enums_proto_enumTypes[2].Descriptor()
}

func (LoanProductType) Type() protoreflect.EnumType {
	return &file_moego_models_capital_v1_loan_enums_proto_enumTypes[2]
}

func (x LoanProductType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use LoanProductType.Descriptor instead.
func (LoanProductType) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_capital_v1_loan_enums_proto_rawDescGZIP(), []int{2}
}

// The status of a loan offer
type LoanOfferStatus int32

const (
	// Unspecified
	LoanOfferStatus_LOAN_OFFER_STATUS_UNSPECIFIED LoanOfferStatus = 0
	// Created
	LoanOfferStatus_CREATED LoanOfferStatus = 1
	// Accepted
	LoanOfferStatus_ACCEPTED LoanOfferStatus = 2
	// Paid out
	LoanOfferStatus_PAID_OUT LoanOfferStatus = 3
	// Fully repaid
	LoanOfferStatus_FULLY_REPAID LoanOfferStatus = 4
	// Expired
	LoanOfferStatus_EXPIRED LoanOfferStatus = 5
	// Rejected
	LoanOfferStatus_REJECTED LoanOfferStatus = 6
	// Canceled
	LoanOfferStatus_CANCELED LoanOfferStatus = 7
	// Replaced (currently not enabled for Stripe)
	LoanOfferStatus_REPLACED LoanOfferStatus = 8
	// Prequalified
	LoanOfferStatus_PREQUALIFIED LoanOfferStatus = 9
)

// Enum value maps for LoanOfferStatus.
var (
	LoanOfferStatus_name = map[int32]string{
		0: "LOAN_OFFER_STATUS_UNSPECIFIED",
		1: "CREATED",
		2: "ACCEPTED",
		3: "PAID_OUT",
		4: "FULLY_REPAID",
		5: "EXPIRED",
		6: "REJECTED",
		7: "CANCELED",
		8: "REPLACED",
		9: "PREQUALIFIED",
	}
	LoanOfferStatus_value = map[string]int32{
		"LOAN_OFFER_STATUS_UNSPECIFIED": 0,
		"CREATED":                       1,
		"ACCEPTED":                      2,
		"PAID_OUT":                      3,
		"FULLY_REPAID":                  4,
		"EXPIRED":                       5,
		"REJECTED":                      6,
		"CANCELED":                      7,
		"REPLACED":                      8,
		"PREQUALIFIED":                  9,
	}
)

func (x LoanOfferStatus) Enum() *LoanOfferStatus {
	p := new(LoanOfferStatus)
	*p = x
	return p
}

func (x LoanOfferStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (LoanOfferStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_capital_v1_loan_enums_proto_enumTypes[3].Descriptor()
}

func (LoanOfferStatus) Type() protoreflect.EnumType {
	return &file_moego_models_capital_v1_loan_enums_proto_enumTypes[3]
}

func (x LoanOfferStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use LoanOfferStatus.Descriptor instead.
func (LoanOfferStatus) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_capital_v1_loan_enums_proto_rawDescGZIP(), []int{3}
}

// The campaign type of a offer term
type LoanCampaignType int32

const (
	// Unspecified
	LoanCampaignType_LOAN_CAMPAIGN_TYPE_UNSPECIFIED LoanCampaignType = 0
	// To Stripe's "newly_eligible_user"
	LoanCampaignType_NEWLY_ELIGIBLE_USER LoanCampaignType = 1
	// To Stripe's "previously_eligible_user"
	LoanCampaignType_PREVIOUSLY_ELIGIBLE_USER LoanCampaignType = 2
	// To Stripe's "repeat_user"
	LoanCampaignType_REPEAT_USER LoanCampaignType = 3
)

// Enum value maps for LoanCampaignType.
var (
	LoanCampaignType_name = map[int32]string{
		0: "LOAN_CAMPAIGN_TYPE_UNSPECIFIED",
		1: "NEWLY_ELIGIBLE_USER",
		2: "PREVIOUSLY_ELIGIBLE_USER",
		3: "REPEAT_USER",
	}
	LoanCampaignType_value = map[string]int32{
		"LOAN_CAMPAIGN_TYPE_UNSPECIFIED": 0,
		"NEWLY_ELIGIBLE_USER":            1,
		"PREVIOUSLY_ELIGIBLE_USER":       2,
		"REPEAT_USER":                    3,
	}
)

func (x LoanCampaignType) Enum() *LoanCampaignType {
	p := new(LoanCampaignType)
	*p = x
	return p
}

func (x LoanCampaignType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (LoanCampaignType) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_capital_v1_loan_enums_proto_enumTypes[4].Descriptor()
}

func (LoanCampaignType) Type() protoreflect.EnumType {
	return &file_moego_models_capital_v1_loan_enums_proto_enumTypes[4]
}

func (x LoanCampaignType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use LoanCampaignType.Descriptor instead.
func (LoanCampaignType) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_capital_v1_loan_enums_proto_rawDescGZIP(), []int{4}
}

// The type of a loan transaction
type LoanTransactionType int32

const (
	// Unspecified
	LoanTransactionType_LOAN_TRANSACTION_TYPE_UNSPECIFIED LoanTransactionType = 0
	// Payout would be the first transaction of a loan
	LoanTransactionType_PAYOUT LoanTransactionType = 1
	// Payment would cause a repayment
	LoanTransactionType_PAYMENT LoanTransactionType = 2
	// Reversal is a refund of a payment
	LoanTransactionType_REVERSAL LoanTransactionType = 3
)

// Enum value maps for LoanTransactionType.
var (
	LoanTransactionType_name = map[int32]string{
		0: "LOAN_TRANSACTION_TYPE_UNSPECIFIED",
		1: "PAYOUT",
		2: "PAYMENT",
		3: "REVERSAL",
	}
	LoanTransactionType_value = map[string]int32{
		"LOAN_TRANSACTION_TYPE_UNSPECIFIED": 0,
		"PAYOUT":                            1,
		"PAYMENT":                           2,
		"REVERSAL":                          3,
	}
)

func (x LoanTransactionType) Enum() *LoanTransactionType {
	p := new(LoanTransactionType)
	*p = x
	return p
}

func (x LoanTransactionType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (LoanTransactionType) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_capital_v1_loan_enums_proto_enumTypes[5].Descriptor()
}

func (LoanTransactionType) Type() protoreflect.EnumType {
	return &file_moego_models_capital_v1_loan_enums_proto_enumTypes[5]
}

func (x LoanTransactionType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use LoanTransactionType.Descriptor instead.
func (LoanTransactionType) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_capital_v1_loan_enums_proto_rawDescGZIP(), []int{5}
}

// The fee type of a loan transaction.
type LoanTransactionFeeType int32

const (
	// Unspecified
	LoanTransactionFeeType_LOAN_TRANSACTION_FEE_TYPE_UNSPECIFIED LoanTransactionFeeType = 0
	// The original definitions for the fee_amount of a transaction, for a MCA loan.
	LoanTransactionFeeType_MCA_FEE LoanTransactionFeeType = 1
	// Kanmon: This happens when a borrower misses a scheduled payment due to late payments.
	LoanTransactionFeeType_LATE_PAYMENT LoanTransactionFeeType = 2
	// Kanmon: This happens when a borrower misses a scheduled payment due to No Sufficient Funds.
	LoanTransactionFeeType_INSUFFICIENT_FUNDS LoanTransactionFeeType = 3
	// NOTE: Used by MoeGo.
	// Kanmon: This only applies to invoice and purchase order financing products, where the financing fee is not from
	// interest but instead from fee-based pricing.
	LoanTransactionFeeType_TRANSACTION_FEE LoanTransactionFeeType = 4
	// NOTE: Used by MoeGo.
	// Kanmon: This only applies to lines of credit, where the line of credit incurs an administrative fee, like an
	// account fee, to keep the line of credit active.
	LoanTransactionFeeType_MAINTENANCE LoanTransactionFeeType = 5
)

// Enum value maps for LoanTransactionFeeType.
var (
	LoanTransactionFeeType_name = map[int32]string{
		0: "LOAN_TRANSACTION_FEE_TYPE_UNSPECIFIED",
		1: "MCA_FEE",
		2: "LATE_PAYMENT",
		3: "INSUFFICIENT_FUNDS",
		4: "TRANSACTION_FEE",
		5: "MAINTENANCE",
	}
	LoanTransactionFeeType_value = map[string]int32{
		"LOAN_TRANSACTION_FEE_TYPE_UNSPECIFIED": 0,
		"MCA_FEE":                               1,
		"LATE_PAYMENT":                          2,
		"INSUFFICIENT_FUNDS":                    3,
		"TRANSACTION_FEE":                       4,
		"MAINTENANCE":                           5,
	}
)

func (x LoanTransactionFeeType) Enum() *LoanTransactionFeeType {
	p := new(LoanTransactionFeeType)
	*p = x
	return p
}

func (x LoanTransactionFeeType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (LoanTransactionFeeType) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_capital_v1_loan_enums_proto_enumTypes[6].Descriptor()
}

func (LoanTransactionFeeType) Type() protoreflect.EnumType {
	return &file_moego_models_capital_v1_loan_enums_proto_enumTypes[6]
}

func (x LoanTransactionFeeType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use LoanTransactionFeeType.Descriptor instead.
func (LoanTransactionFeeType) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_capital_v1_loan_enums_proto_rawDescGZIP(), []int{6}
}

// The reason of a loan transaction. For now, it basically maps from the Stripe's "reason" field.
type LoanTransactionReason int32

const (
	// Unspecified
	LoanTransactionReason_LOAN_TRANSACTION_REASON_UNSPECIFIED LoanTransactionReason = 0
	// "automatic_withholding"
	LoanTransactionReason_AUTOMATIC_WITHHOLDING LoanTransactionReason = 1
	// "automatic_withholding_refund"
	LoanTransactionReason_AUTOMATIC_WITHHOLDING_REFUND LoanTransactionReason = 2
	// "collection"
	LoanTransactionReason_COLLECTION LoanTransactionReason = 3
	// "collection_failure"
	LoanTransactionReason_COLLECTION_FAILURE LoanTransactionReason = 4
	// "financing_cancellation"
	LoanTransactionReason_FINANCING_CANCELLATION LoanTransactionReason = 5
	// "refill"
	LoanTransactionReason_REASON_REFILL LoanTransactionReason = 6
	// "requested_by_user"
	LoanTransactionReason_REQUESTED_BY_USER LoanTransactionReason = 7
	// "user_initiated"
	LoanTransactionReason_USER_INITIATED LoanTransactionReason = 8
)

// Enum value maps for LoanTransactionReason.
var (
	LoanTransactionReason_name = map[int32]string{
		0: "LOAN_TRANSACTION_REASON_UNSPECIFIED",
		1: "AUTOMATIC_WITHHOLDING",
		2: "AUTOMATIC_WITHHOLDING_REFUND",
		3: "COLLECTION",
		4: "COLLECTION_FAILURE",
		5: "FINANCING_CANCELLATION",
		6: "REASON_REFILL",
		7: "REQUESTED_BY_USER",
		8: "USER_INITIATED",
	}
	LoanTransactionReason_value = map[string]int32{
		"LOAN_TRANSACTION_REASON_UNSPECIFIED": 0,
		"AUTOMATIC_WITHHOLDING":               1,
		"AUTOMATIC_WITHHOLDING_REFUND":        2,
		"COLLECTION":                          3,
		"COLLECTION_FAILURE":                  4,
		"FINANCING_CANCELLATION":              5,
		"REASON_REFILL":                       6,
		"REQUESTED_BY_USER":                   7,
		"USER_INITIATED":                      8,
	}
)

func (x LoanTransactionReason) Enum() *LoanTransactionReason {
	p := new(LoanTransactionReason)
	*p = x
	return p
}

func (x LoanTransactionReason) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (LoanTransactionReason) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_capital_v1_loan_enums_proto_enumTypes[7].Descriptor()
}

func (LoanTransactionReason) Type() protoreflect.EnumType {
	return &file_moego_models_capital_v1_loan_enums_proto_enumTypes[7]
}

func (x LoanTransactionReason) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use LoanTransactionReason.Descriptor instead.
func (LoanTransactionReason) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_capital_v1_loan_enums_proto_rawDescGZIP(), []int{7}
}

// The ineligible reason of applying a loan
type LoanIneligibleReason int32

const (
	// Unspecified
	LoanIneligibleReason_LOAN_INELIGIBLE_REASON_UNSPECIFIED LoanIneligibleReason = 0
	// No any PSP (Stripe/Square) is settle up.
	LoanIneligibleReason_NO_PSP LoanIneligibleReason = 1
	// Square is settle up, but MoeGo Pay is not settle up (e.g. no Stripe account is settle up).
	LoanIneligibleReason_NO_MOEGO_PAY LoanIneligibleReason = 2
	// MoeGo Pay is settle up, but not eligible so no offer is available.
	LoanIneligibleReason_STRIPE_INELIGIBLE LoanIneligibleReason = 3
)

// Enum value maps for LoanIneligibleReason.
var (
	LoanIneligibleReason_name = map[int32]string{
		0: "LOAN_INELIGIBLE_REASON_UNSPECIFIED",
		1: "NO_PSP",
		2: "NO_MOEGO_PAY",
		3: "STRIPE_INELIGIBLE",
	}
	LoanIneligibleReason_value = map[string]int32{
		"LOAN_INELIGIBLE_REASON_UNSPECIFIED": 0,
		"NO_PSP":                             1,
		"NO_MOEGO_PAY":                       2,
		"STRIPE_INELIGIBLE":                  3,
	}
)

func (x LoanIneligibleReason) Enum() *LoanIneligibleReason {
	p := new(LoanIneligibleReason)
	*p = x
	return p
}

func (x LoanIneligibleReason) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (LoanIneligibleReason) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_capital_v1_loan_enums_proto_enumTypes[8].Descriptor()
}

func (LoanIneligibleReason) Type() protoreflect.EnumType {
	return &file_moego_models_capital_v1_loan_enums_proto_enumTypes[8]
}

func (x LoanIneligibleReason) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use LoanIneligibleReason.Descriptor instead.
func (LoanIneligibleReason) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_capital_v1_loan_enums_proto_rawDescGZIP(), []int{8}
}

// The status of a loan onboarding
type LoanOnboardingStatus int32

const (
	// Unspecified
	LoanOnboardingStatus_LOAN_ONBOARDING_STATUS_UNSPECIFIED LoanOnboardingStatus = 0
	// Onboarded
	LoanOnboardingStatus_ONBOARDED LoanOnboardingStatus = 1
	// Not onboarded
	LoanOnboardingStatus_NOT_ONBOARDED LoanOnboardingStatus = 2
	// Need to submit additional information
	LoanOnboardingStatus_NEED_ADDITIONAL_INFORMATION LoanOnboardingStatus = 3
)

// Enum value maps for LoanOnboardingStatus.
var (
	LoanOnboardingStatus_name = map[int32]string{
		0: "LOAN_ONBOARDING_STATUS_UNSPECIFIED",
		1: "ONBOARDED",
		2: "NOT_ONBOARDED",
		3: "NEED_ADDITIONAL_INFORMATION",
	}
	LoanOnboardingStatus_value = map[string]int32{
		"LOAN_ONBOARDING_STATUS_UNSPECIFIED": 0,
		"ONBOARDED":                          1,
		"NOT_ONBOARDED":                      2,
		"NEED_ADDITIONAL_INFORMATION":        3,
	}
)

func (x LoanOnboardingStatus) Enum() *LoanOnboardingStatus {
	p := new(LoanOnboardingStatus)
	*p = x
	return p
}

func (x LoanOnboardingStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (LoanOnboardingStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_capital_v1_loan_enums_proto_enumTypes[9].Descriptor()
}

func (LoanOnboardingStatus) Type() protoreflect.EnumType {
	return &file_moego_models_capital_v1_loan_enums_proto_enumTypes[9]
}

func (x LoanOnboardingStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use LoanOnboardingStatus.Descriptor instead.
func (LoanOnboardingStatus) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_capital_v1_loan_enums_proto_rawDescGZIP(), []int{9}
}

var File_moego_models_capital_v1_loan_enums_proto protoreflect.FileDescriptor

var file_moego_models_capital_v1_loan_enums_proto_rawDesc = []byte{
	0x0a, 0x28, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x63,
	0x61, 0x70, 0x69, 0x74, 0x61, 0x6c, 0x2f, 0x76, 0x31, 0x2f, 0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x65,
	0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x17, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x63, 0x61, 0x70, 0x69, 0x74, 0x61, 0x6c,
	0x2e, 0x76, 0x31, 0x2a, 0x43, 0x0a, 0x0b, 0x4c, 0x6f, 0x61, 0x6e, 0x43, 0x68, 0x61, 0x6e, 0x6e,
	0x65, 0x6c, 0x12, 0x1c, 0x0a, 0x18, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x43, 0x48, 0x41, 0x4e, 0x4e,
	0x45, 0x4c, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00,
	0x12, 0x0a, 0x0a, 0x06, 0x53, 0x54, 0x52, 0x49, 0x50, 0x45, 0x10, 0x01, 0x12, 0x0a, 0x0a, 0x06,
	0x4b, 0x41, 0x4e, 0x4d, 0x4f, 0x4e, 0x10, 0x02, 0x2a, 0x48, 0x0a, 0x0d, 0x4c, 0x6f, 0x61, 0x6e,
	0x4f, 0x66, 0x66, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1f, 0x0a, 0x1b, 0x4c, 0x4f, 0x41,
	0x4e, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53,
	0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x07, 0x0a, 0x03, 0x4d, 0x43,
	0x41, 0x10, 0x01, 0x12, 0x0d, 0x0a, 0x09, 0x54, 0x45, 0x52, 0x4d, 0x5f, 0x4c, 0x4f, 0x41, 0x4e,
	0x10, 0x02, 0x2a, 0x4e, 0x0a, 0x0f, 0x4c, 0x6f, 0x61, 0x6e, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63,
	0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x21, 0x0a, 0x1d, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x50, 0x52,
	0x4f, 0x44, 0x55, 0x43, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45,
	0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x53, 0x54, 0x41, 0x4e,
	0x44, 0x41, 0x52, 0x44, 0x10, 0x01, 0x12, 0x0a, 0x0a, 0x06, 0x52, 0x45, 0x46, 0x49, 0x4c, 0x4c,
	0x10, 0x02, 0x2a, 0xb8, 0x01, 0x0a, 0x0f, 0x4c, 0x6f, 0x61, 0x6e, 0x4f, 0x66, 0x66, 0x65, 0x72,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x21, 0x0a, 0x1d, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x4f,
	0x46, 0x46, 0x45, 0x52, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50,
	0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0b, 0x0a, 0x07, 0x43, 0x52, 0x45,
	0x41, 0x54, 0x45, 0x44, 0x10, 0x01, 0x12, 0x0c, 0x0a, 0x08, 0x41, 0x43, 0x43, 0x45, 0x50, 0x54,
	0x45, 0x44, 0x10, 0x02, 0x12, 0x0c, 0x0a, 0x08, 0x50, 0x41, 0x49, 0x44, 0x5f, 0x4f, 0x55, 0x54,
	0x10, 0x03, 0x12, 0x10, 0x0a, 0x0c, 0x46, 0x55, 0x4c, 0x4c, 0x59, 0x5f, 0x52, 0x45, 0x50, 0x41,
	0x49, 0x44, 0x10, 0x04, 0x12, 0x0b, 0x0a, 0x07, 0x45, 0x58, 0x50, 0x49, 0x52, 0x45, 0x44, 0x10,
	0x05, 0x12, 0x0c, 0x0a, 0x08, 0x52, 0x45, 0x4a, 0x45, 0x43, 0x54, 0x45, 0x44, 0x10, 0x06, 0x12,
	0x0c, 0x0a, 0x08, 0x43, 0x41, 0x4e, 0x43, 0x45, 0x4c, 0x45, 0x44, 0x10, 0x07, 0x12, 0x0c, 0x0a,
	0x08, 0x52, 0x45, 0x50, 0x4c, 0x41, 0x43, 0x45, 0x44, 0x10, 0x08, 0x12, 0x10, 0x0a, 0x0c, 0x50,
	0x52, 0x45, 0x51, 0x55, 0x41, 0x4c, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x09, 0x2a, 0x7e, 0x0a,
	0x10, 0x4c, 0x6f, 0x61, 0x6e, 0x43, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x22, 0x0a, 0x1e, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x43, 0x41, 0x4d, 0x50, 0x41, 0x49,
	0x47, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46,
	0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x17, 0x0a, 0x13, 0x4e, 0x45, 0x57, 0x4c, 0x59, 0x5f, 0x45,
	0x4c, 0x49, 0x47, 0x49, 0x42, 0x4c, 0x45, 0x5f, 0x55, 0x53, 0x45, 0x52, 0x10, 0x01, 0x12, 0x1c,
	0x0a, 0x18, 0x50, 0x52, 0x45, 0x56, 0x49, 0x4f, 0x55, 0x53, 0x4c, 0x59, 0x5f, 0x45, 0x4c, 0x49,
	0x47, 0x49, 0x42, 0x4c, 0x45, 0x5f, 0x55, 0x53, 0x45, 0x52, 0x10, 0x02, 0x12, 0x0f, 0x0a, 0x0b,
	0x52, 0x45, 0x50, 0x45, 0x41, 0x54, 0x5f, 0x55, 0x53, 0x45, 0x52, 0x10, 0x03, 0x2a, 0x63, 0x0a,
	0x13, 0x4c, 0x6f, 0x61, 0x6e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x25, 0x0a, 0x21, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x54, 0x52, 0x41,
	0x4e, 0x53, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e,
	0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0a, 0x0a, 0x06, 0x50,
	0x41, 0x59, 0x4f, 0x55, 0x54, 0x10, 0x01, 0x12, 0x0b, 0x0a, 0x07, 0x50, 0x41, 0x59, 0x4d, 0x45,
	0x4e, 0x54, 0x10, 0x02, 0x12, 0x0c, 0x0a, 0x08, 0x52, 0x45, 0x56, 0x45, 0x52, 0x53, 0x41, 0x4c,
	0x10, 0x03, 0x2a, 0xa0, 0x01, 0x0a, 0x16, 0x4c, 0x6f, 0x61, 0x6e, 0x54, 0x72, 0x61, 0x6e, 0x73,
	0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x65, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x29, 0x0a,
	0x25, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x41, 0x43, 0x54, 0x49, 0x4f,
	0x4e, 0x5f, 0x46, 0x45, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45,
	0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0b, 0x0a, 0x07, 0x4d, 0x43, 0x41, 0x5f,
	0x46, 0x45, 0x45, 0x10, 0x01, 0x12, 0x10, 0x0a, 0x0c, 0x4c, 0x41, 0x54, 0x45, 0x5f, 0x50, 0x41,
	0x59, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x02, 0x12, 0x16, 0x0a, 0x12, 0x49, 0x4e, 0x53, 0x55, 0x46,
	0x46, 0x49, 0x43, 0x49, 0x45, 0x4e, 0x54, 0x5f, 0x46, 0x55, 0x4e, 0x44, 0x53, 0x10, 0x03, 0x12,
	0x13, 0x0a, 0x0f, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46,
	0x45, 0x45, 0x10, 0x04, 0x12, 0x0f, 0x0a, 0x0b, 0x4d, 0x41, 0x49, 0x4e, 0x54, 0x45, 0x4e, 0x41,
	0x4e, 0x43, 0x45, 0x10, 0x05, 0x2a, 0xff, 0x01, 0x0a, 0x15, 0x4c, 0x6f, 0x61, 0x6e, 0x54, 0x72,
	0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12,
	0x27, 0x0a, 0x23, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x41, 0x43, 0x54,
	0x49, 0x4f, 0x4e, 0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45,
	0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x19, 0x0a, 0x15, 0x41, 0x55, 0x54, 0x4f,
	0x4d, 0x41, 0x54, 0x49, 0x43, 0x5f, 0x57, 0x49, 0x54, 0x48, 0x48, 0x4f, 0x4c, 0x44, 0x49, 0x4e,
	0x47, 0x10, 0x01, 0x12, 0x20, 0x0a, 0x1c, 0x41, 0x55, 0x54, 0x4f, 0x4d, 0x41, 0x54, 0x49, 0x43,
	0x5f, 0x57, 0x49, 0x54, 0x48, 0x48, 0x4f, 0x4c, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x52, 0x45, 0x46,
	0x55, 0x4e, 0x44, 0x10, 0x02, 0x12, 0x0e, 0x0a, 0x0a, 0x43, 0x4f, 0x4c, 0x4c, 0x45, 0x43, 0x54,
	0x49, 0x4f, 0x4e, 0x10, 0x03, 0x12, 0x16, 0x0a, 0x12, 0x43, 0x4f, 0x4c, 0x4c, 0x45, 0x43, 0x54,
	0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x55, 0x52, 0x45, 0x10, 0x04, 0x12, 0x1a, 0x0a,
	0x16, 0x46, 0x49, 0x4e, 0x41, 0x4e, 0x43, 0x49, 0x4e, 0x47, 0x5f, 0x43, 0x41, 0x4e, 0x43, 0x45,
	0x4c, 0x4c, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x05, 0x12, 0x11, 0x0a, 0x0d, 0x52, 0x45, 0x41,
	0x53, 0x4f, 0x4e, 0x5f, 0x52, 0x45, 0x46, 0x49, 0x4c, 0x4c, 0x10, 0x06, 0x12, 0x15, 0x0a, 0x11,
	0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x45, 0x44, 0x5f, 0x42, 0x59, 0x5f, 0x55, 0x53, 0x45,
	0x52, 0x10, 0x07, 0x12, 0x12, 0x0a, 0x0e, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x49, 0x4e, 0x49, 0x54,
	0x49, 0x41, 0x54, 0x45, 0x44, 0x10, 0x08, 0x2a, 0x73, 0x0a, 0x14, 0x4c, 0x6f, 0x61, 0x6e, 0x49,
	0x6e, 0x65, 0x6c, 0x69, 0x67, 0x69, 0x62, 0x6c, 0x65, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12,
	0x26, 0x0a, 0x22, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x49, 0x4e, 0x45, 0x4c, 0x49, 0x47, 0x49, 0x42,
	0x4c, 0x45, 0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43,
	0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0a, 0x0a, 0x06, 0x4e, 0x4f, 0x5f, 0x50, 0x53,
	0x50, 0x10, 0x01, 0x12, 0x10, 0x0a, 0x0c, 0x4e, 0x4f, 0x5f, 0x4d, 0x4f, 0x45, 0x47, 0x4f, 0x5f,
	0x50, 0x41, 0x59, 0x10, 0x02, 0x12, 0x15, 0x0a, 0x11, 0x53, 0x54, 0x52, 0x49, 0x50, 0x45, 0x5f,
	0x49, 0x4e, 0x45, 0x4c, 0x49, 0x47, 0x49, 0x42, 0x4c, 0x45, 0x10, 0x03, 0x2a, 0x81, 0x01, 0x0a,
	0x14, 0x4c, 0x6f, 0x61, 0x6e, 0x4f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x26, 0x0a, 0x22, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x4f, 0x4e,
	0x42, 0x4f, 0x41, 0x52, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f,
	0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0d, 0x0a,
	0x09, 0x4f, 0x4e, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x45, 0x44, 0x10, 0x01, 0x12, 0x11, 0x0a, 0x0d,
	0x4e, 0x4f, 0x54, 0x5f, 0x4f, 0x4e, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x45, 0x44, 0x10, 0x02, 0x12,
	0x1f, 0x0a, 0x1b, 0x4e, 0x45, 0x45, 0x44, 0x5f, 0x41, 0x44, 0x44, 0x49, 0x54, 0x49, 0x4f, 0x4e,
	0x41, 0x4c, 0x5f, 0x49, 0x4e, 0x46, 0x4f, 0x52, 0x4d, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x03,
	0x42, 0x7b, 0x0a, 0x1f, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64,
	0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x63, 0x61, 0x70, 0x69, 0x74, 0x61, 0x6c,
	0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x56, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f,
	0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x63, 0x61, 0x70, 0x69, 0x74, 0x61, 0x6c,
	0x2f, 0x76, 0x31, 0x3b, 0x63, 0x61, 0x70, 0x69, 0x74, 0x61, 0x6c, 0x70, 0x62, 0x62, 0x06, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_capital_v1_loan_enums_proto_rawDescOnce sync.Once
	file_moego_models_capital_v1_loan_enums_proto_rawDescData = file_moego_models_capital_v1_loan_enums_proto_rawDesc
)

func file_moego_models_capital_v1_loan_enums_proto_rawDescGZIP() []byte {
	file_moego_models_capital_v1_loan_enums_proto_rawDescOnce.Do(func() {
		file_moego_models_capital_v1_loan_enums_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_capital_v1_loan_enums_proto_rawDescData)
	})
	return file_moego_models_capital_v1_loan_enums_proto_rawDescData
}

var file_moego_models_capital_v1_loan_enums_proto_enumTypes = make([]protoimpl.EnumInfo, 10)
var file_moego_models_capital_v1_loan_enums_proto_goTypes = []interface{}{
	(LoanChannel)(0),            // 0: moego.models.capital.v1.LoanChannel
	(LoanOfferType)(0),          // 1: moego.models.capital.v1.LoanOfferType
	(LoanProductType)(0),        // 2: moego.models.capital.v1.LoanProductType
	(LoanOfferStatus)(0),        // 3: moego.models.capital.v1.LoanOfferStatus
	(LoanCampaignType)(0),       // 4: moego.models.capital.v1.LoanCampaignType
	(LoanTransactionType)(0),    // 5: moego.models.capital.v1.LoanTransactionType
	(LoanTransactionFeeType)(0), // 6: moego.models.capital.v1.LoanTransactionFeeType
	(LoanTransactionReason)(0),  // 7: moego.models.capital.v1.LoanTransactionReason
	(LoanIneligibleReason)(0),   // 8: moego.models.capital.v1.LoanIneligibleReason
	(LoanOnboardingStatus)(0),   // 9: moego.models.capital.v1.LoanOnboardingStatus
}
var file_moego_models_capital_v1_loan_enums_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_moego_models_capital_v1_loan_enums_proto_init() }
func file_moego_models_capital_v1_loan_enums_proto_init() {
	if File_moego_models_capital_v1_loan_enums_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_capital_v1_loan_enums_proto_rawDesc,
			NumEnums:      10,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_capital_v1_loan_enums_proto_goTypes,
		DependencyIndexes: file_moego_models_capital_v1_loan_enums_proto_depIdxs,
		EnumInfos:         file_moego_models_capital_v1_loan_enums_proto_enumTypes,
	}.Build()
	File_moego_models_capital_v1_loan_enums_proto = out.File
	file_moego_models_capital_v1_loan_enums_proto_rawDesc = nil
	file_moego_models_capital_v1_loan_enums_proto_goTypes = nil
	file_moego_models_capital_v1_loan_enums_proto_depIdxs = nil
}
