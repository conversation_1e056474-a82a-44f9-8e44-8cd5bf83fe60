// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/activity_log/v1/activity_log_models.proto

package activitylogpb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	structpb "google.golang.org/protobuf/types/known/structpb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Resource type
type Resource_Type int32

const (
	// Unspecified
	Resource_TYPE_UNSPECIFIED Resource_Type = 0
	// Appointment
	Resource_APPOINTMENT Resource_Type = 1
	// Repeat rule
	Resource_REPEAT_RULE Resource_Type = 2
	// Block
	Resource_BLOCK Resource_Type = 3
	// Grooming service
	Resource_GROOMING_SERVICE Resource_Type = 4
	// Grooming note
	Resource_GROOMING_NOTE Resource_Type = 5
	// Online booking
	Resource_LANDING_PAGE_SETTING Resource_Type = 6
	// Online booking setting, aka. Book Online
	Resource_ONLINE_BOOKING_SETTING Resource_Type = 7
	// Online booking team schedule
	Resource_ONLINE_BOOKING_TEAM_SCHEDULE Resource_Type = 8
	// Online booking service
	Resource_ONLINE_BOOKING_SERVICE Resource_Type = 9
	// Online booking pet limit
	Resource_ONLINE_BOOKING_PET_LIMIT Resource_Type = 10
	// Online booking customized payment
	Resource_ONLINE_BOOKING_CUSTOMIZED_PAYMENT Resource_Type = 11
	// Online booking notification
	Resource_ONLINE_BOOKING_NOTIFICATION Resource_Type = 12
	// Online booking question
	Resource_ONLINE_BOOKING_QUESTION Resource_Type = 13
	// Abandoned booking
	Resource_ABANDONED_BOOKING Resource_Type = 14
	// Customer profile request
	Resource_CUSTOMER_PROFILE_REQUEST Resource_Type = 15
	// Customer
	Resource_CUSTOMER Resource_Type = 16
	// Customer tag
	Resource_CUSTOMER_TAG Resource_Type = 17
	// Customer address
	Resource_CUSTOMER_ADDRESS Resource_Type = 18
	// Customer contact
	Resource_CUSTOMER_CONTACT Resource_Type = 19
	// Customer note
	Resource_CUSTOMER_NOTE Resource_Type = 20
	// Customer preferred tip
	Resource_CUSTOMER_PREFERRED_TIP Resource_Type = 21
	// Pet
	Resource_PET Resource_Type = 22
	// Pet note
	Resource_PET_NOTE Resource_Type = 23
	// Pet behavior
	Resource_PET_BEHAVIOR Resource_Type = 24
	// Pet breed
	Resource_PET_BREED Resource_Type = 25
	// Pet code
	Resource_PET_CODE Resource_Type = 26
	// Pet fixed
	Resource_PET_FIXED Resource_Type = 27
	// Pet photo
	Resource_PET_PHOTO Resource_Type = 28
	// Pet type
	Resource_PET_TYPE Resource_Type = 29
	// Pet vaccine
	Resource_PET_VACCINE Resource_Type = 30
	// Pet vaccine binding
	Resource_PET_VACCINE_BINDING Resource_Type = 31
	// Pet hair length
	Resource_PET_HAIR_LENGTH Resource_Type = 32
	// Customized service
	Resource_PET_CUSTOMIZED_SERVICE Resource_Type = 33
	// Agreement signature
	Resource_AGREEMENT_SIGNATURE Resource_Type = 34
	// pet incident report
	Resource_PET_INCIDENT_REPORT Resource_Type = 65
	// Account
	Resource_ACCOUNT Resource_Type = 35
	// Message
	Resource_MESSAGE Resource_Type = 36
	// Chat
	Resource_CHAT Resource_Type = 37
	// Auto message setting
	Resource_AUTO_MESSAGE_SETTING Resource_Type = 38
	// Reminder setting
	Resource_REMINDER_SETTING Resource_Type = 39
	// Auto reply setting
	Resource_AUTO_REPLY_SETTING Resource_Type = 40
	// Schedule message
	Resource_SCHEDULE_MESSAGE Resource_Type = 41
	// Message template
	Resource_MESSAGE_TEMPLATE Resource_Type = 42
	// Review booster
	Resource_REVIEW_BOOSTER Resource_Type = 43
	// Order
	Resource_ORDER Resource_Type = 44
	// Package
	Resource_PACKAGE Resource_Type = 45
	// Customer package
	Resource_CUSTOMER_PACKAGE Resource_Type = 46
	// Payment
	Resource_PAYMENT Resource_Type = 47
	// Refund
	Resource_REFUND Resource_Type = 48
	// Stripe account
	Resource_PAYMENT_STRIPE_ACCOUNT Resource_Type = 49
	// Payout
	Resource_PAYOUT Resource_Type = 50
	// Terminal
	Resource_TERMINAL Resource_Type = 51
	// Terminal location
	Resource_TERMINAL_LOCATION Resource_Type = 52
	// Stripe card
	Resource_STRIPE_CARD Resource_Type = 53
	// Stripe customer
	Resource_STRIPE_CUSTOMER Resource_Type = 54
	// Square card
	Resource_SQUARE_CARD Resource_Type = 55
	// Square customer
	Resource_SQUARE_CUSTOMER Resource_Type = 56
	// Subscription
	Resource_SUBSCRIPTION Resource_Type = 57
	// Email package
	Resource_MESSAGE_EMAIL_PACKAGE Resource_Type = 58
	// Hardware
	Resource_HARDWARE Resource_Type = 59
	// Customized payment setting
	Resource_CUSTOMIZED_PAYMENT_SETTING Resource_Type = 60
	// Company plan feature
	Resource_COMPANY_PLAN_FEATURE Resource_Type = 61
	// Platform care
	Resource_PLATFORM_CARE Resource_Type = 62
	// Staff
	Resource_STAFF Resource_Type = 63
	// Business setting
	Resource_BUSINESS_SETTING Resource_Type = 64
	// Service category
	Resource_SERVICE_CATEGORY Resource_Type = 66
)

// Enum value maps for Resource_Type.
var (
	Resource_Type_name = map[int32]string{
		0:  "TYPE_UNSPECIFIED",
		1:  "APPOINTMENT",
		2:  "REPEAT_RULE",
		3:  "BLOCK",
		4:  "GROOMING_SERVICE",
		5:  "GROOMING_NOTE",
		6:  "LANDING_PAGE_SETTING",
		7:  "ONLINE_BOOKING_SETTING",
		8:  "ONLINE_BOOKING_TEAM_SCHEDULE",
		9:  "ONLINE_BOOKING_SERVICE",
		10: "ONLINE_BOOKING_PET_LIMIT",
		11: "ONLINE_BOOKING_CUSTOMIZED_PAYMENT",
		12: "ONLINE_BOOKING_NOTIFICATION",
		13: "ONLINE_BOOKING_QUESTION",
		14: "ABANDONED_BOOKING",
		15: "CUSTOMER_PROFILE_REQUEST",
		16: "CUSTOMER",
		17: "CUSTOMER_TAG",
		18: "CUSTOMER_ADDRESS",
		19: "CUSTOMER_CONTACT",
		20: "CUSTOMER_NOTE",
		21: "CUSTOMER_PREFERRED_TIP",
		22: "PET",
		23: "PET_NOTE",
		24: "PET_BEHAVIOR",
		25: "PET_BREED",
		26: "PET_CODE",
		27: "PET_FIXED",
		28: "PET_PHOTO",
		29: "PET_TYPE",
		30: "PET_VACCINE",
		31: "PET_VACCINE_BINDING",
		32: "PET_HAIR_LENGTH",
		33: "PET_CUSTOMIZED_SERVICE",
		34: "AGREEMENT_SIGNATURE",
		65: "PET_INCIDENT_REPORT",
		35: "ACCOUNT",
		36: "MESSAGE",
		37: "CHAT",
		38: "AUTO_MESSAGE_SETTING",
		39: "REMINDER_SETTING",
		40: "AUTO_REPLY_SETTING",
		41: "SCHEDULE_MESSAGE",
		42: "MESSAGE_TEMPLATE",
		43: "REVIEW_BOOSTER",
		44: "ORDER",
		45: "PACKAGE",
		46: "CUSTOMER_PACKAGE",
		47: "PAYMENT",
		48: "REFUND",
		49: "PAYMENT_STRIPE_ACCOUNT",
		50: "PAYOUT",
		51: "TERMINAL",
		52: "TERMINAL_LOCATION",
		53: "STRIPE_CARD",
		54: "STRIPE_CUSTOMER",
		55: "SQUARE_CARD",
		56: "SQUARE_CUSTOMER",
		57: "SUBSCRIPTION",
		58: "MESSAGE_EMAIL_PACKAGE",
		59: "HARDWARE",
		60: "CUSTOMIZED_PAYMENT_SETTING",
		61: "COMPANY_PLAN_FEATURE",
		62: "PLATFORM_CARE",
		63: "STAFF",
		64: "BUSINESS_SETTING",
		66: "SERVICE_CATEGORY",
	}
	Resource_Type_value = map[string]int32{
		"TYPE_UNSPECIFIED":                  0,
		"APPOINTMENT":                       1,
		"REPEAT_RULE":                       2,
		"BLOCK":                             3,
		"GROOMING_SERVICE":                  4,
		"GROOMING_NOTE":                     5,
		"LANDING_PAGE_SETTING":              6,
		"ONLINE_BOOKING_SETTING":            7,
		"ONLINE_BOOKING_TEAM_SCHEDULE":      8,
		"ONLINE_BOOKING_SERVICE":            9,
		"ONLINE_BOOKING_PET_LIMIT":          10,
		"ONLINE_BOOKING_CUSTOMIZED_PAYMENT": 11,
		"ONLINE_BOOKING_NOTIFICATION":       12,
		"ONLINE_BOOKING_QUESTION":           13,
		"ABANDONED_BOOKING":                 14,
		"CUSTOMER_PROFILE_REQUEST":          15,
		"CUSTOMER":                          16,
		"CUSTOMER_TAG":                      17,
		"CUSTOMER_ADDRESS":                  18,
		"CUSTOMER_CONTACT":                  19,
		"CUSTOMER_NOTE":                     20,
		"CUSTOMER_PREFERRED_TIP":            21,
		"PET":                               22,
		"PET_NOTE":                          23,
		"PET_BEHAVIOR":                      24,
		"PET_BREED":                         25,
		"PET_CODE":                          26,
		"PET_FIXED":                         27,
		"PET_PHOTO":                         28,
		"PET_TYPE":                          29,
		"PET_VACCINE":                       30,
		"PET_VACCINE_BINDING":               31,
		"PET_HAIR_LENGTH":                   32,
		"PET_CUSTOMIZED_SERVICE":            33,
		"AGREEMENT_SIGNATURE":               34,
		"PET_INCIDENT_REPORT":               65,
		"ACCOUNT":                           35,
		"MESSAGE":                           36,
		"CHAT":                              37,
		"AUTO_MESSAGE_SETTING":              38,
		"REMINDER_SETTING":                  39,
		"AUTO_REPLY_SETTING":                40,
		"SCHEDULE_MESSAGE":                  41,
		"MESSAGE_TEMPLATE":                  42,
		"REVIEW_BOOSTER":                    43,
		"ORDER":                             44,
		"PACKAGE":                           45,
		"CUSTOMER_PACKAGE":                  46,
		"PAYMENT":                           47,
		"REFUND":                            48,
		"PAYMENT_STRIPE_ACCOUNT":            49,
		"PAYOUT":                            50,
		"TERMINAL":                          51,
		"TERMINAL_LOCATION":                 52,
		"STRIPE_CARD":                       53,
		"STRIPE_CUSTOMER":                   54,
		"SQUARE_CARD":                       55,
		"SQUARE_CUSTOMER":                   56,
		"SUBSCRIPTION":                      57,
		"MESSAGE_EMAIL_PACKAGE":             58,
		"HARDWARE":                          59,
		"CUSTOMIZED_PAYMENT_SETTING":        60,
		"COMPANY_PLAN_FEATURE":              61,
		"PLATFORM_CARE":                     62,
		"STAFF":                             63,
		"BUSINESS_SETTING":                  64,
		"SERVICE_CATEGORY":                  66,
	}
)

func (x Resource_Type) Enum() *Resource_Type {
	p := new(Resource_Type)
	*p = x
	return p
}

func (x Resource_Type) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Resource_Type) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_activity_log_v1_activity_log_models_proto_enumTypes[0].Descriptor()
}

func (Resource_Type) Type() protoreflect.EnumType {
	return &file_moego_models_activity_log_v1_activity_log_models_proto_enumTypes[0]
}

func (x Resource_Type) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Resource_Type.Descriptor instead.
func (Resource_Type) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_activity_log_v1_activity_log_models_proto_rawDescGZIP(), []int{4, 0}
}

// ActivityLog model
type ActivityLogModel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// business id
	BusinessId int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// operator
	Operator *Operator `protobuf:"bytes,3,opt,name=operator,proto3" json:"operator,omitempty"`
	// action
	Action string `protobuf:"bytes,4,opt,name=action,proto3" json:"action,omitempty"`
	// resource
	Resource *Resource `protobuf:"bytes,5,opt,name=resource,proto3" json:"resource,omitempty"`
	// owner, nullable
	Owner *Owner `protobuf:"bytes,8,opt,name=owner,proto3,oneof" json:"owner,omitempty"`
	// timestamp
	Time *timestamppb.Timestamp `protobuf:"bytes,9,opt,name=time,proto3" json:"time,omitempty"`
	// updated result, usually a JSON string
	Details *structpb.Value `protobuf:"bytes,10,opt,name=details,proto3,oneof" json:"details,omitempty"`
	// root cause activity log id, nullable
	RootActivityLogId *string `protobuf:"bytes,12,opt,name=root_activity_log_id,json=rootActivityLogId,proto3,oneof" json:"root_activity_log_id,omitempty"`
	// request id
	RequestId *string `protobuf:"bytes,13,opt,name=request_id,json=requestId,proto3,oneof" json:"request_id,omitempty"`
}

func (x *ActivityLogModel) Reset() {
	*x = ActivityLogModel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_activity_log_v1_activity_log_models_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ActivityLogModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ActivityLogModel) ProtoMessage() {}

func (x *ActivityLogModel) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_activity_log_v1_activity_log_models_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ActivityLogModel.ProtoReflect.Descriptor instead.
func (*ActivityLogModel) Descriptor() ([]byte, []int) {
	return file_moego_models_activity_log_v1_activity_log_models_proto_rawDescGZIP(), []int{0}
}

func (x *ActivityLogModel) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *ActivityLogModel) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *ActivityLogModel) GetOperator() *Operator {
	if x != nil {
		return x.Operator
	}
	return nil
}

func (x *ActivityLogModel) GetAction() string {
	if x != nil {
		return x.Action
	}
	return ""
}

func (x *ActivityLogModel) GetResource() *Resource {
	if x != nil {
		return x.Resource
	}
	return nil
}

func (x *ActivityLogModel) GetOwner() *Owner {
	if x != nil {
		return x.Owner
	}
	return nil
}

func (x *ActivityLogModel) GetTime() *timestamppb.Timestamp {
	if x != nil {
		return x.Time
	}
	return nil
}

func (x *ActivityLogModel) GetDetails() *structpb.Value {
	if x != nil {
		return x.Details
	}
	return nil
}

func (x *ActivityLogModel) GetRootActivityLogId() string {
	if x != nil && x.RootActivityLogId != nil {
		return *x.RootActivityLogId
	}
	return ""
}

func (x *ActivityLogModel) GetRequestId() string {
	if x != nil && x.RequestId != nil {
		return *x.RequestId
	}
	return ""
}

// ActivityLog model simple view
type ActivityLogModelSimpleView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// business id
	BusinessId int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// operator
	Operator *Operator `protobuf:"bytes,3,opt,name=operator,proto3" json:"operator,omitempty"`
	// action
	Action string `protobuf:"bytes,4,opt,name=action,proto3" json:"action,omitempty"`
	// resource
	Resource *Resource `protobuf:"bytes,5,opt,name=resource,proto3" json:"resource,omitempty"`
	// owner, nullable
	Owner *Owner `protobuf:"bytes,7,opt,name=owner,proto3,oneof" json:"owner,omitempty"`
	// timestamp
	Time *timestamppb.Timestamp `protobuf:"bytes,8,opt,name=time,proto3" json:"time,omitempty"`
	// root cause activity log id, nullable
	RootActivityLogId *string `protobuf:"bytes,9,opt,name=root_activity_log_id,json=rootActivityLogId,proto3,oneof" json:"root_activity_log_id,omitempty"`
}

func (x *ActivityLogModelSimpleView) Reset() {
	*x = ActivityLogModelSimpleView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_activity_log_v1_activity_log_models_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ActivityLogModelSimpleView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ActivityLogModelSimpleView) ProtoMessage() {}

func (x *ActivityLogModelSimpleView) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_activity_log_v1_activity_log_models_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ActivityLogModelSimpleView.ProtoReflect.Descriptor instead.
func (*ActivityLogModelSimpleView) Descriptor() ([]byte, []int) {
	return file_moego_models_activity_log_v1_activity_log_models_proto_rawDescGZIP(), []int{1}
}

func (x *ActivityLogModelSimpleView) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *ActivityLogModelSimpleView) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *ActivityLogModelSimpleView) GetOperator() *Operator {
	if x != nil {
		return x.Operator
	}
	return nil
}

func (x *ActivityLogModelSimpleView) GetAction() string {
	if x != nil {
		return x.Action
	}
	return ""
}

func (x *ActivityLogModelSimpleView) GetResource() *Resource {
	if x != nil {
		return x.Resource
	}
	return nil
}

func (x *ActivityLogModelSimpleView) GetOwner() *Owner {
	if x != nil {
		return x.Owner
	}
	return nil
}

func (x *ActivityLogModelSimpleView) GetTime() *timestamppb.Timestamp {
	if x != nil {
		return x.Time
	}
	return nil
}

func (x *ActivityLogModelSimpleView) GetRootActivityLogId() string {
	if x != nil && x.RootActivityLogId != nil {
		return *x.RootActivityLogId
	}
	return ""
}

// Operator
type Operator struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// operator id
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// operator name
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *Operator) Reset() {
	*x = Operator{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_activity_log_v1_activity_log_models_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Operator) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Operator) ProtoMessage() {}

func (x *Operator) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_activity_log_v1_activity_log_models_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Operator.ProtoReflect.Descriptor instead.
func (*Operator) Descriptor() ([]byte, []int) {
	return file_moego_models_activity_log_v1_activity_log_models_proto_rawDescGZIP(), []int{2}
}

func (x *Operator) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Operator) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

// Owner
type Owner struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// owner id
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// owner name
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *Owner) Reset() {
	*x = Owner{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_activity_log_v1_activity_log_models_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Owner) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Owner) ProtoMessage() {}

func (x *Owner) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_activity_log_v1_activity_log_models_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Owner.ProtoReflect.Descriptor instead.
func (*Owner) Descriptor() ([]byte, []int) {
	return file_moego_models_activity_log_v1_activity_log_models_proto_rawDescGZIP(), []int{3}
}

func (x *Owner) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Owner) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

// Resource
type Resource struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// resource id
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// resource name
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// resource type
	Type string `protobuf:"bytes,3,opt,name=type,proto3" json:"type,omitempty"`
}

func (x *Resource) Reset() {
	*x = Resource{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_activity_log_v1_activity_log_models_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Resource) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Resource) ProtoMessage() {}

func (x *Resource) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_activity_log_v1_activity_log_models_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Resource.ProtoReflect.Descriptor instead.
func (*Resource) Descriptor() ([]byte, []int) {
	return file_moego_models_activity_log_v1_activity_log_models_proto_rawDescGZIP(), []int{4}
}

func (x *Resource) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Resource) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Resource) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

var File_moego_models_activity_log_v1_activity_log_models_proto protoreflect.FileDescriptor

var file_moego_models_activity_log_v1_activity_log_models_proto_rawDesc = []byte{
	0x0a, 0x36, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x61,
	0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x5f, 0x6c, 0x6f, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x61,
	0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x5f, 0x6c, 0x6f, 0x67, 0x5f, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1c, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x5f,
	0x6c, 0x6f, 0x67, 0x2e, 0x76, 0x31, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xa2, 0x04, 0x0a, 0x10, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69,
	0x74, 0x79, 0x4c, 0x6f, 0x67, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x62, 0x75,
	0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x42, 0x0a, 0x08, 0x6f,
	0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x63, 0x74,
	0x69, 0x76, 0x69, 0x74, 0x79, 0x5f, 0x6c, 0x6f, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x70, 0x65,
	0x72, 0x61, 0x74, 0x6f, 0x72, 0x52, 0x08, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x12,
	0x16, 0x0a, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x42, 0x0a, 0x08, 0x72, 0x65, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74,
	0x79, 0x5f, 0x6c, 0x6f, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x52, 0x08, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x3e, 0x0a, 0x05, 0x6f,
	0x77, 0x6e, 0x65, 0x72, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69,
	0x74, 0x79, 0x5f, 0x6c, 0x6f, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x77, 0x6e, 0x65, 0x72, 0x48,
	0x00, 0x52, 0x05, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x88, 0x01, 0x01, 0x12, 0x2e, 0x0a, 0x04, 0x74,
	0x69, 0x6d, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x04, 0x74, 0x69, 0x6d, 0x65, 0x12, 0x35, 0x0a, 0x07, 0x64,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x56,
	0x61, 0x6c, 0x75, 0x65, 0x48, 0x01, 0x52, 0x07, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x88,
	0x01, 0x01, 0x12, 0x34, 0x0a, 0x14, 0x72, 0x6f, 0x6f, 0x74, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x76,
	0x69, 0x74, 0x79, 0x5f, 0x6c, 0x6f, 0x67, 0x5f, 0x69, 0x64, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09,
	0x48, 0x02, 0x52, 0x11, 0x72, 0x6f, 0x6f, 0x74, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79,
	0x4c, 0x6f, 0x67, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x22, 0x0a, 0x0a, 0x72, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x48, 0x03, 0x52, 0x09,
	0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x88, 0x01, 0x01, 0x42, 0x08, 0x0a, 0x06,
	0x5f, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x42, 0x0a, 0x0a, 0x08, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x73, 0x42, 0x17, 0x0a, 0x15, 0x5f, 0x72, 0x6f, 0x6f, 0x74, 0x5f, 0x61, 0x63, 0x74, 0x69,
	0x76, 0x69, 0x74, 0x79, 0x5f, 0x6c, 0x6f, 0x67, 0x5f, 0x69, 0x64, 0x42, 0x0d, 0x0a, 0x0b, 0x5f,
	0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x22, 0xb6, 0x03, 0x0a, 0x1a, 0x41,
	0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x4c, 0x6f, 0x67, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x53,
	0x69, 0x6d, 0x70, 0x6c, 0x65, 0x56, 0x69, 0x65, 0x77, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x62, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x42, 0x0a, 0x08, 0x6f, 0x70,
	0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x63, 0x74, 0x69,
	0x76, 0x69, 0x74, 0x79, 0x5f, 0x6c, 0x6f, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x70, 0x65, 0x72,
	0x61, 0x74, 0x6f, 0x72, 0x52, 0x08, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x12, 0x16,
	0x0a, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x42, 0x0a, 0x08, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79,
	0x5f, 0x6c, 0x6f, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x52, 0x08, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x3e, 0x0a, 0x05, 0x6f, 0x77,
	0x6e, 0x65, 0x72, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74,
	0x79, 0x5f, 0x6c, 0x6f, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x77, 0x6e, 0x65, 0x72, 0x48, 0x00,
	0x52, 0x05, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x88, 0x01, 0x01, 0x12, 0x2e, 0x0a, 0x04, 0x74, 0x69,
	0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x52, 0x04, 0x74, 0x69, 0x6d, 0x65, 0x12, 0x34, 0x0a, 0x14, 0x72, 0x6f,
	0x6f, 0x74, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x5f, 0x6c, 0x6f, 0x67, 0x5f,
	0x69, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x48, 0x01, 0x52, 0x11, 0x72, 0x6f, 0x6f, 0x74,
	0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x4c, 0x6f, 0x67, 0x49, 0x64, 0x88, 0x01, 0x01,
	0x42, 0x08, 0x0a, 0x06, 0x5f, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x42, 0x17, 0x0a, 0x15, 0x5f, 0x72,
	0x6f, 0x6f, 0x74, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x5f, 0x6c, 0x6f, 0x67,
	0x5f, 0x69, 0x64, 0x22, 0x2e, 0x0a, 0x08, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x12,
	0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12,
	0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x22, 0x2b, 0x0a, 0x05, 0x4f, 0x77, 0x6e, 0x65, 0x72, 0x12, 0x0e, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x22, 0xa8, 0x0b, 0x0a, 0x08, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x74, 0x79, 0x70, 0x65, 0x22, 0xe3, 0x0a, 0x0a, 0x04, 0x54, 0x79, 0x70, 0x65, 0x12, 0x14,
	0x0a, 0x10, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49,
	0x45, 0x44, 0x10, 0x00, 0x12, 0x0f, 0x0a, 0x0b, 0x41, 0x50, 0x50, 0x4f, 0x49, 0x4e, 0x54, 0x4d,
	0x45, 0x4e, 0x54, 0x10, 0x01, 0x12, 0x0f, 0x0a, 0x0b, 0x52, 0x45, 0x50, 0x45, 0x41, 0x54, 0x5f,
	0x52, 0x55, 0x4c, 0x45, 0x10, 0x02, 0x12, 0x09, 0x0a, 0x05, 0x42, 0x4c, 0x4f, 0x43, 0x4b, 0x10,
	0x03, 0x12, 0x14, 0x0a, 0x10, 0x47, 0x52, 0x4f, 0x4f, 0x4d, 0x49, 0x4e, 0x47, 0x5f, 0x53, 0x45,
	0x52, 0x56, 0x49, 0x43, 0x45, 0x10, 0x04, 0x12, 0x11, 0x0a, 0x0d, 0x47, 0x52, 0x4f, 0x4f, 0x4d,
	0x49, 0x4e, 0x47, 0x5f, 0x4e, 0x4f, 0x54, 0x45, 0x10, 0x05, 0x12, 0x18, 0x0a, 0x14, 0x4c, 0x41,
	0x4e, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x50, 0x41, 0x47, 0x45, 0x5f, 0x53, 0x45, 0x54, 0x54, 0x49,
	0x4e, 0x47, 0x10, 0x06, 0x12, 0x1a, 0x0a, 0x16, 0x4f, 0x4e, 0x4c, 0x49, 0x4e, 0x45, 0x5f, 0x42,
	0x4f, 0x4f, 0x4b, 0x49, 0x4e, 0x47, 0x5f, 0x53, 0x45, 0x54, 0x54, 0x49, 0x4e, 0x47, 0x10, 0x07,
	0x12, 0x20, 0x0a, 0x1c, 0x4f, 0x4e, 0x4c, 0x49, 0x4e, 0x45, 0x5f, 0x42, 0x4f, 0x4f, 0x4b, 0x49,
	0x4e, 0x47, 0x5f, 0x54, 0x45, 0x41, 0x4d, 0x5f, 0x53, 0x43, 0x48, 0x45, 0x44, 0x55, 0x4c, 0x45,
	0x10, 0x08, 0x12, 0x1a, 0x0a, 0x16, 0x4f, 0x4e, 0x4c, 0x49, 0x4e, 0x45, 0x5f, 0x42, 0x4f, 0x4f,
	0x4b, 0x49, 0x4e, 0x47, 0x5f, 0x53, 0x45, 0x52, 0x56, 0x49, 0x43, 0x45, 0x10, 0x09, 0x12, 0x1c,
	0x0a, 0x18, 0x4f, 0x4e, 0x4c, 0x49, 0x4e, 0x45, 0x5f, 0x42, 0x4f, 0x4f, 0x4b, 0x49, 0x4e, 0x47,
	0x5f, 0x50, 0x45, 0x54, 0x5f, 0x4c, 0x49, 0x4d, 0x49, 0x54, 0x10, 0x0a, 0x12, 0x25, 0x0a, 0x21,
	0x4f, 0x4e, 0x4c, 0x49, 0x4e, 0x45, 0x5f, 0x42, 0x4f, 0x4f, 0x4b, 0x49, 0x4e, 0x47, 0x5f, 0x43,
	0x55, 0x53, 0x54, 0x4f, 0x4d, 0x49, 0x5a, 0x45, 0x44, 0x5f, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e,
	0x54, 0x10, 0x0b, 0x12, 0x1f, 0x0a, 0x1b, 0x4f, 0x4e, 0x4c, 0x49, 0x4e, 0x45, 0x5f, 0x42, 0x4f,
	0x4f, 0x4b, 0x49, 0x4e, 0x47, 0x5f, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49,
	0x4f, 0x4e, 0x10, 0x0c, 0x12, 0x1b, 0x0a, 0x17, 0x4f, 0x4e, 0x4c, 0x49, 0x4e, 0x45, 0x5f, 0x42,
	0x4f, 0x4f, 0x4b, 0x49, 0x4e, 0x47, 0x5f, 0x51, 0x55, 0x45, 0x53, 0x54, 0x49, 0x4f, 0x4e, 0x10,
	0x0d, 0x12, 0x15, 0x0a, 0x11, 0x41, 0x42, 0x41, 0x4e, 0x44, 0x4f, 0x4e, 0x45, 0x44, 0x5f, 0x42,
	0x4f, 0x4f, 0x4b, 0x49, 0x4e, 0x47, 0x10, 0x0e, 0x12, 0x1c, 0x0a, 0x18, 0x43, 0x55, 0x53, 0x54,
	0x4f, 0x4d, 0x45, 0x52, 0x5f, 0x50, 0x52, 0x4f, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x52, 0x45, 0x51,
	0x55, 0x45, 0x53, 0x54, 0x10, 0x0f, 0x12, 0x0c, 0x0a, 0x08, 0x43, 0x55, 0x53, 0x54, 0x4f, 0x4d,
	0x45, 0x52, 0x10, 0x10, 0x12, 0x10, 0x0a, 0x0c, 0x43, 0x55, 0x53, 0x54, 0x4f, 0x4d, 0x45, 0x52,
	0x5f, 0x54, 0x41, 0x47, 0x10, 0x11, 0x12, 0x14, 0x0a, 0x10, 0x43, 0x55, 0x53, 0x54, 0x4f, 0x4d,
	0x45, 0x52, 0x5f, 0x41, 0x44, 0x44, 0x52, 0x45, 0x53, 0x53, 0x10, 0x12, 0x12, 0x14, 0x0a, 0x10,
	0x43, 0x55, 0x53, 0x54, 0x4f, 0x4d, 0x45, 0x52, 0x5f, 0x43, 0x4f, 0x4e, 0x54, 0x41, 0x43, 0x54,
	0x10, 0x13, 0x12, 0x11, 0x0a, 0x0d, 0x43, 0x55, 0x53, 0x54, 0x4f, 0x4d, 0x45, 0x52, 0x5f, 0x4e,
	0x4f, 0x54, 0x45, 0x10, 0x14, 0x12, 0x1a, 0x0a, 0x16, 0x43, 0x55, 0x53, 0x54, 0x4f, 0x4d, 0x45,
	0x52, 0x5f, 0x50, 0x52, 0x45, 0x46, 0x45, 0x52, 0x52, 0x45, 0x44, 0x5f, 0x54, 0x49, 0x50, 0x10,
	0x15, 0x12, 0x07, 0x0a, 0x03, 0x50, 0x45, 0x54, 0x10, 0x16, 0x12, 0x0c, 0x0a, 0x08, 0x50, 0x45,
	0x54, 0x5f, 0x4e, 0x4f, 0x54, 0x45, 0x10, 0x17, 0x12, 0x10, 0x0a, 0x0c, 0x50, 0x45, 0x54, 0x5f,
	0x42, 0x45, 0x48, 0x41, 0x56, 0x49, 0x4f, 0x52, 0x10, 0x18, 0x12, 0x0d, 0x0a, 0x09, 0x50, 0x45,
	0x54, 0x5f, 0x42, 0x52, 0x45, 0x45, 0x44, 0x10, 0x19, 0x12, 0x0c, 0x0a, 0x08, 0x50, 0x45, 0x54,
	0x5f, 0x43, 0x4f, 0x44, 0x45, 0x10, 0x1a, 0x12, 0x0d, 0x0a, 0x09, 0x50, 0x45, 0x54, 0x5f, 0x46,
	0x49, 0x58, 0x45, 0x44, 0x10, 0x1b, 0x12, 0x0d, 0x0a, 0x09, 0x50, 0x45, 0x54, 0x5f, 0x50, 0x48,
	0x4f, 0x54, 0x4f, 0x10, 0x1c, 0x12, 0x0c, 0x0a, 0x08, 0x50, 0x45, 0x54, 0x5f, 0x54, 0x59, 0x50,
	0x45, 0x10, 0x1d, 0x12, 0x0f, 0x0a, 0x0b, 0x50, 0x45, 0x54, 0x5f, 0x56, 0x41, 0x43, 0x43, 0x49,
	0x4e, 0x45, 0x10, 0x1e, 0x12, 0x17, 0x0a, 0x13, 0x50, 0x45, 0x54, 0x5f, 0x56, 0x41, 0x43, 0x43,
	0x49, 0x4e, 0x45, 0x5f, 0x42, 0x49, 0x4e, 0x44, 0x49, 0x4e, 0x47, 0x10, 0x1f, 0x12, 0x13, 0x0a,
	0x0f, 0x50, 0x45, 0x54, 0x5f, 0x48, 0x41, 0x49, 0x52, 0x5f, 0x4c, 0x45, 0x4e, 0x47, 0x54, 0x48,
	0x10, 0x20, 0x12, 0x1a, 0x0a, 0x16, 0x50, 0x45, 0x54, 0x5f, 0x43, 0x55, 0x53, 0x54, 0x4f, 0x4d,
	0x49, 0x5a, 0x45, 0x44, 0x5f, 0x53, 0x45, 0x52, 0x56, 0x49, 0x43, 0x45, 0x10, 0x21, 0x12, 0x17,
	0x0a, 0x13, 0x41, 0x47, 0x52, 0x45, 0x45, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x53, 0x49, 0x47, 0x4e,
	0x41, 0x54, 0x55, 0x52, 0x45, 0x10, 0x22, 0x12, 0x17, 0x0a, 0x13, 0x50, 0x45, 0x54, 0x5f, 0x49,
	0x4e, 0x43, 0x49, 0x44, 0x45, 0x4e, 0x54, 0x5f, 0x52, 0x45, 0x50, 0x4f, 0x52, 0x54, 0x10, 0x41,
	0x12, 0x0b, 0x0a, 0x07, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x10, 0x23, 0x12, 0x0b, 0x0a,
	0x07, 0x4d, 0x45, 0x53, 0x53, 0x41, 0x47, 0x45, 0x10, 0x24, 0x12, 0x08, 0x0a, 0x04, 0x43, 0x48,
	0x41, 0x54, 0x10, 0x25, 0x12, 0x18, 0x0a, 0x14, 0x41, 0x55, 0x54, 0x4f, 0x5f, 0x4d, 0x45, 0x53,
	0x53, 0x41, 0x47, 0x45, 0x5f, 0x53, 0x45, 0x54, 0x54, 0x49, 0x4e, 0x47, 0x10, 0x26, 0x12, 0x14,
	0x0a, 0x10, 0x52, 0x45, 0x4d, 0x49, 0x4e, 0x44, 0x45, 0x52, 0x5f, 0x53, 0x45, 0x54, 0x54, 0x49,
	0x4e, 0x47, 0x10, 0x27, 0x12, 0x16, 0x0a, 0x12, 0x41, 0x55, 0x54, 0x4f, 0x5f, 0x52, 0x45, 0x50,
	0x4c, 0x59, 0x5f, 0x53, 0x45, 0x54, 0x54, 0x49, 0x4e, 0x47, 0x10, 0x28, 0x12, 0x14, 0x0a, 0x10,
	0x53, 0x43, 0x48, 0x45, 0x44, 0x55, 0x4c, 0x45, 0x5f, 0x4d, 0x45, 0x53, 0x53, 0x41, 0x47, 0x45,
	0x10, 0x29, 0x12, 0x14, 0x0a, 0x10, 0x4d, 0x45, 0x53, 0x53, 0x41, 0x47, 0x45, 0x5f, 0x54, 0x45,
	0x4d, 0x50, 0x4c, 0x41, 0x54, 0x45, 0x10, 0x2a, 0x12, 0x12, 0x0a, 0x0e, 0x52, 0x45, 0x56, 0x49,
	0x45, 0x57, 0x5f, 0x42, 0x4f, 0x4f, 0x53, 0x54, 0x45, 0x52, 0x10, 0x2b, 0x12, 0x09, 0x0a, 0x05,
	0x4f, 0x52, 0x44, 0x45, 0x52, 0x10, 0x2c, 0x12, 0x0b, 0x0a, 0x07, 0x50, 0x41, 0x43, 0x4b, 0x41,
	0x47, 0x45, 0x10, 0x2d, 0x12, 0x14, 0x0a, 0x10, 0x43, 0x55, 0x53, 0x54, 0x4f, 0x4d, 0x45, 0x52,
	0x5f, 0x50, 0x41, 0x43, 0x4b, 0x41, 0x47, 0x45, 0x10, 0x2e, 0x12, 0x0b, 0x0a, 0x07, 0x50, 0x41,
	0x59, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x2f, 0x12, 0x0a, 0x0a, 0x06, 0x52, 0x45, 0x46, 0x55, 0x4e,
	0x44, 0x10, 0x30, 0x12, 0x1a, 0x0a, 0x16, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x53,
	0x54, 0x52, 0x49, 0x50, 0x45, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x10, 0x31, 0x12,
	0x0a, 0x0a, 0x06, 0x50, 0x41, 0x59, 0x4f, 0x55, 0x54, 0x10, 0x32, 0x12, 0x0c, 0x0a, 0x08, 0x54,
	0x45, 0x52, 0x4d, 0x49, 0x4e, 0x41, 0x4c, 0x10, 0x33, 0x12, 0x15, 0x0a, 0x11, 0x54, 0x45, 0x52,
	0x4d, 0x49, 0x4e, 0x41, 0x4c, 0x5f, 0x4c, 0x4f, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x34,
	0x12, 0x0f, 0x0a, 0x0b, 0x53, 0x54, 0x52, 0x49, 0x50, 0x45, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x10,
	0x35, 0x12, 0x13, 0x0a, 0x0f, 0x53, 0x54, 0x52, 0x49, 0x50, 0x45, 0x5f, 0x43, 0x55, 0x53, 0x54,
	0x4f, 0x4d, 0x45, 0x52, 0x10, 0x36, 0x12, 0x0f, 0x0a, 0x0b, 0x53, 0x51, 0x55, 0x41, 0x52, 0x45,
	0x5f, 0x43, 0x41, 0x52, 0x44, 0x10, 0x37, 0x12, 0x13, 0x0a, 0x0f, 0x53, 0x51, 0x55, 0x41, 0x52,
	0x45, 0x5f, 0x43, 0x55, 0x53, 0x54, 0x4f, 0x4d, 0x45, 0x52, 0x10, 0x38, 0x12, 0x10, 0x0a, 0x0c,
	0x53, 0x55, 0x42, 0x53, 0x43, 0x52, 0x49, 0x50, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x39, 0x12, 0x19,
	0x0a, 0x15, 0x4d, 0x45, 0x53, 0x53, 0x41, 0x47, 0x45, 0x5f, 0x45, 0x4d, 0x41, 0x49, 0x4c, 0x5f,
	0x50, 0x41, 0x43, 0x4b, 0x41, 0x47, 0x45, 0x10, 0x3a, 0x12, 0x0c, 0x0a, 0x08, 0x48, 0x41, 0x52,
	0x44, 0x57, 0x41, 0x52, 0x45, 0x10, 0x3b, 0x12, 0x1e, 0x0a, 0x1a, 0x43, 0x55, 0x53, 0x54, 0x4f,
	0x4d, 0x49, 0x5a, 0x45, 0x44, 0x5f, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x53, 0x45,
	0x54, 0x54, 0x49, 0x4e, 0x47, 0x10, 0x3c, 0x12, 0x18, 0x0a, 0x14, 0x43, 0x4f, 0x4d, 0x50, 0x41,
	0x4e, 0x59, 0x5f, 0x50, 0x4c, 0x41, 0x4e, 0x5f, 0x46, 0x45, 0x41, 0x54, 0x55, 0x52, 0x45, 0x10,
	0x3d, 0x12, 0x11, 0x0a, 0x0d, 0x50, 0x4c, 0x41, 0x54, 0x46, 0x4f, 0x52, 0x4d, 0x5f, 0x43, 0x41,
	0x52, 0x45, 0x10, 0x3e, 0x12, 0x09, 0x0a, 0x05, 0x53, 0x54, 0x41, 0x46, 0x46, 0x10, 0x3f, 0x12,
	0x14, 0x0a, 0x10, 0x42, 0x55, 0x53, 0x49, 0x4e, 0x45, 0x53, 0x53, 0x5f, 0x53, 0x45, 0x54, 0x54,
	0x49, 0x4e, 0x47, 0x10, 0x40, 0x12, 0x14, 0x0a, 0x10, 0x53, 0x45, 0x52, 0x56, 0x49, 0x43, 0x45,
	0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x10, 0x42, 0x42, 0x89, 0x01, 0x0a, 0x24,
	0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x5f, 0x6c, 0x6f,
	0x67, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x5f, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63,
	0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69,
	0x74, 0x79, 0x5f, 0x6c, 0x6f, 0x67, 0x2f, 0x76, 0x31, 0x3b, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69,
	0x74, 0x79, 0x6c, 0x6f, 0x67, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_activity_log_v1_activity_log_models_proto_rawDescOnce sync.Once
	file_moego_models_activity_log_v1_activity_log_models_proto_rawDescData = file_moego_models_activity_log_v1_activity_log_models_proto_rawDesc
)

func file_moego_models_activity_log_v1_activity_log_models_proto_rawDescGZIP() []byte {
	file_moego_models_activity_log_v1_activity_log_models_proto_rawDescOnce.Do(func() {
		file_moego_models_activity_log_v1_activity_log_models_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_activity_log_v1_activity_log_models_proto_rawDescData)
	})
	return file_moego_models_activity_log_v1_activity_log_models_proto_rawDescData
}

var file_moego_models_activity_log_v1_activity_log_models_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_moego_models_activity_log_v1_activity_log_models_proto_msgTypes = make([]protoimpl.MessageInfo, 5)
var file_moego_models_activity_log_v1_activity_log_models_proto_goTypes = []interface{}{
	(Resource_Type)(0),                 // 0: moego.models.activity_log.v1.Resource.Type
	(*ActivityLogModel)(nil),           // 1: moego.models.activity_log.v1.ActivityLogModel
	(*ActivityLogModelSimpleView)(nil), // 2: moego.models.activity_log.v1.ActivityLogModelSimpleView
	(*Operator)(nil),                   // 3: moego.models.activity_log.v1.Operator
	(*Owner)(nil),                      // 4: moego.models.activity_log.v1.Owner
	(*Resource)(nil),                   // 5: moego.models.activity_log.v1.Resource
	(*timestamppb.Timestamp)(nil),      // 6: google.protobuf.Timestamp
	(*structpb.Value)(nil),             // 7: google.protobuf.Value
}
var file_moego_models_activity_log_v1_activity_log_models_proto_depIdxs = []int32{
	3, // 0: moego.models.activity_log.v1.ActivityLogModel.operator:type_name -> moego.models.activity_log.v1.Operator
	5, // 1: moego.models.activity_log.v1.ActivityLogModel.resource:type_name -> moego.models.activity_log.v1.Resource
	4, // 2: moego.models.activity_log.v1.ActivityLogModel.owner:type_name -> moego.models.activity_log.v1.Owner
	6, // 3: moego.models.activity_log.v1.ActivityLogModel.time:type_name -> google.protobuf.Timestamp
	7, // 4: moego.models.activity_log.v1.ActivityLogModel.details:type_name -> google.protobuf.Value
	3, // 5: moego.models.activity_log.v1.ActivityLogModelSimpleView.operator:type_name -> moego.models.activity_log.v1.Operator
	5, // 6: moego.models.activity_log.v1.ActivityLogModelSimpleView.resource:type_name -> moego.models.activity_log.v1.Resource
	4, // 7: moego.models.activity_log.v1.ActivityLogModelSimpleView.owner:type_name -> moego.models.activity_log.v1.Owner
	6, // 8: moego.models.activity_log.v1.ActivityLogModelSimpleView.time:type_name -> google.protobuf.Timestamp
	9, // [9:9] is the sub-list for method output_type
	9, // [9:9] is the sub-list for method input_type
	9, // [9:9] is the sub-list for extension type_name
	9, // [9:9] is the sub-list for extension extendee
	0, // [0:9] is the sub-list for field type_name
}

func init() { file_moego_models_activity_log_v1_activity_log_models_proto_init() }
func file_moego_models_activity_log_v1_activity_log_models_proto_init() {
	if File_moego_models_activity_log_v1_activity_log_models_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_models_activity_log_v1_activity_log_models_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ActivityLogModel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_activity_log_v1_activity_log_models_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ActivityLogModelSimpleView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_activity_log_v1_activity_log_models_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Operator); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_activity_log_v1_activity_log_models_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Owner); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_activity_log_v1_activity_log_models_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Resource); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_models_activity_log_v1_activity_log_models_proto_msgTypes[0].OneofWrappers = []interface{}{}
	file_moego_models_activity_log_v1_activity_log_models_proto_msgTypes[1].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_activity_log_v1_activity_log_models_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   5,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_activity_log_v1_activity_log_models_proto_goTypes,
		DependencyIndexes: file_moego_models_activity_log_v1_activity_log_models_proto_depIdxs,
		EnumInfos:         file_moego_models_activity_log_v1_activity_log_models_proto_enumTypes,
		MessageInfos:      file_moego_models_activity_log_v1_activity_log_models_proto_msgTypes,
	}.Build()
	File_moego_models_activity_log_v1_activity_log_models_proto = out.File
	file_moego_models_activity_log_v1_activity_log_models_proto_rawDesc = nil
	file_moego_models_activity_log_v1_activity_log_models_proto_goTypes = nil
	file_moego_models_activity_log_v1_activity_log_models_proto_depIdxs = nil
}
