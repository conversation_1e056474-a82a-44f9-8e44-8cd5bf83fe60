// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/customer/v1/customer_pet_vaccine_models.proto

package customerpb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// pet vaccine model
type PetVaccineModel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// pet id
	PetId int64 `protobuf:"varint,2,opt,name=pet_id,json=petId,proto3" json:"pet_id,omitempty"`
	// vaccine metadata id
	VaccineMetadataId int32 `protobuf:"varint,3,opt,name=vaccine_metadata_id,json=vaccineMetadataId,proto3" json:"vaccine_metadata_id,omitempty"`
	// expiration date
	ExpirationDate string `protobuf:"bytes,4,opt,name=expiration_date,json=expirationDate,proto3" json:"expiration_date,omitempty"`
	// document url list
	DocumentUrlList []string `protobuf:"bytes,5,rep,name=document_url_list,json=documentUrlList,proto3" json:"document_url_list,omitempty"`
}

func (x *PetVaccineModel) Reset() {
	*x = PetVaccineModel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_customer_v1_customer_pet_vaccine_models_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PetVaccineModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PetVaccineModel) ProtoMessage() {}

func (x *PetVaccineModel) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_customer_v1_customer_pet_vaccine_models_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PetVaccineModel.ProtoReflect.Descriptor instead.
func (*PetVaccineModel) Descriptor() ([]byte, []int) {
	return file_moego_models_customer_v1_customer_pet_vaccine_models_proto_rawDescGZIP(), []int{0}
}

func (x *PetVaccineModel) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *PetVaccineModel) GetPetId() int64 {
	if x != nil {
		return x.PetId
	}
	return 0
}

func (x *PetVaccineModel) GetVaccineMetadataId() int32 {
	if x != nil {
		return x.VaccineMetadataId
	}
	return 0
}

func (x *PetVaccineModel) GetExpirationDate() string {
	if x != nil {
		return x.ExpirationDate
	}
	return ""
}

func (x *PetVaccineModel) GetDocumentUrlList() []string {
	if x != nil {
		return x.DocumentUrlList
	}
	return nil
}

// pet vaccine simple view
type PetVaccineSimpleView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// vaccine metadata id
	VaccineMetadataId int32 `protobuf:"varint,1,opt,name=vaccine_metadata_id,json=vaccineMetadataId,proto3" json:"vaccine_metadata_id,omitempty"`
	// expiration date
	ExpirationDate string `protobuf:"bytes,2,opt,name=expiration_date,json=expirationDate,proto3" json:"expiration_date,omitempty"`
	// document url list
	DocumentUrlList []string `protobuf:"bytes,3,rep,name=document_url_list,json=documentUrlList,proto3" json:"document_url_list,omitempty"`
}

func (x *PetVaccineSimpleView) Reset() {
	*x = PetVaccineSimpleView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_customer_v1_customer_pet_vaccine_models_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PetVaccineSimpleView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PetVaccineSimpleView) ProtoMessage() {}

func (x *PetVaccineSimpleView) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_customer_v1_customer_pet_vaccine_models_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PetVaccineSimpleView.ProtoReflect.Descriptor instead.
func (*PetVaccineSimpleView) Descriptor() ([]byte, []int) {
	return file_moego_models_customer_v1_customer_pet_vaccine_models_proto_rawDescGZIP(), []int{1}
}

func (x *PetVaccineSimpleView) GetVaccineMetadataId() int32 {
	if x != nil {
		return x.VaccineMetadataId
	}
	return 0
}

func (x *PetVaccineSimpleView) GetExpirationDate() string {
	if x != nil {
		return x.ExpirationDate
	}
	return ""
}

func (x *PetVaccineSimpleView) GetDocumentUrlList() []string {
	if x != nil {
		return x.DocumentUrlList
	}
	return nil
}

// pet vaccine online booking view
type PetVaccineOnlineBookingView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// vaccine binding id
	VaccineBindingId int32 `protobuf:"varint,1,opt,name=vaccine_binding_id,json=vaccineBindingId,proto3" json:"vaccine_binding_id,omitempty"`
	// vaccine id
	VaccineId int32 `protobuf:"varint,2,opt,name=vaccine_id,json=vaccineId,proto3" json:"vaccine_id,omitempty"`
	// expiration date
	ExpirationDate string `protobuf:"bytes,3,opt,name=expiration_date,json=expirationDate,proto3" json:"expiration_date,omitempty"`
	// document url list
	DocumentUrls []string `protobuf:"bytes,4,rep,name=document_urls,json=documentUrls,proto3" json:"document_urls,omitempty"`
}

func (x *PetVaccineOnlineBookingView) Reset() {
	*x = PetVaccineOnlineBookingView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_customer_v1_customer_pet_vaccine_models_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PetVaccineOnlineBookingView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PetVaccineOnlineBookingView) ProtoMessage() {}

func (x *PetVaccineOnlineBookingView) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_customer_v1_customer_pet_vaccine_models_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PetVaccineOnlineBookingView.ProtoReflect.Descriptor instead.
func (*PetVaccineOnlineBookingView) Descriptor() ([]byte, []int) {
	return file_moego_models_customer_v1_customer_pet_vaccine_models_proto_rawDescGZIP(), []int{2}
}

func (x *PetVaccineOnlineBookingView) GetVaccineBindingId() int32 {
	if x != nil {
		return x.VaccineBindingId
	}
	return 0
}

func (x *PetVaccineOnlineBookingView) GetVaccineId() int32 {
	if x != nil {
		return x.VaccineId
	}
	return 0
}

func (x *PetVaccineOnlineBookingView) GetExpirationDate() string {
	if x != nil {
		return x.ExpirationDate
	}
	return ""
}

func (x *PetVaccineOnlineBookingView) GetDocumentUrls() []string {
	if x != nil {
		return x.DocumentUrls
	}
	return nil
}

var File_moego_models_customer_v1_customer_pet_vaccine_models_proto protoreflect.FileDescriptor

var file_moego_models_customer_v1_customer_pet_vaccine_models_proto_rawDesc = []byte{
	0x0a, 0x3a, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x63,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x5f, 0x70, 0x65, 0x74, 0x5f, 0x76, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x5f,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x18, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x22, 0xbd, 0x01, 0x0a, 0x0f, 0x50, 0x65, 0x74, 0x56, 0x61,
	0x63, 0x63, 0x69, 0x6e, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x15, 0x0a, 0x06, 0x70, 0x65,
	0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x70, 0x65, 0x74, 0x49,
	0x64, 0x12, 0x2e, 0x0a, 0x13, 0x76, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x5f, 0x6d, 0x65, 0x74,
	0x61, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x11,
	0x76, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x49,
	0x64, 0x12, 0x27, 0x0a, 0x0f, 0x65, 0x78, 0x70, 0x69, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x64, 0x61, 0x74, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x65, 0x78, 0x70, 0x69,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x65, 0x12, 0x2a, 0x0a, 0x11, 0x64, 0x6f,
	0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x75, 0x72, 0x6c, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18,
	0x05, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0f, 0x64, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x55,
	0x72, 0x6c, 0x4c, 0x69, 0x73, 0x74, 0x22, 0x9b, 0x01, 0x0a, 0x14, 0x50, 0x65, 0x74, 0x56, 0x61,
	0x63, 0x63, 0x69, 0x6e, 0x65, 0x53, 0x69, 0x6d, 0x70, 0x6c, 0x65, 0x56, 0x69, 0x65, 0x77, 0x12,
	0x2e, 0x0a, 0x13, 0x76, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x64,
	0x61, 0x74, 0x61, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x11, 0x76, 0x61,
	0x63, 0x63, 0x69, 0x6e, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x49, 0x64, 0x12,
	0x27, 0x0a, 0x0f, 0x65, 0x78, 0x70, 0x69, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x61,
	0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x65, 0x78, 0x70, 0x69, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x65, 0x12, 0x2a, 0x0a, 0x11, 0x64, 0x6f, 0x63, 0x75,
	0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x75, 0x72, 0x6c, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x03, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x0f, 0x64, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x55, 0x72, 0x6c,
	0x4c, 0x69, 0x73, 0x74, 0x22, 0xb8, 0x01, 0x0a, 0x1b, 0x50, 0x65, 0x74, 0x56, 0x61, 0x63, 0x63,
	0x69, 0x6e, 0x65, 0x4f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67,
	0x56, 0x69, 0x65, 0x77, 0x12, 0x2c, 0x0a, 0x12, 0x76, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x5f,
	0x62, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x10, 0x76, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67,
	0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x76, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x5f, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x76, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x49,
	0x64, 0x12, 0x27, 0x0a, 0x0f, 0x65, 0x78, 0x70, 0x69, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x64, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x65, 0x78, 0x70, 0x69,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x64, 0x6f,
	0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x75, 0x72, 0x6c, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28,
	0x09, 0x52, 0x0c, 0x64, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x55, 0x72, 0x6c, 0x73, 0x42,
	0x7e, 0x0a, 0x20, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x58, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f,
	0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x2f, 0x76, 0x31, 0x3b, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x70, 0x62, 0x62,
	0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_customer_v1_customer_pet_vaccine_models_proto_rawDescOnce sync.Once
	file_moego_models_customer_v1_customer_pet_vaccine_models_proto_rawDescData = file_moego_models_customer_v1_customer_pet_vaccine_models_proto_rawDesc
)

func file_moego_models_customer_v1_customer_pet_vaccine_models_proto_rawDescGZIP() []byte {
	file_moego_models_customer_v1_customer_pet_vaccine_models_proto_rawDescOnce.Do(func() {
		file_moego_models_customer_v1_customer_pet_vaccine_models_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_customer_v1_customer_pet_vaccine_models_proto_rawDescData)
	})
	return file_moego_models_customer_v1_customer_pet_vaccine_models_proto_rawDescData
}

var file_moego_models_customer_v1_customer_pet_vaccine_models_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_moego_models_customer_v1_customer_pet_vaccine_models_proto_goTypes = []interface{}{
	(*PetVaccineModel)(nil),             // 0: moego.models.customer.v1.PetVaccineModel
	(*PetVaccineSimpleView)(nil),        // 1: moego.models.customer.v1.PetVaccineSimpleView
	(*PetVaccineOnlineBookingView)(nil), // 2: moego.models.customer.v1.PetVaccineOnlineBookingView
}
var file_moego_models_customer_v1_customer_pet_vaccine_models_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_moego_models_customer_v1_customer_pet_vaccine_models_proto_init() }
func file_moego_models_customer_v1_customer_pet_vaccine_models_proto_init() {
	if File_moego_models_customer_v1_customer_pet_vaccine_models_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_models_customer_v1_customer_pet_vaccine_models_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PetVaccineModel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_customer_v1_customer_pet_vaccine_models_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PetVaccineSimpleView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_customer_v1_customer_pet_vaccine_models_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PetVaccineOnlineBookingView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_customer_v1_customer_pet_vaccine_models_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_customer_v1_customer_pet_vaccine_models_proto_goTypes,
		DependencyIndexes: file_moego_models_customer_v1_customer_pet_vaccine_models_proto_depIdxs,
		MessageInfos:      file_moego_models_customer_v1_customer_pet_vaccine_models_proto_msgTypes,
	}.Build()
	File_moego_models_customer_v1_customer_pet_vaccine_models_proto = out.File
	file_moego_models_customer_v1_customer_pet_vaccine_models_proto_rawDesc = nil
	file_moego_models_customer_v1_customer_pet_vaccine_models_proto_goTypes = nil
	file_moego_models_customer_v1_customer_pet_vaccine_models_proto_depIdxs = nil
}
