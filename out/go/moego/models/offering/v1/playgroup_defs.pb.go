// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/offering/v1/playgroup_defs.proto

package offeringpb

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// create playgroup def
type CreatePlaygroupDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// name
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// color code
	ColorCode string `protobuf:"bytes,2,opt,name=color_code,json=colorCode,proto3" json:"color_code,omitempty"`
	// max pet capacity
	MaxPetCapacity int32 `protobuf:"varint,3,opt,name=max_pet_capacity,json=maxPetCapacity,proto3" json:"max_pet_capacity,omitempty"`
	// description
	Description string `protobuf:"bytes,4,opt,name=description,proto3" json:"description,omitempty"`
}

func (x *CreatePlaygroupDef) Reset() {
	*x = CreatePlaygroupDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_offering_v1_playgroup_defs_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreatePlaygroupDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreatePlaygroupDef) ProtoMessage() {}

func (x *CreatePlaygroupDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_offering_v1_playgroup_defs_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreatePlaygroupDef.ProtoReflect.Descriptor instead.
func (*CreatePlaygroupDef) Descriptor() ([]byte, []int) {
	return file_moego_models_offering_v1_playgroup_defs_proto_rawDescGZIP(), []int{0}
}

func (x *CreatePlaygroupDef) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CreatePlaygroupDef) GetColorCode() string {
	if x != nil {
		return x.ColorCode
	}
	return ""
}

func (x *CreatePlaygroupDef) GetMaxPetCapacity() int32 {
	if x != nil {
		return x.MaxPetCapacity
	}
	return 0
}

func (x *CreatePlaygroupDef) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

// update playgroup def
type UpdatePlaygroupDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// name
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// color code
	ColorCode string `protobuf:"bytes,3,opt,name=color_code,json=colorCode,proto3" json:"color_code,omitempty"`
	// max pet capacity
	MaxPetCapacity int32 `protobuf:"varint,4,opt,name=max_pet_capacity,json=maxPetCapacity,proto3" json:"max_pet_capacity,omitempty"`
	// description
	Description string `protobuf:"bytes,5,opt,name=description,proto3" json:"description,omitempty"`
}

func (x *UpdatePlaygroupDef) Reset() {
	*x = UpdatePlaygroupDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_offering_v1_playgroup_defs_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdatePlaygroupDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdatePlaygroupDef) ProtoMessage() {}

func (x *UpdatePlaygroupDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_offering_v1_playgroup_defs_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdatePlaygroupDef.ProtoReflect.Descriptor instead.
func (*UpdatePlaygroupDef) Descriptor() ([]byte, []int) {
	return file_moego_models_offering_v1_playgroup_defs_proto_rawDescGZIP(), []int{1}
}

func (x *UpdatePlaygroupDef) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdatePlaygroupDef) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *UpdatePlaygroupDef) GetColorCode() string {
	if x != nil {
		return x.ColorCode
	}
	return ""
}

func (x *UpdatePlaygroupDef) GetMaxPetCapacity() int32 {
	if x != nil {
		return x.MaxPetCapacity
	}
	return 0
}

func (x *UpdatePlaygroupDef) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

var File_moego_models_offering_v1_playgroup_defs_proto protoreflect.FileDescriptor

var file_moego_models_offering_v1_playgroup_defs_proto_rawDesc = []byte{
	0x0a, 0x2d, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f,
	0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x70, 0x6c, 0x61, 0x79, 0x67,
	0x72, 0x6f, 0x75, 0x70, 0x5f, 0x64, 0x65, 0x66, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12,
	0x18, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66,
	0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64,
	0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x22, 0xce, 0x01, 0x0a, 0x12, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x6c, 0x61,
	0x79, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x44, 0x65, 0x66, 0x12, 0x1d, 0x0a, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01,
	0x18, 0x14, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x6f, 0x6c, 0x6f,
	0x72, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x1a, 0xfa, 0x42,
	0x17, 0x72, 0x15, 0x32, 0x13, 0x5e, 0x23, 0x28, 0x5b, 0x41, 0x2d, 0x46, 0x61, 0x2d, 0x66, 0x30,
	0x2d, 0x39, 0x5d, 0x7b, 0x36, 0x7d, 0x29, 0x24, 0x52, 0x09, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x43,
	0x6f, 0x64, 0x65, 0x12, 0x31, 0x0a, 0x10, 0x6d, 0x61, 0x78, 0x5f, 0x70, 0x65, 0x74, 0x5f, 0x63,
	0x61, 0x70, 0x61, 0x63, 0x69, 0x74, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x1a, 0x02, 0x20, 0x00, 0x52, 0x0e, 0x6d, 0x61, 0x78, 0x50, 0x65, 0x74, 0x43, 0x61,
	0x70, 0x61, 0x63, 0x69, 0x74, 0x79, 0x12, 0x2b, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06,
	0x72, 0x04, 0x10, 0x00, 0x18, 0x64, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x22, 0xe7, 0x01, 0x0a, 0x12, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x6c,
	0x61, 0x79, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x44, 0x65, 0x66, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52,
	0x02, 0x69, 0x64, 0x12, 0x1d, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x14, 0x52, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x5f, 0x63, 0x6f, 0x64, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x1a, 0xfa, 0x42, 0x17, 0x72, 0x15, 0x32, 0x13, 0x5e,
	0x23, 0x28, 0x5b, 0x41, 0x2d, 0x46, 0x61, 0x2d, 0x66, 0x30, 0x2d, 0x39, 0x5d, 0x7b, 0x36, 0x7d,
	0x29, 0x24, 0x52, 0x09, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x31, 0x0a,
	0x10, 0x6d, 0x61, 0x78, 0x5f, 0x70, 0x65, 0x74, 0x5f, 0x63, 0x61, 0x70, 0x61, 0x63, 0x69, 0x74,
	0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x1a, 0x02, 0x20, 0x00,
	0x52, 0x0e, 0x6d, 0x61, 0x78, 0x50, 0x65, 0x74, 0x43, 0x61, 0x70, 0x61, 0x63, 0x69, 0x74, 0x79,
	0x12, 0x2b, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x00, 0x18, 0x64,
	0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x7e, 0x0a,
	0x20, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76,
	0x31, 0x50, 0x01, 0x5a, 0x58, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f,
	0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2f,
	0x76, 0x31, 0x3b, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x70, 0x62, 0x62, 0x06, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_offering_v1_playgroup_defs_proto_rawDescOnce sync.Once
	file_moego_models_offering_v1_playgroup_defs_proto_rawDescData = file_moego_models_offering_v1_playgroup_defs_proto_rawDesc
)

func file_moego_models_offering_v1_playgroup_defs_proto_rawDescGZIP() []byte {
	file_moego_models_offering_v1_playgroup_defs_proto_rawDescOnce.Do(func() {
		file_moego_models_offering_v1_playgroup_defs_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_offering_v1_playgroup_defs_proto_rawDescData)
	})
	return file_moego_models_offering_v1_playgroup_defs_proto_rawDescData
}

var file_moego_models_offering_v1_playgroup_defs_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_moego_models_offering_v1_playgroup_defs_proto_goTypes = []interface{}{
	(*CreatePlaygroupDef)(nil), // 0: moego.models.offering.v1.CreatePlaygroupDef
	(*UpdatePlaygroupDef)(nil), // 1: moego.models.offering.v1.UpdatePlaygroupDef
}
var file_moego_models_offering_v1_playgroup_defs_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_moego_models_offering_v1_playgroup_defs_proto_init() }
func file_moego_models_offering_v1_playgroup_defs_proto_init() {
	if File_moego_models_offering_v1_playgroup_defs_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_models_offering_v1_playgroup_defs_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreatePlaygroupDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_offering_v1_playgroup_defs_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdatePlaygroupDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_offering_v1_playgroup_defs_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_offering_v1_playgroup_defs_proto_goTypes,
		DependencyIndexes: file_moego_models_offering_v1_playgroup_defs_proto_depIdxs,
		MessageInfos:      file_moego_models_offering_v1_playgroup_defs_proto_msgTypes,
	}.Build()
	File_moego_models_offering_v1_playgroup_defs_proto = out.File
	file_moego_models_offering_v1_playgroup_defs_proto_rawDesc = nil
	file_moego_models_offering_v1_playgroup_defs_proto_goTypes = nil
	file_moego_models_offering_v1_playgroup_defs_proto_depIdxs = nil
}
