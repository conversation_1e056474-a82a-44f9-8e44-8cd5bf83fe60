// @since 2024-06-05 11:24:51
// <AUTHOR> <<EMAIL>>

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/auto_message/v1/auto_message_task_enums.proto

package automessagepb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// message task object type
type AutoMessageTaskObjectType int32

const (
	// unspecified value
	AutoMessageTaskObjectType_AUTO_MESSAGE_TASK_OBJECT_TYPE_UNSPECIFIED AutoMessageTaskObjectType = 0
	// message object type: appointment
	AutoMessageTaskObjectType_AUTO_MESSAGE_TASK_OBJECT_TYPE_APPOINTMENT AutoMessageTaskObjectType = 1
	// message object type: pet
	AutoMessageTaskObjectType_AUTO_MESSAGE_TASK_OBJECT_TYPE_PET AutoMessageTaskObjectType = 2
	// message object type: customer
	AutoMessageTaskObjectType_AUTO_MESSAGE_TASK_OBJECT_TYPE_CUSTOMER AutoMessageTaskObjectType = 3
)

// Enum value maps for AutoMessageTaskObjectType.
var (
	AutoMessageTaskObjectType_name = map[int32]string{
		0: "AUTO_MESSAGE_TASK_OBJECT_TYPE_UNSPECIFIED",
		1: "AUTO_MESSAGE_TASK_OBJECT_TYPE_APPOINTMENT",
		2: "AUTO_MESSAGE_TASK_OBJECT_TYPE_PET",
		3: "AUTO_MESSAGE_TASK_OBJECT_TYPE_CUSTOMER",
	}
	AutoMessageTaskObjectType_value = map[string]int32{
		"AUTO_MESSAGE_TASK_OBJECT_TYPE_UNSPECIFIED": 0,
		"AUTO_MESSAGE_TASK_OBJECT_TYPE_APPOINTMENT": 1,
		"AUTO_MESSAGE_TASK_OBJECT_TYPE_PET":         2,
		"AUTO_MESSAGE_TASK_OBJECT_TYPE_CUSTOMER":    3,
	}
)

func (x AutoMessageTaskObjectType) Enum() *AutoMessageTaskObjectType {
	p := new(AutoMessageTaskObjectType)
	*p = x
	return p
}

func (x AutoMessageTaskObjectType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AutoMessageTaskObjectType) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_auto_message_v1_auto_message_task_enums_proto_enumTypes[0].Descriptor()
}

func (AutoMessageTaskObjectType) Type() protoreflect.EnumType {
	return &file_moego_models_auto_message_v1_auto_message_task_enums_proto_enumTypes[0]
}

func (x AutoMessageTaskObjectType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AutoMessageTaskObjectType.Descriptor instead.
func (AutoMessageTaskObjectType) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_auto_message_v1_auto_message_task_enums_proto_rawDescGZIP(), []int{0}
}

// message task receiver type
type AutoMessageTaskReceiverType int32

const (
	// unspecified value
	AutoMessageTaskReceiverType_AUTO_MESSAGE_TASK_RECEIVER_TYPE_UNSPECIFIED AutoMessageTaskReceiverType = 0
	// message task receiver type: customer
	AutoMessageTaskReceiverType_AUTO_MESSAGE_TASK_RECEIVER_TYPE_CUSTOMER AutoMessageTaskReceiverType = 1
)

// Enum value maps for AutoMessageTaskReceiverType.
var (
	AutoMessageTaskReceiverType_name = map[int32]string{
		0: "AUTO_MESSAGE_TASK_RECEIVER_TYPE_UNSPECIFIED",
		1: "AUTO_MESSAGE_TASK_RECEIVER_TYPE_CUSTOMER",
	}
	AutoMessageTaskReceiverType_value = map[string]int32{
		"AUTO_MESSAGE_TASK_RECEIVER_TYPE_UNSPECIFIED": 0,
		"AUTO_MESSAGE_TASK_RECEIVER_TYPE_CUSTOMER":    1,
	}
)

func (x AutoMessageTaskReceiverType) Enum() *AutoMessageTaskReceiverType {
	p := new(AutoMessageTaskReceiverType)
	*p = x
	return p
}

func (x AutoMessageTaskReceiverType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AutoMessageTaskReceiverType) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_auto_message_v1_auto_message_task_enums_proto_enumTypes[1].Descriptor()
}

func (AutoMessageTaskReceiverType) Type() protoreflect.EnumType {
	return &file_moego_models_auto_message_v1_auto_message_task_enums_proto_enumTypes[1]
}

func (x AutoMessageTaskReceiverType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AutoMessageTaskReceiverType.Descriptor instead.
func (AutoMessageTaskReceiverType) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_auto_message_v1_auto_message_task_enums_proto_rawDescGZIP(), []int{1}
}

// message task status
type AutoMessageTaskStatus int32

const (
	// unspecified value
	AutoMessageTaskStatus_AUTO_MESSAGE_TASK_STATUS_UNSPECIFIED AutoMessageTaskStatus = 0
	// init
	AutoMessageTaskStatus_AUTO_MESSAGE_TASK_STATUS_INIT AutoMessageTaskStatus = 1
	// sending
	AutoMessageTaskStatus_AUTO_MESSAGE_TASK_STATUS_SENDING AutoMessageTaskStatus = 2
	// succeed
	AutoMessageTaskStatus_AUTO_MESSAGE_TASK_STATUS_SUCCEED AutoMessageTaskStatus = 3
	// failed
	AutoMessageTaskStatus_AUTO_MESSAGE_TASK_STATUS_FAILED AutoMessageTaskStatus = 4
	// cancelled by staff
	AutoMessageTaskStatus_AUTO_MESSAGE_TASK_STATUS_CANCELLED_BY_STAFF AutoMessageTaskStatus = 5
	// cancelled by system
	AutoMessageTaskStatus_AUTO_MESSAGE_TASK_STATUS_CANCELLED_BY_SYSTEM AutoMessageTaskStatus = 6
)

// Enum value maps for AutoMessageTaskStatus.
var (
	AutoMessageTaskStatus_name = map[int32]string{
		0: "AUTO_MESSAGE_TASK_STATUS_UNSPECIFIED",
		1: "AUTO_MESSAGE_TASK_STATUS_INIT",
		2: "AUTO_MESSAGE_TASK_STATUS_SENDING",
		3: "AUTO_MESSAGE_TASK_STATUS_SUCCEED",
		4: "AUTO_MESSAGE_TASK_STATUS_FAILED",
		5: "AUTO_MESSAGE_TASK_STATUS_CANCELLED_BY_STAFF",
		6: "AUTO_MESSAGE_TASK_STATUS_CANCELLED_BY_SYSTEM",
	}
	AutoMessageTaskStatus_value = map[string]int32{
		"AUTO_MESSAGE_TASK_STATUS_UNSPECIFIED":         0,
		"AUTO_MESSAGE_TASK_STATUS_INIT":                1,
		"AUTO_MESSAGE_TASK_STATUS_SENDING":             2,
		"AUTO_MESSAGE_TASK_STATUS_SUCCEED":             3,
		"AUTO_MESSAGE_TASK_STATUS_FAILED":              4,
		"AUTO_MESSAGE_TASK_STATUS_CANCELLED_BY_STAFF":  5,
		"AUTO_MESSAGE_TASK_STATUS_CANCELLED_BY_SYSTEM": 6,
	}
)

func (x AutoMessageTaskStatus) Enum() *AutoMessageTaskStatus {
	p := new(AutoMessageTaskStatus)
	*p = x
	return p
}

func (x AutoMessageTaskStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AutoMessageTaskStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_auto_message_v1_auto_message_task_enums_proto_enumTypes[2].Descriptor()
}

func (AutoMessageTaskStatus) Type() protoreflect.EnumType {
	return &file_moego_models_auto_message_v1_auto_message_task_enums_proto_enumTypes[2]
}

func (x AutoMessageTaskStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AutoMessageTaskStatus.Descriptor instead.
func (AutoMessageTaskStatus) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_auto_message_v1_auto_message_task_enums_proto_rawDescGZIP(), []int{2}
}

var File_moego_models_auto_message_v1_auto_message_task_enums_proto protoreflect.FileDescriptor

var file_moego_models_auto_message_v1_auto_message_task_enums_proto_rawDesc = []byte{
	0x0a, 0x3a, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x61,
	0x75, 0x74, 0x6f, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2f, 0x76, 0x31, 0x2f, 0x61,
	0x75, 0x74, 0x6f, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x61, 0x73, 0x6b,
	0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1c, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x75, 0x74, 0x6f, 0x5f,
	0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x76, 0x31, 0x2a, 0xcc, 0x01, 0x0a, 0x19, 0x41,
	0x75, 0x74, 0x6f, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x4f, 0x62,
	0x6a, 0x65, 0x63, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x2d, 0x0a, 0x29, 0x41, 0x55, 0x54, 0x4f,
	0x5f, 0x4d, 0x45, 0x53, 0x53, 0x41, 0x47, 0x45, 0x5f, 0x54, 0x41, 0x53, 0x4b, 0x5f, 0x4f, 0x42,
	0x4a, 0x45, 0x43, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43,
	0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x2d, 0x0a, 0x29, 0x41, 0x55, 0x54, 0x4f, 0x5f,
	0x4d, 0x45, 0x53, 0x53, 0x41, 0x47, 0x45, 0x5f, 0x54, 0x41, 0x53, 0x4b, 0x5f, 0x4f, 0x42, 0x4a,
	0x45, 0x43, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x41, 0x50, 0x50, 0x4f, 0x49, 0x4e, 0x54,
	0x4d, 0x45, 0x4e, 0x54, 0x10, 0x01, 0x12, 0x25, 0x0a, 0x21, 0x41, 0x55, 0x54, 0x4f, 0x5f, 0x4d,
	0x45, 0x53, 0x53, 0x41, 0x47, 0x45, 0x5f, 0x54, 0x41, 0x53, 0x4b, 0x5f, 0x4f, 0x42, 0x4a, 0x45,
	0x43, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x50, 0x45, 0x54, 0x10, 0x02, 0x12, 0x2a, 0x0a,
	0x26, 0x41, 0x55, 0x54, 0x4f, 0x5f, 0x4d, 0x45, 0x53, 0x53, 0x41, 0x47, 0x45, 0x5f, 0x54, 0x41,
	0x53, 0x4b, 0x5f, 0x4f, 0x42, 0x4a, 0x45, 0x43, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x43,
	0x55, 0x53, 0x54, 0x4f, 0x4d, 0x45, 0x52, 0x10, 0x03, 0x2a, 0x7c, 0x0a, 0x1b, 0x41, 0x75, 0x74,
	0x6f, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x63, 0x65,
	0x69, 0x76, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x12, 0x2f, 0x0a, 0x2b, 0x41, 0x55, 0x54, 0x4f,
	0x5f, 0x4d, 0x45, 0x53, 0x53, 0x41, 0x47, 0x45, 0x5f, 0x54, 0x41, 0x53, 0x4b, 0x5f, 0x52, 0x45,
	0x43, 0x45, 0x49, 0x56, 0x45, 0x52, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50,
	0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x2c, 0x0a, 0x28, 0x41, 0x55, 0x54,
	0x4f, 0x5f, 0x4d, 0x45, 0x53, 0x53, 0x41, 0x47, 0x45, 0x5f, 0x54, 0x41, 0x53, 0x4b, 0x5f, 0x52,
	0x45, 0x43, 0x45, 0x49, 0x56, 0x45, 0x52, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x43, 0x55, 0x53,
	0x54, 0x4f, 0x4d, 0x45, 0x52, 0x10, 0x01, 0x2a, 0xb8, 0x02, 0x0a, 0x15, 0x41, 0x75, 0x74, 0x6f,
	0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x28, 0x0a, 0x24, 0x41, 0x55, 0x54, 0x4f, 0x5f, 0x4d, 0x45, 0x53, 0x53, 0x41, 0x47,
	0x45, 0x5f, 0x54, 0x41, 0x53, 0x4b, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e,
	0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x21, 0x0a, 0x1d, 0x41,
	0x55, 0x54, 0x4f, 0x5f, 0x4d, 0x45, 0x53, 0x53, 0x41, 0x47, 0x45, 0x5f, 0x54, 0x41, 0x53, 0x4b,
	0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x49, 0x4e, 0x49, 0x54, 0x10, 0x01, 0x12, 0x24,
	0x0a, 0x20, 0x41, 0x55, 0x54, 0x4f, 0x5f, 0x4d, 0x45, 0x53, 0x53, 0x41, 0x47, 0x45, 0x5f, 0x54,
	0x41, 0x53, 0x4b, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x53, 0x45, 0x4e, 0x44, 0x49,
	0x4e, 0x47, 0x10, 0x02, 0x12, 0x24, 0x0a, 0x20, 0x41, 0x55, 0x54, 0x4f, 0x5f, 0x4d, 0x45, 0x53,
	0x53, 0x41, 0x47, 0x45, 0x5f, 0x54, 0x41, 0x53, 0x4b, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53,
	0x5f, 0x53, 0x55, 0x43, 0x43, 0x45, 0x45, 0x44, 0x10, 0x03, 0x12, 0x23, 0x0a, 0x1f, 0x41, 0x55,
	0x54, 0x4f, 0x5f, 0x4d, 0x45, 0x53, 0x53, 0x41, 0x47, 0x45, 0x5f, 0x54, 0x41, 0x53, 0x4b, 0x5f,
	0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x04, 0x12,
	0x2f, 0x0a, 0x2b, 0x41, 0x55, 0x54, 0x4f, 0x5f, 0x4d, 0x45, 0x53, 0x53, 0x41, 0x47, 0x45, 0x5f,
	0x54, 0x41, 0x53, 0x4b, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x43, 0x41, 0x4e, 0x43,
	0x45, 0x4c, 0x4c, 0x45, 0x44, 0x5f, 0x42, 0x59, 0x5f, 0x53, 0x54, 0x41, 0x46, 0x46, 0x10, 0x05,
	0x12, 0x30, 0x0a, 0x2c, 0x41, 0x55, 0x54, 0x4f, 0x5f, 0x4d, 0x45, 0x53, 0x53, 0x41, 0x47, 0x45,
	0x5f, 0x54, 0x41, 0x53, 0x4b, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x43, 0x41, 0x4e,
	0x43, 0x45, 0x4c, 0x4c, 0x45, 0x44, 0x5f, 0x42, 0x59, 0x5f, 0x53, 0x59, 0x53, 0x54, 0x45, 0x4d,
	0x10, 0x06, 0x42, 0x89, 0x01, 0x0a, 0x24, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x75, 0x74, 0x6f,
	0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x5f, 0x67,
	0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c,
	0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69,
	0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74,
	0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2f, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2f, 0x76, 0x31,
	0x3b, 0x61, 0x75, 0x74, 0x6f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x70, 0x62, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_auto_message_v1_auto_message_task_enums_proto_rawDescOnce sync.Once
	file_moego_models_auto_message_v1_auto_message_task_enums_proto_rawDescData = file_moego_models_auto_message_v1_auto_message_task_enums_proto_rawDesc
)

func file_moego_models_auto_message_v1_auto_message_task_enums_proto_rawDescGZIP() []byte {
	file_moego_models_auto_message_v1_auto_message_task_enums_proto_rawDescOnce.Do(func() {
		file_moego_models_auto_message_v1_auto_message_task_enums_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_auto_message_v1_auto_message_task_enums_proto_rawDescData)
	})
	return file_moego_models_auto_message_v1_auto_message_task_enums_proto_rawDescData
}

var file_moego_models_auto_message_v1_auto_message_task_enums_proto_enumTypes = make([]protoimpl.EnumInfo, 3)
var file_moego_models_auto_message_v1_auto_message_task_enums_proto_goTypes = []interface{}{
	(AutoMessageTaskObjectType)(0),   // 0: moego.models.auto_message.v1.AutoMessageTaskObjectType
	(AutoMessageTaskReceiverType)(0), // 1: moego.models.auto_message.v1.AutoMessageTaskReceiverType
	(AutoMessageTaskStatus)(0),       // 2: moego.models.auto_message.v1.AutoMessageTaskStatus
}
var file_moego_models_auto_message_v1_auto_message_task_enums_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_moego_models_auto_message_v1_auto_message_task_enums_proto_init() }
func file_moego_models_auto_message_v1_auto_message_task_enums_proto_init() {
	if File_moego_models_auto_message_v1_auto_message_task_enums_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_auto_message_v1_auto_message_task_enums_proto_rawDesc,
			NumEnums:      3,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_auto_message_v1_auto_message_task_enums_proto_goTypes,
		DependencyIndexes: file_moego_models_auto_message_v1_auto_message_task_enums_proto_depIdxs,
		EnumInfos:         file_moego_models_auto_message_v1_auto_message_task_enums_proto_enumTypes,
	}.Build()
	File_moego_models_auto_message_v1_auto_message_task_enums_proto = out.File
	file_moego_models_auto_message_v1_auto_message_task_enums_proto_rawDesc = nil
	file_moego_models_auto_message_v1_auto_message_task_enums_proto_goTypes = nil
	file_moego_models_auto_message_v1_auto_message_task_enums_proto_depIdxs = nil
}
