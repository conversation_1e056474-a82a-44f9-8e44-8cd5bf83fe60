// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/notification/v1/notification_enums.proto

package notificationpb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// notification type
type NotificationType int32

const (
	// unspecified
	NotificationType_NOTIFICATION_TYPE_UNSPECIFIED NotificationType = 0
	// submit booking request
	NotificationType_NOTIFICATION_TYPE_OB_REQUEST NotificationType = 1
	// appointment assigned
	NotificationType_NOTIFICATION_TYPE_APPT_ASSIGNED NotificationType = 2
	// invoice paid
	NotificationType_NOTIFICATION_TYPE_INVOICE_PAID NotificationType = 3
	// appointment cancelled
	NotificationType_NOTIFICATION_TYPE_APPT_CANCELLED NotificationType = 4
	// appointment rescheduled
	NotificationType_NOTIFICATION_TYPE_APPT_RESCHEDULED NotificationType = 5
	// appointment created
	NotificationType_NOTIFICATION_TYPE_APPT_CREATED NotificationType = 6
	// agreement signed
	NotificationType_NOTIFICATION_TYPE_AGREEMENT_SIGNED NotificationType = 7
	// review booster submitted
	NotificationType_NOTIFICATION_TYPE_REVIEW_SUBMITTED NotificationType = 8
	// new intake form submitted
	NotificationType_NOTIFICATION_TYPE_NEW_INTAKE_FORM NotificationType = 9
	// appointment confirmed by client
	NotificationType_NOTIFICATION_TYPE_APPT_CONFIRMED_BY_CLIENT NotificationType = 10
	// appointment cancelled by client
	NotificationType_NOTIFICATION_TYPE_APPT_CANCELLED_BY_CLIENT NotificationType = 11
	// sub payment fail
	NotificationType_NOTIFICATION_TYPE_SUB_PAYMENT_FAIL NotificationType = 12
	// sub payment success
	NotificationType_NOTIFICATION_TYPE_SUB_PAYMENT_SUCCESS NotificationType = 13
	// a2p fail
	NotificationType_NOTIFICATION_TYPE_A2P_FAIL NotificationType = 14
	// a2p required
	NotificationType_NOTIFICATION_TYPE_A2P_REQUIRED NotificationType = 15
	// ob request rescheduled
	NotificationType_NOTIFICATION_TYPE_OB_REQUEST_RESCHEDULE NotificationType = 16
	// ob request cancelled
	NotificationType_NOTIFICATION_TYPE_OB_REQUEST_CANCEL NotificationType = 17
	// renew online booking end date
	NotificationType_NOTIFICATION_TYPE_RENEW_ONLINE_BOOKING_END_DATE NotificationType = 18
	// ob abandoned record
	NotificationType_NOTIFICATION_TYPE_OB_ABANDONED NotificationType = 19
	// google reserve geo unmatched
	NotificationType_NOTIFICATION_TYPE_GOOGLE_RESERVE_GEO_UNMATCHED NotificationType = 20
	// google reserve geo matched
	NotificationType_NOTIFICATION_TYPE_GOOGLE_RESERVE_GEO_MATCHED NotificationType = 21
	// OB notification: Online booking settings -> notification
	// booking request sent
	NotificationType_NOTIFICATION_TYPE_BOOKING_REQUEST_SENT NotificationType = 1000
	// booking request accepted
	NotificationType_NOTIFICATION_TYPE_BOOKING_REQUEST_ACCEPTED NotificationType = 1001
	// booking request moved to wait list
	NotificationType_NOTIFICATION_TYPE_BOOKING_REQUEST_MOVED_TO_WAIT_LIST NotificationType = 1002
	// booking request declined
	NotificationType_NOTIFICATION_TYPE_BOOKING_REQUEST_DECLINED NotificationType = 1003
	// Schedule online booking waitlist
	NotificationType_NOTIFICATION_TYPE_SCHEDULE_ONLINE_BOOKING_WAITLIST NotificationType = 1004
	// Delete online booking waitlist
	NotificationType_NOTIFICATION_TYPE_DELETE_ONLINE_BOOKING_WAITLIST NotificationType = 1005
	// Appt auto message: Settings -> auto message -> auto message template
	// appointment booked
	NotificationType_NOTIFICATION_TYPE_APPOINTMENT_BOOKED NotificationType = 1100
	// appointment rescheduled
	NotificationType_NOTIFICATION_TYPE_APPOINTMENT_RESCHEDULED NotificationType = 1101
	// appointment cancelled
	NotificationType_NOTIFICATION_TYPE_APPOINTMENT_CANCELLED NotificationType = 1102
	// appointment first reminder
	NotificationType_NOTIFICATION_TYPE_APPOINTMENT_FIRST_REMINDER NotificationType = 1103
	// appointment second reminder
	NotificationType_NOTIFICATION_TYPE_APPOINTMENT_SECOND_REMINDER NotificationType = 1104
	// appointment general reminder
	NotificationType_NOTIFICATION_TYPE_APPOINTMENT_GENERAL_REMINDER NotificationType = 1105
	// appointment rebook reminder
	NotificationType_NOTIFICATION_TYPE_APPOINTMENT_REBOOK_REMINDER NotificationType = 1106
	// C App appointment tracking
	// when appointment is today
	NotificationType_NOTIFICATION_TYPE_APPOINTMENT_DAY NotificationType = 1201
	// when appointment is ready for pick up
	NotificationType_NOTIFICATION_TYPE_READY_TO_PICK_UP NotificationType = 1202
	// when appointment is finished, ask for review
	NotificationType_NOTIFICATION_TYPE_ASK_FOR_REVIEW NotificationType = 1203
	// when app message received
	NotificationType_NOTIFICATION_TYPE_NEW_APP_MESSAGE NotificationType = 1301
	// appointment tracking notification
	// start
	NotificationType_NOTIFICATION_TYPE_TRACKING_START NotificationType = 1401
	// delay slightly from last in transit
	NotificationType_NOTIFICATION_TYPE_APPOINTMENT_DELAY_SLIGHTLY NotificationType = 1402
	// delay seriously from last in transit
	NotificationType_NOTIFICATION_TYPE_APPOINTMENT_DELAY_SERIOUSLY NotificationType = 1403
	// delay slightly when last in transit finish on time
	NotificationType_NOTIFICATION_TYPE_APPOINTMENT_DELAY_SLIGHTLY_WHEN_LAST_TRACKING_FINISH_ON_TIME NotificationType = 1404
	// delay seriously when last in transit finish on time
	NotificationType_NOTIFICATION_TYPE_APPOINTMENT_DELAY_SERIOUSLY_WHEN_LAST_TRACKING_FINISH_ON_TIME NotificationType = 1405
)

// Enum value maps for NotificationType.
var (
	NotificationType_name = map[int32]string{
		0:    "NOTIFICATION_TYPE_UNSPECIFIED",
		1:    "NOTIFICATION_TYPE_OB_REQUEST",
		2:    "NOTIFICATION_TYPE_APPT_ASSIGNED",
		3:    "NOTIFICATION_TYPE_INVOICE_PAID",
		4:    "NOTIFICATION_TYPE_APPT_CANCELLED",
		5:    "NOTIFICATION_TYPE_APPT_RESCHEDULED",
		6:    "NOTIFICATION_TYPE_APPT_CREATED",
		7:    "NOTIFICATION_TYPE_AGREEMENT_SIGNED",
		8:    "NOTIFICATION_TYPE_REVIEW_SUBMITTED",
		9:    "NOTIFICATION_TYPE_NEW_INTAKE_FORM",
		10:   "NOTIFICATION_TYPE_APPT_CONFIRMED_BY_CLIENT",
		11:   "NOTIFICATION_TYPE_APPT_CANCELLED_BY_CLIENT",
		12:   "NOTIFICATION_TYPE_SUB_PAYMENT_FAIL",
		13:   "NOTIFICATION_TYPE_SUB_PAYMENT_SUCCESS",
		14:   "NOTIFICATION_TYPE_A2P_FAIL",
		15:   "NOTIFICATION_TYPE_A2P_REQUIRED",
		16:   "NOTIFICATION_TYPE_OB_REQUEST_RESCHEDULE",
		17:   "NOTIFICATION_TYPE_OB_REQUEST_CANCEL",
		18:   "NOTIFICATION_TYPE_RENEW_ONLINE_BOOKING_END_DATE",
		19:   "NOTIFICATION_TYPE_OB_ABANDONED",
		20:   "NOTIFICATION_TYPE_GOOGLE_RESERVE_GEO_UNMATCHED",
		21:   "NOTIFICATION_TYPE_GOOGLE_RESERVE_GEO_MATCHED",
		1000: "NOTIFICATION_TYPE_BOOKING_REQUEST_SENT",
		1001: "NOTIFICATION_TYPE_BOOKING_REQUEST_ACCEPTED",
		1002: "NOTIFICATION_TYPE_BOOKING_REQUEST_MOVED_TO_WAIT_LIST",
		1003: "NOTIFICATION_TYPE_BOOKING_REQUEST_DECLINED",
		1004: "NOTIFICATION_TYPE_SCHEDULE_ONLINE_BOOKING_WAITLIST",
		1005: "NOTIFICATION_TYPE_DELETE_ONLINE_BOOKING_WAITLIST",
		1100: "NOTIFICATION_TYPE_APPOINTMENT_BOOKED",
		1101: "NOTIFICATION_TYPE_APPOINTMENT_RESCHEDULED",
		1102: "NOTIFICATION_TYPE_APPOINTMENT_CANCELLED",
		1103: "NOTIFICATION_TYPE_APPOINTMENT_FIRST_REMINDER",
		1104: "NOTIFICATION_TYPE_APPOINTMENT_SECOND_REMINDER",
		1105: "NOTIFICATION_TYPE_APPOINTMENT_GENERAL_REMINDER",
		1106: "NOTIFICATION_TYPE_APPOINTMENT_REBOOK_REMINDER",
		1201: "NOTIFICATION_TYPE_APPOINTMENT_DAY",
		1202: "NOTIFICATION_TYPE_READY_TO_PICK_UP",
		1203: "NOTIFICATION_TYPE_ASK_FOR_REVIEW",
		1301: "NOTIFICATION_TYPE_NEW_APP_MESSAGE",
		1401: "NOTIFICATION_TYPE_TRACKING_START",
		1402: "NOTIFICATION_TYPE_APPOINTMENT_DELAY_SLIGHTLY",
		1403: "NOTIFICATION_TYPE_APPOINTMENT_DELAY_SERIOUSLY",
		1404: "NOTIFICATION_TYPE_APPOINTMENT_DELAY_SLIGHTLY_WHEN_LAST_TRACKING_FINISH_ON_TIME",
		1405: "NOTIFICATION_TYPE_APPOINTMENT_DELAY_SERIOUSLY_WHEN_LAST_TRACKING_FINISH_ON_TIME",
	}
	NotificationType_value = map[string]int32{
		"NOTIFICATION_TYPE_UNSPECIFIED":                                                   0,
		"NOTIFICATION_TYPE_OB_REQUEST":                                                    1,
		"NOTIFICATION_TYPE_APPT_ASSIGNED":                                                 2,
		"NOTIFICATION_TYPE_INVOICE_PAID":                                                  3,
		"NOTIFICATION_TYPE_APPT_CANCELLED":                                                4,
		"NOTIFICATION_TYPE_APPT_RESCHEDULED":                                              5,
		"NOTIFICATION_TYPE_APPT_CREATED":                                                  6,
		"NOTIFICATION_TYPE_AGREEMENT_SIGNED":                                              7,
		"NOTIFICATION_TYPE_REVIEW_SUBMITTED":                                              8,
		"NOTIFICATION_TYPE_NEW_INTAKE_FORM":                                               9,
		"NOTIFICATION_TYPE_APPT_CONFIRMED_BY_CLIENT":                                      10,
		"NOTIFICATION_TYPE_APPT_CANCELLED_BY_CLIENT":                                      11,
		"NOTIFICATION_TYPE_SUB_PAYMENT_FAIL":                                              12,
		"NOTIFICATION_TYPE_SUB_PAYMENT_SUCCESS":                                           13,
		"NOTIFICATION_TYPE_A2P_FAIL":                                                      14,
		"NOTIFICATION_TYPE_A2P_REQUIRED":                                                  15,
		"NOTIFICATION_TYPE_OB_REQUEST_RESCHEDULE":                                         16,
		"NOTIFICATION_TYPE_OB_REQUEST_CANCEL":                                             17,
		"NOTIFICATION_TYPE_RENEW_ONLINE_BOOKING_END_DATE":                                 18,
		"NOTIFICATION_TYPE_OB_ABANDONED":                                                  19,
		"NOTIFICATION_TYPE_GOOGLE_RESERVE_GEO_UNMATCHED":                                  20,
		"NOTIFICATION_TYPE_GOOGLE_RESERVE_GEO_MATCHED":                                    21,
		"NOTIFICATION_TYPE_BOOKING_REQUEST_SENT":                                          1000,
		"NOTIFICATION_TYPE_BOOKING_REQUEST_ACCEPTED":                                      1001,
		"NOTIFICATION_TYPE_BOOKING_REQUEST_MOVED_TO_WAIT_LIST":                            1002,
		"NOTIFICATION_TYPE_BOOKING_REQUEST_DECLINED":                                      1003,
		"NOTIFICATION_TYPE_SCHEDULE_ONLINE_BOOKING_WAITLIST":                              1004,
		"NOTIFICATION_TYPE_DELETE_ONLINE_BOOKING_WAITLIST":                                1005,
		"NOTIFICATION_TYPE_APPOINTMENT_BOOKED":                                            1100,
		"NOTIFICATION_TYPE_APPOINTMENT_RESCHEDULED":                                       1101,
		"NOTIFICATION_TYPE_APPOINTMENT_CANCELLED":                                         1102,
		"NOTIFICATION_TYPE_APPOINTMENT_FIRST_REMINDER":                                    1103,
		"NOTIFICATION_TYPE_APPOINTMENT_SECOND_REMINDER":                                   1104,
		"NOTIFICATION_TYPE_APPOINTMENT_GENERAL_REMINDER":                                  1105,
		"NOTIFICATION_TYPE_APPOINTMENT_REBOOK_REMINDER":                                   1106,
		"NOTIFICATION_TYPE_APPOINTMENT_DAY":                                               1201,
		"NOTIFICATION_TYPE_READY_TO_PICK_UP":                                              1202,
		"NOTIFICATION_TYPE_ASK_FOR_REVIEW":                                                1203,
		"NOTIFICATION_TYPE_NEW_APP_MESSAGE":                                               1301,
		"NOTIFICATION_TYPE_TRACKING_START":                                                1401,
		"NOTIFICATION_TYPE_APPOINTMENT_DELAY_SLIGHTLY":                                    1402,
		"NOTIFICATION_TYPE_APPOINTMENT_DELAY_SERIOUSLY":                                   1403,
		"NOTIFICATION_TYPE_APPOINTMENT_DELAY_SLIGHTLY_WHEN_LAST_TRACKING_FINISH_ON_TIME":  1404,
		"NOTIFICATION_TYPE_APPOINTMENT_DELAY_SERIOUSLY_WHEN_LAST_TRACKING_FINISH_ON_TIME": 1405,
	}
)

func (x NotificationType) Enum() *NotificationType {
	p := new(NotificationType)
	*p = x
	return p
}

func (x NotificationType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (NotificationType) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_notification_v1_notification_enums_proto_enumTypes[0].Descriptor()
}

func (NotificationType) Type() protoreflect.EnumType {
	return &file_moego_models_notification_v1_notification_enums_proto_enumTypes[0]
}

func (x NotificationType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use NotificationType.Descriptor instead.
func (NotificationType) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_notification_v1_notification_enums_proto_rawDescGZIP(), []int{0}
}

// notification method
type NotificationMethod int32

const (
	// unspecified
	NotificationMethod_NOTIFICATION_METHOD_UNSPECIFIED NotificationMethod = 0
	// business web
	NotificationMethod_NOTIFICATION_METHOD_BUSINESS_WEB NotificationMethod = 1
	// business app
	NotificationMethod_NOTIFICATION_METHOD_BUSINESS_APP NotificationMethod = 2
	// sms
	NotificationMethod_NOTIFICATION_METHOD_SMS NotificationMethod = 3
	// email
	NotificationMethod_NOTIFICATION_METHOD_EMAIL NotificationMethod = 4
	// pet parent app
	NotificationMethod_NOTIFICATION_METHOD_PET_PARENT_APP NotificationMethod = 5
)

// Enum value maps for NotificationMethod.
var (
	NotificationMethod_name = map[int32]string{
		0: "NOTIFICATION_METHOD_UNSPECIFIED",
		1: "NOTIFICATION_METHOD_BUSINESS_WEB",
		2: "NOTIFICATION_METHOD_BUSINESS_APP",
		3: "NOTIFICATION_METHOD_SMS",
		4: "NOTIFICATION_METHOD_EMAIL",
		5: "NOTIFICATION_METHOD_PET_PARENT_APP",
	}
	NotificationMethod_value = map[string]int32{
		"NOTIFICATION_METHOD_UNSPECIFIED":    0,
		"NOTIFICATION_METHOD_BUSINESS_WEB":   1,
		"NOTIFICATION_METHOD_BUSINESS_APP":   2,
		"NOTIFICATION_METHOD_SMS":            3,
		"NOTIFICATION_METHOD_EMAIL":          4,
		"NOTIFICATION_METHOD_PET_PARENT_APP": 5,
	}
)

func (x NotificationMethod) Enum() *NotificationMethod {
	p := new(NotificationMethod)
	*p = x
	return p
}

func (x NotificationMethod) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (NotificationMethod) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_notification_v1_notification_enums_proto_enumTypes[1].Descriptor()
}

func (NotificationMethod) Type() protoreflect.EnumType {
	return &file_moego_models_notification_v1_notification_enums_proto_enumTypes[1]
}

func (x NotificationMethod) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use NotificationMethod.Descriptor instead.
func (NotificationMethod) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_notification_v1_notification_enums_proto_rawDescGZIP(), []int{1}
}

// notification source
type NotificationSource int32

const (
	// unspecified
	NotificationSource_NOTIFICATION_SOURCE_UNSPECIFIED NotificationSource = 0
	// MoeGo platform
	NotificationSource_NOTIFICATION_SOURCE_PLATFORM NotificationSource = 1
	// client
	NotificationSource_NOTIFICATION_SOURCE_CLIENT NotificationSource = 2
	// business
	NotificationSource_NOTIFICATION_SOURCE_BUSINESS NotificationSource = 3
)

// Enum value maps for NotificationSource.
var (
	NotificationSource_name = map[int32]string{
		0: "NOTIFICATION_SOURCE_UNSPECIFIED",
		1: "NOTIFICATION_SOURCE_PLATFORM",
		2: "NOTIFICATION_SOURCE_CLIENT",
		3: "NOTIFICATION_SOURCE_BUSINESS",
	}
	NotificationSource_value = map[string]int32{
		"NOTIFICATION_SOURCE_UNSPECIFIED": 0,
		"NOTIFICATION_SOURCE_PLATFORM":    1,
		"NOTIFICATION_SOURCE_CLIENT":      2,
		"NOTIFICATION_SOURCE_BUSINESS":    3,
	}
)

func (x NotificationSource) Enum() *NotificationSource {
	p := new(NotificationSource)
	*p = x
	return p
}

func (x NotificationSource) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (NotificationSource) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_notification_v1_notification_enums_proto_enumTypes[2].Descriptor()
}

func (NotificationSource) Type() protoreflect.EnumType {
	return &file_moego_models_notification_v1_notification_enums_proto_enumTypes[2]
}

func (x NotificationSource) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use NotificationSource.Descriptor instead.
func (NotificationSource) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_notification_v1_notification_enums_proto_rawDescGZIP(), []int{2}
}

// notification list sort field
type NotificationSortField int32

const (
	// unspecified
	NotificationSortField_NOTIFICATION_SORT_FIELD_UNSPECIFIED NotificationSortField = 0
	// sent at
	NotificationSortField_NOTIFICATION_SORT_FIELD_SENT_AT NotificationSortField = 1
	// read at
	NotificationSortField_NOTIFICATION_SORT_FIELD_READ_AT NotificationSortField = 2
)

// Enum value maps for NotificationSortField.
var (
	NotificationSortField_name = map[int32]string{
		0: "NOTIFICATION_SORT_FIELD_UNSPECIFIED",
		1: "NOTIFICATION_SORT_FIELD_SENT_AT",
		2: "NOTIFICATION_SORT_FIELD_READ_AT",
	}
	NotificationSortField_value = map[string]int32{
		"NOTIFICATION_SORT_FIELD_UNSPECIFIED": 0,
		"NOTIFICATION_SORT_FIELD_SENT_AT":     1,
		"NOTIFICATION_SORT_FIELD_READ_AT":     2,
	}
)

func (x NotificationSortField) Enum() *NotificationSortField {
	p := new(NotificationSortField)
	*p = x
	return p
}

func (x NotificationSortField) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (NotificationSortField) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_notification_v1_notification_enums_proto_enumTypes[3].Descriptor()
}

func (NotificationSortField) Type() protoreflect.EnumType {
	return &file_moego_models_notification_v1_notification_enums_proto_enumTypes[3]
}

func (x NotificationSortField) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use NotificationSortField.Descriptor instead.
func (NotificationSortField) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_notification_v1_notification_enums_proto_rawDescGZIP(), []int{3}
}

// app push device type
type DeviceType int32

const (
	// unspecified
	DeviceType_DEVICE_TYPE_UNSPECIFIED DeviceType = 0
	// ios
	DeviceType_DEVICE_TYPE_IOS DeviceType = 1
	// android
	DeviceType_DEVICE_TYPE_ANDROID DeviceType = 2
)

// Enum value maps for DeviceType.
var (
	DeviceType_name = map[int32]string{
		0: "DEVICE_TYPE_UNSPECIFIED",
		1: "DEVICE_TYPE_IOS",
		2: "DEVICE_TYPE_ANDROID",
	}
	DeviceType_value = map[string]int32{
		"DEVICE_TYPE_UNSPECIFIED": 0,
		"DEVICE_TYPE_IOS":         1,
		"DEVICE_TYPE_ANDROID":     2,
	}
)

func (x DeviceType) Enum() *DeviceType {
	p := new(DeviceType)
	*p = x
	return p
}

func (x DeviceType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DeviceType) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_notification_v1_notification_enums_proto_enumTypes[4].Descriptor()
}

func (DeviceType) Type() protoreflect.EnumType {
	return &file_moego_models_notification_v1_notification_enums_proto_enumTypes[4]
}

func (x DeviceType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DeviceType.Descriptor instead.
func (DeviceType) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_notification_v1_notification_enums_proto_rawDescGZIP(), []int{4}
}

// app push token source
type PushTokenSource int32

const (
	// unspecified
	PushTokenSource_PUSH_TOKEN_SOURCE_UNSPECIFIED PushTokenSource = 0
	// business
	PushTokenSource_PUSH_TOKEN_SOURCE_BUSINESS PushTokenSource = 1
	// client
	PushTokenSource_PUSH_TOKEN_SOURCE_CLIENT PushTokenSource = 2
)

// Enum value maps for PushTokenSource.
var (
	PushTokenSource_name = map[int32]string{
		0: "PUSH_TOKEN_SOURCE_UNSPECIFIED",
		1: "PUSH_TOKEN_SOURCE_BUSINESS",
		2: "PUSH_TOKEN_SOURCE_CLIENT",
	}
	PushTokenSource_value = map[string]int32{
		"PUSH_TOKEN_SOURCE_UNSPECIFIED": 0,
		"PUSH_TOKEN_SOURCE_BUSINESS":    1,
		"PUSH_TOKEN_SOURCE_CLIENT":      2,
	}
)

func (x PushTokenSource) Enum() *PushTokenSource {
	p := new(PushTokenSource)
	*p = x
	return p
}

func (x PushTokenSource) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PushTokenSource) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_notification_v1_notification_enums_proto_enumTypes[5].Descriptor()
}

func (PushTokenSource) Type() protoreflect.EnumType {
	return &file_moego_models_notification_v1_notification_enums_proto_enumTypes[5]
}

func (x PushTokenSource) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PushTokenSource.Descriptor instead.
func (PushTokenSource) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_notification_v1_notification_enums_proto_rawDescGZIP(), []int{5}
}

var File_moego_models_notification_v1_notification_enums_proto protoreflect.FileDescriptor

var file_moego_models_notification_v1_notification_enums_proto_rawDesc = []byte{
	0x0a, 0x35, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6e,
	0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x6e,
	0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x65, 0x6e, 0x75, 0x6d,
	0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1c, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2a, 0x81, 0x10, 0x0a, 0x10, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x21, 0x0a, 0x1d, 0x4e, 0x4f,
	0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f,
	0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x20, 0x0a,
	0x1c, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59,
	0x50, 0x45, 0x5f, 0x4f, 0x42, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x10, 0x01, 0x12,
	0x23, 0x0a, 0x1f, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f,
	0x54, 0x59, 0x50, 0x45, 0x5f, 0x41, 0x50, 0x50, 0x54, 0x5f, 0x41, 0x53, 0x53, 0x49, 0x47, 0x4e,
	0x45, 0x44, 0x10, 0x02, 0x12, 0x22, 0x0a, 0x1e, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49, 0x43, 0x41,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x49, 0x4e, 0x56, 0x4f, 0x49, 0x43,
	0x45, 0x5f, 0x50, 0x41, 0x49, 0x44, 0x10, 0x03, 0x12, 0x24, 0x0a, 0x20, 0x4e, 0x4f, 0x54, 0x49,
	0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x41, 0x50,
	0x50, 0x54, 0x5f, 0x43, 0x41, 0x4e, 0x43, 0x45, 0x4c, 0x4c, 0x45, 0x44, 0x10, 0x04, 0x12, 0x26,
	0x0a, 0x22, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54,
	0x59, 0x50, 0x45, 0x5f, 0x41, 0x50, 0x50, 0x54, 0x5f, 0x52, 0x45, 0x53, 0x43, 0x48, 0x45, 0x44,
	0x55, 0x4c, 0x45, 0x44, 0x10, 0x05, 0x12, 0x22, 0x0a, 0x1e, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49,
	0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x41, 0x50, 0x50, 0x54,
	0x5f, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x44, 0x10, 0x06, 0x12, 0x26, 0x0a, 0x22, 0x4e, 0x4f,
	0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f,
	0x41, 0x47, 0x52, 0x45, 0x45, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x53, 0x49, 0x47, 0x4e, 0x45, 0x44,
	0x10, 0x07, 0x12, 0x26, 0x0a, 0x22, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49,
	0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x52, 0x45, 0x56, 0x49, 0x45, 0x57, 0x5f, 0x53,
	0x55, 0x42, 0x4d, 0x49, 0x54, 0x54, 0x45, 0x44, 0x10, 0x08, 0x12, 0x25, 0x0a, 0x21, 0x4e, 0x4f,
	0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f,
	0x4e, 0x45, 0x57, 0x5f, 0x49, 0x4e, 0x54, 0x41, 0x4b, 0x45, 0x5f, 0x46, 0x4f, 0x52, 0x4d, 0x10,
	0x09, 0x12, 0x2e, 0x0a, 0x2a, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f,
	0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x41, 0x50, 0x50, 0x54, 0x5f, 0x43, 0x4f, 0x4e, 0x46,
	0x49, 0x52, 0x4d, 0x45, 0x44, 0x5f, 0x42, 0x59, 0x5f, 0x43, 0x4c, 0x49, 0x45, 0x4e, 0x54, 0x10,
	0x0a, 0x12, 0x2e, 0x0a, 0x2a, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f,
	0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x41, 0x50, 0x50, 0x54, 0x5f, 0x43, 0x41, 0x4e, 0x43,
	0x45, 0x4c, 0x4c, 0x45, 0x44, 0x5f, 0x42, 0x59, 0x5f, 0x43, 0x4c, 0x49, 0x45, 0x4e, 0x54, 0x10,
	0x0b, 0x12, 0x26, 0x0a, 0x22, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f,
	0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x50, 0x41, 0x59, 0x4d, 0x45,
	0x4e, 0x54, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x10, 0x0c, 0x12, 0x29, 0x0a, 0x25, 0x4e, 0x4f, 0x54,
	0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x53,
	0x55, 0x42, 0x5f, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x53, 0x55, 0x43, 0x43, 0x45,
	0x53, 0x53, 0x10, 0x0d, 0x12, 0x1e, 0x0a, 0x1a, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49, 0x43, 0x41,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x41, 0x32, 0x50, 0x5f, 0x46, 0x41,
	0x49, 0x4c, 0x10, 0x0e, 0x12, 0x22, 0x0a, 0x1e, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49, 0x43, 0x41,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x41, 0x32, 0x50, 0x5f, 0x52, 0x45,
	0x51, 0x55, 0x49, 0x52, 0x45, 0x44, 0x10, 0x0f, 0x12, 0x2b, 0x0a, 0x27, 0x4e, 0x4f, 0x54, 0x49,
	0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4f, 0x42,
	0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x52, 0x45, 0x53, 0x43, 0x48, 0x45, 0x44,
	0x55, 0x4c, 0x45, 0x10, 0x10, 0x12, 0x27, 0x0a, 0x23, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49, 0x43,
	0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4f, 0x42, 0x5f, 0x52, 0x45,
	0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x43, 0x41, 0x4e, 0x43, 0x45, 0x4c, 0x10, 0x11, 0x12, 0x33,
	0x0a, 0x2f, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54,
	0x59, 0x50, 0x45, 0x5f, 0x52, 0x45, 0x4e, 0x45, 0x57, 0x5f, 0x4f, 0x4e, 0x4c, 0x49, 0x4e, 0x45,
	0x5f, 0x42, 0x4f, 0x4f, 0x4b, 0x49, 0x4e, 0x47, 0x5f, 0x45, 0x4e, 0x44, 0x5f, 0x44, 0x41, 0x54,
	0x45, 0x10, 0x12, 0x12, 0x22, 0x0a, 0x1e, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54,
	0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4f, 0x42, 0x5f, 0x41, 0x42, 0x41, 0x4e,
	0x44, 0x4f, 0x4e, 0x45, 0x44, 0x10, 0x13, 0x12, 0x32, 0x0a, 0x2e, 0x4e, 0x4f, 0x54, 0x49, 0x46,
	0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x47, 0x4f, 0x4f,
	0x47, 0x4c, 0x45, 0x5f, 0x52, 0x45, 0x53, 0x45, 0x52, 0x56, 0x45, 0x5f, 0x47, 0x45, 0x4f, 0x5f,
	0x55, 0x4e, 0x4d, 0x41, 0x54, 0x43, 0x48, 0x45, 0x44, 0x10, 0x14, 0x12, 0x30, 0x0a, 0x2c, 0x4e,
	0x4f, 0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x5f, 0x47, 0x4f, 0x4f, 0x47, 0x4c, 0x45, 0x5f, 0x52, 0x45, 0x53, 0x45, 0x52, 0x56, 0x45, 0x5f,
	0x47, 0x45, 0x4f, 0x5f, 0x4d, 0x41, 0x54, 0x43, 0x48, 0x45, 0x44, 0x10, 0x15, 0x12, 0x2b, 0x0a,
	0x26, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59,
	0x50, 0x45, 0x5f, 0x42, 0x4f, 0x4f, 0x4b, 0x49, 0x4e, 0x47, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45,
	0x53, 0x54, 0x5f, 0x53, 0x45, 0x4e, 0x54, 0x10, 0xe8, 0x07, 0x12, 0x2f, 0x0a, 0x2a, 0x4e, 0x4f,
	0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f,
	0x42, 0x4f, 0x4f, 0x4b, 0x49, 0x4e, 0x47, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f,
	0x41, 0x43, 0x43, 0x45, 0x50, 0x54, 0x45, 0x44, 0x10, 0xe9, 0x07, 0x12, 0x39, 0x0a, 0x34, 0x4e,
	0x4f, 0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x5f, 0x42, 0x4f, 0x4f, 0x4b, 0x49, 0x4e, 0x47, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54,
	0x5f, 0x4d, 0x4f, 0x56, 0x45, 0x44, 0x5f, 0x54, 0x4f, 0x5f, 0x57, 0x41, 0x49, 0x54, 0x5f, 0x4c,
	0x49, 0x53, 0x54, 0x10, 0xea, 0x07, 0x12, 0x2f, 0x0a, 0x2a, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49,
	0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x42, 0x4f, 0x4f, 0x4b,
	0x49, 0x4e, 0x47, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x44, 0x45, 0x43, 0x4c,
	0x49, 0x4e, 0x45, 0x44, 0x10, 0xeb, 0x07, 0x12, 0x37, 0x0a, 0x32, 0x4e, 0x4f, 0x54, 0x49, 0x46,
	0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x53, 0x43, 0x48,
	0x45, 0x44, 0x55, 0x4c, 0x45, 0x5f, 0x4f, 0x4e, 0x4c, 0x49, 0x4e, 0x45, 0x5f, 0x42, 0x4f, 0x4f,
	0x4b, 0x49, 0x4e, 0x47, 0x5f, 0x57, 0x41, 0x49, 0x54, 0x4c, 0x49, 0x53, 0x54, 0x10, 0xec, 0x07,
	0x12, 0x35, 0x0a, 0x30, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x44, 0x45, 0x4c, 0x45, 0x54, 0x45, 0x5f, 0x4f, 0x4e, 0x4c,
	0x49, 0x4e, 0x45, 0x5f, 0x42, 0x4f, 0x4f, 0x4b, 0x49, 0x4e, 0x47, 0x5f, 0x57, 0x41, 0x49, 0x54,
	0x4c, 0x49, 0x53, 0x54, 0x10, 0xed, 0x07, 0x12, 0x29, 0x0a, 0x24, 0x4e, 0x4f, 0x54, 0x49, 0x46,
	0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x41, 0x50, 0x50,
	0x4f, 0x49, 0x4e, 0x54, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x42, 0x4f, 0x4f, 0x4b, 0x45, 0x44, 0x10,
	0xcc, 0x08, 0x12, 0x2e, 0x0a, 0x29, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49,
	0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x41, 0x50, 0x50, 0x4f, 0x49, 0x4e, 0x54, 0x4d,
	0x45, 0x4e, 0x54, 0x5f, 0x52, 0x45, 0x53, 0x43, 0x48, 0x45, 0x44, 0x55, 0x4c, 0x45, 0x44, 0x10,
	0xcd, 0x08, 0x12, 0x2c, 0x0a, 0x27, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49,
	0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x41, 0x50, 0x50, 0x4f, 0x49, 0x4e, 0x54, 0x4d,
	0x45, 0x4e, 0x54, 0x5f, 0x43, 0x41, 0x4e, 0x43, 0x45, 0x4c, 0x4c, 0x45, 0x44, 0x10, 0xce, 0x08,
	0x12, 0x31, 0x0a, 0x2c, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x41, 0x50, 0x50, 0x4f, 0x49, 0x4e, 0x54, 0x4d, 0x45, 0x4e,
	0x54, 0x5f, 0x46, 0x49, 0x52, 0x53, 0x54, 0x5f, 0x52, 0x45, 0x4d, 0x49, 0x4e, 0x44, 0x45, 0x52,
	0x10, 0xcf, 0x08, 0x12, 0x32, 0x0a, 0x2d, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54,
	0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x41, 0x50, 0x50, 0x4f, 0x49, 0x4e, 0x54,
	0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x53, 0x45, 0x43, 0x4f, 0x4e, 0x44, 0x5f, 0x52, 0x45, 0x4d, 0x49,
	0x4e, 0x44, 0x45, 0x52, 0x10, 0xd0, 0x08, 0x12, 0x33, 0x0a, 0x2e, 0x4e, 0x4f, 0x54, 0x49, 0x46,
	0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x41, 0x50, 0x50,
	0x4f, 0x49, 0x4e, 0x54, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x47, 0x45, 0x4e, 0x45, 0x52, 0x41, 0x4c,
	0x5f, 0x52, 0x45, 0x4d, 0x49, 0x4e, 0x44, 0x45, 0x52, 0x10, 0xd1, 0x08, 0x12, 0x32, 0x0a, 0x2d,
	0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50,
	0x45, 0x5f, 0x41, 0x50, 0x50, 0x4f, 0x49, 0x4e, 0x54, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x52, 0x45,
	0x42, 0x4f, 0x4f, 0x4b, 0x5f, 0x52, 0x45, 0x4d, 0x49, 0x4e, 0x44, 0x45, 0x52, 0x10, 0xd2, 0x08,
	0x12, 0x26, 0x0a, 0x21, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x41, 0x50, 0x50, 0x4f, 0x49, 0x4e, 0x54, 0x4d, 0x45, 0x4e,
	0x54, 0x5f, 0x44, 0x41, 0x59, 0x10, 0xb1, 0x09, 0x12, 0x27, 0x0a, 0x22, 0x4e, 0x4f, 0x54, 0x49,
	0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x52, 0x45,
	0x41, 0x44, 0x59, 0x5f, 0x54, 0x4f, 0x5f, 0x50, 0x49, 0x43, 0x4b, 0x5f, 0x55, 0x50, 0x10, 0xb2,
	0x09, 0x12, 0x25, 0x0a, 0x20, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f,
	0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x41, 0x53, 0x4b, 0x5f, 0x46, 0x4f, 0x52, 0x5f, 0x52,
	0x45, 0x56, 0x49, 0x45, 0x57, 0x10, 0xb3, 0x09, 0x12, 0x26, 0x0a, 0x21, 0x4e, 0x4f, 0x54, 0x49,
	0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4e, 0x45,
	0x57, 0x5f, 0x41, 0x50, 0x50, 0x5f, 0x4d, 0x45, 0x53, 0x53, 0x41, 0x47, 0x45, 0x10, 0x95, 0x0a,
	0x12, 0x25, 0x0a, 0x20, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x54, 0x52, 0x41, 0x43, 0x4b, 0x49, 0x4e, 0x47, 0x5f, 0x53,
	0x54, 0x41, 0x52, 0x54, 0x10, 0xf9, 0x0a, 0x12, 0x31, 0x0a, 0x2c, 0x4e, 0x4f, 0x54, 0x49, 0x46,
	0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x41, 0x50, 0x50,
	0x4f, 0x49, 0x4e, 0x54, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x44, 0x45, 0x4c, 0x41, 0x59, 0x5f, 0x53,
	0x4c, 0x49, 0x47, 0x48, 0x54, 0x4c, 0x59, 0x10, 0xfa, 0x0a, 0x12, 0x32, 0x0a, 0x2d, 0x4e, 0x4f,
	0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f,
	0x41, 0x50, 0x50, 0x4f, 0x49, 0x4e, 0x54, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x44, 0x45, 0x4c, 0x41,
	0x59, 0x5f, 0x53, 0x45, 0x52, 0x49, 0x4f, 0x55, 0x53, 0x4c, 0x59, 0x10, 0xfb, 0x0a, 0x12, 0x53,
	0x0a, 0x4e, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54,
	0x59, 0x50, 0x45, 0x5f, 0x41, 0x50, 0x50, 0x4f, 0x49, 0x4e, 0x54, 0x4d, 0x45, 0x4e, 0x54, 0x5f,
	0x44, 0x45, 0x4c, 0x41, 0x59, 0x5f, 0x53, 0x4c, 0x49, 0x47, 0x48, 0x54, 0x4c, 0x59, 0x5f, 0x57,
	0x48, 0x45, 0x4e, 0x5f, 0x4c, 0x41, 0x53, 0x54, 0x5f, 0x54, 0x52, 0x41, 0x43, 0x4b, 0x49, 0x4e,
	0x47, 0x5f, 0x46, 0x49, 0x4e, 0x49, 0x53, 0x48, 0x5f, 0x4f, 0x4e, 0x5f, 0x54, 0x49, 0x4d, 0x45,
	0x10, 0xfc, 0x0a, 0x12, 0x54, 0x0a, 0x4f, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54,
	0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x41, 0x50, 0x50, 0x4f, 0x49, 0x4e, 0x54,
	0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x44, 0x45, 0x4c, 0x41, 0x59, 0x5f, 0x53, 0x45, 0x52, 0x49, 0x4f,
	0x55, 0x53, 0x4c, 0x59, 0x5f, 0x57, 0x48, 0x45, 0x4e, 0x5f, 0x4c, 0x41, 0x53, 0x54, 0x5f, 0x54,
	0x52, 0x41, 0x43, 0x4b, 0x49, 0x4e, 0x47, 0x5f, 0x46, 0x49, 0x4e, 0x49, 0x53, 0x48, 0x5f, 0x4f,
	0x4e, 0x5f, 0x54, 0x49, 0x4d, 0x45, 0x10, 0xfd, 0x0a, 0x2a, 0xe9, 0x01, 0x0a, 0x12, 0x4e, 0x6f,
	0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64,
	0x12, 0x23, 0x0a, 0x1f, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e,
	0x5f, 0x4d, 0x45, 0x54, 0x48, 0x4f, 0x44, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46,
	0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x24, 0x0a, 0x20, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49, 0x43,
	0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x4d, 0x45, 0x54, 0x48, 0x4f, 0x44, 0x5f, 0x42, 0x55, 0x53,
	0x49, 0x4e, 0x45, 0x53, 0x53, 0x5f, 0x57, 0x45, 0x42, 0x10, 0x01, 0x12, 0x24, 0x0a, 0x20, 0x4e,
	0x4f, 0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x4d, 0x45, 0x54, 0x48,
	0x4f, 0x44, 0x5f, 0x42, 0x55, 0x53, 0x49, 0x4e, 0x45, 0x53, 0x53, 0x5f, 0x41, 0x50, 0x50, 0x10,
	0x02, 0x12, 0x1b, 0x0a, 0x17, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f,
	0x4e, 0x5f, 0x4d, 0x45, 0x54, 0x48, 0x4f, 0x44, 0x5f, 0x53, 0x4d, 0x53, 0x10, 0x03, 0x12, 0x1d,
	0x0a, 0x19, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x4d,
	0x45, 0x54, 0x48, 0x4f, 0x44, 0x5f, 0x45, 0x4d, 0x41, 0x49, 0x4c, 0x10, 0x04, 0x12, 0x26, 0x0a,
	0x22, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x4d, 0x45,
	0x54, 0x48, 0x4f, 0x44, 0x5f, 0x50, 0x45, 0x54, 0x5f, 0x50, 0x41, 0x52, 0x45, 0x4e, 0x54, 0x5f,
	0x41, 0x50, 0x50, 0x10, 0x05, 0x2a, 0x9d, 0x01, 0x0a, 0x12, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x23, 0x0a, 0x1f,
	0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x4f, 0x55,
	0x52, 0x43, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10,
	0x00, 0x12, 0x20, 0x0a, 0x1c, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f,
	0x4e, 0x5f, 0x53, 0x4f, 0x55, 0x52, 0x43, 0x45, 0x5f, 0x50, 0x4c, 0x41, 0x54, 0x46, 0x4f, 0x52,
	0x4d, 0x10, 0x01, 0x12, 0x1e, 0x0a, 0x1a, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54,
	0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x4f, 0x55, 0x52, 0x43, 0x45, 0x5f, 0x43, 0x4c, 0x49, 0x45, 0x4e,
	0x54, 0x10, 0x02, 0x12, 0x20, 0x0a, 0x1c, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54,
	0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x4f, 0x55, 0x52, 0x43, 0x45, 0x5f, 0x42, 0x55, 0x53, 0x49, 0x4e,
	0x45, 0x53, 0x53, 0x10, 0x03, 0x2a, 0x8a, 0x01, 0x0a, 0x15, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x6f, 0x72, 0x74, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x12,
	0x27, 0x0a, 0x23, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f,
	0x53, 0x4f, 0x52, 0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45,
	0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x23, 0x0a, 0x1f, 0x4e, 0x4f, 0x54, 0x49,
	0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x4f, 0x52, 0x54, 0x5f, 0x46, 0x49,
	0x45, 0x4c, 0x44, 0x5f, 0x53, 0x45, 0x4e, 0x54, 0x5f, 0x41, 0x54, 0x10, 0x01, 0x12, 0x23, 0x0a,
	0x1f, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x4f,
	0x52, 0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x52, 0x45, 0x41, 0x44, 0x5f, 0x41, 0x54,
	0x10, 0x02, 0x2a, 0x57, 0x0a, 0x0a, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x1b, 0x0a, 0x17, 0x44, 0x45, 0x56, 0x49, 0x43, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f,
	0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x13, 0x0a,
	0x0f, 0x44, 0x45, 0x56, 0x49, 0x43, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x49, 0x4f, 0x53,
	0x10, 0x01, 0x12, 0x17, 0x0a, 0x13, 0x44, 0x45, 0x56, 0x49, 0x43, 0x45, 0x5f, 0x54, 0x59, 0x50,
	0x45, 0x5f, 0x41, 0x4e, 0x44, 0x52, 0x4f, 0x49, 0x44, 0x10, 0x02, 0x2a, 0x72, 0x0a, 0x0f, 0x50,
	0x75, 0x73, 0x68, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x21,
	0x0a, 0x1d, 0x50, 0x55, 0x53, 0x48, 0x5f, 0x54, 0x4f, 0x4b, 0x45, 0x4e, 0x5f, 0x53, 0x4f, 0x55,
	0x52, 0x43, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10,
	0x00, 0x12, 0x1e, 0x0a, 0x1a, 0x50, 0x55, 0x53, 0x48, 0x5f, 0x54, 0x4f, 0x4b, 0x45, 0x4e, 0x5f,
	0x53, 0x4f, 0x55, 0x52, 0x43, 0x45, 0x5f, 0x42, 0x55, 0x53, 0x49, 0x4e, 0x45, 0x53, 0x53, 0x10,
	0x01, 0x12, 0x1c, 0x0a, 0x18, 0x50, 0x55, 0x53, 0x48, 0x5f, 0x54, 0x4f, 0x4b, 0x45, 0x4e, 0x5f,
	0x53, 0x4f, 0x55, 0x52, 0x43, 0x45, 0x5f, 0x43, 0x4c, 0x49, 0x45, 0x4e, 0x54, 0x10, 0x02, 0x42,
	0x8a, 0x01, 0x0a, 0x24, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64,
	0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x60, 0x67, 0x69, 0x74, 0x68,
	0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72,
	0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65,
	0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f,
	0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6e, 0x6f,
	0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x3b, 0x6e, 0x6f,
	0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_notification_v1_notification_enums_proto_rawDescOnce sync.Once
	file_moego_models_notification_v1_notification_enums_proto_rawDescData = file_moego_models_notification_v1_notification_enums_proto_rawDesc
)

func file_moego_models_notification_v1_notification_enums_proto_rawDescGZIP() []byte {
	file_moego_models_notification_v1_notification_enums_proto_rawDescOnce.Do(func() {
		file_moego_models_notification_v1_notification_enums_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_notification_v1_notification_enums_proto_rawDescData)
	})
	return file_moego_models_notification_v1_notification_enums_proto_rawDescData
}

var file_moego_models_notification_v1_notification_enums_proto_enumTypes = make([]protoimpl.EnumInfo, 6)
var file_moego_models_notification_v1_notification_enums_proto_goTypes = []interface{}{
	(NotificationType)(0),      // 0: moego.models.notification.v1.NotificationType
	(NotificationMethod)(0),    // 1: moego.models.notification.v1.NotificationMethod
	(NotificationSource)(0),    // 2: moego.models.notification.v1.NotificationSource
	(NotificationSortField)(0), // 3: moego.models.notification.v1.NotificationSortField
	(DeviceType)(0),            // 4: moego.models.notification.v1.DeviceType
	(PushTokenSource)(0),       // 5: moego.models.notification.v1.PushTokenSource
}
var file_moego_models_notification_v1_notification_enums_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_moego_models_notification_v1_notification_enums_proto_init() }
func file_moego_models_notification_v1_notification_enums_proto_init() {
	if File_moego_models_notification_v1_notification_enums_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_notification_v1_notification_enums_proto_rawDesc,
			NumEnums:      6,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_notification_v1_notification_enums_proto_goTypes,
		DependencyIndexes: file_moego_models_notification_v1_notification_enums_proto_depIdxs,
		EnumInfos:         file_moego_models_notification_v1_notification_enums_proto_enumTypes,
	}.Build()
	File_moego_models_notification_v1_notification_enums_proto = out.File
	file_moego_models_notification_v1_notification_enums_proto_rawDesc = nil
	file_moego_models_notification_v1_notification_enums_proto_goTypes = nil
	file_moego_models_notification_v1_notification_enums_proto_depIdxs = nil
}
