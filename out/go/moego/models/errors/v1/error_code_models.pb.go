// @since 2022-06-24 15:44:42
// <AUTHOR> <<EMAIL>>

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/errors/v1/error_code_models.proto

package errorspb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// the error codes
// NOTE: not allow alias
type Code int32

const (
	// reserved
	Code_CODE_UNSPECIFIED Code = 0
	// success
	Code_CODE_SUCCESS Code = 200
	// params error
	Code_CODE_PARAMS_ERROR Code = 400
	// unauthorized, means need to login again
	Code_CODE_UNAUTHORIZED_ERROR Code = 401
	// no permission to operate
	Code_CODE_FORBIDDEN Code = 403
	// rate limit quota exceeded
	Code_CODE_TOO_MANY_REQUESTS Code = 429
	// server internal error
	Code_CODE_SERVER_ERROR Code = 500
	// moego-boarding error code start 10000 end 20000
	// boarding is not used so far , so this is also used as Common Error Code
	// ORDERID_EXISTS
	Code_CODE_ORDERID_EXISTS Code = 10010
	// ROOM_ERROR
	Code_CODE_ROOM_ERROR Code = 10015
	// PRIMARY_ID_EMPTY
	Code_CODE_PRIMARY_ID_EMPTY Code = 10016
	// VERIFY_CODE_NOT_EQUAL
	Code_CODE_VERIFY_CODE_NOT_EQUAL Code = 10017
	// PARALLEL_ERROR
	Code_CODE_PARALLEL_ERROR Code = 10018
	// server forced branch mismatch
	Code_CODE_BRANCH_MISMATCH Code = 10999
	// assign item amount error
	Code_CODE_ASSIGN_ITEM_AMOUNT_ONCE_ONLY_ERROR Code = 11001
	// asign item amount data permission error
	Code_CODE_ASSIGN_ITEM_AMOUNT_DATA_PERMISSION_ERROR Code = 11002
	// moego-business error code start 20000 end 30000
	// CHECK_IN_ERROR
	Code_CODE_CHECK_IN_ERROR Code = 20001
	// CHECK_OUT_ERROR
	Code_CODE_CHECK_OUT_ERROR Code = 20002
	// PASSWORD_ERROR
	Code_CODE_PASSWORD_ERROR Code = 20003
	// ACCOUNT_NOT_FOUND
	Code_CODE_ACCOUNT_NOT_FOUND Code = 20004
	// INVALID_CODE
	Code_CODE_INVALID_CODE Code = 20005
	// STAFF_BINDING_ERROR1
	Code_CODE_STAFF_BINDING_ERROR1 Code = 20006
	// STAFF_BINDING_ERROR2
	Code_CODE_STAFF_BINDING_ERROR2 Code = 20007
	// STAFF_BINDING_ERROR3
	Code_CODE_STAFF_BINDING_ERROR3 Code = 20008
	// STAFF_BINDING_ERROR4
	Code_CODE_STAFF_BINDING_ERROR4 Code = 20009
	// DELETE_ROLE_ERROR
	Code_CODE_DELETE_ROLE_ERROR Code = 20010
	// PAYMENT_METHOD_NAME_EXISTS
	Code_CODE_PAYMENT_METHOD_NAME_EXISTS Code = 20011
	// DEFAULT_METHOD_OPER_FALSE
	Code_CODE_DEFAULT_METHOD_OPER_FALSE Code = 20012
	// FILE_SIZE_EXCEEDS_LIMIT
	Code_CODE_FILE_SIZE_EXCEEDS_LIMIT Code = 20013
	// TAX_IS_USED
	Code_CODE_TAX_IS_USED Code = 20014
	// TAG_NAME_EXIST
	Code_CODE_TAG_NAME_EXIST Code = 20015
	// TAG_NAME_NULL
	Code_CODE_TAG_NAME_NULL Code = 20016
	// THE_LAST_DELETED
	Code_CODE_THE_LAST_DELETED Code = 20017
	// THE_LAST_INACTIVE
	Code_CODE_THE_LAST_INACTIVE Code = 20018
	// PERMISSION_NOT_ENOUGH
	Code_CODE_PERMISSION_NOT_ENOUGH Code = 20019
	// NO_PERMISSION_SHOW_MESSAGE_THREAD
	Code_CODE_NO_PERMISSION_SHOW_MESSAGE_THREAD Code = 20020
	// TAX_IS_USED_RETAIL
	Code_CODE_TAX_IS_USED_RETAIL Code = 20021
	// NOT_ENOUGH_VANS_NUM
	Code_CODE_NOT_ENOUGH_VANS_NUM Code = 20022
	// PASSWORD_UPDATE_OLDPWD_ERROR
	Code_CODE_PASSWORD_UPDATE_OLDPWD_ERROR Code = 20023
	// ACCOUNT_NOT_OWN_THIS_COMPANY
	Code_CODE_ACCOUNT_NOT_OWN_THIS_COMPANY Code = 20024
	// CHECK_EMAIL_EXIST_PHP_VERSION
	Code_CODE_CHECK_EMAIL_EXIST_PHP_VERSION Code = 20025
	// COMPANY_NOT_FOUND
	Code_CODE_COMPANY_NOT_FOUND Code = 20026
	// SWITCH_BUSINESS_ERROR
	Code_CODE_SWITCH_BUSINESS_ERROR Code = 20027
	// STAFF_ONLY_BANDING_ONE_VAN(20028, "Staff can only be bound to one van, please refresh and try again.")
	Code_CODE_STAFF_ONLY_BANDING_ONE_VAN Code = 20028
	// STAFF_NOT_FOUND_IN_FROM_VAN(20029, "Staff record not found in from van")
	Code_CODE_STAFF_NOT_FOUND_IN_FROM_VAN Code = 20029
	// STAFF_FOUND_IN_TO_VAN(20030, "Staff record was found in to van")
	Code_CODE_STAFF_FOUND_IN_TO_VAN Code = 20030
	// PROCESSING_FEE_SHOULD_IN_STRIPE_PAY(20031, "This feature is for Stripe only. Please set Stripe as primary credit card provider.")
	Code_CODE_PROCESSING_FEE_SHOULD_IN_STRIPE_PAY Code = 20031
	// PROCESSING_FEE_SHOULD_IN_US_AREA(20032, "Passing credit card processing fees to clients is not available in this area.")
	Code_CODE_PROCESSING_FEE_SHOULD_IN_US_AREA Code = 20032
	// STAFF_UNLINK_REASON_OWNER(20033, "Business Owner cannot be unlink")
	Code_CODE_STAFF_UNLINK_REASON_OWNER Code = 20033
	// STAFF_UNLINK_REASON_NO_LINK(20034, "This staff is not linked to an account")
	Code_CODE_STAFF_UNLINK_REASON_NO_LINK Code = 20034
	// STAFF_UNLINK_REASON_HAS_BEEN_DELETED(20035, "Staff has been deleted")
	Code_CODE_STAFF_UNLINK_REASON_HAS_BEEN_DELETED Code = 20035
	// CAN_NOT_CREATE_STAFF(20036, "The current plan cannot create more staff.")
	Code_CODE_CAN_NOT_CREATE_STAFF Code = 20036
	// NOT_ENOUGH_VANS_NUM2(20027, "can't create new van, need upgrade") 20027 已被占用，修改为20037
	Code_CODE_NOT_ENOUGH_VANS_NUM2 Code = 20037
	// SMART_SCHEDULE_RULE_ALREADY_EXISTS
	Code_CODE_SMART_SCHEDULE_RULE_ALREADY_EXISTS Code = 20038
	// BUSINESS_NOT_FOUND
	Code_CODE_BUSINESS_NOT_FOUND Code = 20039
	// ACCOUNT_NOT_OWN_THIS_ENTERPRISE
	Code_CODE_ACCOUNT_NOT_OWN_THIS_ENTERPRISE Code = 20040
	// ENTERPRISE_NOT_OWN_THIS_COMPANY
	Code_CODE_ENTERPRISE_NOT_OWN_THIS_COMPANY Code = 20041
	// ENTERPRISE_NOT_FOUND
	Code_CODE_ENTERPRISE_NOT_FOUND Code = 20042
	// COMPANY_REACH_MAX_LIMIT
	Code_CODE_COMPANY_REACH_MAX_LIMIT Code = 20043
	// SWITCH_COMPANY_ERROR
	Code_CODE_SWITCH_COMPANY_ERROR Code = 20044
	// STAFF_NOT_FOUND
	Code_CODE_STAFF_NOT_FOUND Code = 20045
	// STAFF_WORKING_LOCATION_LIST_IS_EMPTY
	Code_CODE_STAFF_WORKING_LOCATION_LIST_IS_EMPTY Code = 20046
	// STAFF_BINDING_ERROR5
	Code_CODE_STAFF_BINDING_EMAIL_INCORRECT Code = 20047
	// STAFF LOGIN LIMIT
	Code_CODE_STAFF_LOGIN_LIMIT Code = 20048
	// moego-customer error code start 30000 end 40000
	// EMAIL_NOT_EXIST
	Code_CODE_EMAIL_NOT_EXIST Code = 30000
	// EMAIL_EXIST
	Code_CODE_EMAIL_EXIST Code = 30001
	// PHONE_EXIST
	Code_CODE_PHONE_EXIST Code = 30002
	// CUSTOMER_NOT_FOUND
	Code_CODE_CUSTOMER_NOT_FOUND Code = 30003
	// PET_NOT_FOUND
	Code_CODE_PET_NOT_FOUND Code = 30004
	// APPOINTMENT_NOT_FOUND
	Code_CODE_APPOINTMENT_NOT_FOUND Code = 30005
	// PET_HAVE_APPOINTMENT
	Code_CODE_PET_HAVE_APPOINTMENT Code = 30006
	// APPOINTMENT_SIGN_OVER
	Code_CODE_APPOINTMENT_SIGN_OVER Code = 30007
	// APPOINTMENT_PET_NOT_EXIST
	Code_CODE_APPOINTMENT_PET_NOT_EXIST Code = 30008
	// APPOINTMENT_AGREEMENT_SEND_CONTENT_NOT_CONFIG
	Code_CODE_APPOINTMENT_AGREEMENT_SEND_CONTENT_NOT_CONFIG Code = 30009
	// PET_CODE_EXISTS
	Code_CODE_PET_CODE_EXISTS Code = 30010
	// EMAIL_EXISTS_MULTI
	Code_CODE_EMAIL_EXISTS_MULTI Code = 30011
	// BIRTHDAY_FORMAT_ERROR
	Code_CODE_BIRTHDAY_FORMAT_ERROR Code = 30012
	// CUSTOMER_IS_BLOCKED
	Code_CODE_CUSTOMER_IS_BLOCKED Code = 30013
	// CUSTOMER_ALREADY_EXISTS
	Code_CODE_CUSTOMER_ALREADY_EXISTS Code = 30014
	// INTAKE_FORM_IS_DELETED
	Code_CODE_INTAKE_FORM_IS_DELETED Code = 30015
	// INTAKE_FORM_MESSAGE_TOO_LONG
	Code_CODE_INTAKE_FORM_MESSAGE_TOO_LONG Code = 30016
	// PET_BREED_NOT_ALLOW_DELETE
	Code_CODE_PET_BREED_NOT_ALLOW_DELETE Code = 30017
	// CUSTOMER_ACCOUNT_OR_PASSWORD_ERROR
	Code_CODE_CUSTOMER_ACCOUNT_OR_PASSWORD_ERROR Code = 30018
	// CUSTOMER_ACCOUNT_NOT_FOUND
	Code_CODE_CUSTOMER_ACCOUNT_NOT_FOUND Code = 30019
	// CUSTOMER_SESSION_NOT_FOUND
	Code_CODE_CUSTOMER_SESSION_NOT_FOUND Code = 30020
	// CUSTOMER_INVALID_CODE
	Code_CODE_CUSTOMER_INVALID_CODE Code = 30021
	// PET_CODE_DESCRIPTION_TOO_LONG(30022, "Description should less than 50 characters.")
	Code_CODE_PET_CODE_DESCRIPTION_TOO_LONG Code = 30022
	// EMAIL_OR_CODE_ERROR(30023, "Email or verification code error.")
	Code_CODE_EMAIL_OR_CODE_ERROR Code = 30023
	// PHONE_OR_CODE_ERROR(30024, "Phone number or verification code error.")
	Code_CODE_PHONE_OR_CODE_ERROR Code = 30024
	// LICENSE_INVALID(30025, "License is invalid.")
	Code_CODE_LICENSE_INVALID Code = 30025
	// PHONE_INVALID(30026, "The phone number is invalid, please contact your service provider.")
	Code_CODE_PHONE_INVALID Code = 30026
	// EMAIL_INVALID(30027, "The email is invalid, please contact your service provider.")
	Code_CODE_EMAIL_INVALID Code = 30027
	// customer creation from given source is disabled
	Code_CODE_CUSTOMER_CREATION_FROM_GIVEN_SOURCE_IS_DISABLED Code = 30028
	// customer is merging
	Code_CODE_CUSTOMER_MERGING Code = 30029
	// customer not duplicate
	Code_CODE_CUSTOMER_NOT_DUPLICATE Code = 30030
	// business customer address error code
	// ADDRESS_NOT_FOUND
	Code_CODE_ADDRESS_NOT_FOUND Code = 30100
	// CANNOT_DELETE_PRIMARY_ADDRESS
	Code_CODE_CANNOT_DELETE_PRIMARY_ADDRESS Code = 30101
	// INVALID_LATITUDE_OR_LONGITUDE
	Code_CODE_INVALID_LATITUDE_OR_LONGITUDE Code = 30102
	// ADDRESS_IS_EMPTY
	Code_CODE_ADDRESS_IS_EMPTY Code = 30103
	// TOO_MANY_ADDRESS
	Code_CODE_TOO_MANY_ADDRESSES Code = 30104
	// book online client error code start 40000 end 50000
	// BOOK_ONLINE_NAME_INVALID
	Code_CODE_BOOK_ONLINE_NAME_INVALID Code = 40404
	// BOOK_ONLINE_NOT_ENABLE
	Code_CODE_BOOK_ONLINE_NOT_ENABLE Code = 40070
	// AGREEMENT_NOT_CONFIRM
	Code_CODE_AGREEMENT_NOT_CONFIRM Code = 40090
	// SIGNATURE_IS_EMPTY
	Code_CODE_SIGNATURE_IS_EMPTY Code = 40091
	// ACCEPT_TYPE_NOW_ALLOWED
	Code_CODE_ACCEPT_TYPE_NOW_ALLOWED Code = 40092
	// CANCEL_POLICY_NOT_CONFIRMED
	Code_CODE_CANCEL_POLICY_NOT_CONFIRMED Code = 40093
	// CUSTOMER_NOT_FOUND_FOR_OB
	Code_CODE_CUSTOMER_NOT_FOUND_FOR_OB Code = 40094
	// STRIPE_CARD_TOKEN_IS_EMPTY
	Code_CODE_STRIPE_CARD_TOKEN_IS_EMPTY Code = 40095
	// APPOINTMENT_TIME_IS_NOT_AVAILABLE
	Code_CODE_APPOINTMENT_TIME_IS_NOT_AVAILABLE Code = 40096
	// APPOINTMENT_CANCELED_INVOICE_INVALID
	Code_CODE_APPOINTMENT_CANCELED_INVOICE_INVALID Code = 40097
	// The appointment time submitted by ob is locked
	Code_CODE_APPOINTMENT_TIME_LOCKED Code = 40098
	// The OB's deposit must be paid first
	Code_CODE_OB_DEPOSIT_NOT_PAID Code = 40099
	// Business custom ob domain is invalid
	Code_CODE_BOOK_ONLINE_DOMAIN_INVALID Code = 40100
	// Business doesn't publish ob website
	Code_CODE_BOOK_ONLINE_SITE_NOT_FOUND Code = 40101
	// moego-grooming error code start 50000 end 60000
	// SERVICE_CATEGORY_NAME_IS_EXIST
	Code_CODE_SERVICE_CATEGORY_NAME_IS_EXIST Code = 51001
	// SERVICE_NAME_IS_EXIST
	Code_CODE_SERVICE_NAME_IS_EXIST Code = 51002
	// SERVICE_CATEGORY_NOT_FOUND
	Code_CODE_SERVICE_CATEGORY_NOT_FOUND Code = 51003
	// INVALID_VALUE_TYPE
	Code_CODE_INVALID_VALUE_TYPE Code = 51004
	// APPLY_PACKAGE_CHANGED
	Code_CODE_APPLY_PACKAGE_CHANGED Code = 51005
	// INVOICE_INVALID_STATUS
	Code_CODE_INVOICE_INVALID_STATUS Code = 51006
	// SERVICE_NOT_FOUND
	Code_CODE_SERVICE_NOT_FOUND Code = 51007
	// APPOINTMENT_INVALID_STATUS
	Code_CODE_APPOINTMENT_INVALID_STATUS Code = 51008
	// SERVICE_HAVE_BINDING
	Code_CODE_SERVICE_HAVE_BINDING Code = 51009
	// NO_AVAILABLE_SERVICE_SELECTED
	Code_CODE_NO_AVAILABLE_SERVICE_SELECTED Code = 51010
	// NO_CUSTOMER_ADDRESS
	Code_CODE_NO_CUSTOMER_ADDRESS Code = 51011
	// TOO_MANY_DAYS_TO_QUERY
	Code_CODE_TOO_MANY_DAYS_TO_QUERY Code = 51012
	// NO_AVAILABLE_STAFF_WHEN_SUBMIT
	Code_CODE_NO_AVAILABLE_STAFF_WHEN_SUBMIT Code = 51013
	// CREDIT_CARD_NEED_OPEN
	Code_CODE_CREDIT_CARD_NEED_OPEN Code = 51014
	// GOOGLE_INVALID_ADDRESS
	Code_CODE_GOOGLE_INVALID_ADDRESS Code = 51015
	// TASK_FAILURE_GENERAL
	Code_CODE_TASK_FAILURE_GENERAL Code = 51016
	// TASK_MESSAGE_RESET_FAILURE
	Code_CODE_TASK_MESSAGE_RESET_FAILURE Code = 51017
	// TOO_MANY_APPOINTMENTS
	Code_CODE_TOO_MANY_APPOINTMENTS Code = 51018
	// SERVICE_CATEGORY_NAME_IS_TOO_LONG
	Code_CODE_SERVICE_CATEGORY_NAME_IS_TOO_LONG Code = 51019
	// REPEAT_IS_NOT_FOUND
	Code_CODE_REPEAT_IS_NOT_FOUND Code = 51020
	// QUICKBOOKS_DEV_ERROR
	Code_CODE_QUICKBOOKS_DEV_ERROR Code = 51021
	// QUICKBOOKS_OAUTH_ERROR
	Code_CODE_QUICKBOOKS_OAUTH_ERROR Code = 51022
	// QUICKBOOKS_REFRESH_TOKEN_ERROR
	Code_CODE_QUICKBOOKS_REFRESH_TOKEN_ERROR Code = 51023
	// QUICKBOOKS_UNEXPECTED_EXCEPTION
	Code_CODE_QUICKBOOKS_UNEXPECTED_EXCEPTION Code = 51024
	// QUICKBOOKS_SETTING_ERROR
	Code_CODE_QUICKBOOKS_SETTING_ERROR Code = 51025
	// QUICKBOOKS_DATA_DUPLICATE_NAME_EXISTS
	Code_CODE_QUICKBOOKS_DATA_DUPLICATE_NAME_EXISTS Code = 51026
	// QUICKBOOKS_DATA_DUPLICATE_EMAIL_FORMAT_ERROR
	Code_CODE_QUICKBOOKS_DATA_DUPLICATE_EMAIL_FORMAT_ERROR Code = 51027
	// google calendar
	// GOOGLE_OAUTH_CHECK_ERROR
	Code_CODE_GOOGLE_OAUTH_CHECK_ERROR Code = 51028
	// GOOGLE_OAUTH_ERROR
	Code_CODE_GOOGLE_OAUTH_ERROR Code = 51029
	// GOOGLE_OAUTH_IS_USED
	Code_CODE_GOOGLE_OAUTH_IS_USED Code = 51030
	// UPCOMING_ID_ERROR
	Code_CODE_UPCOMING_ID_ERROR Code = 51031
	// CREATE_ERROR
	Code_CODE_CREATE_ERROR Code = 51032
	// WATCH_EVENT_ERROR
	Code_CODE_WATCH_EVENT_ERROR Code = 51033
	// GET_EVENT_ERROR
	Code_CODE_GET_EVENT_ERROR Code = 51034
	// SS_FOR_REPEAT_FREQUENCY_PARAMS_ERROR
	Code_CODE_SS_FOR_REPEAT_FREQUENCY_PARAMS_ERROR Code = 51035
	// FREE_SS_FOR_REPEAT_TIMES_USED_OUT
	Code_CODE_FREE_SS_FOR_REPEAT_TIMES_USED_OUT Code = 51036
	// SS_FOR_REPEAT_TIMES_OVER_LIMIT
	Code_CODE_SS_FOR_REPEAT_TIMES_OVER_LIMIT Code = 51037
	// INVOICE_SET_TIPS_ERROR
	Code_CODE_INVOICE_SET_TIPS_ERROR Code = 51038
	// CODE_QUESTION_NAME_IS_EXIST
	Code_CODE_QUESTION_NAME_IS_EXIST Code = 51039
	// INVOICE_NOT_FOUND
	Code_CODE_INVOICE_NOT_FOUND Code = 51040
	// PET_SIZE_IN_USED
	Code_CODE_PET_SIZE_IN_USED Code = 51041
	// CODE_GROOMING_REPORT_NOT_AVAILABLE
	Code_CODE_GROOMING_REPORT_NOT_AVAILABLE Code = 51042
	// CODE_GROOMING_REPORT_BOOK_AGAIN_EXPIRED
	Code_CODE_GROOMING_REPORT_BOOK_AGAIN_EXPIRED Code = 51043
	// CODE_SERVICE_CHARGE_NOT_AVAILABLE
	Code_CODE_SERVICE_CHARGE_NOT_AVAILABLE Code = 51044
	// CODE_EMPTY_PET_LIST
	Code_CODE_EMPTY_PET_LIST Code = 51045
	// CODE_EMPTY_SERVICE_LIST
	Code_CODE_EMPTY_SERVICE_LIST Code = 51046
	// CODE_APPOINTMENT_TRACKING_NOT_FOUND
	Code_CODE_APPOINTMENT_TRACKING_NOT_FOUND Code = 51047
	// CODE_APPOINTMENT_TRACKING_SHARED_BY_OTHER
	Code_CODE_APPOINTMENT_TRACKING_SHARED_BY_OTHER Code = 51048
	// CODE_APPOINTMENT_TRACKING_STAFF_HAS_ANOTHER_SHARING_APPOINTMENT
	Code_CODE_APPOINTMENT_TRACKING_STAFF_HAS_ANOTHER_SHARING_APPOINTMENT Code = 51049
	// CODE_APPOINTMENT_TRACKING_STAFF_LOCATION_STATUS_CHANGE_NOT_ALLOWED
	Code_CODE_APPOINTMENT_TRACKING_STAFF_LOCATION_STATUS_CHANGE_NOT_ALLOWED Code = 51050
	// moego-retail error code start 60000 end 70000
	// BUSINESS_IS_EMPTY
	Code_CODE_BUSINESS_IS_EMPTY Code = 60000
	// SUPPLIER_NOT_FOUND
	Code_CODE_SUPPLIER_NOT_FOUND Code = 60001
	// CATEGORY_NOT_FOUND
	Code_CODE_CATEGORY_NOT_FOUND Code = 60002
	// PACKAGE_NOT_FOUND
	Code_CODE_PACKAGE_NOT_FOUND Code = 60003
	// PRODUCT_NOT_FOUND
	Code_CODE_PRODUCT_NOT_FOUND Code = 60004
	// CART_NOT_FOUND
	Code_CODE_CART_NOT_FOUND Code = 60005
	// NAME_IS_EXIST
	Code_CODE_NAME_IS_EXIST Code = 60006
	// SKU_IS_EXIST
	Code_CODE_SKU_IS_EXIST Code = 60007
	// NOT_PAID
	Code_CODE_NOT_PAID Code = 60008
	// INVALID_CART_ID
	Code_CODE_INVALID_CART_ID Code = 60009
	// CART_ALREADY_PROCESSING
	Code_CODE_CART_ALREADY_PROCESSING Code = 60010
	// CART_ALREADY_COMPLETED
	Code_CODE_CART_ALREADY_COMPLETED Code = 60011
	// INVALID_DISCOUNT_TYPE
	Code_CODE_INVALID_DISCOUNT_TYPE Code = 60012
	// NAME_IS_EMPTY
	Code_CODE_NAME_IS_EMPTY Code = 60013
	// STAFF_IS_EMPTY
	Code_CODE_STAFF_IS_EMPTY Code = 60014
	// BARCODE_IS_EXIST(60015, "Barcode is already in use")
	Code_CODE_BARCODE_IS_EXIST Code = 60015
	// payment error code start 70000 end 80000
	// PAY_AMOUNT_INVALID
	Code_CODE_PAY_AMOUNT_INVALID Code = 70001
	// PAY_MODULE_EMPTY
	Code_CODE_PAY_MODULE_EMPTY Code = 70002
	// PAY_INVOICE_ID_EMPTY
	Code_CODE_PAY_INVOICE_ID_EMPTY Code = 70003
	// PAY_METHOD_INVALID
	Code_CODE_PAY_METHOD_INVALID Code = 70004
	// PAY_DATA_INVALID
	Code_CODE_PAY_DATA_INVALID Code = 70005
	// INVALID_CHECK_NUMBER
	Code_CODE_INVALID_CHECK_NUMBER Code = 70006
	// REFUND_AMOUNT_INVALID
	Code_CODE_REFUND_AMOUNT_INVALID Code = 70007
	// STRIPE_INTENT_NOT_FOUND
	Code_CODE_STRIPE_INTENT_NOT_FOUND Code = 70008
	// PAYMENT_NOT_FOUND
	Code_CODE_PAYMENT_NOT_FOUND Code = 70009
	// STRIPE_ACCOUNT_NOT_FOUND
	Code_CODE_STRIPE_ACCOUNT_NOT_FOUND Code = 70011
	// SUBSCRIPTION_EXPIRATION
	Code_CODE_SUBSCRIPTION_EXPIRATION Code = 70012
	// SUBSCRIPTION_NOT_EXIST
	Code_CODE_SUBSCRIPTION_NOT_EXIST Code = 70013
	// COMPANY_STATE_NOT_VALID
	Code_CODE_COMPANY_STATE_NOT_VALID Code = 70014
	// STRIPE_ACCOUNT_ERROR
	Code_CODE_STRIPE_ACCOUNT_ERROR Code = 70015
	// REFUSE_TO_STRIPE_SET_UP
	Code_CODE_REFUSE_TO_STRIPE_SET_UP Code = 70016
	// SUBSCRIPTION_NOT_VALID
	Code_CODE_SUBSCRIPTION_NOT_VALID Code = 70017
	// REFUND_TIME_OUT
	Code_CODE_REFUND_TIME_OUT Code = 70018
	// STRIPE_CARD_EXCEPTION
	Code_CODE_STRIPE_CARD_EXCEPTION Code = 70019
	// READER_STATUS_ERROR
	Code_CODE_TERMINAL_READER_IN_PROGRESSING Code = 70020
	// CARD PRE AUTH FAILED
	Code_CODE_PRE_AUTHENTICATION_FAILED Code = 70021
	// PREAUTH OPENED
	Code_CODE_PRE_AUTHENTICATION_DUPLICATED Code = 70022
	// PREAUTH ERROR STATUS
	Code_CODE_PRE_AUTHENTICATION_STATUS_ERROR Code = 70023
	// DEPOSIT_HAS_PAID_EXCEPTION(70024, "This invoice has already paid deposit")
	Code_CODE_DEPOSIT_HAS_PAID_EXCEPTION Code = 70024
	// A2P_NOW_ALLOW_EDITING(70025, "The current state does not allow editing of the data")
	Code_CODE_A2P_NOW_ALLOW_EDITING Code = 70025
	// A2P_CHARGE_FAILED(70026, "Payment failed, please check your credit card.")
	Code_CODE_A2P_CHARGE_FAILED Code = 70026
	// 以下是旧的错误码，同步至新的错误码，代码里还是使用旧的错误码，后续可以替换成新的，前端如果有用到则需要改成新的
	// PAYMENT_STATUS_EXCEPTION(70020, "Payment status has been updated, please refresh.")
	Code_CODE_PAYMENT_STATUS_EXCEPTION Code = 70027
	// SUBSCRIPTION_UPDATE_FAILED(70021, "Cannot Update subscription for failed payments.")
	Code_CODE_SUBSCRIPTION_UPDATE_FAILED Code = 70028
	// STRIPE_COMPANY_CUSTOMER_NOT_FOUND(70022, "Stripe company customer is not found")
	Code_CODE_STRIPE_COMPANY_CUSTOMER_NOT_FOUND Code = 70029
	// STRIPE_UPDATE_CUSTOMER_EXCEPTION(70023, "Update Stripe customer error")
	Code_CODE_STRIPE_UPDATE_CUSTOMER_EXCEPTION Code = 70030
	// STRIPE_CREATE_HARDWARE_INVOICE_EXCEPTION(70024, "Create hardware invoice error")
	Code_CODE_STRIPE_CREATE_HARDWARE_INVOICE_EXCEPTION Code = 70031
	// STRIPE_CREATE_HARDWARE_ORDER_EXCEPTION(70025, "Create hardware order error")
	Code_CODE_STRIPE_CREATE_HARDWARE_ORDER_EXCEPTION Code = 70032
	// PLATFORM_CARE_AGREEMENT_NOT_FOUND(70033, "The agreement is not found")
	Code_CODE_PLATFORM_CARE_AGREEMENT_NOT_FOUND Code = 70033
	// PLATFORM_CARE_RECORD_NOT_FOUND(70034, "The record is not found")
	Code_CODE_PLATFORM_CARE_RECORD_NOT_FOUND Code = 70034
	// PLATFORM_CARE_RECORD_NOT_FOUND_BY_EMAIL(70035, "This email cannot be found in the records.")
	Code_CODE_PLATFORM_CARE_RECORD_NOT_FOUND_BY_EMAIL Code = 70035
	// PLATFORM_CARE_EMAIL_ADDRESS_MISMATCH(70036, "The signing email is inconsistent with the registered email")
	Code_CODE_PLATFORM_CARE_EMAIL_ADDRESS_MISMATCH Code = 70036
	// PLATFORM_CARE_ASSIGN_AGREEMENT_ERROR(70037, "The agreement sign failed")
	Code_CODE_PLATFORM_CARE_ASSIGN_AGREEMENT_FAILED Code = 70037
	// PLATFORM_CARE_RECORD_IS_EXIST(70038, "The record is exist")
	Code_CODE_PLATFORM_CARE_RECORD_IS_EXIST Code = 70038
	// ENTERPRISE_STRIPE_CUSTOMER_IS_EXIST
	Code_CODE_ENTERPRISE_STRIPE_CUSTOMER_IS_EXIST Code = 70039
	// COMPANY_STRIPE_CUSTOMER_IS_EXIST
	Code_CODE_COMPANY_STRIPE_CUSTOMER_IS_EXIST Code = 70040
	// twilio fine charge fail
	Code_CODE_TWILIO_FINE_CHARGE_FAIL Code = 70041
	// moego pay is not set up
	Code_CODE_MOEGO_PAY_NOT_SET_UP Code = 70042
	// Not found refund.
	Code_CODE_REFUND_NOT_FOUND Code = 70043
	// moego-message error code start 80000 end 90000
	// MESSAGE_AUTO_TEMPLATE_NOT_CONFIG
	Code_CODE_MESSAGE_AUTO_TEMPLATE_NOT_CONFIG Code = 80100
	// MESSAGE_SEND_PHONE_IS_NULL
	Code_CODE_MESSAGE_SEND_PHONE_IS_NULL Code = 80101
	// MESSAGE_SEND_EMAIL_IS_NULL
	Code_CODE_MESSAGE_SEND_EMAIL_IS_NULL Code = 80102
	// MESSAGE_SEND_REVIEW_IS_NULL
	Code_CODE_MESSAGE_SEND_REVIEW_IS_NULL Code = 80103
	// MESSAGE_SEND_PHONE_FAILED
	Code_CODE_MESSAGE_SEND_PHONE_FAILED Code = 80104
	// MESSAGE_SEND_EMAIL_FAILED
	Code_CODE_MESSAGE_SEND_EMAIL_FAILED Code = 80105
	// MESSAGE_CANNOT_DELETE_OTHER_STAFF_MESSAGE
	Code_CODE_MESSAGE_CANNOT_DELETE_OTHER_STAFF_MESSAGE Code = 80106
	// CUSTOMER_PHONE_NUMBER_ERROR
	Code_CODE_CUSTOMER_PHONE_NUMBER_ERROR Code = 80107
	// NOTIFICATION_TYPE_NOT_FOUND
	Code_CODE_NOTIFICATION_TYPE_NOT_FOUND Code = 80108
	// MESSAGE_AMOUNT_RUN_OUT
	Code_CODE_MESSAGE_AMOUNT_RUN_OUT Code = 80109
	// CODE_SEND_LIMIT
	Code_CODE_CODE_SEND_LIMIT Code = 80110
	// FAIL_TO_SEND_CODE
	Code_CODE_FAIL_TO_SEND_CODE Code = 80111
	// BUSINESS_TWILIO_MESSAGE_NOE_MORE
	Code_CODE_BUSINESS_TWILIO_MESSAGE_NOE_MORE Code = 80112
	// MESSAGE_CONTROL_NO_RECORD
	Code_CODE_MESSAGE_CONTROL_NO_RECORD Code = 80113
	// MESSAGE_SEND_FAILED
	Code_CODE_MESSAGE_SEND_FAILED Code = 80114
	// MESSAGE_SEND_FAILED_BY_SWITCH_CLOSE
	Code_CODE_MESSAGE_SEND_FAILED_BY_SWITCH_CLOSE Code = 80115
	// MESSAGE_EMAIL_RECEIVE_NOT_FOUND_BUSINESS
	Code_CODE_MESSAGE_EMAIL_RECEIVE_NOT_FOUND_BUSINESS Code = 80116
	// NOT_ALLOW_UPDATE_TWILIO_CALL_FORWARDING
	Code_CODE_NOT_ALLOW_UPDATE_TWILIO_CALL_FORWARDING Code = 80117
	// TWILIO_QUERY_ERROR
	Code_CODE_TWILIO_QUERY_ERROR Code = 80118
	// invalid_email_event_type
	Code_CODE_INVALID_EMAIL_EVENT_TYPE Code = 80119
	// invalid_email_status
	Code_CODE_INVALID_EMAIL_STATUS Code = 80120
	// email_not_found
	Code_CODE_EMAIL_NOT_FOUND Code = 80121
	// grooming report send failed
	Code_CODE_GROOMING_REPORT_SEND_FAILED Code = 80122
	// not allow submit review
	Code_CODE_NOT_ALLOW_SUBMIT_REVIEW Code = 80123
	// a2p data forbidden edit
	Code_CODE_ATP_DATA_FORBIDDEN_EDIT Code = 80124
	// a2p phone number type error
	Code_CODE_ATP_PN_TYPE_ERROR Code = 80125
	// a2p submit lock
	Code_CODE_ATP_SUBMIT_RETRY Code = 80126
	// a2p atp brand otp retry
	Code_CODE_ATP_BRAND_OTP_RETRY Code = 80127
	// message not found
	Code_CODE_MESSAGE_NOT_FOUND Code = 80128
	// Phone number cannot be formatted
	Code_CODE_PHONE_NUMBER_CANNOT_FORMATTED Code = 80129
	// client app error code start 90000 end 94999
	// 90000-94999 for client app error
	// link business not found
	Code_CODE_LINK_BUSINESS_NOT_FOUND Code = 90000
	// invitation code not found
	Code_CODE_INVITATION_CODE_NOT_FOUND Code = 90001
	// link phone number not found
	Code_CODE_LINK_PHONE_NUMBER_NOT_FOUND Code = 90002
	// No linked customers found
	Code_CODE_NO_LINKED_CUSTOMERS_FOUND Code = 90003
	// Linked too many customers
	Code_CODE_LINKED_TOO_MANY_CUSTOMERS Code = 90004
	// branded app not found
	Code_CODE_BRANDED_APP_NOT_FOUND Code = 90005
	// Cancellations are not allowed
	Code_CODE_CANCELLATION_NOT_ALLOWED Code = 90006
	// Reschedule are not allowed
	Code_CODE_RESCHEDULE_NOT_ALLOWED Code = 90007
	// fintech app error code start 95000 end 99999
	// force update
	Code_CODE_FINTECH_BUSINESS_APP_UPDATE_FORCE Code = 95000
	// closable update
	Code_CODE_FINTECH_BUSINESS_APP_UPDATE_CLOSABLE Code = 95001
	// moego-account error code start 100000 end 110000
	// 100000 ~ 100999 for account error
	// account not exist
	Code_CODE_ACCOUNT_NOT_EXIST Code = 100000
	// password too weak
	Code_CODE_PASSWORD_TOO_WEAK Code = 100001
	// email conflict
	Code_CODE_EMAIL_CONFLICT Code = 100002
	// phone number conflict
	Code_CODE_PHONE_NUMBER_CONFLICT Code = 100003
	// account or password error
	Code_CODE_ACCOUNT_OR_PASSWORD_ERROR Code = 100004
	// old password error
	Code_CODE_OLD_PASSWORD_ERROR Code = 100005
	// account already recovered
	Code_CODE_ACCOUNT_ALREADY_RECOVERED Code = 100006
	// verification code not match
	Code_CODE_VERIFICATION_CODE_NOT_MATCH Code = 100007
	// account frozen
	Code_CODE_ACCOUNT_FROZEN Code = 100008
	// 101000 ~ 101999 for session error
	// session not exist
	Code_CODE_SESSION_NOT_EXIST Code = 101000
	// session expired
	Code_CODE_SESSION_EXPIRED Code = 101001
	// session token invalid
	Code_CODE_SESSION_TOKEN_INVALID Code = 101002
	// 102000 ~ 102999 for account association error
	// platform account already associated
	Code_CODE_PLATFORM_ACCOUNT_ALREADY_ASSOCIATED Code = 102000
	// ai assistant error code start 120000 end 130000
	// conversation not exist
	Code_CODE_CONVERSATION_NOT_EXIST Code = 120000
	// conversation business not match (conversation not belong to business)
	Code_CODE_CONVERSATION_BUSINESS_NOT_MATCH Code = 120001
	// conversation closed
	Code_CODE_CONVERSATION_CLOSED Code = 120003
	// conversation failed to reply
	Code_CODE_CONVERSATION_FAILED_TO_REPLY Code = 120004
	// conversation balance not enough
	Code_CODE_CONVERSATION_BALANCE_NOT_ENOUGH Code = 120005
	// conversation question not exist
	Code_CODE_CONVERSATION_QUESTION_NOT_EXIST Code = 120006
	// conversation question not match (question not belong to conversation)
	Code_CODE_CONVERSATION_QUESTION_NOT_MATCH Code = 120007
	// conversation question excessive
	Code_CODE_CONVERSATION_QUESTION_EXCESSIVE Code = 120008
	// google recaptcha error code start 130000 end 140000
	// google recaptcha v2 version verification failed
	Code_CODE_GOOGLE_RECAPTCHA_INVALID Code = 130000
	// google recaptcha v3 version score is less than threshold
	Code_CODE_GOOGLE_RECAPTCHA_THRESHOLD_NOT_REACHED Code = 130001
	// google recaptcha action not found
	Code_CODE_GOOGLE_RECAPTCHA_ACTION_NOT_FOUND Code = 130002
	// send verification code limited
	Code_CODE_VERIFICATION_CODE_SENT_COUNT_LIMITED Code = 130003
	// send verification code interval not reached
	Code_CODE_VERIFICATION_CODE_SEND_INTERVAL_NOT_REACHED Code = 130004
	// invalid verification code
	Code_CODE_INVALID_VERIFICATION_CODE Code = 130005
	// verification code expired
	Code_CODE_VERIFICATION_CODE_EXPIRED Code = 130006
	// moego-file error code start 140000 end 150000
	// file record does not exist in db
	Code_CODE_FILE_RECORD_NOT_EXIST Code = 140000
	// file object does not exist in s3
	Code_CODE_FILE_OBJECT_NOT_EXIST Code = 140001
	// the file extension is not allowed
	Code_CODE_FILE_EXT_FORBIDDEN Code = 140002
	// moego-permission error code start 150000 end 160000
	// role name already exists
	Code_CODE_ROLE_NAME_ALREADY_EXISTS Code = 150000
	// role not found, maybe be deleted or company id not match
	Code_CODE_ROLE_NOT_FOUND Code = 150001
	// moego-offering error code start 160000 end 170000
	// lodging
	// operation can't be performed on lodging type in use
	Code_CODE_LODGING_TYPE_IN_USE Code = 160000
	// lodging type not found
	Code_CODE_LODGING_TYPE_NOT_FOUND Code = 160001
	// operation can't be performed on lodging unit in use
	Code_CODE_LODGING_UNIT_IN_USE Code = 160002
	// moego-finance error code start 170000 end 180000
	// finance-common 170000-171000
	// The requested API is internal API and not allowed for current user.
	Code_CODE_FINANCE_NO_ACCESS_INTERNAL_API Code = 170000
	// capital 171000-172000
	// The requested offer is not found.
	Code_CODE_CAPITAL_OFFER_NOT_FOUND Code = 171000
	// The current user has no permission to access the requested offer.
	Code_CODE_CAPITAL_OFFER_NO_ACCESS Code = 171001
	// The requested offer is in a wrong status for the operation to perform.
	Code_CODE_CAPITAL_ILLEGAL_OFFER_STATUS Code = 171002
	// The requested channel account is not found.
	Code_CODE_CAPITAL_CHANNEL_ACCOUNT_NOT_FOUND Code = 171003
	// The database error occurred when operating the capital data.
	Code_CODE_CAPITAL_DB_ERROR Code = 171004
	// The capital lock failed.
	Code_CODE_CAPITAL_GET_LOCK_FAILED Code = 171005
	// The capital is locked by other goroutine.
	Code_CODE_CAPITAL_LOCKED_BY_OTHER Code = 171006
	// The capital is fully paid with remaining amount.
	Code_CODE_CAPITAL_FULLY_PAID_WITH_REMAINING Code = 171007
	// The transaction type is unknown.
	Code_CODE_CAPITAL_UNKNOWN_TRANSACTION_TYPE Code = 171008
	// The capital interval paid amount mismatch.
	Code_CODE_CAPITAL_INTERVAL_PAID_AMOUNT_MISMATCH Code = 171009
	// The capital offer remaining amount mismatch.
	Code_CODE_CAPITAL_OFFER_REMAINING_AMOUNT_MISMATCH Code = 171010
	// The capital transaction is not found.
	Code_CODE_CAPITAL_TRANSACTION_NOT_FOUND Code = 171011
	// The capital channel status is unknown.
	Code_CODE_CAPITAL_CHANNEL_STATUS_UNKNOWN Code = 171012
	// The capital stripe API error.
	Code_CODE_CAPITAL_STRIPE_API_ERROR Code = 171013
	// The capital cache error.
	Code_CODE_CAPITAL_CACHE_ERROR Code = 171014
	// The capital illegal status transition.
	Code_CODE_CAPITAL_ILLEGAL_STATUS_TRANSITION Code = 171015
	// The capital network error.
	Code_CODE_CAPITAL_NETWORK_ERROR Code = 171016
	// The capital unmarshal error.
	Code_CODE_CAPITAL_UNMARSHAL_ERROR Code = 171017
	// The capital unknown entity type.
	Code_CODE_CAPITAL_UNKNOWN_ENTITY_TYPE Code = 171018
	// The capital Kanmon API error.
	Code_CODE_CAPITAL_KANMON_API_ERROR Code = 171019
	// The capital channel status is unknown.
	Code_CODE_CAPITAL_CHANNEL_UNKNOWN Code = 171020
	// The capital processing amount is greater than remaining amount.
	Code_CODE_CAPITAL_PROCESSING_AMOUNT_GREATER_THAN_REMAINING Code = 171021
	// Failed to deliver message.
	Code_CODE_CAPITAL_FAILED_TO_DELIVER_MESSAGE Code = 171022
	// finance-gw 172000-173000
	// Invalid Stripe webhook payload (wrong signature, wrong json, etc.)
	Code_CODE_FINANCE_GW_INVALID_STRIPE_WEBHOOK_PAYLOAD Code = 172000
	// The downstream handler failed to handle the request
	Code_CODE_FINANCE_GW_DOWNSTREAM_ERROR Code = 172001
	// Invalid Kanmon webhook payload (wrong signature, wrong json, etc.)
	Code_CODE_FINANCE_GW_INVALID_KANMON_WEBHOOK_PAYLOAD Code = 172002
	// Unknown offer type
	Code_CODE_FINANCE_GW_UNKNOWN_OFFER_TYPE Code = 172003
	// Unknown account event type
	Code_CODE_FINANCE_GW_UNKNOWN_ACCOUNT_EVENT_TYPE Code = 172004
	// Unknown offer event type
	Code_CODE_FINANCE_GW_UNKNOWN_OFFER_EVENT_TYPE Code = 172005
	// Unknown offer transaction event type
	Code_CODE_FINANCE_GW_UNKNOWN_TRANSACTION_EVENT_TYPE Code = 172006
	// Invalid kanmon webhook secret
	Code_CODE_FINANCE_GW_INVALID_KANMON_WEBHOOK_SECRET Code = 172007
	// Signature verification failed
	Code_CODE_FINANCE_GW_SIGNATURE_VERIFICATION_FAILED Code = 172008
	// Unmarshal error
	Code_CODE_FINANCE_GW_PAYLOAD_UNMARSHAL_ERROR Code = 172009
	// moego-rest-api error code start 180000 end 190000
	// IO failure
	Code_CODE_REST_IO Code = 180000
	// The invoked downstream returns error
	Code_CODE_REST_DOWNSTREAM_ERROR Code = 180001
	// moego-svc-finance-tools error code start 190000 end 200000
	// Cash Drawer
	// The time range is invalid (overlapping, end before start, e.g.)
	Code_CODE_FINANCE_TOOLS_INVALID_TIME_RANGE Code = 190000
	// The given payments total does not match the realtime calculated payments total
	Code_CODE_FINANCE_TOOLS_PAYMENTS_TOTAL_UNMATCHED Code = 190001
	// The given adjustments total does not match the realtime calculated adjustments total
	Code_CODE_FINANCE_TOOLS_ADJUSTMENTS_TOTAL_UNMATCHED Code = 190002
	// moego-svc-split-payment error code start 200000 end 210000
	// Split payment acceptance failed
	Code_CODE_SPLIT_PAYMENT_ACCEPTANCE_FAILED Code = 200000
	// Split payment reversal acceptance failed
	Code_CODE_SPLIT_PAYMENT_REVERSAL_ACCEPTANCE_FAILED Code = 200001
	// moego-svc-accounting error code start 210000 end 220000
	// accounting unknown error
	Code_CODE_ACCOUNTING_UNKNOWN_ERROR Code = 210000
	// 数据库访问异常
	Code_CODE_ACCOUNTING_DATA_ACCESS_ERROR Code = 210001
	// 调用第三方异常
	Code_CODE_ACCOUNTING_THIRD_PARTY_API_ERROR Code = 210002
	// 调用内部二方RPC异常
	Code_CODE_ACCOUNTING_RPC_CALL_ERROR Code = 210003
	// 数据同步器获取不到异常
	Code_CODE_ACCOUNTING_DATA_SYNCER_NOT_FOUND_ERROR Code = 210004
	// 内部业务异常
	Code_CODE_ACCOUNTING_INTERNAL_BIZ_ERROR Code = 210005
	// 未订阅，无法访问
	Code_CODE_ACCOUNTING_UNSUBSCRIBED_ERROR Code = 210006
	// TODO(chi, Kuroko, Perqin, P1): 排序、分组错误码
	// moego-svc-payment error code start 220000 end 230000
	// payment unknown error
	Code_CODE_PAYMENT_UNKNOWN_ERROR Code = 220000
	// 数据库访问异常
	Code_CODE_PAYMENT_DATA_ACCESS_ERROR Code = 220001
	// 内部业务异常
	Code_CODE_PAYMENT_INTERNAL_ERROR Code = 220002
	// 错误渠道
	Code_CODE_PAYMENT_INVALID_CHANNEL Code = 220003
	// 错误 Onboard 校验状态
	Code_CODE_PAYMENT_INVALID_VERIFICATION_STATUS Code = 220004
	// Company 下没有 location，理论上不能发生，除非是异常数据
	Code_CODE_PAYMENT_NO_LOCATION Code = 220005
	// 获取 company 失败
	Code_CODE_PAYMENT_GET_COMPANY_FAILED Code = 220006
	// 获取 channel account 失败
	Code_CODE_PAYMENT_GET_CHANNEL_ACCOUNT_FAILED Code = 220007
	// Adyen 获取 account holder 失败
	Code_CODE_PAYMENT_ADYEN_GET_ACCOUNT_HOLDER_FAILED Code = 220008
	// 获取 businesses 失败
	Code_CODE_PAYMENT_LIST_BUSINESSES_FAILED Code = 220009
	// Adyen 创建 legal entity 失败
	Code_CODE_PAYMENT_ADYEN_CREATE_LEGAL_ENTITY_FAILED Code = 220010
	// Adyen 创建 account holder 失败
	Code_CODE_PAYMENT_ADYEN_CREATE_ACCOUNT_HOLDER_FAILED Code = 220011
	// Adyen 创建 store 失败
	Code_CODE_PAYMENT_ADYEN_CREATE_STORE_FAILED Code = 220012
	// Adyen 创建 balance account 失败
	Code_CODE_PAYMENT_ADYEN_CREATE_BALANCE_ACCOUNT_FAILED Code = 220013
	// 创建 channel account 失败
	Code_CODE_PAYMENT_CREATE_CHANNEL_ACCOUNT_FAILED Code = 220014
	// 更新 channel account 失败
	Code_CODE_PAYMENT_UPDATE_CHANNEL_ACCOUNT_FAILED Code = 220015
	// Onboard 需要的预配置信息不合法
	Code_CODE_PAYMENT_ONBOARD_INVALID_PRE_CONFIG Code = 220016
	// Adyen 创建 business line 失败
	Code_CODE_PAYMENT_ADYEN_CREATE_BUSINESS_LINE_FAILED Code = 220017
	// Adyen 创建 onboard link 失败
	Code_CODE_PAYMENT_ADYEN_CREATE_ONBOARD_LINK_FAILED Code = 220018
	// 状态非法
	Code_CODE_PAYMENT_STATUS_INVALID Code = 220019
	// 调用第三方异常
	Code_CODE_PAYMENT_THIRD_PARTY_API_ERROR Code = 220020
	// 调用内部二方RPC异常
	Code_CODE_PAYMENT_RPC_CALL_ERROR Code = 220021
	// 传入的 reader 错误（例如 reader 不存在）
	Code_CODE_PAYMENT_INVALID_READER Code = 220022
	// 调用 Adyen Terminal API 失败
	Code_CODE_PAYMENT_ADYEN_TERMINAL_REQUEST_ERROR Code = 220023
	// Adyen 获取 store 失败
	Code_CODE_PAYMENT_ADYEN_GET_STORE_FAILED Code = 220024
	// Adyen 获取 balance account 失败
	Code_CODE_PAYMENT_ADYEN_GET_BALANCE_ACCOUNT_FAILED Code = 220025
	// 未找到对应的 MoeGo user account
	Code_CODE_PAYMENT_GET_ACCOUNT_FAILED Code = 220026
	// Adyen 给 stores 添加 payment methods 失败
	Code_CODE_PAYMENT_ADYEN_ADD_PAYMENT_METHODS_FAILED Code = 220027
	// Onboard 已经完成
	Code_CODE_PAYMENT_ONBOARD_ALREADY_FINISHED Code = 220028
	// Onboard 步骤非法
	Code_CODE_PAYMENT_ADYEN_INVALID_ONBOARD_STEP Code = 220029
	// Adyen 获取 legal entity 失败
	Code_CODE_PAYMENT_ADYEN_GET_LEGAL_ENTITY_FAILED Code = 220030
	// Adyen 获取 transfer instrument 失败
	Code_CODE_PAYMENT_ADYEN_GET_TRANSFER_INSTRUMENT_FAILED Code = 220031
	// 创建 payment setting 失败
	Code_CODE_PAYMENT_CREATE_PAYMENT_SETTING_FAILED Code = 220032
	// Adyen 获取 payment methods 失败
	Code_CODE_PAYMENT_ADYEN_GET_PAYMENT_METHODS_FAILED Code = 220033
	// 未找到对应的 channel account
	Code_CODE_PAYMENT_CHANNEL_ACCOUNT_NOT_FOUND Code = 220034
	// payout from 221000-222000
	// 未找到对应的 payout
	Code_CODE_PAYMENT_PAYOUT_NOT_FOUND Code = 221001
	// 获取 payout 失败
	Code_CODE_PAYMENT_PAYOUT_GET_FAILED Code = 221002
	// 创建 payout 失败
	Code_CODE_PAYMENT_PAYOUT_CREATE_FAILED Code = 221003
	// billing from 222000-223000
	// billing unknown error
	Code_CODE_BILLING_UNKNOWN_ERROR Code = 222001
	// 数据库访问异常
	Code_CODE_BILLING_DATA_ACCESS_ERROR Code = 222002
	// 内部业务异常
	Code_CODE_BILLING_INTERNAL_ERROR Code = 222003
)

// Enum value maps for Code.
var (
	Code_name = map[int32]string{
		0:      "CODE_UNSPECIFIED",
		200:    "CODE_SUCCESS",
		400:    "CODE_PARAMS_ERROR",
		401:    "CODE_UNAUTHORIZED_ERROR",
		403:    "CODE_FORBIDDEN",
		429:    "CODE_TOO_MANY_REQUESTS",
		500:    "CODE_SERVER_ERROR",
		10010:  "CODE_ORDERID_EXISTS",
		10015:  "CODE_ROOM_ERROR",
		10016:  "CODE_PRIMARY_ID_EMPTY",
		10017:  "CODE_VERIFY_CODE_NOT_EQUAL",
		10018:  "CODE_PARALLEL_ERROR",
		10999:  "CODE_BRANCH_MISMATCH",
		11001:  "CODE_ASSIGN_ITEM_AMOUNT_ONCE_ONLY_ERROR",
		11002:  "CODE_ASSIGN_ITEM_AMOUNT_DATA_PERMISSION_ERROR",
		20001:  "CODE_CHECK_IN_ERROR",
		20002:  "CODE_CHECK_OUT_ERROR",
		20003:  "CODE_PASSWORD_ERROR",
		20004:  "CODE_ACCOUNT_NOT_FOUND",
		20005:  "CODE_INVALID_CODE",
		20006:  "CODE_STAFF_BINDING_ERROR1",
		20007:  "CODE_STAFF_BINDING_ERROR2",
		20008:  "CODE_STAFF_BINDING_ERROR3",
		20009:  "CODE_STAFF_BINDING_ERROR4",
		20010:  "CODE_DELETE_ROLE_ERROR",
		20011:  "CODE_PAYMENT_METHOD_NAME_EXISTS",
		20012:  "CODE_DEFAULT_METHOD_OPER_FALSE",
		20013:  "CODE_FILE_SIZE_EXCEEDS_LIMIT",
		20014:  "CODE_TAX_IS_USED",
		20015:  "CODE_TAG_NAME_EXIST",
		20016:  "CODE_TAG_NAME_NULL",
		20017:  "CODE_THE_LAST_DELETED",
		20018:  "CODE_THE_LAST_INACTIVE",
		20019:  "CODE_PERMISSION_NOT_ENOUGH",
		20020:  "CODE_NO_PERMISSION_SHOW_MESSAGE_THREAD",
		20021:  "CODE_TAX_IS_USED_RETAIL",
		20022:  "CODE_NOT_ENOUGH_VANS_NUM",
		20023:  "CODE_PASSWORD_UPDATE_OLDPWD_ERROR",
		20024:  "CODE_ACCOUNT_NOT_OWN_THIS_COMPANY",
		20025:  "CODE_CHECK_EMAIL_EXIST_PHP_VERSION",
		20026:  "CODE_COMPANY_NOT_FOUND",
		20027:  "CODE_SWITCH_BUSINESS_ERROR",
		20028:  "CODE_STAFF_ONLY_BANDING_ONE_VAN",
		20029:  "CODE_STAFF_NOT_FOUND_IN_FROM_VAN",
		20030:  "CODE_STAFF_FOUND_IN_TO_VAN",
		20031:  "CODE_PROCESSING_FEE_SHOULD_IN_STRIPE_PAY",
		20032:  "CODE_PROCESSING_FEE_SHOULD_IN_US_AREA",
		20033:  "CODE_STAFF_UNLINK_REASON_OWNER",
		20034:  "CODE_STAFF_UNLINK_REASON_NO_LINK",
		20035:  "CODE_STAFF_UNLINK_REASON_HAS_BEEN_DELETED",
		20036:  "CODE_CAN_NOT_CREATE_STAFF",
		20037:  "CODE_NOT_ENOUGH_VANS_NUM2",
		20038:  "CODE_SMART_SCHEDULE_RULE_ALREADY_EXISTS",
		20039:  "CODE_BUSINESS_NOT_FOUND",
		20040:  "CODE_ACCOUNT_NOT_OWN_THIS_ENTERPRISE",
		20041:  "CODE_ENTERPRISE_NOT_OWN_THIS_COMPANY",
		20042:  "CODE_ENTERPRISE_NOT_FOUND",
		20043:  "CODE_COMPANY_REACH_MAX_LIMIT",
		20044:  "CODE_SWITCH_COMPANY_ERROR",
		20045:  "CODE_STAFF_NOT_FOUND",
		20046:  "CODE_STAFF_WORKING_LOCATION_LIST_IS_EMPTY",
		20047:  "CODE_STAFF_BINDING_EMAIL_INCORRECT",
		20048:  "CODE_STAFF_LOGIN_LIMIT",
		30000:  "CODE_EMAIL_NOT_EXIST",
		30001:  "CODE_EMAIL_EXIST",
		30002:  "CODE_PHONE_EXIST",
		30003:  "CODE_CUSTOMER_NOT_FOUND",
		30004:  "CODE_PET_NOT_FOUND",
		30005:  "CODE_APPOINTMENT_NOT_FOUND",
		30006:  "CODE_PET_HAVE_APPOINTMENT",
		30007:  "CODE_APPOINTMENT_SIGN_OVER",
		30008:  "CODE_APPOINTMENT_PET_NOT_EXIST",
		30009:  "CODE_APPOINTMENT_AGREEMENT_SEND_CONTENT_NOT_CONFIG",
		30010:  "CODE_PET_CODE_EXISTS",
		30011:  "CODE_EMAIL_EXISTS_MULTI",
		30012:  "CODE_BIRTHDAY_FORMAT_ERROR",
		30013:  "CODE_CUSTOMER_IS_BLOCKED",
		30014:  "CODE_CUSTOMER_ALREADY_EXISTS",
		30015:  "CODE_INTAKE_FORM_IS_DELETED",
		30016:  "CODE_INTAKE_FORM_MESSAGE_TOO_LONG",
		30017:  "CODE_PET_BREED_NOT_ALLOW_DELETE",
		30018:  "CODE_CUSTOMER_ACCOUNT_OR_PASSWORD_ERROR",
		30019:  "CODE_CUSTOMER_ACCOUNT_NOT_FOUND",
		30020:  "CODE_CUSTOMER_SESSION_NOT_FOUND",
		30021:  "CODE_CUSTOMER_INVALID_CODE",
		30022:  "CODE_PET_CODE_DESCRIPTION_TOO_LONG",
		30023:  "CODE_EMAIL_OR_CODE_ERROR",
		30024:  "CODE_PHONE_OR_CODE_ERROR",
		30025:  "CODE_LICENSE_INVALID",
		30026:  "CODE_PHONE_INVALID",
		30027:  "CODE_EMAIL_INVALID",
		30028:  "CODE_CUSTOMER_CREATION_FROM_GIVEN_SOURCE_IS_DISABLED",
		30029:  "CODE_CUSTOMER_MERGING",
		30030:  "CODE_CUSTOMER_NOT_DUPLICATE",
		30100:  "CODE_ADDRESS_NOT_FOUND",
		30101:  "CODE_CANNOT_DELETE_PRIMARY_ADDRESS",
		30102:  "CODE_INVALID_LATITUDE_OR_LONGITUDE",
		30103:  "CODE_ADDRESS_IS_EMPTY",
		30104:  "CODE_TOO_MANY_ADDRESSES",
		40404:  "CODE_BOOK_ONLINE_NAME_INVALID",
		40070:  "CODE_BOOK_ONLINE_NOT_ENABLE",
		40090:  "CODE_AGREEMENT_NOT_CONFIRM",
		40091:  "CODE_SIGNATURE_IS_EMPTY",
		40092:  "CODE_ACCEPT_TYPE_NOW_ALLOWED",
		40093:  "CODE_CANCEL_POLICY_NOT_CONFIRMED",
		40094:  "CODE_CUSTOMER_NOT_FOUND_FOR_OB",
		40095:  "CODE_STRIPE_CARD_TOKEN_IS_EMPTY",
		40096:  "CODE_APPOINTMENT_TIME_IS_NOT_AVAILABLE",
		40097:  "CODE_APPOINTMENT_CANCELED_INVOICE_INVALID",
		40098:  "CODE_APPOINTMENT_TIME_LOCKED",
		40099:  "CODE_OB_DEPOSIT_NOT_PAID",
		40100:  "CODE_BOOK_ONLINE_DOMAIN_INVALID",
		40101:  "CODE_BOOK_ONLINE_SITE_NOT_FOUND",
		51001:  "CODE_SERVICE_CATEGORY_NAME_IS_EXIST",
		51002:  "CODE_SERVICE_NAME_IS_EXIST",
		51003:  "CODE_SERVICE_CATEGORY_NOT_FOUND",
		51004:  "CODE_INVALID_VALUE_TYPE",
		51005:  "CODE_APPLY_PACKAGE_CHANGED",
		51006:  "CODE_INVOICE_INVALID_STATUS",
		51007:  "CODE_SERVICE_NOT_FOUND",
		51008:  "CODE_APPOINTMENT_INVALID_STATUS",
		51009:  "CODE_SERVICE_HAVE_BINDING",
		51010:  "CODE_NO_AVAILABLE_SERVICE_SELECTED",
		51011:  "CODE_NO_CUSTOMER_ADDRESS",
		51012:  "CODE_TOO_MANY_DAYS_TO_QUERY",
		51013:  "CODE_NO_AVAILABLE_STAFF_WHEN_SUBMIT",
		51014:  "CODE_CREDIT_CARD_NEED_OPEN",
		51015:  "CODE_GOOGLE_INVALID_ADDRESS",
		51016:  "CODE_TASK_FAILURE_GENERAL",
		51017:  "CODE_TASK_MESSAGE_RESET_FAILURE",
		51018:  "CODE_TOO_MANY_APPOINTMENTS",
		51019:  "CODE_SERVICE_CATEGORY_NAME_IS_TOO_LONG",
		51020:  "CODE_REPEAT_IS_NOT_FOUND",
		51021:  "CODE_QUICKBOOKS_DEV_ERROR",
		51022:  "CODE_QUICKBOOKS_OAUTH_ERROR",
		51023:  "CODE_QUICKBOOKS_REFRESH_TOKEN_ERROR",
		51024:  "CODE_QUICKBOOKS_UNEXPECTED_EXCEPTION",
		51025:  "CODE_QUICKBOOKS_SETTING_ERROR",
		51026:  "CODE_QUICKBOOKS_DATA_DUPLICATE_NAME_EXISTS",
		51027:  "CODE_QUICKBOOKS_DATA_DUPLICATE_EMAIL_FORMAT_ERROR",
		51028:  "CODE_GOOGLE_OAUTH_CHECK_ERROR",
		51029:  "CODE_GOOGLE_OAUTH_ERROR",
		51030:  "CODE_GOOGLE_OAUTH_IS_USED",
		51031:  "CODE_UPCOMING_ID_ERROR",
		51032:  "CODE_CREATE_ERROR",
		51033:  "CODE_WATCH_EVENT_ERROR",
		51034:  "CODE_GET_EVENT_ERROR",
		51035:  "CODE_SS_FOR_REPEAT_FREQUENCY_PARAMS_ERROR",
		51036:  "CODE_FREE_SS_FOR_REPEAT_TIMES_USED_OUT",
		51037:  "CODE_SS_FOR_REPEAT_TIMES_OVER_LIMIT",
		51038:  "CODE_INVOICE_SET_TIPS_ERROR",
		51039:  "CODE_QUESTION_NAME_IS_EXIST",
		51040:  "CODE_INVOICE_NOT_FOUND",
		51041:  "CODE_PET_SIZE_IN_USED",
		51042:  "CODE_GROOMING_REPORT_NOT_AVAILABLE",
		51043:  "CODE_GROOMING_REPORT_BOOK_AGAIN_EXPIRED",
		51044:  "CODE_SERVICE_CHARGE_NOT_AVAILABLE",
		51045:  "CODE_EMPTY_PET_LIST",
		51046:  "CODE_EMPTY_SERVICE_LIST",
		51047:  "CODE_APPOINTMENT_TRACKING_NOT_FOUND",
		51048:  "CODE_APPOINTMENT_TRACKING_SHARED_BY_OTHER",
		51049:  "CODE_APPOINTMENT_TRACKING_STAFF_HAS_ANOTHER_SHARING_APPOINTMENT",
		51050:  "CODE_APPOINTMENT_TRACKING_STAFF_LOCATION_STATUS_CHANGE_NOT_ALLOWED",
		60000:  "CODE_BUSINESS_IS_EMPTY",
		60001:  "CODE_SUPPLIER_NOT_FOUND",
		60002:  "CODE_CATEGORY_NOT_FOUND",
		60003:  "CODE_PACKAGE_NOT_FOUND",
		60004:  "CODE_PRODUCT_NOT_FOUND",
		60005:  "CODE_CART_NOT_FOUND",
		60006:  "CODE_NAME_IS_EXIST",
		60007:  "CODE_SKU_IS_EXIST",
		60008:  "CODE_NOT_PAID",
		60009:  "CODE_INVALID_CART_ID",
		60010:  "CODE_CART_ALREADY_PROCESSING",
		60011:  "CODE_CART_ALREADY_COMPLETED",
		60012:  "CODE_INVALID_DISCOUNT_TYPE",
		60013:  "CODE_NAME_IS_EMPTY",
		60014:  "CODE_STAFF_IS_EMPTY",
		60015:  "CODE_BARCODE_IS_EXIST",
		70001:  "CODE_PAY_AMOUNT_INVALID",
		70002:  "CODE_PAY_MODULE_EMPTY",
		70003:  "CODE_PAY_INVOICE_ID_EMPTY",
		70004:  "CODE_PAY_METHOD_INVALID",
		70005:  "CODE_PAY_DATA_INVALID",
		70006:  "CODE_INVALID_CHECK_NUMBER",
		70007:  "CODE_REFUND_AMOUNT_INVALID",
		70008:  "CODE_STRIPE_INTENT_NOT_FOUND",
		70009:  "CODE_PAYMENT_NOT_FOUND",
		70011:  "CODE_STRIPE_ACCOUNT_NOT_FOUND",
		70012:  "CODE_SUBSCRIPTION_EXPIRATION",
		70013:  "CODE_SUBSCRIPTION_NOT_EXIST",
		70014:  "CODE_COMPANY_STATE_NOT_VALID",
		70015:  "CODE_STRIPE_ACCOUNT_ERROR",
		70016:  "CODE_REFUSE_TO_STRIPE_SET_UP",
		70017:  "CODE_SUBSCRIPTION_NOT_VALID",
		70018:  "CODE_REFUND_TIME_OUT",
		70019:  "CODE_STRIPE_CARD_EXCEPTION",
		70020:  "CODE_TERMINAL_READER_IN_PROGRESSING",
		70021:  "CODE_PRE_AUTHENTICATION_FAILED",
		70022:  "CODE_PRE_AUTHENTICATION_DUPLICATED",
		70023:  "CODE_PRE_AUTHENTICATION_STATUS_ERROR",
		70024:  "CODE_DEPOSIT_HAS_PAID_EXCEPTION",
		70025:  "CODE_A2P_NOW_ALLOW_EDITING",
		70026:  "CODE_A2P_CHARGE_FAILED",
		70027:  "CODE_PAYMENT_STATUS_EXCEPTION",
		70028:  "CODE_SUBSCRIPTION_UPDATE_FAILED",
		70029:  "CODE_STRIPE_COMPANY_CUSTOMER_NOT_FOUND",
		70030:  "CODE_STRIPE_UPDATE_CUSTOMER_EXCEPTION",
		70031:  "CODE_STRIPE_CREATE_HARDWARE_INVOICE_EXCEPTION",
		70032:  "CODE_STRIPE_CREATE_HARDWARE_ORDER_EXCEPTION",
		70033:  "CODE_PLATFORM_CARE_AGREEMENT_NOT_FOUND",
		70034:  "CODE_PLATFORM_CARE_RECORD_NOT_FOUND",
		70035:  "CODE_PLATFORM_CARE_RECORD_NOT_FOUND_BY_EMAIL",
		70036:  "CODE_PLATFORM_CARE_EMAIL_ADDRESS_MISMATCH",
		70037:  "CODE_PLATFORM_CARE_ASSIGN_AGREEMENT_FAILED",
		70038:  "CODE_PLATFORM_CARE_RECORD_IS_EXIST",
		70039:  "CODE_ENTERPRISE_STRIPE_CUSTOMER_IS_EXIST",
		70040:  "CODE_COMPANY_STRIPE_CUSTOMER_IS_EXIST",
		70041:  "CODE_TWILIO_FINE_CHARGE_FAIL",
		70042:  "CODE_MOEGO_PAY_NOT_SET_UP",
		70043:  "CODE_REFUND_NOT_FOUND",
		80100:  "CODE_MESSAGE_AUTO_TEMPLATE_NOT_CONFIG",
		80101:  "CODE_MESSAGE_SEND_PHONE_IS_NULL",
		80102:  "CODE_MESSAGE_SEND_EMAIL_IS_NULL",
		80103:  "CODE_MESSAGE_SEND_REVIEW_IS_NULL",
		80104:  "CODE_MESSAGE_SEND_PHONE_FAILED",
		80105:  "CODE_MESSAGE_SEND_EMAIL_FAILED",
		80106:  "CODE_MESSAGE_CANNOT_DELETE_OTHER_STAFF_MESSAGE",
		80107:  "CODE_CUSTOMER_PHONE_NUMBER_ERROR",
		80108:  "CODE_NOTIFICATION_TYPE_NOT_FOUND",
		80109:  "CODE_MESSAGE_AMOUNT_RUN_OUT",
		80110:  "CODE_CODE_SEND_LIMIT",
		80111:  "CODE_FAIL_TO_SEND_CODE",
		80112:  "CODE_BUSINESS_TWILIO_MESSAGE_NOE_MORE",
		80113:  "CODE_MESSAGE_CONTROL_NO_RECORD",
		80114:  "CODE_MESSAGE_SEND_FAILED",
		80115:  "CODE_MESSAGE_SEND_FAILED_BY_SWITCH_CLOSE",
		80116:  "CODE_MESSAGE_EMAIL_RECEIVE_NOT_FOUND_BUSINESS",
		80117:  "CODE_NOT_ALLOW_UPDATE_TWILIO_CALL_FORWARDING",
		80118:  "CODE_TWILIO_QUERY_ERROR",
		80119:  "CODE_INVALID_EMAIL_EVENT_TYPE",
		80120:  "CODE_INVALID_EMAIL_STATUS",
		80121:  "CODE_EMAIL_NOT_FOUND",
		80122:  "CODE_GROOMING_REPORT_SEND_FAILED",
		80123:  "CODE_NOT_ALLOW_SUBMIT_REVIEW",
		80124:  "CODE_ATP_DATA_FORBIDDEN_EDIT",
		80125:  "CODE_ATP_PN_TYPE_ERROR",
		80126:  "CODE_ATP_SUBMIT_RETRY",
		80127:  "CODE_ATP_BRAND_OTP_RETRY",
		80128:  "CODE_MESSAGE_NOT_FOUND",
		80129:  "CODE_PHONE_NUMBER_CANNOT_FORMATTED",
		90000:  "CODE_LINK_BUSINESS_NOT_FOUND",
		90001:  "CODE_INVITATION_CODE_NOT_FOUND",
		90002:  "CODE_LINK_PHONE_NUMBER_NOT_FOUND",
		90003:  "CODE_NO_LINKED_CUSTOMERS_FOUND",
		90004:  "CODE_LINKED_TOO_MANY_CUSTOMERS",
		90005:  "CODE_BRANDED_APP_NOT_FOUND",
		90006:  "CODE_CANCELLATION_NOT_ALLOWED",
		90007:  "CODE_RESCHEDULE_NOT_ALLOWED",
		95000:  "CODE_FINTECH_BUSINESS_APP_UPDATE_FORCE",
		95001:  "CODE_FINTECH_BUSINESS_APP_UPDATE_CLOSABLE",
		100000: "CODE_ACCOUNT_NOT_EXIST",
		100001: "CODE_PASSWORD_TOO_WEAK",
		100002: "CODE_EMAIL_CONFLICT",
		100003: "CODE_PHONE_NUMBER_CONFLICT",
		100004: "CODE_ACCOUNT_OR_PASSWORD_ERROR",
		100005: "CODE_OLD_PASSWORD_ERROR",
		100006: "CODE_ACCOUNT_ALREADY_RECOVERED",
		100007: "CODE_VERIFICATION_CODE_NOT_MATCH",
		100008: "CODE_ACCOUNT_FROZEN",
		101000: "CODE_SESSION_NOT_EXIST",
		101001: "CODE_SESSION_EXPIRED",
		101002: "CODE_SESSION_TOKEN_INVALID",
		102000: "CODE_PLATFORM_ACCOUNT_ALREADY_ASSOCIATED",
		120000: "CODE_CONVERSATION_NOT_EXIST",
		120001: "CODE_CONVERSATION_BUSINESS_NOT_MATCH",
		120003: "CODE_CONVERSATION_CLOSED",
		120004: "CODE_CONVERSATION_FAILED_TO_REPLY",
		120005: "CODE_CONVERSATION_BALANCE_NOT_ENOUGH",
		120006: "CODE_CONVERSATION_QUESTION_NOT_EXIST",
		120007: "CODE_CONVERSATION_QUESTION_NOT_MATCH",
		120008: "CODE_CONVERSATION_QUESTION_EXCESSIVE",
		130000: "CODE_GOOGLE_RECAPTCHA_INVALID",
		130001: "CODE_GOOGLE_RECAPTCHA_THRESHOLD_NOT_REACHED",
		130002: "CODE_GOOGLE_RECAPTCHA_ACTION_NOT_FOUND",
		130003: "CODE_VERIFICATION_CODE_SENT_COUNT_LIMITED",
		130004: "CODE_VERIFICATION_CODE_SEND_INTERVAL_NOT_REACHED",
		130005: "CODE_INVALID_VERIFICATION_CODE",
		130006: "CODE_VERIFICATION_CODE_EXPIRED",
		140000: "CODE_FILE_RECORD_NOT_EXIST",
		140001: "CODE_FILE_OBJECT_NOT_EXIST",
		140002: "CODE_FILE_EXT_FORBIDDEN",
		150000: "CODE_ROLE_NAME_ALREADY_EXISTS",
		150001: "CODE_ROLE_NOT_FOUND",
		160000: "CODE_LODGING_TYPE_IN_USE",
		160001: "CODE_LODGING_TYPE_NOT_FOUND",
		160002: "CODE_LODGING_UNIT_IN_USE",
		170000: "CODE_FINANCE_NO_ACCESS_INTERNAL_API",
		171000: "CODE_CAPITAL_OFFER_NOT_FOUND",
		171001: "CODE_CAPITAL_OFFER_NO_ACCESS",
		171002: "CODE_CAPITAL_ILLEGAL_OFFER_STATUS",
		171003: "CODE_CAPITAL_CHANNEL_ACCOUNT_NOT_FOUND",
		171004: "CODE_CAPITAL_DB_ERROR",
		171005: "CODE_CAPITAL_GET_LOCK_FAILED",
		171006: "CODE_CAPITAL_LOCKED_BY_OTHER",
		171007: "CODE_CAPITAL_FULLY_PAID_WITH_REMAINING",
		171008: "CODE_CAPITAL_UNKNOWN_TRANSACTION_TYPE",
		171009: "CODE_CAPITAL_INTERVAL_PAID_AMOUNT_MISMATCH",
		171010: "CODE_CAPITAL_OFFER_REMAINING_AMOUNT_MISMATCH",
		171011: "CODE_CAPITAL_TRANSACTION_NOT_FOUND",
		171012: "CODE_CAPITAL_CHANNEL_STATUS_UNKNOWN",
		171013: "CODE_CAPITAL_STRIPE_API_ERROR",
		171014: "CODE_CAPITAL_CACHE_ERROR",
		171015: "CODE_CAPITAL_ILLEGAL_STATUS_TRANSITION",
		171016: "CODE_CAPITAL_NETWORK_ERROR",
		171017: "CODE_CAPITAL_UNMARSHAL_ERROR",
		171018: "CODE_CAPITAL_UNKNOWN_ENTITY_TYPE",
		171019: "CODE_CAPITAL_KANMON_API_ERROR",
		171020: "CODE_CAPITAL_CHANNEL_UNKNOWN",
		171021: "CODE_CAPITAL_PROCESSING_AMOUNT_GREATER_THAN_REMAINING",
		171022: "CODE_CAPITAL_FAILED_TO_DELIVER_MESSAGE",
		172000: "CODE_FINANCE_GW_INVALID_STRIPE_WEBHOOK_PAYLOAD",
		172001: "CODE_FINANCE_GW_DOWNSTREAM_ERROR",
		172002: "CODE_FINANCE_GW_INVALID_KANMON_WEBHOOK_PAYLOAD",
		172003: "CODE_FINANCE_GW_UNKNOWN_OFFER_TYPE",
		172004: "CODE_FINANCE_GW_UNKNOWN_ACCOUNT_EVENT_TYPE",
		172005: "CODE_FINANCE_GW_UNKNOWN_OFFER_EVENT_TYPE",
		172006: "CODE_FINANCE_GW_UNKNOWN_TRANSACTION_EVENT_TYPE",
		172007: "CODE_FINANCE_GW_INVALID_KANMON_WEBHOOK_SECRET",
		172008: "CODE_FINANCE_GW_SIGNATURE_VERIFICATION_FAILED",
		172009: "CODE_FINANCE_GW_PAYLOAD_UNMARSHAL_ERROR",
		180000: "CODE_REST_IO",
		180001: "CODE_REST_DOWNSTREAM_ERROR",
		190000: "CODE_FINANCE_TOOLS_INVALID_TIME_RANGE",
		190001: "CODE_FINANCE_TOOLS_PAYMENTS_TOTAL_UNMATCHED",
		190002: "CODE_FINANCE_TOOLS_ADJUSTMENTS_TOTAL_UNMATCHED",
		200000: "CODE_SPLIT_PAYMENT_ACCEPTANCE_FAILED",
		200001: "CODE_SPLIT_PAYMENT_REVERSAL_ACCEPTANCE_FAILED",
		210000: "CODE_ACCOUNTING_UNKNOWN_ERROR",
		210001: "CODE_ACCOUNTING_DATA_ACCESS_ERROR",
		210002: "CODE_ACCOUNTING_THIRD_PARTY_API_ERROR",
		210003: "CODE_ACCOUNTING_RPC_CALL_ERROR",
		210004: "CODE_ACCOUNTING_DATA_SYNCER_NOT_FOUND_ERROR",
		210005: "CODE_ACCOUNTING_INTERNAL_BIZ_ERROR",
		210006: "CODE_ACCOUNTING_UNSUBSCRIBED_ERROR",
		220000: "CODE_PAYMENT_UNKNOWN_ERROR",
		220001: "CODE_PAYMENT_DATA_ACCESS_ERROR",
		220002: "CODE_PAYMENT_INTERNAL_ERROR",
		220003: "CODE_PAYMENT_INVALID_CHANNEL",
		220004: "CODE_PAYMENT_INVALID_VERIFICATION_STATUS",
		220005: "CODE_PAYMENT_NO_LOCATION",
		220006: "CODE_PAYMENT_GET_COMPANY_FAILED",
		220007: "CODE_PAYMENT_GET_CHANNEL_ACCOUNT_FAILED",
		220008: "CODE_PAYMENT_ADYEN_GET_ACCOUNT_HOLDER_FAILED",
		220009: "CODE_PAYMENT_LIST_BUSINESSES_FAILED",
		220010: "CODE_PAYMENT_ADYEN_CREATE_LEGAL_ENTITY_FAILED",
		220011: "CODE_PAYMENT_ADYEN_CREATE_ACCOUNT_HOLDER_FAILED",
		220012: "CODE_PAYMENT_ADYEN_CREATE_STORE_FAILED",
		220013: "CODE_PAYMENT_ADYEN_CREATE_BALANCE_ACCOUNT_FAILED",
		220014: "CODE_PAYMENT_CREATE_CHANNEL_ACCOUNT_FAILED",
		220015: "CODE_PAYMENT_UPDATE_CHANNEL_ACCOUNT_FAILED",
		220016: "CODE_PAYMENT_ONBOARD_INVALID_PRE_CONFIG",
		220017: "CODE_PAYMENT_ADYEN_CREATE_BUSINESS_LINE_FAILED",
		220018: "CODE_PAYMENT_ADYEN_CREATE_ONBOARD_LINK_FAILED",
		220019: "CODE_PAYMENT_STATUS_INVALID",
		220020: "CODE_PAYMENT_THIRD_PARTY_API_ERROR",
		220021: "CODE_PAYMENT_RPC_CALL_ERROR",
		220022: "CODE_PAYMENT_INVALID_READER",
		220023: "CODE_PAYMENT_ADYEN_TERMINAL_REQUEST_ERROR",
		220024: "CODE_PAYMENT_ADYEN_GET_STORE_FAILED",
		220025: "CODE_PAYMENT_ADYEN_GET_BALANCE_ACCOUNT_FAILED",
		220026: "CODE_PAYMENT_GET_ACCOUNT_FAILED",
		220027: "CODE_PAYMENT_ADYEN_ADD_PAYMENT_METHODS_FAILED",
		220028: "CODE_PAYMENT_ONBOARD_ALREADY_FINISHED",
		220029: "CODE_PAYMENT_ADYEN_INVALID_ONBOARD_STEP",
		220030: "CODE_PAYMENT_ADYEN_GET_LEGAL_ENTITY_FAILED",
		220031: "CODE_PAYMENT_ADYEN_GET_TRANSFER_INSTRUMENT_FAILED",
		220032: "CODE_PAYMENT_CREATE_PAYMENT_SETTING_FAILED",
		220033: "CODE_PAYMENT_ADYEN_GET_PAYMENT_METHODS_FAILED",
		220034: "CODE_PAYMENT_CHANNEL_ACCOUNT_NOT_FOUND",
		221001: "CODE_PAYMENT_PAYOUT_NOT_FOUND",
		221002: "CODE_PAYMENT_PAYOUT_GET_FAILED",
		221003: "CODE_PAYMENT_PAYOUT_CREATE_FAILED",
		222001: "CODE_BILLING_UNKNOWN_ERROR",
		222002: "CODE_BILLING_DATA_ACCESS_ERROR",
		222003: "CODE_BILLING_INTERNAL_ERROR",
	}
	Code_value = map[string]int32{
		"CODE_UNSPECIFIED":                                                   0,
		"CODE_SUCCESS":                                                       200,
		"CODE_PARAMS_ERROR":                                                  400,
		"CODE_UNAUTHORIZED_ERROR":                                            401,
		"CODE_FORBIDDEN":                                                     403,
		"CODE_TOO_MANY_REQUESTS":                                             429,
		"CODE_SERVER_ERROR":                                                  500,
		"CODE_ORDERID_EXISTS":                                                10010,
		"CODE_ROOM_ERROR":                                                    10015,
		"CODE_PRIMARY_ID_EMPTY":                                              10016,
		"CODE_VERIFY_CODE_NOT_EQUAL":                                         10017,
		"CODE_PARALLEL_ERROR":                                                10018,
		"CODE_BRANCH_MISMATCH":                                               10999,
		"CODE_ASSIGN_ITEM_AMOUNT_ONCE_ONLY_ERROR":                            11001,
		"CODE_ASSIGN_ITEM_AMOUNT_DATA_PERMISSION_ERROR":                      11002,
		"CODE_CHECK_IN_ERROR":                                                20001,
		"CODE_CHECK_OUT_ERROR":                                               20002,
		"CODE_PASSWORD_ERROR":                                                20003,
		"CODE_ACCOUNT_NOT_FOUND":                                             20004,
		"CODE_INVALID_CODE":                                                  20005,
		"CODE_STAFF_BINDING_ERROR1":                                          20006,
		"CODE_STAFF_BINDING_ERROR2":                                          20007,
		"CODE_STAFF_BINDING_ERROR3":                                          20008,
		"CODE_STAFF_BINDING_ERROR4":                                          20009,
		"CODE_DELETE_ROLE_ERROR":                                             20010,
		"CODE_PAYMENT_METHOD_NAME_EXISTS":                                    20011,
		"CODE_DEFAULT_METHOD_OPER_FALSE":                                     20012,
		"CODE_FILE_SIZE_EXCEEDS_LIMIT":                                       20013,
		"CODE_TAX_IS_USED":                                                   20014,
		"CODE_TAG_NAME_EXIST":                                                20015,
		"CODE_TAG_NAME_NULL":                                                 20016,
		"CODE_THE_LAST_DELETED":                                              20017,
		"CODE_THE_LAST_INACTIVE":                                             20018,
		"CODE_PERMISSION_NOT_ENOUGH":                                         20019,
		"CODE_NO_PERMISSION_SHOW_MESSAGE_THREAD":                             20020,
		"CODE_TAX_IS_USED_RETAIL":                                            20021,
		"CODE_NOT_ENOUGH_VANS_NUM":                                           20022,
		"CODE_PASSWORD_UPDATE_OLDPWD_ERROR":                                  20023,
		"CODE_ACCOUNT_NOT_OWN_THIS_COMPANY":                                  20024,
		"CODE_CHECK_EMAIL_EXIST_PHP_VERSION":                                 20025,
		"CODE_COMPANY_NOT_FOUND":                                             20026,
		"CODE_SWITCH_BUSINESS_ERROR":                                         20027,
		"CODE_STAFF_ONLY_BANDING_ONE_VAN":                                    20028,
		"CODE_STAFF_NOT_FOUND_IN_FROM_VAN":                                   20029,
		"CODE_STAFF_FOUND_IN_TO_VAN":                                         20030,
		"CODE_PROCESSING_FEE_SHOULD_IN_STRIPE_PAY":                           20031,
		"CODE_PROCESSING_FEE_SHOULD_IN_US_AREA":                              20032,
		"CODE_STAFF_UNLINK_REASON_OWNER":                                     20033,
		"CODE_STAFF_UNLINK_REASON_NO_LINK":                                   20034,
		"CODE_STAFF_UNLINK_REASON_HAS_BEEN_DELETED":                          20035,
		"CODE_CAN_NOT_CREATE_STAFF":                                          20036,
		"CODE_NOT_ENOUGH_VANS_NUM2":                                          20037,
		"CODE_SMART_SCHEDULE_RULE_ALREADY_EXISTS":                            20038,
		"CODE_BUSINESS_NOT_FOUND":                                            20039,
		"CODE_ACCOUNT_NOT_OWN_THIS_ENTERPRISE":                               20040,
		"CODE_ENTERPRISE_NOT_OWN_THIS_COMPANY":                               20041,
		"CODE_ENTERPRISE_NOT_FOUND":                                          20042,
		"CODE_COMPANY_REACH_MAX_LIMIT":                                       20043,
		"CODE_SWITCH_COMPANY_ERROR":                                          20044,
		"CODE_STAFF_NOT_FOUND":                                               20045,
		"CODE_STAFF_WORKING_LOCATION_LIST_IS_EMPTY":                          20046,
		"CODE_STAFF_BINDING_EMAIL_INCORRECT":                                 20047,
		"CODE_STAFF_LOGIN_LIMIT":                                             20048,
		"CODE_EMAIL_NOT_EXIST":                                               30000,
		"CODE_EMAIL_EXIST":                                                   30001,
		"CODE_PHONE_EXIST":                                                   30002,
		"CODE_CUSTOMER_NOT_FOUND":                                            30003,
		"CODE_PET_NOT_FOUND":                                                 30004,
		"CODE_APPOINTMENT_NOT_FOUND":                                         30005,
		"CODE_PET_HAVE_APPOINTMENT":                                          30006,
		"CODE_APPOINTMENT_SIGN_OVER":                                         30007,
		"CODE_APPOINTMENT_PET_NOT_EXIST":                                     30008,
		"CODE_APPOINTMENT_AGREEMENT_SEND_CONTENT_NOT_CONFIG":                 30009,
		"CODE_PET_CODE_EXISTS":                                               30010,
		"CODE_EMAIL_EXISTS_MULTI":                                            30011,
		"CODE_BIRTHDAY_FORMAT_ERROR":                                         30012,
		"CODE_CUSTOMER_IS_BLOCKED":                                           30013,
		"CODE_CUSTOMER_ALREADY_EXISTS":                                       30014,
		"CODE_INTAKE_FORM_IS_DELETED":                                        30015,
		"CODE_INTAKE_FORM_MESSAGE_TOO_LONG":                                  30016,
		"CODE_PET_BREED_NOT_ALLOW_DELETE":                                    30017,
		"CODE_CUSTOMER_ACCOUNT_OR_PASSWORD_ERROR":                            30018,
		"CODE_CUSTOMER_ACCOUNT_NOT_FOUND":                                    30019,
		"CODE_CUSTOMER_SESSION_NOT_FOUND":                                    30020,
		"CODE_CUSTOMER_INVALID_CODE":                                         30021,
		"CODE_PET_CODE_DESCRIPTION_TOO_LONG":                                 30022,
		"CODE_EMAIL_OR_CODE_ERROR":                                           30023,
		"CODE_PHONE_OR_CODE_ERROR":                                           30024,
		"CODE_LICENSE_INVALID":                                               30025,
		"CODE_PHONE_INVALID":                                                 30026,
		"CODE_EMAIL_INVALID":                                                 30027,
		"CODE_CUSTOMER_CREATION_FROM_GIVEN_SOURCE_IS_DISABLED":               30028,
		"CODE_CUSTOMER_MERGING":                                              30029,
		"CODE_CUSTOMER_NOT_DUPLICATE":                                        30030,
		"CODE_ADDRESS_NOT_FOUND":                                             30100,
		"CODE_CANNOT_DELETE_PRIMARY_ADDRESS":                                 30101,
		"CODE_INVALID_LATITUDE_OR_LONGITUDE":                                 30102,
		"CODE_ADDRESS_IS_EMPTY":                                              30103,
		"CODE_TOO_MANY_ADDRESSES":                                            30104,
		"CODE_BOOK_ONLINE_NAME_INVALID":                                      40404,
		"CODE_BOOK_ONLINE_NOT_ENABLE":                                        40070,
		"CODE_AGREEMENT_NOT_CONFIRM":                                         40090,
		"CODE_SIGNATURE_IS_EMPTY":                                            40091,
		"CODE_ACCEPT_TYPE_NOW_ALLOWED":                                       40092,
		"CODE_CANCEL_POLICY_NOT_CONFIRMED":                                   40093,
		"CODE_CUSTOMER_NOT_FOUND_FOR_OB":                                     40094,
		"CODE_STRIPE_CARD_TOKEN_IS_EMPTY":                                    40095,
		"CODE_APPOINTMENT_TIME_IS_NOT_AVAILABLE":                             40096,
		"CODE_APPOINTMENT_CANCELED_INVOICE_INVALID":                          40097,
		"CODE_APPOINTMENT_TIME_LOCKED":                                       40098,
		"CODE_OB_DEPOSIT_NOT_PAID":                                           40099,
		"CODE_BOOK_ONLINE_DOMAIN_INVALID":                                    40100,
		"CODE_BOOK_ONLINE_SITE_NOT_FOUND":                                    40101,
		"CODE_SERVICE_CATEGORY_NAME_IS_EXIST":                                51001,
		"CODE_SERVICE_NAME_IS_EXIST":                                         51002,
		"CODE_SERVICE_CATEGORY_NOT_FOUND":                                    51003,
		"CODE_INVALID_VALUE_TYPE":                                            51004,
		"CODE_APPLY_PACKAGE_CHANGED":                                         51005,
		"CODE_INVOICE_INVALID_STATUS":                                        51006,
		"CODE_SERVICE_NOT_FOUND":                                             51007,
		"CODE_APPOINTMENT_INVALID_STATUS":                                    51008,
		"CODE_SERVICE_HAVE_BINDING":                                          51009,
		"CODE_NO_AVAILABLE_SERVICE_SELECTED":                                 51010,
		"CODE_NO_CUSTOMER_ADDRESS":                                           51011,
		"CODE_TOO_MANY_DAYS_TO_QUERY":                                        51012,
		"CODE_NO_AVAILABLE_STAFF_WHEN_SUBMIT":                                51013,
		"CODE_CREDIT_CARD_NEED_OPEN":                                         51014,
		"CODE_GOOGLE_INVALID_ADDRESS":                                        51015,
		"CODE_TASK_FAILURE_GENERAL":                                          51016,
		"CODE_TASK_MESSAGE_RESET_FAILURE":                                    51017,
		"CODE_TOO_MANY_APPOINTMENTS":                                         51018,
		"CODE_SERVICE_CATEGORY_NAME_IS_TOO_LONG":                             51019,
		"CODE_REPEAT_IS_NOT_FOUND":                                           51020,
		"CODE_QUICKBOOKS_DEV_ERROR":                                          51021,
		"CODE_QUICKBOOKS_OAUTH_ERROR":                                        51022,
		"CODE_QUICKBOOKS_REFRESH_TOKEN_ERROR":                                51023,
		"CODE_QUICKBOOKS_UNEXPECTED_EXCEPTION":                               51024,
		"CODE_QUICKBOOKS_SETTING_ERROR":                                      51025,
		"CODE_QUICKBOOKS_DATA_DUPLICATE_NAME_EXISTS":                         51026,
		"CODE_QUICKBOOKS_DATA_DUPLICATE_EMAIL_FORMAT_ERROR":                  51027,
		"CODE_GOOGLE_OAUTH_CHECK_ERROR":                                      51028,
		"CODE_GOOGLE_OAUTH_ERROR":                                            51029,
		"CODE_GOOGLE_OAUTH_IS_USED":                                          51030,
		"CODE_UPCOMING_ID_ERROR":                                             51031,
		"CODE_CREATE_ERROR":                                                  51032,
		"CODE_WATCH_EVENT_ERROR":                                             51033,
		"CODE_GET_EVENT_ERROR":                                               51034,
		"CODE_SS_FOR_REPEAT_FREQUENCY_PARAMS_ERROR":                          51035,
		"CODE_FREE_SS_FOR_REPEAT_TIMES_USED_OUT":                             51036,
		"CODE_SS_FOR_REPEAT_TIMES_OVER_LIMIT":                                51037,
		"CODE_INVOICE_SET_TIPS_ERROR":                                        51038,
		"CODE_QUESTION_NAME_IS_EXIST":                                        51039,
		"CODE_INVOICE_NOT_FOUND":                                             51040,
		"CODE_PET_SIZE_IN_USED":                                              51041,
		"CODE_GROOMING_REPORT_NOT_AVAILABLE":                                 51042,
		"CODE_GROOMING_REPORT_BOOK_AGAIN_EXPIRED":                            51043,
		"CODE_SERVICE_CHARGE_NOT_AVAILABLE":                                  51044,
		"CODE_EMPTY_PET_LIST":                                                51045,
		"CODE_EMPTY_SERVICE_LIST":                                            51046,
		"CODE_APPOINTMENT_TRACKING_NOT_FOUND":                                51047,
		"CODE_APPOINTMENT_TRACKING_SHARED_BY_OTHER":                          51048,
		"CODE_APPOINTMENT_TRACKING_STAFF_HAS_ANOTHER_SHARING_APPOINTMENT":    51049,
		"CODE_APPOINTMENT_TRACKING_STAFF_LOCATION_STATUS_CHANGE_NOT_ALLOWED": 51050,
		"CODE_BUSINESS_IS_EMPTY":                                             60000,
		"CODE_SUPPLIER_NOT_FOUND":                                            60001,
		"CODE_CATEGORY_NOT_FOUND":                                            60002,
		"CODE_PACKAGE_NOT_FOUND":                                             60003,
		"CODE_PRODUCT_NOT_FOUND":                                             60004,
		"CODE_CART_NOT_FOUND":                                                60005,
		"CODE_NAME_IS_EXIST":                                                 60006,
		"CODE_SKU_IS_EXIST":                                                  60007,
		"CODE_NOT_PAID":                                                      60008,
		"CODE_INVALID_CART_ID":                                               60009,
		"CODE_CART_ALREADY_PROCESSING":                                       60010,
		"CODE_CART_ALREADY_COMPLETED":                                        60011,
		"CODE_INVALID_DISCOUNT_TYPE":                                         60012,
		"CODE_NAME_IS_EMPTY":                                                 60013,
		"CODE_STAFF_IS_EMPTY":                                                60014,
		"CODE_BARCODE_IS_EXIST":                                              60015,
		"CODE_PAY_AMOUNT_INVALID":                                            70001,
		"CODE_PAY_MODULE_EMPTY":                                              70002,
		"CODE_PAY_INVOICE_ID_EMPTY":                                          70003,
		"CODE_PAY_METHOD_INVALID":                                            70004,
		"CODE_PAY_DATA_INVALID":                                              70005,
		"CODE_INVALID_CHECK_NUMBER":                                          70006,
		"CODE_REFUND_AMOUNT_INVALID":                                         70007,
		"CODE_STRIPE_INTENT_NOT_FOUND":                                       70008,
		"CODE_PAYMENT_NOT_FOUND":                                             70009,
		"CODE_STRIPE_ACCOUNT_NOT_FOUND":                                      70011,
		"CODE_SUBSCRIPTION_EXPIRATION":                                       70012,
		"CODE_SUBSCRIPTION_NOT_EXIST":                                        70013,
		"CODE_COMPANY_STATE_NOT_VALID":                                       70014,
		"CODE_STRIPE_ACCOUNT_ERROR":                                          70015,
		"CODE_REFUSE_TO_STRIPE_SET_UP":                                       70016,
		"CODE_SUBSCRIPTION_NOT_VALID":                                        70017,
		"CODE_REFUND_TIME_OUT":                                               70018,
		"CODE_STRIPE_CARD_EXCEPTION":                                         70019,
		"CODE_TERMINAL_READER_IN_PROGRESSING":                                70020,
		"CODE_PRE_AUTHENTICATION_FAILED":                                     70021,
		"CODE_PRE_AUTHENTICATION_DUPLICATED":                                 70022,
		"CODE_PRE_AUTHENTICATION_STATUS_ERROR":                               70023,
		"CODE_DEPOSIT_HAS_PAID_EXCEPTION":                                    70024,
		"CODE_A2P_NOW_ALLOW_EDITING":                                         70025,
		"CODE_A2P_CHARGE_FAILED":                                             70026,
		"CODE_PAYMENT_STATUS_EXCEPTION":                                      70027,
		"CODE_SUBSCRIPTION_UPDATE_FAILED":                                    70028,
		"CODE_STRIPE_COMPANY_CUSTOMER_NOT_FOUND":                             70029,
		"CODE_STRIPE_UPDATE_CUSTOMER_EXCEPTION":                              70030,
		"CODE_STRIPE_CREATE_HARDWARE_INVOICE_EXCEPTION":                      70031,
		"CODE_STRIPE_CREATE_HARDWARE_ORDER_EXCEPTION":                        70032,
		"CODE_PLATFORM_CARE_AGREEMENT_NOT_FOUND":                             70033,
		"CODE_PLATFORM_CARE_RECORD_NOT_FOUND":                                70034,
		"CODE_PLATFORM_CARE_RECORD_NOT_FOUND_BY_EMAIL":                       70035,
		"CODE_PLATFORM_CARE_EMAIL_ADDRESS_MISMATCH":                          70036,
		"CODE_PLATFORM_CARE_ASSIGN_AGREEMENT_FAILED":                         70037,
		"CODE_PLATFORM_CARE_RECORD_IS_EXIST":                                 70038,
		"CODE_ENTERPRISE_STRIPE_CUSTOMER_IS_EXIST":                           70039,
		"CODE_COMPANY_STRIPE_CUSTOMER_IS_EXIST":                              70040,
		"CODE_TWILIO_FINE_CHARGE_FAIL":                                       70041,
		"CODE_MOEGO_PAY_NOT_SET_UP":                                          70042,
		"CODE_REFUND_NOT_FOUND":                                              70043,
		"CODE_MESSAGE_AUTO_TEMPLATE_NOT_CONFIG":                              80100,
		"CODE_MESSAGE_SEND_PHONE_IS_NULL":                                    80101,
		"CODE_MESSAGE_SEND_EMAIL_IS_NULL":                                    80102,
		"CODE_MESSAGE_SEND_REVIEW_IS_NULL":                                   80103,
		"CODE_MESSAGE_SEND_PHONE_FAILED":                                     80104,
		"CODE_MESSAGE_SEND_EMAIL_FAILED":                                     80105,
		"CODE_MESSAGE_CANNOT_DELETE_OTHER_STAFF_MESSAGE":                     80106,
		"CODE_CUSTOMER_PHONE_NUMBER_ERROR":                                   80107,
		"CODE_NOTIFICATION_TYPE_NOT_FOUND":                                   80108,
		"CODE_MESSAGE_AMOUNT_RUN_OUT":                                        80109,
		"CODE_CODE_SEND_LIMIT":                                               80110,
		"CODE_FAIL_TO_SEND_CODE":                                             80111,
		"CODE_BUSINESS_TWILIO_MESSAGE_NOE_MORE":                              80112,
		"CODE_MESSAGE_CONTROL_NO_RECORD":                                     80113,
		"CODE_MESSAGE_SEND_FAILED":                                           80114,
		"CODE_MESSAGE_SEND_FAILED_BY_SWITCH_CLOSE":                           80115,
		"CODE_MESSAGE_EMAIL_RECEIVE_NOT_FOUND_BUSINESS":                      80116,
		"CODE_NOT_ALLOW_UPDATE_TWILIO_CALL_FORWARDING":                       80117,
		"CODE_TWILIO_QUERY_ERROR":                                            80118,
		"CODE_INVALID_EMAIL_EVENT_TYPE":                                      80119,
		"CODE_INVALID_EMAIL_STATUS":                                          80120,
		"CODE_EMAIL_NOT_FOUND":                                               80121,
		"CODE_GROOMING_REPORT_SEND_FAILED":                                   80122,
		"CODE_NOT_ALLOW_SUBMIT_REVIEW":                                       80123,
		"CODE_ATP_DATA_FORBIDDEN_EDIT":                                       80124,
		"CODE_ATP_PN_TYPE_ERROR":                                             80125,
		"CODE_ATP_SUBMIT_RETRY":                                              80126,
		"CODE_ATP_BRAND_OTP_RETRY":                                           80127,
		"CODE_MESSAGE_NOT_FOUND":                                             80128,
		"CODE_PHONE_NUMBER_CANNOT_FORMATTED":                                 80129,
		"CODE_LINK_BUSINESS_NOT_FOUND":                                       90000,
		"CODE_INVITATION_CODE_NOT_FOUND":                                     90001,
		"CODE_LINK_PHONE_NUMBER_NOT_FOUND":                                   90002,
		"CODE_NO_LINKED_CUSTOMERS_FOUND":                                     90003,
		"CODE_LINKED_TOO_MANY_CUSTOMERS":                                     90004,
		"CODE_BRANDED_APP_NOT_FOUND":                                         90005,
		"CODE_CANCELLATION_NOT_ALLOWED":                                      90006,
		"CODE_RESCHEDULE_NOT_ALLOWED":                                        90007,
		"CODE_FINTECH_BUSINESS_APP_UPDATE_FORCE":                             95000,
		"CODE_FINTECH_BUSINESS_APP_UPDATE_CLOSABLE":                          95001,
		"CODE_ACCOUNT_NOT_EXIST":                                             100000,
		"CODE_PASSWORD_TOO_WEAK":                                             100001,
		"CODE_EMAIL_CONFLICT":                                                100002,
		"CODE_PHONE_NUMBER_CONFLICT":                                         100003,
		"CODE_ACCOUNT_OR_PASSWORD_ERROR":                                     100004,
		"CODE_OLD_PASSWORD_ERROR":                                            100005,
		"CODE_ACCOUNT_ALREADY_RECOVERED":                                     100006,
		"CODE_VERIFICATION_CODE_NOT_MATCH":                                   100007,
		"CODE_ACCOUNT_FROZEN":                                                100008,
		"CODE_SESSION_NOT_EXIST":                                             101000,
		"CODE_SESSION_EXPIRED":                                               101001,
		"CODE_SESSION_TOKEN_INVALID":                                         101002,
		"CODE_PLATFORM_ACCOUNT_ALREADY_ASSOCIATED":                           102000,
		"CODE_CONVERSATION_NOT_EXIST":                                        120000,
		"CODE_CONVERSATION_BUSINESS_NOT_MATCH":                               120001,
		"CODE_CONVERSATION_CLOSED":                                           120003,
		"CODE_CONVERSATION_FAILED_TO_REPLY":                                  120004,
		"CODE_CONVERSATION_BALANCE_NOT_ENOUGH":                               120005,
		"CODE_CONVERSATION_QUESTION_NOT_EXIST":                               120006,
		"CODE_CONVERSATION_QUESTION_NOT_MATCH":                               120007,
		"CODE_CONVERSATION_QUESTION_EXCESSIVE":                               120008,
		"CODE_GOOGLE_RECAPTCHA_INVALID":                                      130000,
		"CODE_GOOGLE_RECAPTCHA_THRESHOLD_NOT_REACHED":                        130001,
		"CODE_GOOGLE_RECAPTCHA_ACTION_NOT_FOUND":                             130002,
		"CODE_VERIFICATION_CODE_SENT_COUNT_LIMITED":                          130003,
		"CODE_VERIFICATION_CODE_SEND_INTERVAL_NOT_REACHED":                   130004,
		"CODE_INVALID_VERIFICATION_CODE":                                     130005,
		"CODE_VERIFICATION_CODE_EXPIRED":                                     130006,
		"CODE_FILE_RECORD_NOT_EXIST":                                         140000,
		"CODE_FILE_OBJECT_NOT_EXIST":                                         140001,
		"CODE_FILE_EXT_FORBIDDEN":                                            140002,
		"CODE_ROLE_NAME_ALREADY_EXISTS":                                      150000,
		"CODE_ROLE_NOT_FOUND":                                                150001,
		"CODE_LODGING_TYPE_IN_USE":                                           160000,
		"CODE_LODGING_TYPE_NOT_FOUND":                                        160001,
		"CODE_LODGING_UNIT_IN_USE":                                           160002,
		"CODE_FINANCE_NO_ACCESS_INTERNAL_API":                                170000,
		"CODE_CAPITAL_OFFER_NOT_FOUND":                                       171000,
		"CODE_CAPITAL_OFFER_NO_ACCESS":                                       171001,
		"CODE_CAPITAL_ILLEGAL_OFFER_STATUS":                                  171002,
		"CODE_CAPITAL_CHANNEL_ACCOUNT_NOT_FOUND":                             171003,
		"CODE_CAPITAL_DB_ERROR":                                              171004,
		"CODE_CAPITAL_GET_LOCK_FAILED":                                       171005,
		"CODE_CAPITAL_LOCKED_BY_OTHER":                                       171006,
		"CODE_CAPITAL_FULLY_PAID_WITH_REMAINING":                             171007,
		"CODE_CAPITAL_UNKNOWN_TRANSACTION_TYPE":                              171008,
		"CODE_CAPITAL_INTERVAL_PAID_AMOUNT_MISMATCH":                         171009,
		"CODE_CAPITAL_OFFER_REMAINING_AMOUNT_MISMATCH":                       171010,
		"CODE_CAPITAL_TRANSACTION_NOT_FOUND":                                 171011,
		"CODE_CAPITAL_CHANNEL_STATUS_UNKNOWN":                                171012,
		"CODE_CAPITAL_STRIPE_API_ERROR":                                      171013,
		"CODE_CAPITAL_CACHE_ERROR":                                           171014,
		"CODE_CAPITAL_ILLEGAL_STATUS_TRANSITION":                             171015,
		"CODE_CAPITAL_NETWORK_ERROR":                                         171016,
		"CODE_CAPITAL_UNMARSHAL_ERROR":                                       171017,
		"CODE_CAPITAL_UNKNOWN_ENTITY_TYPE":                                   171018,
		"CODE_CAPITAL_KANMON_API_ERROR":                                      171019,
		"CODE_CAPITAL_CHANNEL_UNKNOWN":                                       171020,
		"CODE_CAPITAL_PROCESSING_AMOUNT_GREATER_THAN_REMAINING":              171021,
		"CODE_CAPITAL_FAILED_TO_DELIVER_MESSAGE":                             171022,
		"CODE_FINANCE_GW_INVALID_STRIPE_WEBHOOK_PAYLOAD":                     172000,
		"CODE_FINANCE_GW_DOWNSTREAM_ERROR":                                   172001,
		"CODE_FINANCE_GW_INVALID_KANMON_WEBHOOK_PAYLOAD":                     172002,
		"CODE_FINANCE_GW_UNKNOWN_OFFER_TYPE":                                 172003,
		"CODE_FINANCE_GW_UNKNOWN_ACCOUNT_EVENT_TYPE":                         172004,
		"CODE_FINANCE_GW_UNKNOWN_OFFER_EVENT_TYPE":                           172005,
		"CODE_FINANCE_GW_UNKNOWN_TRANSACTION_EVENT_TYPE":                     172006,
		"CODE_FINANCE_GW_INVALID_KANMON_WEBHOOK_SECRET":                      172007,
		"CODE_FINANCE_GW_SIGNATURE_VERIFICATION_FAILED":                      172008,
		"CODE_FINANCE_GW_PAYLOAD_UNMARSHAL_ERROR":                            172009,
		"CODE_REST_IO":                                                       180000,
		"CODE_REST_DOWNSTREAM_ERROR":                                         180001,
		"CODE_FINANCE_TOOLS_INVALID_TIME_RANGE":                              190000,
		"CODE_FINANCE_TOOLS_PAYMENTS_TOTAL_UNMATCHED":                        190001,
		"CODE_FINANCE_TOOLS_ADJUSTMENTS_TOTAL_UNMATCHED":                     190002,
		"CODE_SPLIT_PAYMENT_ACCEPTANCE_FAILED":                               200000,
		"CODE_SPLIT_PAYMENT_REVERSAL_ACCEPTANCE_FAILED":                      200001,
		"CODE_ACCOUNTING_UNKNOWN_ERROR":                                      210000,
		"CODE_ACCOUNTING_DATA_ACCESS_ERROR":                                  210001,
		"CODE_ACCOUNTING_THIRD_PARTY_API_ERROR":                              210002,
		"CODE_ACCOUNTING_RPC_CALL_ERROR":                                     210003,
		"CODE_ACCOUNTING_DATA_SYNCER_NOT_FOUND_ERROR":                        210004,
		"CODE_ACCOUNTING_INTERNAL_BIZ_ERROR":                                 210005,
		"CODE_ACCOUNTING_UNSUBSCRIBED_ERROR":                                 210006,
		"CODE_PAYMENT_UNKNOWN_ERROR":                                         220000,
		"CODE_PAYMENT_DATA_ACCESS_ERROR":                                     220001,
		"CODE_PAYMENT_INTERNAL_ERROR":                                        220002,
		"CODE_PAYMENT_INVALID_CHANNEL":                                       220003,
		"CODE_PAYMENT_INVALID_VERIFICATION_STATUS":                           220004,
		"CODE_PAYMENT_NO_LOCATION":                                           220005,
		"CODE_PAYMENT_GET_COMPANY_FAILED":                                    220006,
		"CODE_PAYMENT_GET_CHANNEL_ACCOUNT_FAILED":                            220007,
		"CODE_PAYMENT_ADYEN_GET_ACCOUNT_HOLDER_FAILED":                       220008,
		"CODE_PAYMENT_LIST_BUSINESSES_FAILED":                                220009,
		"CODE_PAYMENT_ADYEN_CREATE_LEGAL_ENTITY_FAILED":                      220010,
		"CODE_PAYMENT_ADYEN_CREATE_ACCOUNT_HOLDER_FAILED":                    220011,
		"CODE_PAYMENT_ADYEN_CREATE_STORE_FAILED":                             220012,
		"CODE_PAYMENT_ADYEN_CREATE_BALANCE_ACCOUNT_FAILED":                   220013,
		"CODE_PAYMENT_CREATE_CHANNEL_ACCOUNT_FAILED":                         220014,
		"CODE_PAYMENT_UPDATE_CHANNEL_ACCOUNT_FAILED":                         220015,
		"CODE_PAYMENT_ONBOARD_INVALID_PRE_CONFIG":                            220016,
		"CODE_PAYMENT_ADYEN_CREATE_BUSINESS_LINE_FAILED":                     220017,
		"CODE_PAYMENT_ADYEN_CREATE_ONBOARD_LINK_FAILED":                      220018,
		"CODE_PAYMENT_STATUS_INVALID":                                        220019,
		"CODE_PAYMENT_THIRD_PARTY_API_ERROR":                                 220020,
		"CODE_PAYMENT_RPC_CALL_ERROR":                                        220021,
		"CODE_PAYMENT_INVALID_READER":                                        220022,
		"CODE_PAYMENT_ADYEN_TERMINAL_REQUEST_ERROR":                          220023,
		"CODE_PAYMENT_ADYEN_GET_STORE_FAILED":                                220024,
		"CODE_PAYMENT_ADYEN_GET_BALANCE_ACCOUNT_FAILED":                      220025,
		"CODE_PAYMENT_GET_ACCOUNT_FAILED":                                    220026,
		"CODE_PAYMENT_ADYEN_ADD_PAYMENT_METHODS_FAILED":                      220027,
		"CODE_PAYMENT_ONBOARD_ALREADY_FINISHED":                              220028,
		"CODE_PAYMENT_ADYEN_INVALID_ONBOARD_STEP":                            220029,
		"CODE_PAYMENT_ADYEN_GET_LEGAL_ENTITY_FAILED":                         220030,
		"CODE_PAYMENT_ADYEN_GET_TRANSFER_INSTRUMENT_FAILED":                  220031,
		"CODE_PAYMENT_CREATE_PAYMENT_SETTING_FAILED":                         220032,
		"CODE_PAYMENT_ADYEN_GET_PAYMENT_METHODS_FAILED":                      220033,
		"CODE_PAYMENT_CHANNEL_ACCOUNT_NOT_FOUND":                             220034,
		"CODE_PAYMENT_PAYOUT_NOT_FOUND":                                      221001,
		"CODE_PAYMENT_PAYOUT_GET_FAILED":                                     221002,
		"CODE_PAYMENT_PAYOUT_CREATE_FAILED":                                  221003,
		"CODE_BILLING_UNKNOWN_ERROR":                                         222001,
		"CODE_BILLING_DATA_ACCESS_ERROR":                                     222002,
		"CODE_BILLING_INTERNAL_ERROR":                                        222003,
	}
)

func (x Code) Enum() *Code {
	p := new(Code)
	*p = x
	return p
}

func (x Code) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Code) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_errors_v1_error_code_models_proto_enumTypes[0].Descriptor()
}

func (Code) Type() protoreflect.EnumType {
	return &file_moego_models_errors_v1_error_code_models_proto_enumTypes[0]
}

func (x Code) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Code.Descriptor instead.
func (Code) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_errors_v1_error_code_models_proto_rawDescGZIP(), []int{0}
}

var File_moego_models_errors_v1_error_code_models_proto protoreflect.FileDescriptor

var file_moego_models_errors_v1_error_code_models_proto_rawDesc = []byte{
	0x0a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x65,
	0x72, 0x72, 0x6f, 0x72, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x63,
	0x6f, 0x64, 0x65, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x12, 0x16, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65,
	0x72, 0x72, 0x6f, 0x72, 0x73, 0x2e, 0x76, 0x31, 0x2a, 0xdc, 0x72, 0x0a, 0x04, 0x43, 0x6f, 0x64,
	0x65, 0x12, 0x14, 0x0a, 0x10, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43,
	0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x11, 0x0a, 0x0c, 0x43, 0x4f, 0x44, 0x45, 0x5f,
	0x53, 0x55, 0x43, 0x43, 0x45, 0x53, 0x53, 0x10, 0xc8, 0x01, 0x12, 0x16, 0x0a, 0x11, 0x43, 0x4f,
	0x44, 0x45, 0x5f, 0x50, 0x41, 0x52, 0x41, 0x4d, 0x53, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x10,
	0x90, 0x03, 0x12, 0x1c, 0x0a, 0x17, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x55, 0x4e, 0x41, 0x55, 0x54,
	0x48, 0x4f, 0x52, 0x49, 0x5a, 0x45, 0x44, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x10, 0x91, 0x03,
	0x12, 0x13, 0x0a, 0x0e, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x46, 0x4f, 0x52, 0x42, 0x49, 0x44, 0x44,
	0x45, 0x4e, 0x10, 0x93, 0x03, 0x12, 0x1b, 0x0a, 0x16, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x54, 0x4f,
	0x4f, 0x5f, 0x4d, 0x41, 0x4e, 0x59, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x53, 0x10,
	0xad, 0x03, 0x12, 0x16, 0x0a, 0x11, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x53, 0x45, 0x52, 0x56, 0x45,
	0x52, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x10, 0xf4, 0x03, 0x12, 0x18, 0x0a, 0x13, 0x43, 0x4f,
	0x44, 0x45, 0x5f, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x49, 0x44, 0x5f, 0x45, 0x58, 0x49, 0x53, 0x54,
	0x53, 0x10, 0x9a, 0x4e, 0x12, 0x14, 0x0a, 0x0f, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x52, 0x4f, 0x4f,
	0x4d, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x10, 0x9f, 0x4e, 0x12, 0x1a, 0x0a, 0x15, 0x43, 0x4f,
	0x44, 0x45, 0x5f, 0x50, 0x52, 0x49, 0x4d, 0x41, 0x52, 0x59, 0x5f, 0x49, 0x44, 0x5f, 0x45, 0x4d,
	0x50, 0x54, 0x59, 0x10, 0xa0, 0x4e, 0x12, 0x1f, 0x0a, 0x1a, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x56,
	0x45, 0x52, 0x49, 0x46, 0x59, 0x5f, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x45,
	0x51, 0x55, 0x41, 0x4c, 0x10, 0xa1, 0x4e, 0x12, 0x18, 0x0a, 0x13, 0x43, 0x4f, 0x44, 0x45, 0x5f,
	0x50, 0x41, 0x52, 0x41, 0x4c, 0x4c, 0x45, 0x4c, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x10, 0xa2,
	0x4e, 0x12, 0x19, 0x0a, 0x14, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x42, 0x52, 0x41, 0x4e, 0x43, 0x48,
	0x5f, 0x4d, 0x49, 0x53, 0x4d, 0x41, 0x54, 0x43, 0x48, 0x10, 0xf7, 0x55, 0x12, 0x2c, 0x0a, 0x27,
	0x43, 0x4f, 0x44, 0x45, 0x5f, 0x41, 0x53, 0x53, 0x49, 0x47, 0x4e, 0x5f, 0x49, 0x54, 0x45, 0x4d,
	0x5f, 0x41, 0x4d, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x4f, 0x4e, 0x43, 0x45, 0x5f, 0x4f, 0x4e, 0x4c,
	0x59, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x10, 0xf9, 0x55, 0x12, 0x32, 0x0a, 0x2d, 0x43, 0x4f,
	0x44, 0x45, 0x5f, 0x41, 0x53, 0x53, 0x49, 0x47, 0x4e, 0x5f, 0x49, 0x54, 0x45, 0x4d, 0x5f, 0x41,
	0x4d, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x44, 0x41, 0x54, 0x41, 0x5f, 0x50, 0x45, 0x52, 0x4d, 0x49,
	0x53, 0x53, 0x49, 0x4f, 0x4e, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x10, 0xfa, 0x55, 0x12, 0x19,
	0x0a, 0x13, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x43, 0x48, 0x45, 0x43, 0x4b, 0x5f, 0x49, 0x4e, 0x5f,
	0x45, 0x52, 0x52, 0x4f, 0x52, 0x10, 0xa1, 0x9c, 0x01, 0x12, 0x1a, 0x0a, 0x14, 0x43, 0x4f, 0x44,
	0x45, 0x5f, 0x43, 0x48, 0x45, 0x43, 0x4b, 0x5f, 0x4f, 0x55, 0x54, 0x5f, 0x45, 0x52, 0x52, 0x4f,
	0x52, 0x10, 0xa2, 0x9c, 0x01, 0x12, 0x19, 0x0a, 0x13, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x50, 0x41,
	0x53, 0x53, 0x57, 0x4f, 0x52, 0x44, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x10, 0xa3, 0x9c, 0x01,
	0x12, 0x1c, 0x0a, 0x16, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54,
	0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x46, 0x4f, 0x55, 0x4e, 0x44, 0x10, 0xa4, 0x9c, 0x01, 0x12, 0x17,
	0x0a, 0x11, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x43,
	0x4f, 0x44, 0x45, 0x10, 0xa5, 0x9c, 0x01, 0x12, 0x1f, 0x0a, 0x19, 0x43, 0x4f, 0x44, 0x45, 0x5f,
	0x53, 0x54, 0x41, 0x46, 0x46, 0x5f, 0x42, 0x49, 0x4e, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x45, 0x52,
	0x52, 0x4f, 0x52, 0x31, 0x10, 0xa6, 0x9c, 0x01, 0x12, 0x1f, 0x0a, 0x19, 0x43, 0x4f, 0x44, 0x45,
	0x5f, 0x53, 0x54, 0x41, 0x46, 0x46, 0x5f, 0x42, 0x49, 0x4e, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x45,
	0x52, 0x52, 0x4f, 0x52, 0x32, 0x10, 0xa7, 0x9c, 0x01, 0x12, 0x1f, 0x0a, 0x19, 0x43, 0x4f, 0x44,
	0x45, 0x5f, 0x53, 0x54, 0x41, 0x46, 0x46, 0x5f, 0x42, 0x49, 0x4e, 0x44, 0x49, 0x4e, 0x47, 0x5f,
	0x45, 0x52, 0x52, 0x4f, 0x52, 0x33, 0x10, 0xa8, 0x9c, 0x01, 0x12, 0x1f, 0x0a, 0x19, 0x43, 0x4f,
	0x44, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x46, 0x46, 0x5f, 0x42, 0x49, 0x4e, 0x44, 0x49, 0x4e, 0x47,
	0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x34, 0x10, 0xa9, 0x9c, 0x01, 0x12, 0x1c, 0x0a, 0x16, 0x43,
	0x4f, 0x44, 0x45, 0x5f, 0x44, 0x45, 0x4c, 0x45, 0x54, 0x45, 0x5f, 0x52, 0x4f, 0x4c, 0x45, 0x5f,
	0x45, 0x52, 0x52, 0x4f, 0x52, 0x10, 0xaa, 0x9c, 0x01, 0x12, 0x25, 0x0a, 0x1f, 0x43, 0x4f, 0x44,
	0x45, 0x5f, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x4d, 0x45, 0x54, 0x48, 0x4f, 0x44,
	0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x45, 0x58, 0x49, 0x53, 0x54, 0x53, 0x10, 0xab, 0x9c, 0x01,
	0x12, 0x24, 0x0a, 0x1e, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x44, 0x45, 0x46, 0x41, 0x55, 0x4c, 0x54,
	0x5f, 0x4d, 0x45, 0x54, 0x48, 0x4f, 0x44, 0x5f, 0x4f, 0x50, 0x45, 0x52, 0x5f, 0x46, 0x41, 0x4c,
	0x53, 0x45, 0x10, 0xac, 0x9c, 0x01, 0x12, 0x22, 0x0a, 0x1c, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x46,
	0x49, 0x4c, 0x45, 0x5f, 0x53, 0x49, 0x5a, 0x45, 0x5f, 0x45, 0x58, 0x43, 0x45, 0x45, 0x44, 0x53,
	0x5f, 0x4c, 0x49, 0x4d, 0x49, 0x54, 0x10, 0xad, 0x9c, 0x01, 0x12, 0x16, 0x0a, 0x10, 0x43, 0x4f,
	0x44, 0x45, 0x5f, 0x54, 0x41, 0x58, 0x5f, 0x49, 0x53, 0x5f, 0x55, 0x53, 0x45, 0x44, 0x10, 0xae,
	0x9c, 0x01, 0x12, 0x19, 0x0a, 0x13, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x54, 0x41, 0x47, 0x5f, 0x4e,
	0x41, 0x4d, 0x45, 0x5f, 0x45, 0x58, 0x49, 0x53, 0x54, 0x10, 0xaf, 0x9c, 0x01, 0x12, 0x18, 0x0a,
	0x12, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x54, 0x41, 0x47, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x4e,
	0x55, 0x4c, 0x4c, 0x10, 0xb0, 0x9c, 0x01, 0x12, 0x1b, 0x0a, 0x15, 0x43, 0x4f, 0x44, 0x45, 0x5f,
	0x54, 0x48, 0x45, 0x5f, 0x4c, 0x41, 0x53, 0x54, 0x5f, 0x44, 0x45, 0x4c, 0x45, 0x54, 0x45, 0x44,
	0x10, 0xb1, 0x9c, 0x01, 0x12, 0x1c, 0x0a, 0x16, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x54, 0x48, 0x45,
	0x5f, 0x4c, 0x41, 0x53, 0x54, 0x5f, 0x49, 0x4e, 0x41, 0x43, 0x54, 0x49, 0x56, 0x45, 0x10, 0xb2,
	0x9c, 0x01, 0x12, 0x20, 0x0a, 0x1a, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x50, 0x45, 0x52, 0x4d, 0x49,
	0x53, 0x53, 0x49, 0x4f, 0x4e, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x45, 0x4e, 0x4f, 0x55, 0x47, 0x48,
	0x10, 0xb3, 0x9c, 0x01, 0x12, 0x2c, 0x0a, 0x26, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x4e, 0x4f, 0x5f,
	0x50, 0x45, 0x52, 0x4d, 0x49, 0x53, 0x53, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x48, 0x4f, 0x57, 0x5f,
	0x4d, 0x45, 0x53, 0x53, 0x41, 0x47, 0x45, 0x5f, 0x54, 0x48, 0x52, 0x45, 0x41, 0x44, 0x10, 0xb4,
	0x9c, 0x01, 0x12, 0x1d, 0x0a, 0x17, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x54, 0x41, 0x58, 0x5f, 0x49,
	0x53, 0x5f, 0x55, 0x53, 0x45, 0x44, 0x5f, 0x52, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x10, 0xb5, 0x9c,
	0x01, 0x12, 0x1e, 0x0a, 0x18, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x45, 0x4e,
	0x4f, 0x55, 0x47, 0x48, 0x5f, 0x56, 0x41, 0x4e, 0x53, 0x5f, 0x4e, 0x55, 0x4d, 0x10, 0xb6, 0x9c,
	0x01, 0x12, 0x27, 0x0a, 0x21, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x50, 0x41, 0x53, 0x53, 0x57, 0x4f,
	0x52, 0x44, 0x5f, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x4f, 0x4c, 0x44, 0x50, 0x57, 0x44,
	0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x10, 0xb7, 0x9c, 0x01, 0x12, 0x27, 0x0a, 0x21, 0x43, 0x4f,
	0x44, 0x45, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x4f,
	0x57, 0x4e, 0x5f, 0x54, 0x48, 0x49, 0x53, 0x5f, 0x43, 0x4f, 0x4d, 0x50, 0x41, 0x4e, 0x59, 0x10,
	0xb8, 0x9c, 0x01, 0x12, 0x28, 0x0a, 0x22, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x43, 0x48, 0x45, 0x43,
	0x4b, 0x5f, 0x45, 0x4d, 0x41, 0x49, 0x4c, 0x5f, 0x45, 0x58, 0x49, 0x53, 0x54, 0x5f, 0x50, 0x48,
	0x50, 0x5f, 0x56, 0x45, 0x52, 0x53, 0x49, 0x4f, 0x4e, 0x10, 0xb9, 0x9c, 0x01, 0x12, 0x1c, 0x0a,
	0x16, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x43, 0x4f, 0x4d, 0x50, 0x41, 0x4e, 0x59, 0x5f, 0x4e, 0x4f,
	0x54, 0x5f, 0x46, 0x4f, 0x55, 0x4e, 0x44, 0x10, 0xba, 0x9c, 0x01, 0x12, 0x20, 0x0a, 0x1a, 0x43,
	0x4f, 0x44, 0x45, 0x5f, 0x53, 0x57, 0x49, 0x54, 0x43, 0x48, 0x5f, 0x42, 0x55, 0x53, 0x49, 0x4e,
	0x45, 0x53, 0x53, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x10, 0xbb, 0x9c, 0x01, 0x12, 0x25, 0x0a,
	0x1f, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x46, 0x46, 0x5f, 0x4f, 0x4e, 0x4c, 0x59,
	0x5f, 0x42, 0x41, 0x4e, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x4f, 0x4e, 0x45, 0x5f, 0x56, 0x41, 0x4e,
	0x10, 0xbc, 0x9c, 0x01, 0x12, 0x26, 0x0a, 0x20, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x53, 0x54, 0x41,
	0x46, 0x46, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x46, 0x4f, 0x55, 0x4e, 0x44, 0x5f, 0x49, 0x4e, 0x5f,
	0x46, 0x52, 0x4f, 0x4d, 0x5f, 0x56, 0x41, 0x4e, 0x10, 0xbd, 0x9c, 0x01, 0x12, 0x20, 0x0a, 0x1a,
	0x43, 0x4f, 0x44, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x46, 0x46, 0x5f, 0x46, 0x4f, 0x55, 0x4e, 0x44,
	0x5f, 0x49, 0x4e, 0x5f, 0x54, 0x4f, 0x5f, 0x56, 0x41, 0x4e, 0x10, 0xbe, 0x9c, 0x01, 0x12, 0x2e,
	0x0a, 0x28, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x50, 0x52, 0x4f, 0x43, 0x45, 0x53, 0x53, 0x49, 0x4e,
	0x47, 0x5f, 0x46, 0x45, 0x45, 0x5f, 0x53, 0x48, 0x4f, 0x55, 0x4c, 0x44, 0x5f, 0x49, 0x4e, 0x5f,
	0x53, 0x54, 0x52, 0x49, 0x50, 0x45, 0x5f, 0x50, 0x41, 0x59, 0x10, 0xbf, 0x9c, 0x01, 0x12, 0x2b,
	0x0a, 0x25, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x50, 0x52, 0x4f, 0x43, 0x45, 0x53, 0x53, 0x49, 0x4e,
	0x47, 0x5f, 0x46, 0x45, 0x45, 0x5f, 0x53, 0x48, 0x4f, 0x55, 0x4c, 0x44, 0x5f, 0x49, 0x4e, 0x5f,
	0x55, 0x53, 0x5f, 0x41, 0x52, 0x45, 0x41, 0x10, 0xc0, 0x9c, 0x01, 0x12, 0x24, 0x0a, 0x1e, 0x43,
	0x4f, 0x44, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x46, 0x46, 0x5f, 0x55, 0x4e, 0x4c, 0x49, 0x4e, 0x4b,
	0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x4f, 0x57, 0x4e, 0x45, 0x52, 0x10, 0xc1, 0x9c,
	0x01, 0x12, 0x26, 0x0a, 0x20, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x46, 0x46, 0x5f,
	0x55, 0x4e, 0x4c, 0x49, 0x4e, 0x4b, 0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x4e, 0x4f,
	0x5f, 0x4c, 0x49, 0x4e, 0x4b, 0x10, 0xc2, 0x9c, 0x01, 0x12, 0x2f, 0x0a, 0x29, 0x43, 0x4f, 0x44,
	0x45, 0x5f, 0x53, 0x54, 0x41, 0x46, 0x46, 0x5f, 0x55, 0x4e, 0x4c, 0x49, 0x4e, 0x4b, 0x5f, 0x52,
	0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x48, 0x41, 0x53, 0x5f, 0x42, 0x45, 0x45, 0x4e, 0x5f, 0x44,
	0x45, 0x4c, 0x45, 0x54, 0x45, 0x44, 0x10, 0xc3, 0x9c, 0x01, 0x12, 0x1f, 0x0a, 0x19, 0x43, 0x4f,
	0x44, 0x45, 0x5f, 0x43, 0x41, 0x4e, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x43, 0x52, 0x45, 0x41, 0x54,
	0x45, 0x5f, 0x53, 0x54, 0x41, 0x46, 0x46, 0x10, 0xc4, 0x9c, 0x01, 0x12, 0x1f, 0x0a, 0x19, 0x43,
	0x4f, 0x44, 0x45, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x45, 0x4e, 0x4f, 0x55, 0x47, 0x48, 0x5f, 0x56,
	0x41, 0x4e, 0x53, 0x5f, 0x4e, 0x55, 0x4d, 0x32, 0x10, 0xc5, 0x9c, 0x01, 0x12, 0x2d, 0x0a, 0x27,
	0x43, 0x4f, 0x44, 0x45, 0x5f, 0x53, 0x4d, 0x41, 0x52, 0x54, 0x5f, 0x53, 0x43, 0x48, 0x45, 0x44,
	0x55, 0x4c, 0x45, 0x5f, 0x52, 0x55, 0x4c, 0x45, 0x5f, 0x41, 0x4c, 0x52, 0x45, 0x41, 0x44, 0x59,
	0x5f, 0x45, 0x58, 0x49, 0x53, 0x54, 0x53, 0x10, 0xc6, 0x9c, 0x01, 0x12, 0x1d, 0x0a, 0x17, 0x43,
	0x4f, 0x44, 0x45, 0x5f, 0x42, 0x55, 0x53, 0x49, 0x4e, 0x45, 0x53, 0x53, 0x5f, 0x4e, 0x4f, 0x54,
	0x5f, 0x46, 0x4f, 0x55, 0x4e, 0x44, 0x10, 0xc7, 0x9c, 0x01, 0x12, 0x2a, 0x0a, 0x24, 0x43, 0x4f,
	0x44, 0x45, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x4f,
	0x57, 0x4e, 0x5f, 0x54, 0x48, 0x49, 0x53, 0x5f, 0x45, 0x4e, 0x54, 0x45, 0x52, 0x50, 0x52, 0x49,
	0x53, 0x45, 0x10, 0xc8, 0x9c, 0x01, 0x12, 0x2a, 0x0a, 0x24, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x45,
	0x4e, 0x54, 0x45, 0x52, 0x50, 0x52, 0x49, 0x53, 0x45, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x4f, 0x57,
	0x4e, 0x5f, 0x54, 0x48, 0x49, 0x53, 0x5f, 0x43, 0x4f, 0x4d, 0x50, 0x41, 0x4e, 0x59, 0x10, 0xc9,
	0x9c, 0x01, 0x12, 0x1f, 0x0a, 0x19, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x45, 0x4e, 0x54, 0x45, 0x52,
	0x50, 0x52, 0x49, 0x53, 0x45, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x46, 0x4f, 0x55, 0x4e, 0x44, 0x10,
	0xca, 0x9c, 0x01, 0x12, 0x22, 0x0a, 0x1c, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x43, 0x4f, 0x4d, 0x50,
	0x41, 0x4e, 0x59, 0x5f, 0x52, 0x45, 0x41, 0x43, 0x48, 0x5f, 0x4d, 0x41, 0x58, 0x5f, 0x4c, 0x49,
	0x4d, 0x49, 0x54, 0x10, 0xcb, 0x9c, 0x01, 0x12, 0x1f, 0x0a, 0x19, 0x43, 0x4f, 0x44, 0x45, 0x5f,
	0x53, 0x57, 0x49, 0x54, 0x43, 0x48, 0x5f, 0x43, 0x4f, 0x4d, 0x50, 0x41, 0x4e, 0x59, 0x5f, 0x45,
	0x52, 0x52, 0x4f, 0x52, 0x10, 0xcc, 0x9c, 0x01, 0x12, 0x1a, 0x0a, 0x14, 0x43, 0x4f, 0x44, 0x45,
	0x5f, 0x53, 0x54, 0x41, 0x46, 0x46, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x46, 0x4f, 0x55, 0x4e, 0x44,
	0x10, 0xcd, 0x9c, 0x01, 0x12, 0x2f, 0x0a, 0x29, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x53, 0x54, 0x41,
	0x46, 0x46, 0x5f, 0x57, 0x4f, 0x52, 0x4b, 0x49, 0x4e, 0x47, 0x5f, 0x4c, 0x4f, 0x43, 0x41, 0x54,
	0x49, 0x4f, 0x4e, 0x5f, 0x4c, 0x49, 0x53, 0x54, 0x5f, 0x49, 0x53, 0x5f, 0x45, 0x4d, 0x50, 0x54,
	0x59, 0x10, 0xce, 0x9c, 0x01, 0x12, 0x28, 0x0a, 0x22, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x53, 0x54,
	0x41, 0x46, 0x46, 0x5f, 0x42, 0x49, 0x4e, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x45, 0x4d, 0x41, 0x49,
	0x4c, 0x5f, 0x49, 0x4e, 0x43, 0x4f, 0x52, 0x52, 0x45, 0x43, 0x54, 0x10, 0xcf, 0x9c, 0x01, 0x12,
	0x1c, 0x0a, 0x16, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x46, 0x46, 0x5f, 0x4c, 0x4f,
	0x47, 0x49, 0x4e, 0x5f, 0x4c, 0x49, 0x4d, 0x49, 0x54, 0x10, 0xd0, 0x9c, 0x01, 0x12, 0x1a, 0x0a,
	0x14, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x45, 0x4d, 0x41, 0x49, 0x4c, 0x5f, 0x4e, 0x4f, 0x54, 0x5f,
	0x45, 0x58, 0x49, 0x53, 0x54, 0x10, 0xb0, 0xea, 0x01, 0x12, 0x16, 0x0a, 0x10, 0x43, 0x4f, 0x44,
	0x45, 0x5f, 0x45, 0x4d, 0x41, 0x49, 0x4c, 0x5f, 0x45, 0x58, 0x49, 0x53, 0x54, 0x10, 0xb1, 0xea,
	0x01, 0x12, 0x16, 0x0a, 0x10, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x50, 0x48, 0x4f, 0x4e, 0x45, 0x5f,
	0x45, 0x58, 0x49, 0x53, 0x54, 0x10, 0xb2, 0xea, 0x01, 0x12, 0x1d, 0x0a, 0x17, 0x43, 0x4f, 0x44,
	0x45, 0x5f, 0x43, 0x55, 0x53, 0x54, 0x4f, 0x4d, 0x45, 0x52, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x46,
	0x4f, 0x55, 0x4e, 0x44, 0x10, 0xb3, 0xea, 0x01, 0x12, 0x18, 0x0a, 0x12, 0x43, 0x4f, 0x44, 0x45,
	0x5f, 0x50, 0x45, 0x54, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x46, 0x4f, 0x55, 0x4e, 0x44, 0x10, 0xb4,
	0xea, 0x01, 0x12, 0x20, 0x0a, 0x1a, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x41, 0x50, 0x50, 0x4f, 0x49,
	0x4e, 0x54, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x46, 0x4f, 0x55, 0x4e, 0x44,
	0x10, 0xb5, 0xea, 0x01, 0x12, 0x1f, 0x0a, 0x19, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x50, 0x45, 0x54,
	0x5f, 0x48, 0x41, 0x56, 0x45, 0x5f, 0x41, 0x50, 0x50, 0x4f, 0x49, 0x4e, 0x54, 0x4d, 0x45, 0x4e,
	0x54, 0x10, 0xb6, 0xea, 0x01, 0x12, 0x20, 0x0a, 0x1a, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x41, 0x50,
	0x50, 0x4f, 0x49, 0x4e, 0x54, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x53, 0x49, 0x47, 0x4e, 0x5f, 0x4f,
	0x56, 0x45, 0x52, 0x10, 0xb7, 0xea, 0x01, 0x12, 0x24, 0x0a, 0x1e, 0x43, 0x4f, 0x44, 0x45, 0x5f,
	0x41, 0x50, 0x50, 0x4f, 0x49, 0x4e, 0x54, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x50, 0x45, 0x54, 0x5f,
	0x4e, 0x4f, 0x54, 0x5f, 0x45, 0x58, 0x49, 0x53, 0x54, 0x10, 0xb8, 0xea, 0x01, 0x12, 0x38, 0x0a,
	0x32, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x41, 0x50, 0x50, 0x4f, 0x49, 0x4e, 0x54, 0x4d, 0x45, 0x4e,
	0x54, 0x5f, 0x41, 0x47, 0x52, 0x45, 0x45, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x53, 0x45, 0x4e, 0x44,
	0x5f, 0x43, 0x4f, 0x4e, 0x54, 0x45, 0x4e, 0x54, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x43, 0x4f, 0x4e,
	0x46, 0x49, 0x47, 0x10, 0xb9, 0xea, 0x01, 0x12, 0x1a, 0x0a, 0x14, 0x43, 0x4f, 0x44, 0x45, 0x5f,
	0x50, 0x45, 0x54, 0x5f, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x45, 0x58, 0x49, 0x53, 0x54, 0x53, 0x10,
	0xba, 0xea, 0x01, 0x12, 0x1d, 0x0a, 0x17, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x45, 0x4d, 0x41, 0x49,
	0x4c, 0x5f, 0x45, 0x58, 0x49, 0x53, 0x54, 0x53, 0x5f, 0x4d, 0x55, 0x4c, 0x54, 0x49, 0x10, 0xbb,
	0xea, 0x01, 0x12, 0x20, 0x0a, 0x1a, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x42, 0x49, 0x52, 0x54, 0x48,
	0x44, 0x41, 0x59, 0x5f, 0x46, 0x4f, 0x52, 0x4d, 0x41, 0x54, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52,
	0x10, 0xbc, 0xea, 0x01, 0x12, 0x1e, 0x0a, 0x18, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x43, 0x55, 0x53,
	0x54, 0x4f, 0x4d, 0x45, 0x52, 0x5f, 0x49, 0x53, 0x5f, 0x42, 0x4c, 0x4f, 0x43, 0x4b, 0x45, 0x44,
	0x10, 0xbd, 0xea, 0x01, 0x12, 0x22, 0x0a, 0x1c, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x43, 0x55, 0x53,
	0x54, 0x4f, 0x4d, 0x45, 0x52, 0x5f, 0x41, 0x4c, 0x52, 0x45, 0x41, 0x44, 0x59, 0x5f, 0x45, 0x58,
	0x49, 0x53, 0x54, 0x53, 0x10, 0xbe, 0xea, 0x01, 0x12, 0x21, 0x0a, 0x1b, 0x43, 0x4f, 0x44, 0x45,
	0x5f, 0x49, 0x4e, 0x54, 0x41, 0x4b, 0x45, 0x5f, 0x46, 0x4f, 0x52, 0x4d, 0x5f, 0x49, 0x53, 0x5f,
	0x44, 0x45, 0x4c, 0x45, 0x54, 0x45, 0x44, 0x10, 0xbf, 0xea, 0x01, 0x12, 0x27, 0x0a, 0x21, 0x43,
	0x4f, 0x44, 0x45, 0x5f, 0x49, 0x4e, 0x54, 0x41, 0x4b, 0x45, 0x5f, 0x46, 0x4f, 0x52, 0x4d, 0x5f,
	0x4d, 0x45, 0x53, 0x53, 0x41, 0x47, 0x45, 0x5f, 0x54, 0x4f, 0x4f, 0x5f, 0x4c, 0x4f, 0x4e, 0x47,
	0x10, 0xc0, 0xea, 0x01, 0x12, 0x25, 0x0a, 0x1f, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x50, 0x45, 0x54,
	0x5f, 0x42, 0x52, 0x45, 0x45, 0x44, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x41, 0x4c, 0x4c, 0x4f, 0x57,
	0x5f, 0x44, 0x45, 0x4c, 0x45, 0x54, 0x45, 0x10, 0xc1, 0xea, 0x01, 0x12, 0x2d, 0x0a, 0x27, 0x43,
	0x4f, 0x44, 0x45, 0x5f, 0x43, 0x55, 0x53, 0x54, 0x4f, 0x4d, 0x45, 0x52, 0x5f, 0x41, 0x43, 0x43,
	0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x4f, 0x52, 0x5f, 0x50, 0x41, 0x53, 0x53, 0x57, 0x4f, 0x52, 0x44,
	0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x10, 0xc2, 0xea, 0x01, 0x12, 0x25, 0x0a, 0x1f, 0x43, 0x4f,
	0x44, 0x45, 0x5f, 0x43, 0x55, 0x53, 0x54, 0x4f, 0x4d, 0x45, 0x52, 0x5f, 0x41, 0x43, 0x43, 0x4f,
	0x55, 0x4e, 0x54, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x46, 0x4f, 0x55, 0x4e, 0x44, 0x10, 0xc3, 0xea,
	0x01, 0x12, 0x25, 0x0a, 0x1f, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x43, 0x55, 0x53, 0x54, 0x4f, 0x4d,
	0x45, 0x52, 0x5f, 0x53, 0x45, 0x53, 0x53, 0x49, 0x4f, 0x4e, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x46,
	0x4f, 0x55, 0x4e, 0x44, 0x10, 0xc4, 0xea, 0x01, 0x12, 0x20, 0x0a, 0x1a, 0x43, 0x4f, 0x44, 0x45,
	0x5f, 0x43, 0x55, 0x53, 0x54, 0x4f, 0x4d, 0x45, 0x52, 0x5f, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49,
	0x44, 0x5f, 0x43, 0x4f, 0x44, 0x45, 0x10, 0xc5, 0xea, 0x01, 0x12, 0x28, 0x0a, 0x22, 0x43, 0x4f,
	0x44, 0x45, 0x5f, 0x50, 0x45, 0x54, 0x5f, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x44, 0x45, 0x53, 0x43,
	0x52, 0x49, 0x50, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x4f, 0x4f, 0x5f, 0x4c, 0x4f, 0x4e, 0x47,
	0x10, 0xc6, 0xea, 0x01, 0x12, 0x1e, 0x0a, 0x18, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x45, 0x4d, 0x41,
	0x49, 0x4c, 0x5f, 0x4f, 0x52, 0x5f, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52,
	0x10, 0xc7, 0xea, 0x01, 0x12, 0x1e, 0x0a, 0x18, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x50, 0x48, 0x4f,
	0x4e, 0x45, 0x5f, 0x4f, 0x52, 0x5f, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52,
	0x10, 0xc8, 0xea, 0x01, 0x12, 0x1a, 0x0a, 0x14, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x4c, 0x49, 0x43,
	0x45, 0x4e, 0x53, 0x45, 0x5f, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x10, 0xc9, 0xea, 0x01,
	0x12, 0x18, 0x0a, 0x12, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x50, 0x48, 0x4f, 0x4e, 0x45, 0x5f, 0x49,
	0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x10, 0xca, 0xea, 0x01, 0x12, 0x18, 0x0a, 0x12, 0x43, 0x4f,
	0x44, 0x45, 0x5f, 0x45, 0x4d, 0x41, 0x49, 0x4c, 0x5f, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44,
	0x10, 0xcb, 0xea, 0x01, 0x12, 0x3a, 0x0a, 0x34, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x43, 0x55, 0x53,
	0x54, 0x4f, 0x4d, 0x45, 0x52, 0x5f, 0x43, 0x52, 0x45, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46,
	0x52, 0x4f, 0x4d, 0x5f, 0x47, 0x49, 0x56, 0x45, 0x4e, 0x5f, 0x53, 0x4f, 0x55, 0x52, 0x43, 0x45,
	0x5f, 0x49, 0x53, 0x5f, 0x44, 0x49, 0x53, 0x41, 0x42, 0x4c, 0x45, 0x44, 0x10, 0xcc, 0xea, 0x01,
	0x12, 0x1b, 0x0a, 0x15, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x43, 0x55, 0x53, 0x54, 0x4f, 0x4d, 0x45,
	0x52, 0x5f, 0x4d, 0x45, 0x52, 0x47, 0x49, 0x4e, 0x47, 0x10, 0xcd, 0xea, 0x01, 0x12, 0x21, 0x0a,
	0x1b, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x43, 0x55, 0x53, 0x54, 0x4f, 0x4d, 0x45, 0x52, 0x5f, 0x4e,
	0x4f, 0x54, 0x5f, 0x44, 0x55, 0x50, 0x4c, 0x49, 0x43, 0x41, 0x54, 0x45, 0x10, 0xce, 0xea, 0x01,
	0x12, 0x1c, 0x0a, 0x16, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x41, 0x44, 0x44, 0x52, 0x45, 0x53, 0x53,
	0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x46, 0x4f, 0x55, 0x4e, 0x44, 0x10, 0x94, 0xeb, 0x01, 0x12, 0x28,
	0x0a, 0x22, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x43, 0x41, 0x4e, 0x4e, 0x4f, 0x54, 0x5f, 0x44, 0x45,
	0x4c, 0x45, 0x54, 0x45, 0x5f, 0x50, 0x52, 0x49, 0x4d, 0x41, 0x52, 0x59, 0x5f, 0x41, 0x44, 0x44,
	0x52, 0x45, 0x53, 0x53, 0x10, 0x95, 0xeb, 0x01, 0x12, 0x28, 0x0a, 0x22, 0x43, 0x4f, 0x44, 0x45,
	0x5f, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x4c, 0x41, 0x54, 0x49, 0x54, 0x55, 0x44,
	0x45, 0x5f, 0x4f, 0x52, 0x5f, 0x4c, 0x4f, 0x4e, 0x47, 0x49, 0x54, 0x55, 0x44, 0x45, 0x10, 0x96,
	0xeb, 0x01, 0x12, 0x1b, 0x0a, 0x15, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x41, 0x44, 0x44, 0x52, 0x45,
	0x53, 0x53, 0x5f, 0x49, 0x53, 0x5f, 0x45, 0x4d, 0x50, 0x54, 0x59, 0x10, 0x97, 0xeb, 0x01, 0x12,
	0x1d, 0x0a, 0x17, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x54, 0x4f, 0x4f, 0x5f, 0x4d, 0x41, 0x4e, 0x59,
	0x5f, 0x41, 0x44, 0x44, 0x52, 0x45, 0x53, 0x53, 0x45, 0x53, 0x10, 0x98, 0xeb, 0x01, 0x12, 0x23,
	0x0a, 0x1d, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x42, 0x4f, 0x4f, 0x4b, 0x5f, 0x4f, 0x4e, 0x4c, 0x49,
	0x4e, 0x45, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x10,
	0xd4, 0xbb, 0x02, 0x12, 0x21, 0x0a, 0x1b, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x42, 0x4f, 0x4f, 0x4b,
	0x5f, 0x4f, 0x4e, 0x4c, 0x49, 0x4e, 0x45, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x45, 0x4e, 0x41, 0x42,
	0x4c, 0x45, 0x10, 0x86, 0xb9, 0x02, 0x12, 0x20, 0x0a, 0x1a, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x41,
	0x47, 0x52, 0x45, 0x45, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x43, 0x4f, 0x4e,
	0x46, 0x49, 0x52, 0x4d, 0x10, 0x9a, 0xb9, 0x02, 0x12, 0x1d, 0x0a, 0x17, 0x43, 0x4f, 0x44, 0x45,
	0x5f, 0x53, 0x49, 0x47, 0x4e, 0x41, 0x54, 0x55, 0x52, 0x45, 0x5f, 0x49, 0x53, 0x5f, 0x45, 0x4d,
	0x50, 0x54, 0x59, 0x10, 0x9b, 0xb9, 0x02, 0x12, 0x22, 0x0a, 0x1c, 0x43, 0x4f, 0x44, 0x45, 0x5f,
	0x41, 0x43, 0x43, 0x45, 0x50, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4e, 0x4f, 0x57, 0x5f,
	0x41, 0x4c, 0x4c, 0x4f, 0x57, 0x45, 0x44, 0x10, 0x9c, 0xb9, 0x02, 0x12, 0x26, 0x0a, 0x20, 0x43,
	0x4f, 0x44, 0x45, 0x5f, 0x43, 0x41, 0x4e, 0x43, 0x45, 0x4c, 0x5f, 0x50, 0x4f, 0x4c, 0x49, 0x43,
	0x59, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x43, 0x4f, 0x4e, 0x46, 0x49, 0x52, 0x4d, 0x45, 0x44, 0x10,
	0x9d, 0xb9, 0x02, 0x12, 0x24, 0x0a, 0x1e, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x43, 0x55, 0x53, 0x54,
	0x4f, 0x4d, 0x45, 0x52, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x46, 0x4f, 0x55, 0x4e, 0x44, 0x5f, 0x46,
	0x4f, 0x52, 0x5f, 0x4f, 0x42, 0x10, 0x9e, 0xb9, 0x02, 0x12, 0x25, 0x0a, 0x1f, 0x43, 0x4f, 0x44,
	0x45, 0x5f, 0x53, 0x54, 0x52, 0x49, 0x50, 0x45, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x54, 0x4f,
	0x4b, 0x45, 0x4e, 0x5f, 0x49, 0x53, 0x5f, 0x45, 0x4d, 0x50, 0x54, 0x59, 0x10, 0x9f, 0xb9, 0x02,
	0x12, 0x2c, 0x0a, 0x26, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x41, 0x50, 0x50, 0x4f, 0x49, 0x4e, 0x54,
	0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x49, 0x4d, 0x45, 0x5f, 0x49, 0x53, 0x5f, 0x4e, 0x4f, 0x54,
	0x5f, 0x41, 0x56, 0x41, 0x49, 0x4c, 0x41, 0x42, 0x4c, 0x45, 0x10, 0xa0, 0xb9, 0x02, 0x12, 0x2f,
	0x0a, 0x29, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x41, 0x50, 0x50, 0x4f, 0x49, 0x4e, 0x54, 0x4d, 0x45,
	0x4e, 0x54, 0x5f, 0x43, 0x41, 0x4e, 0x43, 0x45, 0x4c, 0x45, 0x44, 0x5f, 0x49, 0x4e, 0x56, 0x4f,
	0x49, 0x43, 0x45, 0x5f, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x10, 0xa1, 0xb9, 0x02, 0x12,
	0x22, 0x0a, 0x1c, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x41, 0x50, 0x50, 0x4f, 0x49, 0x4e, 0x54, 0x4d,
	0x45, 0x4e, 0x54, 0x5f, 0x54, 0x49, 0x4d, 0x45, 0x5f, 0x4c, 0x4f, 0x43, 0x4b, 0x45, 0x44, 0x10,
	0xa2, 0xb9, 0x02, 0x12, 0x1e, 0x0a, 0x18, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x4f, 0x42, 0x5f, 0x44,
	0x45, 0x50, 0x4f, 0x53, 0x49, 0x54, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x50, 0x41, 0x49, 0x44, 0x10,
	0xa3, 0xb9, 0x02, 0x12, 0x25, 0x0a, 0x1f, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x42, 0x4f, 0x4f, 0x4b,
	0x5f, 0x4f, 0x4e, 0x4c, 0x49, 0x4e, 0x45, 0x5f, 0x44, 0x4f, 0x4d, 0x41, 0x49, 0x4e, 0x5f, 0x49,
	0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x10, 0xa4, 0xb9, 0x02, 0x12, 0x25, 0x0a, 0x1f, 0x43, 0x4f,
	0x44, 0x45, 0x5f, 0x42, 0x4f, 0x4f, 0x4b, 0x5f, 0x4f, 0x4e, 0x4c, 0x49, 0x4e, 0x45, 0x5f, 0x53,
	0x49, 0x54, 0x45, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x46, 0x4f, 0x55, 0x4e, 0x44, 0x10, 0xa5, 0xb9,
	0x02, 0x12, 0x29, 0x0a, 0x23, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x53, 0x45, 0x52, 0x56, 0x49, 0x43,
	0x45, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f,
	0x49, 0x53, 0x5f, 0x45, 0x58, 0x49, 0x53, 0x54, 0x10, 0xb9, 0x8e, 0x03, 0x12, 0x20, 0x0a, 0x1a,
	0x43, 0x4f, 0x44, 0x45, 0x5f, 0x53, 0x45, 0x52, 0x56, 0x49, 0x43, 0x45, 0x5f, 0x4e, 0x41, 0x4d,
	0x45, 0x5f, 0x49, 0x53, 0x5f, 0x45, 0x58, 0x49, 0x53, 0x54, 0x10, 0xba, 0x8e, 0x03, 0x12, 0x25,
	0x0a, 0x1f, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x53, 0x45, 0x52, 0x56, 0x49, 0x43, 0x45, 0x5f, 0x43,
	0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x46, 0x4f, 0x55, 0x4e,
	0x44, 0x10, 0xbb, 0x8e, 0x03, 0x12, 0x1d, 0x0a, 0x17, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x49, 0x4e,
	0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x56, 0x41, 0x4c, 0x55, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x10, 0xbc, 0x8e, 0x03, 0x12, 0x20, 0x0a, 0x1a, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x41, 0x50, 0x50,
	0x4c, 0x59, 0x5f, 0x50, 0x41, 0x43, 0x4b, 0x41, 0x47, 0x45, 0x5f, 0x43, 0x48, 0x41, 0x4e, 0x47,
	0x45, 0x44, 0x10, 0xbd, 0x8e, 0x03, 0x12, 0x21, 0x0a, 0x1b, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x49,
	0x4e, 0x56, 0x4f, 0x49, 0x43, 0x45, 0x5f, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x53,
	0x54, 0x41, 0x54, 0x55, 0x53, 0x10, 0xbe, 0x8e, 0x03, 0x12, 0x1c, 0x0a, 0x16, 0x43, 0x4f, 0x44,
	0x45, 0x5f, 0x53, 0x45, 0x52, 0x56, 0x49, 0x43, 0x45, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x46, 0x4f,
	0x55, 0x4e, 0x44, 0x10, 0xbf, 0x8e, 0x03, 0x12, 0x25, 0x0a, 0x1f, 0x43, 0x4f, 0x44, 0x45, 0x5f,
	0x41, 0x50, 0x50, 0x4f, 0x49, 0x4e, 0x54, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x49, 0x4e, 0x56, 0x41,
	0x4c, 0x49, 0x44, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x10, 0xc0, 0x8e, 0x03, 0x12, 0x1f,
	0x0a, 0x19, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x53, 0x45, 0x52, 0x56, 0x49, 0x43, 0x45, 0x5f, 0x48,
	0x41, 0x56, 0x45, 0x5f, 0x42, 0x49, 0x4e, 0x44, 0x49, 0x4e, 0x47, 0x10, 0xc1, 0x8e, 0x03, 0x12,
	0x28, 0x0a, 0x22, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x4e, 0x4f, 0x5f, 0x41, 0x56, 0x41, 0x49, 0x4c,
	0x41, 0x42, 0x4c, 0x45, 0x5f, 0x53, 0x45, 0x52, 0x56, 0x49, 0x43, 0x45, 0x5f, 0x53, 0x45, 0x4c,
	0x45, 0x43, 0x54, 0x45, 0x44, 0x10, 0xc2, 0x8e, 0x03, 0x12, 0x1e, 0x0a, 0x18, 0x43, 0x4f, 0x44,
	0x45, 0x5f, 0x4e, 0x4f, 0x5f, 0x43, 0x55, 0x53, 0x54, 0x4f, 0x4d, 0x45, 0x52, 0x5f, 0x41, 0x44,
	0x44, 0x52, 0x45, 0x53, 0x53, 0x10, 0xc3, 0x8e, 0x03, 0x12, 0x21, 0x0a, 0x1b, 0x43, 0x4f, 0x44,
	0x45, 0x5f, 0x54, 0x4f, 0x4f, 0x5f, 0x4d, 0x41, 0x4e, 0x59, 0x5f, 0x44, 0x41, 0x59, 0x53, 0x5f,
	0x54, 0x4f, 0x5f, 0x51, 0x55, 0x45, 0x52, 0x59, 0x10, 0xc4, 0x8e, 0x03, 0x12, 0x29, 0x0a, 0x23,
	0x43, 0x4f, 0x44, 0x45, 0x5f, 0x4e, 0x4f, 0x5f, 0x41, 0x56, 0x41, 0x49, 0x4c, 0x41, 0x42, 0x4c,
	0x45, 0x5f, 0x53, 0x54, 0x41, 0x46, 0x46, 0x5f, 0x57, 0x48, 0x45, 0x4e, 0x5f, 0x53, 0x55, 0x42,
	0x4d, 0x49, 0x54, 0x10, 0xc5, 0x8e, 0x03, 0x12, 0x20, 0x0a, 0x1a, 0x43, 0x4f, 0x44, 0x45, 0x5f,
	0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x4e, 0x45, 0x45, 0x44,
	0x5f, 0x4f, 0x50, 0x45, 0x4e, 0x10, 0xc6, 0x8e, 0x03, 0x12, 0x21, 0x0a, 0x1b, 0x43, 0x4f, 0x44,
	0x45, 0x5f, 0x47, 0x4f, 0x4f, 0x47, 0x4c, 0x45, 0x5f, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44,
	0x5f, 0x41, 0x44, 0x44, 0x52, 0x45, 0x53, 0x53, 0x10, 0xc7, 0x8e, 0x03, 0x12, 0x1f, 0x0a, 0x19,
	0x43, 0x4f, 0x44, 0x45, 0x5f, 0x54, 0x41, 0x53, 0x4b, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x55, 0x52,
	0x45, 0x5f, 0x47, 0x45, 0x4e, 0x45, 0x52, 0x41, 0x4c, 0x10, 0xc8, 0x8e, 0x03, 0x12, 0x25, 0x0a,
	0x1f, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x54, 0x41, 0x53, 0x4b, 0x5f, 0x4d, 0x45, 0x53, 0x53, 0x41,
	0x47, 0x45, 0x5f, 0x52, 0x45, 0x53, 0x45, 0x54, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x55, 0x52, 0x45,
	0x10, 0xc9, 0x8e, 0x03, 0x12, 0x20, 0x0a, 0x1a, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x54, 0x4f, 0x4f,
	0x5f, 0x4d, 0x41, 0x4e, 0x59, 0x5f, 0x41, 0x50, 0x50, 0x4f, 0x49, 0x4e, 0x54, 0x4d, 0x45, 0x4e,
	0x54, 0x53, 0x10, 0xca, 0x8e, 0x03, 0x12, 0x2c, 0x0a, 0x26, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x53,
	0x45, 0x52, 0x56, 0x49, 0x43, 0x45, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f,
	0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x49, 0x53, 0x5f, 0x54, 0x4f, 0x4f, 0x5f, 0x4c, 0x4f, 0x4e, 0x47,
	0x10, 0xcb, 0x8e, 0x03, 0x12, 0x1e, 0x0a, 0x18, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x52, 0x45, 0x50,
	0x45, 0x41, 0x54, 0x5f, 0x49, 0x53, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x46, 0x4f, 0x55, 0x4e, 0x44,
	0x10, 0xcc, 0x8e, 0x03, 0x12, 0x1f, 0x0a, 0x19, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x51, 0x55, 0x49,
	0x43, 0x4b, 0x42, 0x4f, 0x4f, 0x4b, 0x53, 0x5f, 0x44, 0x45, 0x56, 0x5f, 0x45, 0x52, 0x52, 0x4f,
	0x52, 0x10, 0xcd, 0x8e, 0x03, 0x12, 0x21, 0x0a, 0x1b, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x51, 0x55,
	0x49, 0x43, 0x4b, 0x42, 0x4f, 0x4f, 0x4b, 0x53, 0x5f, 0x4f, 0x41, 0x55, 0x54, 0x48, 0x5f, 0x45,
	0x52, 0x52, 0x4f, 0x52, 0x10, 0xce, 0x8e, 0x03, 0x12, 0x29, 0x0a, 0x23, 0x43, 0x4f, 0x44, 0x45,
	0x5f, 0x51, 0x55, 0x49, 0x43, 0x4b, 0x42, 0x4f, 0x4f, 0x4b, 0x53, 0x5f, 0x52, 0x45, 0x46, 0x52,
	0x45, 0x53, 0x48, 0x5f, 0x54, 0x4f, 0x4b, 0x45, 0x4e, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x10,
	0xcf, 0x8e, 0x03, 0x12, 0x2a, 0x0a, 0x24, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x51, 0x55, 0x49, 0x43,
	0x4b, 0x42, 0x4f, 0x4f, 0x4b, 0x53, 0x5f, 0x55, 0x4e, 0x45, 0x58, 0x50, 0x45, 0x43, 0x54, 0x45,
	0x44, 0x5f, 0x45, 0x58, 0x43, 0x45, 0x50, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0xd0, 0x8e, 0x03, 0x12,
	0x23, 0x0a, 0x1d, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x51, 0x55, 0x49, 0x43, 0x4b, 0x42, 0x4f, 0x4f,
	0x4b, 0x53, 0x5f, 0x53, 0x45, 0x54, 0x54, 0x49, 0x4e, 0x47, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52,
	0x10, 0xd1, 0x8e, 0x03, 0x12, 0x30, 0x0a, 0x2a, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x51, 0x55, 0x49,
	0x43, 0x4b, 0x42, 0x4f, 0x4f, 0x4b, 0x53, 0x5f, 0x44, 0x41, 0x54, 0x41, 0x5f, 0x44, 0x55, 0x50,
	0x4c, 0x49, 0x43, 0x41, 0x54, 0x45, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x45, 0x58, 0x49, 0x53,
	0x54, 0x53, 0x10, 0xd2, 0x8e, 0x03, 0x12, 0x37, 0x0a, 0x31, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x51,
	0x55, 0x49, 0x43, 0x4b, 0x42, 0x4f, 0x4f, 0x4b, 0x53, 0x5f, 0x44, 0x41, 0x54, 0x41, 0x5f, 0x44,
	0x55, 0x50, 0x4c, 0x49, 0x43, 0x41, 0x54, 0x45, 0x5f, 0x45, 0x4d, 0x41, 0x49, 0x4c, 0x5f, 0x46,
	0x4f, 0x52, 0x4d, 0x41, 0x54, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x10, 0xd3, 0x8e, 0x03, 0x12,
	0x23, 0x0a, 0x1d, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x47, 0x4f, 0x4f, 0x47, 0x4c, 0x45, 0x5f, 0x4f,
	0x41, 0x55, 0x54, 0x48, 0x5f, 0x43, 0x48, 0x45, 0x43, 0x4b, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52,
	0x10, 0xd4, 0x8e, 0x03, 0x12, 0x1d, 0x0a, 0x17, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x47, 0x4f, 0x4f,
	0x47, 0x4c, 0x45, 0x5f, 0x4f, 0x41, 0x55, 0x54, 0x48, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x10,
	0xd5, 0x8e, 0x03, 0x12, 0x1f, 0x0a, 0x19, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x47, 0x4f, 0x4f, 0x47,
	0x4c, 0x45, 0x5f, 0x4f, 0x41, 0x55, 0x54, 0x48, 0x5f, 0x49, 0x53, 0x5f, 0x55, 0x53, 0x45, 0x44,
	0x10, 0xd6, 0x8e, 0x03, 0x12, 0x1c, 0x0a, 0x16, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x55, 0x50, 0x43,
	0x4f, 0x4d, 0x49, 0x4e, 0x47, 0x5f, 0x49, 0x44, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x10, 0xd7,
	0x8e, 0x03, 0x12, 0x17, 0x0a, 0x11, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x43, 0x52, 0x45, 0x41, 0x54,
	0x45, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x10, 0xd8, 0x8e, 0x03, 0x12, 0x1c, 0x0a, 0x16, 0x43,
	0x4f, 0x44, 0x45, 0x5f, 0x57, 0x41, 0x54, 0x43, 0x48, 0x5f, 0x45, 0x56, 0x45, 0x4e, 0x54, 0x5f,
	0x45, 0x52, 0x52, 0x4f, 0x52, 0x10, 0xd9, 0x8e, 0x03, 0x12, 0x1a, 0x0a, 0x14, 0x43, 0x4f, 0x44,
	0x45, 0x5f, 0x47, 0x45, 0x54, 0x5f, 0x45, 0x56, 0x45, 0x4e, 0x54, 0x5f, 0x45, 0x52, 0x52, 0x4f,
	0x52, 0x10, 0xda, 0x8e, 0x03, 0x12, 0x2f, 0x0a, 0x29, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x53, 0x53,
	0x5f, 0x46, 0x4f, 0x52, 0x5f, 0x52, 0x45, 0x50, 0x45, 0x41, 0x54, 0x5f, 0x46, 0x52, 0x45, 0x51,
	0x55, 0x45, 0x4e, 0x43, 0x59, 0x5f, 0x50, 0x41, 0x52, 0x41, 0x4d, 0x53, 0x5f, 0x45, 0x52, 0x52,
	0x4f, 0x52, 0x10, 0xdb, 0x8e, 0x03, 0x12, 0x2c, 0x0a, 0x26, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x46,
	0x52, 0x45, 0x45, 0x5f, 0x53, 0x53, 0x5f, 0x46, 0x4f, 0x52, 0x5f, 0x52, 0x45, 0x50, 0x45, 0x41,
	0x54, 0x5f, 0x54, 0x49, 0x4d, 0x45, 0x53, 0x5f, 0x55, 0x53, 0x45, 0x44, 0x5f, 0x4f, 0x55, 0x54,
	0x10, 0xdc, 0x8e, 0x03, 0x12, 0x29, 0x0a, 0x23, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x53, 0x53, 0x5f,
	0x46, 0x4f, 0x52, 0x5f, 0x52, 0x45, 0x50, 0x45, 0x41, 0x54, 0x5f, 0x54, 0x49, 0x4d, 0x45, 0x53,
	0x5f, 0x4f, 0x56, 0x45, 0x52, 0x5f, 0x4c, 0x49, 0x4d, 0x49, 0x54, 0x10, 0xdd, 0x8e, 0x03, 0x12,
	0x21, 0x0a, 0x1b, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x49, 0x4e, 0x56, 0x4f, 0x49, 0x43, 0x45, 0x5f,
	0x53, 0x45, 0x54, 0x5f, 0x54, 0x49, 0x50, 0x53, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x10, 0xde,
	0x8e, 0x03, 0x12, 0x21, 0x0a, 0x1b, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x51, 0x55, 0x45, 0x53, 0x54,
	0x49, 0x4f, 0x4e, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x49, 0x53, 0x5f, 0x45, 0x58, 0x49, 0x53,
	0x54, 0x10, 0xdf, 0x8e, 0x03, 0x12, 0x1c, 0x0a, 0x16, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x49, 0x4e,
	0x56, 0x4f, 0x49, 0x43, 0x45, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x46, 0x4f, 0x55, 0x4e, 0x44, 0x10,
	0xe0, 0x8e, 0x03, 0x12, 0x1b, 0x0a, 0x15, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x50, 0x45, 0x54, 0x5f,
	0x53, 0x49, 0x5a, 0x45, 0x5f, 0x49, 0x4e, 0x5f, 0x55, 0x53, 0x45, 0x44, 0x10, 0xe1, 0x8e, 0x03,
	0x12, 0x28, 0x0a, 0x22, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x47, 0x52, 0x4f, 0x4f, 0x4d, 0x49, 0x4e,
	0x47, 0x5f, 0x52, 0x45, 0x50, 0x4f, 0x52, 0x54, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x41, 0x56, 0x41,
	0x49, 0x4c, 0x41, 0x42, 0x4c, 0x45, 0x10, 0xe2, 0x8e, 0x03, 0x12, 0x2d, 0x0a, 0x27, 0x43, 0x4f,
	0x44, 0x45, 0x5f, 0x47, 0x52, 0x4f, 0x4f, 0x4d, 0x49, 0x4e, 0x47, 0x5f, 0x52, 0x45, 0x50, 0x4f,
	0x52, 0x54, 0x5f, 0x42, 0x4f, 0x4f, 0x4b, 0x5f, 0x41, 0x47, 0x41, 0x49, 0x4e, 0x5f, 0x45, 0x58,
	0x50, 0x49, 0x52, 0x45, 0x44, 0x10, 0xe3, 0x8e, 0x03, 0x12, 0x27, 0x0a, 0x21, 0x43, 0x4f, 0x44,
	0x45, 0x5f, 0x53, 0x45, 0x52, 0x56, 0x49, 0x43, 0x45, 0x5f, 0x43, 0x48, 0x41, 0x52, 0x47, 0x45,
	0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x41, 0x56, 0x41, 0x49, 0x4c, 0x41, 0x42, 0x4c, 0x45, 0x10, 0xe4,
	0x8e, 0x03, 0x12, 0x19, 0x0a, 0x13, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x45, 0x4d, 0x50, 0x54, 0x59,
	0x5f, 0x50, 0x45, 0x54, 0x5f, 0x4c, 0x49, 0x53, 0x54, 0x10, 0xe5, 0x8e, 0x03, 0x12, 0x1d, 0x0a,
	0x17, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x45, 0x4d, 0x50, 0x54, 0x59, 0x5f, 0x53, 0x45, 0x52, 0x56,
	0x49, 0x43, 0x45, 0x5f, 0x4c, 0x49, 0x53, 0x54, 0x10, 0xe6, 0x8e, 0x03, 0x12, 0x29, 0x0a, 0x23,
	0x43, 0x4f, 0x44, 0x45, 0x5f, 0x41, 0x50, 0x50, 0x4f, 0x49, 0x4e, 0x54, 0x4d, 0x45, 0x4e, 0x54,
	0x5f, 0x54, 0x52, 0x41, 0x43, 0x4b, 0x49, 0x4e, 0x47, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x46, 0x4f,
	0x55, 0x4e, 0x44, 0x10, 0xe7, 0x8e, 0x03, 0x12, 0x2f, 0x0a, 0x29, 0x43, 0x4f, 0x44, 0x45, 0x5f,
	0x41, 0x50, 0x50, 0x4f, 0x49, 0x4e, 0x54, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x52, 0x41, 0x43,
	0x4b, 0x49, 0x4e, 0x47, 0x5f, 0x53, 0x48, 0x41, 0x52, 0x45, 0x44, 0x5f, 0x42, 0x59, 0x5f, 0x4f,
	0x54, 0x48, 0x45, 0x52, 0x10, 0xe8, 0x8e, 0x03, 0x12, 0x45, 0x0a, 0x3f, 0x43, 0x4f, 0x44, 0x45,
	0x5f, 0x41, 0x50, 0x50, 0x4f, 0x49, 0x4e, 0x54, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x52, 0x41,
	0x43, 0x4b, 0x49, 0x4e, 0x47, 0x5f, 0x53, 0x54, 0x41, 0x46, 0x46, 0x5f, 0x48, 0x41, 0x53, 0x5f,
	0x41, 0x4e, 0x4f, 0x54, 0x48, 0x45, 0x52, 0x5f, 0x53, 0x48, 0x41, 0x52, 0x49, 0x4e, 0x47, 0x5f,
	0x41, 0x50, 0x50, 0x4f, 0x49, 0x4e, 0x54, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0xe9, 0x8e, 0x03, 0x12,
	0x48, 0x0a, 0x42, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x41, 0x50, 0x50, 0x4f, 0x49, 0x4e, 0x54, 0x4d,
	0x45, 0x4e, 0x54, 0x5f, 0x54, 0x52, 0x41, 0x43, 0x4b, 0x49, 0x4e, 0x47, 0x5f, 0x53, 0x54, 0x41,
	0x46, 0x46, 0x5f, 0x4c, 0x4f, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x41, 0x54,
	0x55, 0x53, 0x5f, 0x43, 0x48, 0x41, 0x4e, 0x47, 0x45, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x41, 0x4c,
	0x4c, 0x4f, 0x57, 0x45, 0x44, 0x10, 0xea, 0x8e, 0x03, 0x12, 0x1c, 0x0a, 0x16, 0x43, 0x4f, 0x44,
	0x45, 0x5f, 0x42, 0x55, 0x53, 0x49, 0x4e, 0x45, 0x53, 0x53, 0x5f, 0x49, 0x53, 0x5f, 0x45, 0x4d,
	0x50, 0x54, 0x59, 0x10, 0xe0, 0xd4, 0x03, 0x12, 0x1d, 0x0a, 0x17, 0x43, 0x4f, 0x44, 0x45, 0x5f,
	0x53, 0x55, 0x50, 0x50, 0x4c, 0x49, 0x45, 0x52, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x46, 0x4f, 0x55,
	0x4e, 0x44, 0x10, 0xe1, 0xd4, 0x03, 0x12, 0x1d, 0x0a, 0x17, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x43,
	0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x46, 0x4f, 0x55, 0x4e,
	0x44, 0x10, 0xe2, 0xd4, 0x03, 0x12, 0x1c, 0x0a, 0x16, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x50, 0x41,
	0x43, 0x4b, 0x41, 0x47, 0x45, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x46, 0x4f, 0x55, 0x4e, 0x44, 0x10,
	0xe3, 0xd4, 0x03, 0x12, 0x1c, 0x0a, 0x16, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x50, 0x52, 0x4f, 0x44,
	0x55, 0x43, 0x54, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x46, 0x4f, 0x55, 0x4e, 0x44, 0x10, 0xe4, 0xd4,
	0x03, 0x12, 0x19, 0x0a, 0x13, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x43, 0x41, 0x52, 0x54, 0x5f, 0x4e,
	0x4f, 0x54, 0x5f, 0x46, 0x4f, 0x55, 0x4e, 0x44, 0x10, 0xe5, 0xd4, 0x03, 0x12, 0x18, 0x0a, 0x12,
	0x43, 0x4f, 0x44, 0x45, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x49, 0x53, 0x5f, 0x45, 0x58, 0x49,
	0x53, 0x54, 0x10, 0xe6, 0xd4, 0x03, 0x12, 0x17, 0x0a, 0x11, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x53,
	0x4b, 0x55, 0x5f, 0x49, 0x53, 0x5f, 0x45, 0x58, 0x49, 0x53, 0x54, 0x10, 0xe7, 0xd4, 0x03, 0x12,
	0x13, 0x0a, 0x0d, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x50, 0x41, 0x49, 0x44,
	0x10, 0xe8, 0xd4, 0x03, 0x12, 0x1a, 0x0a, 0x14, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x49, 0x4e, 0x56,
	0x41, 0x4c, 0x49, 0x44, 0x5f, 0x43, 0x41, 0x52, 0x54, 0x5f, 0x49, 0x44, 0x10, 0xe9, 0xd4, 0x03,
	0x12, 0x22, 0x0a, 0x1c, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x43, 0x41, 0x52, 0x54, 0x5f, 0x41, 0x4c,
	0x52, 0x45, 0x41, 0x44, 0x59, 0x5f, 0x50, 0x52, 0x4f, 0x43, 0x45, 0x53, 0x53, 0x49, 0x4e, 0x47,
	0x10, 0xea, 0xd4, 0x03, 0x12, 0x21, 0x0a, 0x1b, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x43, 0x41, 0x52,
	0x54, 0x5f, 0x41, 0x4c, 0x52, 0x45, 0x41, 0x44, 0x59, 0x5f, 0x43, 0x4f, 0x4d, 0x50, 0x4c, 0x45,
	0x54, 0x45, 0x44, 0x10, 0xeb, 0xd4, 0x03, 0x12, 0x20, 0x0a, 0x1a, 0x43, 0x4f, 0x44, 0x45, 0x5f,
	0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x44, 0x49, 0x53, 0x43, 0x4f, 0x55, 0x4e, 0x54,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x10, 0xec, 0xd4, 0x03, 0x12, 0x18, 0x0a, 0x12, 0x43, 0x4f, 0x44,
	0x45, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x49, 0x53, 0x5f, 0x45, 0x4d, 0x50, 0x54, 0x59, 0x10,
	0xed, 0xd4, 0x03, 0x12, 0x19, 0x0a, 0x13, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x46,
	0x46, 0x5f, 0x49, 0x53, 0x5f, 0x45, 0x4d, 0x50, 0x54, 0x59, 0x10, 0xee, 0xd4, 0x03, 0x12, 0x1b,
	0x0a, 0x15, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x42, 0x41, 0x52, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x49,
	0x53, 0x5f, 0x45, 0x58, 0x49, 0x53, 0x54, 0x10, 0xef, 0xd4, 0x03, 0x12, 0x1d, 0x0a, 0x17, 0x43,
	0x4f, 0x44, 0x45, 0x5f, 0x50, 0x41, 0x59, 0x5f, 0x41, 0x4d, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x49,
	0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x10, 0xf1, 0xa2, 0x04, 0x12, 0x1b, 0x0a, 0x15, 0x43, 0x4f,
	0x44, 0x45, 0x5f, 0x50, 0x41, 0x59, 0x5f, 0x4d, 0x4f, 0x44, 0x55, 0x4c, 0x45, 0x5f, 0x45, 0x4d,
	0x50, 0x54, 0x59, 0x10, 0xf2, 0xa2, 0x04, 0x12, 0x1f, 0x0a, 0x19, 0x43, 0x4f, 0x44, 0x45, 0x5f,
	0x50, 0x41, 0x59, 0x5f, 0x49, 0x4e, 0x56, 0x4f, 0x49, 0x43, 0x45, 0x5f, 0x49, 0x44, 0x5f, 0x45,
	0x4d, 0x50, 0x54, 0x59, 0x10, 0xf3, 0xa2, 0x04, 0x12, 0x1d, 0x0a, 0x17, 0x43, 0x4f, 0x44, 0x45,
	0x5f, 0x50, 0x41, 0x59, 0x5f, 0x4d, 0x45, 0x54, 0x48, 0x4f, 0x44, 0x5f, 0x49, 0x4e, 0x56, 0x41,
	0x4c, 0x49, 0x44, 0x10, 0xf4, 0xa2, 0x04, 0x12, 0x1b, 0x0a, 0x15, 0x43, 0x4f, 0x44, 0x45, 0x5f,
	0x50, 0x41, 0x59, 0x5f, 0x44, 0x41, 0x54, 0x41, 0x5f, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44,
	0x10, 0xf5, 0xa2, 0x04, 0x12, 0x1f, 0x0a, 0x19, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x49, 0x4e, 0x56,
	0x41, 0x4c, 0x49, 0x44, 0x5f, 0x43, 0x48, 0x45, 0x43, 0x4b, 0x5f, 0x4e, 0x55, 0x4d, 0x42, 0x45,
	0x52, 0x10, 0xf6, 0xa2, 0x04, 0x12, 0x20, 0x0a, 0x1a, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x52, 0x45,
	0x46, 0x55, 0x4e, 0x44, 0x5f, 0x41, 0x4d, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x49, 0x4e, 0x56, 0x41,
	0x4c, 0x49, 0x44, 0x10, 0xf7, 0xa2, 0x04, 0x12, 0x22, 0x0a, 0x1c, 0x43, 0x4f, 0x44, 0x45, 0x5f,
	0x53, 0x54, 0x52, 0x49, 0x50, 0x45, 0x5f, 0x49, 0x4e, 0x54, 0x45, 0x4e, 0x54, 0x5f, 0x4e, 0x4f,
	0x54, 0x5f, 0x46, 0x4f, 0x55, 0x4e, 0x44, 0x10, 0xf8, 0xa2, 0x04, 0x12, 0x1c, 0x0a, 0x16, 0x43,
	0x4f, 0x44, 0x45, 0x5f, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x4e, 0x4f, 0x54, 0x5f,
	0x46, 0x4f, 0x55, 0x4e, 0x44, 0x10, 0xf9, 0xa2, 0x04, 0x12, 0x23, 0x0a, 0x1d, 0x43, 0x4f, 0x44,
	0x45, 0x5f, 0x53, 0x54, 0x52, 0x49, 0x50, 0x45, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54,
	0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x46, 0x4f, 0x55, 0x4e, 0x44, 0x10, 0xfb, 0xa2, 0x04, 0x12, 0x22,
	0x0a, 0x1c, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x53, 0x55, 0x42, 0x53, 0x43, 0x52, 0x49, 0x50, 0x54,
	0x49, 0x4f, 0x4e, 0x5f, 0x45, 0x58, 0x50, 0x49, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0xfc,
	0xa2, 0x04, 0x12, 0x21, 0x0a, 0x1b, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x53, 0x55, 0x42, 0x53, 0x43,
	0x52, 0x49, 0x50, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x45, 0x58, 0x49, 0x53,
	0x54, 0x10, 0xfd, 0xa2, 0x04, 0x12, 0x22, 0x0a, 0x1c, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x43, 0x4f,
	0x4d, 0x50, 0x41, 0x4e, 0x59, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x45, 0x5f, 0x4e, 0x4f, 0x54, 0x5f,
	0x56, 0x41, 0x4c, 0x49, 0x44, 0x10, 0xfe, 0xa2, 0x04, 0x12, 0x1f, 0x0a, 0x19, 0x43, 0x4f, 0x44,
	0x45, 0x5f, 0x53, 0x54, 0x52, 0x49, 0x50, 0x45, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54,
	0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x10, 0xff, 0xa2, 0x04, 0x12, 0x22, 0x0a, 0x1c, 0x43, 0x4f,
	0x44, 0x45, 0x5f, 0x52, 0x45, 0x46, 0x55, 0x53, 0x45, 0x5f, 0x54, 0x4f, 0x5f, 0x53, 0x54, 0x52,
	0x49, 0x50, 0x45, 0x5f, 0x53, 0x45, 0x54, 0x5f, 0x55, 0x50, 0x10, 0x80, 0xa3, 0x04, 0x12, 0x21,
	0x0a, 0x1b, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x53, 0x55, 0x42, 0x53, 0x43, 0x52, 0x49, 0x50, 0x54,
	0x49, 0x4f, 0x4e, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x10, 0x81, 0xa3,
	0x04, 0x12, 0x1a, 0x0a, 0x14, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x52, 0x45, 0x46, 0x55, 0x4e, 0x44,
	0x5f, 0x54, 0x49, 0x4d, 0x45, 0x5f, 0x4f, 0x55, 0x54, 0x10, 0x82, 0xa3, 0x04, 0x12, 0x20, 0x0a,
	0x1a, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x53, 0x54, 0x52, 0x49, 0x50, 0x45, 0x5f, 0x43, 0x41, 0x52,
	0x44, 0x5f, 0x45, 0x58, 0x43, 0x45, 0x50, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x83, 0xa3, 0x04, 0x12,
	0x29, 0x0a, 0x23, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x54, 0x45, 0x52, 0x4d, 0x49, 0x4e, 0x41, 0x4c,
	0x5f, 0x52, 0x45, 0x41, 0x44, 0x45, 0x52, 0x5f, 0x49, 0x4e, 0x5f, 0x50, 0x52, 0x4f, 0x47, 0x52,
	0x45, 0x53, 0x53, 0x49, 0x4e, 0x47, 0x10, 0x84, 0xa3, 0x04, 0x12, 0x24, 0x0a, 0x1e, 0x43, 0x4f,
	0x44, 0x45, 0x5f, 0x50, 0x52, 0x45, 0x5f, 0x41, 0x55, 0x54, 0x48, 0x45, 0x4e, 0x54, 0x49, 0x43,
	0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x85, 0xa3, 0x04,
	0x12, 0x28, 0x0a, 0x22, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x50, 0x52, 0x45, 0x5f, 0x41, 0x55, 0x54,
	0x48, 0x45, 0x4e, 0x54, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x44, 0x55, 0x50, 0x4c,
	0x49, 0x43, 0x41, 0x54, 0x45, 0x44, 0x10, 0x86, 0xa3, 0x04, 0x12, 0x2a, 0x0a, 0x24, 0x43, 0x4f,
	0x44, 0x45, 0x5f, 0x50, 0x52, 0x45, 0x5f, 0x41, 0x55, 0x54, 0x48, 0x45, 0x4e, 0x54, 0x49, 0x43,
	0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x45, 0x52, 0x52,
	0x4f, 0x52, 0x10, 0x87, 0xa3, 0x04, 0x12, 0x25, 0x0a, 0x1f, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x44,
	0x45, 0x50, 0x4f, 0x53, 0x49, 0x54, 0x5f, 0x48, 0x41, 0x53, 0x5f, 0x50, 0x41, 0x49, 0x44, 0x5f,
	0x45, 0x58, 0x43, 0x45, 0x50, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x88, 0xa3, 0x04, 0x12, 0x20, 0x0a,
	0x1a, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x41, 0x32, 0x50, 0x5f, 0x4e, 0x4f, 0x57, 0x5f, 0x41, 0x4c,
	0x4c, 0x4f, 0x57, 0x5f, 0x45, 0x44, 0x49, 0x54, 0x49, 0x4e, 0x47, 0x10, 0x89, 0xa3, 0x04, 0x12,
	0x1c, 0x0a, 0x16, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x41, 0x32, 0x50, 0x5f, 0x43, 0x48, 0x41, 0x52,
	0x47, 0x45, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x8a, 0xa3, 0x04, 0x12, 0x23, 0x0a,
	0x1d, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x53, 0x54,
	0x41, 0x54, 0x55, 0x53, 0x5f, 0x45, 0x58, 0x43, 0x45, 0x50, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x8b,
	0xa3, 0x04, 0x12, 0x25, 0x0a, 0x1f, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x53, 0x55, 0x42, 0x53, 0x43,
	0x52, 0x49, 0x50, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x46,
	0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x8c, 0xa3, 0x04, 0x12, 0x2c, 0x0a, 0x26, 0x43, 0x4f, 0x44,
	0x45, 0x5f, 0x53, 0x54, 0x52, 0x49, 0x50, 0x45, 0x5f, 0x43, 0x4f, 0x4d, 0x50, 0x41, 0x4e, 0x59,
	0x5f, 0x43, 0x55, 0x53, 0x54, 0x4f, 0x4d, 0x45, 0x52, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x46, 0x4f,
	0x55, 0x4e, 0x44, 0x10, 0x8d, 0xa3, 0x04, 0x12, 0x2b, 0x0a, 0x25, 0x43, 0x4f, 0x44, 0x45, 0x5f,
	0x53, 0x54, 0x52, 0x49, 0x50, 0x45, 0x5f, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x43, 0x55,
	0x53, 0x54, 0x4f, 0x4d, 0x45, 0x52, 0x5f, 0x45, 0x58, 0x43, 0x45, 0x50, 0x54, 0x49, 0x4f, 0x4e,
	0x10, 0x8e, 0xa3, 0x04, 0x12, 0x33, 0x0a, 0x2d, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x53, 0x54, 0x52,
	0x49, 0x50, 0x45, 0x5f, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x5f, 0x48, 0x41, 0x52, 0x44, 0x57,
	0x41, 0x52, 0x45, 0x5f, 0x49, 0x4e, 0x56, 0x4f, 0x49, 0x43, 0x45, 0x5f, 0x45, 0x58, 0x43, 0x45,
	0x50, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x8f, 0xa3, 0x04, 0x12, 0x31, 0x0a, 0x2b, 0x43, 0x4f, 0x44,
	0x45, 0x5f, 0x53, 0x54, 0x52, 0x49, 0x50, 0x45, 0x5f, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x5f,
	0x48, 0x41, 0x52, 0x44, 0x57, 0x41, 0x52, 0x45, 0x5f, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x45,
	0x58, 0x43, 0x45, 0x50, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x90, 0xa3, 0x04, 0x12, 0x2c, 0x0a, 0x26,
	0x43, 0x4f, 0x44, 0x45, 0x5f, 0x50, 0x4c, 0x41, 0x54, 0x46, 0x4f, 0x52, 0x4d, 0x5f, 0x43, 0x41,
	0x52, 0x45, 0x5f, 0x41, 0x47, 0x52, 0x45, 0x45, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x4e, 0x4f, 0x54,
	0x5f, 0x46, 0x4f, 0x55, 0x4e, 0x44, 0x10, 0x91, 0xa3, 0x04, 0x12, 0x29, 0x0a, 0x23, 0x43, 0x4f,
	0x44, 0x45, 0x5f, 0x50, 0x4c, 0x41, 0x54, 0x46, 0x4f, 0x52, 0x4d, 0x5f, 0x43, 0x41, 0x52, 0x45,
	0x5f, 0x52, 0x45, 0x43, 0x4f, 0x52, 0x44, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x46, 0x4f, 0x55, 0x4e,
	0x44, 0x10, 0x92, 0xa3, 0x04, 0x12, 0x32, 0x0a, 0x2c, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x50, 0x4c,
	0x41, 0x54, 0x46, 0x4f, 0x52, 0x4d, 0x5f, 0x43, 0x41, 0x52, 0x45, 0x5f, 0x52, 0x45, 0x43, 0x4f,
	0x52, 0x44, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x46, 0x4f, 0x55, 0x4e, 0x44, 0x5f, 0x42, 0x59, 0x5f,
	0x45, 0x4d, 0x41, 0x49, 0x4c, 0x10, 0x93, 0xa3, 0x04, 0x12, 0x2f, 0x0a, 0x29, 0x43, 0x4f, 0x44,
	0x45, 0x5f, 0x50, 0x4c, 0x41, 0x54, 0x46, 0x4f, 0x52, 0x4d, 0x5f, 0x43, 0x41, 0x52, 0x45, 0x5f,
	0x45, 0x4d, 0x41, 0x49, 0x4c, 0x5f, 0x41, 0x44, 0x44, 0x52, 0x45, 0x53, 0x53, 0x5f, 0x4d, 0x49,
	0x53, 0x4d, 0x41, 0x54, 0x43, 0x48, 0x10, 0x94, 0xa3, 0x04, 0x12, 0x30, 0x0a, 0x2a, 0x43, 0x4f,
	0x44, 0x45, 0x5f, 0x50, 0x4c, 0x41, 0x54, 0x46, 0x4f, 0x52, 0x4d, 0x5f, 0x43, 0x41, 0x52, 0x45,
	0x5f, 0x41, 0x53, 0x53, 0x49, 0x47, 0x4e, 0x5f, 0x41, 0x47, 0x52, 0x45, 0x45, 0x4d, 0x45, 0x4e,
	0x54, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x95, 0xa3, 0x04, 0x12, 0x28, 0x0a, 0x22,
	0x43, 0x4f, 0x44, 0x45, 0x5f, 0x50, 0x4c, 0x41, 0x54, 0x46, 0x4f, 0x52, 0x4d, 0x5f, 0x43, 0x41,
	0x52, 0x45, 0x5f, 0x52, 0x45, 0x43, 0x4f, 0x52, 0x44, 0x5f, 0x49, 0x53, 0x5f, 0x45, 0x58, 0x49,
	0x53, 0x54, 0x10, 0x96, 0xa3, 0x04, 0x12, 0x2e, 0x0a, 0x28, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x45,
	0x4e, 0x54, 0x45, 0x52, 0x50, 0x52, 0x49, 0x53, 0x45, 0x5f, 0x53, 0x54, 0x52, 0x49, 0x50, 0x45,
	0x5f, 0x43, 0x55, 0x53, 0x54, 0x4f, 0x4d, 0x45, 0x52, 0x5f, 0x49, 0x53, 0x5f, 0x45, 0x58, 0x49,
	0x53, 0x54, 0x10, 0x97, 0xa3, 0x04, 0x12, 0x2b, 0x0a, 0x25, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x43,
	0x4f, 0x4d, 0x50, 0x41, 0x4e, 0x59, 0x5f, 0x53, 0x54, 0x52, 0x49, 0x50, 0x45, 0x5f, 0x43, 0x55,
	0x53, 0x54, 0x4f, 0x4d, 0x45, 0x52, 0x5f, 0x49, 0x53, 0x5f, 0x45, 0x58, 0x49, 0x53, 0x54, 0x10,
	0x98, 0xa3, 0x04, 0x12, 0x22, 0x0a, 0x1c, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x54, 0x57, 0x49, 0x4c,
	0x49, 0x4f, 0x5f, 0x46, 0x49, 0x4e, 0x45, 0x5f, 0x43, 0x48, 0x41, 0x52, 0x47, 0x45, 0x5f, 0x46,
	0x41, 0x49, 0x4c, 0x10, 0x99, 0xa3, 0x04, 0x12, 0x1f, 0x0a, 0x19, 0x43, 0x4f, 0x44, 0x45, 0x5f,
	0x4d, 0x4f, 0x45, 0x47, 0x4f, 0x5f, 0x50, 0x41, 0x59, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x53, 0x45,
	0x54, 0x5f, 0x55, 0x50, 0x10, 0x9a, 0xa3, 0x04, 0x12, 0x1b, 0x0a, 0x15, 0x43, 0x4f, 0x44, 0x45,
	0x5f, 0x52, 0x45, 0x46, 0x55, 0x4e, 0x44, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x46, 0x4f, 0x55, 0x4e,
	0x44, 0x10, 0x9b, 0xa3, 0x04, 0x12, 0x2b, 0x0a, 0x25, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x4d, 0x45,
	0x53, 0x53, 0x41, 0x47, 0x45, 0x5f, 0x41, 0x55, 0x54, 0x4f, 0x5f, 0x54, 0x45, 0x4d, 0x50, 0x4c,
	0x41, 0x54, 0x45, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x43, 0x4f, 0x4e, 0x46, 0x49, 0x47, 0x10, 0xe4,
	0xf1, 0x04, 0x12, 0x25, 0x0a, 0x1f, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x4d, 0x45, 0x53, 0x53, 0x41,
	0x47, 0x45, 0x5f, 0x53, 0x45, 0x4e, 0x44, 0x5f, 0x50, 0x48, 0x4f, 0x4e, 0x45, 0x5f, 0x49, 0x53,
	0x5f, 0x4e, 0x55, 0x4c, 0x4c, 0x10, 0xe5, 0xf1, 0x04, 0x12, 0x25, 0x0a, 0x1f, 0x43, 0x4f, 0x44,
	0x45, 0x5f, 0x4d, 0x45, 0x53, 0x53, 0x41, 0x47, 0x45, 0x5f, 0x53, 0x45, 0x4e, 0x44, 0x5f, 0x45,
	0x4d, 0x41, 0x49, 0x4c, 0x5f, 0x49, 0x53, 0x5f, 0x4e, 0x55, 0x4c, 0x4c, 0x10, 0xe6, 0xf1, 0x04,
	0x12, 0x26, 0x0a, 0x20, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x4d, 0x45, 0x53, 0x53, 0x41, 0x47, 0x45,
	0x5f, 0x53, 0x45, 0x4e, 0x44, 0x5f, 0x52, 0x45, 0x56, 0x49, 0x45, 0x57, 0x5f, 0x49, 0x53, 0x5f,
	0x4e, 0x55, 0x4c, 0x4c, 0x10, 0xe7, 0xf1, 0x04, 0x12, 0x24, 0x0a, 0x1e, 0x43, 0x4f, 0x44, 0x45,
	0x5f, 0x4d, 0x45, 0x53, 0x53, 0x41, 0x47, 0x45, 0x5f, 0x53, 0x45, 0x4e, 0x44, 0x5f, 0x50, 0x48,
	0x4f, 0x4e, 0x45, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0xe8, 0xf1, 0x04, 0x12, 0x24,
	0x0a, 0x1e, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x4d, 0x45, 0x53, 0x53, 0x41, 0x47, 0x45, 0x5f, 0x53,
	0x45, 0x4e, 0x44, 0x5f, 0x45, 0x4d, 0x41, 0x49, 0x4c, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44,
	0x10, 0xe9, 0xf1, 0x04, 0x12, 0x34, 0x0a, 0x2e, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x4d, 0x45, 0x53,
	0x53, 0x41, 0x47, 0x45, 0x5f, 0x43, 0x41, 0x4e, 0x4e, 0x4f, 0x54, 0x5f, 0x44, 0x45, 0x4c, 0x45,
	0x54, 0x45, 0x5f, 0x4f, 0x54, 0x48, 0x45, 0x52, 0x5f, 0x53, 0x54, 0x41, 0x46, 0x46, 0x5f, 0x4d,
	0x45, 0x53, 0x53, 0x41, 0x47, 0x45, 0x10, 0xea, 0xf1, 0x04, 0x12, 0x26, 0x0a, 0x20, 0x43, 0x4f,
	0x44, 0x45, 0x5f, 0x43, 0x55, 0x53, 0x54, 0x4f, 0x4d, 0x45, 0x52, 0x5f, 0x50, 0x48, 0x4f, 0x4e,
	0x45, 0x5f, 0x4e, 0x55, 0x4d, 0x42, 0x45, 0x52, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x10, 0xeb,
	0xf1, 0x04, 0x12, 0x26, 0x0a, 0x20, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x4e, 0x4f, 0x54, 0x49, 0x46,
	0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4e, 0x4f, 0x54,
	0x5f, 0x46, 0x4f, 0x55, 0x4e, 0x44, 0x10, 0xec, 0xf1, 0x04, 0x12, 0x21, 0x0a, 0x1b, 0x43, 0x4f,
	0x44, 0x45, 0x5f, 0x4d, 0x45, 0x53, 0x53, 0x41, 0x47, 0x45, 0x5f, 0x41, 0x4d, 0x4f, 0x55, 0x4e,
	0x54, 0x5f, 0x52, 0x55, 0x4e, 0x5f, 0x4f, 0x55, 0x54, 0x10, 0xed, 0xf1, 0x04, 0x12, 0x1a, 0x0a,
	0x14, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x53, 0x45, 0x4e, 0x44, 0x5f,
	0x4c, 0x49, 0x4d, 0x49, 0x54, 0x10, 0xee, 0xf1, 0x04, 0x12, 0x1c, 0x0a, 0x16, 0x43, 0x4f, 0x44,
	0x45, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x5f, 0x54, 0x4f, 0x5f, 0x53, 0x45, 0x4e, 0x44, 0x5f, 0x43,
	0x4f, 0x44, 0x45, 0x10, 0xef, 0xf1, 0x04, 0x12, 0x2b, 0x0a, 0x25, 0x43, 0x4f, 0x44, 0x45, 0x5f,
	0x42, 0x55, 0x53, 0x49, 0x4e, 0x45, 0x53, 0x53, 0x5f, 0x54, 0x57, 0x49, 0x4c, 0x49, 0x4f, 0x5f,
	0x4d, 0x45, 0x53, 0x53, 0x41, 0x47, 0x45, 0x5f, 0x4e, 0x4f, 0x45, 0x5f, 0x4d, 0x4f, 0x52, 0x45,
	0x10, 0xf0, 0xf1, 0x04, 0x12, 0x24, 0x0a, 0x1e, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x4d, 0x45, 0x53,
	0x53, 0x41, 0x47, 0x45, 0x5f, 0x43, 0x4f, 0x4e, 0x54, 0x52, 0x4f, 0x4c, 0x5f, 0x4e, 0x4f, 0x5f,
	0x52, 0x45, 0x43, 0x4f, 0x52, 0x44, 0x10, 0xf1, 0xf1, 0x04, 0x12, 0x1e, 0x0a, 0x18, 0x43, 0x4f,
	0x44, 0x45, 0x5f, 0x4d, 0x45, 0x53, 0x53, 0x41, 0x47, 0x45, 0x5f, 0x53, 0x45, 0x4e, 0x44, 0x5f,
	0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0xf2, 0xf1, 0x04, 0x12, 0x2e, 0x0a, 0x28, 0x43, 0x4f,
	0x44, 0x45, 0x5f, 0x4d, 0x45, 0x53, 0x53, 0x41, 0x47, 0x45, 0x5f, 0x53, 0x45, 0x4e, 0x44, 0x5f,
	0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x5f, 0x42, 0x59, 0x5f, 0x53, 0x57, 0x49, 0x54, 0x43, 0x48,
	0x5f, 0x43, 0x4c, 0x4f, 0x53, 0x45, 0x10, 0xf3, 0xf1, 0x04, 0x12, 0x33, 0x0a, 0x2d, 0x43, 0x4f,
	0x44, 0x45, 0x5f, 0x4d, 0x45, 0x53, 0x53, 0x41, 0x47, 0x45, 0x5f, 0x45, 0x4d, 0x41, 0x49, 0x4c,
	0x5f, 0x52, 0x45, 0x43, 0x45, 0x49, 0x56, 0x45, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x46, 0x4f, 0x55,
	0x4e, 0x44, 0x5f, 0x42, 0x55, 0x53, 0x49, 0x4e, 0x45, 0x53, 0x53, 0x10, 0xf4, 0xf1, 0x04, 0x12,
	0x32, 0x0a, 0x2c, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x41, 0x4c, 0x4c, 0x4f,
	0x57, 0x5f, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x54, 0x57, 0x49, 0x4c, 0x49, 0x4f, 0x5f,
	0x43, 0x41, 0x4c, 0x4c, 0x5f, 0x46, 0x4f, 0x52, 0x57, 0x41, 0x52, 0x44, 0x49, 0x4e, 0x47, 0x10,
	0xf5, 0xf1, 0x04, 0x12, 0x1d, 0x0a, 0x17, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x54, 0x57, 0x49, 0x4c,
	0x49, 0x4f, 0x5f, 0x51, 0x55, 0x45, 0x52, 0x59, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x10, 0xf6,
	0xf1, 0x04, 0x12, 0x23, 0x0a, 0x1d, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x49, 0x4e, 0x56, 0x41, 0x4c,
	0x49, 0x44, 0x5f, 0x45, 0x4d, 0x41, 0x49, 0x4c, 0x5f, 0x45, 0x56, 0x45, 0x4e, 0x54, 0x5f, 0x54,
	0x59, 0x50, 0x45, 0x10, 0xf7, 0xf1, 0x04, 0x12, 0x1f, 0x0a, 0x19, 0x43, 0x4f, 0x44, 0x45, 0x5f,
	0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x45, 0x4d, 0x41, 0x49, 0x4c, 0x5f, 0x53, 0x54,
	0x41, 0x54, 0x55, 0x53, 0x10, 0xf8, 0xf1, 0x04, 0x12, 0x1a, 0x0a, 0x14, 0x43, 0x4f, 0x44, 0x45,
	0x5f, 0x45, 0x4d, 0x41, 0x49, 0x4c, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x46, 0x4f, 0x55, 0x4e, 0x44,
	0x10, 0xf9, 0xf1, 0x04, 0x12, 0x26, 0x0a, 0x20, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x47, 0x52, 0x4f,
	0x4f, 0x4d, 0x49, 0x4e, 0x47, 0x5f, 0x52, 0x45, 0x50, 0x4f, 0x52, 0x54, 0x5f, 0x53, 0x45, 0x4e,
	0x44, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0xfa, 0xf1, 0x04, 0x12, 0x22, 0x0a, 0x1c,
	0x43, 0x4f, 0x44, 0x45, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x41, 0x4c, 0x4c, 0x4f, 0x57, 0x5f, 0x53,
	0x55, 0x42, 0x4d, 0x49, 0x54, 0x5f, 0x52, 0x45, 0x56, 0x49, 0x45, 0x57, 0x10, 0xfb, 0xf1, 0x04,
	0x12, 0x22, 0x0a, 0x1c, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x41, 0x54, 0x50, 0x5f, 0x44, 0x41, 0x54,
	0x41, 0x5f, 0x46, 0x4f, 0x52, 0x42, 0x49, 0x44, 0x44, 0x45, 0x4e, 0x5f, 0x45, 0x44, 0x49, 0x54,
	0x10, 0xfc, 0xf1, 0x04, 0x12, 0x1c, 0x0a, 0x16, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x41, 0x54, 0x50,
	0x5f, 0x50, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x10, 0xfd,
	0xf1, 0x04, 0x12, 0x1b, 0x0a, 0x15, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x41, 0x54, 0x50, 0x5f, 0x53,
	0x55, 0x42, 0x4d, 0x49, 0x54, 0x5f, 0x52, 0x45, 0x54, 0x52, 0x59, 0x10, 0xfe, 0xf1, 0x04, 0x12,
	0x1e, 0x0a, 0x18, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x41, 0x54, 0x50, 0x5f, 0x42, 0x52, 0x41, 0x4e,
	0x44, 0x5f, 0x4f, 0x54, 0x50, 0x5f, 0x52, 0x45, 0x54, 0x52, 0x59, 0x10, 0xff, 0xf1, 0x04, 0x12,
	0x1c, 0x0a, 0x16, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x4d, 0x45, 0x53, 0x53, 0x41, 0x47, 0x45, 0x5f,
	0x4e, 0x4f, 0x54, 0x5f, 0x46, 0x4f, 0x55, 0x4e, 0x44, 0x10, 0x80, 0xf2, 0x04, 0x12, 0x28, 0x0a,
	0x22, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x50, 0x48, 0x4f, 0x4e, 0x45, 0x5f, 0x4e, 0x55, 0x4d, 0x42,
	0x45, 0x52, 0x5f, 0x43, 0x41, 0x4e, 0x4e, 0x4f, 0x54, 0x5f, 0x46, 0x4f, 0x52, 0x4d, 0x41, 0x54,
	0x54, 0x45, 0x44, 0x10, 0x81, 0xf2, 0x04, 0x12, 0x22, 0x0a, 0x1c, 0x43, 0x4f, 0x44, 0x45, 0x5f,
	0x4c, 0x49, 0x4e, 0x4b, 0x5f, 0x42, 0x55, 0x53, 0x49, 0x4e, 0x45, 0x53, 0x53, 0x5f, 0x4e, 0x4f,
	0x54, 0x5f, 0x46, 0x4f, 0x55, 0x4e, 0x44, 0x10, 0x90, 0xbf, 0x05, 0x12, 0x24, 0x0a, 0x1e, 0x43,
	0x4f, 0x44, 0x45, 0x5f, 0x49, 0x4e, 0x56, 0x49, 0x54, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x43,
	0x4f, 0x44, 0x45, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x46, 0x4f, 0x55, 0x4e, 0x44, 0x10, 0x91, 0xbf,
	0x05, 0x12, 0x26, 0x0a, 0x20, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x4c, 0x49, 0x4e, 0x4b, 0x5f, 0x50,
	0x48, 0x4f, 0x4e, 0x45, 0x5f, 0x4e, 0x55, 0x4d, 0x42, 0x45, 0x52, 0x5f, 0x4e, 0x4f, 0x54, 0x5f,
	0x46, 0x4f, 0x55, 0x4e, 0x44, 0x10, 0x92, 0xbf, 0x05, 0x12, 0x24, 0x0a, 0x1e, 0x43, 0x4f, 0x44,
	0x45, 0x5f, 0x4e, 0x4f, 0x5f, 0x4c, 0x49, 0x4e, 0x4b, 0x45, 0x44, 0x5f, 0x43, 0x55, 0x53, 0x54,
	0x4f, 0x4d, 0x45, 0x52, 0x53, 0x5f, 0x46, 0x4f, 0x55, 0x4e, 0x44, 0x10, 0x93, 0xbf, 0x05, 0x12,
	0x24, 0x0a, 0x1e, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x4c, 0x49, 0x4e, 0x4b, 0x45, 0x44, 0x5f, 0x54,
	0x4f, 0x4f, 0x5f, 0x4d, 0x41, 0x4e, 0x59, 0x5f, 0x43, 0x55, 0x53, 0x54, 0x4f, 0x4d, 0x45, 0x52,
	0x53, 0x10, 0x94, 0xbf, 0x05, 0x12, 0x20, 0x0a, 0x1a, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x42, 0x52,
	0x41, 0x4e, 0x44, 0x45, 0x44, 0x5f, 0x41, 0x50, 0x50, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x46, 0x4f,
	0x55, 0x4e, 0x44, 0x10, 0x95, 0xbf, 0x05, 0x12, 0x23, 0x0a, 0x1d, 0x43, 0x4f, 0x44, 0x45, 0x5f,
	0x43, 0x41, 0x4e, 0x43, 0x45, 0x4c, 0x4c, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x4e, 0x4f, 0x54,
	0x5f, 0x41, 0x4c, 0x4c, 0x4f, 0x57, 0x45, 0x44, 0x10, 0x96, 0xbf, 0x05, 0x12, 0x21, 0x0a, 0x1b,
	0x43, 0x4f, 0x44, 0x45, 0x5f, 0x52, 0x45, 0x53, 0x43, 0x48, 0x45, 0x44, 0x55, 0x4c, 0x45, 0x5f,
	0x4e, 0x4f, 0x54, 0x5f, 0x41, 0x4c, 0x4c, 0x4f, 0x57, 0x45, 0x44, 0x10, 0x97, 0xbf, 0x05, 0x12,
	0x2c, 0x0a, 0x26, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x46, 0x49, 0x4e, 0x54, 0x45, 0x43, 0x48, 0x5f,
	0x42, 0x55, 0x53, 0x49, 0x4e, 0x45, 0x53, 0x53, 0x5f, 0x41, 0x50, 0x50, 0x5f, 0x55, 0x50, 0x44,
	0x41, 0x54, 0x45, 0x5f, 0x46, 0x4f, 0x52, 0x43, 0x45, 0x10, 0x98, 0xe6, 0x05, 0x12, 0x2f, 0x0a,
	0x29, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x46, 0x49, 0x4e, 0x54, 0x45, 0x43, 0x48, 0x5f, 0x42, 0x55,
	0x53, 0x49, 0x4e, 0x45, 0x53, 0x53, 0x5f, 0x41, 0x50, 0x50, 0x5f, 0x55, 0x50, 0x44, 0x41, 0x54,
	0x45, 0x5f, 0x43, 0x4c, 0x4f, 0x53, 0x41, 0x42, 0x4c, 0x45, 0x10, 0x99, 0xe6, 0x05, 0x12, 0x1c,
	0x0a, 0x16, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x4e,
	0x4f, 0x54, 0x5f, 0x45, 0x58, 0x49, 0x53, 0x54, 0x10, 0xa0, 0x8d, 0x06, 0x12, 0x1c, 0x0a, 0x16,
	0x43, 0x4f, 0x44, 0x45, 0x5f, 0x50, 0x41, 0x53, 0x53, 0x57, 0x4f, 0x52, 0x44, 0x5f, 0x54, 0x4f,
	0x4f, 0x5f, 0x57, 0x45, 0x41, 0x4b, 0x10, 0xa1, 0x8d, 0x06, 0x12, 0x19, 0x0a, 0x13, 0x43, 0x4f,
	0x44, 0x45, 0x5f, 0x45, 0x4d, 0x41, 0x49, 0x4c, 0x5f, 0x43, 0x4f, 0x4e, 0x46, 0x4c, 0x49, 0x43,
	0x54, 0x10, 0xa2, 0x8d, 0x06, 0x12, 0x20, 0x0a, 0x1a, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x50, 0x48,
	0x4f, 0x4e, 0x45, 0x5f, 0x4e, 0x55, 0x4d, 0x42, 0x45, 0x52, 0x5f, 0x43, 0x4f, 0x4e, 0x46, 0x4c,
	0x49, 0x43, 0x54, 0x10, 0xa3, 0x8d, 0x06, 0x12, 0x24, 0x0a, 0x1e, 0x43, 0x4f, 0x44, 0x45, 0x5f,
	0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x4f, 0x52, 0x5f, 0x50, 0x41, 0x53, 0x53, 0x57,
	0x4f, 0x52, 0x44, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x10, 0xa4, 0x8d, 0x06, 0x12, 0x1d, 0x0a,
	0x17, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x4f, 0x4c, 0x44, 0x5f, 0x50, 0x41, 0x53, 0x53, 0x57, 0x4f,
	0x52, 0x44, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x10, 0xa5, 0x8d, 0x06, 0x12, 0x24, 0x0a, 0x1e,
	0x43, 0x4f, 0x44, 0x45, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x41, 0x4c, 0x52,
	0x45, 0x41, 0x44, 0x59, 0x5f, 0x52, 0x45, 0x43, 0x4f, 0x56, 0x45, 0x52, 0x45, 0x44, 0x10, 0xa6,
	0x8d, 0x06, 0x12, 0x26, 0x0a, 0x20, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x56, 0x45, 0x52, 0x49, 0x46,
	0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x4e, 0x4f, 0x54,
	0x5f, 0x4d, 0x41, 0x54, 0x43, 0x48, 0x10, 0xa7, 0x8d, 0x06, 0x12, 0x19, 0x0a, 0x13, 0x43, 0x4f,
	0x44, 0x45, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x46, 0x52, 0x4f, 0x5a, 0x45,
	0x4e, 0x10, 0xa8, 0x8d, 0x06, 0x12, 0x1c, 0x0a, 0x16, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x53, 0x45,
	0x53, 0x53, 0x49, 0x4f, 0x4e, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x45, 0x58, 0x49, 0x53, 0x54, 0x10,
	0x88, 0x95, 0x06, 0x12, 0x1a, 0x0a, 0x14, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x53, 0x45, 0x53, 0x53,
	0x49, 0x4f, 0x4e, 0x5f, 0x45, 0x58, 0x50, 0x49, 0x52, 0x45, 0x44, 0x10, 0x89, 0x95, 0x06, 0x12,
	0x20, 0x0a, 0x1a, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x53, 0x45, 0x53, 0x53, 0x49, 0x4f, 0x4e, 0x5f,
	0x54, 0x4f, 0x4b, 0x45, 0x4e, 0x5f, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x10, 0x8a, 0x95,
	0x06, 0x12, 0x2e, 0x0a, 0x28, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x50, 0x4c, 0x41, 0x54, 0x46, 0x4f,
	0x52, 0x4d, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x41, 0x4c, 0x52, 0x45, 0x41,
	0x44, 0x59, 0x5f, 0x41, 0x53, 0x53, 0x4f, 0x43, 0x49, 0x41, 0x54, 0x45, 0x44, 0x10, 0xf0, 0x9c,
	0x06, 0x12, 0x21, 0x0a, 0x1b, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x43, 0x4f, 0x4e, 0x56, 0x45, 0x52,
	0x53, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x45, 0x58, 0x49, 0x53, 0x54,
	0x10, 0xc0, 0xa9, 0x07, 0x12, 0x2a, 0x0a, 0x24, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x43, 0x4f, 0x4e,
	0x56, 0x45, 0x52, 0x53, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x42, 0x55, 0x53, 0x49, 0x4e, 0x45,
	0x53, 0x53, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x4d, 0x41, 0x54, 0x43, 0x48, 0x10, 0xc1, 0xa9, 0x07,
	0x12, 0x1e, 0x0a, 0x18, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x43, 0x4f, 0x4e, 0x56, 0x45, 0x52, 0x53,
	0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x43, 0x4c, 0x4f, 0x53, 0x45, 0x44, 0x10, 0xc3, 0xa9, 0x07,
	0x12, 0x27, 0x0a, 0x21, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x43, 0x4f, 0x4e, 0x56, 0x45, 0x52, 0x53,
	0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x5f, 0x54, 0x4f, 0x5f,
	0x52, 0x45, 0x50, 0x4c, 0x59, 0x10, 0xc4, 0xa9, 0x07, 0x12, 0x2a, 0x0a, 0x24, 0x43, 0x4f, 0x44,
	0x45, 0x5f, 0x43, 0x4f, 0x4e, 0x56, 0x45, 0x52, 0x53, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x42,
	0x41, 0x4c, 0x41, 0x4e, 0x43, 0x45, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x45, 0x4e, 0x4f, 0x55, 0x47,
	0x48, 0x10, 0xc5, 0xa9, 0x07, 0x12, 0x2a, 0x0a, 0x24, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x43, 0x4f,
	0x4e, 0x56, 0x45, 0x52, 0x53, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x51, 0x55, 0x45, 0x53, 0x54,
	0x49, 0x4f, 0x4e, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x45, 0x58, 0x49, 0x53, 0x54, 0x10, 0xc6, 0xa9,
	0x07, 0x12, 0x2a, 0x0a, 0x24, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x43, 0x4f, 0x4e, 0x56, 0x45, 0x52,
	0x53, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x51, 0x55, 0x45, 0x53, 0x54, 0x49, 0x4f, 0x4e, 0x5f,
	0x4e, 0x4f, 0x54, 0x5f, 0x4d, 0x41, 0x54, 0x43, 0x48, 0x10, 0xc7, 0xa9, 0x07, 0x12, 0x2a, 0x0a,
	0x24, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x43, 0x4f, 0x4e, 0x56, 0x45, 0x52, 0x53, 0x41, 0x54, 0x49,
	0x4f, 0x4e, 0x5f, 0x51, 0x55, 0x45, 0x53, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x45, 0x58, 0x43, 0x45,
	0x53, 0x53, 0x49, 0x56, 0x45, 0x10, 0xc8, 0xa9, 0x07, 0x12, 0x23, 0x0a, 0x1d, 0x43, 0x4f, 0x44,
	0x45, 0x5f, 0x47, 0x4f, 0x4f, 0x47, 0x4c, 0x45, 0x5f, 0x52, 0x45, 0x43, 0x41, 0x50, 0x54, 0x43,
	0x48, 0x41, 0x5f, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x10, 0xd0, 0xf7, 0x07, 0x12, 0x31,
	0x0a, 0x2b, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x47, 0x4f, 0x4f, 0x47, 0x4c, 0x45, 0x5f, 0x52, 0x45,
	0x43, 0x41, 0x50, 0x54, 0x43, 0x48, 0x41, 0x5f, 0x54, 0x48, 0x52, 0x45, 0x53, 0x48, 0x4f, 0x4c,
	0x44, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x52, 0x45, 0x41, 0x43, 0x48, 0x45, 0x44, 0x10, 0xd1, 0xf7,
	0x07, 0x12, 0x2c, 0x0a, 0x26, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x47, 0x4f, 0x4f, 0x47, 0x4c, 0x45,
	0x5f, 0x52, 0x45, 0x43, 0x41, 0x50, 0x54, 0x43, 0x48, 0x41, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x4f,
	0x4e, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x46, 0x4f, 0x55, 0x4e, 0x44, 0x10, 0xd2, 0xf7, 0x07, 0x12,
	0x2f, 0x0a, 0x29, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x56, 0x45, 0x52, 0x49, 0x46, 0x49, 0x43, 0x41,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x53, 0x45, 0x4e, 0x54, 0x5f, 0x43,
	0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x4c, 0x49, 0x4d, 0x49, 0x54, 0x45, 0x44, 0x10, 0xd3, 0xf7, 0x07,
	0x12, 0x36, 0x0a, 0x30, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x56, 0x45, 0x52, 0x49, 0x46, 0x49, 0x43,
	0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x53, 0x45, 0x4e, 0x44, 0x5f,
	0x49, 0x4e, 0x54, 0x45, 0x52, 0x56, 0x41, 0x4c, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x52, 0x45, 0x41,
	0x43, 0x48, 0x45, 0x44, 0x10, 0xd4, 0xf7, 0x07, 0x12, 0x24, 0x0a, 0x1e, 0x43, 0x4f, 0x44, 0x45,
	0x5f, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x56, 0x45, 0x52, 0x49, 0x46, 0x49, 0x43,
	0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x43, 0x4f, 0x44, 0x45, 0x10, 0xd5, 0xf7, 0x07, 0x12, 0x24,
	0x0a, 0x1e, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x56, 0x45, 0x52, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54,
	0x49, 0x4f, 0x4e, 0x5f, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x45, 0x58, 0x50, 0x49, 0x52, 0x45, 0x44,
	0x10, 0xd6, 0xf7, 0x07, 0x12, 0x20, 0x0a, 0x1a, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x46, 0x49, 0x4c,
	0x45, 0x5f, 0x52, 0x45, 0x43, 0x4f, 0x52, 0x44, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x45, 0x58, 0x49,
	0x53, 0x54, 0x10, 0xe0, 0xc5, 0x08, 0x12, 0x20, 0x0a, 0x1a, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x46,
	0x49, 0x4c, 0x45, 0x5f, 0x4f, 0x42, 0x4a, 0x45, 0x43, 0x54, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x45,
	0x58, 0x49, 0x53, 0x54, 0x10, 0xe1, 0xc5, 0x08, 0x12, 0x1d, 0x0a, 0x17, 0x43, 0x4f, 0x44, 0x45,
	0x5f, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x45, 0x58, 0x54, 0x5f, 0x46, 0x4f, 0x52, 0x42, 0x49, 0x44,
	0x44, 0x45, 0x4e, 0x10, 0xe2, 0xc5, 0x08, 0x12, 0x23, 0x0a, 0x1d, 0x43, 0x4f, 0x44, 0x45, 0x5f,
	0x52, 0x4f, 0x4c, 0x45, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x41, 0x4c, 0x52, 0x45, 0x41, 0x44,
	0x59, 0x5f, 0x45, 0x58, 0x49, 0x53, 0x54, 0x53, 0x10, 0xf0, 0x93, 0x09, 0x12, 0x19, 0x0a, 0x13,
	0x43, 0x4f, 0x44, 0x45, 0x5f, 0x52, 0x4f, 0x4c, 0x45, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x46, 0x4f,
	0x55, 0x4e, 0x44, 0x10, 0xf1, 0x93, 0x09, 0x12, 0x1e, 0x0a, 0x18, 0x43, 0x4f, 0x44, 0x45, 0x5f,
	0x4c, 0x4f, 0x44, 0x47, 0x49, 0x4e, 0x47, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x49, 0x4e, 0x5f,
	0x55, 0x53, 0x45, 0x10, 0x80, 0xe2, 0x09, 0x12, 0x21, 0x0a, 0x1b, 0x43, 0x4f, 0x44, 0x45, 0x5f,
	0x4c, 0x4f, 0x44, 0x47, 0x49, 0x4e, 0x47, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4e, 0x4f, 0x54,
	0x5f, 0x46, 0x4f, 0x55, 0x4e, 0x44, 0x10, 0x81, 0xe2, 0x09, 0x12, 0x1e, 0x0a, 0x18, 0x43, 0x4f,
	0x44, 0x45, 0x5f, 0x4c, 0x4f, 0x44, 0x47, 0x49, 0x4e, 0x47, 0x5f, 0x55, 0x4e, 0x49, 0x54, 0x5f,
	0x49, 0x4e, 0x5f, 0x55, 0x53, 0x45, 0x10, 0x82, 0xe2, 0x09, 0x12, 0x29, 0x0a, 0x23, 0x43, 0x4f,
	0x44, 0x45, 0x5f, 0x46, 0x49, 0x4e, 0x41, 0x4e, 0x43, 0x45, 0x5f, 0x4e, 0x4f, 0x5f, 0x41, 0x43,
	0x43, 0x45, 0x53, 0x53, 0x5f, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x5f, 0x41, 0x50,
	0x49, 0x10, 0x90, 0xb0, 0x0a, 0x12, 0x22, 0x0a, 0x1c, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x43, 0x41,
	0x50, 0x49, 0x54, 0x41, 0x4c, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x5f, 0x4e, 0x4f, 0x54, 0x5f,
	0x46, 0x4f, 0x55, 0x4e, 0x44, 0x10, 0xf8, 0xb7, 0x0a, 0x12, 0x22, 0x0a, 0x1c, 0x43, 0x4f, 0x44,
	0x45, 0x5f, 0x43, 0x41, 0x50, 0x49, 0x54, 0x41, 0x4c, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x5f,
	0x4e, 0x4f, 0x5f, 0x41, 0x43, 0x43, 0x45, 0x53, 0x53, 0x10, 0xf9, 0xb7, 0x0a, 0x12, 0x27, 0x0a,
	0x21, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x43, 0x41, 0x50, 0x49, 0x54, 0x41, 0x4c, 0x5f, 0x49, 0x4c,
	0x4c, 0x45, 0x47, 0x41, 0x4c, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x5f, 0x53, 0x54, 0x41, 0x54,
	0x55, 0x53, 0x10, 0xfa, 0xb7, 0x0a, 0x12, 0x2c, 0x0a, 0x26, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x43,
	0x41, 0x50, 0x49, 0x54, 0x41, 0x4c, 0x5f, 0x43, 0x48, 0x41, 0x4e, 0x4e, 0x45, 0x4c, 0x5f, 0x41,
	0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x46, 0x4f, 0x55, 0x4e, 0x44,
	0x10, 0xfb, 0xb7, 0x0a, 0x12, 0x1b, 0x0a, 0x15, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x43, 0x41, 0x50,
	0x49, 0x54, 0x41, 0x4c, 0x5f, 0x44, 0x42, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x10, 0xfc, 0xb7,
	0x0a, 0x12, 0x22, 0x0a, 0x1c, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x43, 0x41, 0x50, 0x49, 0x54, 0x41,
	0x4c, 0x5f, 0x47, 0x45, 0x54, 0x5f, 0x4c, 0x4f, 0x43, 0x4b, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45,
	0x44, 0x10, 0xfd, 0xb7, 0x0a, 0x12, 0x22, 0x0a, 0x1c, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x43, 0x41,
	0x50, 0x49, 0x54, 0x41, 0x4c, 0x5f, 0x4c, 0x4f, 0x43, 0x4b, 0x45, 0x44, 0x5f, 0x42, 0x59, 0x5f,
	0x4f, 0x54, 0x48, 0x45, 0x52, 0x10, 0xfe, 0xb7, 0x0a, 0x12, 0x2c, 0x0a, 0x26, 0x43, 0x4f, 0x44,
	0x45, 0x5f, 0x43, 0x41, 0x50, 0x49, 0x54, 0x41, 0x4c, 0x5f, 0x46, 0x55, 0x4c, 0x4c, 0x59, 0x5f,
	0x50, 0x41, 0x49, 0x44, 0x5f, 0x57, 0x49, 0x54, 0x48, 0x5f, 0x52, 0x45, 0x4d, 0x41, 0x49, 0x4e,
	0x49, 0x4e, 0x47, 0x10, 0xff, 0xb7, 0x0a, 0x12, 0x2b, 0x0a, 0x25, 0x43, 0x4f, 0x44, 0x45, 0x5f,
	0x43, 0x41, 0x50, 0x49, 0x54, 0x41, 0x4c, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x5f,
	0x54, 0x52, 0x41, 0x4e, 0x53, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x10, 0x80, 0xb8, 0x0a, 0x12, 0x30, 0x0a, 0x2a, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x43, 0x41, 0x50,
	0x49, 0x54, 0x41, 0x4c, 0x5f, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x56, 0x41, 0x4c, 0x5f, 0x50, 0x41,
	0x49, 0x44, 0x5f, 0x41, 0x4d, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x4d, 0x49, 0x53, 0x4d, 0x41, 0x54,
	0x43, 0x48, 0x10, 0x81, 0xb8, 0x0a, 0x12, 0x32, 0x0a, 0x2c, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x43,
	0x41, 0x50, 0x49, 0x54, 0x41, 0x4c, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x5f, 0x52, 0x45, 0x4d,
	0x41, 0x49, 0x4e, 0x49, 0x4e, 0x47, 0x5f, 0x41, 0x4d, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x4d, 0x49,
	0x53, 0x4d, 0x41, 0x54, 0x43, 0x48, 0x10, 0x82, 0xb8, 0x0a, 0x12, 0x28, 0x0a, 0x22, 0x43, 0x4f,
	0x44, 0x45, 0x5f, 0x43, 0x41, 0x50, 0x49, 0x54, 0x41, 0x4c, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53,
	0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x46, 0x4f, 0x55, 0x4e, 0x44,
	0x10, 0x83, 0xb8, 0x0a, 0x12, 0x29, 0x0a, 0x23, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x43, 0x41, 0x50,
	0x49, 0x54, 0x41, 0x4c, 0x5f, 0x43, 0x48, 0x41, 0x4e, 0x4e, 0x45, 0x4c, 0x5f, 0x53, 0x54, 0x41,
	0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x84, 0xb8, 0x0a, 0x12,
	0x23, 0x0a, 0x1d, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x43, 0x41, 0x50, 0x49, 0x54, 0x41, 0x4c, 0x5f,
	0x53, 0x54, 0x52, 0x49, 0x50, 0x45, 0x5f, 0x41, 0x50, 0x49, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52,
	0x10, 0x85, 0xb8, 0x0a, 0x12, 0x1e, 0x0a, 0x18, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x43, 0x41, 0x50,
	0x49, 0x54, 0x41, 0x4c, 0x5f, 0x43, 0x41, 0x43, 0x48, 0x45, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52,
	0x10, 0x86, 0xb8, 0x0a, 0x12, 0x2c, 0x0a, 0x26, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x43, 0x41, 0x50,
	0x49, 0x54, 0x41, 0x4c, 0x5f, 0x49, 0x4c, 0x4c, 0x45, 0x47, 0x41, 0x4c, 0x5f, 0x53, 0x54, 0x41,
	0x54, 0x55, 0x53, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x49, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x87,
	0xb8, 0x0a, 0x12, 0x20, 0x0a, 0x1a, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x43, 0x41, 0x50, 0x49, 0x54,
	0x41, 0x4c, 0x5f, 0x4e, 0x45, 0x54, 0x57, 0x4f, 0x52, 0x4b, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52,
	0x10, 0x88, 0xb8, 0x0a, 0x12, 0x22, 0x0a, 0x1c, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x43, 0x41, 0x50,
	0x49, 0x54, 0x41, 0x4c, 0x5f, 0x55, 0x4e, 0x4d, 0x41, 0x52, 0x53, 0x48, 0x41, 0x4c, 0x5f, 0x45,
	0x52, 0x52, 0x4f, 0x52, 0x10, 0x89, 0xb8, 0x0a, 0x12, 0x26, 0x0a, 0x20, 0x43, 0x4f, 0x44, 0x45,
	0x5f, 0x43, 0x41, 0x50, 0x49, 0x54, 0x41, 0x4c, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e,
	0x5f, 0x45, 0x4e, 0x54, 0x49, 0x54, 0x59, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x10, 0x8a, 0xb8, 0x0a,
	0x12, 0x23, 0x0a, 0x1d, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x43, 0x41, 0x50, 0x49, 0x54, 0x41, 0x4c,
	0x5f, 0x4b, 0x41, 0x4e, 0x4d, 0x4f, 0x4e, 0x5f, 0x41, 0x50, 0x49, 0x5f, 0x45, 0x52, 0x52, 0x4f,
	0x52, 0x10, 0x8b, 0xb8, 0x0a, 0x12, 0x22, 0x0a, 0x1c, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x43, 0x41,
	0x50, 0x49, 0x54, 0x41, 0x4c, 0x5f, 0x43, 0x48, 0x41, 0x4e, 0x4e, 0x45, 0x4c, 0x5f, 0x55, 0x4e,
	0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x8c, 0xb8, 0x0a, 0x12, 0x3b, 0x0a, 0x35, 0x43, 0x4f, 0x44,
	0x45, 0x5f, 0x43, 0x41, 0x50, 0x49, 0x54, 0x41, 0x4c, 0x5f, 0x50, 0x52, 0x4f, 0x43, 0x45, 0x53,
	0x53, 0x49, 0x4e, 0x47, 0x5f, 0x41, 0x4d, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x47, 0x52, 0x45, 0x41,
	0x54, 0x45, 0x52, 0x5f, 0x54, 0x48, 0x41, 0x4e, 0x5f, 0x52, 0x45, 0x4d, 0x41, 0x49, 0x4e, 0x49,
	0x4e, 0x47, 0x10, 0x8d, 0xb8, 0x0a, 0x12, 0x2c, 0x0a, 0x26, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x43,
	0x41, 0x50, 0x49, 0x54, 0x41, 0x4c, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x5f, 0x54, 0x4f,
	0x5f, 0x44, 0x45, 0x4c, 0x49, 0x56, 0x45, 0x52, 0x5f, 0x4d, 0x45, 0x53, 0x53, 0x41, 0x47, 0x45,
	0x10, 0x8e, 0xb8, 0x0a, 0x12, 0x34, 0x0a, 0x2e, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x46, 0x49, 0x4e,
	0x41, 0x4e, 0x43, 0x45, 0x5f, 0x47, 0x57, 0x5f, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f,
	0x53, 0x54, 0x52, 0x49, 0x50, 0x45, 0x5f, 0x57, 0x45, 0x42, 0x48, 0x4f, 0x4f, 0x4b, 0x5f, 0x50,
	0x41, 0x59, 0x4c, 0x4f, 0x41, 0x44, 0x10, 0xe0, 0xbf, 0x0a, 0x12, 0x26, 0x0a, 0x20, 0x43, 0x4f,
	0x44, 0x45, 0x5f, 0x46, 0x49, 0x4e, 0x41, 0x4e, 0x43, 0x45, 0x5f, 0x47, 0x57, 0x5f, 0x44, 0x4f,
	0x57, 0x4e, 0x53, 0x54, 0x52, 0x45, 0x41, 0x4d, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x10, 0xe1,
	0xbf, 0x0a, 0x12, 0x34, 0x0a, 0x2e, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x46, 0x49, 0x4e, 0x41, 0x4e,
	0x43, 0x45, 0x5f, 0x47, 0x57, 0x5f, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x4b, 0x41,
	0x4e, 0x4d, 0x4f, 0x4e, 0x5f, 0x57, 0x45, 0x42, 0x48, 0x4f, 0x4f, 0x4b, 0x5f, 0x50, 0x41, 0x59,
	0x4c, 0x4f, 0x41, 0x44, 0x10, 0xe2, 0xbf, 0x0a, 0x12, 0x28, 0x0a, 0x22, 0x43, 0x4f, 0x44, 0x45,
	0x5f, 0x46, 0x49, 0x4e, 0x41, 0x4e, 0x43, 0x45, 0x5f, 0x47, 0x57, 0x5f, 0x55, 0x4e, 0x4b, 0x4e,
	0x4f, 0x57, 0x4e, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x10, 0xe3,
	0xbf, 0x0a, 0x12, 0x30, 0x0a, 0x2a, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x46, 0x49, 0x4e, 0x41, 0x4e,
	0x43, 0x45, 0x5f, 0x47, 0x57, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x5f, 0x41, 0x43,
	0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x45, 0x56, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x10, 0xe4, 0xbf, 0x0a, 0x12, 0x2e, 0x0a, 0x28, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x46, 0x49, 0x4e,
	0x41, 0x4e, 0x43, 0x45, 0x5f, 0x47, 0x57, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x5f,
	0x4f, 0x46, 0x46, 0x45, 0x52, 0x5f, 0x45, 0x56, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x10, 0xe5, 0xbf, 0x0a, 0x12, 0x34, 0x0a, 0x2e, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x46, 0x49, 0x4e,
	0x41, 0x4e, 0x43, 0x45, 0x5f, 0x47, 0x57, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x5f,
	0x54, 0x52, 0x41, 0x4e, 0x53, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x45, 0x56, 0x45, 0x4e,
	0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x10, 0xe6, 0xbf, 0x0a, 0x12, 0x33, 0x0a, 0x2d, 0x43, 0x4f,
	0x44, 0x45, 0x5f, 0x46, 0x49, 0x4e, 0x41, 0x4e, 0x43, 0x45, 0x5f, 0x47, 0x57, 0x5f, 0x49, 0x4e,
	0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x4b, 0x41, 0x4e, 0x4d, 0x4f, 0x4e, 0x5f, 0x57, 0x45, 0x42,
	0x48, 0x4f, 0x4f, 0x4b, 0x5f, 0x53, 0x45, 0x43, 0x52, 0x45, 0x54, 0x10, 0xe7, 0xbf, 0x0a, 0x12,
	0x33, 0x0a, 0x2d, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x46, 0x49, 0x4e, 0x41, 0x4e, 0x43, 0x45, 0x5f,
	0x47, 0x57, 0x5f, 0x53, 0x49, 0x47, 0x4e, 0x41, 0x54, 0x55, 0x52, 0x45, 0x5f, 0x56, 0x45, 0x52,
	0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44,
	0x10, 0xe8, 0xbf, 0x0a, 0x12, 0x2d, 0x0a, 0x27, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x46, 0x49, 0x4e,
	0x41, 0x4e, 0x43, 0x45, 0x5f, 0x47, 0x57, 0x5f, 0x50, 0x41, 0x59, 0x4c, 0x4f, 0x41, 0x44, 0x5f,
	0x55, 0x4e, 0x4d, 0x41, 0x52, 0x53, 0x48, 0x41, 0x4c, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x10,
	0xe9, 0xbf, 0x0a, 0x12, 0x12, 0x0a, 0x0c, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x52, 0x45, 0x53, 0x54,
	0x5f, 0x49, 0x4f, 0x10, 0xa0, 0xfe, 0x0a, 0x12, 0x20, 0x0a, 0x1a, 0x43, 0x4f, 0x44, 0x45, 0x5f,
	0x52, 0x45, 0x53, 0x54, 0x5f, 0x44, 0x4f, 0x57, 0x4e, 0x53, 0x54, 0x52, 0x45, 0x41, 0x4d, 0x5f,
	0x45, 0x52, 0x52, 0x4f, 0x52, 0x10, 0xa1, 0xfe, 0x0a, 0x12, 0x2b, 0x0a, 0x25, 0x43, 0x4f, 0x44,
	0x45, 0x5f, 0x46, 0x49, 0x4e, 0x41, 0x4e, 0x43, 0x45, 0x5f, 0x54, 0x4f, 0x4f, 0x4c, 0x53, 0x5f,
	0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x54, 0x49, 0x4d, 0x45, 0x5f, 0x52, 0x41, 0x4e,
	0x47, 0x45, 0x10, 0xb0, 0xcc, 0x0b, 0x12, 0x31, 0x0a, 0x2b, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x46,
	0x49, 0x4e, 0x41, 0x4e, 0x43, 0x45, 0x5f, 0x54, 0x4f, 0x4f, 0x4c, 0x53, 0x5f, 0x50, 0x41, 0x59,
	0x4d, 0x45, 0x4e, 0x54, 0x53, 0x5f, 0x54, 0x4f, 0x54, 0x41, 0x4c, 0x5f, 0x55, 0x4e, 0x4d, 0x41,
	0x54, 0x43, 0x48, 0x45, 0x44, 0x10, 0xb1, 0xcc, 0x0b, 0x12, 0x34, 0x0a, 0x2e, 0x43, 0x4f, 0x44,
	0x45, 0x5f, 0x46, 0x49, 0x4e, 0x41, 0x4e, 0x43, 0x45, 0x5f, 0x54, 0x4f, 0x4f, 0x4c, 0x53, 0x5f,
	0x41, 0x44, 0x4a, 0x55, 0x53, 0x54, 0x4d, 0x45, 0x4e, 0x54, 0x53, 0x5f, 0x54, 0x4f, 0x54, 0x41,
	0x4c, 0x5f, 0x55, 0x4e, 0x4d, 0x41, 0x54, 0x43, 0x48, 0x45, 0x44, 0x10, 0xb2, 0xcc, 0x0b, 0x12,
	0x2a, 0x0a, 0x24, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x53, 0x50, 0x4c, 0x49, 0x54, 0x5f, 0x50, 0x41,
	0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x41, 0x43, 0x43, 0x45, 0x50, 0x54, 0x41, 0x4e, 0x43, 0x45,
	0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0xc0, 0x9a, 0x0c, 0x12, 0x33, 0x0a, 0x2d, 0x43,
	0x4f, 0x44, 0x45, 0x5f, 0x53, 0x50, 0x4c, 0x49, 0x54, 0x5f, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e,
	0x54, 0x5f, 0x52, 0x45, 0x56, 0x45, 0x52, 0x53, 0x41, 0x4c, 0x5f, 0x41, 0x43, 0x43, 0x45, 0x50,
	0x54, 0x41, 0x4e, 0x43, 0x45, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0xc1, 0x9a, 0x0c,
	0x12, 0x23, 0x0a, 0x1d, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54,
	0x49, 0x4e, 0x47, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x5f, 0x45, 0x52, 0x52, 0x4f,
	0x52, 0x10, 0xd0, 0xe8, 0x0c, 0x12, 0x27, 0x0a, 0x21, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x41, 0x43,
	0x43, 0x4f, 0x55, 0x4e, 0x54, 0x49, 0x4e, 0x47, 0x5f, 0x44, 0x41, 0x54, 0x41, 0x5f, 0x41, 0x43,
	0x43, 0x45, 0x53, 0x53, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x10, 0xd1, 0xe8, 0x0c, 0x12, 0x2b,
	0x0a, 0x25, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x49, 0x4e,
	0x47, 0x5f, 0x54, 0x48, 0x49, 0x52, 0x44, 0x5f, 0x50, 0x41, 0x52, 0x54, 0x59, 0x5f, 0x41, 0x50,
	0x49, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x10, 0xd2, 0xe8, 0x0c, 0x12, 0x24, 0x0a, 0x1e, 0x43,
	0x4f, 0x44, 0x45, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x49, 0x4e, 0x47, 0x5f, 0x52,
	0x50, 0x43, 0x5f, 0x43, 0x41, 0x4c, 0x4c, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x10, 0xd3, 0xe8,
	0x0c, 0x12, 0x31, 0x0a, 0x2b, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e,
	0x54, 0x49, 0x4e, 0x47, 0x5f, 0x44, 0x41, 0x54, 0x41, 0x5f, 0x53, 0x59, 0x4e, 0x43, 0x45, 0x52,
	0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x46, 0x4f, 0x55, 0x4e, 0x44, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52,
	0x10, 0xd4, 0xe8, 0x0c, 0x12, 0x28, 0x0a, 0x22, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x41, 0x43, 0x43,
	0x4f, 0x55, 0x4e, 0x54, 0x49, 0x4e, 0x47, 0x5f, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c,
	0x5f, 0x42, 0x49, 0x5a, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x10, 0xd5, 0xe8, 0x0c, 0x12, 0x28,
	0x0a, 0x22, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x49, 0x4e,
	0x47, 0x5f, 0x55, 0x4e, 0x53, 0x55, 0x42, 0x53, 0x43, 0x52, 0x49, 0x42, 0x45, 0x44, 0x5f, 0x45,
	0x52, 0x52, 0x4f, 0x52, 0x10, 0xd6, 0xe8, 0x0c, 0x12, 0x20, 0x0a, 0x1a, 0x43, 0x4f, 0x44, 0x45,
	0x5f, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e,
	0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x10, 0xe0, 0xb6, 0x0d, 0x12, 0x24, 0x0a, 0x1e, 0x43, 0x4f,
	0x44, 0x45, 0x5f, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x44, 0x41, 0x54, 0x41, 0x5f,
	0x41, 0x43, 0x43, 0x45, 0x53, 0x53, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x10, 0xe1, 0xb6, 0x0d,
	0x12, 0x21, 0x0a, 0x1b, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54,
	0x5f, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x10,
	0xe2, 0xb6, 0x0d, 0x12, 0x22, 0x0a, 0x1c, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x50, 0x41, 0x59, 0x4d,
	0x45, 0x4e, 0x54, 0x5f, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x43, 0x48, 0x41, 0x4e,
	0x4e, 0x45, 0x4c, 0x10, 0xe3, 0xb6, 0x0d, 0x12, 0x2e, 0x0a, 0x28, 0x43, 0x4f, 0x44, 0x45, 0x5f,
	0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f,
	0x56, 0x45, 0x52, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x41,
	0x54, 0x55, 0x53, 0x10, 0xe4, 0xb6, 0x0d, 0x12, 0x1e, 0x0a, 0x18, 0x43, 0x4f, 0x44, 0x45, 0x5f,
	0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x4e, 0x4f, 0x5f, 0x4c, 0x4f, 0x43, 0x41, 0x54,
	0x49, 0x4f, 0x4e, 0x10, 0xe5, 0xb6, 0x0d, 0x12, 0x25, 0x0a, 0x1f, 0x43, 0x4f, 0x44, 0x45, 0x5f,
	0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x47, 0x45, 0x54, 0x5f, 0x43, 0x4f, 0x4d, 0x50,
	0x41, 0x4e, 0x59, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0xe6, 0xb6, 0x0d, 0x12, 0x2d,
	0x0a, 0x27, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x47,
	0x45, 0x54, 0x5f, 0x43, 0x48, 0x41, 0x4e, 0x4e, 0x45, 0x4c, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55,
	0x4e, 0x54, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0xe7, 0xb6, 0x0d, 0x12, 0x32, 0x0a,
	0x2c, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x41, 0x44,
	0x59, 0x45, 0x4e, 0x5f, 0x47, 0x45, 0x54, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f,
	0x48, 0x4f, 0x4c, 0x44, 0x45, 0x52, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0xe8, 0xb6,
	0x0d, 0x12, 0x29, 0x0a, 0x23, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e,
	0x54, 0x5f, 0x4c, 0x49, 0x53, 0x54, 0x5f, 0x42, 0x55, 0x53, 0x49, 0x4e, 0x45, 0x53, 0x53, 0x45,
	0x53, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0xe9, 0xb6, 0x0d, 0x12, 0x33, 0x0a, 0x2d,
	0x43, 0x4f, 0x44, 0x45, 0x5f, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x41, 0x44, 0x59,
	0x45, 0x4e, 0x5f, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x5f, 0x4c, 0x45, 0x47, 0x41, 0x4c, 0x5f,
	0x45, 0x4e, 0x54, 0x49, 0x54, 0x59, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0xea, 0xb6,
	0x0d, 0x12, 0x35, 0x0a, 0x2f, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e,
	0x54, 0x5f, 0x41, 0x44, 0x59, 0x45, 0x4e, 0x5f, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x5f, 0x41,
	0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x48, 0x4f, 0x4c, 0x44, 0x45, 0x52, 0x5f, 0x46, 0x41,
	0x49, 0x4c, 0x45, 0x44, 0x10, 0xeb, 0xb6, 0x0d, 0x12, 0x2c, 0x0a, 0x26, 0x43, 0x4f, 0x44, 0x45,
	0x5f, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x41, 0x44, 0x59, 0x45, 0x4e, 0x5f, 0x43,
	0x52, 0x45, 0x41, 0x54, 0x45, 0x5f, 0x53, 0x54, 0x4f, 0x52, 0x45, 0x5f, 0x46, 0x41, 0x49, 0x4c,
	0x45, 0x44, 0x10, 0xec, 0xb6, 0x0d, 0x12, 0x36, 0x0a, 0x30, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x50,
	0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x41, 0x44, 0x59, 0x45, 0x4e, 0x5f, 0x43, 0x52, 0x45,
	0x41, 0x54, 0x45, 0x5f, 0x42, 0x41, 0x4c, 0x41, 0x4e, 0x43, 0x45, 0x5f, 0x41, 0x43, 0x43, 0x4f,
	0x55, 0x4e, 0x54, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0xed, 0xb6, 0x0d, 0x12, 0x30,
	0x0a, 0x2a, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x43,
	0x52, 0x45, 0x41, 0x54, 0x45, 0x5f, 0x43, 0x48, 0x41, 0x4e, 0x4e, 0x45, 0x4c, 0x5f, 0x41, 0x43,
	0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0xee, 0xb6, 0x0d,
	0x12, 0x30, 0x0a, 0x2a, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54,
	0x5f, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x43, 0x48, 0x41, 0x4e, 0x4e, 0x45, 0x4c, 0x5f,
	0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0xef,
	0xb6, 0x0d, 0x12, 0x2d, 0x0a, 0x27, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x50, 0x41, 0x59, 0x4d, 0x45,
	0x4e, 0x54, 0x5f, 0x4f, 0x4e, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x5f, 0x49, 0x4e, 0x56, 0x41, 0x4c,
	0x49, 0x44, 0x5f, 0x50, 0x52, 0x45, 0x5f, 0x43, 0x4f, 0x4e, 0x46, 0x49, 0x47, 0x10, 0xf0, 0xb6,
	0x0d, 0x12, 0x34, 0x0a, 0x2e, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e,
	0x54, 0x5f, 0x41, 0x44, 0x59, 0x45, 0x4e, 0x5f, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x5f, 0x42,
	0x55, 0x53, 0x49, 0x4e, 0x45, 0x53, 0x53, 0x5f, 0x4c, 0x49, 0x4e, 0x45, 0x5f, 0x46, 0x41, 0x49,
	0x4c, 0x45, 0x44, 0x10, 0xf1, 0xb6, 0x0d, 0x12, 0x33, 0x0a, 0x2d, 0x43, 0x4f, 0x44, 0x45, 0x5f,
	0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x41, 0x44, 0x59, 0x45, 0x4e, 0x5f, 0x43, 0x52,
	0x45, 0x41, 0x54, 0x45, 0x5f, 0x4f, 0x4e, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x5f, 0x4c, 0x49, 0x4e,
	0x4b, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0xf2, 0xb6, 0x0d, 0x12, 0x21, 0x0a, 0x1b,
	0x43, 0x4f, 0x44, 0x45, 0x5f, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x53, 0x54, 0x41,
	0x54, 0x55, 0x53, 0x5f, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x10, 0xf3, 0xb6, 0x0d, 0x12,
	0x28, 0x0a, 0x22, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f,
	0x54, 0x48, 0x49, 0x52, 0x44, 0x5f, 0x50, 0x41, 0x52, 0x54, 0x59, 0x5f, 0x41, 0x50, 0x49, 0x5f,
	0x45, 0x52, 0x52, 0x4f, 0x52, 0x10, 0xf4, 0xb6, 0x0d, 0x12, 0x21, 0x0a, 0x1b, 0x43, 0x4f, 0x44,
	0x45, 0x5f, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x52, 0x50, 0x43, 0x5f, 0x43, 0x41,
	0x4c, 0x4c, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x10, 0xf5, 0xb6, 0x0d, 0x12, 0x21, 0x0a, 0x1b,
	0x43, 0x4f, 0x44, 0x45, 0x5f, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x49, 0x4e, 0x56,
	0x41, 0x4c, 0x49, 0x44, 0x5f, 0x52, 0x45, 0x41, 0x44, 0x45, 0x52, 0x10, 0xf6, 0xb6, 0x0d, 0x12,
	0x2f, 0x0a, 0x29, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f,
	0x41, 0x44, 0x59, 0x45, 0x4e, 0x5f, 0x54, 0x45, 0x52, 0x4d, 0x49, 0x4e, 0x41, 0x4c, 0x5f, 0x52,
	0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x10, 0xf7, 0xb6, 0x0d,
	0x12, 0x29, 0x0a, 0x23, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54,
	0x5f, 0x41, 0x44, 0x59, 0x45, 0x4e, 0x5f, 0x47, 0x45, 0x54, 0x5f, 0x53, 0x54, 0x4f, 0x52, 0x45,
	0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0xf8, 0xb6, 0x0d, 0x12, 0x33, 0x0a, 0x2d, 0x43,
	0x4f, 0x44, 0x45, 0x5f, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x41, 0x44, 0x59, 0x45,
	0x4e, 0x5f, 0x47, 0x45, 0x54, 0x5f, 0x42, 0x41, 0x4c, 0x41, 0x4e, 0x43, 0x45, 0x5f, 0x41, 0x43,
	0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0xf9, 0xb6, 0x0d,
	0x12, 0x25, 0x0a, 0x1f, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54,
	0x5f, 0x47, 0x45, 0x54, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x46, 0x41, 0x49,
	0x4c, 0x45, 0x44, 0x10, 0xfa, 0xb6, 0x0d, 0x12, 0x33, 0x0a, 0x2d, 0x43, 0x4f, 0x44, 0x45, 0x5f,
	0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x41, 0x44, 0x59, 0x45, 0x4e, 0x5f, 0x41, 0x44,
	0x44, 0x5f, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x4d, 0x45, 0x54, 0x48, 0x4f, 0x44,
	0x53, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0xfb, 0xb6, 0x0d, 0x12, 0x2b, 0x0a, 0x25,
	0x43, 0x4f, 0x44, 0x45, 0x5f, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x4f, 0x4e, 0x42,
	0x4f, 0x41, 0x52, 0x44, 0x5f, 0x41, 0x4c, 0x52, 0x45, 0x41, 0x44, 0x59, 0x5f, 0x46, 0x49, 0x4e,
	0x49, 0x53, 0x48, 0x45, 0x44, 0x10, 0xfc, 0xb6, 0x0d, 0x12, 0x2d, 0x0a, 0x27, 0x43, 0x4f, 0x44,
	0x45, 0x5f, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x41, 0x44, 0x59, 0x45, 0x4e, 0x5f,
	0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x4f, 0x4e, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x5f,
	0x53, 0x54, 0x45, 0x50, 0x10, 0xfd, 0xb6, 0x0d, 0x12, 0x30, 0x0a, 0x2a, 0x43, 0x4f, 0x44, 0x45,
	0x5f, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x41, 0x44, 0x59, 0x45, 0x4e, 0x5f, 0x47,
	0x45, 0x54, 0x5f, 0x4c, 0x45, 0x47, 0x41, 0x4c, 0x5f, 0x45, 0x4e, 0x54, 0x49, 0x54, 0x59, 0x5f,
	0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0xfe, 0xb6, 0x0d, 0x12, 0x37, 0x0a, 0x31, 0x43, 0x4f,
	0x44, 0x45, 0x5f, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x41, 0x44, 0x59, 0x45, 0x4e,
	0x5f, 0x47, 0x45, 0x54, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x46, 0x45, 0x52, 0x5f, 0x49, 0x4e,
	0x53, 0x54, 0x52, 0x55, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10,
	0xff, 0xb6, 0x0d, 0x12, 0x30, 0x0a, 0x2a, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x50, 0x41, 0x59, 0x4d,
	0x45, 0x4e, 0x54, 0x5f, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x5f, 0x50, 0x41, 0x59, 0x4d, 0x45,
	0x4e, 0x54, 0x5f, 0x53, 0x45, 0x54, 0x54, 0x49, 0x4e, 0x47, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45,
	0x44, 0x10, 0x80, 0xb7, 0x0d, 0x12, 0x33, 0x0a, 0x2d, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x50, 0x41,
	0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x41, 0x44, 0x59, 0x45, 0x4e, 0x5f, 0x47, 0x45, 0x54, 0x5f,
	0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x4d, 0x45, 0x54, 0x48, 0x4f, 0x44, 0x53, 0x5f,
	0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x81, 0xb7, 0x0d, 0x12, 0x2c, 0x0a, 0x26, 0x43, 0x4f,
	0x44, 0x45, 0x5f, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x43, 0x48, 0x41, 0x4e, 0x4e,
	0x45, 0x4c, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x46,
	0x4f, 0x55, 0x4e, 0x44, 0x10, 0x82, 0xb7, 0x0d, 0x12, 0x23, 0x0a, 0x1d, 0x43, 0x4f, 0x44, 0x45,
	0x5f, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x50, 0x41, 0x59, 0x4f, 0x55, 0x54, 0x5f,
	0x4e, 0x4f, 0x54, 0x5f, 0x46, 0x4f, 0x55, 0x4e, 0x44, 0x10, 0xc9, 0xbe, 0x0d, 0x12, 0x24, 0x0a,
	0x1e, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x50, 0x41,
	0x59, 0x4f, 0x55, 0x54, 0x5f, 0x47, 0x45, 0x54, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10,
	0xca, 0xbe, 0x0d, 0x12, 0x27, 0x0a, 0x21, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x50, 0x41, 0x59, 0x4d,
	0x45, 0x4e, 0x54, 0x5f, 0x50, 0x41, 0x59, 0x4f, 0x55, 0x54, 0x5f, 0x43, 0x52, 0x45, 0x41, 0x54,
	0x45, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0xcb, 0xbe, 0x0d, 0x12, 0x20, 0x0a, 0x1a,
	0x43, 0x4f, 0x44, 0x45, 0x5f, 0x42, 0x49, 0x4c, 0x4c, 0x49, 0x4e, 0x47, 0x5f, 0x55, 0x4e, 0x4b,
	0x4e, 0x4f, 0x57, 0x4e, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x10, 0xb1, 0xc6, 0x0d, 0x12, 0x24,
	0x0a, 0x1e, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x42, 0x49, 0x4c, 0x4c, 0x49, 0x4e, 0x47, 0x5f, 0x44,
	0x41, 0x54, 0x41, 0x5f, 0x41, 0x43, 0x43, 0x45, 0x53, 0x53, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52,
	0x10, 0xb2, 0xc6, 0x0d, 0x12, 0x21, 0x0a, 0x1b, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x42, 0x49, 0x4c,
	0x4c, 0x49, 0x4e, 0x47, 0x5f, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x5f, 0x45, 0x52,
	0x52, 0x4f, 0x52, 0x10, 0xb3, 0xc6, 0x0d, 0x42, 0x78, 0x0a, 0x1e, 0x63, 0x6f, 0x6d, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x54, 0x67, 0x69, 0x74,
	0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62,
	0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64,
	0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67,
	0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x65,
	0x72, 0x72, 0x6f, 0x72, 0x73, 0x2f, 0x76, 0x31, 0x3b, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x70,
	0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_errors_v1_error_code_models_proto_rawDescOnce sync.Once
	file_moego_models_errors_v1_error_code_models_proto_rawDescData = file_moego_models_errors_v1_error_code_models_proto_rawDesc
)

func file_moego_models_errors_v1_error_code_models_proto_rawDescGZIP() []byte {
	file_moego_models_errors_v1_error_code_models_proto_rawDescOnce.Do(func() {
		file_moego_models_errors_v1_error_code_models_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_errors_v1_error_code_models_proto_rawDescData)
	})
	return file_moego_models_errors_v1_error_code_models_proto_rawDescData
}

var file_moego_models_errors_v1_error_code_models_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_moego_models_errors_v1_error_code_models_proto_goTypes = []interface{}{
	(Code)(0), // 0: moego.models.errors.v1.Code
}
var file_moego_models_errors_v1_error_code_models_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_moego_models_errors_v1_error_code_models_proto_init() }
func file_moego_models_errors_v1_error_code_models_proto_init() {
	if File_moego_models_errors_v1_error_code_models_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_errors_v1_error_code_models_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_errors_v1_error_code_models_proto_goTypes,
		DependencyIndexes: file_moego_models_errors_v1_error_code_models_proto_depIdxs,
		EnumInfos:         file_moego_models_errors_v1_error_code_models_proto_enumTypes,
	}.Build()
	File_moego_models_errors_v1_error_code_models_proto = out.File
	file_moego_models_errors_v1_error_code_models_proto_rawDesc = nil
	file_moego_models_errors_v1_error_code_models_proto_goTypes = nil
	file_moego_models_errors_v1_error_code_models_proto_depIdxs = nil
}
