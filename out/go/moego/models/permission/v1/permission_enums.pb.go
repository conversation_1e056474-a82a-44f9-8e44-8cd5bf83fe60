// @since 2024-02-21 15:43:52
// <AUTHOR> <zhang<PERSON>@moego.pet>

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/permission/v1/permission_enums.proto

package permissionpb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Permission scope available rule enum
type PermissionScopeAvailableRule int32

const (
	// unspecified
	PermissionScopeAvailableRule_PERMISSION_SCOPE_AVAILABLE_RULE_UNSPECIFIED PermissionScopeAvailableRule = 0
	// only for multi-location user
	PermissionScopeAvailableRule_ONLY_MULTI_LOCATION PermissionScopeAvailableRule = 1
	// only for single-location user
	PermissionScopeAvailableRule_ONLY_SINGLE_LOCATION PermissionScopeAvailableRule = 2
)

// Enum value maps for PermissionScopeAvailableRule.
var (
	PermissionScopeAvailableRule_name = map[int32]string{
		0: "PERMISSION_SCOPE_AVAILABLE_RULE_UNSPECIFIED",
		1: "ONLY_MULTI_LOCATION",
		2: "ONLY_SINGLE_LOCATION",
	}
	PermissionScopeAvailableRule_value = map[string]int32{
		"PERMISSION_SCOPE_AVAILABLE_RULE_UNSPECIFIED": 0,
		"ONLY_MULTI_LOCATION":                         1,
		"ONLY_SINGLE_LOCATION":                        2,
	}
)

func (x PermissionScopeAvailableRule) Enum() *PermissionScopeAvailableRule {
	p := new(PermissionScopeAvailableRule)
	*p = x
	return p
}

func (x PermissionScopeAvailableRule) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PermissionScopeAvailableRule) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_permission_v1_permission_enums_proto_enumTypes[0].Descriptor()
}

func (PermissionScopeAvailableRule) Type() protoreflect.EnumType {
	return &file_moego_models_permission_v1_permission_enums_proto_enumTypes[0]
}

func (x PermissionScopeAvailableRule) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PermissionScopeAvailableRule.Descriptor instead.
func (PermissionScopeAvailableRule) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_permission_v1_permission_enums_proto_rawDescGZIP(), []int{0}
}

// Permission category type enum
type PermissionCategoryType int32

const (
	// unspecified
	PermissionCategoryType_PERMISSION_CATEGORY_TYPE_UNSPECIFIED PermissionCategoryType = 0
	// enterprise hub
	PermissionCategoryType_ENTERPRISE_HUB PermissionCategoryType = 1
	// moego platform
	PermissionCategoryType_MOEGO_PLATFORM PermissionCategoryType = 2
)

// Enum value maps for PermissionCategoryType.
var (
	PermissionCategoryType_name = map[int32]string{
		0: "PERMISSION_CATEGORY_TYPE_UNSPECIFIED",
		1: "ENTERPRISE_HUB",
		2: "MOEGO_PLATFORM",
	}
	PermissionCategoryType_value = map[string]int32{
		"PERMISSION_CATEGORY_TYPE_UNSPECIFIED": 0,
		"ENTERPRISE_HUB":                       1,
		"MOEGO_PLATFORM":                       2,
	}
)

func (x PermissionCategoryType) Enum() *PermissionCategoryType {
	p := new(PermissionCategoryType)
	*p = x
	return p
}

func (x PermissionCategoryType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PermissionCategoryType) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_permission_v1_permission_enums_proto_enumTypes[1].Descriptor()
}

func (PermissionCategoryType) Type() protoreflect.EnumType {
	return &file_moego_models_permission_v1_permission_enums_proto_enumTypes[1]
}

func (x PermissionCategoryType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PermissionCategoryType.Descriptor instead.
func (PermissionCategoryType) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_permission_v1_permission_enums_proto_rawDescGZIP(), []int{1}
}

// Role type
type RoleType int32

const (
	// unspecified
	RoleType_ROLE_TYPE_UNSPECIFIED RoleType = 0
	// company staff role
	RoleType_ROLE_TYPE_COMPANY_STAFF RoleType = 1
	// company owner role
	RoleType_ROLE_TYPE_COMPANY_OWNER RoleType = 2
	// enterprise staff role
	RoleType_ROLE_TYPE_ENTERPRISE_STAFF RoleType = 3
	// enterprise owner role
	RoleType_ROLE_TYPE_ENTERPRISE_OWNER RoleType = 4
	// tenant owner role, a special role in Enterprise Hub.
	// 有 enterprise id 无 company id
	// 在 enterprise 修改该 role 的权限时，会同时修改 MoeGo Platform 下 tenant 对应 company 的 owner 的权限
	RoleType_ROLE_TYPE_TENANT_OWNER RoleType = 5
)

// Enum value maps for RoleType.
var (
	RoleType_name = map[int32]string{
		0: "ROLE_TYPE_UNSPECIFIED",
		1: "ROLE_TYPE_COMPANY_STAFF",
		2: "ROLE_TYPE_COMPANY_OWNER",
		3: "ROLE_TYPE_ENTERPRISE_STAFF",
		4: "ROLE_TYPE_ENTERPRISE_OWNER",
		5: "ROLE_TYPE_TENANT_OWNER",
	}
	RoleType_value = map[string]int32{
		"ROLE_TYPE_UNSPECIFIED":      0,
		"ROLE_TYPE_COMPANY_STAFF":    1,
		"ROLE_TYPE_COMPANY_OWNER":    2,
		"ROLE_TYPE_ENTERPRISE_STAFF": 3,
		"ROLE_TYPE_ENTERPRISE_OWNER": 4,
		"ROLE_TYPE_TENANT_OWNER":     5,
	}
)

func (x RoleType) Enum() *RoleType {
	p := new(RoleType)
	*p = x
	return p
}

func (x RoleType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RoleType) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_permission_v1_permission_enums_proto_enumTypes[2].Descriptor()
}

func (RoleType) Type() protoreflect.EnumType {
	return &file_moego_models_permission_v1_permission_enums_proto_enumTypes[2]
}

func (x RoleType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RoleType.Descriptor instead.
func (RoleType) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_permission_v1_permission_enums_proto_rawDescGZIP(), []int{2}
}

var File_moego_models_permission_v1_permission_enums_proto protoreflect.FileDescriptor

var file_moego_models_permission_v1_permission_enums_proto_rawDesc = []byte{
	0x0a, 0x31, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x70,
	0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x70, 0x65, 0x72,
	0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x1a, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2a,
	0x82, 0x01, 0x0a, 0x1c, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x53, 0x63,
	0x6f, 0x70, 0x65, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x52, 0x75, 0x6c, 0x65,
	0x12, 0x2f, 0x0a, 0x2b, 0x50, 0x45, 0x52, 0x4d, 0x49, 0x53, 0x53, 0x49, 0x4f, 0x4e, 0x5f, 0x53,
	0x43, 0x4f, 0x50, 0x45, 0x5f, 0x41, 0x56, 0x41, 0x49, 0x4c, 0x41, 0x42, 0x4c, 0x45, 0x5f, 0x52,
	0x55, 0x4c, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10,
	0x00, 0x12, 0x17, 0x0a, 0x13, 0x4f, 0x4e, 0x4c, 0x59, 0x5f, 0x4d, 0x55, 0x4c, 0x54, 0x49, 0x5f,
	0x4c, 0x4f, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x01, 0x12, 0x18, 0x0a, 0x14, 0x4f, 0x4e,
	0x4c, 0x59, 0x5f, 0x53, 0x49, 0x4e, 0x47, 0x4c, 0x45, 0x5f, 0x4c, 0x4f, 0x43, 0x41, 0x54, 0x49,
	0x4f, 0x4e, 0x10, 0x02, 0x2a, 0x6a, 0x0a, 0x16, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69,
	0x6f, 0x6e, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x54, 0x79, 0x70, 0x65, 0x12, 0x28,
	0x0a, 0x24, 0x50, 0x45, 0x52, 0x4d, 0x49, 0x53, 0x53, 0x49, 0x4f, 0x4e, 0x5f, 0x43, 0x41, 0x54,
	0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45,
	0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x12, 0x0a, 0x0e, 0x45, 0x4e, 0x54, 0x45,
	0x52, 0x50, 0x52, 0x49, 0x53, 0x45, 0x5f, 0x48, 0x55, 0x42, 0x10, 0x01, 0x12, 0x12, 0x0a, 0x0e,
	0x4d, 0x4f, 0x45, 0x47, 0x4f, 0x5f, 0x50, 0x4c, 0x41, 0x54, 0x46, 0x4f, 0x52, 0x4d, 0x10, 0x02,
	0x2a, 0xbb, 0x01, 0x0a, 0x08, 0x52, 0x6f, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x19, 0x0a,
	0x15, 0x52, 0x4f, 0x4c, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45,
	0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x1b, 0x0a, 0x17, 0x52, 0x4f, 0x4c, 0x45,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x43, 0x4f, 0x4d, 0x50, 0x41, 0x4e, 0x59, 0x5f, 0x53, 0x54,
	0x41, 0x46, 0x46, 0x10, 0x01, 0x12, 0x1b, 0x0a, 0x17, 0x52, 0x4f, 0x4c, 0x45, 0x5f, 0x54, 0x59,
	0x50, 0x45, 0x5f, 0x43, 0x4f, 0x4d, 0x50, 0x41, 0x4e, 0x59, 0x5f, 0x4f, 0x57, 0x4e, 0x45, 0x52,
	0x10, 0x02, 0x12, 0x1e, 0x0a, 0x1a, 0x52, 0x4f, 0x4c, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f,
	0x45, 0x4e, 0x54, 0x45, 0x52, 0x50, 0x52, 0x49, 0x53, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x46, 0x46,
	0x10, 0x03, 0x12, 0x1e, 0x0a, 0x1a, 0x52, 0x4f, 0x4c, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f,
	0x45, 0x4e, 0x54, 0x45, 0x52, 0x50, 0x52, 0x49, 0x53, 0x45, 0x5f, 0x4f, 0x57, 0x4e, 0x45, 0x52,
	0x10, 0x04, 0x12, 0x1a, 0x0a, 0x16, 0x52, 0x4f, 0x4c, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f,
	0x54, 0x45, 0x4e, 0x41, 0x4e, 0x54, 0x5f, 0x4f, 0x57, 0x4e, 0x45, 0x52, 0x10, 0x05, 0x42, 0x84,
	0x01, 0x0a, 0x22, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69,
	0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x5c, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e,
	0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79,
	0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x70, 0x65, 0x72, 0x6d, 0x69,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x3b, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_permission_v1_permission_enums_proto_rawDescOnce sync.Once
	file_moego_models_permission_v1_permission_enums_proto_rawDescData = file_moego_models_permission_v1_permission_enums_proto_rawDesc
)

func file_moego_models_permission_v1_permission_enums_proto_rawDescGZIP() []byte {
	file_moego_models_permission_v1_permission_enums_proto_rawDescOnce.Do(func() {
		file_moego_models_permission_v1_permission_enums_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_permission_v1_permission_enums_proto_rawDescData)
	})
	return file_moego_models_permission_v1_permission_enums_proto_rawDescData
}

var file_moego_models_permission_v1_permission_enums_proto_enumTypes = make([]protoimpl.EnumInfo, 3)
var file_moego_models_permission_v1_permission_enums_proto_goTypes = []interface{}{
	(PermissionScopeAvailableRule)(0), // 0: moego.models.permission.v1.PermissionScopeAvailableRule
	(PermissionCategoryType)(0),       // 1: moego.models.permission.v1.PermissionCategoryType
	(RoleType)(0),                     // 2: moego.models.permission.v1.RoleType
}
var file_moego_models_permission_v1_permission_enums_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_moego_models_permission_v1_permission_enums_proto_init() }
func file_moego_models_permission_v1_permission_enums_proto_init() {
	if File_moego_models_permission_v1_permission_enums_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_permission_v1_permission_enums_proto_rawDesc,
			NumEnums:      3,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_permission_v1_permission_enums_proto_goTypes,
		DependencyIndexes: file_moego_models_permission_v1_permission_enums_proto_depIdxs,
		EnumInfos:         file_moego_models_permission_v1_permission_enums_proto_enumTypes,
	}.Build()
	File_moego_models_permission_v1_permission_enums_proto = out.File
	file_moego_models_permission_v1_permission_enums_proto_rawDesc = nil
	file_moego_models_permission_v1_permission_enums_proto_goTypes = nil
	file_moego_models_permission_v1_permission_enums_proto_depIdxs = nil
}
