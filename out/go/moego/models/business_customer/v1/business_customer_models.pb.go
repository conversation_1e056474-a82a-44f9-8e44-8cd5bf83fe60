// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/business_customer/v1/business_customer_models.proto

package businesscustomerpb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v1"
	date "google.golang.org/genproto/googleapis/type/date"
	dayofweek "google.golang.org/genproto/googleapis/type/dayofweek"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// source of business customer
type BusinessCustomerInfoModel_Source int32

const (
	// unspecified
	BusinessCustomerInfoModel_SOURCE_UNSPECIFIED BusinessCustomerInfoModel_Source = 0
	// 存储值为 "unknown", 未知来源
	BusinessCustomerInfoModel_UNKNOWN BusinessCustomerInfoModel_Source = 1
	// 存储值为 "0", 历史数据, 含义不明
	//
	// Deprecated: Do not use.
	BusinessCustomerInfoModel_SOURCE_ZERO BusinessCustomerInfoModel_Source = 2
	// 存储值为 "1", 历史数据, 含义不明
	//
	// Deprecated: Do not use.
	BusinessCustomerInfoModel_SOURCE_ONE BusinessCustomerInfoModel_Source = 3
	// 存储值为 "2", 历史数据, 含义不明
	//
	// Deprecated: Do not use.
	BusinessCustomerInfoModel_SOURCE_TWO BusinessCustomerInfoModel_Source = 4
	// 存储值为 "3", 历史数据, 含义不明
	//
	// Deprecated: Do not use.
	BusinessCustomerInfoModel_SOURCE_THREE BusinessCustomerInfoModel_Source = 5
	// 存储值为 "4", 历史数据, 含义不明
	//
	// Deprecated: Do not use.
	BusinessCustomerInfoModel_SOURCE_FOUR BusinessCustomerInfoModel_Source = 6
	// 存储值为 "5", 历史数据, 含义不明
	//
	// Deprecated: Do not use.
	BusinessCustomerInfoModel_SOURCE_FIVE BusinessCustomerInfoModel_Source = 7
	// 存储值为 "manual", 表示 B 端手动创建
	BusinessCustomerInfoModel_MANUAL_CREATE BusinessCustomerInfoModel_Source = 8
	// 存储值为 "contacts", 表示 B 端通过 contacts app 导入
	BusinessCustomerInfoModel_SELF_IMPORT BusinessCustomerInfoModel_Source = 9
	// 存储值为 "dm", 表示 customer support 通过 data import 脚本导入
	BusinessCustomerInfoModel_DATA_IMPORT BusinessCustomerInfoModel_Source = 10
	// 存储值为 "ob", 表示通过 online booking 创建
	BusinessCustomerInfoModel_ONLINE_BOOKING BusinessCustomerInfoModel_Source = 11
	// 存储值为 "if", 表示通过 intake form 创建
	BusinessCustomerInfoModel_INTAKE_FORM BusinessCustomerInfoModel_Source = 12
	// 存储值为 "call", 表示通过电话联系创建
	BusinessCustomerInfoModel_CALL_IN BusinessCustomerInfoModel_Source = 13
	// 存储值为 "text", 表示通过短信联系创建
	BusinessCustomerInfoModel_TEXT_IN BusinessCustomerInfoModel_Source = 14
	// 存储值为 "branded", 表示通过 branded app 创建
	BusinessCustomerInfoModel_BRANDED_APP BusinessCustomerInfoModel_Source = 15
)

// Enum value maps for BusinessCustomerInfoModel_Source.
var (
	BusinessCustomerInfoModel_Source_name = map[int32]string{
		0:  "SOURCE_UNSPECIFIED",
		1:  "UNKNOWN",
		2:  "SOURCE_ZERO",
		3:  "SOURCE_ONE",
		4:  "SOURCE_TWO",
		5:  "SOURCE_THREE",
		6:  "SOURCE_FOUR",
		7:  "SOURCE_FIVE",
		8:  "MANUAL_CREATE",
		9:  "SELF_IMPORT",
		10: "DATA_IMPORT",
		11: "ONLINE_BOOKING",
		12: "INTAKE_FORM",
		13: "CALL_IN",
		14: "TEXT_IN",
		15: "BRANDED_APP",
	}
	BusinessCustomerInfoModel_Source_value = map[string]int32{
		"SOURCE_UNSPECIFIED": 0,
		"UNKNOWN":            1,
		"SOURCE_ZERO":        2,
		"SOURCE_ONE":         3,
		"SOURCE_TWO":         4,
		"SOURCE_THREE":       5,
		"SOURCE_FOUR":        6,
		"SOURCE_FIVE":        7,
		"MANUAL_CREATE":      8,
		"SELF_IMPORT":        9,
		"DATA_IMPORT":        10,
		"ONLINE_BOOKING":     11,
		"INTAKE_FORM":        12,
		"CALL_IN":            13,
		"TEXT_IN":            14,
		"BRANDED_APP":        15,
	}
)

func (x BusinessCustomerInfoModel_Source) Enum() *BusinessCustomerInfoModel_Source {
	p := new(BusinessCustomerInfoModel_Source)
	*p = x
	return p
}

func (x BusinessCustomerInfoModel_Source) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BusinessCustomerInfoModel_Source) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_business_customer_v1_business_customer_models_proto_enumTypes[0].Descriptor()
}

func (BusinessCustomerInfoModel_Source) Type() protoreflect.EnumType {
	return &file_moego_models_business_customer_v1_business_customer_models_proto_enumTypes[0]
}

func (x BusinessCustomerInfoModel_Source) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use BusinessCustomerInfoModel_Source.Descriptor instead.
func (BusinessCustomerInfoModel_Source) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_business_customer_v1_business_customer_models_proto_rawDescGZIP(), []int{1, 0}
}

// operation
type BusinessCustomerEventModel_Operation int32

const (
	// unspecified
	BusinessCustomerEventModel_OPERATION_UNSPECIFIED BusinessCustomerEventModel_Operation = 0
	// create
	BusinessCustomerEventModel_CREATE BusinessCustomerEventModel_Operation = 1 // TODO: update and delete
)

// Enum value maps for BusinessCustomerEventModel_Operation.
var (
	BusinessCustomerEventModel_Operation_name = map[int32]string{
		0: "OPERATION_UNSPECIFIED",
		1: "CREATE",
	}
	BusinessCustomerEventModel_Operation_value = map[string]int32{
		"OPERATION_UNSPECIFIED": 0,
		"CREATE":                1,
	}
)

func (x BusinessCustomerEventModel_Operation) Enum() *BusinessCustomerEventModel_Operation {
	p := new(BusinessCustomerEventModel_Operation)
	*p = x
	return p
}

func (x BusinessCustomerEventModel_Operation) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BusinessCustomerEventModel_Operation) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_business_customer_v1_business_customer_models_proto_enumTypes[1].Descriptor()
}

func (BusinessCustomerEventModel_Operation) Type() protoreflect.EnumType {
	return &file_moego_models_business_customer_v1_business_customer_models_proto_enumTypes[1]
}

func (x BusinessCustomerEventModel_Operation) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use BusinessCustomerEventModel_Operation.Descriptor instead.
func (BusinessCustomerEventModel_Operation) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_business_customer_v1_business_customer_models_proto_rawDescGZIP(), []int{2, 0}
}

// the customer model
// 这个模型弃用, 里面的字段太乱了, 没有设计好. 请使用 BusinessCustomerInfoModel
//
// Deprecated: Do not use.
type BusinessCustomerModel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// customer id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// company id
	CompanyId int64 `protobuf:"varint,6,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// preferred business id
	PreferredBusinessId int64 `protobuf:"varint,5,opt,name=preferred_business_id,json=preferredBusinessId,proto3" json:"preferred_business_id,omitempty"`
	// business id, please use preferred_business_id instead
	//
	// Deprecated: Do not use.
	BusinessId int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// customer code
	CustomerCode string `protobuf:"bytes,7,opt,name=customer_code,json=customerCode,proto3" json:"customer_code,omitempty"`
	// account id, 0 means no binding account
	AccountId int64 `protobuf:"varint,8,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"`
	// external id
	ExternalId string `protobuf:"bytes,9,opt,name=external_id,json=externalId,proto3" json:"external_id,omitempty"`
	// first name
	FirstName string `protobuf:"bytes,3,opt,name=first_name,json=firstName,proto3" json:"first_name,omitempty"`
	// last name
	LastName string `protobuf:"bytes,4,opt,name=last_name,json=lastName,proto3" json:"last_name,omitempty"`
	// avatar path
	AvatarPath string `protobuf:"bytes,10,opt,name=avatar_path,json=avatarPath,proto3" json:"avatar_path,omitempty"`
	// email
	Email string `protobuf:"bytes,11,opt,name=email,proto3" json:"email,omitempty"`
	// phone number
	// this is the identifier for the customer,
	// but it may not be the primary phone number
	PhoneNumber string `protobuf:"bytes,12,opt,name=phone_number,json=phoneNumber,proto3" json:"phone_number,omitempty"`
	// client color
	ClientColor string `protobuf:"bytes,13,opt,name=client_color,json=clientColor,proto3" json:"client_color,omitempty"`
	// referral source id
	ReferralSourceId int64 `protobuf:"varint,21,opt,name=referral_source_id,json=referralSourceId,proto3" json:"referral_source_id,omitempty"`
	// source
	Source string `protobuf:"bytes,22,opt,name=source,proto3" json:"source,omitempty"`
	// inactive
	Inactive bool `protobuf:"varint,23,opt,name=inactive,proto3" json:"inactive,omitempty"`
	// if the customer is deleted
	Deleted bool `protobuf:"varint,24,opt,name=deleted,proto3" json:"deleted,omitempty"`
	// created at
	CreatedAt *timestamppb.Timestamp `protobuf:"bytes,25,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	// updated at
	UpdatedAt *timestamppb.Timestamp `protobuf:"bytes,26,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	// created by (staff id)
	CreatedBy int64 `protobuf:"varint,27,opt,name=created_by,json=createdBy,proto3" json:"created_by,omitempty"`
	// updated by (staff id)
	UpdatedBy int64 `protobuf:"varint,28,opt,name=updated_by,json=updatedBy,proto3" json:"updated_by,omitempty"`
	// is block online booking
	IsBlockOnlineBooking bool `protobuf:"varint,14,opt,name=is_block_online_booking,json=isBlockOnlineBooking,proto3" json:"is_block_online_booking,omitempty"`
	// is block message
	IsBlockMessage bool `protobuf:"varint,15,opt,name=is_block_message,json=isBlockMessage,proto3" json:"is_block_message,omitempty"`
	// send auto email
	SendAutoEmail bool `protobuf:"varint,16,opt,name=send_auto_email,json=sendAutoEmail,proto3" json:"send_auto_email,omitempty"`
	// send auto message
	SendAutoMessage bool `protobuf:"varint,17,opt,name=send_auto_message,json=sendAutoMessage,proto3" json:"send_auto_message,omitempty"`
	// unconfirmed reminder by (this field need to be redesigned)
	UnconfirmedReminderBy int32 `protobuf:"varint,18,opt,name=unconfirmed_reminder_by,json=unconfirmedReminderBy,proto3" json:"unconfirmed_reminder_by,omitempty"`
	// is unsubscribed
	IsUnsubscribed bool `protobuf:"varint,19,opt,name=is_unsubscribed,json=isUnsubscribed,proto3" json:"is_unsubscribed,omitempty"`
	// is recurring
	IsRecurring bool `protobuf:"varint,20,opt,name=is_recurring,json=isRecurring,proto3" json:"is_recurring,omitempty"`
	// preferred groomer id
	PreferredGroomerId int64 `protobuf:"varint,30,opt,name=preferred_groomer_id,json=preferredGroomerId,proto3" json:"preferred_groomer_id,omitempty"`
	// preferred grooming frequency
	PreferredGroomingFrequency *v1.TimePeriod `protobuf:"bytes,31,opt,name=preferred_grooming_frequency,json=preferredGroomingFrequency,proto3" json:"preferred_grooming_frequency,omitempty"`
	// preferred day of week
	PreferredDayOfWeek []dayofweek.DayOfWeek `protobuf:"varint,32,rep,packed,name=preferred_day_of_week,json=preferredDayOfWeek,proto3,enum=google.type.DayOfWeek" json:"preferred_day_of_week,omitempty"`
	// preferred time of day
	PreferredTimeOfDay *v1.TimeOfDayInterval `protobuf:"bytes,33,opt,name=preferred_time_of_day,json=preferredTimeOfDay,proto3" json:"preferred_time_of_day,omitempty"`
	// share appt status
	ShareApptStatus int32 `protobuf:"varint,40,opt,name=share_appt_status,json=shareApptStatus,proto3" json:"share_appt_status,omitempty"`
	// share range type
	ShareRangeType int32 `protobuf:"varint,41,opt,name=share_range_type,json=shareRangeType,proto3" json:"share_range_type,omitempty"`
	// share range value
	ShareRangeValue int32 `protobuf:"varint,42,opt,name=share_range_value,json=shareRangeValue,proto3" json:"share_range_value,omitempty"`
	// share appt ids
	ShareApptIds []int64 `protobuf:"varint,43,rep,packed,name=share_appt_ids,json=shareApptIds,proto3" json:"share_appt_ids,omitempty"`
	// last service time, not precise and may not exist, may be removed in the future
	LastServiceTime *date.Date `protobuf:"bytes,44,opt,name=last_service_time,json=lastServiceTime,proto3,oneof" json:"last_service_time,omitempty"`
	// send app auto message
	SendAppAutoMessage bool `protobuf:"varint,45,opt,name=send_app_auto_message,json=sendAppAutoMessage,proto3" json:"send_app_auto_message,omitempty"`
	// birthday
	Birthday *timestamppb.Timestamp `protobuf:"bytes,46,opt,name=birthday,proto3" json:"birthday,omitempty"`
	// referral source desc
	ReferralSourceDesc string `protobuf:"bytes,47,opt,name=referral_source_desc,json=referralSourceDesc,proto3" json:"referral_source_desc,omitempty"`
	// CRM leads 字段
	CustomerType string `protobuf:"bytes,48,opt,name=customer_type,json=customerType,proto3" json:"customer_type,omitempty"`
}

func (x *BusinessCustomerModel) Reset() {
	*x = BusinessCustomerModel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_business_customer_v1_business_customer_models_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BusinessCustomerModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BusinessCustomerModel) ProtoMessage() {}

func (x *BusinessCustomerModel) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_business_customer_v1_business_customer_models_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BusinessCustomerModel.ProtoReflect.Descriptor instead.
func (*BusinessCustomerModel) Descriptor() ([]byte, []int) {
	return file_moego_models_business_customer_v1_business_customer_models_proto_rawDescGZIP(), []int{0}
}

func (x *BusinessCustomerModel) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *BusinessCustomerModel) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *BusinessCustomerModel) GetPreferredBusinessId() int64 {
	if x != nil {
		return x.PreferredBusinessId
	}
	return 0
}

// Deprecated: Do not use.
func (x *BusinessCustomerModel) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *BusinessCustomerModel) GetCustomerCode() string {
	if x != nil {
		return x.CustomerCode
	}
	return ""
}

func (x *BusinessCustomerModel) GetAccountId() int64 {
	if x != nil {
		return x.AccountId
	}
	return 0
}

func (x *BusinessCustomerModel) GetExternalId() string {
	if x != nil {
		return x.ExternalId
	}
	return ""
}

func (x *BusinessCustomerModel) GetFirstName() string {
	if x != nil {
		return x.FirstName
	}
	return ""
}

func (x *BusinessCustomerModel) GetLastName() string {
	if x != nil {
		return x.LastName
	}
	return ""
}

func (x *BusinessCustomerModel) GetAvatarPath() string {
	if x != nil {
		return x.AvatarPath
	}
	return ""
}

func (x *BusinessCustomerModel) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *BusinessCustomerModel) GetPhoneNumber() string {
	if x != nil {
		return x.PhoneNumber
	}
	return ""
}

func (x *BusinessCustomerModel) GetClientColor() string {
	if x != nil {
		return x.ClientColor
	}
	return ""
}

func (x *BusinessCustomerModel) GetReferralSourceId() int64 {
	if x != nil {
		return x.ReferralSourceId
	}
	return 0
}

func (x *BusinessCustomerModel) GetSource() string {
	if x != nil {
		return x.Source
	}
	return ""
}

func (x *BusinessCustomerModel) GetInactive() bool {
	if x != nil {
		return x.Inactive
	}
	return false
}

func (x *BusinessCustomerModel) GetDeleted() bool {
	if x != nil {
		return x.Deleted
	}
	return false
}

func (x *BusinessCustomerModel) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *BusinessCustomerModel) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *BusinessCustomerModel) GetCreatedBy() int64 {
	if x != nil {
		return x.CreatedBy
	}
	return 0
}

func (x *BusinessCustomerModel) GetUpdatedBy() int64 {
	if x != nil {
		return x.UpdatedBy
	}
	return 0
}

func (x *BusinessCustomerModel) GetIsBlockOnlineBooking() bool {
	if x != nil {
		return x.IsBlockOnlineBooking
	}
	return false
}

func (x *BusinessCustomerModel) GetIsBlockMessage() bool {
	if x != nil {
		return x.IsBlockMessage
	}
	return false
}

func (x *BusinessCustomerModel) GetSendAutoEmail() bool {
	if x != nil {
		return x.SendAutoEmail
	}
	return false
}

func (x *BusinessCustomerModel) GetSendAutoMessage() bool {
	if x != nil {
		return x.SendAutoMessage
	}
	return false
}

func (x *BusinessCustomerModel) GetUnconfirmedReminderBy() int32 {
	if x != nil {
		return x.UnconfirmedReminderBy
	}
	return 0
}

func (x *BusinessCustomerModel) GetIsUnsubscribed() bool {
	if x != nil {
		return x.IsUnsubscribed
	}
	return false
}

func (x *BusinessCustomerModel) GetIsRecurring() bool {
	if x != nil {
		return x.IsRecurring
	}
	return false
}

func (x *BusinessCustomerModel) GetPreferredGroomerId() int64 {
	if x != nil {
		return x.PreferredGroomerId
	}
	return 0
}

func (x *BusinessCustomerModel) GetPreferredGroomingFrequency() *v1.TimePeriod {
	if x != nil {
		return x.PreferredGroomingFrequency
	}
	return nil
}

func (x *BusinessCustomerModel) GetPreferredDayOfWeek() []dayofweek.DayOfWeek {
	if x != nil {
		return x.PreferredDayOfWeek
	}
	return nil
}

func (x *BusinessCustomerModel) GetPreferredTimeOfDay() *v1.TimeOfDayInterval {
	if x != nil {
		return x.PreferredTimeOfDay
	}
	return nil
}

func (x *BusinessCustomerModel) GetShareApptStatus() int32 {
	if x != nil {
		return x.ShareApptStatus
	}
	return 0
}

func (x *BusinessCustomerModel) GetShareRangeType() int32 {
	if x != nil {
		return x.ShareRangeType
	}
	return 0
}

func (x *BusinessCustomerModel) GetShareRangeValue() int32 {
	if x != nil {
		return x.ShareRangeValue
	}
	return 0
}

func (x *BusinessCustomerModel) GetShareApptIds() []int64 {
	if x != nil {
		return x.ShareApptIds
	}
	return nil
}

func (x *BusinessCustomerModel) GetLastServiceTime() *date.Date {
	if x != nil {
		return x.LastServiceTime
	}
	return nil
}

func (x *BusinessCustomerModel) GetSendAppAutoMessage() bool {
	if x != nil {
		return x.SendAppAutoMessage
	}
	return false
}

func (x *BusinessCustomerModel) GetBirthday() *timestamppb.Timestamp {
	if x != nil {
		return x.Birthday
	}
	return nil
}

func (x *BusinessCustomerModel) GetReferralSourceDesc() string {
	if x != nil {
		return x.ReferralSourceDesc
	}
	return ""
}

func (x *BusinessCustomerModel) GetCustomerType() string {
	if x != nil {
		return x.CustomerType
	}
	return ""
}

// info model for business customer
// 注意, 如果是业务相关的 preference 字段, 请优先考虑加到对应的 preference model 里,
// 主要是考虑到 preference model 可能在以后会收拢到对应的业务域中.
type BusinessCustomerInfoModel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// customer id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// company id
	CompanyId int64 `protobuf:"varint,2,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// preferred business id
	PreferredBusinessId int64 `protobuf:"varint,3,opt,name=preferred_business_id,json=preferredBusinessId,proto3" json:"preferred_business_id,omitempty"`
	// source
	Source BusinessCustomerInfoModel_Source `protobuf:"varint,4,opt,name=source,proto3,enum=moego.models.business_customer.v1.BusinessCustomerInfoModel_Source" json:"source,omitempty"`
	// avatar path
	AvatarPath string `protobuf:"bytes,5,opt,name=avatar_path,json=avatarPath,proto3" json:"avatar_path,omitempty"`
	// first name
	FirstName string `protobuf:"bytes,6,opt,name=first_name,json=firstName,proto3" json:"first_name,omitempty"`
	// last name
	LastName string `protobuf:"bytes,7,opt,name=last_name,json=lastName,proto3" json:"last_name,omitempty"`
	// email
	Email string `protobuf:"bytes,8,opt,name=email,proto3" json:"email,omitempty"`
	// phone number
	// this is the identifier for the customer,
	// but it may not be the primary phone number
	PhoneNumber string `protobuf:"bytes,9,opt,name=phone_number,json=phoneNumber,proto3" json:"phone_number,omitempty"`
	// client color
	ClientColor string `protobuf:"bytes,10,opt,name=client_color,json=clientColor,proto3" json:"client_color,omitempty"`
	// customer code
	CustomerCode string `protobuf:"bytes,11,opt,name=customer_code,json=customerCode,proto3" json:"customer_code,omitempty"`
	// account id, 0 means no binding account
	AccountId int64 `protobuf:"varint,12,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"`
	// referral source id
	ReferralSourceId int64 `protobuf:"varint,13,opt,name=referral_source_id,json=referralSourceId,proto3" json:"referral_source_id,omitempty"`
	// customer tag ids
	TagIds []int64 `protobuf:"varint,14,rep,packed,name=tag_ids,json=tagIds,proto3" json:"tag_ids,omitempty"`
	// inactive
	Inactive bool `protobuf:"varint,15,opt,name=inactive,proto3" json:"inactive,omitempty"`
	// if the customer is deleted
	Deleted bool `protobuf:"varint,16,opt,name=deleted,proto3" json:"deleted,omitempty"`
}

func (x *BusinessCustomerInfoModel) Reset() {
	*x = BusinessCustomerInfoModel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_business_customer_v1_business_customer_models_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BusinessCustomerInfoModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BusinessCustomerInfoModel) ProtoMessage() {}

func (x *BusinessCustomerInfoModel) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_business_customer_v1_business_customer_models_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BusinessCustomerInfoModel.ProtoReflect.Descriptor instead.
func (*BusinessCustomerInfoModel) Descriptor() ([]byte, []int) {
	return file_moego_models_business_customer_v1_business_customer_models_proto_rawDescGZIP(), []int{1}
}

func (x *BusinessCustomerInfoModel) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *BusinessCustomerInfoModel) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *BusinessCustomerInfoModel) GetPreferredBusinessId() int64 {
	if x != nil {
		return x.PreferredBusinessId
	}
	return 0
}

func (x *BusinessCustomerInfoModel) GetSource() BusinessCustomerInfoModel_Source {
	if x != nil {
		return x.Source
	}
	return BusinessCustomerInfoModel_SOURCE_UNSPECIFIED
}

func (x *BusinessCustomerInfoModel) GetAvatarPath() string {
	if x != nil {
		return x.AvatarPath
	}
	return ""
}

func (x *BusinessCustomerInfoModel) GetFirstName() string {
	if x != nil {
		return x.FirstName
	}
	return ""
}

func (x *BusinessCustomerInfoModel) GetLastName() string {
	if x != nil {
		return x.LastName
	}
	return ""
}

func (x *BusinessCustomerInfoModel) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *BusinessCustomerInfoModel) GetPhoneNumber() string {
	if x != nil {
		return x.PhoneNumber
	}
	return ""
}

func (x *BusinessCustomerInfoModel) GetClientColor() string {
	if x != nil {
		return x.ClientColor
	}
	return ""
}

func (x *BusinessCustomerInfoModel) GetCustomerCode() string {
	if x != nil {
		return x.CustomerCode
	}
	return ""
}

func (x *BusinessCustomerInfoModel) GetAccountId() int64 {
	if x != nil {
		return x.AccountId
	}
	return 0
}

func (x *BusinessCustomerInfoModel) GetReferralSourceId() int64 {
	if x != nil {
		return x.ReferralSourceId
	}
	return 0
}

func (x *BusinessCustomerInfoModel) GetTagIds() []int64 {
	if x != nil {
		return x.TagIds
	}
	return nil
}

func (x *BusinessCustomerInfoModel) GetInactive() bool {
	if x != nil {
		return x.Inactive
	}
	return false
}

func (x *BusinessCustomerInfoModel) GetDeleted() bool {
	if x != nil {
		return x.Deleted
	}
	return false
}

// event bus model for business customer
type BusinessCustomerEventModel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// operation
	Operation BusinessCustomerEventModel_Operation `protobuf:"varint,1,opt,name=operation,proto3,enum=moego.models.business_customer.v1.BusinessCustomerEventModel_Operation" json:"operation,omitempty"`
	// customer id
	Id int64 `protobuf:"varint,2,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *BusinessCustomerEventModel) Reset() {
	*x = BusinessCustomerEventModel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_business_customer_v1_business_customer_models_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BusinessCustomerEventModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BusinessCustomerEventModel) ProtoMessage() {}

func (x *BusinessCustomerEventModel) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_business_customer_v1_business_customer_models_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BusinessCustomerEventModel.ProtoReflect.Descriptor instead.
func (*BusinessCustomerEventModel) Descriptor() ([]byte, []int) {
	return file_moego_models_business_customer_v1_business_customer_models_proto_rawDescGZIP(), []int{2}
}

func (x *BusinessCustomerEventModel) GetOperation() BusinessCustomerEventModel_Operation {
	if x != nil {
		return x.Operation
	}
	return BusinessCustomerEventModel_OPERATION_UNSPECIFIED
}

func (x *BusinessCustomerEventModel) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// the customer model
type BusinessCustomerModelPublicView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// customer id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// company id
	CompanyId int64 `protobuf:"varint,6,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// first name
	FirstName string `protobuf:"bytes,3,opt,name=first_name,json=firstName,proto3" json:"first_name,omitempty"`
	// last name
	LastName string `protobuf:"bytes,4,opt,name=last_name,json=lastName,proto3" json:"last_name,omitempty"`
	// avatar path
	AvatarPath string `protobuf:"bytes,10,opt,name=avatar_path,json=avatarPath,proto3" json:"avatar_path,omitempty"`
	// email
	Email string `protobuf:"bytes,11,opt,name=email,proto3" json:"email,omitempty"`
	// phone number
	// this is the identifier for the customer,
	// but it may not be the primary phone number
	PhoneNumber string `protobuf:"bytes,12,opt,name=phone_number,json=phoneNumber,proto3" json:"phone_number,omitempty"`
}

func (x *BusinessCustomerModelPublicView) Reset() {
	*x = BusinessCustomerModelPublicView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_business_customer_v1_business_customer_models_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BusinessCustomerModelPublicView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BusinessCustomerModelPublicView) ProtoMessage() {}

func (x *BusinessCustomerModelPublicView) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_business_customer_v1_business_customer_models_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BusinessCustomerModelPublicView.ProtoReflect.Descriptor instead.
func (*BusinessCustomerModelPublicView) Descriptor() ([]byte, []int) {
	return file_moego_models_business_customer_v1_business_customer_models_proto_rawDescGZIP(), []int{3}
}

func (x *BusinessCustomerModelPublicView) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *BusinessCustomerModelPublicView) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *BusinessCustomerModelPublicView) GetFirstName() string {
	if x != nil {
		return x.FirstName
	}
	return ""
}

func (x *BusinessCustomerModelPublicView) GetLastName() string {
	if x != nil {
		return x.LastName
	}
	return ""
}

func (x *BusinessCustomerModelPublicView) GetAvatarPath() string {
	if x != nil {
		return x.AvatarPath
	}
	return ""
}

func (x *BusinessCustomerModelPublicView) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *BusinessCustomerModelPublicView) GetPhoneNumber() string {
	if x != nil {
		return x.PhoneNumber
	}
	return ""
}

// business customer view for c app client view
type BusinessCustomerModelClientView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// is block online booking
	IsBlockOnlineBooking bool `protobuf:"varint,1,opt,name=is_block_online_booking,json=isBlockOnlineBooking,proto3" json:"is_block_online_booking,omitempty"`
	// is block message
	IsBlockMessage bool `protobuf:"varint,2,opt,name=is_block_message,json=isBlockMessage,proto3" json:"is_block_message,omitempty"`
	// the first name
	FirstName string `protobuf:"bytes,3,opt,name=first_name,json=firstName,proto3" json:"first_name,omitempty"`
	// the last name
	LastName string `protobuf:"bytes,4,opt,name=last_name,json=lastName,proto3" json:"last_name,omitempty"`
}

func (x *BusinessCustomerModelClientView) Reset() {
	*x = BusinessCustomerModelClientView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_business_customer_v1_business_customer_models_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BusinessCustomerModelClientView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BusinessCustomerModelClientView) ProtoMessage() {}

func (x *BusinessCustomerModelClientView) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_business_customer_v1_business_customer_models_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BusinessCustomerModelClientView.ProtoReflect.Descriptor instead.
func (*BusinessCustomerModelClientView) Descriptor() ([]byte, []int) {
	return file_moego_models_business_customer_v1_business_customer_models_proto_rawDescGZIP(), []int{4}
}

func (x *BusinessCustomerModelClientView) GetIsBlockOnlineBooking() bool {
	if x != nil {
		return x.IsBlockOnlineBooking
	}
	return false
}

func (x *BusinessCustomerModelClientView) GetIsBlockMessage() bool {
	if x != nil {
		return x.IsBlockMessage
	}
	return false
}

func (x *BusinessCustomerModelClientView) GetFirstName() string {
	if x != nil {
		return x.FirstName
	}
	return ""
}

func (x *BusinessCustomerModelClientView) GetLastName() string {
	if x != nil {
		return x.LastName
	}
	return ""
}

// business customer view for c app link view
type BusinessCustomerModelLinkView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the first name
	FirstName string `protobuf:"bytes,3,opt,name=first_name,json=firstName,proto3" json:"first_name,omitempty"`
	// the last name
	LastName string `protobuf:"bytes,4,opt,name=last_name,json=lastName,proto3" json:"last_name,omitempty"`
}

func (x *BusinessCustomerModelLinkView) Reset() {
	*x = BusinessCustomerModelLinkView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_business_customer_v1_business_customer_models_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BusinessCustomerModelLinkView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BusinessCustomerModelLinkView) ProtoMessage() {}

func (x *BusinessCustomerModelLinkView) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_business_customer_v1_business_customer_models_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BusinessCustomerModelLinkView.ProtoReflect.Descriptor instead.
func (*BusinessCustomerModelLinkView) Descriptor() ([]byte, []int) {
	return file_moego_models_business_customer_v1_business_customer_models_proto_rawDescGZIP(), []int{5}
}

func (x *BusinessCustomerModelLinkView) GetFirstName() string {
	if x != nil {
		return x.FirstName
	}
	return ""
}

func (x *BusinessCustomerModelLinkView) GetLastName() string {
	if x != nil {
		return x.LastName
	}
	return ""
}

// business customer view for name status
type BusinessCustomerNameStatusView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// customer id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// company id
	CompanyId int64 `protobuf:"varint,2,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// first name
	FirstName string `protobuf:"bytes,3,opt,name=first_name,json=firstName,proto3" json:"first_name,omitempty"`
	// last name
	LastName string `protobuf:"bytes,4,opt,name=last_name,json=lastName,proto3" json:"last_name,omitempty"`
	// if the customer is deleted
	Deleted bool `protobuf:"varint,5,opt,name=deleted,proto3" json:"deleted,omitempty"`
}

func (x *BusinessCustomerNameStatusView) Reset() {
	*x = BusinessCustomerNameStatusView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_business_customer_v1_business_customer_models_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BusinessCustomerNameStatusView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BusinessCustomerNameStatusView) ProtoMessage() {}

func (x *BusinessCustomerNameStatusView) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_business_customer_v1_business_customer_models_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BusinessCustomerNameStatusView.ProtoReflect.Descriptor instead.
func (*BusinessCustomerNameStatusView) Descriptor() ([]byte, []int) {
	return file_moego_models_business_customer_v1_business_customer_models_proto_rawDescGZIP(), []int{6}
}

func (x *BusinessCustomerNameStatusView) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *BusinessCustomerNameStatusView) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *BusinessCustomerNameStatusView) GetFirstName() string {
	if x != nil {
		return x.FirstName
	}
	return ""
}

func (x *BusinessCustomerNameStatusView) GetLastName() string {
	if x != nil {
		return x.LastName
	}
	return ""
}

func (x *BusinessCustomerNameStatusView) GetDeleted() bool {
	if x != nil {
		return x.Deleted
	}
	return false
}

// business customer name view
type BusinessCustomerModelNameView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// customer id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// the first name
	FirstName string `protobuf:"bytes,3,opt,name=first_name,json=firstName,proto3" json:"first_name,omitempty"`
	// the last name
	LastName string `protobuf:"bytes,4,opt,name=last_name,json=lastName,proto3" json:"last_name,omitempty"`
}

func (x *BusinessCustomerModelNameView) Reset() {
	*x = BusinessCustomerModelNameView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_business_customer_v1_business_customer_models_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BusinessCustomerModelNameView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BusinessCustomerModelNameView) ProtoMessage() {}

func (x *BusinessCustomerModelNameView) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_business_customer_v1_business_customer_models_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BusinessCustomerModelNameView.ProtoReflect.Descriptor instead.
func (*BusinessCustomerModelNameView) Descriptor() ([]byte, []int) {
	return file_moego_models_business_customer_v1_business_customer_models_proto_rawDescGZIP(), []int{7}
}

func (x *BusinessCustomerModelNameView) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *BusinessCustomerModelNameView) GetFirstName() string {
	if x != nil {
		return x.FirstName
	}
	return ""
}

func (x *BusinessCustomerModelNameView) GetLastName() string {
	if x != nil {
		return x.LastName
	}
	return ""
}

// business customer id view
type BusinessCustomerModelIdView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// customer id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// company id
	CompanyId int64 `protobuf:"varint,2,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// preferred business id
	PreferredBusinessId int64 `protobuf:"varint,3,opt,name=preferred_business_id,json=preferredBusinessId,proto3" json:"preferred_business_id,omitempty"`
}

func (x *BusinessCustomerModelIdView) Reset() {
	*x = BusinessCustomerModelIdView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_business_customer_v1_business_customer_models_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BusinessCustomerModelIdView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BusinessCustomerModelIdView) ProtoMessage() {}

func (x *BusinessCustomerModelIdView) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_business_customer_v1_business_customer_models_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BusinessCustomerModelIdView.ProtoReflect.Descriptor instead.
func (*BusinessCustomerModelIdView) Descriptor() ([]byte, []int) {
	return file_moego_models_business_customer_v1_business_customer_models_proto_rawDescGZIP(), []int{8}
}

func (x *BusinessCustomerModelIdView) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *BusinessCustomerModelIdView) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *BusinessCustomerModelIdView) GetPreferredBusinessId() int64 {
	if x != nil {
		return x.PreferredBusinessId
	}
	return 0
}

// business customer view for appointment view
type BusinessCustomerCalendarView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// customer id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// first name
	FirstName string `protobuf:"bytes,2,opt,name=first_name,json=firstName,proto3" json:"first_name,omitempty"`
	// last name
	LastName string `protobuf:"bytes,3,opt,name=last_name,json=lastName,proto3" json:"last_name,omitempty"`
	// avatar path
	AvatarPath string `protobuf:"bytes,4,opt,name=avatar_path,json=avatarPath,proto3" json:"avatar_path,omitempty"`
	// email
	Email string `protobuf:"bytes,11,opt,name=email,proto3" json:"email,omitempty"`
	// phone number
	// this is the identifier for the customer,
	// but it may not be the primary phone number
	PhoneNumber string `protobuf:"bytes,12,opt,name=phone_number,json=phoneNumber,proto3" json:"phone_number,omitempty"`
	// client color
	ClientColor string `protobuf:"bytes,13,opt,name=client_color,json=clientColor,proto3" json:"client_color,omitempty"`
	// referral source id
	ReferralSourceId int64 `protobuf:"varint,21,opt,name=referral_source_id,json=referralSourceId,proto3" json:"referral_source_id,omitempty"`
	// source
	Source string `protobuf:"bytes,22,opt,name=source,proto3" json:"source,omitempty"`
	// inactive
	Inactive bool `protobuf:"varint,23,opt,name=inactive,proto3" json:"inactive,omitempty"`
	// if the customer is deleted
	Deleted bool `protobuf:"varint,24,opt,name=deleted,proto3" json:"deleted,omitempty"`
	// is block online booking
	IsBlockOnlineBooking bool `protobuf:"varint,14,opt,name=is_block_online_booking,json=isBlockOnlineBooking,proto3" json:"is_block_online_booking,omitempty"`
	// is block message
	IsBlockMessage bool `protobuf:"varint,15,opt,name=is_block_message,json=isBlockMessage,proto3" json:"is_block_message,omitempty"`
	// send auto email
	SendAutoEmail bool `protobuf:"varint,16,opt,name=send_auto_email,json=sendAutoEmail,proto3" json:"send_auto_email,omitempty"`
	// send auto message
	SendAutoMessage bool `protobuf:"varint,17,opt,name=send_auto_message,json=sendAutoMessage,proto3" json:"send_auto_message,omitempty"`
	// unconfirmed reminder by (this field need to be redesigned)
	UnconfirmedReminderBy int32 `protobuf:"varint,18,opt,name=unconfirmed_reminder_by,json=unconfirmedReminderBy,proto3" json:"unconfirmed_reminder_by,omitempty"`
	// is unsubscribed
	IsUnsubscribed bool `protobuf:"varint,19,opt,name=is_unsubscribed,json=isUnsubscribed,proto3" json:"is_unsubscribed,omitempty"`
	// is recurring
	IsRecurring bool `protobuf:"varint,20,opt,name=is_recurring,json=isRecurring,proto3" json:"is_recurring,omitempty"`
	// preferred groomer id
	PreferredGroomerId int64 `protobuf:"varint,30,opt,name=preferred_groomer_id,json=preferredGroomerId,proto3" json:"preferred_groomer_id,omitempty"`
	// preferred grooming frequency
	PreferredGroomingFrequency *v1.TimePeriod `protobuf:"bytes,31,opt,name=preferred_grooming_frequency,json=preferredGroomingFrequency,proto3" json:"preferred_grooming_frequency,omitempty"`
	// preferred day of week
	PreferredDayOfWeek []dayofweek.DayOfWeek `protobuf:"varint,32,rep,packed,name=preferred_day_of_week,json=preferredDayOfWeek,proto3,enum=google.type.DayOfWeek" json:"preferred_day_of_week,omitempty"`
	// preferred time of day
	PreferredTimeOfDay *v1.TimeOfDayInterval `protobuf:"bytes,33,opt,name=preferred_time_of_day,json=preferredTimeOfDay,proto3" json:"preferred_time_of_day,omitempty"`
	// send app auto message
	SendAppAutoMessage bool `protobuf:"varint,34,opt,name=send_app_auto_message,json=sendAppAutoMessage,proto3" json:"send_app_auto_message,omitempty"`
}

func (x *BusinessCustomerCalendarView) Reset() {
	*x = BusinessCustomerCalendarView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_business_customer_v1_business_customer_models_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BusinessCustomerCalendarView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BusinessCustomerCalendarView) ProtoMessage() {}

func (x *BusinessCustomerCalendarView) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_business_customer_v1_business_customer_models_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BusinessCustomerCalendarView.ProtoReflect.Descriptor instead.
func (*BusinessCustomerCalendarView) Descriptor() ([]byte, []int) {
	return file_moego_models_business_customer_v1_business_customer_models_proto_rawDescGZIP(), []int{9}
}

func (x *BusinessCustomerCalendarView) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *BusinessCustomerCalendarView) GetFirstName() string {
	if x != nil {
		return x.FirstName
	}
	return ""
}

func (x *BusinessCustomerCalendarView) GetLastName() string {
	if x != nil {
		return x.LastName
	}
	return ""
}

func (x *BusinessCustomerCalendarView) GetAvatarPath() string {
	if x != nil {
		return x.AvatarPath
	}
	return ""
}

func (x *BusinessCustomerCalendarView) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *BusinessCustomerCalendarView) GetPhoneNumber() string {
	if x != nil {
		return x.PhoneNumber
	}
	return ""
}

func (x *BusinessCustomerCalendarView) GetClientColor() string {
	if x != nil {
		return x.ClientColor
	}
	return ""
}

func (x *BusinessCustomerCalendarView) GetReferralSourceId() int64 {
	if x != nil {
		return x.ReferralSourceId
	}
	return 0
}

func (x *BusinessCustomerCalendarView) GetSource() string {
	if x != nil {
		return x.Source
	}
	return ""
}

func (x *BusinessCustomerCalendarView) GetInactive() bool {
	if x != nil {
		return x.Inactive
	}
	return false
}

func (x *BusinessCustomerCalendarView) GetDeleted() bool {
	if x != nil {
		return x.Deleted
	}
	return false
}

func (x *BusinessCustomerCalendarView) GetIsBlockOnlineBooking() bool {
	if x != nil {
		return x.IsBlockOnlineBooking
	}
	return false
}

func (x *BusinessCustomerCalendarView) GetIsBlockMessage() bool {
	if x != nil {
		return x.IsBlockMessage
	}
	return false
}

func (x *BusinessCustomerCalendarView) GetSendAutoEmail() bool {
	if x != nil {
		return x.SendAutoEmail
	}
	return false
}

func (x *BusinessCustomerCalendarView) GetSendAutoMessage() bool {
	if x != nil {
		return x.SendAutoMessage
	}
	return false
}

func (x *BusinessCustomerCalendarView) GetUnconfirmedReminderBy() int32 {
	if x != nil {
		return x.UnconfirmedReminderBy
	}
	return 0
}

func (x *BusinessCustomerCalendarView) GetIsUnsubscribed() bool {
	if x != nil {
		return x.IsUnsubscribed
	}
	return false
}

func (x *BusinessCustomerCalendarView) GetIsRecurring() bool {
	if x != nil {
		return x.IsRecurring
	}
	return false
}

func (x *BusinessCustomerCalendarView) GetPreferredGroomerId() int64 {
	if x != nil {
		return x.PreferredGroomerId
	}
	return 0
}

func (x *BusinessCustomerCalendarView) GetPreferredGroomingFrequency() *v1.TimePeriod {
	if x != nil {
		return x.PreferredGroomingFrequency
	}
	return nil
}

func (x *BusinessCustomerCalendarView) GetPreferredDayOfWeek() []dayofweek.DayOfWeek {
	if x != nil {
		return x.PreferredDayOfWeek
	}
	return nil
}

func (x *BusinessCustomerCalendarView) GetPreferredTimeOfDay() *v1.TimeOfDayInterval {
	if x != nil {
		return x.PreferredTimeOfDay
	}
	return nil
}

func (x *BusinessCustomerCalendarView) GetSendAppAutoMessage() bool {
	if x != nil {
		return x.SendAppAutoMessage
	}
	return false
}

// business customer view for branded app account view
type BusinessCustomerBrandedAppView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// customer id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// the first name
	FirstName string `protobuf:"bytes,2,opt,name=first_name,json=firstName,proto3" json:"first_name,omitempty"`
	// the last name
	LastName string `protobuf:"bytes,3,opt,name=last_name,json=lastName,proto3" json:"last_name,omitempty"`
	// avatar path
	AvatarPath string `protobuf:"bytes,4,opt,name=avatar_path,json=avatarPath,proto3" json:"avatar_path,omitempty"`
	// email
	Email string `protobuf:"bytes,5,opt,name=email,proto3" json:"email,omitempty"`
	// phone number
	PhoneNumber string `protobuf:"bytes,6,opt,name=phone_number,json=phoneNumber,proto3" json:"phone_number,omitempty"`
}

func (x *BusinessCustomerBrandedAppView) Reset() {
	*x = BusinessCustomerBrandedAppView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_business_customer_v1_business_customer_models_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BusinessCustomerBrandedAppView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BusinessCustomerBrandedAppView) ProtoMessage() {}

func (x *BusinessCustomerBrandedAppView) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_business_customer_v1_business_customer_models_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BusinessCustomerBrandedAppView.ProtoReflect.Descriptor instead.
func (*BusinessCustomerBrandedAppView) Descriptor() ([]byte, []int) {
	return file_moego_models_business_customer_v1_business_customer_models_proto_rawDescGZIP(), []int{10}
}

func (x *BusinessCustomerBrandedAppView) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *BusinessCustomerBrandedAppView) GetFirstName() string {
	if x != nil {
		return x.FirstName
	}
	return ""
}

func (x *BusinessCustomerBrandedAppView) GetLastName() string {
	if x != nil {
		return x.LastName
	}
	return ""
}

func (x *BusinessCustomerBrandedAppView) GetAvatarPath() string {
	if x != nil {
		return x.AvatarPath
	}
	return ""
}

func (x *BusinessCustomerBrandedAppView) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *BusinessCustomerBrandedAppView) GetPhoneNumber() string {
	if x != nil {
		return x.PhoneNumber
	}
	return ""
}

var File_moego_models_business_customer_v1_business_customer_models_proto protoreflect.FileDescriptor

var file_moego_models_business_customer_v1_business_customer_models_proto_rawDesc = []byte{
	0x0a, 0x40, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x2f, 0x76, 0x31, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x21, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x65, 0x72, 0x2e, 0x76, 0x31, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x16, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74,
	0x79, 0x70, 0x65, 0x2f, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x64, 0x61, 0x79, 0x6f,
	0x66, 0x77, 0x65, 0x65, 0x6b, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x29, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2f, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x74, 0x69, 0x6d, 0x65,
	0x5f, 0x6f, 0x66, 0x5f, 0x64, 0x61, 0x79, 0x5f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x20, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x75, 0x74,
	0x69, 0x6c, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x70, 0x65, 0x72, 0x69,
	0x6f, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x9f, 0x0e, 0x0a, 0x15, 0x42, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x4d, 0x6f, 0x64,
	0x65, 0x6c, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02,
	0x69, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49,
	0x64, 0x12, 0x32, 0x0a, 0x15, 0x70, 0x72, 0x65, 0x66, 0x65, 0x72, 0x72, 0x65, 0x64, 0x5f, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x13, 0x70, 0x72, 0x65, 0x66, 0x65, 0x72, 0x72, 0x65, 0x64, 0x42, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x23, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x02, 0x18, 0x01, 0x52, 0x0a,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x63, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0c, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x12,
	0x1d, 0x0a, 0x0a, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x09, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x1f,
	0x0a, 0x0b, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x49, 0x64, 0x12,
	0x1d, 0x0a, 0x0a, 0x66, 0x69, 0x72, 0x73, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x66, 0x69, 0x72, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1b,
	0x0a, 0x09, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x6c, 0x61, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x61,
	0x76, 0x61, 0x74, 0x61, 0x72, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x50, 0x61, 0x74, 0x68, 0x12, 0x14, 0x0a, 0x05,
	0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x65, 0x6d, 0x61,
	0x69, 0x6c, 0x12, 0x21, 0x0a, 0x0c, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62,
	0x65, 0x72, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x4e,
	0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f,
	0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x6c, 0x69,
	0x65, 0x6e, 0x74, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x2c, 0x0a, 0x12, 0x72, 0x65, 0x66, 0x65,
	0x72, 0x72, 0x61, 0x6c, 0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x15,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x10, 0x72, 0x65, 0x66, 0x65, 0x72, 0x72, 0x61, 0x6c, 0x53, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x18, 0x16, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x1a,
	0x0a, 0x08, 0x69, 0x6e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x18, 0x17, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x08, 0x69, 0x6e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x64, 0x65,
	0x6c, 0x65, 0x74, 0x65, 0x64, 0x18, 0x18, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x64, 0x65, 0x6c,
	0x65, 0x74, 0x65, 0x64, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f,
	0x61, 0x74, 0x18, 0x19, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12,
	0x39, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x1a, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52,
	0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x18, 0x1b, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x42, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x18, 0x1c, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x75,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x42, 0x79, 0x12, 0x35, 0x0a, 0x17, 0x69, 0x73, 0x5f, 0x62,
	0x6c, 0x6f, 0x63, 0x6b, 0x5f, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b,
	0x69, 0x6e, 0x67, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x08, 0x52, 0x14, 0x69, 0x73, 0x42, 0x6c, 0x6f,
	0x63, 0x6b, 0x4f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x12,
	0x28, 0x0a, 0x10, 0x69, 0x73, 0x5f, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x5f, 0x6d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0e, 0x69, 0x73, 0x42, 0x6c, 0x6f,
	0x63, 0x6b, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x26, 0x0a, 0x0f, 0x73, 0x65, 0x6e,
	0x64, 0x5f, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x10, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x0d, 0x73, 0x65, 0x6e, 0x64, 0x41, 0x75, 0x74, 0x6f, 0x45, 0x6d, 0x61, 0x69,
	0x6c, 0x12, 0x2a, 0x0a, 0x11, 0x73, 0x65, 0x6e, 0x64, 0x5f, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x6d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x11, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0f, 0x73, 0x65,
	0x6e, 0x64, 0x41, 0x75, 0x74, 0x6f, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x36, 0x0a,
	0x17, 0x75, 0x6e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x65, 0x64, 0x5f, 0x72, 0x65, 0x6d,
	0x69, 0x6e, 0x64, 0x65, 0x72, 0x5f, 0x62, 0x79, 0x18, 0x12, 0x20, 0x01, 0x28, 0x05, 0x52, 0x15,
	0x75, 0x6e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x65, 0x64, 0x52, 0x65, 0x6d, 0x69, 0x6e,
	0x64, 0x65, 0x72, 0x42, 0x79, 0x12, 0x27, 0x0a, 0x0f, 0x69, 0x73, 0x5f, 0x75, 0x6e, 0x73, 0x75,
	0x62, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x64, 0x18, 0x13, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0e,
	0x69, 0x73, 0x55, 0x6e, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x64, 0x12, 0x21,
	0x0a, 0x0c, 0x69, 0x73, 0x5f, 0x72, 0x65, 0x63, 0x75, 0x72, 0x72, 0x69, 0x6e, 0x67, 0x18, 0x14,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x69, 0x73, 0x52, 0x65, 0x63, 0x75, 0x72, 0x72, 0x69, 0x6e,
	0x67, 0x12, 0x30, 0x0a, 0x14, 0x70, 0x72, 0x65, 0x66, 0x65, 0x72, 0x72, 0x65, 0x64, 0x5f, 0x67,
	0x72, 0x6f, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x1e, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x12, 0x70, 0x72, 0x65, 0x66, 0x65, 0x72, 0x72, 0x65, 0x64, 0x47, 0x72, 0x6f, 0x6f, 0x6d, 0x65,
	0x72, 0x49, 0x64, 0x12, 0x5c, 0x0a, 0x1c, 0x70, 0x72, 0x65, 0x66, 0x65, 0x72, 0x72, 0x65, 0x64,
	0x5f, 0x67, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x5f, 0x66, 0x72, 0x65, 0x71, 0x75, 0x65,
	0x6e, 0x63, 0x79, 0x18, 0x1f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x50,
	0x65, 0x72, 0x69, 0x6f, 0x64, 0x52, 0x1a, 0x70, 0x72, 0x65, 0x66, 0x65, 0x72, 0x72, 0x65, 0x64,
	0x47, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x46, 0x72, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63,
	0x79, 0x12, 0x49, 0x0a, 0x15, 0x70, 0x72, 0x65, 0x66, 0x65, 0x72, 0x72, 0x65, 0x64, 0x5f, 0x64,
	0x61, 0x79, 0x5f, 0x6f, 0x66, 0x5f, 0x77, 0x65, 0x65, 0x6b, 0x18, 0x20, 0x20, 0x03, 0x28, 0x0e,
	0x32, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44,
	0x61, 0x79, 0x4f, 0x66, 0x57, 0x65, 0x65, 0x6b, 0x52, 0x12, 0x70, 0x72, 0x65, 0x66, 0x65, 0x72,
	0x72, 0x65, 0x64, 0x44, 0x61, 0x79, 0x4f, 0x66, 0x57, 0x65, 0x65, 0x6b, 0x12, 0x54, 0x0a, 0x15,
	0x70, 0x72, 0x65, 0x66, 0x65, 0x72, 0x72, 0x65, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x6f,
	0x66, 0x5f, 0x64, 0x61, 0x79, 0x18, 0x21, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x69, 0x6d,
	0x65, 0x4f, 0x66, 0x44, 0x61, 0x79, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x52, 0x12,
	0x70, 0x72, 0x65, 0x66, 0x65, 0x72, 0x72, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x4f, 0x66, 0x44,
	0x61, 0x79, 0x12, 0x2a, 0x0a, 0x11, 0x73, 0x68, 0x61, 0x72, 0x65, 0x5f, 0x61, 0x70, 0x70, 0x74,
	0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x28, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0f, 0x73,
	0x68, 0x61, 0x72, 0x65, 0x41, 0x70, 0x70, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x28,
	0x0a, 0x10, 0x73, 0x68, 0x61, 0x72, 0x65, 0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x29, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0e, 0x73, 0x68, 0x61, 0x72, 0x65, 0x52,
	0x61, 0x6e, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x2a, 0x0a, 0x11, 0x73, 0x68, 0x61, 0x72,
	0x65, 0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x2a, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x0f, 0x73, 0x68, 0x61, 0x72, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x56,
	0x61, 0x6c, 0x75, 0x65, 0x12, 0x24, 0x0a, 0x0e, 0x73, 0x68, 0x61, 0x72, 0x65, 0x5f, 0x61, 0x70,
	0x70, 0x74, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x2b, 0x20, 0x03, 0x28, 0x03, 0x52, 0x0c, 0x73, 0x68,
	0x61, 0x72, 0x65, 0x41, 0x70, 0x70, 0x74, 0x49, 0x64, 0x73, 0x12, 0x42, 0x0a, 0x11, 0x6c, 0x61,
	0x73, 0x74, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18,
	0x2c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x48, 0x00, 0x52, 0x0f, 0x6c, 0x61, 0x73, 0x74,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x12, 0x31,
	0x0a, 0x15, 0x73, 0x65, 0x6e, 0x64, 0x5f, 0x61, 0x70, 0x70, 0x5f, 0x61, 0x75, 0x74, 0x6f, 0x5f,
	0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x2d, 0x20, 0x01, 0x28, 0x08, 0x52, 0x12, 0x73,
	0x65, 0x6e, 0x64, 0x41, 0x70, 0x70, 0x41, 0x75, 0x74, 0x6f, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x12, 0x36, 0x0a, 0x08, 0x62, 0x69, 0x72, 0x74, 0x68, 0x64, 0x61, 0x79, 0x18, 0x2e, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52,
	0x08, 0x62, 0x69, 0x72, 0x74, 0x68, 0x64, 0x61, 0x79, 0x12, 0x30, 0x0a, 0x14, 0x72, 0x65, 0x66,
	0x65, 0x72, 0x72, 0x61, 0x6c, 0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x64, 0x65, 0x73,
	0x63, 0x18, 0x2f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x72, 0x65, 0x66, 0x65, 0x72, 0x72, 0x61,
	0x6c, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x44, 0x65, 0x73, 0x63, 0x12, 0x23, 0x0a, 0x0d, 0x63,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x30, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0c, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65,
	0x3a, 0x02, 0x18, 0x01, 0x42, 0x14, 0x0a, 0x12, 0x5f, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x22, 0x87, 0x07, 0x0a, 0x19, 0x42,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49,
	0x6e, 0x66, 0x6f, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70,
	0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x6f,
	0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x32, 0x0a, 0x15, 0x70, 0x72, 0x65, 0x66, 0x65,
	0x72, 0x72, 0x65, 0x64, 0x5f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x13, 0x70, 0x72, 0x65, 0x66, 0x65, 0x72, 0x72, 0x65,
	0x64, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x5b, 0x0a, 0x06, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x43, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e,
	0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x49, 0x6e, 0x66, 0x6f, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x52, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x76, 0x61, 0x74,
	0x61, 0x72, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x61,
	0x76, 0x61, 0x74, 0x61, 0x72, 0x50, 0x61, 0x74, 0x68, 0x12, 0x1d, 0x0a, 0x0a, 0x66, 0x69, 0x72,
	0x73, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x66,
	0x69, 0x72, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x6c, 0x61, 0x73, 0x74,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6c, 0x61, 0x73,
	0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x21, 0x0a, 0x0c, 0x70,
	0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x09, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x21,
	0x0a, 0x0c, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x0a,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x43, 0x6f, 0x6c, 0x6f,
	0x72, 0x12, 0x23, 0x0a, 0x0d, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x63, 0x6f,
	0x64, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x65, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x5f, 0x69, 0x64, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x2c, 0x0a, 0x12, 0x72, 0x65, 0x66, 0x65, 0x72, 0x72, 0x61,
	0x6c, 0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x0d, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x10, 0x72, 0x65, 0x66, 0x65, 0x72, 0x72, 0x61, 0x6c, 0x53, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x74, 0x61, 0x67, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x0e,
	0x20, 0x03, 0x28, 0x03, 0x52, 0x06, 0x74, 0x61, 0x67, 0x49, 0x64, 0x73, 0x12, 0x1a, 0x0a, 0x08,
	0x69, 0x6e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08,
	0x69, 0x6e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x64, 0x65, 0x6c, 0x65,
	0x74, 0x65, 0x64, 0x18, 0x10, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x64, 0x65, 0x6c, 0x65, 0x74,
	0x65, 0x64, 0x22, 0xaf, 0x02, 0x0a, 0x06, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x16, 0x0a,
	0x12, 0x53, 0x4f, 0x55, 0x52, 0x43, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46,
	0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0b, 0x0a, 0x07, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e,
	0x10, 0x01, 0x12, 0x13, 0x0a, 0x0b, 0x53, 0x4f, 0x55, 0x52, 0x43, 0x45, 0x5f, 0x5a, 0x45, 0x52,
	0x4f, 0x10, 0x02, 0x1a, 0x02, 0x08, 0x01, 0x12, 0x12, 0x0a, 0x0a, 0x53, 0x4f, 0x55, 0x52, 0x43,
	0x45, 0x5f, 0x4f, 0x4e, 0x45, 0x10, 0x03, 0x1a, 0x02, 0x08, 0x01, 0x12, 0x12, 0x0a, 0x0a, 0x53,
	0x4f, 0x55, 0x52, 0x43, 0x45, 0x5f, 0x54, 0x57, 0x4f, 0x10, 0x04, 0x1a, 0x02, 0x08, 0x01, 0x12,
	0x14, 0x0a, 0x0c, 0x53, 0x4f, 0x55, 0x52, 0x43, 0x45, 0x5f, 0x54, 0x48, 0x52, 0x45, 0x45, 0x10,
	0x05, 0x1a, 0x02, 0x08, 0x01, 0x12, 0x13, 0x0a, 0x0b, 0x53, 0x4f, 0x55, 0x52, 0x43, 0x45, 0x5f,
	0x46, 0x4f, 0x55, 0x52, 0x10, 0x06, 0x1a, 0x02, 0x08, 0x01, 0x12, 0x13, 0x0a, 0x0b, 0x53, 0x4f,
	0x55, 0x52, 0x43, 0x45, 0x5f, 0x46, 0x49, 0x56, 0x45, 0x10, 0x07, 0x1a, 0x02, 0x08, 0x01, 0x12,
	0x11, 0x0a, 0x0d, 0x4d, 0x41, 0x4e, 0x55, 0x41, 0x4c, 0x5f, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45,
	0x10, 0x08, 0x12, 0x0f, 0x0a, 0x0b, 0x53, 0x45, 0x4c, 0x46, 0x5f, 0x49, 0x4d, 0x50, 0x4f, 0x52,
	0x54, 0x10, 0x09, 0x12, 0x0f, 0x0a, 0x0b, 0x44, 0x41, 0x54, 0x41, 0x5f, 0x49, 0x4d, 0x50, 0x4f,
	0x52, 0x54, 0x10, 0x0a, 0x12, 0x12, 0x0a, 0x0e, 0x4f, 0x4e, 0x4c, 0x49, 0x4e, 0x45, 0x5f, 0x42,
	0x4f, 0x4f, 0x4b, 0x49, 0x4e, 0x47, 0x10, 0x0b, 0x12, 0x0f, 0x0a, 0x0b, 0x49, 0x4e, 0x54, 0x41,
	0x4b, 0x45, 0x5f, 0x46, 0x4f, 0x52, 0x4d, 0x10, 0x0c, 0x12, 0x0b, 0x0a, 0x07, 0x43, 0x41, 0x4c,
	0x4c, 0x5f, 0x49, 0x4e, 0x10, 0x0d, 0x12, 0x0b, 0x0a, 0x07, 0x54, 0x45, 0x58, 0x54, 0x5f, 0x49,
	0x4e, 0x10, 0x0e, 0x12, 0x0f, 0x0a, 0x0b, 0x42, 0x52, 0x41, 0x4e, 0x44, 0x45, 0x44, 0x5f, 0x41,
	0x50, 0x50, 0x10, 0x0f, 0x22, 0xc7, 0x01, 0x0a, 0x1a, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x4d, 0x6f,
	0x64, 0x65, 0x6c, 0x12, 0x65, 0x0a, 0x09, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x47, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x45, 0x76, 0x65, 0x6e, 0x74,
	0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x09, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x22, 0x32, 0x0a, 0x09, 0x4f, 0x70,
	0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x19, 0x0a, 0x15, 0x4f, 0x50, 0x45, 0x52, 0x41,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44,
	0x10, 0x00, 0x12, 0x0a, 0x0a, 0x06, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x10, 0x01, 0x22, 0xe6,
	0x01, 0x0a, 0x1f, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x43, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x56, 0x69,
	0x65, 0x77, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02,
	0x69, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49,
	0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x66, 0x69, 0x72, 0x73, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x66, 0x69, 0x72, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x1b, 0x0a, 0x09, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x6c, 0x61, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1f, 0x0a,
	0x0b, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x0a, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0a, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x50, 0x61, 0x74, 0x68, 0x12, 0x14,
	0x0a, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x65,
	0x6d, 0x61, 0x69, 0x6c, 0x12, 0x21, 0x0a, 0x0c, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75,
	0x6d, 0x62, 0x65, 0x72, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x70, 0x68, 0x6f, 0x6e,
	0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x22, 0xbe, 0x01, 0x0a, 0x1f, 0x42, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x4d, 0x6f, 0x64, 0x65,
	0x6c, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x56, 0x69, 0x65, 0x77, 0x12, 0x35, 0x0a, 0x17, 0x69,
	0x73, 0x5f, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x5f, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62,
	0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x14, 0x69, 0x73,
	0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x4f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x69,
	0x6e, 0x67, 0x12, 0x28, 0x0a, 0x10, 0x69, 0x73, 0x5f, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x5f, 0x6d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0e, 0x69, 0x73,
	0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x1d, 0x0a, 0x0a,
	0x66, 0x69, 0x72, 0x73, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x66, 0x69, 0x72, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x6c,
	0x61, 0x73, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x6c, 0x61, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x5b, 0x0a, 0x1d, 0x42, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x4d, 0x6f, 0x64, 0x65,
	0x6c, 0x4c, 0x69, 0x6e, 0x6b, 0x56, 0x69, 0x65, 0x77, 0x12, 0x1d, 0x0a, 0x0a, 0x66, 0x69, 0x72,
	0x73, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x66,
	0x69, 0x72, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x6c, 0x61, 0x73, 0x74,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6c, 0x61, 0x73,
	0x74, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0xa5, 0x01, 0x0a, 0x1e, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x56, 0x69, 0x65, 0x77, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70,
	0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x6f,
	0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x66, 0x69, 0x72, 0x73, 0x74,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x66, 0x69, 0x72,
	0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6c, 0x61, 0x73, 0x74, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x22, 0x6b, 0x0a,
	0x1d, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x56, 0x69, 0x65, 0x77, 0x12, 0x0e,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1d,
	0x0a, 0x0a, 0x66, 0x69, 0x72, 0x73, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x66, 0x69, 0x72, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1b, 0x0a,
	0x09, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x6c, 0x61, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x80, 0x01, 0x0a, 0x1b, 0x42,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x4d,
	0x6f, 0x64, 0x65, 0x6c, 0x49, 0x64, 0x56, 0x69, 0x65, 0x77, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6f,
	0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09,
	0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x32, 0x0a, 0x15, 0x70, 0x72, 0x65,
	0x66, 0x65, 0x72, 0x72, 0x65, 0x64, 0x5f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f,
	0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x13, 0x70, 0x72, 0x65, 0x66, 0x65, 0x72,
	0x72, 0x65, 0x64, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x22, 0x80, 0x08,
	0x0a, 0x1c, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x65, 0x72, 0x43, 0x61, 0x6c, 0x65, 0x6e, 0x64, 0x61, 0x72, 0x56, 0x69, 0x65, 0x77, 0x12, 0x0e,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1d,
	0x0a, 0x0a, 0x66, 0x69, 0x72, 0x73, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x66, 0x69, 0x72, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1b, 0x0a,
	0x09, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x6c, 0x61, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x76,
	0x61, 0x74, 0x61, 0x72, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0a, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x50, 0x61, 0x74, 0x68, 0x12, 0x14, 0x0a, 0x05, 0x65,
	0x6d, 0x61, 0x69, 0x6c, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x65, 0x6d, 0x61, 0x69,
	0x6c, 0x12, 0x21, 0x0a, 0x0c, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65,
	0x72, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75,
	0x6d, 0x62, 0x65, 0x72, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x63,
	0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x6c, 0x69, 0x65,
	0x6e, 0x74, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x2c, 0x0a, 0x12, 0x72, 0x65, 0x66, 0x65, 0x72,
	0x72, 0x61, 0x6c, 0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x15, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x10, 0x72, 0x65, 0x66, 0x65, 0x72, 0x72, 0x61, 0x6c, 0x53, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18,
	0x16, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x1a, 0x0a,
	0x08, 0x69, 0x6e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x18, 0x17, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x08, 0x69, 0x6e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x64, 0x65, 0x6c,
	0x65, 0x74, 0x65, 0x64, 0x18, 0x18, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x64, 0x65, 0x6c, 0x65,
	0x74, 0x65, 0x64, 0x12, 0x35, 0x0a, 0x17, 0x69, 0x73, 0x5f, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x5f,
	0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x18, 0x0e,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x14, 0x69, 0x73, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x4f, 0x6e, 0x6c,
	0x69, 0x6e, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x12, 0x28, 0x0a, 0x10, 0x69, 0x73,
	0x5f, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x0f,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x0e, 0x69, 0x73, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x4d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x12, 0x26, 0x0a, 0x0f, 0x73, 0x65, 0x6e, 0x64, 0x5f, 0x61, 0x75, 0x74,
	0x6f, 0x5f, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x10, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0d, 0x73,
	0x65, 0x6e, 0x64, 0x41, 0x75, 0x74, 0x6f, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x2a, 0x0a, 0x11,
	0x73, 0x65, 0x6e, 0x64, 0x5f, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x18, 0x11, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0f, 0x73, 0x65, 0x6e, 0x64, 0x41, 0x75, 0x74,
	0x6f, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x36, 0x0a, 0x17, 0x75, 0x6e, 0x63, 0x6f,
	0x6e, 0x66, 0x69, 0x72, 0x6d, 0x65, 0x64, 0x5f, 0x72, 0x65, 0x6d, 0x69, 0x6e, 0x64, 0x65, 0x72,
	0x5f, 0x62, 0x79, 0x18, 0x12, 0x20, 0x01, 0x28, 0x05, 0x52, 0x15, 0x75, 0x6e, 0x63, 0x6f, 0x6e,
	0x66, 0x69, 0x72, 0x6d, 0x65, 0x64, 0x52, 0x65, 0x6d, 0x69, 0x6e, 0x64, 0x65, 0x72, 0x42, 0x79,
	0x12, 0x27, 0x0a, 0x0f, 0x69, 0x73, 0x5f, 0x75, 0x6e, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69,
	0x62, 0x65, 0x64, 0x18, 0x13, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0e, 0x69, 0x73, 0x55, 0x6e, 0x73,
	0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x69, 0x73, 0x5f,
	0x72, 0x65, 0x63, 0x75, 0x72, 0x72, 0x69, 0x6e, 0x67, 0x18, 0x14, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x0b, 0x69, 0x73, 0x52, 0x65, 0x63, 0x75, 0x72, 0x72, 0x69, 0x6e, 0x67, 0x12, 0x30, 0x0a, 0x14,
	0x70, 0x72, 0x65, 0x66, 0x65, 0x72, 0x72, 0x65, 0x64, 0x5f, 0x67, 0x72, 0x6f, 0x6f, 0x6d, 0x65,
	0x72, 0x5f, 0x69, 0x64, 0x18, 0x1e, 0x20, 0x01, 0x28, 0x03, 0x52, 0x12, 0x70, 0x72, 0x65, 0x66,
	0x65, 0x72, 0x72, 0x65, 0x64, 0x47, 0x72, 0x6f, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64, 0x12, 0x5c,
	0x0a, 0x1c, 0x70, 0x72, 0x65, 0x66, 0x65, 0x72, 0x72, 0x65, 0x64, 0x5f, 0x67, 0x72, 0x6f, 0x6f,
	0x6d, 0x69, 0x6e, 0x67, 0x5f, 0x66, 0x72, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x79, 0x18, 0x1f,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69,
	0x6c, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x50, 0x65, 0x72, 0x69, 0x6f, 0x64,
	0x52, 0x1a, 0x70, 0x72, 0x65, 0x66, 0x65, 0x72, 0x72, 0x65, 0x64, 0x47, 0x72, 0x6f, 0x6f, 0x6d,
	0x69, 0x6e, 0x67, 0x46, 0x72, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x79, 0x12, 0x49, 0x0a, 0x15,
	0x70, 0x72, 0x65, 0x66, 0x65, 0x72, 0x72, 0x65, 0x64, 0x5f, 0x64, 0x61, 0x79, 0x5f, 0x6f, 0x66,
	0x5f, 0x77, 0x65, 0x65, 0x6b, 0x18, 0x20, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x79, 0x4f, 0x66, 0x57,
	0x65, 0x65, 0x6b, 0x52, 0x12, 0x70, 0x72, 0x65, 0x66, 0x65, 0x72, 0x72, 0x65, 0x64, 0x44, 0x61,
	0x79, 0x4f, 0x66, 0x57, 0x65, 0x65, 0x6b, 0x12, 0x54, 0x0a, 0x15, 0x70, 0x72, 0x65, 0x66, 0x65,
	0x72, 0x72, 0x65, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x6f, 0x66, 0x5f, 0x64, 0x61, 0x79,
	0x18, 0x21, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75,
	0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x4f, 0x66, 0x44, 0x61,
	0x79, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x52, 0x12, 0x70, 0x72, 0x65, 0x66, 0x65,
	0x72, 0x72, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x4f, 0x66, 0x44, 0x61, 0x79, 0x12, 0x31, 0x0a,
	0x15, 0x73, 0x65, 0x6e, 0x64, 0x5f, 0x61, 0x70, 0x70, 0x5f, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x6d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x22, 0x20, 0x01, 0x28, 0x08, 0x52, 0x12, 0x73, 0x65,
	0x6e, 0x64, 0x41, 0x70, 0x70, 0x41, 0x75, 0x74, 0x6f, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x22, 0xc6, 0x01, 0x0a, 0x1e, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x43, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x42, 0x72, 0x61, 0x6e, 0x64, 0x65, 0x64, 0x41, 0x70, 0x70, 0x56,
	0x69, 0x65, 0x77, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x02, 0x69, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x66, 0x69, 0x72, 0x73, 0x74, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x66, 0x69, 0x72, 0x73, 0x74, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6c, 0x61, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x1f, 0x0a, 0x0b, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x50, 0x61, 0x74, 0x68,
	0x12, 0x14, 0x0a, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x21, 0x0a, 0x0c, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f,
	0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x70, 0x68,
	0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x42, 0x98, 0x01, 0x0a, 0x29, 0x63, 0x6f,
	0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x69, 0x67, 0x69, 0x74, 0x68, 0x75,
	0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61,
	0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66,
	0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x62, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2f, 0x76,
	0x31, 0x3b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x65, 0x72, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_business_customer_v1_business_customer_models_proto_rawDescOnce sync.Once
	file_moego_models_business_customer_v1_business_customer_models_proto_rawDescData = file_moego_models_business_customer_v1_business_customer_models_proto_rawDesc
)

func file_moego_models_business_customer_v1_business_customer_models_proto_rawDescGZIP() []byte {
	file_moego_models_business_customer_v1_business_customer_models_proto_rawDescOnce.Do(func() {
		file_moego_models_business_customer_v1_business_customer_models_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_business_customer_v1_business_customer_models_proto_rawDescData)
	})
	return file_moego_models_business_customer_v1_business_customer_models_proto_rawDescData
}

var file_moego_models_business_customer_v1_business_customer_models_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_moego_models_business_customer_v1_business_customer_models_proto_msgTypes = make([]protoimpl.MessageInfo, 11)
var file_moego_models_business_customer_v1_business_customer_models_proto_goTypes = []interface{}{
	(BusinessCustomerInfoModel_Source)(0),     // 0: moego.models.business_customer.v1.BusinessCustomerInfoModel.Source
	(BusinessCustomerEventModel_Operation)(0), // 1: moego.models.business_customer.v1.BusinessCustomerEventModel.Operation
	(*BusinessCustomerModel)(nil),             // 2: moego.models.business_customer.v1.BusinessCustomerModel
	(*BusinessCustomerInfoModel)(nil),         // 3: moego.models.business_customer.v1.BusinessCustomerInfoModel
	(*BusinessCustomerEventModel)(nil),        // 4: moego.models.business_customer.v1.BusinessCustomerEventModel
	(*BusinessCustomerModelPublicView)(nil),   // 5: moego.models.business_customer.v1.BusinessCustomerModelPublicView
	(*BusinessCustomerModelClientView)(nil),   // 6: moego.models.business_customer.v1.BusinessCustomerModelClientView
	(*BusinessCustomerModelLinkView)(nil),     // 7: moego.models.business_customer.v1.BusinessCustomerModelLinkView
	(*BusinessCustomerNameStatusView)(nil),    // 8: moego.models.business_customer.v1.BusinessCustomerNameStatusView
	(*BusinessCustomerModelNameView)(nil),     // 9: moego.models.business_customer.v1.BusinessCustomerModelNameView
	(*BusinessCustomerModelIdView)(nil),       // 10: moego.models.business_customer.v1.BusinessCustomerModelIdView
	(*BusinessCustomerCalendarView)(nil),      // 11: moego.models.business_customer.v1.BusinessCustomerCalendarView
	(*BusinessCustomerBrandedAppView)(nil),    // 12: moego.models.business_customer.v1.BusinessCustomerBrandedAppView
	(*timestamppb.Timestamp)(nil),             // 13: google.protobuf.Timestamp
	(*v1.TimePeriod)(nil),                     // 14: moego.utils.v1.TimePeriod
	(dayofweek.DayOfWeek)(0),                  // 15: google.type.DayOfWeek
	(*v1.TimeOfDayInterval)(nil),              // 16: moego.utils.v1.TimeOfDayInterval
	(*date.Date)(nil),                         // 17: google.type.Date
}
var file_moego_models_business_customer_v1_business_customer_models_proto_depIdxs = []int32{
	13, // 0: moego.models.business_customer.v1.BusinessCustomerModel.created_at:type_name -> google.protobuf.Timestamp
	13, // 1: moego.models.business_customer.v1.BusinessCustomerModel.updated_at:type_name -> google.protobuf.Timestamp
	14, // 2: moego.models.business_customer.v1.BusinessCustomerModel.preferred_grooming_frequency:type_name -> moego.utils.v1.TimePeriod
	15, // 3: moego.models.business_customer.v1.BusinessCustomerModel.preferred_day_of_week:type_name -> google.type.DayOfWeek
	16, // 4: moego.models.business_customer.v1.BusinessCustomerModel.preferred_time_of_day:type_name -> moego.utils.v1.TimeOfDayInterval
	17, // 5: moego.models.business_customer.v1.BusinessCustomerModel.last_service_time:type_name -> google.type.Date
	13, // 6: moego.models.business_customer.v1.BusinessCustomerModel.birthday:type_name -> google.protobuf.Timestamp
	0,  // 7: moego.models.business_customer.v1.BusinessCustomerInfoModel.source:type_name -> moego.models.business_customer.v1.BusinessCustomerInfoModel.Source
	1,  // 8: moego.models.business_customer.v1.BusinessCustomerEventModel.operation:type_name -> moego.models.business_customer.v1.BusinessCustomerEventModel.Operation
	14, // 9: moego.models.business_customer.v1.BusinessCustomerCalendarView.preferred_grooming_frequency:type_name -> moego.utils.v1.TimePeriod
	15, // 10: moego.models.business_customer.v1.BusinessCustomerCalendarView.preferred_day_of_week:type_name -> google.type.DayOfWeek
	16, // 11: moego.models.business_customer.v1.BusinessCustomerCalendarView.preferred_time_of_day:type_name -> moego.utils.v1.TimeOfDayInterval
	12, // [12:12] is the sub-list for method output_type
	12, // [12:12] is the sub-list for method input_type
	12, // [12:12] is the sub-list for extension type_name
	12, // [12:12] is the sub-list for extension extendee
	0,  // [0:12] is the sub-list for field type_name
}

func init() { file_moego_models_business_customer_v1_business_customer_models_proto_init() }
func file_moego_models_business_customer_v1_business_customer_models_proto_init() {
	if File_moego_models_business_customer_v1_business_customer_models_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_models_business_customer_v1_business_customer_models_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BusinessCustomerModel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_business_customer_v1_business_customer_models_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BusinessCustomerInfoModel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_business_customer_v1_business_customer_models_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BusinessCustomerEventModel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_business_customer_v1_business_customer_models_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BusinessCustomerModelPublicView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_business_customer_v1_business_customer_models_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BusinessCustomerModelClientView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_business_customer_v1_business_customer_models_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BusinessCustomerModelLinkView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_business_customer_v1_business_customer_models_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BusinessCustomerNameStatusView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_business_customer_v1_business_customer_models_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BusinessCustomerModelNameView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_business_customer_v1_business_customer_models_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BusinessCustomerModelIdView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_business_customer_v1_business_customer_models_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BusinessCustomerCalendarView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_business_customer_v1_business_customer_models_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BusinessCustomerBrandedAppView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_models_business_customer_v1_business_customer_models_proto_msgTypes[0].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_business_customer_v1_business_customer_models_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   11,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_business_customer_v1_business_customer_models_proto_goTypes,
		DependencyIndexes: file_moego_models_business_customer_v1_business_customer_models_proto_depIdxs,
		EnumInfos:         file_moego_models_business_customer_v1_business_customer_models_proto_enumTypes,
		MessageInfos:      file_moego_models_business_customer_v1_business_customer_models_proto_msgTypes,
	}.Build()
	File_moego_models_business_customer_v1_business_customer_models_proto = out.File
	file_moego_models_business_customer_v1_business_customer_models_proto_rawDesc = nil
	file_moego_models_business_customer_v1_business_customer_models_proto_goTypes = nil
	file_moego_models_business_customer_v1_business_customer_models_proto_depIdxs = nil
}
