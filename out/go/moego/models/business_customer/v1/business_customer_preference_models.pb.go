// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/business_customer/v1/business_customer_preference_models.proto

package businesscustomerpb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v1"
	dayofweek "google.golang.org/genproto/googleapis/type/dayofweek"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Auto tipping type
type BusinessCustomerPaymentPreferenceModel_AutoTippingType int32

const (
	// Unspecified
	BusinessCustomerPaymentPreferenceModel_AUTO_TIPPING_TYPE_UNSPECIFIED BusinessCustomerPaymentPreferenceModel_AutoTippingType = 0
	// By percentage
	BusinessCustomerPaymentPreferenceModel_BY_PERCENTAGE BusinessCustomerPaymentPreferenceModel_AutoTippingType = 1
	// By amount
	BusinessCustomerPaymentPreferenceModel_BY_AMOUNT BusinessCustomerPaymentPreferenceModel_AutoTippingType = 2
)

// Enum value maps for BusinessCustomerPaymentPreferenceModel_AutoTippingType.
var (
	BusinessCustomerPaymentPreferenceModel_AutoTippingType_name = map[int32]string{
		0: "AUTO_TIPPING_TYPE_UNSPECIFIED",
		1: "BY_PERCENTAGE",
		2: "BY_AMOUNT",
	}
	BusinessCustomerPaymentPreferenceModel_AutoTippingType_value = map[string]int32{
		"AUTO_TIPPING_TYPE_UNSPECIFIED": 0,
		"BY_PERCENTAGE":                 1,
		"BY_AMOUNT":                     2,
	}
)

func (x BusinessCustomerPaymentPreferenceModel_AutoTippingType) Enum() *BusinessCustomerPaymentPreferenceModel_AutoTippingType {
	p := new(BusinessCustomerPaymentPreferenceModel_AutoTippingType)
	*p = x
	return p
}

func (x BusinessCustomerPaymentPreferenceModel_AutoTippingType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BusinessCustomerPaymentPreferenceModel_AutoTippingType) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_business_customer_v1_business_customer_preference_models_proto_enumTypes[0].Descriptor()
}

func (BusinessCustomerPaymentPreferenceModel_AutoTippingType) Type() protoreflect.EnumType {
	return &file_moego_models_business_customer_v1_business_customer_preference_models_proto_enumTypes[0]
}

func (x BusinessCustomerPaymentPreferenceModel_AutoTippingType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use BusinessCustomerPaymentPreferenceModel_AutoTippingType.Descriptor instead.
func (BusinessCustomerPaymentPreferenceModel_AutoTippingType) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_business_customer_v1_business_customer_preference_models_proto_rawDescGZIP(), []int{2, 0}
}

// Communication preference for a business customer
// 沟通相关的设置项, 涉及到是否接受短信, 邮件, 电话, 是否屏蔽某个用户的消息
type BusinessCustomerCommunicationPreferenceModel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// If block message from this customer
	BlockMessage bool `protobuf:"varint,1,opt,name=block_message,json=blockMessage,proto3" json:"block_message,omitempty"`
	// Enable auto message - SMS
	EnableAutoMessageSms bool `protobuf:"varint,2,opt,name=enable_auto_message_sms,json=enableAutoMessageSms,proto3" json:"enable_auto_message_sms,omitempty"`
	// Enable auto message - email
	EnableAutoMessageEmail bool `protobuf:"varint,3,opt,name=enable_auto_message_email,json=enableAutoMessageEmail,proto3" json:"enable_auto_message_email,omitempty"`
	// Enable auto message - call  = 4?
	// Enable auto message - app
	EnableAutoMessageApp bool `protobuf:"varint,11,opt,name=enable_auto_message_app,json=enableAutoMessageApp,proto3" json:"enable_auto_message_app,omitempty"`
	// Enable appointment reminder - SMS
	EnableAppointmentReminderSms bool `protobuf:"varint,5,opt,name=enable_appointment_reminder_sms,json=enableAppointmentReminderSms,proto3" json:"enable_appointment_reminder_sms,omitempty"`
	// Enable appointment reminder - email
	EnableAppointmentReminderEmail bool `protobuf:"varint,6,opt,name=enable_appointment_reminder_email,json=enableAppointmentReminderEmail,proto3" json:"enable_appointment_reminder_email,omitempty"`
	// Enable appointment reminder - call
	EnableAppointmentReminderCall bool `protobuf:"varint,7,opt,name=enable_appointment_reminder_call,json=enableAppointmentReminderCall,proto3" json:"enable_appointment_reminder_call,omitempty"`
	// Enable appointment reminder - app
	EnableAppointmentReminderApp bool `protobuf:"varint,12,opt,name=enable_appointment_reminder_app,json=enableAppointmentReminderApp,proto3" json:"enable_appointment_reminder_app,omitempty"`
	// Enable marketing - email
	EnableMarketingEmail bool `protobuf:"varint,8,opt,name=enable_marketing_email,json=enableMarketingEmail,proto3" json:"enable_marketing_email,omitempty"` // Enable marketing - SMS / call = 9 / 10?
}

func (x *BusinessCustomerCommunicationPreferenceModel) Reset() {
	*x = BusinessCustomerCommunicationPreferenceModel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_business_customer_v1_business_customer_preference_models_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BusinessCustomerCommunicationPreferenceModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BusinessCustomerCommunicationPreferenceModel) ProtoMessage() {}

func (x *BusinessCustomerCommunicationPreferenceModel) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_business_customer_v1_business_customer_preference_models_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BusinessCustomerCommunicationPreferenceModel.ProtoReflect.Descriptor instead.
func (*BusinessCustomerCommunicationPreferenceModel) Descriptor() ([]byte, []int) {
	return file_moego_models_business_customer_v1_business_customer_preference_models_proto_rawDescGZIP(), []int{0}
}

func (x *BusinessCustomerCommunicationPreferenceModel) GetBlockMessage() bool {
	if x != nil {
		return x.BlockMessage
	}
	return false
}

func (x *BusinessCustomerCommunicationPreferenceModel) GetEnableAutoMessageSms() bool {
	if x != nil {
		return x.EnableAutoMessageSms
	}
	return false
}

func (x *BusinessCustomerCommunicationPreferenceModel) GetEnableAutoMessageEmail() bool {
	if x != nil {
		return x.EnableAutoMessageEmail
	}
	return false
}

func (x *BusinessCustomerCommunicationPreferenceModel) GetEnableAutoMessageApp() bool {
	if x != nil {
		return x.EnableAutoMessageApp
	}
	return false
}

func (x *BusinessCustomerCommunicationPreferenceModel) GetEnableAppointmentReminderSms() bool {
	if x != nil {
		return x.EnableAppointmentReminderSms
	}
	return false
}

func (x *BusinessCustomerCommunicationPreferenceModel) GetEnableAppointmentReminderEmail() bool {
	if x != nil {
		return x.EnableAppointmentReminderEmail
	}
	return false
}

func (x *BusinessCustomerCommunicationPreferenceModel) GetEnableAppointmentReminderCall() bool {
	if x != nil {
		return x.EnableAppointmentReminderCall
	}
	return false
}

func (x *BusinessCustomerCommunicationPreferenceModel) GetEnableAppointmentReminderApp() bool {
	if x != nil {
		return x.EnableAppointmentReminderApp
	}
	return false
}

func (x *BusinessCustomerCommunicationPreferenceModel) GetEnableMarketingEmail() bool {
	if x != nil {
		return x.EnableMarketingEmail
	}
	return false
}

// Appointment preference for a business customer
// 预约相关的设置项, 涉及到是否有指定的美容师, 预约频率, 预约时间等
type BusinessCustomerAppointmentPreferenceModel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Preferred groomer id. If not set, this customer does not have a preferred groomer
	PreferredGroomerId *int64 `protobuf:"varint,1,opt,name=preferred_groomer_id,json=preferredGroomerId,proto3,oneof" json:"preferred_groomer_id,omitempty"`
	// Preferred grooming frequency
	PreferredGroomingFrequency *v1.TimePeriod `protobuf:"bytes,2,opt,name=preferred_grooming_frequency,json=preferredGroomingFrequency,proto3" json:"preferred_grooming_frequency,omitempty"`
	// Preferred day of week. If empty, no preferred day of week
	PreferredDayOfWeek []dayofweek.DayOfWeek `protobuf:"varint,3,rep,packed,name=preferred_day_of_week,json=preferredDayOfWeek,proto3,enum=google.type.DayOfWeek" json:"preferred_day_of_week,omitempty"`
	// Preferred time of day
	PreferredTimeOfDay *v1.TimeOfDayInterval `protobuf:"bytes,4,opt,name=preferred_time_of_day,json=preferredTimeOfDay,proto3" json:"preferred_time_of_day,omitempty"`
}

func (x *BusinessCustomerAppointmentPreferenceModel) Reset() {
	*x = BusinessCustomerAppointmentPreferenceModel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_business_customer_v1_business_customer_preference_models_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BusinessCustomerAppointmentPreferenceModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BusinessCustomerAppointmentPreferenceModel) ProtoMessage() {}

func (x *BusinessCustomerAppointmentPreferenceModel) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_business_customer_v1_business_customer_preference_models_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BusinessCustomerAppointmentPreferenceModel.ProtoReflect.Descriptor instead.
func (*BusinessCustomerAppointmentPreferenceModel) Descriptor() ([]byte, []int) {
	return file_moego_models_business_customer_v1_business_customer_preference_models_proto_rawDescGZIP(), []int{1}
}

func (x *BusinessCustomerAppointmentPreferenceModel) GetPreferredGroomerId() int64 {
	if x != nil && x.PreferredGroomerId != nil {
		return *x.PreferredGroomerId
	}
	return 0
}

func (x *BusinessCustomerAppointmentPreferenceModel) GetPreferredGroomingFrequency() *v1.TimePeriod {
	if x != nil {
		return x.PreferredGroomingFrequency
	}
	return nil
}

func (x *BusinessCustomerAppointmentPreferenceModel) GetPreferredDayOfWeek() []dayofweek.DayOfWeek {
	if x != nil {
		return x.PreferredDayOfWeek
	}
	return nil
}

func (x *BusinessCustomerAppointmentPreferenceModel) GetPreferredTimeOfDay() *v1.TimeOfDayInterval {
	if x != nil {
		return x.PreferredTimeOfDay
	}
	return nil
}

// Payment preference for a business customer
// 支付相关的设置项, 涉及到是否开启自动小费, 小费类型, 小费金额等
type BusinessCustomerPaymentPreferenceModel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Enable auto tipping
	EnableAutoTipping bool `protobuf:"varint,1,opt,name=enable_auto_tipping,json=enableAutoTipping,proto3" json:"enable_auto_tipping,omitempty"`
	// Auto tipping type
	AutoTippingType BusinessCustomerPaymentPreferenceModel_AutoTippingType `protobuf:"varint,2,opt,name=auto_tipping_type,json=autoTippingType,proto3,enum=moego.models.business_customer.v1.BusinessCustomerPaymentPreferenceModel_AutoTippingType" json:"auto_tipping_type,omitempty"`
	// Auto tipping percentage, range from 1 to 100.
	AutoTippingPercentage int32 `protobuf:"varint,3,opt,name=auto_tipping_percentage,json=autoTippingPercentage,proto3" json:"auto_tipping_percentage,omitempty"`
	// Auto tipping amount, should be greater than or equal to 0, and keep at most two decimals. e.g. 1, 1.1, 1.01.
	AutoTippingAmount float64 `protobuf:"fixed64,4,opt,name=auto_tipping_amount,json=autoTippingAmount,proto3" json:"auto_tipping_amount,omitempty"`
}

func (x *BusinessCustomerPaymentPreferenceModel) Reset() {
	*x = BusinessCustomerPaymentPreferenceModel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_business_customer_v1_business_customer_preference_models_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BusinessCustomerPaymentPreferenceModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BusinessCustomerPaymentPreferenceModel) ProtoMessage() {}

func (x *BusinessCustomerPaymentPreferenceModel) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_business_customer_v1_business_customer_preference_models_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BusinessCustomerPaymentPreferenceModel.ProtoReflect.Descriptor instead.
func (*BusinessCustomerPaymentPreferenceModel) Descriptor() ([]byte, []int) {
	return file_moego_models_business_customer_v1_business_customer_preference_models_proto_rawDescGZIP(), []int{2}
}

func (x *BusinessCustomerPaymentPreferenceModel) GetEnableAutoTipping() bool {
	if x != nil {
		return x.EnableAutoTipping
	}
	return false
}

func (x *BusinessCustomerPaymentPreferenceModel) GetAutoTippingType() BusinessCustomerPaymentPreferenceModel_AutoTippingType {
	if x != nil {
		return x.AutoTippingType
	}
	return BusinessCustomerPaymentPreferenceModel_AUTO_TIPPING_TYPE_UNSPECIFIED
}

func (x *BusinessCustomerPaymentPreferenceModel) GetAutoTippingPercentage() int32 {
	if x != nil {
		return x.AutoTippingPercentage
	}
	return 0
}

func (x *BusinessCustomerPaymentPreferenceModel) GetAutoTippingAmount() float64 {
	if x != nil {
		return x.AutoTippingAmount
	}
	return 0
}

var File_moego_models_business_customer_v1_business_customer_preference_models_proto protoreflect.FileDescriptor

var file_moego_models_business_customer_v1_business_customer_preference_models_proto_rawDesc = []byte{
	0x0a, 0x4b, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x2f, 0x76, 0x31, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65,
	0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x21, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31,
	0x1a, 0x1b, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x64, 0x61,
	0x79, 0x6f, 0x66, 0x77, 0x65, 0x65, 0x6b, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x29, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x74, 0x69,
	0x6d, 0x65, 0x5f, 0x6f, 0x66, 0x5f, 0x64, 0x61, 0x79, 0x5f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x76,
	0x61, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x20, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f,
	0x75, 0x74, 0x69, 0x6c, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x70, 0x65,
	0x72, 0x69, 0x6f, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xd4, 0x04, 0x0a, 0x2c, 0x42,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x43,
	0x6f, 0x6d, 0x6d, 0x75, 0x6e, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x72, 0x65, 0x66,
	0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x23, 0x0a, 0x0d, 0x62,
	0x6c, 0x6f, 0x63, 0x6b, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x0c, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x12, 0x35, 0x0a, 0x17, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x61, 0x75, 0x74, 0x6f, 0x5f,
	0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x6d, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x14, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x41, 0x75, 0x74, 0x6f, 0x4d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x53, 0x6d, 0x73, 0x12, 0x39, 0x0a, 0x19, 0x65, 0x6e, 0x61, 0x62, 0x6c,
	0x65, 0x5f, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x5f, 0x65,
	0x6d, 0x61, 0x69, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x16, 0x65, 0x6e, 0x61, 0x62,
	0x6c, 0x65, 0x41, 0x75, 0x74, 0x6f, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x45, 0x6d, 0x61,
	0x69, 0x6c, 0x12, 0x35, 0x0a, 0x17, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x61, 0x75, 0x74,
	0x6f, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x5f, 0x61, 0x70, 0x70, 0x18, 0x0b, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x14, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x41, 0x75, 0x74, 0x6f, 0x4d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x41, 0x70, 0x70, 0x12, 0x45, 0x0a, 0x1f, 0x65, 0x6e, 0x61,
	0x62, 0x6c, 0x65, 0x5f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f,
	0x72, 0x65, 0x6d, 0x69, 0x6e, 0x64, 0x65, 0x72, 0x5f, 0x73, 0x6d, 0x73, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x1c, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x6d, 0x69, 0x6e, 0x64, 0x65, 0x72, 0x53, 0x6d, 0x73,
	0x12, 0x49, 0x0a, 0x21, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x61, 0x70, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x72, 0x65, 0x6d, 0x69, 0x6e, 0x64, 0x65, 0x72, 0x5f,
	0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x52, 0x1e, 0x65, 0x6e, 0x61,
	0x62, 0x6c, 0x65, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65,
	0x6d, 0x69, 0x6e, 0x64, 0x65, 0x72, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x47, 0x0a, 0x20, 0x65,
	0x6e, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x5f, 0x72, 0x65, 0x6d, 0x69, 0x6e, 0x64, 0x65, 0x72, 0x5f, 0x63, 0x61, 0x6c, 0x6c, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x08, 0x52, 0x1d, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x41, 0x70, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x6d, 0x69, 0x6e, 0x64, 0x65, 0x72,
	0x43, 0x61, 0x6c, 0x6c, 0x12, 0x45, 0x0a, 0x1f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x61,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x72, 0x65, 0x6d, 0x69, 0x6e,
	0x64, 0x65, 0x72, 0x5f, 0x61, 0x70, 0x70, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x08, 0x52, 0x1c, 0x65,
	0x6e, 0x61, 0x62, 0x6c, 0x65, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x52, 0x65, 0x6d, 0x69, 0x6e, 0x64, 0x65, 0x72, 0x41, 0x70, 0x70, 0x12, 0x34, 0x0a, 0x16, 0x65,
	0x6e, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x69, 0x6e, 0x67, 0x5f,
	0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x08, 0x20, 0x01, 0x28, 0x08, 0x52, 0x14, 0x65, 0x6e, 0x61,
	0x62, 0x6c, 0x65, 0x4d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x69, 0x6e, 0x67, 0x45, 0x6d, 0x61, 0x69,
	0x6c, 0x22, 0xfb, 0x02, 0x0a, 0x2a, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x43, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x50, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c,
	0x12, 0x35, 0x0a, 0x14, 0x70, 0x72, 0x65, 0x66, 0x65, 0x72, 0x72, 0x65, 0x64, 0x5f, 0x67, 0x72,
	0x6f, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x48, 0x00,
	0x52, 0x12, 0x70, 0x72, 0x65, 0x66, 0x65, 0x72, 0x72, 0x65, 0x64, 0x47, 0x72, 0x6f, 0x6f, 0x6d,
	0x65, 0x72, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x5c, 0x0a, 0x1c, 0x70, 0x72, 0x65, 0x66, 0x65,
	0x72, 0x72, 0x65, 0x64, 0x5f, 0x67, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x5f, 0x66, 0x72,
	0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x54,
	0x69, 0x6d, 0x65, 0x50, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x52, 0x1a, 0x70, 0x72, 0x65, 0x66, 0x65,
	0x72, 0x72, 0x65, 0x64, 0x47, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x46, 0x72, 0x65, 0x71,
	0x75, 0x65, 0x6e, 0x63, 0x79, 0x12, 0x49, 0x0a, 0x15, 0x70, 0x72, 0x65, 0x66, 0x65, 0x72, 0x72,
	0x65, 0x64, 0x5f, 0x64, 0x61, 0x79, 0x5f, 0x6f, 0x66, 0x5f, 0x77, 0x65, 0x65, 0x6b, 0x18, 0x03,
	0x20, 0x03, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x2e, 0x44, 0x61, 0x79, 0x4f, 0x66, 0x57, 0x65, 0x65, 0x6b, 0x52, 0x12, 0x70, 0x72,
	0x65, 0x66, 0x65, 0x72, 0x72, 0x65, 0x64, 0x44, 0x61, 0x79, 0x4f, 0x66, 0x57, 0x65, 0x65, 0x6b,
	0x12, 0x54, 0x0a, 0x15, 0x70, 0x72, 0x65, 0x66, 0x65, 0x72, 0x72, 0x65, 0x64, 0x5f, 0x74, 0x69,
	0x6d, 0x65, 0x5f, 0x6f, 0x66, 0x5f, 0x64, 0x61, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x21, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x31,
	0x2e, 0x54, 0x69, 0x6d, 0x65, 0x4f, 0x66, 0x44, 0x61, 0x79, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x76,
	0x61, 0x6c, 0x52, 0x12, 0x70, 0x72, 0x65, 0x66, 0x65, 0x72, 0x72, 0x65, 0x64, 0x54, 0x69, 0x6d,
	0x65, 0x4f, 0x66, 0x44, 0x61, 0x79, 0x42, 0x17, 0x0a, 0x15, 0x5f, 0x70, 0x72, 0x65, 0x66, 0x65,
	0x72, 0x72, 0x65, 0x64, 0x5f, 0x67, 0x72, 0x6f, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x22,
	0xa0, 0x03, 0x0a, 0x26, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x43, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x50, 0x72, 0x65, 0x66, 0x65,
	0x72, 0x65, 0x6e, 0x63, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x2e, 0x0a, 0x13, 0x65, 0x6e,
	0x61, 0x62, 0x6c, 0x65, 0x5f, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x74, 0x69, 0x70, 0x70, 0x69, 0x6e,
	0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x11, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x41,
	0x75, 0x74, 0x6f, 0x54, 0x69, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x12, 0x85, 0x01, 0x0a, 0x11, 0x61,
	0x75, 0x74, 0x6f, 0x5f, 0x74, 0x69, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x59, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x50, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x50, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x4d, 0x6f, 0x64, 0x65,
	0x6c, 0x2e, 0x41, 0x75, 0x74, 0x6f, 0x54, 0x69, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70,
	0x65, 0x52, 0x0f, 0x61, 0x75, 0x74, 0x6f, 0x54, 0x69, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x36, 0x0a, 0x17, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x74, 0x69, 0x70, 0x70, 0x69,
	0x6e, 0x67, 0x5f, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x61, 0x67, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x15, 0x61, 0x75, 0x74, 0x6f, 0x54, 0x69, 0x70, 0x70, 0x69, 0x6e, 0x67,
	0x50, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x61, 0x67, 0x65, 0x12, 0x2e, 0x0a, 0x13, 0x61, 0x75,
	0x74, 0x6f, 0x5f, 0x74, 0x69, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e,
	0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x01, 0x52, 0x11, 0x61, 0x75, 0x74, 0x6f, 0x54, 0x69, 0x70,
	0x70, 0x69, 0x6e, 0x67, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0x56, 0x0a, 0x0f, 0x41, 0x75,
	0x74, 0x6f, 0x54, 0x69, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70, 0x65, 0x12, 0x21, 0x0a,
	0x1d, 0x41, 0x55, 0x54, 0x4f, 0x5f, 0x54, 0x49, 0x50, 0x50, 0x49, 0x4e, 0x47, 0x5f, 0x54, 0x59,
	0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00,
	0x12, 0x11, 0x0a, 0x0d, 0x42, 0x59, 0x5f, 0x50, 0x45, 0x52, 0x43, 0x45, 0x4e, 0x54, 0x41, 0x47,
	0x45, 0x10, 0x01, 0x12, 0x0d, 0x0a, 0x09, 0x42, 0x59, 0x5f, 0x41, 0x4d, 0x4f, 0x55, 0x4e, 0x54,
	0x10, 0x02, 0x42, 0x98, 0x01, 0x0a, 0x29, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31,
	0x50, 0x01, 0x5a, 0x69, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d,
	0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x3b, 0x62, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x70, 0x62, 0x62, 0x06, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_business_customer_v1_business_customer_preference_models_proto_rawDescOnce sync.Once
	file_moego_models_business_customer_v1_business_customer_preference_models_proto_rawDescData = file_moego_models_business_customer_v1_business_customer_preference_models_proto_rawDesc
)

func file_moego_models_business_customer_v1_business_customer_preference_models_proto_rawDescGZIP() []byte {
	file_moego_models_business_customer_v1_business_customer_preference_models_proto_rawDescOnce.Do(func() {
		file_moego_models_business_customer_v1_business_customer_preference_models_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_business_customer_v1_business_customer_preference_models_proto_rawDescData)
	})
	return file_moego_models_business_customer_v1_business_customer_preference_models_proto_rawDescData
}

var file_moego_models_business_customer_v1_business_customer_preference_models_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_moego_models_business_customer_v1_business_customer_preference_models_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_moego_models_business_customer_v1_business_customer_preference_models_proto_goTypes = []interface{}{
	(BusinessCustomerPaymentPreferenceModel_AutoTippingType)(0), // 0: moego.models.business_customer.v1.BusinessCustomerPaymentPreferenceModel.AutoTippingType
	(*BusinessCustomerCommunicationPreferenceModel)(nil),        // 1: moego.models.business_customer.v1.BusinessCustomerCommunicationPreferenceModel
	(*BusinessCustomerAppointmentPreferenceModel)(nil),          // 2: moego.models.business_customer.v1.BusinessCustomerAppointmentPreferenceModel
	(*BusinessCustomerPaymentPreferenceModel)(nil),              // 3: moego.models.business_customer.v1.BusinessCustomerPaymentPreferenceModel
	(*v1.TimePeriod)(nil),                                       // 4: moego.utils.v1.TimePeriod
	(dayofweek.DayOfWeek)(0),                                    // 5: google.type.DayOfWeek
	(*v1.TimeOfDayInterval)(nil),                                // 6: moego.utils.v1.TimeOfDayInterval
}
var file_moego_models_business_customer_v1_business_customer_preference_models_proto_depIdxs = []int32{
	4, // 0: moego.models.business_customer.v1.BusinessCustomerAppointmentPreferenceModel.preferred_grooming_frequency:type_name -> moego.utils.v1.TimePeriod
	5, // 1: moego.models.business_customer.v1.BusinessCustomerAppointmentPreferenceModel.preferred_day_of_week:type_name -> google.type.DayOfWeek
	6, // 2: moego.models.business_customer.v1.BusinessCustomerAppointmentPreferenceModel.preferred_time_of_day:type_name -> moego.utils.v1.TimeOfDayInterval
	0, // 3: moego.models.business_customer.v1.BusinessCustomerPaymentPreferenceModel.auto_tipping_type:type_name -> moego.models.business_customer.v1.BusinessCustomerPaymentPreferenceModel.AutoTippingType
	4, // [4:4] is the sub-list for method output_type
	4, // [4:4] is the sub-list for method input_type
	4, // [4:4] is the sub-list for extension type_name
	4, // [4:4] is the sub-list for extension extendee
	0, // [0:4] is the sub-list for field type_name
}

func init() { file_moego_models_business_customer_v1_business_customer_preference_models_proto_init() }
func file_moego_models_business_customer_v1_business_customer_preference_models_proto_init() {
	if File_moego_models_business_customer_v1_business_customer_preference_models_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_models_business_customer_v1_business_customer_preference_models_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BusinessCustomerCommunicationPreferenceModel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_business_customer_v1_business_customer_preference_models_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BusinessCustomerAppointmentPreferenceModel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_business_customer_v1_business_customer_preference_models_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BusinessCustomerPaymentPreferenceModel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_models_business_customer_v1_business_customer_preference_models_proto_msgTypes[1].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_business_customer_v1_business_customer_preference_models_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_business_customer_v1_business_customer_preference_models_proto_goTypes,
		DependencyIndexes: file_moego_models_business_customer_v1_business_customer_preference_models_proto_depIdxs,
		EnumInfos:         file_moego_models_business_customer_v1_business_customer_preference_models_proto_enumTypes,
		MessageInfos:      file_moego_models_business_customer_v1_business_customer_preference_models_proto_msgTypes,
	}.Build()
	File_moego_models_business_customer_v1_business_customer_preference_models_proto = out.File
	file_moego_models_business_customer_v1_business_customer_preference_models_proto_rawDesc = nil
	file_moego_models_business_customer_v1_business_customer_preference_models_proto_goTypes = nil
	file_moego_models_business_customer_v1_business_customer_preference_models_proto_depIdxs = nil
}
