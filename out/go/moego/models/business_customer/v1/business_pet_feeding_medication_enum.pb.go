// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/business_customer/v1/business_pet_feeding_medication_enum.proto

package businesscustomerpb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// feeding medication schedule date type
type FeedingMedicationScheduleDateType int32

const (
	// unspecified
	FeedingMedicationScheduleDateType_FEEDING_MEDICATION_SCHEDULE_DATE_TYPE_UNSPECIFIED FeedingMedicationScheduleDateType = 0
	// Every day except for checkout day
	FeedingMedicationScheduleDateType_EVERYDAY_EXCEPT_CHECKOUT_DATE FeedingMedicationScheduleDateType = 1
	// Every day include checkout day
	FeedingMedicationScheduleDateType_EVERYDAY_INCLUDE_CHECKOUT_DATE FeedingMedicationScheduleDateType = 2
	// Specific date
	FeedingMedicationScheduleDateType_SPECIFIC_DATE FeedingMedicationScheduleDateType = 3
)

// Enum value maps for FeedingMedicationScheduleDateType.
var (
	FeedingMedicationScheduleDateType_name = map[int32]string{
		0: "FEEDING_MEDICATION_SCHEDULE_DATE_TYPE_UNSPECIFIED",
		1: "EVERYDAY_EXCEPT_CHECKOUT_DATE",
		2: "EVERYDAY_INCLUDE_CHECKOUT_DATE",
		3: "SPECIFIC_DATE",
	}
	FeedingMedicationScheduleDateType_value = map[string]int32{
		"FEEDING_MEDICATION_SCHEDULE_DATE_TYPE_UNSPECIFIED": 0,
		"EVERYDAY_EXCEPT_CHECKOUT_DATE":                     1,
		"EVERYDAY_INCLUDE_CHECKOUT_DATE":                    2,
		"SPECIFIC_DATE":                                     3,
	}
)

func (x FeedingMedicationScheduleDateType) Enum() *FeedingMedicationScheduleDateType {
	p := new(FeedingMedicationScheduleDateType)
	*p = x
	return p
}

func (x FeedingMedicationScheduleDateType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (FeedingMedicationScheduleDateType) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_business_customer_v1_business_pet_feeding_medication_enum_proto_enumTypes[0].Descriptor()
}

func (FeedingMedicationScheduleDateType) Type() protoreflect.EnumType {
	return &file_moego_models_business_customer_v1_business_pet_feeding_medication_enum_proto_enumTypes[0]
}

func (x FeedingMedicationScheduleDateType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use FeedingMedicationScheduleDateType.Descriptor instead.
func (FeedingMedicationScheduleDateType) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_business_customer_v1_business_pet_feeding_medication_enum_proto_rawDescGZIP(), []int{0}
}

var File_moego_models_business_customer_v1_business_pet_feeding_medication_enum_proto protoreflect.FileDescriptor

var file_moego_models_business_customer_v1_business_pet_feeding_medication_enum_proto_rawDesc = []byte{
	0x0a, 0x4c, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x2f, 0x76, 0x31, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x70, 0x65, 0x74,
	0x5f, 0x66, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x6d, 0x65, 0x64, 0x69, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x21,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76,
	0x31, 0x2a, 0xb4, 0x01, 0x0a, 0x21, 0x46, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x4d, 0x65, 0x64,
	0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x44,
	0x61, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x35, 0x0a, 0x31, 0x46, 0x45, 0x45, 0x44, 0x49,
	0x4e, 0x47, 0x5f, 0x4d, 0x45, 0x44, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x43,
	0x48, 0x45, 0x44, 0x55, 0x4c, 0x45, 0x5f, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x21,
	0x0a, 0x1d, 0x45, 0x56, 0x45, 0x52, 0x59, 0x44, 0x41, 0x59, 0x5f, 0x45, 0x58, 0x43, 0x45, 0x50,
	0x54, 0x5f, 0x43, 0x48, 0x45, 0x43, 0x4b, 0x4f, 0x55, 0x54, 0x5f, 0x44, 0x41, 0x54, 0x45, 0x10,
	0x01, 0x12, 0x22, 0x0a, 0x1e, 0x45, 0x56, 0x45, 0x52, 0x59, 0x44, 0x41, 0x59, 0x5f, 0x49, 0x4e,
	0x43, 0x4c, 0x55, 0x44, 0x45, 0x5f, 0x43, 0x48, 0x45, 0x43, 0x4b, 0x4f, 0x55, 0x54, 0x5f, 0x44,
	0x41, 0x54, 0x45, 0x10, 0x02, 0x12, 0x11, 0x0a, 0x0d, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49,
	0x43, 0x5f, 0x44, 0x41, 0x54, 0x45, 0x10, 0x03, 0x42, 0x98, 0x01, 0x0a, 0x29, 0x63, 0x6f, 0x6d,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x69, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62,
	0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72,
	0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69,
	0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x62, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2f, 0x76, 0x31,
	0x3b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_business_customer_v1_business_pet_feeding_medication_enum_proto_rawDescOnce sync.Once
	file_moego_models_business_customer_v1_business_pet_feeding_medication_enum_proto_rawDescData = file_moego_models_business_customer_v1_business_pet_feeding_medication_enum_proto_rawDesc
)

func file_moego_models_business_customer_v1_business_pet_feeding_medication_enum_proto_rawDescGZIP() []byte {
	file_moego_models_business_customer_v1_business_pet_feeding_medication_enum_proto_rawDescOnce.Do(func() {
		file_moego_models_business_customer_v1_business_pet_feeding_medication_enum_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_business_customer_v1_business_pet_feeding_medication_enum_proto_rawDescData)
	})
	return file_moego_models_business_customer_v1_business_pet_feeding_medication_enum_proto_rawDescData
}

var file_moego_models_business_customer_v1_business_pet_feeding_medication_enum_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_moego_models_business_customer_v1_business_pet_feeding_medication_enum_proto_goTypes = []interface{}{
	(FeedingMedicationScheduleDateType)(0), // 0: moego.models.business_customer.v1.FeedingMedicationScheduleDateType
}
var file_moego_models_business_customer_v1_business_pet_feeding_medication_enum_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_moego_models_business_customer_v1_business_pet_feeding_medication_enum_proto_init() }
func file_moego_models_business_customer_v1_business_pet_feeding_medication_enum_proto_init() {
	if File_moego_models_business_customer_v1_business_pet_feeding_medication_enum_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_business_customer_v1_business_pet_feeding_medication_enum_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_business_customer_v1_business_pet_feeding_medication_enum_proto_goTypes,
		DependencyIndexes: file_moego_models_business_customer_v1_business_pet_feeding_medication_enum_proto_depIdxs,
		EnumInfos:         file_moego_models_business_customer_v1_business_pet_feeding_medication_enum_proto_enumTypes,
	}.Build()
	File_moego_models_business_customer_v1_business_pet_feeding_medication_enum_proto = out.File
	file_moego_models_business_customer_v1_business_pet_feeding_medication_enum_proto_rawDesc = nil
	file_moego_models_business_customer_v1_business_pet_feeding_medication_enum_proto_goTypes = nil
	file_moego_models_business_customer_v1_business_pet_feeding_medication_enum_proto_depIdxs = nil
}
