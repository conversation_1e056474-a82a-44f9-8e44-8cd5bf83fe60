// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/finance_gw/v1/webhook_models.proto

package financegwpb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Request for HandleEvent. Ideally, most HTTP data should be passed in this request.
type HttpWebhookEvent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The path of the HTTP event.
	Path string `protobuf:"bytes,1,opt,name=path,proto3" json:"path,omitempty"`
	// headers
	Headers map[string]*HttpWebhookEvent_HeaderValues `protobuf:"bytes,2,rep,name=headers,proto3" json:"headers,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// The raw HTTP event body.
	EventBody []byte `protobuf:"bytes,3,opt,name=event_body,json=eventBody,proto3" json:"event_body,omitempty"`
}

func (x *HttpWebhookEvent) Reset() {
	*x = HttpWebhookEvent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_finance_gw_v1_webhook_models_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HttpWebhookEvent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HttpWebhookEvent) ProtoMessage() {}

func (x *HttpWebhookEvent) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_finance_gw_v1_webhook_models_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HttpWebhookEvent.ProtoReflect.Descriptor instead.
func (*HttpWebhookEvent) Descriptor() ([]byte, []int) {
	return file_moego_models_finance_gw_v1_webhook_models_proto_rawDescGZIP(), []int{0}
}

func (x *HttpWebhookEvent) GetPath() string {
	if x != nil {
		return x.Path
	}
	return ""
}

func (x *HttpWebhookEvent) GetHeaders() map[string]*HttpWebhookEvent_HeaderValues {
	if x != nil {
		return x.Headers
	}
	return nil
}

func (x *HttpWebhookEvent) GetEventBody() []byte {
	if x != nil {
		return x.EventBody
	}
	return nil
}

// A list of values for a header key.
type HttpWebhookEvent_HeaderValues struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The values
	Values []string `protobuf:"bytes,1,rep,name=values,proto3" json:"values,omitempty"`
}

func (x *HttpWebhookEvent_HeaderValues) Reset() {
	*x = HttpWebhookEvent_HeaderValues{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_finance_gw_v1_webhook_models_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HttpWebhookEvent_HeaderValues) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HttpWebhookEvent_HeaderValues) ProtoMessage() {}

func (x *HttpWebhookEvent_HeaderValues) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_finance_gw_v1_webhook_models_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HttpWebhookEvent_HeaderValues.ProtoReflect.Descriptor instead.
func (*HttpWebhookEvent_HeaderValues) Descriptor() ([]byte, []int) {
	return file_moego_models_finance_gw_v1_webhook_models_proto_rawDescGZIP(), []int{0, 0}
}

func (x *HttpWebhookEvent_HeaderValues) GetValues() []string {
	if x != nil {
		return x.Values
	}
	return nil
}

var File_moego_models_finance_gw_v1_webhook_models_proto protoreflect.FileDescriptor

var file_moego_models_finance_gw_v1_webhook_models_proto_rawDesc = []byte{
	0x0a, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x66,
	0x69, 0x6e, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x67, 0x77, 0x2f, 0x76, 0x31, 0x2f, 0x77, 0x65, 0x62,
	0x68, 0x6f, 0x6f, 0x6b, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x1a, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x66, 0x69, 0x6e, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x67, 0x77, 0x2e, 0x76, 0x31, 0x22, 0xb9, 0x02,
	0x0a, 0x10, 0x48, 0x74, 0x74, 0x70, 0x57, 0x65, 0x62, 0x68, 0x6f, 0x6f, 0x6b, 0x45, 0x76, 0x65,
	0x6e, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x74, 0x68, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x70, 0x61, 0x74, 0x68, 0x12, 0x53, 0x0a, 0x07, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x39, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x66, 0x69, 0x6e, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x67,
	0x77, 0x2e, 0x76, 0x31, 0x2e, 0x48, 0x74, 0x74, 0x70, 0x57, 0x65, 0x62, 0x68, 0x6f, 0x6f, 0x6b,
	0x45, 0x76, 0x65, 0x6e, 0x74, 0x2e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x52, 0x07, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x65,
	0x76, 0x65, 0x6e, 0x74, 0x5f, 0x62, 0x6f, 0x64, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0c, 0x52,
	0x09, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x42, 0x6f, 0x64, 0x79, 0x1a, 0x26, 0x0a, 0x0c, 0x48, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x06, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x73, 0x1a, 0x75, 0x0a, 0x0c, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x6b, 0x65, 0x79, 0x12, 0x4f, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x39, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x66, 0x69, 0x6e, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x67, 0x77, 0x2e, 0x76, 0x31,
	0x2e, 0x48, 0x74, 0x74, 0x70, 0x57, 0x65, 0x62, 0x68, 0x6f, 0x6f, 0x6b, 0x45, 0x76, 0x65, 0x6e,
	0x74, 0x2e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x52, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x42, 0x83, 0x01, 0x0a, 0x22, 0x63, 0x6f,
	0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x66, 0x69, 0x6e, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x67, 0x77, 0x2e, 0x76, 0x31,
	0x50, 0x01, 0x5a, 0x5b, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d,
	0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x66, 0x69, 0x6e, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x67, 0x77,
	0x2f, 0x76, 0x31, 0x3b, 0x66, 0x69, 0x6e, 0x61, 0x6e, 0x63, 0x65, 0x67, 0x77, 0x70, 0x62, 0x62,
	0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_finance_gw_v1_webhook_models_proto_rawDescOnce sync.Once
	file_moego_models_finance_gw_v1_webhook_models_proto_rawDescData = file_moego_models_finance_gw_v1_webhook_models_proto_rawDesc
)

func file_moego_models_finance_gw_v1_webhook_models_proto_rawDescGZIP() []byte {
	file_moego_models_finance_gw_v1_webhook_models_proto_rawDescOnce.Do(func() {
		file_moego_models_finance_gw_v1_webhook_models_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_finance_gw_v1_webhook_models_proto_rawDescData)
	})
	return file_moego_models_finance_gw_v1_webhook_models_proto_rawDescData
}

var file_moego_models_finance_gw_v1_webhook_models_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_moego_models_finance_gw_v1_webhook_models_proto_goTypes = []interface{}{
	(*HttpWebhookEvent)(nil),              // 0: moego.models.finance_gw.v1.HttpWebhookEvent
	(*HttpWebhookEvent_HeaderValues)(nil), // 1: moego.models.finance_gw.v1.HttpWebhookEvent.HeaderValues
	nil,                                   // 2: moego.models.finance_gw.v1.HttpWebhookEvent.HeadersEntry
}
var file_moego_models_finance_gw_v1_webhook_models_proto_depIdxs = []int32{
	2, // 0: moego.models.finance_gw.v1.HttpWebhookEvent.headers:type_name -> moego.models.finance_gw.v1.HttpWebhookEvent.HeadersEntry
	1, // 1: moego.models.finance_gw.v1.HttpWebhookEvent.HeadersEntry.value:type_name -> moego.models.finance_gw.v1.HttpWebhookEvent.HeaderValues
	2, // [2:2] is the sub-list for method output_type
	2, // [2:2] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_moego_models_finance_gw_v1_webhook_models_proto_init() }
func file_moego_models_finance_gw_v1_webhook_models_proto_init() {
	if File_moego_models_finance_gw_v1_webhook_models_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_models_finance_gw_v1_webhook_models_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HttpWebhookEvent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_finance_gw_v1_webhook_models_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HttpWebhookEvent_HeaderValues); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_finance_gw_v1_webhook_models_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_finance_gw_v1_webhook_models_proto_goTypes,
		DependencyIndexes: file_moego_models_finance_gw_v1_webhook_models_proto_depIdxs,
		MessageInfos:      file_moego_models_finance_gw_v1_webhook_models_proto_msgTypes,
	}.Build()
	File_moego_models_finance_gw_v1_webhook_models_proto = out.File
	file_moego_models_finance_gw_v1_webhook_models_proto_rawDesc = nil
	file_moego_models_finance_gw_v1_webhook_models_proto_goTypes = nil
	file_moego_models_finance_gw_v1_webhook_models_proto_depIdxs = nil
}
