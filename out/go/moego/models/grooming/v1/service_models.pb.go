// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/grooming/v1/service_models.proto

package groomingpb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// the grooming service model
type ServiceModel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// service id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// business id
	BusinessId int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// category id
	CategoryId int64 `protobuf:"varint,3,opt,name=category_id,json=categoryId,proto3" json:"category_id,omitempty"`
	// service name
	Name string `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`
	// service description
	Description string `protobuf:"bytes,5,opt,name=description,proto3" json:"description,omitempty"`
	// service type
	Type ServiceType `protobuf:"varint,6,opt,name=type,proto3,enum=moego.models.grooming.v1.ServiceType" json:"type,omitempty"`
	// tax id
	TaxId int64 `protobuf:"varint,7,opt,name=tax_id,json=taxId,proto3" json:"tax_id,omitempty"`
	// service price
	Price float64 `protobuf:"fixed64,8,opt,name=price,proto3" json:"price,omitempty"`
	// service duration, in minutes
	Duration int32 `protobuf:"varint,9,opt,name=duration,proto3" json:"duration,omitempty"`
	// is inactive
	IsInactive bool `protobuf:"varint,10,opt,name=is_inactive,json=isInactive,proto3" json:"is_inactive,omitempty"`
	// sort number
	Sort int32 `protobuf:"varint,11,opt,name=sort,proto3" json:"sort,omitempty"`
	// color code
	ColorCode string `protobuf:"bytes,12,opt,name=color_code,json=colorCode,proto3" json:"color_code,omitempty"`
	// is deleted
	IsDeleted bool `protobuf:"varint,13,opt,name=is_deleted,json=isDeleted,proto3" json:"is_deleted,omitempty"`
	// create time
	CreateTime *timestamppb.Timestamp `protobuf:"bytes,14,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	// update time
	UpdateTime *timestamppb.Timestamp `protobuf:"bytes,15,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	// show base price
	ShowBasePrice ShowBasePrice `protobuf:"varint,16,opt,name=show_base_price,json=showBasePrice,proto3,enum=moego.models.grooming.v1.ShowBasePrice" json:"show_base_price,omitempty"`
	// book online enable
	IsBookOnlineAvailable bool `protobuf:"varint,17,opt,name=is_book_online_available,json=isBookOnlineAvailable,proto3" json:"is_book_online_available,omitempty"`
	// available for all staff
	IsAllStaff bool `protobuf:"varint,18,opt,name=is_all_staff,json=isAllStaff,proto3" json:"is_all_staff,omitempty"`
	// pet breed filter switch
	IsBreedFilter bool `protobuf:"varint,19,opt,name=is_breed_filter,json=isBreedFilter,proto3" json:"is_breed_filter,omitempty"`
	// pet weight filter switch
	IsWeightFilter bool `protobuf:"varint,20,opt,name=is_weight_filter,json=isWeightFilter,proto3" json:"is_weight_filter,omitempty"`
	// pet weight down limit
	WeightDownLimit float64 `protobuf:"fixed64,21,opt,name=weight_down_limit,json=weightDownLimit,proto3" json:"weight_down_limit,omitempty"`
	// pet weight up limit
	WeightUpLimit float64 `protobuf:"fixed64,22,opt,name=weight_up_limit,json=weightUpLimit,proto3" json:"weight_up_limit,omitempty"`
	// pet coat type filter
	IsCoatTypeFilter bool `protobuf:"varint,23,opt,name=is_coat_type_filter,json=isCoatTypeFilter,proto3" json:"is_coat_type_filter,omitempty"`
}

func (x *ServiceModel) Reset() {
	*x = ServiceModel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_grooming_v1_service_models_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ServiceModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceModel) ProtoMessage() {}

func (x *ServiceModel) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_grooming_v1_service_models_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceModel.ProtoReflect.Descriptor instead.
func (*ServiceModel) Descriptor() ([]byte, []int) {
	return file_moego_models_grooming_v1_service_models_proto_rawDescGZIP(), []int{0}
}

func (x *ServiceModel) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ServiceModel) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *ServiceModel) GetCategoryId() int64 {
	if x != nil {
		return x.CategoryId
	}
	return 0
}

func (x *ServiceModel) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ServiceModel) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *ServiceModel) GetType() ServiceType {
	if x != nil {
		return x.Type
	}
	return ServiceType_SERVICE_TYPE_UNSPECIFIED
}

func (x *ServiceModel) GetTaxId() int64 {
	if x != nil {
		return x.TaxId
	}
	return 0
}

func (x *ServiceModel) GetPrice() float64 {
	if x != nil {
		return x.Price
	}
	return 0
}

func (x *ServiceModel) GetDuration() int32 {
	if x != nil {
		return x.Duration
	}
	return 0
}

func (x *ServiceModel) GetIsInactive() bool {
	if x != nil {
		return x.IsInactive
	}
	return false
}

func (x *ServiceModel) GetSort() int32 {
	if x != nil {
		return x.Sort
	}
	return 0
}

func (x *ServiceModel) GetColorCode() string {
	if x != nil {
		return x.ColorCode
	}
	return ""
}

func (x *ServiceModel) GetIsDeleted() bool {
	if x != nil {
		return x.IsDeleted
	}
	return false
}

func (x *ServiceModel) GetCreateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

func (x *ServiceModel) GetUpdateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdateTime
	}
	return nil
}

func (x *ServiceModel) GetShowBasePrice() ShowBasePrice {
	if x != nil {
		return x.ShowBasePrice
	}
	return ShowBasePrice_SHOW_BASE_PRICE_NO
}

func (x *ServiceModel) GetIsBookOnlineAvailable() bool {
	if x != nil {
		return x.IsBookOnlineAvailable
	}
	return false
}

func (x *ServiceModel) GetIsAllStaff() bool {
	if x != nil {
		return x.IsAllStaff
	}
	return false
}

func (x *ServiceModel) GetIsBreedFilter() bool {
	if x != nil {
		return x.IsBreedFilter
	}
	return false
}

func (x *ServiceModel) GetIsWeightFilter() bool {
	if x != nil {
		return x.IsWeightFilter
	}
	return false
}

func (x *ServiceModel) GetWeightDownLimit() float64 {
	if x != nil {
		return x.WeightDownLimit
	}
	return 0
}

func (x *ServiceModel) GetWeightUpLimit() float64 {
	if x != nil {
		return x.WeightUpLimit
	}
	return 0
}

func (x *ServiceModel) GetIsCoatTypeFilter() bool {
	if x != nil {
		return x.IsCoatTypeFilter
	}
	return false
}

// the grooming service in c app appt detail view
type ServiceModelClientView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// service id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// service name
	Name string `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`
	// service type
	Type ServiceType `protobuf:"varint,6,opt,name=type,proto3,enum=moego.models.grooming.v1.ServiceType" json:"type,omitempty"`
	// is inactive
	IsInactive bool `protobuf:"varint,10,opt,name=is_inactive,json=isInactive,proto3" json:"is_inactive,omitempty"`
	// is deleted
	IsDeleted bool `protobuf:"varint,13,opt,name=is_deleted,json=isDeleted,proto3" json:"is_deleted,omitempty"`
	// book online enable
	IsBookOnlineAvailable bool `protobuf:"varint,17,opt,name=is_book_online_available,json=isBookOnlineAvailable,proto3" json:"is_book_online_available,omitempty"`
}

func (x *ServiceModelClientView) Reset() {
	*x = ServiceModelClientView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_grooming_v1_service_models_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ServiceModelClientView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceModelClientView) ProtoMessage() {}

func (x *ServiceModelClientView) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_grooming_v1_service_models_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceModelClientView.ProtoReflect.Descriptor instead.
func (*ServiceModelClientView) Descriptor() ([]byte, []int) {
	return file_moego_models_grooming_v1_service_models_proto_rawDescGZIP(), []int{1}
}

func (x *ServiceModelClientView) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ServiceModelClientView) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ServiceModelClientView) GetType() ServiceType {
	if x != nil {
		return x.Type
	}
	return ServiceType_SERVICE_TYPE_UNSPECIFIED
}

func (x *ServiceModelClientView) GetIsInactive() bool {
	if x != nil {
		return x.IsInactive
	}
	return false
}

func (x *ServiceModelClientView) GetIsDeleted() bool {
	if x != nil {
		return x.IsDeleted
	}
	return false
}

func (x *ServiceModelClientView) GetIsBookOnlineAvailable() bool {
	if x != nil {
		return x.IsBookOnlineAvailable
	}
	return false
}

// the grooming service in c app select service view
type ServiceModelSelectServiceView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// service id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// service name
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// service type
	Type ServiceType `protobuf:"varint,3,opt,name=type,proto3,enum=moego.models.grooming.v1.ServiceType" json:"type,omitempty"`
	// service price
	Price float64 `protobuf:"fixed64,4,opt,name=price,proto3" json:"price,omitempty"`
	// service duration, in minutes
	Duration int32 `protobuf:"varint,5,opt,name=duration,proto3" json:"duration,omitempty"`
	// sort number
	Sort int32 `protobuf:"varint,6,opt,name=sort,proto3" json:"sort,omitempty"`
	// show base price
	ShowBasePrice ShowBasePrice `protobuf:"varint,7,opt,name=show_base_price,json=showBasePrice,proto3,enum=moego.models.grooming.v1.ShowBasePrice" json:"show_base_price,omitempty"`
	// category id
	CategoryId int64 `protobuf:"varint,8,opt,name=category_id,json=categoryId,proto3" json:"category_id,omitempty"`
	// service description
	Description string `protobuf:"bytes,9,opt,name=description,proto3" json:"description,omitempty"`
}

func (x *ServiceModelSelectServiceView) Reset() {
	*x = ServiceModelSelectServiceView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_grooming_v1_service_models_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ServiceModelSelectServiceView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceModelSelectServiceView) ProtoMessage() {}

func (x *ServiceModelSelectServiceView) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_grooming_v1_service_models_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceModelSelectServiceView.ProtoReflect.Descriptor instead.
func (*ServiceModelSelectServiceView) Descriptor() ([]byte, []int) {
	return file_moego_models_grooming_v1_service_models_proto_rawDescGZIP(), []int{2}
}

func (x *ServiceModelSelectServiceView) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ServiceModelSelectServiceView) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ServiceModelSelectServiceView) GetType() ServiceType {
	if x != nil {
		return x.Type
	}
	return ServiceType_SERVICE_TYPE_UNSPECIFIED
}

func (x *ServiceModelSelectServiceView) GetPrice() float64 {
	if x != nil {
		return x.Price
	}
	return 0
}

func (x *ServiceModelSelectServiceView) GetDuration() int32 {
	if x != nil {
		return x.Duration
	}
	return 0
}

func (x *ServiceModelSelectServiceView) GetSort() int32 {
	if x != nil {
		return x.Sort
	}
	return 0
}

func (x *ServiceModelSelectServiceView) GetShowBasePrice() ShowBasePrice {
	if x != nil {
		return x.ShowBasePrice
	}
	return ShowBasePrice_SHOW_BASE_PRICE_NO
}

func (x *ServiceModelSelectServiceView) GetCategoryId() int64 {
	if x != nil {
		return x.CategoryId
	}
	return 0
}

func (x *ServiceModelSelectServiceView) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

var File_moego_models_grooming_v1_service_models_proto protoreflect.FileDescriptor

var file_moego_models_grooming_v1_service_models_proto_rawDesc = []byte{
	0x0a, 0x2d, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x67,
	0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12,
	0x18, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x67, 0x72,
	0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2c, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x67, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e,
	0x67, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x65, 0x6e, 0x75,
	0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x88, 0x07, 0x0a, 0x0c, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x62, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x61,
	0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x0a, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12,
	0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x39, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x25, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x67,
	0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x15, 0x0a, 0x06,
	0x74, 0x61, 0x78, 0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x74, 0x61,
	0x78, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x01, 0x52, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x75, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x09, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x64, 0x75, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1f, 0x0a, 0x0b, 0x69, 0x73, 0x5f, 0x69, 0x6e, 0x61, 0x63,
	0x74, 0x69, 0x76, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x69, 0x73, 0x49, 0x6e,
	0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x6f, 0x72, 0x74, 0x18, 0x0b,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x73, 0x6f, 0x72, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6f,
	0x6c, 0x6f, 0x72, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x69, 0x73, 0x5f,
	0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x69,
	0x73, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x12, 0x3b, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x3b, 0x0a, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f,
	0x74, 0x69, 0x6d, 0x65, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69,
	0x6d, 0x65, 0x12, 0x4f, 0x0a, 0x0f, 0x73, 0x68, 0x6f, 0x77, 0x5f, 0x62, 0x61, 0x73, 0x65, 0x5f,
	0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x10, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x27, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x67, 0x72, 0x6f, 0x6f, 0x6d,
	0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x68, 0x6f, 0x77, 0x42, 0x61, 0x73, 0x65, 0x50,
	0x72, 0x69, 0x63, 0x65, 0x52, 0x0d, 0x73, 0x68, 0x6f, 0x77, 0x42, 0x61, 0x73, 0x65, 0x50, 0x72,
	0x69, 0x63, 0x65, 0x12, 0x37, 0x0a, 0x18, 0x69, 0x73, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x5f, 0x6f,
	0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x18,
	0x11, 0x20, 0x01, 0x28, 0x08, 0x52, 0x15, 0x69, 0x73, 0x42, 0x6f, 0x6f, 0x6b, 0x4f, 0x6e, 0x6c,
	0x69, 0x6e, 0x65, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x20, 0x0a, 0x0c,
	0x69, 0x73, 0x5f, 0x61, 0x6c, 0x6c, 0x5f, 0x73, 0x74, 0x61, 0x66, 0x66, 0x18, 0x12, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x0a, 0x69, 0x73, 0x41, 0x6c, 0x6c, 0x53, 0x74, 0x61, 0x66, 0x66, 0x12, 0x26,
	0x0a, 0x0f, 0x69, 0x73, 0x5f, 0x62, 0x72, 0x65, 0x65, 0x64, 0x5f, 0x66, 0x69, 0x6c, 0x74, 0x65,
	0x72, 0x18, 0x13, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0d, 0x69, 0x73, 0x42, 0x72, 0x65, 0x65, 0x64,
	0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x28, 0x0a, 0x10, 0x69, 0x73, 0x5f, 0x77, 0x65, 0x69,
	0x67, 0x68, 0x74, 0x5f, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x14, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x0e, 0x69, 0x73, 0x57, 0x65, 0x69, 0x67, 0x68, 0x74, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72,
	0x12, 0x2a, 0x0a, 0x11, 0x77, 0x65, 0x69, 0x67, 0x68, 0x74, 0x5f, 0x64, 0x6f, 0x77, 0x6e, 0x5f,
	0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x15, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0f, 0x77, 0x65, 0x69,
	0x67, 0x68, 0x74, 0x44, 0x6f, 0x77, 0x6e, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x12, 0x26, 0x0a, 0x0f,
	0x77, 0x65, 0x69, 0x67, 0x68, 0x74, 0x5f, 0x75, 0x70, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18,
	0x16, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0d, 0x77, 0x65, 0x69, 0x67, 0x68, 0x74, 0x55, 0x70, 0x4c,
	0x69, 0x6d, 0x69, 0x74, 0x12, 0x2d, 0x0a, 0x13, 0x69, 0x73, 0x5f, 0x63, 0x6f, 0x61, 0x74, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x5f, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x17, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x10, 0x69, 0x73, 0x43, 0x6f, 0x61, 0x74, 0x54, 0x79, 0x70, 0x65, 0x46, 0x69, 0x6c,
	0x74, 0x65, 0x72, 0x22, 0xf0, 0x01, 0x0a, 0x16, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x4d,
	0x6f, 0x64, 0x65, 0x6c, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x56, 0x69, 0x65, 0x77, 0x12, 0x0e,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12,
	0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x12, 0x39, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x25, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x67, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x1f, 0x0a,
	0x0b, 0x69, 0x73, 0x5f, 0x69, 0x6e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x18, 0x0a, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x0a, 0x69, 0x73, 0x49, 0x6e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x12, 0x1d,
	0x0a, 0x0a, 0x69, 0x73, 0x5f, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x18, 0x0d, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x09, 0x69, 0x73, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x12, 0x37, 0x0a,
	0x18, 0x69, 0x73, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x5f, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f,
	0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x11, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x15, 0x69, 0x73, 0x42, 0x6f, 0x6f, 0x6b, 0x4f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x41, 0x76, 0x61,
	0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x22, 0xd8, 0x02, 0x0a, 0x1d, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x56, 0x69, 0x65, 0x77, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x39, 0x0a, 0x04,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x25, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x67, 0x72, 0x6f, 0x6f, 0x6d, 0x69,
	0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x54, 0x79, 0x70,
	0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x01, 0x52, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x12, 0x1a, 0x0a,
	0x08, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x08, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x6f, 0x72,
	0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x73, 0x6f, 0x72, 0x74, 0x12, 0x4f, 0x0a,
	0x0f, 0x73, 0x68, 0x6f, 0x77, 0x5f, 0x62, 0x61, 0x73, 0x65, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x65,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x27, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x67, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x2e, 0x76,
	0x31, 0x2e, 0x53, 0x68, 0x6f, 0x77, 0x42, 0x61, 0x73, 0x65, 0x50, 0x72, 0x69, 0x63, 0x65, 0x52,
	0x0d, 0x73, 0x68, 0x6f, 0x77, 0x42, 0x61, 0x73, 0x65, 0x50, 0x72, 0x69, 0x63, 0x65, 0x12, 0x1f,
	0x0a, 0x0b, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x0a, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x49, 0x64, 0x12,
	0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x42, 0x7e, 0x0a, 0x20, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69,
	0x64, 0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x67, 0x72, 0x6f, 0x6f, 0x6d, 0x69,
	0x6e, 0x67, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x58, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e,
	0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79,
	0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x67, 0x72, 0x6f, 0x6f, 0x6d,
	0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x3b, 0x67, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x70,
	0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_grooming_v1_service_models_proto_rawDescOnce sync.Once
	file_moego_models_grooming_v1_service_models_proto_rawDescData = file_moego_models_grooming_v1_service_models_proto_rawDesc
)

func file_moego_models_grooming_v1_service_models_proto_rawDescGZIP() []byte {
	file_moego_models_grooming_v1_service_models_proto_rawDescOnce.Do(func() {
		file_moego_models_grooming_v1_service_models_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_grooming_v1_service_models_proto_rawDescData)
	})
	return file_moego_models_grooming_v1_service_models_proto_rawDescData
}

var file_moego_models_grooming_v1_service_models_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_moego_models_grooming_v1_service_models_proto_goTypes = []interface{}{
	(*ServiceModel)(nil),                  // 0: moego.models.grooming.v1.ServiceModel
	(*ServiceModelClientView)(nil),        // 1: moego.models.grooming.v1.ServiceModelClientView
	(*ServiceModelSelectServiceView)(nil), // 2: moego.models.grooming.v1.ServiceModelSelectServiceView
	(ServiceType)(0),                      // 3: moego.models.grooming.v1.ServiceType
	(*timestamppb.Timestamp)(nil),         // 4: google.protobuf.Timestamp
	(ShowBasePrice)(0),                    // 5: moego.models.grooming.v1.ShowBasePrice
}
var file_moego_models_grooming_v1_service_models_proto_depIdxs = []int32{
	3, // 0: moego.models.grooming.v1.ServiceModel.type:type_name -> moego.models.grooming.v1.ServiceType
	4, // 1: moego.models.grooming.v1.ServiceModel.create_time:type_name -> google.protobuf.Timestamp
	4, // 2: moego.models.grooming.v1.ServiceModel.update_time:type_name -> google.protobuf.Timestamp
	5, // 3: moego.models.grooming.v1.ServiceModel.show_base_price:type_name -> moego.models.grooming.v1.ShowBasePrice
	3, // 4: moego.models.grooming.v1.ServiceModelClientView.type:type_name -> moego.models.grooming.v1.ServiceType
	3, // 5: moego.models.grooming.v1.ServiceModelSelectServiceView.type:type_name -> moego.models.grooming.v1.ServiceType
	5, // 6: moego.models.grooming.v1.ServiceModelSelectServiceView.show_base_price:type_name -> moego.models.grooming.v1.ShowBasePrice
	7, // [7:7] is the sub-list for method output_type
	7, // [7:7] is the sub-list for method input_type
	7, // [7:7] is the sub-list for extension type_name
	7, // [7:7] is the sub-list for extension extendee
	0, // [0:7] is the sub-list for field type_name
}

func init() { file_moego_models_grooming_v1_service_models_proto_init() }
func file_moego_models_grooming_v1_service_models_proto_init() {
	if File_moego_models_grooming_v1_service_models_proto != nil {
		return
	}
	file_moego_models_grooming_v1_service_enums_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_moego_models_grooming_v1_service_models_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ServiceModel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_grooming_v1_service_models_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ServiceModelClientView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_grooming_v1_service_models_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ServiceModelSelectServiceView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_grooming_v1_service_models_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_grooming_v1_service_models_proto_goTypes,
		DependencyIndexes: file_moego_models_grooming_v1_service_models_proto_depIdxs,
		MessageInfos:      file_moego_models_grooming_v1_service_models_proto_msgTypes,
	}.Build()
	File_moego_models_grooming_v1_service_models_proto = out.File
	file_moego_models_grooming_v1_service_models_proto_rawDesc = nil
	file_moego_models_grooming_v1_service_models_proto_goTypes = nil
	file_moego_models_grooming_v1_service_models_proto_depIdxs = nil
}
