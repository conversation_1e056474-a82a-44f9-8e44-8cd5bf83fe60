// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/service/business_customer/v1/business_pet_vaccine_service.proto

package businesscustomersvcpb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// BusinessPetVaccineServiceClient is the client API for BusinessPetVaccineService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type BusinessPetVaccineServiceClient interface {
	// Get a pet vaccine.
	//
	// Error codes:
	// - CODE_PARAMS_ERROR: The pet vaccine does not exist, or does not belong to the given company or business.
	GetPetVaccine(ctx context.Context, in *GetPetVaccineRequest, opts ...grpc.CallOption) (*GetPetVaccineResponse, error)
	// List pet vaccine template.
	// A list of vaccines defined by MoeGo will be returned.
	ListPetVaccineTemplate(ctx context.Context, in *ListPetVaccineTemplateRequest, opts ...grpc.CallOption) (*ListPetVaccineTemplateResponse, error)
	// List pet vaccines.
	// If the company does not exists, or does not define any pet vaccines, an empty list will be returned rather than an error.
	ListPetVaccine(ctx context.Context, in *ListPetVaccineRequest, opts ...grpc.CallOption) (*ListPetVaccineResponse, error)
	// Create a pet vaccine.
	// The name of the new pet vaccine must be unique among all pet vaccines of the company or business.
	//
	// Error codes:
	// - CODE_PARAMS_ERROR: The name is already used by another pet vaccine of the company or business.
	CreatePetVaccine(ctx context.Context, in *CreatePetVaccineRequest, opts ...grpc.CallOption) (*CreatePetVaccineResponse, error)
	// Update a pet vaccine.
	// If the name of the pet vaccine is changed, it must be unique among all pet coat types of the company or business.
	//
	// Error codes:
	//   - CODE_PARAMS_ERROR: The name is already used by another pet vaccine of the company or business, or the pet vaccine
	//     does not exist, or has been deleted, or does not belong to the company or business.
	UpdatePetVaccine(ctx context.Context, in *UpdatePetVaccineRequest, opts ...grpc.CallOption) (*UpdatePetVaccineResponse, error)
	// Sort pet vaccines of the company or business.
	// Pet vaccines will be sorted according to the order of `ids`. If there are vaccines of the company or business whose
	// ids are not included in `ids`, they will be sorted to the end. If an id in `ids` does not exist or does not belong
	// to the company or business, it will be ignored.
	SortPetVaccine(ctx context.Context, in *SortPetVaccineRequest, opts ...grpc.CallOption) (*SortPetVaccineResponse, error)
	// Delete pet vaccine.
	// If the pet vaccine is already deleted, will return success without throwing any error.
	//
	// Error codes:
	// - CODE_PARAMS_ERROR: The pet vaccine does not exist, or does not belong to the given company.
	DeletePetVaccine(ctx context.Context, in *DeletePetVaccineRequest, opts ...grpc.CallOption) (*DeletePetVaccineResponse, error)
}

type businessPetVaccineServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewBusinessPetVaccineServiceClient(cc grpc.ClientConnInterface) BusinessPetVaccineServiceClient {
	return &businessPetVaccineServiceClient{cc}
}

func (c *businessPetVaccineServiceClient) GetPetVaccine(ctx context.Context, in *GetPetVaccineRequest, opts ...grpc.CallOption) (*GetPetVaccineResponse, error) {
	out := new(GetPetVaccineResponse)
	err := c.cc.Invoke(ctx, "/moego.service.business_customer.v1.BusinessPetVaccineService/GetPetVaccine", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessPetVaccineServiceClient) ListPetVaccineTemplate(ctx context.Context, in *ListPetVaccineTemplateRequest, opts ...grpc.CallOption) (*ListPetVaccineTemplateResponse, error) {
	out := new(ListPetVaccineTemplateResponse)
	err := c.cc.Invoke(ctx, "/moego.service.business_customer.v1.BusinessPetVaccineService/ListPetVaccineTemplate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessPetVaccineServiceClient) ListPetVaccine(ctx context.Context, in *ListPetVaccineRequest, opts ...grpc.CallOption) (*ListPetVaccineResponse, error) {
	out := new(ListPetVaccineResponse)
	err := c.cc.Invoke(ctx, "/moego.service.business_customer.v1.BusinessPetVaccineService/ListPetVaccine", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessPetVaccineServiceClient) CreatePetVaccine(ctx context.Context, in *CreatePetVaccineRequest, opts ...grpc.CallOption) (*CreatePetVaccineResponse, error) {
	out := new(CreatePetVaccineResponse)
	err := c.cc.Invoke(ctx, "/moego.service.business_customer.v1.BusinessPetVaccineService/CreatePetVaccine", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessPetVaccineServiceClient) UpdatePetVaccine(ctx context.Context, in *UpdatePetVaccineRequest, opts ...grpc.CallOption) (*UpdatePetVaccineResponse, error) {
	out := new(UpdatePetVaccineResponse)
	err := c.cc.Invoke(ctx, "/moego.service.business_customer.v1.BusinessPetVaccineService/UpdatePetVaccine", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessPetVaccineServiceClient) SortPetVaccine(ctx context.Context, in *SortPetVaccineRequest, opts ...grpc.CallOption) (*SortPetVaccineResponse, error) {
	out := new(SortPetVaccineResponse)
	err := c.cc.Invoke(ctx, "/moego.service.business_customer.v1.BusinessPetVaccineService/SortPetVaccine", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessPetVaccineServiceClient) DeletePetVaccine(ctx context.Context, in *DeletePetVaccineRequest, opts ...grpc.CallOption) (*DeletePetVaccineResponse, error) {
	out := new(DeletePetVaccineResponse)
	err := c.cc.Invoke(ctx, "/moego.service.business_customer.v1.BusinessPetVaccineService/DeletePetVaccine", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// BusinessPetVaccineServiceServer is the server API for BusinessPetVaccineService service.
// All implementations must embed UnimplementedBusinessPetVaccineServiceServer
// for forward compatibility
type BusinessPetVaccineServiceServer interface {
	// Get a pet vaccine.
	//
	// Error codes:
	// - CODE_PARAMS_ERROR: The pet vaccine does not exist, or does not belong to the given company or business.
	GetPetVaccine(context.Context, *GetPetVaccineRequest) (*GetPetVaccineResponse, error)
	// List pet vaccine template.
	// A list of vaccines defined by MoeGo will be returned.
	ListPetVaccineTemplate(context.Context, *ListPetVaccineTemplateRequest) (*ListPetVaccineTemplateResponse, error)
	// List pet vaccines.
	// If the company does not exists, or does not define any pet vaccines, an empty list will be returned rather than an error.
	ListPetVaccine(context.Context, *ListPetVaccineRequest) (*ListPetVaccineResponse, error)
	// Create a pet vaccine.
	// The name of the new pet vaccine must be unique among all pet vaccines of the company or business.
	//
	// Error codes:
	// - CODE_PARAMS_ERROR: The name is already used by another pet vaccine of the company or business.
	CreatePetVaccine(context.Context, *CreatePetVaccineRequest) (*CreatePetVaccineResponse, error)
	// Update a pet vaccine.
	// If the name of the pet vaccine is changed, it must be unique among all pet coat types of the company or business.
	//
	// Error codes:
	//   - CODE_PARAMS_ERROR: The name is already used by another pet vaccine of the company or business, or the pet vaccine
	//     does not exist, or has been deleted, or does not belong to the company or business.
	UpdatePetVaccine(context.Context, *UpdatePetVaccineRequest) (*UpdatePetVaccineResponse, error)
	// Sort pet vaccines of the company or business.
	// Pet vaccines will be sorted according to the order of `ids`. If there are vaccines of the company or business whose
	// ids are not included in `ids`, they will be sorted to the end. If an id in `ids` does not exist or does not belong
	// to the company or business, it will be ignored.
	SortPetVaccine(context.Context, *SortPetVaccineRequest) (*SortPetVaccineResponse, error)
	// Delete pet vaccine.
	// If the pet vaccine is already deleted, will return success without throwing any error.
	//
	// Error codes:
	// - CODE_PARAMS_ERROR: The pet vaccine does not exist, or does not belong to the given company.
	DeletePetVaccine(context.Context, *DeletePetVaccineRequest) (*DeletePetVaccineResponse, error)
	mustEmbedUnimplementedBusinessPetVaccineServiceServer()
}

// UnimplementedBusinessPetVaccineServiceServer must be embedded to have forward compatible implementations.
type UnimplementedBusinessPetVaccineServiceServer struct {
}

func (UnimplementedBusinessPetVaccineServiceServer) GetPetVaccine(context.Context, *GetPetVaccineRequest) (*GetPetVaccineResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPetVaccine not implemented")
}
func (UnimplementedBusinessPetVaccineServiceServer) ListPetVaccineTemplate(context.Context, *ListPetVaccineTemplateRequest) (*ListPetVaccineTemplateResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListPetVaccineTemplate not implemented")
}
func (UnimplementedBusinessPetVaccineServiceServer) ListPetVaccine(context.Context, *ListPetVaccineRequest) (*ListPetVaccineResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListPetVaccine not implemented")
}
func (UnimplementedBusinessPetVaccineServiceServer) CreatePetVaccine(context.Context, *CreatePetVaccineRequest) (*CreatePetVaccineResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreatePetVaccine not implemented")
}
func (UnimplementedBusinessPetVaccineServiceServer) UpdatePetVaccine(context.Context, *UpdatePetVaccineRequest) (*UpdatePetVaccineResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdatePetVaccine not implemented")
}
func (UnimplementedBusinessPetVaccineServiceServer) SortPetVaccine(context.Context, *SortPetVaccineRequest) (*SortPetVaccineResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SortPetVaccine not implemented")
}
func (UnimplementedBusinessPetVaccineServiceServer) DeletePetVaccine(context.Context, *DeletePetVaccineRequest) (*DeletePetVaccineResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeletePetVaccine not implemented")
}
func (UnimplementedBusinessPetVaccineServiceServer) mustEmbedUnimplementedBusinessPetVaccineServiceServer() {
}

// UnsafeBusinessPetVaccineServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to BusinessPetVaccineServiceServer will
// result in compilation errors.
type UnsafeBusinessPetVaccineServiceServer interface {
	mustEmbedUnimplementedBusinessPetVaccineServiceServer()
}

func RegisterBusinessPetVaccineServiceServer(s grpc.ServiceRegistrar, srv BusinessPetVaccineServiceServer) {
	s.RegisterService(&BusinessPetVaccineService_ServiceDesc, srv)
}

func _BusinessPetVaccineService_GetPetVaccine_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPetVaccineRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessPetVaccineServiceServer).GetPetVaccine(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.business_customer.v1.BusinessPetVaccineService/GetPetVaccine",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessPetVaccineServiceServer).GetPetVaccine(ctx, req.(*GetPetVaccineRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessPetVaccineService_ListPetVaccineTemplate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListPetVaccineTemplateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessPetVaccineServiceServer).ListPetVaccineTemplate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.business_customer.v1.BusinessPetVaccineService/ListPetVaccineTemplate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessPetVaccineServiceServer).ListPetVaccineTemplate(ctx, req.(*ListPetVaccineTemplateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessPetVaccineService_ListPetVaccine_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListPetVaccineRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessPetVaccineServiceServer).ListPetVaccine(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.business_customer.v1.BusinessPetVaccineService/ListPetVaccine",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessPetVaccineServiceServer).ListPetVaccine(ctx, req.(*ListPetVaccineRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessPetVaccineService_CreatePetVaccine_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreatePetVaccineRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessPetVaccineServiceServer).CreatePetVaccine(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.business_customer.v1.BusinessPetVaccineService/CreatePetVaccine",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessPetVaccineServiceServer).CreatePetVaccine(ctx, req.(*CreatePetVaccineRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessPetVaccineService_UpdatePetVaccine_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdatePetVaccineRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessPetVaccineServiceServer).UpdatePetVaccine(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.business_customer.v1.BusinessPetVaccineService/UpdatePetVaccine",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessPetVaccineServiceServer).UpdatePetVaccine(ctx, req.(*UpdatePetVaccineRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessPetVaccineService_SortPetVaccine_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SortPetVaccineRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessPetVaccineServiceServer).SortPetVaccine(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.business_customer.v1.BusinessPetVaccineService/SortPetVaccine",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessPetVaccineServiceServer).SortPetVaccine(ctx, req.(*SortPetVaccineRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessPetVaccineService_DeletePetVaccine_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeletePetVaccineRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessPetVaccineServiceServer).DeletePetVaccine(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.business_customer.v1.BusinessPetVaccineService/DeletePetVaccine",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessPetVaccineServiceServer).DeletePetVaccine(ctx, req.(*DeletePetVaccineRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// BusinessPetVaccineService_ServiceDesc is the grpc.ServiceDesc for BusinessPetVaccineService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var BusinessPetVaccineService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.service.business_customer.v1.BusinessPetVaccineService",
	HandlerType: (*BusinessPetVaccineServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetPetVaccine",
			Handler:    _BusinessPetVaccineService_GetPetVaccine_Handler,
		},
		{
			MethodName: "ListPetVaccineTemplate",
			Handler:    _BusinessPetVaccineService_ListPetVaccineTemplate_Handler,
		},
		{
			MethodName: "ListPetVaccine",
			Handler:    _BusinessPetVaccineService_ListPetVaccine_Handler,
		},
		{
			MethodName: "CreatePetVaccine",
			Handler:    _BusinessPetVaccineService_CreatePetVaccine_Handler,
		},
		{
			MethodName: "UpdatePetVaccine",
			Handler:    _BusinessPetVaccineService_UpdatePetVaccine_Handler,
		},
		{
			MethodName: "SortPetVaccine",
			Handler:    _BusinessPetVaccineService_SortPetVaccine_Handler,
		},
		{
			MethodName: "DeletePetVaccine",
			Handler:    _BusinessPetVaccineService_DeletePetVaccine_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/service/business_customer/v1/business_pet_vaccine_service.proto",
}
