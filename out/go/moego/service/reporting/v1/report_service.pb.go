// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/service/reporting/v1/report_service.proto

package reportingsvcpb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/reporting/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// report campaign request
type ReportCampaignRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// reports
	Reports []*v1.CampaignReportModel `protobuf:"bytes,1,rep,name=reports,proto3" json:"reports,omitempty"`
}

func (x *ReportCampaignRequest) Reset() {
	*x = ReportCampaignRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_reporting_v1_report_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReportCampaignRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReportCampaignRequest) ProtoMessage() {}

func (x *ReportCampaignRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_reporting_v1_report_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReportCampaignRequest.ProtoReflect.Descriptor instead.
func (*ReportCampaignRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_reporting_v1_report_service_proto_rawDescGZIP(), []int{0}
}

func (x *ReportCampaignRequest) GetReports() []*v1.CampaignReportModel {
	if x != nil {
		return x.Reports
	}
	return nil
}

// update campaign metrics request
type UpdateCampaignMetricsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// report
	Report *v1.CampaignReportModel `protobuf:"bytes,1,opt,name=report,proto3" json:"report,omitempty"`
}

func (x *UpdateCampaignMetricsRequest) Reset() {
	*x = UpdateCampaignMetricsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_reporting_v1_report_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateCampaignMetricsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateCampaignMetricsRequest) ProtoMessage() {}

func (x *UpdateCampaignMetricsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_reporting_v1_report_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateCampaignMetricsRequest.ProtoReflect.Descriptor instead.
func (*UpdateCampaignMetricsRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_reporting_v1_report_service_proto_rawDescGZIP(), []int{1}
}

func (x *UpdateCampaignMetricsRequest) GetReport() *v1.CampaignReportModel {
	if x != nil {
		return x.Report
	}
	return nil
}

var File_moego_service_reporting_v1_report_service_proto protoreflect.FileDescriptor

var file_moego_service_reporting_v1_report_service_proto_rawDesc = []byte{
	0x0a, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f,
	0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x72, 0x65, 0x70,
	0x6f, 0x72, 0x74, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x1a, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x1a, 0x1b, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x65,
	0x6d, 0x70, 0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x30, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69,
	0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x5f,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x61, 0x0a, 0x15,
	0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x43, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x48, 0x0a, 0x07, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x73,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e,
	0x76, 0x31, 0x2e, 0x43, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x52, 0x65, 0x70, 0x6f, 0x72,
	0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x07, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x22,
	0x66, 0x0a, 0x1c, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67,
	0x6e, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x46, 0x0a, 0x06, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72,
	0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x61, 0x6d, 0x70,
	0x61, 0x69, 0x67, 0x6e, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52,
	0x06, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x32, 0x9d, 0x02, 0x0a, 0x0d, 0x52, 0x65, 0x70, 0x6f,
	0x72, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x5b, 0x0a, 0x0e, 0x52, 0x65, 0x70,
	0x6f, 0x72, 0x74, 0x43, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x12, 0x31, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x72, 0x65, 0x70, 0x6f,
	0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x43,
	0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x69, 0x0a, 0x15, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x43, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x12,
	0x38, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x43, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x4d, 0x65, 0x74, 0x72, 0x69,
	0x63, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74,
	0x79, 0x12, 0x44, 0x0a, 0x12, 0x52, 0x65, 0x70, 0x6c, 0x61, 0x79, 0x41, 0x70, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x1a,
	0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x42, 0x86, 0x01, 0x0a, 0x22, 0x63, 0x6f, 0x6d, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x50, 0x01,
	0x5a, 0x5e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65,
	0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d,
	0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f,
	0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2f, 0x76,
	0x31, 0x3b, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x76, 0x63, 0x70, 0x62,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_service_reporting_v1_report_service_proto_rawDescOnce sync.Once
	file_moego_service_reporting_v1_report_service_proto_rawDescData = file_moego_service_reporting_v1_report_service_proto_rawDesc
)

func file_moego_service_reporting_v1_report_service_proto_rawDescGZIP() []byte {
	file_moego_service_reporting_v1_report_service_proto_rawDescOnce.Do(func() {
		file_moego_service_reporting_v1_report_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_service_reporting_v1_report_service_proto_rawDescData)
	})
	return file_moego_service_reporting_v1_report_service_proto_rawDescData
}

var file_moego_service_reporting_v1_report_service_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_moego_service_reporting_v1_report_service_proto_goTypes = []interface{}{
	(*ReportCampaignRequest)(nil),        // 0: moego.service.reporting.v1.ReportCampaignRequest
	(*UpdateCampaignMetricsRequest)(nil), // 1: moego.service.reporting.v1.UpdateCampaignMetricsRequest
	(*v1.CampaignReportModel)(nil),       // 2: moego.models.reporting.v1.CampaignReportModel
	(*emptypb.Empty)(nil),                // 3: google.protobuf.Empty
}
var file_moego_service_reporting_v1_report_service_proto_depIdxs = []int32{
	2, // 0: moego.service.reporting.v1.ReportCampaignRequest.reports:type_name -> moego.models.reporting.v1.CampaignReportModel
	2, // 1: moego.service.reporting.v1.UpdateCampaignMetricsRequest.report:type_name -> moego.models.reporting.v1.CampaignReportModel
	0, // 2: moego.service.reporting.v1.ReportService.ReportCampaign:input_type -> moego.service.reporting.v1.ReportCampaignRequest
	1, // 3: moego.service.reporting.v1.ReportService.UpdateCampaignMetrics:input_type -> moego.service.reporting.v1.UpdateCampaignMetricsRequest
	3, // 4: moego.service.reporting.v1.ReportService.ReplayAppointments:input_type -> google.protobuf.Empty
	3, // 5: moego.service.reporting.v1.ReportService.ReportCampaign:output_type -> google.protobuf.Empty
	3, // 6: moego.service.reporting.v1.ReportService.UpdateCampaignMetrics:output_type -> google.protobuf.Empty
	3, // 7: moego.service.reporting.v1.ReportService.ReplayAppointments:output_type -> google.protobuf.Empty
	5, // [5:8] is the sub-list for method output_type
	2, // [2:5] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_moego_service_reporting_v1_report_service_proto_init() }
func file_moego_service_reporting_v1_report_service_proto_init() {
	if File_moego_service_reporting_v1_report_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_service_reporting_v1_report_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReportCampaignRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_reporting_v1_report_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateCampaignMetricsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_service_reporting_v1_report_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_service_reporting_v1_report_service_proto_goTypes,
		DependencyIndexes: file_moego_service_reporting_v1_report_service_proto_depIdxs,
		MessageInfos:      file_moego_service_reporting_v1_report_service_proto_msgTypes,
	}.Build()
	File_moego_service_reporting_v1_report_service_proto = out.File
	file_moego_service_reporting_v1_report_service_proto_rawDesc = nil
	file_moego_service_reporting_v1_report_service_proto_goTypes = nil
	file_moego_service_reporting_v1_report_service_proto_depIdxs = nil
}
