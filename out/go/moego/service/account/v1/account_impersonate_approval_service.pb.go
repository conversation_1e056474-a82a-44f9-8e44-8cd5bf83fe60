// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/service/account/v1/account_impersonate_approval_service.proto

package accountsvcpb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/account/v1"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// save approval instance request
type SaveApprovalInstanceRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// approval instance
	Instance *v1.AccountImpersonateApprovalInstanceModel `protobuf:"bytes,1,opt,name=instance,proto3" json:"instance,omitempty"`
}

func (x *SaveApprovalInstanceRequest) Reset() {
	*x = SaveApprovalInstanceRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_account_v1_account_impersonate_approval_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SaveApprovalInstanceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SaveApprovalInstanceRequest) ProtoMessage() {}

func (x *SaveApprovalInstanceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_account_v1_account_impersonate_approval_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SaveApprovalInstanceRequest.ProtoReflect.Descriptor instead.
func (*SaveApprovalInstanceRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_account_v1_account_impersonate_approval_service_proto_rawDescGZIP(), []int{0}
}

func (x *SaveApprovalInstanceRequest) GetInstance() *v1.AccountImpersonateApprovalInstanceModel {
	if x != nil {
		return x.Instance
	}
	return nil
}

// save approval instance response
type SaveApprovalInstanceResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *SaveApprovalInstanceResponse) Reset() {
	*x = SaveApprovalInstanceResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_account_v1_account_impersonate_approval_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SaveApprovalInstanceResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SaveApprovalInstanceResponse) ProtoMessage() {}

func (x *SaveApprovalInstanceResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_account_v1_account_impersonate_approval_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SaveApprovalInstanceResponse.ProtoReflect.Descriptor instead.
func (*SaveApprovalInstanceResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_account_v1_account_impersonate_approval_service_proto_rawDescGZIP(), []int{1}
}

// get latest approval instance request
type GetLatestApprovalInstanceRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// impersonator
	Impersonator string `protobuf:"bytes,1,opt,name=impersonator,proto3" json:"impersonator,omitempty"`
	// source
	Source string `protobuf:"bytes,2,opt,name=source,proto3" json:"source,omitempty"`
	// target account
	//
	// Types that are assignable to TargetAccount:
	//
	//	*GetLatestApprovalInstanceRequest_TargetAccountId
	//	*GetLatestApprovalInstanceRequest_TargetAccountEmail
	TargetAccount isGetLatestApprovalInstanceRequest_TargetAccount `protobuf_oneof:"target_account"`
	// status, if set, use as a filter to get latest approval instance
	Status *string `protobuf:"bytes,5,opt,name=status,proto3,oneof" json:"status,omitempty"`
}

func (x *GetLatestApprovalInstanceRequest) Reset() {
	*x = GetLatestApprovalInstanceRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_account_v1_account_impersonate_approval_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLatestApprovalInstanceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLatestApprovalInstanceRequest) ProtoMessage() {}

func (x *GetLatestApprovalInstanceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_account_v1_account_impersonate_approval_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLatestApprovalInstanceRequest.ProtoReflect.Descriptor instead.
func (*GetLatestApprovalInstanceRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_account_v1_account_impersonate_approval_service_proto_rawDescGZIP(), []int{2}
}

func (x *GetLatestApprovalInstanceRequest) GetImpersonator() string {
	if x != nil {
		return x.Impersonator
	}
	return ""
}

func (x *GetLatestApprovalInstanceRequest) GetSource() string {
	if x != nil {
		return x.Source
	}
	return ""
}

func (m *GetLatestApprovalInstanceRequest) GetTargetAccount() isGetLatestApprovalInstanceRequest_TargetAccount {
	if m != nil {
		return m.TargetAccount
	}
	return nil
}

func (x *GetLatestApprovalInstanceRequest) GetTargetAccountId() int64 {
	if x, ok := x.GetTargetAccount().(*GetLatestApprovalInstanceRequest_TargetAccountId); ok {
		return x.TargetAccountId
	}
	return 0
}

func (x *GetLatestApprovalInstanceRequest) GetTargetAccountEmail() string {
	if x, ok := x.GetTargetAccount().(*GetLatestApprovalInstanceRequest_TargetAccountEmail); ok {
		return x.TargetAccountEmail
	}
	return ""
}

func (x *GetLatestApprovalInstanceRequest) GetStatus() string {
	if x != nil && x.Status != nil {
		return *x.Status
	}
	return ""
}

type isGetLatestApprovalInstanceRequest_TargetAccount interface {
	isGetLatestApprovalInstanceRequest_TargetAccount()
}

type GetLatestApprovalInstanceRequest_TargetAccountId struct {
	// target account id
	TargetAccountId int64 `protobuf:"varint,3,opt,name=target_account_id,json=targetAccountId,proto3,oneof"`
}

type GetLatestApprovalInstanceRequest_TargetAccountEmail struct {
	// target account email
	TargetAccountEmail string `protobuf:"bytes,4,opt,name=target_account_email,json=targetAccountEmail,proto3,oneof"`
}

func (*GetLatestApprovalInstanceRequest_TargetAccountId) isGetLatestApprovalInstanceRequest_TargetAccount() {
}

func (*GetLatestApprovalInstanceRequest_TargetAccountEmail) isGetLatestApprovalInstanceRequest_TargetAccount() {
}

// get latest approval instance response
type GetLatestApprovalInstanceResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// approval instance, may not exist
	Instance *v1.AccountImpersonateApprovalInstanceModel `protobuf:"bytes,1,opt,name=instance,proto3,oneof" json:"instance,omitempty"`
}

func (x *GetLatestApprovalInstanceResponse) Reset() {
	*x = GetLatestApprovalInstanceResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_account_v1_account_impersonate_approval_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLatestApprovalInstanceResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLatestApprovalInstanceResponse) ProtoMessage() {}

func (x *GetLatestApprovalInstanceResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_account_v1_account_impersonate_approval_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLatestApprovalInstanceResponse.ProtoReflect.Descriptor instead.
func (*GetLatestApprovalInstanceResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_account_v1_account_impersonate_approval_service_proto_rawDescGZIP(), []int{3}
}

func (x *GetLatestApprovalInstanceResponse) GetInstance() *v1.AccountImpersonateApprovalInstanceModel {
	if x != nil {
		return x.Instance
	}
	return nil
}

var File_moego_service_account_v1_account_impersonate_approval_service_proto protoreflect.FileDescriptor

var file_moego_service_account_v1_account_impersonate_approval_service_proto_rawDesc = []byte{
	0x0a, 0x43, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x5f, 0x69, 0x6d, 0x70, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x61, 0x74, 0x65, 0x5f, 0x61,
	0x70, 0x70, 0x72, 0x6f, 0x76, 0x61, 0x6c, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x18, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x1a,
	0x41, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x5f, 0x69, 0x6d, 0x70, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x61, 0x74, 0x65, 0x5f, 0x61, 0x70, 0x70,
	0x72, 0x6f, 0x76, 0x61, 0x6c, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c,
	0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x85, 0x01, 0x0a, 0x1b,
	0x53, 0x61, 0x76, 0x65, 0x41, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x61, 0x6c, 0x49, 0x6e, 0x73, 0x74,
	0x61, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x66, 0x0a, 0x08, 0x69,
	0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x40, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49,
	0x6d, 0x70, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x61, 0x74, 0x65, 0x41, 0x70, 0x70, 0x72, 0x6f, 0x76,
	0x61, 0x6c, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x42,
	0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x08, 0x69, 0x6e, 0x73, 0x74, 0x61,
	0x6e, 0x63, 0x65, 0x22, 0x1e, 0x0a, 0x1c, 0x53, 0x61, 0x76, 0x65, 0x41, 0x70, 0x70, 0x72, 0x6f,
	0x76, 0x61, 0x6c, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x22, 0xa5, 0x02, 0x0a, 0x20, 0x47, 0x65, 0x74, 0x4c, 0x61, 0x74, 0x65, 0x73,
	0x74, 0x41, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x61, 0x6c, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2c, 0x0a, 0x0c, 0x69, 0x6d, 0x70, 0x65,
	0x72, 0x73, 0x6f, 0x6e, 0x61, 0x74, 0x6f, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x08,
	0xfa, 0x42, 0x05, 0x72, 0x03, 0x18, 0xff, 0x01, 0x52, 0x0c, 0x69, 0x6d, 0x70, 0x65, 0x72, 0x73,
	0x6f, 0x6e, 0x61, 0x74, 0x6f, 0x72, 0x12, 0x1f, 0x0a, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x18, 0x32, 0x52,
	0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x35, 0x0a, 0x11, 0x74, 0x61, 0x72, 0x67, 0x65,
	0x74, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x48, 0x00, 0x52, 0x0f, 0x74,
	0x61, 0x72, 0x67, 0x65, 0x74, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x3c,
	0x0a, 0x14, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x5f, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x08, 0xfa, 0x42,
	0x05, 0x72, 0x03, 0x18, 0xff, 0x01, 0x48, 0x00, 0x52, 0x12, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74,
	0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x1b, 0x0a, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x48, 0x01, 0x52, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x88, 0x01, 0x01, 0x42, 0x15, 0x0a, 0x0e, 0x74, 0x61, 0x72,
	0x67, 0x65, 0x74, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x03, 0xf8, 0x42, 0x01,
	0x42, 0x09, 0x0a, 0x07, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x93, 0x01, 0x0a, 0x21,
	0x47, 0x65, 0x74, 0x4c, 0x61, 0x74, 0x65, 0x73, 0x74, 0x41, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x61,
	0x6c, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x61, 0x0a, 0x08, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x40, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x6d, 0x70, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x61, 0x74, 0x65,
	0x41, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x61, 0x6c, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65,
	0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x48, 0x00, 0x52, 0x08, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63,
	0x65, 0x88, 0x01, 0x01, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63,
	0x65, 0x32, 0xc2, 0x02, 0x0a, 0x21, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x6d, 0x70,
	0x65, 0x72, 0x73, 0x6f, 0x6e, 0x61, 0x74, 0x65, 0x41, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x61, 0x6c,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x85, 0x01, 0x0a, 0x14, 0x53, 0x61, 0x76, 0x65,
	0x41, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x61, 0x6c, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65,
	0x12, 0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x61, 0x76, 0x65,
	0x41, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x61, 0x6c, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x36, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e,
	0x76, 0x31, 0x2e, 0x53, 0x61, 0x76, 0x65, 0x41, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x61, 0x6c, 0x49,
	0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x94, 0x01, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x4c, 0x61, 0x74, 0x65, 0x73, 0x74, 0x41, 0x70, 0x70,
	0x72, 0x6f, 0x76, 0x61, 0x6c, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x12, 0x3a, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x4c, 0x61, 0x74, 0x65,
	0x73, 0x74, 0x41, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x61, 0x6c, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e,
	0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3b, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x4c, 0x61, 0x74, 0x65, 0x73, 0x74, 0x41, 0x70,
	0x70, 0x72, 0x6f, 0x76, 0x61, 0x6c, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x80, 0x01, 0x0a, 0x20, 0x63, 0x6f, 0x6d, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x5a, 0x67,
	0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c,
	0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69,
	0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74,
	0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x3b, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x73, 0x76, 0x63, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33,
}

var (
	file_moego_service_account_v1_account_impersonate_approval_service_proto_rawDescOnce sync.Once
	file_moego_service_account_v1_account_impersonate_approval_service_proto_rawDescData = file_moego_service_account_v1_account_impersonate_approval_service_proto_rawDesc
)

func file_moego_service_account_v1_account_impersonate_approval_service_proto_rawDescGZIP() []byte {
	file_moego_service_account_v1_account_impersonate_approval_service_proto_rawDescOnce.Do(func() {
		file_moego_service_account_v1_account_impersonate_approval_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_service_account_v1_account_impersonate_approval_service_proto_rawDescData)
	})
	return file_moego_service_account_v1_account_impersonate_approval_service_proto_rawDescData
}

var file_moego_service_account_v1_account_impersonate_approval_service_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_moego_service_account_v1_account_impersonate_approval_service_proto_goTypes = []interface{}{
	(*SaveApprovalInstanceRequest)(nil),                // 0: moego.service.account.v1.SaveApprovalInstanceRequest
	(*SaveApprovalInstanceResponse)(nil),               // 1: moego.service.account.v1.SaveApprovalInstanceResponse
	(*GetLatestApprovalInstanceRequest)(nil),           // 2: moego.service.account.v1.GetLatestApprovalInstanceRequest
	(*GetLatestApprovalInstanceResponse)(nil),          // 3: moego.service.account.v1.GetLatestApprovalInstanceResponse
	(*v1.AccountImpersonateApprovalInstanceModel)(nil), // 4: moego.models.account.v1.AccountImpersonateApprovalInstanceModel
}
var file_moego_service_account_v1_account_impersonate_approval_service_proto_depIdxs = []int32{
	4, // 0: moego.service.account.v1.SaveApprovalInstanceRequest.instance:type_name -> moego.models.account.v1.AccountImpersonateApprovalInstanceModel
	4, // 1: moego.service.account.v1.GetLatestApprovalInstanceResponse.instance:type_name -> moego.models.account.v1.AccountImpersonateApprovalInstanceModel
	0, // 2: moego.service.account.v1.AccountImpersonateApprovalService.SaveApprovalInstance:input_type -> moego.service.account.v1.SaveApprovalInstanceRequest
	2, // 3: moego.service.account.v1.AccountImpersonateApprovalService.GetLatestApprovalInstance:input_type -> moego.service.account.v1.GetLatestApprovalInstanceRequest
	1, // 4: moego.service.account.v1.AccountImpersonateApprovalService.SaveApprovalInstance:output_type -> moego.service.account.v1.SaveApprovalInstanceResponse
	3, // 5: moego.service.account.v1.AccountImpersonateApprovalService.GetLatestApprovalInstance:output_type -> moego.service.account.v1.GetLatestApprovalInstanceResponse
	4, // [4:6] is the sub-list for method output_type
	2, // [2:4] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_moego_service_account_v1_account_impersonate_approval_service_proto_init() }
func file_moego_service_account_v1_account_impersonate_approval_service_proto_init() {
	if File_moego_service_account_v1_account_impersonate_approval_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_service_account_v1_account_impersonate_approval_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SaveApprovalInstanceRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_account_v1_account_impersonate_approval_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SaveApprovalInstanceResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_account_v1_account_impersonate_approval_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLatestApprovalInstanceRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_account_v1_account_impersonate_approval_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLatestApprovalInstanceResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_service_account_v1_account_impersonate_approval_service_proto_msgTypes[2].OneofWrappers = []interface{}{
		(*GetLatestApprovalInstanceRequest_TargetAccountId)(nil),
		(*GetLatestApprovalInstanceRequest_TargetAccountEmail)(nil),
	}
	file_moego_service_account_v1_account_impersonate_approval_service_proto_msgTypes[3].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_service_account_v1_account_impersonate_approval_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_service_account_v1_account_impersonate_approval_service_proto_goTypes,
		DependencyIndexes: file_moego_service_account_v1_account_impersonate_approval_service_proto_depIdxs,
		MessageInfos:      file_moego_service_account_v1_account_impersonate_approval_service_proto_msgTypes,
	}.Build()
	File_moego_service_account_v1_account_impersonate_approval_service_proto = out.File
	file_moego_service_account_v1_account_impersonate_approval_service_proto_rawDesc = nil
	file_moego_service_account_v1_account_impersonate_approval_service_proto_goTypes = nil
	file_moego_service_account_v1_account_impersonate_approval_service_proto_depIdxs = nil
}
