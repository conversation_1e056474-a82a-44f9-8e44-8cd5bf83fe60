// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/service/account/v1/account_service.proto

package accountsvcpb

import (
	context "context"
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/account/v1"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// AccountServiceClient is the client API for AccountService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type AccountServiceClient interface {
	// Check if the account identifier has been used.
	// The account identifier can be email or phone number.
	// An identifier is usable (`used` = false) if it is not associated with any accounts.
	// An identifier is occupied (`used` = true) if it is associated with an account which has not been deleted (can be active or frozen).
	// If an account is deleted, its identifier will be usable again (`used` = false).
	CheckIdentifier(ctx context.Context, in *CheckIdentifierRequest, opts ...grpc.CallOption) (*CheckIdentifierResponse, error)
	// Validate if the password is correct for certain account.
	// If the account does not exist, or does not set a password, or has been deleted,
	// any password inputted will return `correct` = false.
	ValidatePassword(ctx context.Context, in *ValidatePasswordRequest, opts ...grpc.CallOption) (*ValidatePasswordResponse, error)
	// Get an account by one of id, email or phone number.
	// A deleted account can be queried by id but not by email or phone number,
	// because an email or a phone number can be associated with multiple deleted accounts
	// while an id can uniquely identifies an account.
	//
	// Error codes:
	// - CODE_ACCOUNT_NOT_EXIST: The account does not exist.
	GetAccount(ctx context.Context, in *GetAccountRequest, opts ...grpc.CallOption) (*v1.AccountModel, error)
	// Get accounts by id list.
	// Deleted accounts can be queried.
	// If the account of certain id does not exist, the result list will not contain this account.
	// Thus, the size of result list may smaller than the size of request id list.
	// A maximum of 1000 ids can be requested at a time.
	BatchGetAccount(ctx context.Context, in *BatchGetAccountRequest, opts ...grpc.CallOption) (*BatchGetAccountResponse, error)
	// Create a new account.
	//
	// Error codes:
	// - CODE_EMAIL_CONFLICT: the email has been used by another existing account.
	// - CODE_PHONE_NUMBER_CONFLICT: the phone number has been used by another existing account.
	CreateAccount(ctx context.Context, in *CreateAccountRequest, opts ...grpc.CallOption) (*v1.AccountModel, error)
	// Update an existing account.
	// An deleted account can also be updated.
	//
	// Error codes:
	// - CODE_ACCOUNT_NOT_EXIST: The account does not exist.
	// - CODE_EMAIL_CONFLICT: the email has been used by another existing account.
	// - CODE_PHONE_NUMBER_CONFLICT: the phone number has been used by another existing account.
	UpdateAccount(ctx context.Context, in *UpdateAccountRequest, opts ...grpc.CallOption) (*v1.AccountModel, error)
	// Freeze an account by id.
	// A frozen account may not be able to create sessions.
	//
	// Error codes:
	// - CODE_ACCOUNT_NOT_EXIST: The account does not exist or has been deleted.
	FreezeAccount(ctx context.Context, in *FreezeAccountRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// Unfreeze an account by id.
	//
	// Error codes:
	// - CODE_ACCOUNT_NOT_EXIST: The account does not exist or has been deleted.
	UnfreezeAccount(ctx context.Context, in *UnfreezeAccountRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// Delete an account by id.
	//
	// Error codes:
	// - CODE_ACCOUNT_NOT_EXIST: The account does not exist or has been deleted.
	DeleteAccount(ctx context.Context, in *DeleteAccountRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// Try to recover an account by id.
	// An account can be recovered only if it has been deleted by DeleteAccount method.
	// The recovery may fail if the email or phone number of the account has been used by other active accounts.
	//
	// Error codes:
	// - CODE_ACCOUNT_NOT_EXIST: The account does not exist.
	// - CODE_ACCOUNT_ALREADY_RECOVERED: The account has not been deleted.
	// - CODE_EMAIL_CONFLICT: the email has been used by another existing account.
	// - CODE_PHONE_NUMBER_CONFLICT: the phone number has been used by another existing account.
	RecoverAccount(ctx context.Context, in *RecoverAccountRequest, opts ...grpc.CallOption) (*RecoverAccountResponse, error)
	// Get security module last update time.
	// Now support password last update time.
	//
	// Error codes:
	// - CODE_ACCOUNT_NOT_EXIST: The account does not exist.
	GetSecurityLastUpdateTime(ctx context.Context, in *GetSecurityLastUpdateTimeRequest, opts ...grpc.CallOption) (*GetSecurityLastUpdateTimeResponse, error)
}

type accountServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewAccountServiceClient(cc grpc.ClientConnInterface) AccountServiceClient {
	return &accountServiceClient{cc}
}

func (c *accountServiceClient) CheckIdentifier(ctx context.Context, in *CheckIdentifierRequest, opts ...grpc.CallOption) (*CheckIdentifierResponse, error) {
	out := new(CheckIdentifierResponse)
	err := c.cc.Invoke(ctx, "/moego.service.account.v1.AccountService/CheckIdentifier", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *accountServiceClient) ValidatePassword(ctx context.Context, in *ValidatePasswordRequest, opts ...grpc.CallOption) (*ValidatePasswordResponse, error) {
	out := new(ValidatePasswordResponse)
	err := c.cc.Invoke(ctx, "/moego.service.account.v1.AccountService/ValidatePassword", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *accountServiceClient) GetAccount(ctx context.Context, in *GetAccountRequest, opts ...grpc.CallOption) (*v1.AccountModel, error) {
	out := new(v1.AccountModel)
	err := c.cc.Invoke(ctx, "/moego.service.account.v1.AccountService/GetAccount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *accountServiceClient) BatchGetAccount(ctx context.Context, in *BatchGetAccountRequest, opts ...grpc.CallOption) (*BatchGetAccountResponse, error) {
	out := new(BatchGetAccountResponse)
	err := c.cc.Invoke(ctx, "/moego.service.account.v1.AccountService/BatchGetAccount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *accountServiceClient) CreateAccount(ctx context.Context, in *CreateAccountRequest, opts ...grpc.CallOption) (*v1.AccountModel, error) {
	out := new(v1.AccountModel)
	err := c.cc.Invoke(ctx, "/moego.service.account.v1.AccountService/CreateAccount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *accountServiceClient) UpdateAccount(ctx context.Context, in *UpdateAccountRequest, opts ...grpc.CallOption) (*v1.AccountModel, error) {
	out := new(v1.AccountModel)
	err := c.cc.Invoke(ctx, "/moego.service.account.v1.AccountService/UpdateAccount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *accountServiceClient) FreezeAccount(ctx context.Context, in *FreezeAccountRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, "/moego.service.account.v1.AccountService/FreezeAccount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *accountServiceClient) UnfreezeAccount(ctx context.Context, in *UnfreezeAccountRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, "/moego.service.account.v1.AccountService/UnfreezeAccount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *accountServiceClient) DeleteAccount(ctx context.Context, in *DeleteAccountRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, "/moego.service.account.v1.AccountService/DeleteAccount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *accountServiceClient) RecoverAccount(ctx context.Context, in *RecoverAccountRequest, opts ...grpc.CallOption) (*RecoverAccountResponse, error) {
	out := new(RecoverAccountResponse)
	err := c.cc.Invoke(ctx, "/moego.service.account.v1.AccountService/RecoverAccount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *accountServiceClient) GetSecurityLastUpdateTime(ctx context.Context, in *GetSecurityLastUpdateTimeRequest, opts ...grpc.CallOption) (*GetSecurityLastUpdateTimeResponse, error) {
	out := new(GetSecurityLastUpdateTimeResponse)
	err := c.cc.Invoke(ctx, "/moego.service.account.v1.AccountService/GetSecurityLastUpdateTime", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AccountServiceServer is the server API for AccountService service.
// All implementations must embed UnimplementedAccountServiceServer
// for forward compatibility
type AccountServiceServer interface {
	// Check if the account identifier has been used.
	// The account identifier can be email or phone number.
	// An identifier is usable (`used` = false) if it is not associated with any accounts.
	// An identifier is occupied (`used` = true) if it is associated with an account which has not been deleted (can be active or frozen).
	// If an account is deleted, its identifier will be usable again (`used` = false).
	CheckIdentifier(context.Context, *CheckIdentifierRequest) (*CheckIdentifierResponse, error)
	// Validate if the password is correct for certain account.
	// If the account does not exist, or does not set a password, or has been deleted,
	// any password inputted will return `correct` = false.
	ValidatePassword(context.Context, *ValidatePasswordRequest) (*ValidatePasswordResponse, error)
	// Get an account by one of id, email or phone number.
	// A deleted account can be queried by id but not by email or phone number,
	// because an email or a phone number can be associated with multiple deleted accounts
	// while an id can uniquely identifies an account.
	//
	// Error codes:
	// - CODE_ACCOUNT_NOT_EXIST: The account does not exist.
	GetAccount(context.Context, *GetAccountRequest) (*v1.AccountModel, error)
	// Get accounts by id list.
	// Deleted accounts can be queried.
	// If the account of certain id does not exist, the result list will not contain this account.
	// Thus, the size of result list may smaller than the size of request id list.
	// A maximum of 1000 ids can be requested at a time.
	BatchGetAccount(context.Context, *BatchGetAccountRequest) (*BatchGetAccountResponse, error)
	// Create a new account.
	//
	// Error codes:
	// - CODE_EMAIL_CONFLICT: the email has been used by another existing account.
	// - CODE_PHONE_NUMBER_CONFLICT: the phone number has been used by another existing account.
	CreateAccount(context.Context, *CreateAccountRequest) (*v1.AccountModel, error)
	// Update an existing account.
	// An deleted account can also be updated.
	//
	// Error codes:
	// - CODE_ACCOUNT_NOT_EXIST: The account does not exist.
	// - CODE_EMAIL_CONFLICT: the email has been used by another existing account.
	// - CODE_PHONE_NUMBER_CONFLICT: the phone number has been used by another existing account.
	UpdateAccount(context.Context, *UpdateAccountRequest) (*v1.AccountModel, error)
	// Freeze an account by id.
	// A frozen account may not be able to create sessions.
	//
	// Error codes:
	// - CODE_ACCOUNT_NOT_EXIST: The account does not exist or has been deleted.
	FreezeAccount(context.Context, *FreezeAccountRequest) (*emptypb.Empty, error)
	// Unfreeze an account by id.
	//
	// Error codes:
	// - CODE_ACCOUNT_NOT_EXIST: The account does not exist or has been deleted.
	UnfreezeAccount(context.Context, *UnfreezeAccountRequest) (*emptypb.Empty, error)
	// Delete an account by id.
	//
	// Error codes:
	// - CODE_ACCOUNT_NOT_EXIST: The account does not exist or has been deleted.
	DeleteAccount(context.Context, *DeleteAccountRequest) (*emptypb.Empty, error)
	// Try to recover an account by id.
	// An account can be recovered only if it has been deleted by DeleteAccount method.
	// The recovery may fail if the email or phone number of the account has been used by other active accounts.
	//
	// Error codes:
	// - CODE_ACCOUNT_NOT_EXIST: The account does not exist.
	// - CODE_ACCOUNT_ALREADY_RECOVERED: The account has not been deleted.
	// - CODE_EMAIL_CONFLICT: the email has been used by another existing account.
	// - CODE_PHONE_NUMBER_CONFLICT: the phone number has been used by another existing account.
	RecoverAccount(context.Context, *RecoverAccountRequest) (*RecoverAccountResponse, error)
	// Get security module last update time.
	// Now support password last update time.
	//
	// Error codes:
	// - CODE_ACCOUNT_NOT_EXIST: The account does not exist.
	GetSecurityLastUpdateTime(context.Context, *GetSecurityLastUpdateTimeRequest) (*GetSecurityLastUpdateTimeResponse, error)
	mustEmbedUnimplementedAccountServiceServer()
}

// UnimplementedAccountServiceServer must be embedded to have forward compatible implementations.
type UnimplementedAccountServiceServer struct {
}

func (UnimplementedAccountServiceServer) CheckIdentifier(context.Context, *CheckIdentifierRequest) (*CheckIdentifierResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckIdentifier not implemented")
}
func (UnimplementedAccountServiceServer) ValidatePassword(context.Context, *ValidatePasswordRequest) (*ValidatePasswordResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ValidatePassword not implemented")
}
func (UnimplementedAccountServiceServer) GetAccount(context.Context, *GetAccountRequest) (*v1.AccountModel, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAccount not implemented")
}
func (UnimplementedAccountServiceServer) BatchGetAccount(context.Context, *BatchGetAccountRequest) (*BatchGetAccountResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchGetAccount not implemented")
}
func (UnimplementedAccountServiceServer) CreateAccount(context.Context, *CreateAccountRequest) (*v1.AccountModel, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateAccount not implemented")
}
func (UnimplementedAccountServiceServer) UpdateAccount(context.Context, *UpdateAccountRequest) (*v1.AccountModel, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateAccount not implemented")
}
func (UnimplementedAccountServiceServer) FreezeAccount(context.Context, *FreezeAccountRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FreezeAccount not implemented")
}
func (UnimplementedAccountServiceServer) UnfreezeAccount(context.Context, *UnfreezeAccountRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UnfreezeAccount not implemented")
}
func (UnimplementedAccountServiceServer) DeleteAccount(context.Context, *DeleteAccountRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteAccount not implemented")
}
func (UnimplementedAccountServiceServer) RecoverAccount(context.Context, *RecoverAccountRequest) (*RecoverAccountResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RecoverAccount not implemented")
}
func (UnimplementedAccountServiceServer) GetSecurityLastUpdateTime(context.Context, *GetSecurityLastUpdateTimeRequest) (*GetSecurityLastUpdateTimeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSecurityLastUpdateTime not implemented")
}
func (UnimplementedAccountServiceServer) mustEmbedUnimplementedAccountServiceServer() {}

// UnsafeAccountServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to AccountServiceServer will
// result in compilation errors.
type UnsafeAccountServiceServer interface {
	mustEmbedUnimplementedAccountServiceServer()
}

func RegisterAccountServiceServer(s grpc.ServiceRegistrar, srv AccountServiceServer) {
	s.RegisterService(&AccountService_ServiceDesc, srv)
}

func _AccountService_CheckIdentifier_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckIdentifierRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccountServiceServer).CheckIdentifier(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.account.v1.AccountService/CheckIdentifier",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccountServiceServer).CheckIdentifier(ctx, req.(*CheckIdentifierRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AccountService_ValidatePassword_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ValidatePasswordRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccountServiceServer).ValidatePassword(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.account.v1.AccountService/ValidatePassword",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccountServiceServer).ValidatePassword(ctx, req.(*ValidatePasswordRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AccountService_GetAccount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAccountRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccountServiceServer).GetAccount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.account.v1.AccountService/GetAccount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccountServiceServer).GetAccount(ctx, req.(*GetAccountRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AccountService_BatchGetAccount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetAccountRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccountServiceServer).BatchGetAccount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.account.v1.AccountService/BatchGetAccount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccountServiceServer).BatchGetAccount(ctx, req.(*BatchGetAccountRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AccountService_CreateAccount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateAccountRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccountServiceServer).CreateAccount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.account.v1.AccountService/CreateAccount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccountServiceServer).CreateAccount(ctx, req.(*CreateAccountRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AccountService_UpdateAccount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateAccountRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccountServiceServer).UpdateAccount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.account.v1.AccountService/UpdateAccount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccountServiceServer).UpdateAccount(ctx, req.(*UpdateAccountRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AccountService_FreezeAccount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FreezeAccountRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccountServiceServer).FreezeAccount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.account.v1.AccountService/FreezeAccount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccountServiceServer).FreezeAccount(ctx, req.(*FreezeAccountRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AccountService_UnfreezeAccount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UnfreezeAccountRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccountServiceServer).UnfreezeAccount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.account.v1.AccountService/UnfreezeAccount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccountServiceServer).UnfreezeAccount(ctx, req.(*UnfreezeAccountRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AccountService_DeleteAccount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteAccountRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccountServiceServer).DeleteAccount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.account.v1.AccountService/DeleteAccount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccountServiceServer).DeleteAccount(ctx, req.(*DeleteAccountRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AccountService_RecoverAccount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RecoverAccountRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccountServiceServer).RecoverAccount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.account.v1.AccountService/RecoverAccount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccountServiceServer).RecoverAccount(ctx, req.(*RecoverAccountRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AccountService_GetSecurityLastUpdateTime_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSecurityLastUpdateTimeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccountServiceServer).GetSecurityLastUpdateTime(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.account.v1.AccountService/GetSecurityLastUpdateTime",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccountServiceServer).GetSecurityLastUpdateTime(ctx, req.(*GetSecurityLastUpdateTimeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// AccountService_ServiceDesc is the grpc.ServiceDesc for AccountService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var AccountService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.service.account.v1.AccountService",
	HandlerType: (*AccountServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CheckIdentifier",
			Handler:    _AccountService_CheckIdentifier_Handler,
		},
		{
			MethodName: "ValidatePassword",
			Handler:    _AccountService_ValidatePassword_Handler,
		},
		{
			MethodName: "GetAccount",
			Handler:    _AccountService_GetAccount_Handler,
		},
		{
			MethodName: "BatchGetAccount",
			Handler:    _AccountService_BatchGetAccount_Handler,
		},
		{
			MethodName: "CreateAccount",
			Handler:    _AccountService_CreateAccount_Handler,
		},
		{
			MethodName: "UpdateAccount",
			Handler:    _AccountService_UpdateAccount_Handler,
		},
		{
			MethodName: "FreezeAccount",
			Handler:    _AccountService_FreezeAccount_Handler,
		},
		{
			MethodName: "UnfreezeAccount",
			Handler:    _AccountService_UnfreezeAccount_Handler,
		},
		{
			MethodName: "DeleteAccount",
			Handler:    _AccountService_DeleteAccount_Handler,
		},
		{
			MethodName: "RecoverAccount",
			Handler:    _AccountService_RecoverAccount_Handler,
		},
		{
			MethodName: "GetSecurityLastUpdateTime",
			Handler:    _AccountService_GetSecurityLastUpdateTime_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/service/account/v1/account_service.proto",
}
