// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/service/auto_message/v1/auto_message_task_service.proto

package automessagesvcpb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// AutoMessageTaskServiceClient is the client API for AutoMessageTaskService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type AutoMessageTaskServiceClient interface {
	// batch upsert auto message task
	BatchUpsertAutoMessageTask(ctx context.Context, in *BatchUpsertAutoMessageTaskRequest, opts ...grpc.CallOption) (*BatchUpsertAutoMessageTaskResponse, error)
	// list auto message task
	ListAutoMessageTask(ctx context.Context, in *ListAutoMessageTaskRequest, opts ...grpc.CallOption) (*ListAutoMessageTaskResponse, error)
	// run auto message task
	RunAutoMessageTask(ctx context.Context, in *RunAutoMessageTaskRequest, opts ...grpc.CallOption) (*RunAutoMessageTaskResponse, error)
}

type autoMessageTaskServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewAutoMessageTaskServiceClient(cc grpc.ClientConnInterface) AutoMessageTaskServiceClient {
	return &autoMessageTaskServiceClient{cc}
}

func (c *autoMessageTaskServiceClient) BatchUpsertAutoMessageTask(ctx context.Context, in *BatchUpsertAutoMessageTaskRequest, opts ...grpc.CallOption) (*BatchUpsertAutoMessageTaskResponse, error) {
	out := new(BatchUpsertAutoMessageTaskResponse)
	err := c.cc.Invoke(ctx, "/moego.service.auto_message.v1.AutoMessageTaskService/BatchUpsertAutoMessageTask", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *autoMessageTaskServiceClient) ListAutoMessageTask(ctx context.Context, in *ListAutoMessageTaskRequest, opts ...grpc.CallOption) (*ListAutoMessageTaskResponse, error) {
	out := new(ListAutoMessageTaskResponse)
	err := c.cc.Invoke(ctx, "/moego.service.auto_message.v1.AutoMessageTaskService/ListAutoMessageTask", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *autoMessageTaskServiceClient) RunAutoMessageTask(ctx context.Context, in *RunAutoMessageTaskRequest, opts ...grpc.CallOption) (*RunAutoMessageTaskResponse, error) {
	out := new(RunAutoMessageTaskResponse)
	err := c.cc.Invoke(ctx, "/moego.service.auto_message.v1.AutoMessageTaskService/RunAutoMessageTask", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AutoMessageTaskServiceServer is the server API for AutoMessageTaskService service.
// All implementations must embed UnimplementedAutoMessageTaskServiceServer
// for forward compatibility
type AutoMessageTaskServiceServer interface {
	// batch upsert auto message task
	BatchUpsertAutoMessageTask(context.Context, *BatchUpsertAutoMessageTaskRequest) (*BatchUpsertAutoMessageTaskResponse, error)
	// list auto message task
	ListAutoMessageTask(context.Context, *ListAutoMessageTaskRequest) (*ListAutoMessageTaskResponse, error)
	// run auto message task
	RunAutoMessageTask(context.Context, *RunAutoMessageTaskRequest) (*RunAutoMessageTaskResponse, error)
	mustEmbedUnimplementedAutoMessageTaskServiceServer()
}

// UnimplementedAutoMessageTaskServiceServer must be embedded to have forward compatible implementations.
type UnimplementedAutoMessageTaskServiceServer struct {
}

func (UnimplementedAutoMessageTaskServiceServer) BatchUpsertAutoMessageTask(context.Context, *BatchUpsertAutoMessageTaskRequest) (*BatchUpsertAutoMessageTaskResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchUpsertAutoMessageTask not implemented")
}
func (UnimplementedAutoMessageTaskServiceServer) ListAutoMessageTask(context.Context, *ListAutoMessageTaskRequest) (*ListAutoMessageTaskResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListAutoMessageTask not implemented")
}
func (UnimplementedAutoMessageTaskServiceServer) RunAutoMessageTask(context.Context, *RunAutoMessageTaskRequest) (*RunAutoMessageTaskResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RunAutoMessageTask not implemented")
}
func (UnimplementedAutoMessageTaskServiceServer) mustEmbedUnimplementedAutoMessageTaskServiceServer() {
}

// UnsafeAutoMessageTaskServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to AutoMessageTaskServiceServer will
// result in compilation errors.
type UnsafeAutoMessageTaskServiceServer interface {
	mustEmbedUnimplementedAutoMessageTaskServiceServer()
}

func RegisterAutoMessageTaskServiceServer(s grpc.ServiceRegistrar, srv AutoMessageTaskServiceServer) {
	s.RegisterService(&AutoMessageTaskService_ServiceDesc, srv)
}

func _AutoMessageTaskService_BatchUpsertAutoMessageTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchUpsertAutoMessageTaskRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AutoMessageTaskServiceServer).BatchUpsertAutoMessageTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.auto_message.v1.AutoMessageTaskService/BatchUpsertAutoMessageTask",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AutoMessageTaskServiceServer).BatchUpsertAutoMessageTask(ctx, req.(*BatchUpsertAutoMessageTaskRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AutoMessageTaskService_ListAutoMessageTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListAutoMessageTaskRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AutoMessageTaskServiceServer).ListAutoMessageTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.auto_message.v1.AutoMessageTaskService/ListAutoMessageTask",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AutoMessageTaskServiceServer).ListAutoMessageTask(ctx, req.(*ListAutoMessageTaskRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AutoMessageTaskService_RunAutoMessageTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RunAutoMessageTaskRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AutoMessageTaskServiceServer).RunAutoMessageTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.auto_message.v1.AutoMessageTaskService/RunAutoMessageTask",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AutoMessageTaskServiceServer).RunAutoMessageTask(ctx, req.(*RunAutoMessageTaskRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// AutoMessageTaskService_ServiceDesc is the grpc.ServiceDesc for AutoMessageTaskService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var AutoMessageTaskService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.service.auto_message.v1.AutoMessageTaskService",
	HandlerType: (*AutoMessageTaskServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "BatchUpsertAutoMessageTask",
			Handler:    _AutoMessageTaskService_BatchUpsertAutoMessageTask_Handler,
		},
		{
			MethodName: "ListAutoMessageTask",
			Handler:    _AutoMessageTaskService_ListAutoMessageTask_Handler,
		},
		{
			MethodName: "RunAutoMessageTask",
			Handler:    _AutoMessageTaskService_RunAutoMessageTask_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/service/auto_message/v1/auto_message_task_service.proto",
}
