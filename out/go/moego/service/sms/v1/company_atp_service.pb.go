// @since 2023-06-20 17:13:10
// <AUTHOR> <<EMAIL>>

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/service/sms/v1/company_atp_service.proto

package smssvcpb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/sms/v1"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// query company atp status
type CompanyOneAtpStatusQueryRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the company id list
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
}

func (x *CompanyOneAtpStatusQueryRequest) Reset() {
	*x = CompanyOneAtpStatusQueryRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_sms_v1_company_atp_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CompanyOneAtpStatusQueryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CompanyOneAtpStatusQueryRequest) ProtoMessage() {}

func (x *CompanyOneAtpStatusQueryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_sms_v1_company_atp_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CompanyOneAtpStatusQueryRequest.ProtoReflect.Descriptor instead.
func (*CompanyOneAtpStatusQueryRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_sms_v1_company_atp_service_proto_rawDescGZIP(), []int{0}
}

func (x *CompanyOneAtpStatusQueryRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

// response for company atp status
type CompanyOneAtpStatusQueryResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// company atp status
	CompanyAtpStatus *v1.CompanyAtpStatusModel `protobuf:"bytes,1,opt,name=company_atp_status,json=companyAtpStatus,proto3" json:"company_atp_status,omitempty"`
}

func (x *CompanyOneAtpStatusQueryResponse) Reset() {
	*x = CompanyOneAtpStatusQueryResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_sms_v1_company_atp_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CompanyOneAtpStatusQueryResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CompanyOneAtpStatusQueryResponse) ProtoMessage() {}

func (x *CompanyOneAtpStatusQueryResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_sms_v1_company_atp_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CompanyOneAtpStatusQueryResponse.ProtoReflect.Descriptor instead.
func (*CompanyOneAtpStatusQueryResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_sms_v1_company_atp_service_proto_rawDescGZIP(), []int{1}
}

func (x *CompanyOneAtpStatusQueryResponse) GetCompanyAtpStatus() *v1.CompanyAtpStatusModel {
	if x != nil {
		return x.CompanyAtpStatus
	}
	return nil
}

// query company atp status
type CompanyMultiAtpStatusQueryRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the company id list
	CompanyIds []int64 `protobuf:"varint,1,rep,packed,name=company_ids,json=companyIds,proto3" json:"company_ids,omitempty"`
}

func (x *CompanyMultiAtpStatusQueryRequest) Reset() {
	*x = CompanyMultiAtpStatusQueryRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_sms_v1_company_atp_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CompanyMultiAtpStatusQueryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CompanyMultiAtpStatusQueryRequest) ProtoMessage() {}

func (x *CompanyMultiAtpStatusQueryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_sms_v1_company_atp_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CompanyMultiAtpStatusQueryRequest.ProtoReflect.Descriptor instead.
func (*CompanyMultiAtpStatusQueryRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_sms_v1_company_atp_service_proto_rawDescGZIP(), []int{2}
}

func (x *CompanyMultiAtpStatusQueryRequest) GetCompanyIds() []int64 {
	if x != nil {
		return x.CompanyIds
	}
	return nil
}

// response for company atp status
type CompanyMultiAtpStatusQueryResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// company atp status by company_id
	CompanyAtpStatusMap map[int64]*v1.CompanyAtpStatusModel `protobuf:"bytes,1,rep,name=company_atp_status_map,json=companyAtpStatusMap,proto3" json:"company_atp_status_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *CompanyMultiAtpStatusQueryResponse) Reset() {
	*x = CompanyMultiAtpStatusQueryResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_sms_v1_company_atp_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CompanyMultiAtpStatusQueryResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CompanyMultiAtpStatusQueryResponse) ProtoMessage() {}

func (x *CompanyMultiAtpStatusQueryResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_sms_v1_company_atp_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CompanyMultiAtpStatusQueryResponse.ProtoReflect.Descriptor instead.
func (*CompanyMultiAtpStatusQueryResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_sms_v1_company_atp_service_proto_rawDescGZIP(), []int{3}
}

func (x *CompanyMultiAtpStatusQueryResponse) GetCompanyAtpStatusMap() map[int64]*v1.CompanyAtpStatusModel {
	if x != nil {
		return x.CompanyAtpStatusMap
	}
	return nil
}

// query company atp config
type CompanyAtpConfigQueryRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the company id
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
}

func (x *CompanyAtpConfigQueryRequest) Reset() {
	*x = CompanyAtpConfigQueryRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_sms_v1_company_atp_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CompanyAtpConfigQueryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CompanyAtpConfigQueryRequest) ProtoMessage() {}

func (x *CompanyAtpConfigQueryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_sms_v1_company_atp_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CompanyAtpConfigQueryRequest.ProtoReflect.Descriptor instead.
func (*CompanyAtpConfigQueryRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_sms_v1_company_atp_service_proto_rawDescGZIP(), []int{4}
}

func (x *CompanyAtpConfigQueryRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

// company atp config response
type CompanyAtpConfigQueryResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// company atp data model
	CompanyAtpData *v1.CompanyAtpDataModel `protobuf:"bytes,1,opt,name=company_atp_data,json=companyAtpData,proto3" json:"company_atp_data,omitempty"`
}

func (x *CompanyAtpConfigQueryResponse) Reset() {
	*x = CompanyAtpConfigQueryResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_sms_v1_company_atp_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CompanyAtpConfigQueryResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CompanyAtpConfigQueryResponse) ProtoMessage() {}

func (x *CompanyAtpConfigQueryResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_sms_v1_company_atp_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CompanyAtpConfigQueryResponse.ProtoReflect.Descriptor instead.
func (*CompanyAtpConfigQueryResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_sms_v1_company_atp_service_proto_rawDescGZIP(), []int{5}
}

func (x *CompanyAtpConfigQueryResponse) GetCompanyAtpData() *v1.CompanyAtpDataModel {
	if x != nil {
		return x.CompanyAtpData
	}
	return nil
}

// submit atp config data request
type SubmitAtpDataRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the company id
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// business legal name
	BusinessName *string `protobuf:"bytes,2,opt,name=business_name,json=businessName,proto3,oneof" json:"business_name,omitempty"`
	// business legal type
	BusinessType *string `protobuf:"bytes,3,opt,name=business_type,json=businessType,proto3,oneof" json:"business_type,omitempty"`
	// business address1
	Address1 *string `protobuf:"bytes,4,opt,name=address1,proto3,oneof" json:"address1,omitempty"`
	// business address2
	Address2 *string `protobuf:"bytes,5,opt,name=address2,proto3,oneof" json:"address2,omitempty"`
	// business address city
	City *string `protobuf:"bytes,6,opt,name=city,proto3,oneof" json:"city,omitempty"`
	// business address region
	Region *string `protobuf:"bytes,7,opt,name=region,proto3,oneof" json:"region,omitempty"`
	// business address zipcode
	Zipcode *string `protobuf:"bytes,8,opt,name=zipcode,proto3,oneof" json:"zipcode,omitempty"`
	// business address lat
	Lat *string `protobuf:"bytes,9,opt,name=lat,proto3,oneof" json:"lat,omitempty"`
	// business address lng
	Lng *string `protobuf:"bytes,10,opt,name=lng,proto3,oneof" json:"lng,omitempty"`
	// business website
	Website *string `protobuf:"bytes,255,opt,name=website,proto3,oneof" json:"website,omitempty"`
	// business ein
	Ein *string `protobuf:"bytes,12,opt,name=ein,proto3,oneof" json:"ein,omitempty"`
	// business einfiles
	EinFiles []string `protobuf:"bytes,13,rep,name=ein_files,json=einFiles,proto3" json:"ein_files,omitempty"`
	// business owner first_name
	FirstName string `protobuf:"bytes,14,opt,name=first_name,json=firstName,proto3" json:"first_name,omitempty"`
	// business owner last_name
	LastName string `protobuf:"bytes,15,opt,name=last_name,json=lastName,proto3" json:"last_name,omitempty"`
	// business owner mobile phone number
	PhoneNumber string `protobuf:"bytes,16,opt,name=phone_number,json=phoneNumber,proto3" json:"phone_number,omitempty"`
	// business owner email
	Email string `protobuf:"bytes,17,opt,name=email,proto3" json:"email,omitempty"`
	// submit data with ein
	WithEin bool `protobuf:"varint,18,opt,name=with_ein,json=withEin,proto3" json:"with_ein,omitempty"`
}

func (x *SubmitAtpDataRequest) Reset() {
	*x = SubmitAtpDataRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_sms_v1_company_atp_service_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SubmitAtpDataRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubmitAtpDataRequest) ProtoMessage() {}

func (x *SubmitAtpDataRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_sms_v1_company_atp_service_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubmitAtpDataRequest.ProtoReflect.Descriptor instead.
func (*SubmitAtpDataRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_sms_v1_company_atp_service_proto_rawDescGZIP(), []int{6}
}

func (x *SubmitAtpDataRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *SubmitAtpDataRequest) GetBusinessName() string {
	if x != nil && x.BusinessName != nil {
		return *x.BusinessName
	}
	return ""
}

func (x *SubmitAtpDataRequest) GetBusinessType() string {
	if x != nil && x.BusinessType != nil {
		return *x.BusinessType
	}
	return ""
}

func (x *SubmitAtpDataRequest) GetAddress1() string {
	if x != nil && x.Address1 != nil {
		return *x.Address1
	}
	return ""
}

func (x *SubmitAtpDataRequest) GetAddress2() string {
	if x != nil && x.Address2 != nil {
		return *x.Address2
	}
	return ""
}

func (x *SubmitAtpDataRequest) GetCity() string {
	if x != nil && x.City != nil {
		return *x.City
	}
	return ""
}

func (x *SubmitAtpDataRequest) GetRegion() string {
	if x != nil && x.Region != nil {
		return *x.Region
	}
	return ""
}

func (x *SubmitAtpDataRequest) GetZipcode() string {
	if x != nil && x.Zipcode != nil {
		return *x.Zipcode
	}
	return ""
}

func (x *SubmitAtpDataRequest) GetLat() string {
	if x != nil && x.Lat != nil {
		return *x.Lat
	}
	return ""
}

func (x *SubmitAtpDataRequest) GetLng() string {
	if x != nil && x.Lng != nil {
		return *x.Lng
	}
	return ""
}

func (x *SubmitAtpDataRequest) GetWebsite() string {
	if x != nil && x.Website != nil {
		return *x.Website
	}
	return ""
}

func (x *SubmitAtpDataRequest) GetEin() string {
	if x != nil && x.Ein != nil {
		return *x.Ein
	}
	return ""
}

func (x *SubmitAtpDataRequest) GetEinFiles() []string {
	if x != nil {
		return x.EinFiles
	}
	return nil
}

func (x *SubmitAtpDataRequest) GetFirstName() string {
	if x != nil {
		return x.FirstName
	}
	return ""
}

func (x *SubmitAtpDataRequest) GetLastName() string {
	if x != nil {
		return x.LastName
	}
	return ""
}

func (x *SubmitAtpDataRequest) GetPhoneNumber() string {
	if x != nil {
		return x.PhoneNumber
	}
	return ""
}

func (x *SubmitAtpDataRequest) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *SubmitAtpDataRequest) GetWithEin() bool {
	if x != nil {
		return x.WithEin
	}
	return false
}

// submit atp data response
type SubmitAtpDataResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *SubmitAtpDataResponse) Reset() {
	*x = SubmitAtpDataResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_sms_v1_company_atp_service_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SubmitAtpDataResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubmitAtpDataResponse) ProtoMessage() {}

func (x *SubmitAtpDataResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_sms_v1_company_atp_service_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubmitAtpDataResponse.ProtoReflect.Descriptor instead.
func (*SubmitAtpDataResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_sms_v1_company_atp_service_proto_rawDescGZIP(), []int{7}
}

// retry otp message request
type NoEinRetryOTPRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the company id list
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
}

func (x *NoEinRetryOTPRequest) Reset() {
	*x = NoEinRetryOTPRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_sms_v1_company_atp_service_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NoEinRetryOTPRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NoEinRetryOTPRequest) ProtoMessage() {}

func (x *NoEinRetryOTPRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_sms_v1_company_atp_service_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NoEinRetryOTPRequest.ProtoReflect.Descriptor instead.
func (*NoEinRetryOTPRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_sms_v1_company_atp_service_proto_rawDescGZIP(), []int{8}
}

func (x *NoEinRetryOTPRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

// retry otp message response
type NoEinRetryOTPResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *NoEinRetryOTPResponse) Reset() {
	*x = NoEinRetryOTPResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_sms_v1_company_atp_service_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NoEinRetryOTPResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NoEinRetryOTPResponse) ProtoMessage() {}

func (x *NoEinRetryOTPResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_sms_v1_company_atp_service_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NoEinRetryOTPResponse.ProtoReflect.Descriptor instead.
func (*NoEinRetryOTPResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_sms_v1_company_atp_service_proto_rawDescGZIP(), []int{9}
}

// atp task
type AtpStatusCheckTaskRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *AtpStatusCheckTaskRequest) Reset() {
	*x = AtpStatusCheckTaskRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_sms_v1_company_atp_service_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AtpStatusCheckTaskRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AtpStatusCheckTaskRequest) ProtoMessage() {}

func (x *AtpStatusCheckTaskRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_sms_v1_company_atp_service_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AtpStatusCheckTaskRequest.ProtoReflect.Descriptor instead.
func (*AtpStatusCheckTaskRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_sms_v1_company_atp_service_proto_rawDescGZIP(), []int{10}
}

// retry otp message response
type AtpStatusCheckTaskResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *AtpStatusCheckTaskResponse) Reset() {
	*x = AtpStatusCheckTaskResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_sms_v1_company_atp_service_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AtpStatusCheckTaskResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AtpStatusCheckTaskResponse) ProtoMessage() {}

func (x *AtpStatusCheckTaskResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_sms_v1_company_atp_service_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AtpStatusCheckTaskResponse.ProtoReflect.Descriptor instead.
func (*AtpStatusCheckTaskResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_sms_v1_company_atp_service_proto_rawDescGZIP(), []int{11}
}

// task for twilio full resource request
type TwilioFullResourceRefreshRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *TwilioFullResourceRefreshRequest) Reset() {
	*x = TwilioFullResourceRefreshRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_sms_v1_company_atp_service_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TwilioFullResourceRefreshRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TwilioFullResourceRefreshRequest) ProtoMessage() {}

func (x *TwilioFullResourceRefreshRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_sms_v1_company_atp_service_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TwilioFullResourceRefreshRequest.ProtoReflect.Descriptor instead.
func (*TwilioFullResourceRefreshRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_sms_v1_company_atp_service_proto_rawDescGZIP(), []int{12}
}

// task for twilio full resource response
type TwilioFullResourceRefreshResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *TwilioFullResourceRefreshResponse) Reset() {
	*x = TwilioFullResourceRefreshResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_sms_v1_company_atp_service_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TwilioFullResourceRefreshResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TwilioFullResourceRefreshResponse) ProtoMessage() {}

func (x *TwilioFullResourceRefreshResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_sms_v1_company_atp_service_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TwilioFullResourceRefreshResponse.ProtoReflect.Descriptor instead.
func (*TwilioFullResourceRefreshResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_sms_v1_company_atp_service_proto_rawDescGZIP(), []int{13}
}

var File_moego_service_sms_v1_company_atp_service_proto protoreflect.FileDescriptor

var file_moego_service_sms_v1_company_atp_service_proto_rawDesc = []byte{
	0x0a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f,
	0x73, 0x6d, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x61,
	0x74, 0x70, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x12, 0x14, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x73, 0x6d, 0x73, 0x2e, 0x76, 0x31, 0x1a, 0x2c, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2f, 0x73, 0x6d, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x6f, 0x6d, 0x70,
	0x61, 0x6e, 0x79, 0x5f, 0x61, 0x74, 0x70, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76,
	0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x49, 0x0a,
	0x1f, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x4f, 0x6e, 0x65, 0x41, 0x74, 0x70, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x26, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x09, 0x63,
	0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x22, 0x7c, 0x0a, 0x20, 0x43, 0x6f, 0x6d, 0x70,
	0x61, 0x6e, 0x79, 0x4f, 0x6e, 0x65, 0x41, 0x74, 0x70, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x51,
	0x75, 0x65, 0x72, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x58, 0x0a, 0x12,
	0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x61, 0x74, 0x70, 0x5f, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x73, 0x6d, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x43,
	0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x41, 0x74, 0x70, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x4d,
	0x6f, 0x64, 0x65, 0x6c, 0x52, 0x10, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x41, 0x74, 0x70,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x57, 0x0a, 0x21, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e,
	0x79, 0x4d, 0x75, 0x6c, 0x74, 0x69, 0x41, 0x74, 0x70, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x51,
	0x75, 0x65, 0x72, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x32, 0x0a, 0x0b, 0x63,
	0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03,
	0x42, 0x11, 0xfa, 0x42, 0x0e, 0x92, 0x01, 0x0b, 0x08, 0x01, 0x10, 0xf4, 0x03, 0x22, 0x04, 0x22,
	0x02, 0x20, 0x00, 0x52, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x73, 0x22,
	0xa1, 0x02, 0x0a, 0x22, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x4d, 0x75, 0x6c, 0x74, 0x69,
	0x41, 0x74, 0x70, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x86, 0x01, 0x0a, 0x16, 0x63, 0x6f, 0x6d, 0x70, 0x61,
	0x6e, 0x79, 0x5f, 0x61, 0x74, 0x70, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x6d, 0x61,
	0x70, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x51, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x73, 0x6d, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x43,
	0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x4d, 0x75, 0x6c, 0x74, 0x69, 0x41, 0x74, 0x70, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x2e, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x41, 0x74, 0x70, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x13, 0x63, 0x6f, 0x6d, 0x70,
	0x61, 0x6e, 0x79, 0x41, 0x74, 0x70, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x4d, 0x61, 0x70, 0x1a,
	0x72, 0x0a, 0x18, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x41, 0x74, 0x70, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b,
	0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x40, 0x0a,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x73, 0x6d, 0x73, 0x2e,
	0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x41, 0x74, 0x70, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a,
	0x02, 0x38, 0x01, 0x22, 0x46, 0x0a, 0x1c, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x41, 0x74,
	0x70, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x26, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00,
	0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x22, 0x73, 0x0a, 0x1d, 0x43,
	0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x41, 0x74, 0x70, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x51,
	0x75, 0x65, 0x72, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x52, 0x0a, 0x10,
	0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x61, 0x74, 0x70, 0x5f, 0x64, 0x61, 0x74, 0x61,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x73, 0x6d, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6d,
	0x70, 0x61, 0x6e, 0x79, 0x41, 0x74, 0x70, 0x44, 0x61, 0x74, 0x61, 0x4d, 0x6f, 0x64, 0x65, 0x6c,
	0x52, 0x0e, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x41, 0x74, 0x70, 0x44, 0x61, 0x74, 0x61,
	0x22, 0xfa, 0x06, 0x0a, 0x14, 0x53, 0x75, 0x62, 0x6d, 0x69, 0x74, 0x41, 0x74, 0x70, 0x44, 0x61,
	0x74, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x26, 0x0a, 0x0a, 0x63, 0x6f, 0x6d,
	0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49,
	0x64, 0x12, 0x34, 0x0a, 0x0d, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x72, 0x05, 0x10,
	0x00, 0x18, 0xff, 0x01, 0x48, 0x00, 0x52, 0x0c, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x4e, 0x61, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x12, 0x33, 0x0a, 0x0d, 0x62, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09,
	0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x00, 0x18, 0x32, 0x48, 0x01, 0x52, 0x0c, 0x62, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x54, 0x79, 0x70, 0x65, 0x88, 0x01, 0x01, 0x12, 0x2b, 0x0a, 0x08,
	0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x31, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0a,
	0xfa, 0x42, 0x07, 0x72, 0x05, 0x10, 0x00, 0x18, 0xff, 0x01, 0x48, 0x02, 0x52, 0x08, 0x61, 0x64,
	0x64, 0x72, 0x65, 0x73, 0x73, 0x31, 0x88, 0x01, 0x01, 0x12, 0x2b, 0x0a, 0x08, 0x61, 0x64, 0x64,
	0x72, 0x65, 0x73, 0x73, 0x32, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0a, 0xfa, 0x42, 0x07,
	0x72, 0x05, 0x10, 0x00, 0x18, 0xff, 0x01, 0x48, 0x03, 0x52, 0x08, 0x61, 0x64, 0x64, 0x72, 0x65,
	0x73, 0x73, 0x32, 0x88, 0x01, 0x01, 0x12, 0x22, 0x0a, 0x04, 0x63, 0x69, 0x74, 0x79, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x00, 0x18, 0x32, 0x48,
	0x04, 0x52, 0x04, 0x63, 0x69, 0x74, 0x79, 0x88, 0x01, 0x01, 0x12, 0x26, 0x0a, 0x06, 0x72, 0x65,
	0x67, 0x69, 0x6f, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72,
	0x04, 0x10, 0x00, 0x18, 0x32, 0x48, 0x05, 0x52, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x88,
	0x01, 0x01, 0x12, 0x28, 0x0a, 0x07, 0x7a, 0x69, 0x70, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x00, 0x18, 0x14, 0x48, 0x06,
	0x52, 0x07, 0x7a, 0x69, 0x70, 0x63, 0x6f, 0x64, 0x65, 0x88, 0x01, 0x01, 0x12, 0x20, 0x0a, 0x03,
	0x6c, 0x61, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04,
	0x10, 0x00, 0x18, 0x32, 0x48, 0x07, 0x52, 0x03, 0x6c, 0x61, 0x74, 0x88, 0x01, 0x01, 0x12, 0x20,
	0x0a, 0x03, 0x6c, 0x6e, 0x67, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06,
	0x72, 0x04, 0x10, 0x00, 0x18, 0x32, 0x48, 0x08, 0x52, 0x03, 0x6c, 0x6e, 0x67, 0x88, 0x01, 0x01,
	0x12, 0x29, 0x0a, 0x07, 0x77, 0x65, 0x62, 0x73, 0x69, 0x74, 0x65, 0x18, 0xff, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x00, 0x18, 0x32, 0x48, 0x09, 0x52,
	0x07, 0x77, 0x65, 0x62, 0x73, 0x69, 0x74, 0x65, 0x88, 0x01, 0x01, 0x12, 0x20, 0x0a, 0x03, 0x65,
	0x69, 0x6e, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10,
	0x00, 0x18, 0x32, 0x48, 0x0a, 0x52, 0x03, 0x65, 0x69, 0x6e, 0x88, 0x01, 0x01, 0x12, 0x30, 0x0a,
	0x09, 0x65, 0x69, 0x6e, 0x5f, 0x66, 0x69, 0x6c, 0x65, 0x73, 0x18, 0x0d, 0x20, 0x03, 0x28, 0x09,
	0x42, 0x13, 0xfa, 0x42, 0x10, 0x92, 0x01, 0x0d, 0x08, 0x00, 0x10, 0x32, 0x22, 0x07, 0x72, 0x05,
	0x10, 0x00, 0x18, 0xff, 0x01, 0x52, 0x08, 0x65, 0x69, 0x6e, 0x46, 0x69, 0x6c, 0x65, 0x73, 0x12,
	0x28, 0x0a, 0x0a, 0x66, 0x69, 0x72, 0x73, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0e, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x00, 0x18, 0x32, 0x52, 0x09,
	0x66, 0x69, 0x72, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x26, 0x0a, 0x09, 0x6c, 0x61, 0x73,
	0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42,
	0x06, 0x72, 0x04, 0x10, 0x00, 0x18, 0x32, 0x52, 0x08, 0x6c, 0x61, 0x73, 0x74, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x2c, 0x0a, 0x0c, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65,
	0x72, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x00,
	0x18, 0x32, 0x52, 0x0b, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12,
	0x1f, 0x0a, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x11, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09,
	0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x00, 0x18, 0x32, 0x52, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c,
	0x12, 0x19, 0x0a, 0x08, 0x77, 0x69, 0x74, 0x68, 0x5f, 0x65, 0x69, 0x6e, 0x18, 0x12, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x07, 0x77, 0x69, 0x74, 0x68, 0x45, 0x69, 0x6e, 0x42, 0x10, 0x0a, 0x0e, 0x5f,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x42, 0x10, 0x0a,
	0x0e, 0x5f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x42,
	0x0b, 0x0a, 0x09, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x31, 0x42, 0x0b, 0x0a, 0x09,
	0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x32, 0x42, 0x07, 0x0a, 0x05, 0x5f, 0x63, 0x69,
	0x74, 0x79, 0x42, 0x09, 0x0a, 0x07, 0x5f, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x42, 0x0a, 0x0a,
	0x08, 0x5f, 0x7a, 0x69, 0x70, 0x63, 0x6f, 0x64, 0x65, 0x42, 0x06, 0x0a, 0x04, 0x5f, 0x6c, 0x61,
	0x74, 0x42, 0x06, 0x0a, 0x04, 0x5f, 0x6c, 0x6e, 0x67, 0x42, 0x0a, 0x0a, 0x08, 0x5f, 0x77, 0x65,
	0x62, 0x73, 0x69, 0x74, 0x65, 0x42, 0x06, 0x0a, 0x04, 0x5f, 0x65, 0x69, 0x6e, 0x22, 0x17, 0x0a,
	0x15, 0x53, 0x75, 0x62, 0x6d, 0x69, 0x74, 0x41, 0x74, 0x70, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x3e, 0x0a, 0x14, 0x4e, 0x6f, 0x45, 0x69, 0x6e, 0x52,
	0x65, 0x74, 0x72, 0x79, 0x4f, 0x54, 0x50, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x26,
	0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x09, 0x63, 0x6f, 0x6d,
	0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x22, 0x17, 0x0a, 0x15, 0x4e, 0x6f, 0x45, 0x69, 0x6e, 0x52,
	0x65, 0x74, 0x72, 0x79, 0x4f, 0x54, 0x50, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0x1b, 0x0a, 0x19, 0x41, 0x74, 0x70, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x43, 0x68, 0x65, 0x63,
	0x6b, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0x1c, 0x0a, 0x1a,
	0x41, 0x74, 0x70, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x54, 0x61,
	0x73, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x22, 0x0a, 0x20, 0x54, 0x77,
	0x69, 0x6c, 0x69, 0x6f, 0x46, 0x75, 0x6c, 0x6c, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x52, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0x23,
	0x0a, 0x21, 0x54, 0x77, 0x69, 0x6c, 0x69, 0x6f, 0x46, 0x75, 0x6c, 0x6c, 0x52, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x52, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x32, 0x96, 0x07, 0x0a, 0x11, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x41,
	0x74, 0x70, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x8a, 0x01, 0x0a, 0x19, 0x51, 0x75,
	0x65, 0x72, 0x79, 0x4f, 0x6e, 0x65, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x73, 0x41, 0x74,
	0x70, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x73, 0x6d, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x43,
	0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x4f, 0x6e, 0x65, 0x41, 0x74, 0x70, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x36,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x73,
	0x6d, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x4f, 0x6e, 0x65,
	0x41, 0x74, 0x70, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x90, 0x01, 0x0a, 0x1b, 0x51, 0x75, 0x65, 0x72, 0x79,
	0x4d, 0x75, 0x6c, 0x74, 0x69, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x73, 0x41, 0x74, 0x70,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x37, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x73, 0x6d, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f,
	0x6d, 0x70, 0x61, 0x6e, 0x79, 0x4d, 0x75, 0x6c, 0x74, 0x69, 0x41, 0x74, 0x70, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x38, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x73, 0x6d, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x4d, 0x75,
	0x6c, 0x74, 0x69, 0x41, 0x74, 0x70, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x51, 0x75, 0x65, 0x72,
	0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x80, 0x01, 0x0a, 0x15, 0x51, 0x75,
	0x65, 0x72, 0x79, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x41, 0x74, 0x70, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x12, 0x32, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2e, 0x73, 0x6d, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6d, 0x70, 0x61,
	0x6e, 0x79, 0x41, 0x74, 0x70, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x51, 0x75, 0x65, 0x72, 0x79,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x73, 0x6d, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x43,
	0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x41, 0x74, 0x70, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x51,
	0x75, 0x65, 0x72, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x6c, 0x0a, 0x11,
	0x53, 0x75, 0x62, 0x6d, 0x69, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x44, 0x61, 0x74,
	0x61, 0x12, 0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x73, 0x6d, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x75, 0x62, 0x6d, 0x69, 0x74, 0x41,
	0x74, 0x70, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2b, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x73, 0x6d,
	0x73, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x75, 0x62, 0x6d, 0x69, 0x74, 0x41, 0x74, 0x70, 0x44, 0x61,
	0x74, 0x61, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x68, 0x0a, 0x0d, 0x4e, 0x6f,
	0x45, 0x69, 0x6e, 0x52, 0x65, 0x74, 0x72, 0x79, 0x4f, 0x54, 0x50, 0x12, 0x2a, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x73, 0x6d, 0x73, 0x2e,
	0x76, 0x31, 0x2e, 0x4e, 0x6f, 0x45, 0x69, 0x6e, 0x52, 0x65, 0x74, 0x72, 0x79, 0x4f, 0x54, 0x50,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x73, 0x6d, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x4e,
	0x6f, 0x45, 0x69, 0x6e, 0x52, 0x65, 0x74, 0x72, 0x79, 0x4f, 0x54, 0x50, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x77, 0x0a, 0x12, 0x41, 0x74, 0x70, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x54, 0x61, 0x73, 0x6b, 0x12, 0x2f, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x73, 0x6d, 0x73, 0x2e, 0x76,
	0x31, 0x2e, 0x41, 0x74, 0x70, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x43, 0x68, 0x65, 0x63, 0x6b,
	0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x30, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x73, 0x6d, 0x73, 0x2e,
	0x76, 0x31, 0x2e, 0x41, 0x74, 0x70, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x43, 0x68, 0x65, 0x63,
	0x6b, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x8c, 0x01,
	0x0a, 0x19, 0x54, 0x77, 0x69, 0x6c, 0x69, 0x6f, 0x46, 0x75, 0x6c, 0x6c, 0x52, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x52, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x12, 0x36, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x73, 0x6d, 0x73, 0x2e,
	0x76, 0x31, 0x2e, 0x54, 0x77, 0x69, 0x6c, 0x69, 0x6f, 0x46, 0x75, 0x6c, 0x6c, 0x52, 0x65, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x37, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2e, 0x73, 0x6d, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x77, 0x69, 0x6c, 0x69,
	0x6f, 0x46, 0x75, 0x6c, 0x6c, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x65, 0x66,
	0x72, 0x65, 0x73, 0x68, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x74, 0x0a, 0x1c,
	0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x73, 0x6d, 0x73, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x52,
	0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f,
	0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70,
	0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75,
	0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2f, 0x73, 0x6d, 0x73, 0x2f, 0x76, 0x31, 0x3b, 0x73, 0x6d, 0x73, 0x73, 0x76, 0x63,
	0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_service_sms_v1_company_atp_service_proto_rawDescOnce sync.Once
	file_moego_service_sms_v1_company_atp_service_proto_rawDescData = file_moego_service_sms_v1_company_atp_service_proto_rawDesc
)

func file_moego_service_sms_v1_company_atp_service_proto_rawDescGZIP() []byte {
	file_moego_service_sms_v1_company_atp_service_proto_rawDescOnce.Do(func() {
		file_moego_service_sms_v1_company_atp_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_service_sms_v1_company_atp_service_proto_rawDescData)
	})
	return file_moego_service_sms_v1_company_atp_service_proto_rawDescData
}

var file_moego_service_sms_v1_company_atp_service_proto_msgTypes = make([]protoimpl.MessageInfo, 15)
var file_moego_service_sms_v1_company_atp_service_proto_goTypes = []interface{}{
	(*CompanyOneAtpStatusQueryRequest)(nil),    // 0: moego.service.sms.v1.CompanyOneAtpStatusQueryRequest
	(*CompanyOneAtpStatusQueryResponse)(nil),   // 1: moego.service.sms.v1.CompanyOneAtpStatusQueryResponse
	(*CompanyMultiAtpStatusQueryRequest)(nil),  // 2: moego.service.sms.v1.CompanyMultiAtpStatusQueryRequest
	(*CompanyMultiAtpStatusQueryResponse)(nil), // 3: moego.service.sms.v1.CompanyMultiAtpStatusQueryResponse
	(*CompanyAtpConfigQueryRequest)(nil),       // 4: moego.service.sms.v1.CompanyAtpConfigQueryRequest
	(*CompanyAtpConfigQueryResponse)(nil),      // 5: moego.service.sms.v1.CompanyAtpConfigQueryResponse
	(*SubmitAtpDataRequest)(nil),               // 6: moego.service.sms.v1.SubmitAtpDataRequest
	(*SubmitAtpDataResponse)(nil),              // 7: moego.service.sms.v1.SubmitAtpDataResponse
	(*NoEinRetryOTPRequest)(nil),               // 8: moego.service.sms.v1.NoEinRetryOTPRequest
	(*NoEinRetryOTPResponse)(nil),              // 9: moego.service.sms.v1.NoEinRetryOTPResponse
	(*AtpStatusCheckTaskRequest)(nil),          // 10: moego.service.sms.v1.AtpStatusCheckTaskRequest
	(*AtpStatusCheckTaskResponse)(nil),         // 11: moego.service.sms.v1.AtpStatusCheckTaskResponse
	(*TwilioFullResourceRefreshRequest)(nil),   // 12: moego.service.sms.v1.TwilioFullResourceRefreshRequest
	(*TwilioFullResourceRefreshResponse)(nil),  // 13: moego.service.sms.v1.TwilioFullResourceRefreshResponse
	nil,                              // 14: moego.service.sms.v1.CompanyMultiAtpStatusQueryResponse.CompanyAtpStatusMapEntry
	(*v1.CompanyAtpStatusModel)(nil), // 15: moego.models.sms.v1.CompanyAtpStatusModel
	(*v1.CompanyAtpDataModel)(nil),   // 16: moego.models.sms.v1.CompanyAtpDataModel
}
var file_moego_service_sms_v1_company_atp_service_proto_depIdxs = []int32{
	15, // 0: moego.service.sms.v1.CompanyOneAtpStatusQueryResponse.company_atp_status:type_name -> moego.models.sms.v1.CompanyAtpStatusModel
	14, // 1: moego.service.sms.v1.CompanyMultiAtpStatusQueryResponse.company_atp_status_map:type_name -> moego.service.sms.v1.CompanyMultiAtpStatusQueryResponse.CompanyAtpStatusMapEntry
	16, // 2: moego.service.sms.v1.CompanyAtpConfigQueryResponse.company_atp_data:type_name -> moego.models.sms.v1.CompanyAtpDataModel
	15, // 3: moego.service.sms.v1.CompanyMultiAtpStatusQueryResponse.CompanyAtpStatusMapEntry.value:type_name -> moego.models.sms.v1.CompanyAtpStatusModel
	0,  // 4: moego.service.sms.v1.CompanyAtpService.QueryOneCompanysAtpStatus:input_type -> moego.service.sms.v1.CompanyOneAtpStatusQueryRequest
	2,  // 5: moego.service.sms.v1.CompanyAtpService.QueryMultiCompanysAtpStatus:input_type -> moego.service.sms.v1.CompanyMultiAtpStatusQueryRequest
	4,  // 6: moego.service.sms.v1.CompanyAtpService.QueryCompanyAtpConfig:input_type -> moego.service.sms.v1.CompanyAtpConfigQueryRequest
	6,  // 7: moego.service.sms.v1.CompanyAtpService.SubmitCompanyData:input_type -> moego.service.sms.v1.SubmitAtpDataRequest
	8,  // 8: moego.service.sms.v1.CompanyAtpService.NoEinRetryOTP:input_type -> moego.service.sms.v1.NoEinRetryOTPRequest
	10, // 9: moego.service.sms.v1.CompanyAtpService.AtpStatusCheckTask:input_type -> moego.service.sms.v1.AtpStatusCheckTaskRequest
	12, // 10: moego.service.sms.v1.CompanyAtpService.TwilioFullResourceRefresh:input_type -> moego.service.sms.v1.TwilioFullResourceRefreshRequest
	1,  // 11: moego.service.sms.v1.CompanyAtpService.QueryOneCompanysAtpStatus:output_type -> moego.service.sms.v1.CompanyOneAtpStatusQueryResponse
	3,  // 12: moego.service.sms.v1.CompanyAtpService.QueryMultiCompanysAtpStatus:output_type -> moego.service.sms.v1.CompanyMultiAtpStatusQueryResponse
	5,  // 13: moego.service.sms.v1.CompanyAtpService.QueryCompanyAtpConfig:output_type -> moego.service.sms.v1.CompanyAtpConfigQueryResponse
	7,  // 14: moego.service.sms.v1.CompanyAtpService.SubmitCompanyData:output_type -> moego.service.sms.v1.SubmitAtpDataResponse
	9,  // 15: moego.service.sms.v1.CompanyAtpService.NoEinRetryOTP:output_type -> moego.service.sms.v1.NoEinRetryOTPResponse
	11, // 16: moego.service.sms.v1.CompanyAtpService.AtpStatusCheckTask:output_type -> moego.service.sms.v1.AtpStatusCheckTaskResponse
	13, // 17: moego.service.sms.v1.CompanyAtpService.TwilioFullResourceRefresh:output_type -> moego.service.sms.v1.TwilioFullResourceRefreshResponse
	11, // [11:18] is the sub-list for method output_type
	4,  // [4:11] is the sub-list for method input_type
	4,  // [4:4] is the sub-list for extension type_name
	4,  // [4:4] is the sub-list for extension extendee
	0,  // [0:4] is the sub-list for field type_name
}

func init() { file_moego_service_sms_v1_company_atp_service_proto_init() }
func file_moego_service_sms_v1_company_atp_service_proto_init() {
	if File_moego_service_sms_v1_company_atp_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_service_sms_v1_company_atp_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CompanyOneAtpStatusQueryRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_sms_v1_company_atp_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CompanyOneAtpStatusQueryResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_sms_v1_company_atp_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CompanyMultiAtpStatusQueryRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_sms_v1_company_atp_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CompanyMultiAtpStatusQueryResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_sms_v1_company_atp_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CompanyAtpConfigQueryRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_sms_v1_company_atp_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CompanyAtpConfigQueryResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_sms_v1_company_atp_service_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SubmitAtpDataRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_sms_v1_company_atp_service_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SubmitAtpDataResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_sms_v1_company_atp_service_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NoEinRetryOTPRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_sms_v1_company_atp_service_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NoEinRetryOTPResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_sms_v1_company_atp_service_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AtpStatusCheckTaskRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_sms_v1_company_atp_service_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AtpStatusCheckTaskResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_sms_v1_company_atp_service_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TwilioFullResourceRefreshRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_sms_v1_company_atp_service_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TwilioFullResourceRefreshResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_service_sms_v1_company_atp_service_proto_msgTypes[6].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_service_sms_v1_company_atp_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   15,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_service_sms_v1_company_atp_service_proto_goTypes,
		DependencyIndexes: file_moego_service_sms_v1_company_atp_service_proto_depIdxs,
		MessageInfos:      file_moego_service_sms_v1_company_atp_service_proto_msgTypes,
	}.Build()
	File_moego_service_sms_v1_company_atp_service_proto = out.File
	file_moego_service_sms_v1_company_atp_service_proto_rawDesc = nil
	file_moego_service_sms_v1_company_atp_service_proto_goTypes = nil
	file_moego_service_sms_v1_company_atp_service_proto_depIdxs = nil
}
