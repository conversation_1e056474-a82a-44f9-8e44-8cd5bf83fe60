// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/service/online_booking/v1/medication_service.proto

package onlinebookingsvcpb

import (
	v11 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/appointment/v1"
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/online_booking/v1"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Create Medication request
type CreateMedicationRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// service detail type, 1: boarding, 2: daycare
	//
	//	optional int32 service_detail_type = 3 [(validate.rules).int32 = {gt: 0}];
	//
	// Medication time.
	Time []*v1.MedicationModel_MedicationSchedule `protobuf:"bytes,4,rep,name=time,proto3" json:"time,omitempty"`
	// Medication amount, must be greater than 0.
	//
	// Deprecated: Do not use.
	Amount *float64 `protobuf:"fixed64,5,opt,name=amount,proto3,oneof" json:"amount,omitempty"`
	// Medication unit.
	Unit *string `protobuf:"bytes,6,opt,name=unit,proto3,oneof" json:"unit,omitempty"`
	// Medication name.
	MedicationName *string `protobuf:"bytes,7,opt,name=medication_name,json=medicationName,proto3,oneof" json:"medication_name,omitempty"`
	// Additional notes about the medication.
	Notes *string `protobuf:"bytes,8,opt,name=notes,proto3,oneof" json:"notes,omitempty"`
	// createdAt
	CreatedAt *timestamppb.Timestamp `protobuf:"bytes,9,opt,name=created_at,json=createdAt,proto3,oneof" json:"created_at,omitempty"`
	// updatedAt
	UpdatedAt *timestamppb.Timestamp `protobuf:"bytes,10,opt,name=updated_at,json=updatedAt,proto3,oneof" json:"updated_at,omitempty"`
	// Medication amount, such as 1.2, 1/2, 1 etc.
	AmountStr *string `protobuf:"bytes,11,opt,name=amount_str,json=amountStr,proto3,oneof" json:"amount_str,omitempty"`
	// Medication select date
	SelectedDate *v11.AppointmentPetMedicationScheduleDef_SelectedDateDef `protobuf:"bytes,12,opt,name=selected_date,json=selectedDate,proto3,oneof" json:"selected_date,omitempty"`
}

func (x *CreateMedicationRequest) Reset() {
	*x = CreateMedicationRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_online_booking_v1_medication_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateMedicationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateMedicationRequest) ProtoMessage() {}

func (x *CreateMedicationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_online_booking_v1_medication_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateMedicationRequest.ProtoReflect.Descriptor instead.
func (*CreateMedicationRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_online_booking_v1_medication_service_proto_rawDescGZIP(), []int{0}
}

func (x *CreateMedicationRequest) GetTime() []*v1.MedicationModel_MedicationSchedule {
	if x != nil {
		return x.Time
	}
	return nil
}

// Deprecated: Do not use.
func (x *CreateMedicationRequest) GetAmount() float64 {
	if x != nil && x.Amount != nil {
		return *x.Amount
	}
	return 0
}

func (x *CreateMedicationRequest) GetUnit() string {
	if x != nil && x.Unit != nil {
		return *x.Unit
	}
	return ""
}

func (x *CreateMedicationRequest) GetMedicationName() string {
	if x != nil && x.MedicationName != nil {
		return *x.MedicationName
	}
	return ""
}

func (x *CreateMedicationRequest) GetNotes() string {
	if x != nil && x.Notes != nil {
		return *x.Notes
	}
	return ""
}

func (x *CreateMedicationRequest) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *CreateMedicationRequest) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *CreateMedicationRequest) GetAmountStr() string {
	if x != nil && x.AmountStr != nil {
		return *x.AmountStr
	}
	return ""
}

func (x *CreateMedicationRequest) GetSelectedDate() *v11.AppointmentPetMedicationScheduleDef_SelectedDateDef {
	if x != nil {
		return x.SelectedDate
	}
	return nil
}

// Create Medication request list
type CreateMedicationRequestList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// values
	Values []*CreateMedicationRequest `protobuf:"bytes,1,rep,name=values,proto3" json:"values,omitempty"`
}

func (x *CreateMedicationRequestList) Reset() {
	*x = CreateMedicationRequestList{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_online_booking_v1_medication_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateMedicationRequestList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateMedicationRequestList) ProtoMessage() {}

func (x *CreateMedicationRequestList) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_online_booking_v1_medication_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateMedicationRequestList.ProtoReflect.Descriptor instead.
func (*CreateMedicationRequestList) Descriptor() ([]byte, []int) {
	return file_moego_service_online_booking_v1_medication_service_proto_rawDescGZIP(), []int{1}
}

func (x *CreateMedicationRequestList) GetValues() []*CreateMedicationRequest {
	if x != nil {
		return x.Values
	}
	return nil
}

var File_moego_service_online_booking_v1_medication_service_proto protoreflect.FileDescriptor

var file_moego_service_online_booking_v1_medication_service_proto_rawDesc = []byte{
	0x0a, 0x38, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f,
	0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2f, 0x76,
	0x31, 0x2f, 0x6d, 0x65, 0x64, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1f, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65,
	0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x1a, 0x1f, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x4a, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x61, 0x70, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x70, 0x65, 0x74, 0x5f, 0x6d, 0x65, 0x64, 0x69, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x5f, 0x64, 0x65,
	0x66, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x36, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f,
	0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x6d, 0x65, 0x64, 0x69, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64,
	0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xad, 0x05, 0x0a, 0x17, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x4d, 0x65, 0x64, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x56, 0x0a, 0x04, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x42, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e,
	0x67, 0x2e, 0x76, 0x31, 0x2e, 0x4d, 0x65, 0x64, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4d,
	0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x4d, 0x65, 0x64, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53,
	0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x52, 0x04, 0x74, 0x69, 0x6d, 0x65, 0x12, 0x1f, 0x0a,
	0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x01, 0x42, 0x02, 0x18,
	0x01, 0x48, 0x00, 0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x88, 0x01, 0x01, 0x12, 0x21,
	0x0a, 0x04, 0x75, 0x6e, 0x69, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x42, 0x08, 0xfa, 0x42,
	0x05, 0x72, 0x03, 0x18, 0x80, 0x10, 0x48, 0x01, 0x52, 0x04, 0x75, 0x6e, 0x69, 0x74, 0x88, 0x01,
	0x01, 0x12, 0x36, 0x0a, 0x0f, 0x6d, 0x65, 0x64, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x72,
	0x03, 0x18, 0x80, 0x10, 0x48, 0x02, 0x52, 0x0e, 0x6d, 0x65, 0x64, 0x69, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x12, 0x23, 0x0a, 0x05, 0x6e, 0x6f, 0x74,
	0x65, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x72, 0x03, 0x18,
	0x80, 0x10, 0x48, 0x03, 0x52, 0x05, 0x6e, 0x6f, 0x74, 0x65, 0x73, 0x88, 0x01, 0x01, 0x12, 0x3e,
	0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x48, 0x04,
	0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x88, 0x01, 0x01, 0x12, 0x3e,
	0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0a, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x48, 0x05,
	0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x88, 0x01, 0x01, 0x12, 0x2c,
	0x0a, 0x0a, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x73, 0x74, 0x72, 0x18, 0x0b, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x72, 0x03, 0x18, 0xff, 0x01, 0x48, 0x06, 0x52, 0x09,
	0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x53, 0x74, 0x72, 0x88, 0x01, 0x01, 0x12, 0x7a, 0x0a, 0x0d,
	0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x0c, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x50, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76,
	0x31, 0x2e, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x50, 0x65, 0x74,
	0x4d, 0x65, 0x64, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75,
	0x6c, 0x65, 0x44, 0x65, 0x66, 0x2e, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x44, 0x61,
	0x74, 0x65, 0x44, 0x65, 0x66, 0x48, 0x07, 0x52, 0x0c, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65,
	0x64, 0x44, 0x61, 0x74, 0x65, 0x88, 0x01, 0x01, 0x42, 0x09, 0x0a, 0x07, 0x5f, 0x61, 0x6d, 0x6f,
	0x75, 0x6e, 0x74, 0x42, 0x07, 0x0a, 0x05, 0x5f, 0x75, 0x6e, 0x69, 0x74, 0x42, 0x12, 0x0a, 0x10,
	0x5f, 0x6d, 0x65, 0x64, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x42, 0x08, 0x0a, 0x06, 0x5f, 0x6e, 0x6f, 0x74, 0x65, 0x73, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x75, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x61, 0x6d, 0x6f,
	0x75, 0x6e, 0x74, 0x5f, 0x73, 0x74, 0x72, 0x42, 0x10, 0x0a, 0x0e, 0x5f, 0x73, 0x65, 0x6c, 0x65,
	0x63, 0x74, 0x65, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x22, 0x6f, 0x0a, 0x1b, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x4d, 0x65, 0x64, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x50, 0x0a, 0x06, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x38, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f,
	0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x4d, 0x65, 0x64, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x52, 0x06, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x32, 0x13, 0x0a, 0x11, 0x4d, 0x65,
	0x64, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x42,
	0x94, 0x01, 0x0a, 0x27, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64,
	0x6c, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65,
	0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x67, 0x67,
	0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c,
	0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69,
	0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74,
	0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2f, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67,
	0x2f, 0x76, 0x31, 0x3b, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e,
	0x67, 0x73, 0x76, 0x63, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_service_online_booking_v1_medication_service_proto_rawDescOnce sync.Once
	file_moego_service_online_booking_v1_medication_service_proto_rawDescData = file_moego_service_online_booking_v1_medication_service_proto_rawDesc
)

func file_moego_service_online_booking_v1_medication_service_proto_rawDescGZIP() []byte {
	file_moego_service_online_booking_v1_medication_service_proto_rawDescOnce.Do(func() {
		file_moego_service_online_booking_v1_medication_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_service_online_booking_v1_medication_service_proto_rawDescData)
	})
	return file_moego_service_online_booking_v1_medication_service_proto_rawDescData
}

var file_moego_service_online_booking_v1_medication_service_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_moego_service_online_booking_v1_medication_service_proto_goTypes = []interface{}{
	(*CreateMedicationRequest)(nil),                                 // 0: moego.service.online_booking.v1.CreateMedicationRequest
	(*CreateMedicationRequestList)(nil),                             // 1: moego.service.online_booking.v1.CreateMedicationRequestList
	(*v1.MedicationModel_MedicationSchedule)(nil),                   // 2: moego.models.online_booking.v1.MedicationModel.MedicationSchedule
	(*timestamppb.Timestamp)(nil),                                   // 3: google.protobuf.Timestamp
	(*v11.AppointmentPetMedicationScheduleDef_SelectedDateDef)(nil), // 4: moego.models.appointment.v1.AppointmentPetMedicationScheduleDef.SelectedDateDef
}
var file_moego_service_online_booking_v1_medication_service_proto_depIdxs = []int32{
	2, // 0: moego.service.online_booking.v1.CreateMedicationRequest.time:type_name -> moego.models.online_booking.v1.MedicationModel.MedicationSchedule
	3, // 1: moego.service.online_booking.v1.CreateMedicationRequest.created_at:type_name -> google.protobuf.Timestamp
	3, // 2: moego.service.online_booking.v1.CreateMedicationRequest.updated_at:type_name -> google.protobuf.Timestamp
	4, // 3: moego.service.online_booking.v1.CreateMedicationRequest.selected_date:type_name -> moego.models.appointment.v1.AppointmentPetMedicationScheduleDef.SelectedDateDef
	0, // 4: moego.service.online_booking.v1.CreateMedicationRequestList.values:type_name -> moego.service.online_booking.v1.CreateMedicationRequest
	5, // [5:5] is the sub-list for method output_type
	5, // [5:5] is the sub-list for method input_type
	5, // [5:5] is the sub-list for extension type_name
	5, // [5:5] is the sub-list for extension extendee
	0, // [0:5] is the sub-list for field type_name
}

func init() { file_moego_service_online_booking_v1_medication_service_proto_init() }
func file_moego_service_online_booking_v1_medication_service_proto_init() {
	if File_moego_service_online_booking_v1_medication_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_service_online_booking_v1_medication_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateMedicationRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_online_booking_v1_medication_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateMedicationRequestList); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_service_online_booking_v1_medication_service_proto_msgTypes[0].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_service_online_booking_v1_medication_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_service_online_booking_v1_medication_service_proto_goTypes,
		DependencyIndexes: file_moego_service_online_booking_v1_medication_service_proto_depIdxs,
		MessageInfos:      file_moego_service_online_booking_v1_medication_service_proto_msgTypes,
	}.Build()
	File_moego_service_online_booking_v1_medication_service_proto = out.File
	file_moego_service_online_booking_v1_medication_service_proto_rawDesc = nil
	file_moego_service_online_booking_v1_medication_service_proto_goTypes = nil
	file_moego_service_online_booking_v1_medication_service_proto_depIdxs = nil
}
