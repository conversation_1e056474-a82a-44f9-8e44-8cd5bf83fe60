// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/service/ws/v1/ws_service.proto

package wssvcpb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// WsServiceClient is the client API for WsService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type WsServiceClient interface {
	// push new message
	Push(ctx context.Context, in *PushRequest, opts ...grpc.CallOption) (*PushResponse, error)
}

type wsServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewWsServiceClient(cc grpc.ClientConnInterface) WsServiceClient {
	return &wsServiceClient{cc}
}

func (c *wsServiceClient) Push(ctx context.Context, in *PushRequest, opts ...grpc.CallOption) (*PushResponse, error) {
	out := new(PushResponse)
	err := c.cc.Invoke(ctx, "/moego.service.ws.v1.WsService/Push", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// WsServiceServer is the server API for WsService service.
// All implementations must embed UnimplementedWsServiceServer
// for forward compatibility
type WsServiceServer interface {
	// push new message
	Push(context.Context, *PushRequest) (*PushResponse, error)
	mustEmbedUnimplementedWsServiceServer()
}

// UnimplementedWsServiceServer must be embedded to have forward compatible implementations.
type UnimplementedWsServiceServer struct {
}

func (UnimplementedWsServiceServer) Push(context.Context, *PushRequest) (*PushResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Push not implemented")
}
func (UnimplementedWsServiceServer) mustEmbedUnimplementedWsServiceServer() {}

// UnsafeWsServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to WsServiceServer will
// result in compilation errors.
type UnsafeWsServiceServer interface {
	mustEmbedUnimplementedWsServiceServer()
}

func RegisterWsServiceServer(s grpc.ServiceRegistrar, srv WsServiceServer) {
	s.RegisterService(&WsService_ServiceDesc, srv)
}

func _WsService_Push_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PushRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WsServiceServer).Push(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.ws.v1.WsService/Push",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WsServiceServer).Push(ctx, req.(*PushRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// WsService_ServiceDesc is the grpc.ServiceDesc for WsService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var WsService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.service.ws.v1.WsService",
	HandlerType: (*WsServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Push",
			Handler:    _WsService_Push_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/service/ws/v1/ws_service.proto",
}
