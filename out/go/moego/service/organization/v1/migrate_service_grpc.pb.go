// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/service/organization/v1/migrate_service.proto

package organizationsvcpb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// MigrateServiceClient is the client API for MigrateService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type MigrateServiceClient interface {
	// get company migrate status
	GetMigrateStatus(ctx context.Context, in *GetMigrateStatusRequest, opts ...grpc.CallOption) (*GetMigrateStatusResponse, error)
}

type migrateServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewMigrateServiceClient(cc grpc.ClientConnInterface) MigrateServiceClient {
	return &migrateServiceClient{cc}
}

func (c *migrateServiceClient) GetMigrateStatus(ctx context.Context, in *GetMigrateStatusRequest, opts ...grpc.CallOption) (*GetMigrateStatusResponse, error) {
	out := new(GetMigrateStatusResponse)
	err := c.cc.Invoke(ctx, "/moego.service.organization.v1.MigrateService/GetMigrateStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// MigrateServiceServer is the server API for MigrateService service.
// All implementations must embed UnimplementedMigrateServiceServer
// for forward compatibility
type MigrateServiceServer interface {
	// get company migrate status
	GetMigrateStatus(context.Context, *GetMigrateStatusRequest) (*GetMigrateStatusResponse, error)
	mustEmbedUnimplementedMigrateServiceServer()
}

// UnimplementedMigrateServiceServer must be embedded to have forward compatible implementations.
type UnimplementedMigrateServiceServer struct {
}

func (UnimplementedMigrateServiceServer) GetMigrateStatus(context.Context, *GetMigrateStatusRequest) (*GetMigrateStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetMigrateStatus not implemented")
}
func (UnimplementedMigrateServiceServer) mustEmbedUnimplementedMigrateServiceServer() {}

// UnsafeMigrateServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to MigrateServiceServer will
// result in compilation errors.
type UnsafeMigrateServiceServer interface {
	mustEmbedUnimplementedMigrateServiceServer()
}

func RegisterMigrateServiceServer(s grpc.ServiceRegistrar, srv MigrateServiceServer) {
	s.RegisterService(&MigrateService_ServiceDesc, srv)
}

func _MigrateService_GetMigrateStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMigrateStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MigrateServiceServer).GetMigrateStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.organization.v1.MigrateService/GetMigrateStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MigrateServiceServer).GetMigrateStatus(ctx, req.(*GetMigrateStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// MigrateService_ServiceDesc is the grpc.ServiceDesc for MigrateService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var MigrateService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.service.organization.v1.MigrateService",
	HandlerType: (*MigrateServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetMigrateStatus",
			Handler:    _MigrateService_GetMigrateStatus_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/service/organization/v1/migrate_service.proto",
}
