// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/service/file/v2/file_service.proto

package filesvcpb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// FileServiceClient is the client API for FileService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type FileServiceClient interface {
	// get presigned url for upload file
	GetUploadPresignedUrl(ctx context.Context, in *GetUploadPresignedUrlRequest, opts ...grpc.CallOption) (*GetUploadPresignedUrlResponse, error)
	// CreateExportFile: create a file record before upload it to s3,should use UploadExportFile to upload the file
	CreateExportFile(ctx context.Context, in *CreateExportFileRequest, opts ...grpc.CallOption) (*CreateExportFileResponse, error)
	// QueryFile
	QueryFile(ctx context.Context, in *QueryFileRequest, opts ...grpc.CallOption) (*QueryFileResponse, error)
	// UploadExportFile: usually used after CreateExportFile
	UploadExportFile(ctx context.Context, in *UploadExportFileRequest, opts ...grpc.CallOption) (*UploadExportFileResponse, error)
	// UploadFile
	UploadFile(ctx context.Context, in *UploadFileRequest, opts ...grpc.CallOption) (*UploadFileResponse, error)
	// Deprecated: Do not use.
	// UpdateFileStatus: usually used after upload file with presigned URL, deprecated, please use `FlushFile`
	UpdateFileStatus(ctx context.Context, in *UpdateFileStatusRequest, opts ...grpc.CallOption) (*UpdateFileStatusResponse, error)
	// flush file status
	FlushFile(ctx context.Context, in *FlushFileRequest, opts ...grpc.CallOption) (*FlushFileResponse, error)
}

type fileServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewFileServiceClient(cc grpc.ClientConnInterface) FileServiceClient {
	return &fileServiceClient{cc}
}

func (c *fileServiceClient) GetUploadPresignedUrl(ctx context.Context, in *GetUploadPresignedUrlRequest, opts ...grpc.CallOption) (*GetUploadPresignedUrlResponse, error) {
	out := new(GetUploadPresignedUrlResponse)
	err := c.cc.Invoke(ctx, "/moego.service.file.v2.FileService/GetUploadPresignedUrl", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fileServiceClient) CreateExportFile(ctx context.Context, in *CreateExportFileRequest, opts ...grpc.CallOption) (*CreateExportFileResponse, error) {
	out := new(CreateExportFileResponse)
	err := c.cc.Invoke(ctx, "/moego.service.file.v2.FileService/CreateExportFile", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fileServiceClient) QueryFile(ctx context.Context, in *QueryFileRequest, opts ...grpc.CallOption) (*QueryFileResponse, error) {
	out := new(QueryFileResponse)
	err := c.cc.Invoke(ctx, "/moego.service.file.v2.FileService/QueryFile", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fileServiceClient) UploadExportFile(ctx context.Context, in *UploadExportFileRequest, opts ...grpc.CallOption) (*UploadExportFileResponse, error) {
	out := new(UploadExportFileResponse)
	err := c.cc.Invoke(ctx, "/moego.service.file.v2.FileService/UploadExportFile", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fileServiceClient) UploadFile(ctx context.Context, in *UploadFileRequest, opts ...grpc.CallOption) (*UploadFileResponse, error) {
	out := new(UploadFileResponse)
	err := c.cc.Invoke(ctx, "/moego.service.file.v2.FileService/UploadFile", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// Deprecated: Do not use.
func (c *fileServiceClient) UpdateFileStatus(ctx context.Context, in *UpdateFileStatusRequest, opts ...grpc.CallOption) (*UpdateFileStatusResponse, error) {
	out := new(UpdateFileStatusResponse)
	err := c.cc.Invoke(ctx, "/moego.service.file.v2.FileService/UpdateFileStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fileServiceClient) FlushFile(ctx context.Context, in *FlushFileRequest, opts ...grpc.CallOption) (*FlushFileResponse, error) {
	out := new(FlushFileResponse)
	err := c.cc.Invoke(ctx, "/moego.service.file.v2.FileService/FlushFile", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// FileServiceServer is the server API for FileService service.
// All implementations must embed UnimplementedFileServiceServer
// for forward compatibility
type FileServiceServer interface {
	// get presigned url for upload file
	GetUploadPresignedUrl(context.Context, *GetUploadPresignedUrlRequest) (*GetUploadPresignedUrlResponse, error)
	// CreateExportFile: create a file record before upload it to s3,should use UploadExportFile to upload the file
	CreateExportFile(context.Context, *CreateExportFileRequest) (*CreateExportFileResponse, error)
	// QueryFile
	QueryFile(context.Context, *QueryFileRequest) (*QueryFileResponse, error)
	// UploadExportFile: usually used after CreateExportFile
	UploadExportFile(context.Context, *UploadExportFileRequest) (*UploadExportFileResponse, error)
	// UploadFile
	UploadFile(context.Context, *UploadFileRequest) (*UploadFileResponse, error)
	// Deprecated: Do not use.
	// UpdateFileStatus: usually used after upload file with presigned URL, deprecated, please use `FlushFile`
	UpdateFileStatus(context.Context, *UpdateFileStatusRequest) (*UpdateFileStatusResponse, error)
	// flush file status
	FlushFile(context.Context, *FlushFileRequest) (*FlushFileResponse, error)
	mustEmbedUnimplementedFileServiceServer()
}

// UnimplementedFileServiceServer must be embedded to have forward compatible implementations.
type UnimplementedFileServiceServer struct {
}

func (UnimplementedFileServiceServer) GetUploadPresignedUrl(context.Context, *GetUploadPresignedUrlRequest) (*GetUploadPresignedUrlResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUploadPresignedUrl not implemented")
}
func (UnimplementedFileServiceServer) CreateExportFile(context.Context, *CreateExportFileRequest) (*CreateExportFileResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateExportFile not implemented")
}
func (UnimplementedFileServiceServer) QueryFile(context.Context, *QueryFileRequest) (*QueryFileResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryFile not implemented")
}
func (UnimplementedFileServiceServer) UploadExportFile(context.Context, *UploadExportFileRequest) (*UploadExportFileResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UploadExportFile not implemented")
}
func (UnimplementedFileServiceServer) UploadFile(context.Context, *UploadFileRequest) (*UploadFileResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UploadFile not implemented")
}
func (UnimplementedFileServiceServer) UpdateFileStatus(context.Context, *UpdateFileStatusRequest) (*UpdateFileStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateFileStatus not implemented")
}
func (UnimplementedFileServiceServer) FlushFile(context.Context, *FlushFileRequest) (*FlushFileResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FlushFile not implemented")
}
func (UnimplementedFileServiceServer) mustEmbedUnimplementedFileServiceServer() {}

// UnsafeFileServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to FileServiceServer will
// result in compilation errors.
type UnsafeFileServiceServer interface {
	mustEmbedUnimplementedFileServiceServer()
}

func RegisterFileServiceServer(s grpc.ServiceRegistrar, srv FileServiceServer) {
	s.RegisterService(&FileService_ServiceDesc, srv)
}

func _FileService_GetUploadPresignedUrl_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUploadPresignedUrlRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FileServiceServer).GetUploadPresignedUrl(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.file.v2.FileService/GetUploadPresignedUrl",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FileServiceServer).GetUploadPresignedUrl(ctx, req.(*GetUploadPresignedUrlRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FileService_CreateExportFile_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateExportFileRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FileServiceServer).CreateExportFile(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.file.v2.FileService/CreateExportFile",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FileServiceServer).CreateExportFile(ctx, req.(*CreateExportFileRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FileService_QueryFile_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryFileRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FileServiceServer).QueryFile(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.file.v2.FileService/QueryFile",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FileServiceServer).QueryFile(ctx, req.(*QueryFileRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FileService_UploadExportFile_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UploadExportFileRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FileServiceServer).UploadExportFile(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.file.v2.FileService/UploadExportFile",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FileServiceServer).UploadExportFile(ctx, req.(*UploadExportFileRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FileService_UploadFile_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UploadFileRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FileServiceServer).UploadFile(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.file.v2.FileService/UploadFile",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FileServiceServer).UploadFile(ctx, req.(*UploadFileRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FileService_UpdateFileStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateFileStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FileServiceServer).UpdateFileStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.file.v2.FileService/UpdateFileStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FileServiceServer).UpdateFileStatus(ctx, req.(*UpdateFileStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FileService_FlushFile_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FlushFileRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FileServiceServer).FlushFile(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.file.v2.FileService/FlushFile",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FileServiceServer).FlushFile(ctx, req.(*FlushFileRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// FileService_ServiceDesc is the grpc.ServiceDesc for FileService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var FileService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.service.file.v2.FileService",
	HandlerType: (*FileServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetUploadPresignedUrl",
			Handler:    _FileService_GetUploadPresignedUrl_Handler,
		},
		{
			MethodName: "CreateExportFile",
			Handler:    _FileService_CreateExportFile_Handler,
		},
		{
			MethodName: "QueryFile",
			Handler:    _FileService_QueryFile_Handler,
		},
		{
			MethodName: "UploadExportFile",
			Handler:    _FileService_UploadExportFile_Handler,
		},
		{
			MethodName: "UploadFile",
			Handler:    _FileService_UploadFile_Handler,
		},
		{
			MethodName: "UpdateFileStatus",
			Handler:    _FileService_UpdateFileStatus_Handler,
		},
		{
			MethodName: "FlushFile",
			Handler:    _FileService_FlushFile_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/service/file/v2/file_service.proto",
}
