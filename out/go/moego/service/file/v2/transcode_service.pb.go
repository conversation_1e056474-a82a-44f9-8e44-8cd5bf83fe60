// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/service/file/v2/transcode_service.proto

package filesvcpb

import (
	v2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/file/v2"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// GetJobRequest
type GetJobRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// job id
	JobId int64 `protobuf:"varint,1,opt,name=job_id,json=jobId,proto3" json:"job_id,omitempty"`
}

func (x *GetJobRequest) Reset() {
	*x = GetJobRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_file_v2_transcode_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetJobRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetJobRequest) ProtoMessage() {}

func (x *GetJobRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_file_v2_transcode_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetJobRequest.ProtoReflect.Descriptor instead.
func (*GetJobRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_file_v2_transcode_service_proto_rawDescGZIP(), []int{0}
}

func (x *GetJobRequest) GetJobId() int64 {
	if x != nil {
		return x.JobId
	}
	return 0
}

// GetJobResponse
type GetJobResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// job record
	Job *v2.JobModel `protobuf:"bytes,1,opt,name=job,proto3" json:"job,omitempty"`
}

func (x *GetJobResponse) Reset() {
	*x = GetJobResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_file_v2_transcode_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetJobResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetJobResponse) ProtoMessage() {}

func (x *GetJobResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_file_v2_transcode_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetJobResponse.ProtoReflect.Descriptor instead.
func (*GetJobResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_file_v2_transcode_service_proto_rawDescGZIP(), []int{1}
}

func (x *GetJobResponse) GetJob() *v2.JobModel {
	if x != nil {
		return x.Job
	}
	return nil
}

// CancelJobRequest
type CancelJobRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// job id
	JobId int64 `protobuf:"varint,1,opt,name=job_id,json=jobId,proto3" json:"job_id,omitempty"`
}

func (x *CancelJobRequest) Reset() {
	*x = CancelJobRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_file_v2_transcode_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CancelJobRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CancelJobRequest) ProtoMessage() {}

func (x *CancelJobRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_file_v2_transcode_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CancelJobRequest.ProtoReflect.Descriptor instead.
func (*CancelJobRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_file_v2_transcode_service_proto_rawDescGZIP(), []int{2}
}

func (x *CancelJobRequest) GetJobId() int64 {
	if x != nil {
		return x.JobId
	}
	return 0
}

// CancelJobResponse
type CancelJobResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// job record
	Job *v2.JobModel `protobuf:"bytes,1,opt,name=job,proto3" json:"job,omitempty"`
}

func (x *CancelJobResponse) Reset() {
	*x = CancelJobResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_file_v2_transcode_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CancelJobResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CancelJobResponse) ProtoMessage() {}

func (x *CancelJobResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_file_v2_transcode_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CancelJobResponse.ProtoReflect.Descriptor instead.
func (*CancelJobResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_file_v2_transcode_service_proto_rawDescGZIP(), []int{3}
}

func (x *CancelJobResponse) GetJob() *v2.JobModel {
	if x != nil {
		return x.Job
	}
	return nil
}

// CreateJobRequest
type CreateJobRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// account id for the creator
	CreatorId *int64 `protobuf:"varint,1,opt,name=creator_id,json=creatorId,proto3,oneof" json:"creator_id,omitempty"`
	// owner type,eg:staff,pet...
	OwnerType *string `protobuf:"bytes,2,opt,name=owner_type,json=ownerType,proto3,oneof" json:"owner_type,omitempty"`
	// owner id
	OwnerId *int64 `protobuf:"varint,3,opt,name=owner_id,json=ownerId,proto3,oneof" json:"owner_id,omitempty"`
	// source
	//
	// Types that are assignable to Source:
	//
	//	*CreateJobRequest_Platform
	//	*CreateJobRequest_Tenant
	Source isCreateJobRequest_Source `protobuf_oneof:"source"`
	// input source
	InputSource *v2.InputSource `protobuf:"bytes,6,opt,name=input_source,json=inputSource,proto3" json:"input_source,omitempty"`
	// input settings
	InputSettings *v2.InputSettings `protobuf:"bytes,7,opt,name=input_settings,json=inputSettings,proto3,oneof" json:"input_settings,omitempty"`
	// Note: Both parameters `template_id` and `output_groups` are optional, and you can specify neither.
	// Usually, you only need to specify one of them. We recommend specifying parameter `template_id`.
	// If both are specified, parameter `template_id` will be used first, and parameter `output_groups` will be ignored.
	// If neither is specified, the service will use the default settings for transcoding.
	TemplateId *string `protobuf:"bytes,8,opt,name=template_id,json=templateId,proto3,oneof" json:"template_id,omitempty"`
	// custom output settings
	OutputGroups []*v2.OutputGroup `protobuf:"bytes,9,rep,name=output_groups,json=outputGroups,proto3" json:"output_groups,omitempty"`
}

func (x *CreateJobRequest) Reset() {
	*x = CreateJobRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_file_v2_transcode_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateJobRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateJobRequest) ProtoMessage() {}

func (x *CreateJobRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_file_v2_transcode_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateJobRequest.ProtoReflect.Descriptor instead.
func (*CreateJobRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_file_v2_transcode_service_proto_rawDescGZIP(), []int{4}
}

func (x *CreateJobRequest) GetCreatorId() int64 {
	if x != nil && x.CreatorId != nil {
		return *x.CreatorId
	}
	return 0
}

func (x *CreateJobRequest) GetOwnerType() string {
	if x != nil && x.OwnerType != nil {
		return *x.OwnerType
	}
	return ""
}

func (x *CreateJobRequest) GetOwnerId() int64 {
	if x != nil && x.OwnerId != nil {
		return *x.OwnerId
	}
	return 0
}

func (m *CreateJobRequest) GetSource() isCreateJobRequest_Source {
	if m != nil {
		return m.Source
	}
	return nil
}

func (x *CreateJobRequest) GetPlatform() *v2.PlatformSourceDef {
	if x, ok := x.GetSource().(*CreateJobRequest_Platform); ok {
		return x.Platform
	}
	return nil
}

func (x *CreateJobRequest) GetTenant() *v2.TenantSourceDef {
	if x, ok := x.GetSource().(*CreateJobRequest_Tenant); ok {
		return x.Tenant
	}
	return nil
}

func (x *CreateJobRequest) GetInputSource() *v2.InputSource {
	if x != nil {
		return x.InputSource
	}
	return nil
}

func (x *CreateJobRequest) GetInputSettings() *v2.InputSettings {
	if x != nil {
		return x.InputSettings
	}
	return nil
}

func (x *CreateJobRequest) GetTemplateId() string {
	if x != nil && x.TemplateId != nil {
		return *x.TemplateId
	}
	return ""
}

func (x *CreateJobRequest) GetOutputGroups() []*v2.OutputGroup {
	if x != nil {
		return x.OutputGroups
	}
	return nil
}

type isCreateJobRequest_Source interface {
	isCreateJobRequest_Source()
}

type CreateJobRequest_Platform struct {
	// platform
	Platform *v2.PlatformSourceDef `protobuf:"bytes,4,opt,name=platform,proto3,oneof"`
}

type CreateJobRequest_Tenant struct {
	// tenant
	Tenant *v2.TenantSourceDef `protobuf:"bytes,5,opt,name=tenant,proto3,oneof"`
}

func (*CreateJobRequest_Platform) isCreateJobRequest_Source() {}

func (*CreateJobRequest_Tenant) isCreateJobRequest_Source() {}

// CreateJobResponse
type CreateJobResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// job record
	Job *v2.JobModel `protobuf:"bytes,1,opt,name=job,proto3" json:"job,omitempty"`
}

func (x *CreateJobResponse) Reset() {
	*x = CreateJobResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_file_v2_transcode_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateJobResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateJobResponse) ProtoMessage() {}

func (x *CreateJobResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_file_v2_transcode_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateJobResponse.ProtoReflect.Descriptor instead.
func (*CreateJobResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_file_v2_transcode_service_proto_rawDescGZIP(), []int{5}
}

func (x *CreateJobResponse) GetJob() *v2.JobModel {
	if x != nil {
		return x.Job
	}
	return nil
}

// UpdateJobRequest
type UpdateJobRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// job identity
	//
	// Types that are assignable to JobIdentity:
	//
	//	*UpdateJobRequest_JobId
	//	*UpdateJobRequest_AwsMediaConvertJobId
	JobIdentity isUpdateJobRequest_JobIdentity `protobuf_oneof:"job_identity"`
	// job status
	Status *v2.JobStatus `protobuf:"varint,7,opt,name=status,proto3,enum=moego.models.file.v2.JobStatus,oneof" json:"status,omitempty"`
	// output manifest
	Manifests []*v2.OutputManifests `protobuf:"bytes,8,rep,name=manifests,proto3" json:"manifests,omitempty"`
}

func (x *UpdateJobRequest) Reset() {
	*x = UpdateJobRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_file_v2_transcode_service_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateJobRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateJobRequest) ProtoMessage() {}

func (x *UpdateJobRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_file_v2_transcode_service_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateJobRequest.ProtoReflect.Descriptor instead.
func (*UpdateJobRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_file_v2_transcode_service_proto_rawDescGZIP(), []int{6}
}

func (m *UpdateJobRequest) GetJobIdentity() isUpdateJobRequest_JobIdentity {
	if m != nil {
		return m.JobIdentity
	}
	return nil
}

func (x *UpdateJobRequest) GetJobId() int64 {
	if x, ok := x.GetJobIdentity().(*UpdateJobRequest_JobId); ok {
		return x.JobId
	}
	return 0
}

func (x *UpdateJobRequest) GetAwsMediaConvertJobId() string {
	if x, ok := x.GetJobIdentity().(*UpdateJobRequest_AwsMediaConvertJobId); ok {
		return x.AwsMediaConvertJobId
	}
	return ""
}

func (x *UpdateJobRequest) GetStatus() v2.JobStatus {
	if x != nil && x.Status != nil {
		return *x.Status
	}
	return v2.JobStatus(0)
}

func (x *UpdateJobRequest) GetManifests() []*v2.OutputManifests {
	if x != nil {
		return x.Manifests
	}
	return nil
}

type isUpdateJobRequest_JobIdentity interface {
	isUpdateJobRequest_JobIdentity()
}

type UpdateJobRequest_JobId struct {
	// job id in database
	JobId int64 `protobuf:"varint,1,opt,name=job_id,json=jobId,proto3,oneof"`
}

type UpdateJobRequest_AwsMediaConvertJobId struct {
	// aws MediaConvert job id
	AwsMediaConvertJobId string `protobuf:"bytes,2,opt,name=aws_media_convert_job_id,json=awsMediaConvertJobId,proto3,oneof"`
}

func (*UpdateJobRequest_JobId) isUpdateJobRequest_JobIdentity() {}

func (*UpdateJobRequest_AwsMediaConvertJobId) isUpdateJobRequest_JobIdentity() {}

// UpdateJobResponse
type UpdateJobResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// job record
	Job *v2.JobModel `protobuf:"bytes,1,opt,name=job,proto3" json:"job,omitempty"`
}

func (x *UpdateJobResponse) Reset() {
	*x = UpdateJobResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_file_v2_transcode_service_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateJobResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateJobResponse) ProtoMessage() {}

func (x *UpdateJobResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_file_v2_transcode_service_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateJobResponse.ProtoReflect.Descriptor instead.
func (*UpdateJobResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_file_v2_transcode_service_proto_rawDescGZIP(), []int{7}
}

func (x *UpdateJobResponse) GetJob() *v2.JobModel {
	if x != nil {
		return x.Job
	}
	return nil
}

var File_moego_service_file_v2_transcode_service_proto protoreflect.FileDescriptor

var file_moego_service_file_v2_transcode_service_proto_rawDesc = []byte{
	0x0a, 0x2d, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f,
	0x66, 0x69, 0x6c, 0x65, 0x2f, 0x76, 0x32, 0x2f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x63, 0x6f, 0x64,
	0x65, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12,
	0x15, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x66,
	0x69, 0x6c, 0x65, 0x2e, 0x76, 0x32, 0x1a, 0x24, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2f, 0x66, 0x69, 0x6c, 0x65, 0x2f, 0x76, 0x32, 0x2f, 0x66, 0x69, 0x6c,
	0x65, 0x5f, 0x64, 0x65, 0x66, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2b, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x66, 0x69, 0x6c, 0x65, 0x2f,
	0x76, 0x32, 0x2f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x63, 0x6f, 0x64, 0x65, 0x5f, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64,
	0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x22, 0x26, 0x0a, 0x0d, 0x47, 0x65, 0x74, 0x4a, 0x6f, 0x62, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x15, 0x0a, 0x06, 0x6a, 0x6f, 0x62, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x05, 0x6a, 0x6f, 0x62, 0x49, 0x64, 0x22, 0x42, 0x0a, 0x0e, 0x47, 0x65,
	0x74, 0x4a, 0x6f, 0x62, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x30, 0x0a, 0x03,
	0x6a, 0x6f, 0x62, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x76, 0x32,
	0x2e, 0x4a, 0x6f, 0x62, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x03, 0x6a, 0x6f, 0x62, 0x22, 0x29,
	0x0a, 0x10, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x4a, 0x6f, 0x62, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x15, 0x0a, 0x06, 0x6a, 0x6f, 0x62, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x05, 0x6a, 0x6f, 0x62, 0x49, 0x64, 0x22, 0x45, 0x0a, 0x11, 0x43, 0x61, 0x6e,
	0x63, 0x65, 0x6c, 0x4a, 0x6f, 0x62, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x30,
	0x0a, 0x03, 0x6a, 0x6f, 0x62, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x66, 0x69, 0x6c, 0x65, 0x2e,
	0x76, 0x32, 0x2e, 0x4a, 0x6f, 0x62, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x03, 0x6a, 0x6f, 0x62,
	0x22, 0x81, 0x05, 0x0a, 0x10, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4a, 0x6f, 0x62, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2b, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02,
	0x20, 0x00, 0x48, 0x01, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x88,
	0x01, 0x01, 0x12, 0x2d, 0x0a, 0x0a, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18,
	0x32, 0x48, 0x02, 0x52, 0x09, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x88, 0x01,
	0x01, 0x12, 0x27, 0x0a, 0x08, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x48, 0x03, 0x52, 0x07,
	0x6f, 0x77, 0x6e, 0x65, 0x72, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x45, 0x0a, 0x08, 0x70, 0x6c,
	0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x66, 0x69, 0x6c, 0x65,
	0x2e, 0x76, 0x32, 0x2e, 0x50, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x53, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x44, 0x65, 0x66, 0x48, 0x00, 0x52, 0x08, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72,
	0x6d, 0x12, 0x3f, 0x0a, 0x06, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x25, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x76, 0x32, 0x2e, 0x54, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x53,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x44, 0x65, 0x66, 0x48, 0x00, 0x52, 0x06, 0x74, 0x65, 0x6e, 0x61,
	0x6e, 0x74, 0x12, 0x44, 0x0a, 0x0c, 0x69, 0x6e, 0x70, 0x75, 0x74, 0x5f, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x76, 0x32, 0x2e,
	0x49, 0x6e, 0x70, 0x75, 0x74, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x0b, 0x69, 0x6e, 0x70,
	0x75, 0x74, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x4f, 0x0a, 0x0e, 0x69, 0x6e, 0x70, 0x75,
	0x74, 0x5f, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x23, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x66, 0x69, 0x6c, 0x65, 0x2e, 0x76, 0x32, 0x2e, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x53, 0x65, 0x74,
	0x74, 0x69, 0x6e, 0x67, 0x73, 0x48, 0x04, 0x52, 0x0d, 0x69, 0x6e, 0x70, 0x75, 0x74, 0x53, 0x65,
	0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x88, 0x01, 0x01, 0x12, 0x24, 0x0a, 0x0b, 0x74, 0x65, 0x6d,
	0x70, 0x6c, 0x61, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x48, 0x05,
	0x52, 0x0a, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12,
	0x46, 0x0a, 0x0d, 0x6f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x73,
	0x18, 0x09, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x76, 0x32, 0x2e, 0x4f, 0x75,
	0x74, 0x70, 0x75, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x0c, 0x6f, 0x75, 0x74, 0x70, 0x75,
	0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x42, 0x0d, 0x0a, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x12, 0x03, 0xf8, 0x42, 0x01, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x6f, 0x72, 0x5f, 0x69, 0x64, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x5f, 0x69,
	0x64, 0x42, 0x11, 0x0a, 0x0f, 0x5f, 0x69, 0x6e, 0x70, 0x75, 0x74, 0x5f, 0x73, 0x65, 0x74, 0x74,
	0x69, 0x6e, 0x67, 0x73, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74,
	0x65, 0x5f, 0x69, 0x64, 0x22, 0x45, 0x0a, 0x11, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4a, 0x6f,
	0x62, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x30, 0x0a, 0x03, 0x6a, 0x6f, 0x62,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x76, 0x32, 0x2e, 0x4a, 0x6f,
	0x62, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x03, 0x6a, 0x6f, 0x62, 0x22, 0x83, 0x02, 0x0a, 0x10,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4a, 0x6f, 0x62, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x17, 0x0a, 0x06, 0x6a, 0x6f, 0x62, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x48, 0x00, 0x52, 0x05, 0x6a, 0x6f, 0x62, 0x49, 0x64, 0x12, 0x38, 0x0a, 0x18, 0x61, 0x77, 0x73,
	0x5f, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x5f, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x74, 0x5f, 0x6a,
	0x6f, 0x62, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x14, 0x61,
	0x77, 0x73, 0x4d, 0x65, 0x64, 0x69, 0x61, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x74, 0x4a, 0x6f,
	0x62, 0x49, 0x64, 0x12, 0x3c, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x76, 0x32, 0x2e, 0x4a, 0x6f, 0x62, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x48, 0x01, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x88, 0x01,
	0x01, 0x12, 0x43, 0x0a, 0x09, 0x6d, 0x61, 0x6e, 0x69, 0x66, 0x65, 0x73, 0x74, 0x73, 0x18, 0x08,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x76, 0x32, 0x2e, 0x4f, 0x75, 0x74, 0x70,
	0x75, 0x74, 0x4d, 0x61, 0x6e, 0x69, 0x66, 0x65, 0x73, 0x74, 0x73, 0x52, 0x09, 0x6d, 0x61, 0x6e,
	0x69, 0x66, 0x65, 0x73, 0x74, 0x73, 0x42, 0x0e, 0x0a, 0x0c, 0x6a, 0x6f, 0x62, 0x5f, 0x69, 0x64,
	0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x42, 0x09, 0x0a, 0x07, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x22, 0x45, 0x0a, 0x11, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4a, 0x6f, 0x62, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x30, 0x0a, 0x03, 0x6a, 0x6f, 0x62, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x76, 0x32, 0x2e, 0x4a, 0x6f, 0x62, 0x4d, 0x6f,
	0x64, 0x65, 0x6c, 0x52, 0x03, 0x6a, 0x6f, 0x62, 0x32, 0x89, 0x03, 0x0a, 0x10, 0x54, 0x72, 0x61,
	0x6e, 0x73, 0x63, 0x6f, 0x64, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x55, 0x0a,
	0x06, 0x47, 0x65, 0x74, 0x4a, 0x6f, 0x62, 0x12, 0x24, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x76, 0x32, 0x2e,
	0x47, 0x65, 0x74, 0x4a, 0x6f, 0x62, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x25, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x66, 0x69,
	0x6c, 0x65, 0x2e, 0x76, 0x32, 0x2e, 0x47, 0x65, 0x74, 0x4a, 0x6f, 0x62, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x5e, 0x0a, 0x09, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4a, 0x6f,
	0x62, 0x12, 0x27, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x76, 0x32, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x4a, 0x6f, 0x62, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x28, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x66, 0x69, 0x6c, 0x65, 0x2e,
	0x76, 0x32, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4a, 0x6f, 0x62, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x5e, 0x0a, 0x09, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4a, 0x6f,
	0x62, 0x12, 0x27, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x76, 0x32, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x4a, 0x6f, 0x62, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x28, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x66, 0x69, 0x6c, 0x65, 0x2e,
	0x76, 0x32, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4a, 0x6f, 0x62, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x5e, 0x0a, 0x09, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x4a, 0x6f,
	0x62, 0x12, 0x27, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x76, 0x32, 0x2e, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c,
	0x4a, 0x6f, 0x62, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x28, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x66, 0x69, 0x6c, 0x65, 0x2e,
	0x76, 0x32, 0x2e, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x4a, 0x6f, 0x62, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x42, 0x77, 0x0a, 0x1d, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x66, 0x69,
	0x6c, 0x65, 0x2e, 0x76, 0x32, 0x50, 0x01, 0x5a, 0x54, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e,
	0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79,
	0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f, 0x66, 0x69, 0x6c, 0x65,
	0x2f, 0x76, 0x32, 0x3b, 0x66, 0x69, 0x6c, 0x65, 0x73, 0x76, 0x63, 0x70, 0x62, 0x62, 0x06, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_service_file_v2_transcode_service_proto_rawDescOnce sync.Once
	file_moego_service_file_v2_transcode_service_proto_rawDescData = file_moego_service_file_v2_transcode_service_proto_rawDesc
)

func file_moego_service_file_v2_transcode_service_proto_rawDescGZIP() []byte {
	file_moego_service_file_v2_transcode_service_proto_rawDescOnce.Do(func() {
		file_moego_service_file_v2_transcode_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_service_file_v2_transcode_service_proto_rawDescData)
	})
	return file_moego_service_file_v2_transcode_service_proto_rawDescData
}

var file_moego_service_file_v2_transcode_service_proto_msgTypes = make([]protoimpl.MessageInfo, 8)
var file_moego_service_file_v2_transcode_service_proto_goTypes = []interface{}{
	(*GetJobRequest)(nil),        // 0: moego.service.file.v2.GetJobRequest
	(*GetJobResponse)(nil),       // 1: moego.service.file.v2.GetJobResponse
	(*CancelJobRequest)(nil),     // 2: moego.service.file.v2.CancelJobRequest
	(*CancelJobResponse)(nil),    // 3: moego.service.file.v2.CancelJobResponse
	(*CreateJobRequest)(nil),     // 4: moego.service.file.v2.CreateJobRequest
	(*CreateJobResponse)(nil),    // 5: moego.service.file.v2.CreateJobResponse
	(*UpdateJobRequest)(nil),     // 6: moego.service.file.v2.UpdateJobRequest
	(*UpdateJobResponse)(nil),    // 7: moego.service.file.v2.UpdateJobResponse
	(*v2.JobModel)(nil),          // 8: moego.models.file.v2.JobModel
	(*v2.PlatformSourceDef)(nil), // 9: moego.models.file.v2.PlatformSourceDef
	(*v2.TenantSourceDef)(nil),   // 10: moego.models.file.v2.TenantSourceDef
	(*v2.InputSource)(nil),       // 11: moego.models.file.v2.InputSource
	(*v2.InputSettings)(nil),     // 12: moego.models.file.v2.InputSettings
	(*v2.OutputGroup)(nil),       // 13: moego.models.file.v2.OutputGroup
	(v2.JobStatus)(0),            // 14: moego.models.file.v2.JobStatus
	(*v2.OutputManifests)(nil),   // 15: moego.models.file.v2.OutputManifests
}
var file_moego_service_file_v2_transcode_service_proto_depIdxs = []int32{
	8,  // 0: moego.service.file.v2.GetJobResponse.job:type_name -> moego.models.file.v2.JobModel
	8,  // 1: moego.service.file.v2.CancelJobResponse.job:type_name -> moego.models.file.v2.JobModel
	9,  // 2: moego.service.file.v2.CreateJobRequest.platform:type_name -> moego.models.file.v2.PlatformSourceDef
	10, // 3: moego.service.file.v2.CreateJobRequest.tenant:type_name -> moego.models.file.v2.TenantSourceDef
	11, // 4: moego.service.file.v2.CreateJobRequest.input_source:type_name -> moego.models.file.v2.InputSource
	12, // 5: moego.service.file.v2.CreateJobRequest.input_settings:type_name -> moego.models.file.v2.InputSettings
	13, // 6: moego.service.file.v2.CreateJobRequest.output_groups:type_name -> moego.models.file.v2.OutputGroup
	8,  // 7: moego.service.file.v2.CreateJobResponse.job:type_name -> moego.models.file.v2.JobModel
	14, // 8: moego.service.file.v2.UpdateJobRequest.status:type_name -> moego.models.file.v2.JobStatus
	15, // 9: moego.service.file.v2.UpdateJobRequest.manifests:type_name -> moego.models.file.v2.OutputManifests
	8,  // 10: moego.service.file.v2.UpdateJobResponse.job:type_name -> moego.models.file.v2.JobModel
	0,  // 11: moego.service.file.v2.TranscodeService.GetJob:input_type -> moego.service.file.v2.GetJobRequest
	4,  // 12: moego.service.file.v2.TranscodeService.CreateJob:input_type -> moego.service.file.v2.CreateJobRequest
	6,  // 13: moego.service.file.v2.TranscodeService.UpdateJob:input_type -> moego.service.file.v2.UpdateJobRequest
	2,  // 14: moego.service.file.v2.TranscodeService.CancelJob:input_type -> moego.service.file.v2.CancelJobRequest
	1,  // 15: moego.service.file.v2.TranscodeService.GetJob:output_type -> moego.service.file.v2.GetJobResponse
	5,  // 16: moego.service.file.v2.TranscodeService.CreateJob:output_type -> moego.service.file.v2.CreateJobResponse
	7,  // 17: moego.service.file.v2.TranscodeService.UpdateJob:output_type -> moego.service.file.v2.UpdateJobResponse
	3,  // 18: moego.service.file.v2.TranscodeService.CancelJob:output_type -> moego.service.file.v2.CancelJobResponse
	15, // [15:19] is the sub-list for method output_type
	11, // [11:15] is the sub-list for method input_type
	11, // [11:11] is the sub-list for extension type_name
	11, // [11:11] is the sub-list for extension extendee
	0,  // [0:11] is the sub-list for field type_name
}

func init() { file_moego_service_file_v2_transcode_service_proto_init() }
func file_moego_service_file_v2_transcode_service_proto_init() {
	if File_moego_service_file_v2_transcode_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_service_file_v2_transcode_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetJobRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_file_v2_transcode_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetJobResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_file_v2_transcode_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CancelJobRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_file_v2_transcode_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CancelJobResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_file_v2_transcode_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateJobRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_file_v2_transcode_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateJobResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_file_v2_transcode_service_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateJobRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_file_v2_transcode_service_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateJobResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_service_file_v2_transcode_service_proto_msgTypes[4].OneofWrappers = []interface{}{
		(*CreateJobRequest_Platform)(nil),
		(*CreateJobRequest_Tenant)(nil),
	}
	file_moego_service_file_v2_transcode_service_proto_msgTypes[6].OneofWrappers = []interface{}{
		(*UpdateJobRequest_JobId)(nil),
		(*UpdateJobRequest_AwsMediaConvertJobId)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_service_file_v2_transcode_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   8,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_service_file_v2_transcode_service_proto_goTypes,
		DependencyIndexes: file_moego_service_file_v2_transcode_service_proto_depIdxs,
		MessageInfos:      file_moego_service_file_v2_transcode_service_proto_msgTypes,
	}.Build()
	File_moego_service_file_v2_transcode_service_proto = out.File
	file_moego_service_file_v2_transcode_service_proto_rawDesc = nil
	file_moego_service_file_v2_transcode_service_proto_goTypes = nil
	file_moego_service_file_v2_transcode_service_proto_depIdxs = nil
}
