// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/service/appointment/v1/appointment_tracking_service.proto

package appointmentsvcpb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/appointment/v1"
	v2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v2"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	latlng "google.golang.org/genproto/googleapis/type/latlng"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// get appointment tracking request
// if not found, will init a new one
type GetOrInitAppointmentTrackingRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// appointment id
	AppointmentId int64 `protobuf:"varint,1,opt,name=appointment_id,json=appointmentId,proto3" json:"appointment_id,omitempty"`
}

func (x *GetOrInitAppointmentTrackingRequest) Reset() {
	*x = GetOrInitAppointmentTrackingRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_appointment_v1_appointment_tracking_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetOrInitAppointmentTrackingRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOrInitAppointmentTrackingRequest) ProtoMessage() {}

func (x *GetOrInitAppointmentTrackingRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_appointment_v1_appointment_tracking_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOrInitAppointmentTrackingRequest.ProtoReflect.Descriptor instead.
func (*GetOrInitAppointmentTrackingRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_appointment_v1_appointment_tracking_service_proto_rawDescGZIP(), []int{0}
}

func (x *GetOrInitAppointmentTrackingRequest) GetAppointmentId() int64 {
	if x != nil {
		return x.AppointmentId
	}
	return 0
}

// get appointment tracking response
type GetOrInitAppointmentTrackingResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// appointment tracking
	AppointmentTracking *v1.AppointmentTracking `protobuf:"bytes,1,opt,name=appointment_tracking,json=appointmentTracking,proto3" json:"appointment_tracking,omitempty"`
}

func (x *GetOrInitAppointmentTrackingResponse) Reset() {
	*x = GetOrInitAppointmentTrackingResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_appointment_v1_appointment_tracking_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetOrInitAppointmentTrackingResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOrInitAppointmentTrackingResponse) ProtoMessage() {}

func (x *GetOrInitAppointmentTrackingResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_appointment_v1_appointment_tracking_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOrInitAppointmentTrackingResponse.ProtoReflect.Descriptor instead.
func (*GetOrInitAppointmentTrackingResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_appointment_v1_appointment_tracking_service_proto_rawDescGZIP(), []int{1}
}

func (x *GetOrInitAppointmentTrackingResponse) GetAppointmentTracking() *v1.AppointmentTracking {
	if x != nil {
		return x.AppointmentTracking
	}
	return nil
}

// list appointment tracking request
type ListAppointmentTrackingRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pagination
	Pagination *v2.PaginationRequest `protobuf:"bytes,1,opt,name=pagination,proto3,oneof" json:"pagination,omitempty"`
	// filter
	Filter *ListAppointmentTrackingRequest_Filter `protobuf:"bytes,2,opt,name=filter,proto3" json:"filter,omitempty"`
}

func (x *ListAppointmentTrackingRequest) Reset() {
	*x = ListAppointmentTrackingRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_appointment_v1_appointment_tracking_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListAppointmentTrackingRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAppointmentTrackingRequest) ProtoMessage() {}

func (x *ListAppointmentTrackingRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_appointment_v1_appointment_tracking_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAppointmentTrackingRequest.ProtoReflect.Descriptor instead.
func (*ListAppointmentTrackingRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_appointment_v1_appointment_tracking_service_proto_rawDescGZIP(), []int{2}
}

func (x *ListAppointmentTrackingRequest) GetPagination() *v2.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *ListAppointmentTrackingRequest) GetFilter() *ListAppointmentTrackingRequest_Filter {
	if x != nil {
		return x.Filter
	}
	return nil
}

// list appointment tracking response
type ListAppointmentTrackingResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pagination
	Pagination *v2.PaginationResponse `protobuf:"bytes,1,opt,name=pagination,proto3,oneof" json:"pagination,omitempty"`
	// appointment tracking
	AppointmentTracking []*v1.AppointmentTracking `protobuf:"bytes,2,rep,name=appointment_tracking,json=appointmentTracking,proto3" json:"appointment_tracking,omitempty"`
}

func (x *ListAppointmentTrackingResponse) Reset() {
	*x = ListAppointmentTrackingResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_appointment_v1_appointment_tracking_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListAppointmentTrackingResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAppointmentTrackingResponse) ProtoMessage() {}

func (x *ListAppointmentTrackingResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_appointment_v1_appointment_tracking_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAppointmentTrackingResponse.ProtoReflect.Descriptor instead.
func (*ListAppointmentTrackingResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_appointment_v1_appointment_tracking_service_proto_rawDescGZIP(), []int{3}
}

func (x *ListAppointmentTrackingResponse) GetPagination() *v2.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *ListAppointmentTrackingResponse) GetAppointmentTracking() []*v1.AppointmentTracking {
	if x != nil {
		return x.AppointmentTracking
	}
	return nil
}

// upsert appointment tracking request
type UpsertAppointmentTrackingRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// appointment id
	AppointmentId int64 `protobuf:"varint,1,opt,name=appointment_id,json=appointmentId,proto3" json:"appointment_id,omitempty"`
	// update appointment tracking def
	AppointmentTracking *v1.UpdateAppointmentTrackingDef `protobuf:"bytes,2,opt,name=appointment_tracking,json=appointmentTracking,proto3" json:"appointment_tracking,omitempty"`
}

func (x *UpsertAppointmentTrackingRequest) Reset() {
	*x = UpsertAppointmentTrackingRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_appointment_v1_appointment_tracking_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpsertAppointmentTrackingRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpsertAppointmentTrackingRequest) ProtoMessage() {}

func (x *UpsertAppointmentTrackingRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_appointment_v1_appointment_tracking_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpsertAppointmentTrackingRequest.ProtoReflect.Descriptor instead.
func (*UpsertAppointmentTrackingRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_appointment_v1_appointment_tracking_service_proto_rawDescGZIP(), []int{4}
}

func (x *UpsertAppointmentTrackingRequest) GetAppointmentId() int64 {
	if x != nil {
		return x.AppointmentId
	}
	return 0
}

func (x *UpsertAppointmentTrackingRequest) GetAppointmentTracking() *v1.UpdateAppointmentTrackingDef {
	if x != nil {
		return x.AppointmentTracking
	}
	return nil
}

// upsert appointment tracking  response
type UpsertAppointmentTrackingResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// appointment tracking
	AppointmentTracking *v1.AppointmentTracking `protobuf:"bytes,1,opt,name=appointment_tracking,json=appointmentTracking,proto3" json:"appointment_tracking,omitempty"`
}

func (x *UpsertAppointmentTrackingResponse) Reset() {
	*x = UpsertAppointmentTrackingResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_appointment_v1_appointment_tracking_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpsertAppointmentTrackingResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpsertAppointmentTrackingResponse) ProtoMessage() {}

func (x *UpsertAppointmentTrackingResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_appointment_v1_appointment_tracking_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpsertAppointmentTrackingResponse.ProtoReflect.Descriptor instead.
func (*UpsertAppointmentTrackingResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_appointment_v1_appointment_tracking_service_proto_rawDescGZIP(), []int{5}
}

func (x *UpsertAppointmentTrackingResponse) GetAppointmentTracking() *v1.AppointmentTracking {
	if x != nil {
		return x.AppointmentTracking
	}
	return nil
}

// update appointment tracking request
type UpdateAppointmentTrackingRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// company
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// appointment ids
	AppointmentIds []int64 `protobuf:"varint,2,rep,packed,name=appointment_ids,json=appointmentIds,proto3" json:"appointment_ids,omitempty"`
	// update appointment tracking def
	AppointmentTracking *v1.UpdateAppointmentTrackingDef `protobuf:"bytes,3,opt,name=appointment_tracking,json=appointmentTracking,proto3" json:"appointment_tracking,omitempty"`
}

func (x *UpdateAppointmentTrackingRequest) Reset() {
	*x = UpdateAppointmentTrackingRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_appointment_v1_appointment_tracking_service_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateAppointmentTrackingRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateAppointmentTrackingRequest) ProtoMessage() {}

func (x *UpdateAppointmentTrackingRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_appointment_v1_appointment_tracking_service_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateAppointmentTrackingRequest.ProtoReflect.Descriptor instead.
func (*UpdateAppointmentTrackingRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_appointment_v1_appointment_tracking_service_proto_rawDescGZIP(), []int{6}
}

func (x *UpdateAppointmentTrackingRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *UpdateAppointmentTrackingRequest) GetAppointmentIds() []int64 {
	if x != nil {
		return x.AppointmentIds
	}
	return nil
}

func (x *UpdateAppointmentTrackingRequest) GetAppointmentTracking() *v1.UpdateAppointmentTrackingDef {
	if x != nil {
		return x.AppointmentTracking
	}
	return nil
}

// update appointment tracking response
type UpdateAppointmentTrackingResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// appointment tracking
	AppointmentTracking []*v1.AppointmentTracking `protobuf:"bytes,1,rep,name=appointment_tracking,json=appointmentTracking,proto3" json:"appointment_tracking,omitempty"`
}

func (x *UpdateAppointmentTrackingResponse) Reset() {
	*x = UpdateAppointmentTrackingResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_appointment_v1_appointment_tracking_service_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateAppointmentTrackingResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateAppointmentTrackingResponse) ProtoMessage() {}

func (x *UpdateAppointmentTrackingResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_appointment_v1_appointment_tracking_service_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateAppointmentTrackingResponse.ProtoReflect.Descriptor instead.
func (*UpdateAppointmentTrackingResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_appointment_v1_appointment_tracking_service_proto_rawDescGZIP(), []int{7}
}

func (x *UpdateAppointmentTrackingResponse) GetAppointmentTracking() []*v1.AppointmentTracking {
	if x != nil {
		return x.AppointmentTracking
	}
	return nil
}

// batch upsert appointment tracking request
type BatchUpsertAppointmentTrackingRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// company id
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// update appointment tracking def
	AppointmentTracking *v1.UpdateAppointmentTrackingDef `protobuf:"bytes,2,opt,name=appointment_tracking,json=appointmentTracking,proto3" json:"appointment_tracking,omitempty"`
	// appointment id list
	AppointmentIds []int64 `protobuf:"varint,3,rep,packed,name=appointment_ids,json=appointmentIds,proto3" json:"appointment_ids,omitempty"`
	// filter
	Filter *BatchUpsertAppointmentTrackingRequest_Filter `protobuf:"bytes,4,opt,name=filter,proto3" json:"filter,omitempty"`
}

func (x *BatchUpsertAppointmentTrackingRequest) Reset() {
	*x = BatchUpsertAppointmentTrackingRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_appointment_v1_appointment_tracking_service_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchUpsertAppointmentTrackingRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchUpsertAppointmentTrackingRequest) ProtoMessage() {}

func (x *BatchUpsertAppointmentTrackingRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_appointment_v1_appointment_tracking_service_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchUpsertAppointmentTrackingRequest.ProtoReflect.Descriptor instead.
func (*BatchUpsertAppointmentTrackingRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_appointment_v1_appointment_tracking_service_proto_rawDescGZIP(), []int{8}
}

func (x *BatchUpsertAppointmentTrackingRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *BatchUpsertAppointmentTrackingRequest) GetAppointmentTracking() *v1.UpdateAppointmentTrackingDef {
	if x != nil {
		return x.AppointmentTracking
	}
	return nil
}

func (x *BatchUpsertAppointmentTrackingRequest) GetAppointmentIds() []int64 {
	if x != nil {
		return x.AppointmentIds
	}
	return nil
}

func (x *BatchUpsertAppointmentTrackingRequest) GetFilter() *BatchUpsertAppointmentTrackingRequest_Filter {
	if x != nil {
		return x.Filter
	}
	return nil
}

// batch upsert appointment tracking response
type BatchUpsertAppointmentTrackingResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// appointment tracking
	AppointmentTracking []*v1.AppointmentTracking `protobuf:"bytes,1,rep,name=appointment_tracking,json=appointmentTracking,proto3" json:"appointment_tracking,omitempty"`
}

func (x *BatchUpsertAppointmentTrackingResponse) Reset() {
	*x = BatchUpsertAppointmentTrackingResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_appointment_v1_appointment_tracking_service_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchUpsertAppointmentTrackingResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchUpsertAppointmentTrackingResponse) ProtoMessage() {}

func (x *BatchUpsertAppointmentTrackingResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_appointment_v1_appointment_tracking_service_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchUpsertAppointmentTrackingResponse.ProtoReflect.Descriptor instead.
func (*BatchUpsertAppointmentTrackingResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_appointment_v1_appointment_tracking_service_proto_rawDescGZIP(), []int{9}
}

func (x *BatchUpsertAppointmentTrackingResponse) GetAppointmentTracking() []*v1.AppointmentTracking {
	if x != nil {
		return x.AppointmentTracking
	}
	return nil
}

// batch get or init appointment tracking request
type BatchGetOrInitAppointmentTrackingRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// company id
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// appointment id list
	AppointmentIds []int64 `protobuf:"varint,2,rep,packed,name=appointment_ids,json=appointmentIds,proto3" json:"appointment_ids,omitempty"`
}

func (x *BatchGetOrInitAppointmentTrackingRequest) Reset() {
	*x = BatchGetOrInitAppointmentTrackingRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_appointment_v1_appointment_tracking_service_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchGetOrInitAppointmentTrackingRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchGetOrInitAppointmentTrackingRequest) ProtoMessage() {}

func (x *BatchGetOrInitAppointmentTrackingRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_appointment_v1_appointment_tracking_service_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchGetOrInitAppointmentTrackingRequest.ProtoReflect.Descriptor instead.
func (*BatchGetOrInitAppointmentTrackingRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_appointment_v1_appointment_tracking_service_proto_rawDescGZIP(), []int{10}
}

func (x *BatchGetOrInitAppointmentTrackingRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *BatchGetOrInitAppointmentTrackingRequest) GetAppointmentIds() []int64 {
	if x != nil {
		return x.AppointmentIds
	}
	return nil
}

// batch get or init appointment tracking response
type BatchGetOrInitAppointmentTrackingResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// appointment tracking
	AppointmentTracking []*v1.AppointmentTracking `protobuf:"bytes,1,rep,name=appointment_tracking,json=appointmentTracking,proto3" json:"appointment_tracking,omitempty"`
}

func (x *BatchGetOrInitAppointmentTrackingResponse) Reset() {
	*x = BatchGetOrInitAppointmentTrackingResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_appointment_v1_appointment_tracking_service_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchGetOrInitAppointmentTrackingResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchGetOrInitAppointmentTrackingResponse) ProtoMessage() {}

func (x *BatchGetOrInitAppointmentTrackingResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_appointment_v1_appointment_tracking_service_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchGetOrInitAppointmentTrackingResponse.ProtoReflect.Descriptor instead.
func (*BatchGetOrInitAppointmentTrackingResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_appointment_v1_appointment_tracking_service_proto_rawDescGZIP(), []int{11}
}

func (x *BatchGetOrInitAppointmentTrackingResponse) GetAppointmentTracking() []*v1.AppointmentTracking {
	if x != nil {
		return x.AppointmentTracking
	}
	return nil
}

// sync appointment tracking request
type SyncAppointmentTrackingRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// company id
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// appointment id
	StaffId int64 `protobuf:"varint,2,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
	// coordinate
	Coordinate *latlng.LatLng `protobuf:"bytes,3,opt,name=coordinate,proto3" json:"coordinate,omitempty"`
	// device id
	DeviceId string `protobuf:"bytes,4,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
}

func (x *SyncAppointmentTrackingRequest) Reset() {
	*x = SyncAppointmentTrackingRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_appointment_v1_appointment_tracking_service_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SyncAppointmentTrackingRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SyncAppointmentTrackingRequest) ProtoMessage() {}

func (x *SyncAppointmentTrackingRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_appointment_v1_appointment_tracking_service_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SyncAppointmentTrackingRequest.ProtoReflect.Descriptor instead.
func (*SyncAppointmentTrackingRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_appointment_v1_appointment_tracking_service_proto_rawDescGZIP(), []int{12}
}

func (x *SyncAppointmentTrackingRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *SyncAppointmentTrackingRequest) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

func (x *SyncAppointmentTrackingRequest) GetCoordinate() *latlng.LatLng {
	if x != nil {
		return x.Coordinate
	}
	return nil
}

func (x *SyncAppointmentTrackingRequest) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

// sync appointment tracking response
type SyncAppointmentTrackingResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *SyncAppointmentTrackingResponse) Reset() {
	*x = SyncAppointmentTrackingResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_appointment_v1_appointment_tracking_service_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SyncAppointmentTrackingResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SyncAppointmentTrackingResponse) ProtoMessage() {}

func (x *SyncAppointmentTrackingResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_appointment_v1_appointment_tracking_service_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SyncAppointmentTrackingResponse.ProtoReflect.Descriptor instead.
func (*SyncAppointmentTrackingResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_appointment_v1_appointment_tracking_service_proto_rawDescGZIP(), []int{13}
}

// update appointment tracking StaffLocationStatus request
type UpdateStaffLocationStatusRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// company id
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// appointment id
	AppointmentId int64 `protobuf:"varint,2,opt,name=appointment_id,json=appointmentId,proto3" json:"appointment_id,omitempty"`
	// staff id
	StaffId int64 `protobuf:"varint,3,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
	// status
	StaffLocationStatus v1.StaffLocationStatus `protobuf:"varint,4,opt,name=staff_location_status,json=staffLocationStatus,proto3,enum=moego.models.appointment.v1.StaffLocationStatus" json:"staff_location_status,omitempty"`
	// staff coordinate
	StaffCoordinate *latlng.LatLng `protobuf:"bytes,5,opt,name=staff_coordinate,json=staffCoordinate,proto3,oneof" json:"staff_coordinate,omitempty"`
	// device id, must have when staff location status is change to IN_TRANSIT
	DeviceId *string `protobuf:"bytes,6,opt,name=device_id,json=deviceId,proto3,oneof" json:"device_id,omitempty"`
}

func (x *UpdateStaffLocationStatusRequest) Reset() {
	*x = UpdateStaffLocationStatusRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_appointment_v1_appointment_tracking_service_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateStaffLocationStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateStaffLocationStatusRequest) ProtoMessage() {}

func (x *UpdateStaffLocationStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_appointment_v1_appointment_tracking_service_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateStaffLocationStatusRequest.ProtoReflect.Descriptor instead.
func (*UpdateStaffLocationStatusRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_appointment_v1_appointment_tracking_service_proto_rawDescGZIP(), []int{14}
}

func (x *UpdateStaffLocationStatusRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *UpdateStaffLocationStatusRequest) GetAppointmentId() int64 {
	if x != nil {
		return x.AppointmentId
	}
	return 0
}

func (x *UpdateStaffLocationStatusRequest) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

func (x *UpdateStaffLocationStatusRequest) GetStaffLocationStatus() v1.StaffLocationStatus {
	if x != nil {
		return x.StaffLocationStatus
	}
	return v1.StaffLocationStatus(0)
}

func (x *UpdateStaffLocationStatusRequest) GetStaffCoordinate() *latlng.LatLng {
	if x != nil {
		return x.StaffCoordinate
	}
	return nil
}

func (x *UpdateStaffLocationStatusRequest) GetDeviceId() string {
	if x != nil && x.DeviceId != nil {
		return *x.DeviceId
	}
	return ""
}

// update appointment tracking status response
type UpdateStaffLocationStatusResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// appointment tracking
	AppointmentTracking *v1.AppointmentTracking `protobuf:"bytes,1,opt,name=appointment_tracking,json=appointmentTracking,proto3" json:"appointment_tracking,omitempty"`
}

func (x *UpdateStaffLocationStatusResponse) Reset() {
	*x = UpdateStaffLocationStatusResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_appointment_v1_appointment_tracking_service_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateStaffLocationStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateStaffLocationStatusResponse) ProtoMessage() {}

func (x *UpdateStaffLocationStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_appointment_v1_appointment_tracking_service_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateStaffLocationStatusResponse.ProtoReflect.Descriptor instead.
func (*UpdateStaffLocationStatusResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_appointment_v1_appointment_tracking_service_proto_rawDescGZIP(), []int{15}
}

func (x *UpdateStaffLocationStatusResponse) GetAppointmentTracking() *v1.AppointmentTracking {
	if x != nil {
		return x.AppointmentTracking
	}
	return nil
}

// filter
type ListAppointmentTrackingRequest_Filter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// appointment id,must have when pagination is not set
	AppointmentIds []int64 `protobuf:"varint,1,rep,packed,name=appointment_ids,json=appointmentIds,proto3" json:"appointment_ids,omitempty"`
	// staff location status
	StaffLocationStatuses []v1.StaffLocationStatus `protobuf:"varint,2,rep,packed,name=staff_location_statuses,json=staffLocationStatuses,proto3,enum=moego.models.appointment.v1.StaffLocationStatus" json:"staff_location_statuses,omitempty"`
	// staff id list
	LocationSharingStaffIds []int64 `protobuf:"varint,3,rep,packed,name=location_sharing_staff_ids,json=locationSharingStaffIds,proto3" json:"location_sharing_staff_ids,omitempty"`
}

func (x *ListAppointmentTrackingRequest_Filter) Reset() {
	*x = ListAppointmentTrackingRequest_Filter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_appointment_v1_appointment_tracking_service_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListAppointmentTrackingRequest_Filter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAppointmentTrackingRequest_Filter) ProtoMessage() {}

func (x *ListAppointmentTrackingRequest_Filter) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_appointment_v1_appointment_tracking_service_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAppointmentTrackingRequest_Filter.ProtoReflect.Descriptor instead.
func (*ListAppointmentTrackingRequest_Filter) Descriptor() ([]byte, []int) {
	return file_moego_service_appointment_v1_appointment_tracking_service_proto_rawDescGZIP(), []int{2, 0}
}

func (x *ListAppointmentTrackingRequest_Filter) GetAppointmentIds() []int64 {
	if x != nil {
		return x.AppointmentIds
	}
	return nil
}

func (x *ListAppointmentTrackingRequest_Filter) GetStaffLocationStatuses() []v1.StaffLocationStatus {
	if x != nil {
		return x.StaffLocationStatuses
	}
	return nil
}

func (x *ListAppointmentTrackingRequest_Filter) GetLocationSharingStaffIds() []int64 {
	if x != nil {
		return x.LocationSharingStaffIds
	}
	return nil
}

// filter
type BatchUpsertAppointmentTrackingRequest_Filter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// staff location status
	StaffLocationStatuses []v1.StaffLocationStatus `protobuf:"varint,2,rep,packed,name=staff_location_statuses,json=staffLocationStatuses,proto3,enum=moego.models.appointment.v1.StaffLocationStatus" json:"staff_location_statuses,omitempty"`
	// staff id list
	LocationSharingStaffIds []int64 `protobuf:"varint,3,rep,packed,name=location_sharing_staff_ids,json=locationSharingStaffIds,proto3" json:"location_sharing_staff_ids,omitempty"`
}

func (x *BatchUpsertAppointmentTrackingRequest_Filter) Reset() {
	*x = BatchUpsertAppointmentTrackingRequest_Filter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_appointment_v1_appointment_tracking_service_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchUpsertAppointmentTrackingRequest_Filter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchUpsertAppointmentTrackingRequest_Filter) ProtoMessage() {}

func (x *BatchUpsertAppointmentTrackingRequest_Filter) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_appointment_v1_appointment_tracking_service_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchUpsertAppointmentTrackingRequest_Filter.ProtoReflect.Descriptor instead.
func (*BatchUpsertAppointmentTrackingRequest_Filter) Descriptor() ([]byte, []int) {
	return file_moego_service_appointment_v1_appointment_tracking_service_proto_rawDescGZIP(), []int{8, 0}
}

func (x *BatchUpsertAppointmentTrackingRequest_Filter) GetStaffLocationStatuses() []v1.StaffLocationStatus {
	if x != nil {
		return x.StaffLocationStatuses
	}
	return nil
}

func (x *BatchUpsertAppointmentTrackingRequest_Filter) GetLocationSharingStaffIds() []int64 {
	if x != nil {
		return x.LocationSharingStaffIds
	}
	return nil
}

var File_moego_service_appointment_v1_appointment_tracking_service_proto protoreflect.FileDescriptor

var file_moego_service_appointment_v1_appointment_tracking_service_proto_rawDesc = []byte{
	0x0a, 0x3f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f,
	0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x61,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x6b,
	0x69, 0x6e, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x1c, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x1a,
	0x18, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x6c, 0x61, 0x74,
	0x6c, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x36, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x28, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2f, 0x76,
	0x32, 0x2f, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c,
	0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x22, 0x4c, 0x0a, 0x23, 0x47, 0x65, 0x74, 0x4f, 0x72, 0x49, 0x6e, 0x69,
	0x74, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x72, 0x61, 0x63,
	0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x25, 0x0a, 0x0e, 0x61,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x0d, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x49, 0x64, 0x22, 0x8b, 0x01, 0x0a, 0x24, 0x47, 0x65, 0x74, 0x4f, 0x72, 0x49, 0x6e, 0x69, 0x74,
	0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x72, 0x61, 0x63, 0x6b,
	0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x63, 0x0a, 0x14, 0x61,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x6b,
	0x69, 0x6e, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x13, 0x61, 0x70, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67,
	0x22, 0xda, 0x03, 0x0a, 0x1e, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x46, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x00, 0x52, 0x0a, 0x70, 0x61,
	0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x88, 0x01, 0x01, 0x12, 0x5b, 0x0a, 0x06, 0x66,
	0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x43, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x70, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x41,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69,
	0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72,
	0x52, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x1a, 0x83, 0x02, 0x0a, 0x06, 0x46, 0x69, 0x6c,
	0x74, 0x65, 0x72, 0x12, 0x34, 0x0a, 0x0f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x42, 0x0b, 0xfa, 0x42,
	0x08, 0x92, 0x01, 0x05, 0x10, 0xb8, 0x17, 0x28, 0x01, 0x52, 0x0e, 0x61, 0x70, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x73, 0x12, 0x79, 0x0a, 0x17, 0x73, 0x74, 0x61,
	0x66, 0x66, 0x5f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x30, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x61, 0x66, 0x66, 0x4c, 0x6f,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x42, 0x0f, 0xfa, 0x42,
	0x0c, 0x92, 0x01, 0x09, 0x22, 0x05, 0x82, 0x01, 0x02, 0x10, 0x01, 0x28, 0x01, 0x52, 0x15, 0x73,
	0x74, 0x61, 0x66, 0x66, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x65, 0x73, 0x12, 0x48, 0x0a, 0x1a, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x73, 0x68, 0x61, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69,
	0x64, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x03, 0x42, 0x0b, 0xfa, 0x42, 0x08, 0x92, 0x01, 0x05,
	0x10, 0xb8, 0x17, 0x28, 0x01, 0x52, 0x17, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53,
	0x68, 0x61, 0x72, 0x69, 0x6e, 0x67, 0x53, 0x74, 0x61, 0x66, 0x66, 0x49, 0x64, 0x73, 0x42, 0x0d,
	0x0a, 0x0b, 0x5f, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0xde, 0x01,
	0x0a, 0x1f, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x47, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74,
	0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x00, 0x52, 0x0a, 0x70, 0x61, 0x67,
	0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x88, 0x01, 0x01, 0x12, 0x63, 0x0a, 0x14, 0x61, 0x70,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69,
	0x6e, 0x67, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x13, 0x61, 0x70, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x42,
	0x0d, 0x0a, 0x0b, 0x5f, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0xb7,
	0x01, 0x0a, 0x20, 0x55, 0x70, 0x73, 0x65, 0x72, 0x74, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x25, 0x0a, 0x0e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x61, 0x70, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x6c, 0x0a, 0x14, 0x61, 0x70,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69,
	0x6e, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x39, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x70, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67,
	0x44, 0x65, 0x66, 0x52, 0x13, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x22, 0x88, 0x01, 0x0a, 0x21, 0x55, 0x70, 0x73,
	0x65, 0x72, 0x74, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x72,
	0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x63,
	0x0a, 0x14, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x72,
	0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x70, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x13,
	0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x72, 0x61, 0x63, 0x6b,
	0x69, 0x6e, 0x67, 0x22, 0xee, 0x01, 0x0a, 0x20, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x70,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e,
	0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x26, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70,
	0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42,
	0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64,
	0x12, 0x34, 0x0a, 0x0f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f,
	0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x03, 0x42, 0x0b, 0xfa, 0x42, 0x08, 0x92, 0x01,
	0x05, 0x08, 0x01, 0x10, 0xe8, 0x07, 0x52, 0x0e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x49, 0x64, 0x73, 0x12, 0x6c, 0x0a, 0x14, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x39, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x44, 0x65, 0x66, 0x52,
	0x13, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x72, 0x61, 0x63,
	0x6b, 0x69, 0x6e, 0x67, 0x22, 0x88, 0x01, 0x0a, 0x21, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69,
	0x6e, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x63, 0x0a, 0x14, 0x61, 0x70,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69,
	0x6e, 0x67, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x13, 0x61, 0x70, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x22,
	0xa9, 0x04, 0x0a, 0x25, 0x42, 0x61, 0x74, 0x63, 0x68, 0x55, 0x70, 0x73, 0x65, 0x72, 0x74, 0x41,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69,
	0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x26, 0x0a, 0x0a, 0x63, 0x6f, 0x6d,
	0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49,
	0x64, 0x12, 0x6c, 0x0a, 0x14, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x5f, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x39, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x54,
	0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x44, 0x65, 0x66, 0x52, 0x13, 0x61, 0x70, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x12,
	0x36, 0x0a, 0x0f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69,
	0x64, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x03, 0x42, 0x0d, 0xfa, 0x42, 0x0a, 0x92, 0x01, 0x07,
	0x08, 0x01, 0x10, 0xe8, 0x07, 0x28, 0x01, 0x52, 0x0e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x73, 0x12, 0x62, 0x0a, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65,
	0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x4a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x55, 0x70, 0x73, 0x65,
	0x72, 0x74, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x72, 0x61,
	0x63, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x46, 0x69, 0x6c,
	0x74, 0x65, 0x72, 0x52, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x1a, 0xcd, 0x01, 0x0a, 0x06,
	0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x79, 0x0a, 0x17, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f,
	0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x65,
	0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x30, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x61, 0x66, 0x66, 0x4c, 0x6f, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x42, 0x0f, 0xfa, 0x42, 0x0c, 0x92, 0x01,
	0x09, 0x22, 0x05, 0x82, 0x01, 0x02, 0x10, 0x01, 0x28, 0x01, 0x52, 0x15, 0x73, 0x74, 0x61, 0x66,
	0x66, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x65,
	0x73, 0x12, 0x48, 0x0a, 0x1a, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x68,
	0x61, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x73, 0x18,
	0x03, 0x20, 0x03, 0x28, 0x03, 0x42, 0x0b, 0xfa, 0x42, 0x08, 0x92, 0x01, 0x05, 0x10, 0xb8, 0x17,
	0x28, 0x01, 0x52, 0x17, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x68, 0x61, 0x72,
	0x69, 0x6e, 0x67, 0x53, 0x74, 0x61, 0x66, 0x66, 0x49, 0x64, 0x73, 0x22, 0x8d, 0x01, 0x0a, 0x26,
	0x42, 0x61, 0x74, 0x63, 0x68, 0x55, 0x70, 0x73, 0x65, 0x72, 0x74, 0x41, 0x70, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x63, 0x0a, 0x14, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x76, 0x31, 0x2e, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x72,
	0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x13, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x22, 0x8a, 0x01, 0x0a, 0x28,
	0x42, 0x61, 0x74, 0x63, 0x68, 0x47, 0x65, 0x74, 0x4f, 0x72, 0x49, 0x6e, 0x69, 0x74, 0x41, 0x70,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e,
	0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x26, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70,
	0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42,
	0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64,
	0x12, 0x36, 0x0a, 0x0f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f,
	0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x03, 0x42, 0x0d, 0xfa, 0x42, 0x0a, 0x92, 0x01,
	0x07, 0x08, 0x01, 0x10, 0xe8, 0x07, 0x28, 0x01, 0x52, 0x0e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x73, 0x22, 0x90, 0x01, 0x0a, 0x29, 0x42, 0x61, 0x74,
	0x63, 0x68, 0x47, 0x65, 0x74, 0x4f, 0x72, 0x49, 0x6e, 0x69, 0x74, 0x41, 0x70, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x63, 0x0a, 0x14, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x76, 0x31, 0x2e, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x72,
	0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x13, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x22, 0xbe, 0x01, 0x0a, 0x1e,
	0x53, 0x79, 0x6e, 0x63, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x54,
	0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x26,
	0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x09, 0x63, 0x6f, 0x6d,
	0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x22, 0x0a, 0x08, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20,
	0x00, 0x52, 0x07, 0x73, 0x74, 0x61, 0x66, 0x66, 0x49, 0x64, 0x12, 0x33, 0x0a, 0x0a, 0x63, 0x6f,
	0x6f, 0x72, 0x64, 0x69, 0x6e, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4c, 0x61, 0x74,
	0x4c, 0x6e, 0x67, 0x52, 0x0a, 0x63, 0x6f, 0x6f, 0x72, 0x64, 0x69, 0x6e, 0x61, 0x74, 0x65, 0x12,
	0x1b, 0x0a, 0x09, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x22, 0x21, 0x0a, 0x1f,
	0x53, 0x79, 0x6e, 0x63, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x54,
	0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0x9a, 0x03, 0x0a, 0x20, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x74, 0x61, 0x66, 0x66, 0x4c,
	0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x26, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20,
	0x00, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x2e, 0x0a, 0x0e,
	0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0d, 0x61,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x22, 0x0a, 0x08,
	0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x07, 0x73, 0x74, 0x61, 0x66, 0x66, 0x49, 0x64,
	0x12, 0x70, 0x0a, 0x15, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x30, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74,
	0x61, 0x66, 0x66, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x52, 0x13, 0x73,
	0x74, 0x61, 0x66, 0x66, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x43, 0x0a, 0x10, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x63, 0x6f, 0x6f, 0x72,
	0x64, 0x69, 0x6e, 0x61, 0x74, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4c, 0x61, 0x74, 0x4c, 0x6e,
	0x67, 0x48, 0x00, 0x52, 0x0f, 0x73, 0x74, 0x61, 0x66, 0x66, 0x43, 0x6f, 0x6f, 0x72, 0x64, 0x69,
	0x6e, 0x61, 0x74, 0x65, 0x88, 0x01, 0x01, 0x12, 0x20, 0x0a, 0x09, 0x64, 0x65, 0x76, 0x69, 0x63,
	0x65, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x48, 0x01, 0x52, 0x08, 0x64, 0x65,
	0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x88, 0x01, 0x01, 0x42, 0x13, 0x0a, 0x11, 0x5f, 0x73, 0x74,
	0x61, 0x66, 0x66, 0x5f, 0x63, 0x6f, 0x6f, 0x72, 0x64, 0x69, 0x6e, 0x61, 0x74, 0x65, 0x42, 0x0c,
	0x0a, 0x0a, 0x5f, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x22, 0x88, 0x01, 0x0a,
	0x21, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x74, 0x61, 0x66, 0x66, 0x4c, 0x6f, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x63, 0x0a, 0x14, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x30, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x41,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69,
	0x6e, 0x67, 0x52, 0x13, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x54,
	0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x32, 0xb8, 0x0a, 0x0a, 0x1a, 0x41, 0x70, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0xa5, 0x01, 0x0a, 0x1c, 0x47, 0x65, 0x74, 0x4f, 0x72,
	0x49, 0x6e, 0x69, 0x74, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x54,
	0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x12, 0x41, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x4f, 0x72, 0x49, 0x6e, 0x69, 0x74,
	0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x72, 0x61, 0x63, 0x6b,
	0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x42, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x4f, 0x72, 0x49,
	0x6e, 0x69, 0x74, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x72,
	0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x9c,
	0x01, 0x0a, 0x19, 0x55, 0x70, 0x73, 0x65, 0x72, 0x74, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x12, 0x3e, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x70, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x73, 0x65,
	0x72, 0x74, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x72, 0x61,
	0x63, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3f, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x70, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x73, 0x65,
	0x72, 0x74, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x72, 0x61,
	0x63, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x9c, 0x01,
	0x0a, 0x19, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x12, 0x3e, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x70, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x72, 0x61, 0x63,
	0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3f, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x70, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x72, 0x61, 0x63,
	0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x96, 0x01, 0x0a,
	0x17, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x12, 0x3c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x70, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0xb4, 0x01, 0x0a, 0x21, 0x42, 0x61, 0x74, 0x63, 0x68, 0x47,
	0x65, 0x74, 0x4f, 0x72, 0x49, 0x6e, 0x69, 0x74, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x12, 0x46, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x70, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68,
	0x47, 0x65, 0x74, 0x4f, 0x72, 0x49, 0x6e, 0x69, 0x74, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x47, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x76, 0x31, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x47, 0x65, 0x74, 0x4f, 0x72, 0x49, 0x6e, 0x69,
	0x74, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x72, 0x61, 0x63,
	0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0xab, 0x01, 0x0a,
	0x1e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x55, 0x70, 0x73, 0x65, 0x72, 0x74, 0x41, 0x70, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x12,
	0x43, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x42,
	0x61, 0x74, 0x63, 0x68, 0x55, 0x70, 0x73, 0x65, 0x72, 0x74, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x44, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x76, 0x31, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x55, 0x70, 0x73, 0x65, 0x72, 0x74, 0x41,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69,
	0x6e, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x96, 0x01, 0x0a, 0x17, 0x53,
	0x79, 0x6e, 0x63, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x72,
	0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x12, 0x3c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x79, 0x6e, 0x63, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x3d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x76, 0x31, 0x2e, 0x53, 0x79, 0x6e, 0x63, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x9c, 0x01, 0x0a, 0x19, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x74,
	0x61, 0x66, 0x66, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x3e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31,
	0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x74, 0x61, 0x66, 0x66, 0x4c, 0x6f, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x3f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31,
	0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x74, 0x61, 0x66, 0x66, 0x4c, 0x6f, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x42, 0x8c, 0x01, 0x0a, 0x24, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x70, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x62, 0x67,
	0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c,
	0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69,
	0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74,
	0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31,
	0x3b, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x76, 0x63, 0x70,
	0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_service_appointment_v1_appointment_tracking_service_proto_rawDescOnce sync.Once
	file_moego_service_appointment_v1_appointment_tracking_service_proto_rawDescData = file_moego_service_appointment_v1_appointment_tracking_service_proto_rawDesc
)

func file_moego_service_appointment_v1_appointment_tracking_service_proto_rawDescGZIP() []byte {
	file_moego_service_appointment_v1_appointment_tracking_service_proto_rawDescOnce.Do(func() {
		file_moego_service_appointment_v1_appointment_tracking_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_service_appointment_v1_appointment_tracking_service_proto_rawDescData)
	})
	return file_moego_service_appointment_v1_appointment_tracking_service_proto_rawDescData
}

var file_moego_service_appointment_v1_appointment_tracking_service_proto_msgTypes = make([]protoimpl.MessageInfo, 18)
var file_moego_service_appointment_v1_appointment_tracking_service_proto_goTypes = []interface{}{
	(*GetOrInitAppointmentTrackingRequest)(nil),          // 0: moego.service.appointment.v1.GetOrInitAppointmentTrackingRequest
	(*GetOrInitAppointmentTrackingResponse)(nil),         // 1: moego.service.appointment.v1.GetOrInitAppointmentTrackingResponse
	(*ListAppointmentTrackingRequest)(nil),               // 2: moego.service.appointment.v1.ListAppointmentTrackingRequest
	(*ListAppointmentTrackingResponse)(nil),              // 3: moego.service.appointment.v1.ListAppointmentTrackingResponse
	(*UpsertAppointmentTrackingRequest)(nil),             // 4: moego.service.appointment.v1.UpsertAppointmentTrackingRequest
	(*UpsertAppointmentTrackingResponse)(nil),            // 5: moego.service.appointment.v1.UpsertAppointmentTrackingResponse
	(*UpdateAppointmentTrackingRequest)(nil),             // 6: moego.service.appointment.v1.UpdateAppointmentTrackingRequest
	(*UpdateAppointmentTrackingResponse)(nil),            // 7: moego.service.appointment.v1.UpdateAppointmentTrackingResponse
	(*BatchUpsertAppointmentTrackingRequest)(nil),        // 8: moego.service.appointment.v1.BatchUpsertAppointmentTrackingRequest
	(*BatchUpsertAppointmentTrackingResponse)(nil),       // 9: moego.service.appointment.v1.BatchUpsertAppointmentTrackingResponse
	(*BatchGetOrInitAppointmentTrackingRequest)(nil),     // 10: moego.service.appointment.v1.BatchGetOrInitAppointmentTrackingRequest
	(*BatchGetOrInitAppointmentTrackingResponse)(nil),    // 11: moego.service.appointment.v1.BatchGetOrInitAppointmentTrackingResponse
	(*SyncAppointmentTrackingRequest)(nil),               // 12: moego.service.appointment.v1.SyncAppointmentTrackingRequest
	(*SyncAppointmentTrackingResponse)(nil),              // 13: moego.service.appointment.v1.SyncAppointmentTrackingResponse
	(*UpdateStaffLocationStatusRequest)(nil),             // 14: moego.service.appointment.v1.UpdateStaffLocationStatusRequest
	(*UpdateStaffLocationStatusResponse)(nil),            // 15: moego.service.appointment.v1.UpdateStaffLocationStatusResponse
	(*ListAppointmentTrackingRequest_Filter)(nil),        // 16: moego.service.appointment.v1.ListAppointmentTrackingRequest.Filter
	(*BatchUpsertAppointmentTrackingRequest_Filter)(nil), // 17: moego.service.appointment.v1.BatchUpsertAppointmentTrackingRequest.Filter
	(*v1.AppointmentTracking)(nil),                       // 18: moego.models.appointment.v1.AppointmentTracking
	(*v2.PaginationRequest)(nil),                         // 19: moego.utils.v2.PaginationRequest
	(*v2.PaginationResponse)(nil),                        // 20: moego.utils.v2.PaginationResponse
	(*v1.UpdateAppointmentTrackingDef)(nil),              // 21: moego.models.appointment.v1.UpdateAppointmentTrackingDef
	(*latlng.LatLng)(nil),                                // 22: google.type.LatLng
	(v1.StaffLocationStatus)(0),                          // 23: moego.models.appointment.v1.StaffLocationStatus
}
var file_moego_service_appointment_v1_appointment_tracking_service_proto_depIdxs = []int32{
	18, // 0: moego.service.appointment.v1.GetOrInitAppointmentTrackingResponse.appointment_tracking:type_name -> moego.models.appointment.v1.AppointmentTracking
	19, // 1: moego.service.appointment.v1.ListAppointmentTrackingRequest.pagination:type_name -> moego.utils.v2.PaginationRequest
	16, // 2: moego.service.appointment.v1.ListAppointmentTrackingRequest.filter:type_name -> moego.service.appointment.v1.ListAppointmentTrackingRequest.Filter
	20, // 3: moego.service.appointment.v1.ListAppointmentTrackingResponse.pagination:type_name -> moego.utils.v2.PaginationResponse
	18, // 4: moego.service.appointment.v1.ListAppointmentTrackingResponse.appointment_tracking:type_name -> moego.models.appointment.v1.AppointmentTracking
	21, // 5: moego.service.appointment.v1.UpsertAppointmentTrackingRequest.appointment_tracking:type_name -> moego.models.appointment.v1.UpdateAppointmentTrackingDef
	18, // 6: moego.service.appointment.v1.UpsertAppointmentTrackingResponse.appointment_tracking:type_name -> moego.models.appointment.v1.AppointmentTracking
	21, // 7: moego.service.appointment.v1.UpdateAppointmentTrackingRequest.appointment_tracking:type_name -> moego.models.appointment.v1.UpdateAppointmentTrackingDef
	18, // 8: moego.service.appointment.v1.UpdateAppointmentTrackingResponse.appointment_tracking:type_name -> moego.models.appointment.v1.AppointmentTracking
	21, // 9: moego.service.appointment.v1.BatchUpsertAppointmentTrackingRequest.appointment_tracking:type_name -> moego.models.appointment.v1.UpdateAppointmentTrackingDef
	17, // 10: moego.service.appointment.v1.BatchUpsertAppointmentTrackingRequest.filter:type_name -> moego.service.appointment.v1.BatchUpsertAppointmentTrackingRequest.Filter
	18, // 11: moego.service.appointment.v1.BatchUpsertAppointmentTrackingResponse.appointment_tracking:type_name -> moego.models.appointment.v1.AppointmentTracking
	18, // 12: moego.service.appointment.v1.BatchGetOrInitAppointmentTrackingResponse.appointment_tracking:type_name -> moego.models.appointment.v1.AppointmentTracking
	22, // 13: moego.service.appointment.v1.SyncAppointmentTrackingRequest.coordinate:type_name -> google.type.LatLng
	23, // 14: moego.service.appointment.v1.UpdateStaffLocationStatusRequest.staff_location_status:type_name -> moego.models.appointment.v1.StaffLocationStatus
	22, // 15: moego.service.appointment.v1.UpdateStaffLocationStatusRequest.staff_coordinate:type_name -> google.type.LatLng
	18, // 16: moego.service.appointment.v1.UpdateStaffLocationStatusResponse.appointment_tracking:type_name -> moego.models.appointment.v1.AppointmentTracking
	23, // 17: moego.service.appointment.v1.ListAppointmentTrackingRequest.Filter.staff_location_statuses:type_name -> moego.models.appointment.v1.StaffLocationStatus
	23, // 18: moego.service.appointment.v1.BatchUpsertAppointmentTrackingRequest.Filter.staff_location_statuses:type_name -> moego.models.appointment.v1.StaffLocationStatus
	0,  // 19: moego.service.appointment.v1.AppointmentTrackingService.GetOrInitAppointmentTracking:input_type -> moego.service.appointment.v1.GetOrInitAppointmentTrackingRequest
	4,  // 20: moego.service.appointment.v1.AppointmentTrackingService.UpsertAppointmentTracking:input_type -> moego.service.appointment.v1.UpsertAppointmentTrackingRequest
	6,  // 21: moego.service.appointment.v1.AppointmentTrackingService.UpdateAppointmentTracking:input_type -> moego.service.appointment.v1.UpdateAppointmentTrackingRequest
	2,  // 22: moego.service.appointment.v1.AppointmentTrackingService.ListAppointmentTracking:input_type -> moego.service.appointment.v1.ListAppointmentTrackingRequest
	10, // 23: moego.service.appointment.v1.AppointmentTrackingService.BatchGetOrInitAppointmentTracking:input_type -> moego.service.appointment.v1.BatchGetOrInitAppointmentTrackingRequest
	8,  // 24: moego.service.appointment.v1.AppointmentTrackingService.BatchUpsertAppointmentTracking:input_type -> moego.service.appointment.v1.BatchUpsertAppointmentTrackingRequest
	12, // 25: moego.service.appointment.v1.AppointmentTrackingService.SyncAppointmentTracking:input_type -> moego.service.appointment.v1.SyncAppointmentTrackingRequest
	14, // 26: moego.service.appointment.v1.AppointmentTrackingService.UpdateStaffLocationStatus:input_type -> moego.service.appointment.v1.UpdateStaffLocationStatusRequest
	1,  // 27: moego.service.appointment.v1.AppointmentTrackingService.GetOrInitAppointmentTracking:output_type -> moego.service.appointment.v1.GetOrInitAppointmentTrackingResponse
	5,  // 28: moego.service.appointment.v1.AppointmentTrackingService.UpsertAppointmentTracking:output_type -> moego.service.appointment.v1.UpsertAppointmentTrackingResponse
	7,  // 29: moego.service.appointment.v1.AppointmentTrackingService.UpdateAppointmentTracking:output_type -> moego.service.appointment.v1.UpdateAppointmentTrackingResponse
	3,  // 30: moego.service.appointment.v1.AppointmentTrackingService.ListAppointmentTracking:output_type -> moego.service.appointment.v1.ListAppointmentTrackingResponse
	11, // 31: moego.service.appointment.v1.AppointmentTrackingService.BatchGetOrInitAppointmentTracking:output_type -> moego.service.appointment.v1.BatchGetOrInitAppointmentTrackingResponse
	9,  // 32: moego.service.appointment.v1.AppointmentTrackingService.BatchUpsertAppointmentTracking:output_type -> moego.service.appointment.v1.BatchUpsertAppointmentTrackingResponse
	13, // 33: moego.service.appointment.v1.AppointmentTrackingService.SyncAppointmentTracking:output_type -> moego.service.appointment.v1.SyncAppointmentTrackingResponse
	15, // 34: moego.service.appointment.v1.AppointmentTrackingService.UpdateStaffLocationStatus:output_type -> moego.service.appointment.v1.UpdateStaffLocationStatusResponse
	27, // [27:35] is the sub-list for method output_type
	19, // [19:27] is the sub-list for method input_type
	19, // [19:19] is the sub-list for extension type_name
	19, // [19:19] is the sub-list for extension extendee
	0,  // [0:19] is the sub-list for field type_name
}

func init() { file_moego_service_appointment_v1_appointment_tracking_service_proto_init() }
func file_moego_service_appointment_v1_appointment_tracking_service_proto_init() {
	if File_moego_service_appointment_v1_appointment_tracking_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_service_appointment_v1_appointment_tracking_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetOrInitAppointmentTrackingRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_appointment_v1_appointment_tracking_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetOrInitAppointmentTrackingResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_appointment_v1_appointment_tracking_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListAppointmentTrackingRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_appointment_v1_appointment_tracking_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListAppointmentTrackingResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_appointment_v1_appointment_tracking_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpsertAppointmentTrackingRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_appointment_v1_appointment_tracking_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpsertAppointmentTrackingResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_appointment_v1_appointment_tracking_service_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateAppointmentTrackingRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_appointment_v1_appointment_tracking_service_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateAppointmentTrackingResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_appointment_v1_appointment_tracking_service_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchUpsertAppointmentTrackingRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_appointment_v1_appointment_tracking_service_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchUpsertAppointmentTrackingResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_appointment_v1_appointment_tracking_service_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchGetOrInitAppointmentTrackingRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_appointment_v1_appointment_tracking_service_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchGetOrInitAppointmentTrackingResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_appointment_v1_appointment_tracking_service_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SyncAppointmentTrackingRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_appointment_v1_appointment_tracking_service_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SyncAppointmentTrackingResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_appointment_v1_appointment_tracking_service_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateStaffLocationStatusRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_appointment_v1_appointment_tracking_service_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateStaffLocationStatusResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_appointment_v1_appointment_tracking_service_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListAppointmentTrackingRequest_Filter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_appointment_v1_appointment_tracking_service_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchUpsertAppointmentTrackingRequest_Filter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_service_appointment_v1_appointment_tracking_service_proto_msgTypes[2].OneofWrappers = []interface{}{}
	file_moego_service_appointment_v1_appointment_tracking_service_proto_msgTypes[3].OneofWrappers = []interface{}{}
	file_moego_service_appointment_v1_appointment_tracking_service_proto_msgTypes[14].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_service_appointment_v1_appointment_tracking_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   18,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_service_appointment_v1_appointment_tracking_service_proto_goTypes,
		DependencyIndexes: file_moego_service_appointment_v1_appointment_tracking_service_proto_depIdxs,
		MessageInfos:      file_moego_service_appointment_v1_appointment_tracking_service_proto_msgTypes,
	}.Build()
	File_moego_service_appointment_v1_appointment_tracking_service_proto = out.File
	file_moego_service_appointment_v1_appointment_tracking_service_proto_rawDesc = nil
	file_moego_service_appointment_v1_appointment_tracking_service_proto_goTypes = nil
	file_moego_service_appointment_v1_appointment_tracking_service_proto_depIdxs = nil
}
