// @since 2025-04-02 17:04:12
// <AUTHOR> <<EMAIL>>

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/service/appointment/v1/boarding_split_lodging_service.proto

package appointmentsvcpb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/appointment/v1"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// list boarding_split_lodging request
type ListBoardingSplitLodgingsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the appointment id
	AppointmentIds []int64 `protobuf:"varint,1,rep,packed,name=appointment_ids,json=appointmentIds,proto3" json:"appointment_ids,omitempty"`
}

func (x *ListBoardingSplitLodgingsRequest) Reset() {
	*x = ListBoardingSplitLodgingsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_appointment_v1_boarding_split_lodging_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListBoardingSplitLodgingsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListBoardingSplitLodgingsRequest) ProtoMessage() {}

func (x *ListBoardingSplitLodgingsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_appointment_v1_boarding_split_lodging_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListBoardingSplitLodgingsRequest.ProtoReflect.Descriptor instead.
func (*ListBoardingSplitLodgingsRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_appointment_v1_boarding_split_lodging_service_proto_rawDescGZIP(), []int{0}
}

func (x *ListBoardingSplitLodgingsRequest) GetAppointmentIds() []int64 {
	if x != nil {
		return x.AppointmentIds
	}
	return nil
}

// list boarding_split_lodging response
type ListBoardingSplitLodgingsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the boarding_split_lodging
	BoardingSplitLodgings []*v1.BoardingSplitLodgingModel `protobuf:"bytes,1,rep,name=boarding_split_lodgings,json=boardingSplitLodgings,proto3" json:"boarding_split_lodgings,omitempty"`
}

func (x *ListBoardingSplitLodgingsResponse) Reset() {
	*x = ListBoardingSplitLodgingsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_appointment_v1_boarding_split_lodging_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListBoardingSplitLodgingsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListBoardingSplitLodgingsResponse) ProtoMessage() {}

func (x *ListBoardingSplitLodgingsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_appointment_v1_boarding_split_lodging_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListBoardingSplitLodgingsResponse.ProtoReflect.Descriptor instead.
func (*ListBoardingSplitLodgingsResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_appointment_v1_boarding_split_lodging_service_proto_rawDescGZIP(), []int{1}
}

func (x *ListBoardingSplitLodgingsResponse) GetBoardingSplitLodgings() []*v1.BoardingSplitLodgingModel {
	if x != nil {
		return x.BoardingSplitLodgings
	}
	return nil
}

var File_moego_service_appointment_v1_boarding_split_lodging_service_proto protoreflect.FileDescriptor

var file_moego_service_appointment_v1_boarding_split_lodging_service_proto_rawDesc = []byte{
	0x0a, 0x41, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f,
	0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x62,
	0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x73, 0x70, 0x6c, 0x69, 0x74, 0x5f, 0x6c, 0x6f,
	0x64, 0x67, 0x69, 0x6e, 0x67, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x1c, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76,
	0x31, 0x1a, 0x3f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f,
	0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x62,
	0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x73, 0x70, 0x6c, 0x69, 0x74, 0x5f, 0x6c, 0x6f,
	0x64, 0x67, 0x69, 0x6e, 0x67, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c,
	0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x59, 0x0a, 0x20, 0x4c,
	0x69, 0x73, 0x74, 0x42, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x53, 0x70, 0x6c, 0x69, 0x74,
	0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x35, 0x0a, 0x0f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69,
	0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x42, 0x0c, 0xfa, 0x42, 0x09, 0x92, 0x01, 0x06,
	0x22, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x49, 0x64, 0x73, 0x22, 0x93, 0x01, 0x0a, 0x21, 0x4c, 0x69, 0x73, 0x74, 0x42,
	0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x53, 0x70, 0x6c, 0x69, 0x74, 0x4c, 0x6f, 0x64, 0x67,
	0x69, 0x6e, 0x67, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x6e, 0x0a, 0x17,
	0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x73, 0x70, 0x6c, 0x69, 0x74, 0x5f, 0x6c,
	0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x36, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x70, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x6f, 0x61, 0x72,
	0x64, 0x69, 0x6e, 0x67, 0x53, 0x70, 0x6c, 0x69, 0x74, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67,
	0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x15, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x53,
	0x70, 0x6c, 0x69, 0x74, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x73, 0x32, 0xbc, 0x01, 0x0a,
	0x1b, 0x42, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x53, 0x70, 0x6c, 0x69, 0x74, 0x4c, 0x6f,
	0x64, 0x67, 0x69, 0x6e, 0x67, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x9c, 0x01, 0x0a,
	0x19, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x53, 0x70, 0x6c,
	0x69, 0x74, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x73, 0x12, 0x3e, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x6f,
	0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x53, 0x70, 0x6c, 0x69, 0x74, 0x4c, 0x6f, 0x64, 0x67, 0x69,
	0x6e, 0x67, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3f, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x6f,
	0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x53, 0x70, 0x6c, 0x69, 0x74, 0x4c, 0x6f, 0x64, 0x67, 0x69,
	0x6e, 0x67, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x8c, 0x01, 0x0a, 0x24,
	0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x62, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63,
	0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f, 0x61, 0x70, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x3b, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x76, 0x63, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x33,
}

var (
	file_moego_service_appointment_v1_boarding_split_lodging_service_proto_rawDescOnce sync.Once
	file_moego_service_appointment_v1_boarding_split_lodging_service_proto_rawDescData = file_moego_service_appointment_v1_boarding_split_lodging_service_proto_rawDesc
)

func file_moego_service_appointment_v1_boarding_split_lodging_service_proto_rawDescGZIP() []byte {
	file_moego_service_appointment_v1_boarding_split_lodging_service_proto_rawDescOnce.Do(func() {
		file_moego_service_appointment_v1_boarding_split_lodging_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_service_appointment_v1_boarding_split_lodging_service_proto_rawDescData)
	})
	return file_moego_service_appointment_v1_boarding_split_lodging_service_proto_rawDescData
}

var file_moego_service_appointment_v1_boarding_split_lodging_service_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_moego_service_appointment_v1_boarding_split_lodging_service_proto_goTypes = []interface{}{
	(*ListBoardingSplitLodgingsRequest)(nil),  // 0: moego.service.appointment.v1.ListBoardingSplitLodgingsRequest
	(*ListBoardingSplitLodgingsResponse)(nil), // 1: moego.service.appointment.v1.ListBoardingSplitLodgingsResponse
	(*v1.BoardingSplitLodgingModel)(nil),      // 2: moego.models.appointment.v1.BoardingSplitLodgingModel
}
var file_moego_service_appointment_v1_boarding_split_lodging_service_proto_depIdxs = []int32{
	2, // 0: moego.service.appointment.v1.ListBoardingSplitLodgingsResponse.boarding_split_lodgings:type_name -> moego.models.appointment.v1.BoardingSplitLodgingModel
	0, // 1: moego.service.appointment.v1.BoardingSplitLodgingService.ListBoardingSplitLodgings:input_type -> moego.service.appointment.v1.ListBoardingSplitLodgingsRequest
	1, // 2: moego.service.appointment.v1.BoardingSplitLodgingService.ListBoardingSplitLodgings:output_type -> moego.service.appointment.v1.ListBoardingSplitLodgingsResponse
	2, // [2:3] is the sub-list for method output_type
	1, // [1:2] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_moego_service_appointment_v1_boarding_split_lodging_service_proto_init() }
func file_moego_service_appointment_v1_boarding_split_lodging_service_proto_init() {
	if File_moego_service_appointment_v1_boarding_split_lodging_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_service_appointment_v1_boarding_split_lodging_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListBoardingSplitLodgingsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_appointment_v1_boarding_split_lodging_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListBoardingSplitLodgingsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_service_appointment_v1_boarding_split_lodging_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_service_appointment_v1_boarding_split_lodging_service_proto_goTypes,
		DependencyIndexes: file_moego_service_appointment_v1_boarding_split_lodging_service_proto_depIdxs,
		MessageInfos:      file_moego_service_appointment_v1_boarding_split_lodging_service_proto_msgTypes,
	}.Build()
	File_moego_service_appointment_v1_boarding_split_lodging_service_proto = out.File
	file_moego_service_appointment_v1_boarding_split_lodging_service_proto_rawDesc = nil
	file_moego_service_appointment_v1_boarding_split_lodging_service_proto_goTypes = nil
	file_moego_service_appointment_v1_boarding_split_lodging_service_proto_depIdxs = nil
}
