// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/service/finance_tools/v1/cash_drawer_service.proto

package financetoolssvcpb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// CashDrawerServiceClient is the client API for CashDrawerService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type CashDrawerServiceClient interface {
	// List cash drawer reports.
	ListReports(ctx context.Context, in *ListReportsRequest, opts ...grpc.CallOption) (*ListReportsResponse, error)
	// Get the last report on given timestamp.
	GetLastReport(ctx context.Context, in *GetLastReportRequest, opts ...grpc.CallOption) (*GetLastReportResponse, error)
	// Get the reported cash total, summing the cash payments charged from the clients (through appointments, retail,
	// etc.) as well as the refunds.
	GetReportedCashTotal(ctx context.Context, in *GetReportedCashTotalRequest, opts ...grpc.CallOption) (*GetReportedCashTotalResponse, error)
	// List cash adjustments.
	ListCashAdjustments(ctx context.Context, in *ListCashAdjustmentsRequest, opts ...grpc.CallOption) (*ListCashAdjustmentsResponse, error)
	// Create a new cash in/out adjustment.
	CreateCashAdjustment(ctx context.Context, in *CreateCashAdjustmentRequest, opts ...grpc.CallOption) (*CreateCashAdjustmentResponse, error)
	// Create a new report. Note that the un-attached cash adjustments within this report's range will also be attached to
	// this report.
	CreateReport(ctx context.Context, in *CreateReportRequest, opts ...grpc.CallOption) (*CreateReportResponse, error)
	// Update a report.
	UpdateReport(ctx context.Context, in *UpdateReportRequest, opts ...grpc.CallOption) (*UpdateReportResponse, error)
}

type cashDrawerServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewCashDrawerServiceClient(cc grpc.ClientConnInterface) CashDrawerServiceClient {
	return &cashDrawerServiceClient{cc}
}

func (c *cashDrawerServiceClient) ListReports(ctx context.Context, in *ListReportsRequest, opts ...grpc.CallOption) (*ListReportsResponse, error) {
	out := new(ListReportsResponse)
	err := c.cc.Invoke(ctx, "/moego.service.finance_tools.v1.CashDrawerService/ListReports", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cashDrawerServiceClient) GetLastReport(ctx context.Context, in *GetLastReportRequest, opts ...grpc.CallOption) (*GetLastReportResponse, error) {
	out := new(GetLastReportResponse)
	err := c.cc.Invoke(ctx, "/moego.service.finance_tools.v1.CashDrawerService/GetLastReport", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cashDrawerServiceClient) GetReportedCashTotal(ctx context.Context, in *GetReportedCashTotalRequest, opts ...grpc.CallOption) (*GetReportedCashTotalResponse, error) {
	out := new(GetReportedCashTotalResponse)
	err := c.cc.Invoke(ctx, "/moego.service.finance_tools.v1.CashDrawerService/GetReportedCashTotal", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cashDrawerServiceClient) ListCashAdjustments(ctx context.Context, in *ListCashAdjustmentsRequest, opts ...grpc.CallOption) (*ListCashAdjustmentsResponse, error) {
	out := new(ListCashAdjustmentsResponse)
	err := c.cc.Invoke(ctx, "/moego.service.finance_tools.v1.CashDrawerService/ListCashAdjustments", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cashDrawerServiceClient) CreateCashAdjustment(ctx context.Context, in *CreateCashAdjustmentRequest, opts ...grpc.CallOption) (*CreateCashAdjustmentResponse, error) {
	out := new(CreateCashAdjustmentResponse)
	err := c.cc.Invoke(ctx, "/moego.service.finance_tools.v1.CashDrawerService/CreateCashAdjustment", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cashDrawerServiceClient) CreateReport(ctx context.Context, in *CreateReportRequest, opts ...grpc.CallOption) (*CreateReportResponse, error) {
	out := new(CreateReportResponse)
	err := c.cc.Invoke(ctx, "/moego.service.finance_tools.v1.CashDrawerService/CreateReport", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cashDrawerServiceClient) UpdateReport(ctx context.Context, in *UpdateReportRequest, opts ...grpc.CallOption) (*UpdateReportResponse, error) {
	out := new(UpdateReportResponse)
	err := c.cc.Invoke(ctx, "/moego.service.finance_tools.v1.CashDrawerService/UpdateReport", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// CashDrawerServiceServer is the server API for CashDrawerService service.
// All implementations must embed UnimplementedCashDrawerServiceServer
// for forward compatibility
type CashDrawerServiceServer interface {
	// List cash drawer reports.
	ListReports(context.Context, *ListReportsRequest) (*ListReportsResponse, error)
	// Get the last report on given timestamp.
	GetLastReport(context.Context, *GetLastReportRequest) (*GetLastReportResponse, error)
	// Get the reported cash total, summing the cash payments charged from the clients (through appointments, retail,
	// etc.) as well as the refunds.
	GetReportedCashTotal(context.Context, *GetReportedCashTotalRequest) (*GetReportedCashTotalResponse, error)
	// List cash adjustments.
	ListCashAdjustments(context.Context, *ListCashAdjustmentsRequest) (*ListCashAdjustmentsResponse, error)
	// Create a new cash in/out adjustment.
	CreateCashAdjustment(context.Context, *CreateCashAdjustmentRequest) (*CreateCashAdjustmentResponse, error)
	// Create a new report. Note that the un-attached cash adjustments within this report's range will also be attached to
	// this report.
	CreateReport(context.Context, *CreateReportRequest) (*CreateReportResponse, error)
	// Update a report.
	UpdateReport(context.Context, *UpdateReportRequest) (*UpdateReportResponse, error)
	mustEmbedUnimplementedCashDrawerServiceServer()
}

// UnimplementedCashDrawerServiceServer must be embedded to have forward compatible implementations.
type UnimplementedCashDrawerServiceServer struct {
}

func (UnimplementedCashDrawerServiceServer) ListReports(context.Context, *ListReportsRequest) (*ListReportsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListReports not implemented")
}
func (UnimplementedCashDrawerServiceServer) GetLastReport(context.Context, *GetLastReportRequest) (*GetLastReportResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetLastReport not implemented")
}
func (UnimplementedCashDrawerServiceServer) GetReportedCashTotal(context.Context, *GetReportedCashTotalRequest) (*GetReportedCashTotalResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetReportedCashTotal not implemented")
}
func (UnimplementedCashDrawerServiceServer) ListCashAdjustments(context.Context, *ListCashAdjustmentsRequest) (*ListCashAdjustmentsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListCashAdjustments not implemented")
}
func (UnimplementedCashDrawerServiceServer) CreateCashAdjustment(context.Context, *CreateCashAdjustmentRequest) (*CreateCashAdjustmentResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateCashAdjustment not implemented")
}
func (UnimplementedCashDrawerServiceServer) CreateReport(context.Context, *CreateReportRequest) (*CreateReportResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateReport not implemented")
}
func (UnimplementedCashDrawerServiceServer) UpdateReport(context.Context, *UpdateReportRequest) (*UpdateReportResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateReport not implemented")
}
func (UnimplementedCashDrawerServiceServer) mustEmbedUnimplementedCashDrawerServiceServer() {}

// UnsafeCashDrawerServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to CashDrawerServiceServer will
// result in compilation errors.
type UnsafeCashDrawerServiceServer interface {
	mustEmbedUnimplementedCashDrawerServiceServer()
}

func RegisterCashDrawerServiceServer(s grpc.ServiceRegistrar, srv CashDrawerServiceServer) {
	s.RegisterService(&CashDrawerService_ServiceDesc, srv)
}

func _CashDrawerService_ListReports_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListReportsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CashDrawerServiceServer).ListReports(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.finance_tools.v1.CashDrawerService/ListReports",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CashDrawerServiceServer).ListReports(ctx, req.(*ListReportsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CashDrawerService_GetLastReport_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLastReportRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CashDrawerServiceServer).GetLastReport(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.finance_tools.v1.CashDrawerService/GetLastReport",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CashDrawerServiceServer).GetLastReport(ctx, req.(*GetLastReportRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CashDrawerService_GetReportedCashTotal_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetReportedCashTotalRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CashDrawerServiceServer).GetReportedCashTotal(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.finance_tools.v1.CashDrawerService/GetReportedCashTotal",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CashDrawerServiceServer).GetReportedCashTotal(ctx, req.(*GetReportedCashTotalRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CashDrawerService_ListCashAdjustments_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListCashAdjustmentsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CashDrawerServiceServer).ListCashAdjustments(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.finance_tools.v1.CashDrawerService/ListCashAdjustments",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CashDrawerServiceServer).ListCashAdjustments(ctx, req.(*ListCashAdjustmentsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CashDrawerService_CreateCashAdjustment_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateCashAdjustmentRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CashDrawerServiceServer).CreateCashAdjustment(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.finance_tools.v1.CashDrawerService/CreateCashAdjustment",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CashDrawerServiceServer).CreateCashAdjustment(ctx, req.(*CreateCashAdjustmentRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CashDrawerService_CreateReport_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateReportRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CashDrawerServiceServer).CreateReport(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.finance_tools.v1.CashDrawerService/CreateReport",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CashDrawerServiceServer).CreateReport(ctx, req.(*CreateReportRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CashDrawerService_UpdateReport_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateReportRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CashDrawerServiceServer).UpdateReport(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.finance_tools.v1.CashDrawerService/UpdateReport",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CashDrawerServiceServer).UpdateReport(ctx, req.(*UpdateReportRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// CashDrawerService_ServiceDesc is the grpc.ServiceDesc for CashDrawerService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var CashDrawerService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.service.finance_tools.v1.CashDrawerService",
	HandlerType: (*CashDrawerServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ListReports",
			Handler:    _CashDrawerService_ListReports_Handler,
		},
		{
			MethodName: "GetLastReport",
			Handler:    _CashDrawerService_GetLastReport_Handler,
		},
		{
			MethodName: "GetReportedCashTotal",
			Handler:    _CashDrawerService_GetReportedCashTotal_Handler,
		},
		{
			MethodName: "ListCashAdjustments",
			Handler:    _CashDrawerService_ListCashAdjustments_Handler,
		},
		{
			MethodName: "CreateCashAdjustment",
			Handler:    _CashDrawerService_CreateCashAdjustment_Handler,
		},
		{
			MethodName: "CreateReport",
			Handler:    _CashDrawerService_CreateReport_Handler,
		},
		{
			MethodName: "UpdateReport",
			Handler:    _CashDrawerService_UpdateReport_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/service/finance_tools/v1/cash_drawer_service.proto",
}
