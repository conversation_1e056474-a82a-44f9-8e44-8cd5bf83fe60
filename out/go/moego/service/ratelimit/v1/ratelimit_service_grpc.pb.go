// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/service/ratelimit/v1/ratelimit_service.proto

package ratelimitsvcpb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// RateLimitServiceClient is the client API for RateLimitService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type RateLimitServiceClient interface {
	// 校验请求是否允许通过
	Allow(ctx context.Context, in *AllowRequest, opts ...grpc.CallOption) (*AllowResponse, error)
	// 给指定 Domain 注册限流规则
	RegisterRules(ctx context.Context, in *RegisterRulesRequest, opts ...grpc.CallOption) (*RegisterRulesResponse, error)
}

type rateLimitServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewRateLimitServiceClient(cc grpc.ClientConnInterface) RateLimitServiceClient {
	return &rateLimitServiceClient{cc}
}

func (c *rateLimitServiceClient) Allow(ctx context.Context, in *AllowRequest, opts ...grpc.CallOption) (*AllowResponse, error) {
	out := new(AllowResponse)
	err := c.cc.Invoke(ctx, "/moego.service.ratelimit.v1.RateLimitService/Allow", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rateLimitServiceClient) RegisterRules(ctx context.Context, in *RegisterRulesRequest, opts ...grpc.CallOption) (*RegisterRulesResponse, error) {
	out := new(RegisterRulesResponse)
	err := c.cc.Invoke(ctx, "/moego.service.ratelimit.v1.RateLimitService/RegisterRules", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// RateLimitServiceServer is the server API for RateLimitService service.
// All implementations must embed UnimplementedRateLimitServiceServer
// for forward compatibility
type RateLimitServiceServer interface {
	// 校验请求是否允许通过
	Allow(context.Context, *AllowRequest) (*AllowResponse, error)
	// 给指定 Domain 注册限流规则
	RegisterRules(context.Context, *RegisterRulesRequest) (*RegisterRulesResponse, error)
	mustEmbedUnimplementedRateLimitServiceServer()
}

// UnimplementedRateLimitServiceServer must be embedded to have forward compatible implementations.
type UnimplementedRateLimitServiceServer struct {
}

func (UnimplementedRateLimitServiceServer) Allow(context.Context, *AllowRequest) (*AllowResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Allow not implemented")
}
func (UnimplementedRateLimitServiceServer) RegisterRules(context.Context, *RegisterRulesRequest) (*RegisterRulesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RegisterRules not implemented")
}
func (UnimplementedRateLimitServiceServer) mustEmbedUnimplementedRateLimitServiceServer() {}

// UnsafeRateLimitServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to RateLimitServiceServer will
// result in compilation errors.
type UnsafeRateLimitServiceServer interface {
	mustEmbedUnimplementedRateLimitServiceServer()
}

func RegisterRateLimitServiceServer(s grpc.ServiceRegistrar, srv RateLimitServiceServer) {
	s.RegisterService(&RateLimitService_ServiceDesc, srv)
}

func _RateLimitService_Allow_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AllowRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RateLimitServiceServer).Allow(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.ratelimit.v1.RateLimitService/Allow",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RateLimitServiceServer).Allow(ctx, req.(*AllowRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RateLimitService_RegisterRules_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RegisterRulesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RateLimitServiceServer).RegisterRules(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.ratelimit.v1.RateLimitService/RegisterRules",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RateLimitServiceServer).RegisterRules(ctx, req.(*RegisterRulesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// RateLimitService_ServiceDesc is the grpc.ServiceDesc for RateLimitService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var RateLimitService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.service.ratelimit.v1.RateLimitService",
	HandlerType: (*RateLimitServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Allow",
			Handler:    _RateLimitService_Allow_Handler,
		},
		{
			MethodName: "RegisterRules",
			Handler:    _RateLimitService_RegisterRules_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/service/ratelimit/v1/ratelimit_service.proto",
}
