// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/enterprise/configuration/v1/configuration_api.proto

package configurationapipb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// ConfigurationServiceClient is the client API for ConfigurationService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ConfigurationServiceClient interface {
	// publish template, apply change to franchisees
	PublishTemplate(ctx context.Context, in *PublishTemplateParams, opts ...grpc.CallOption) (*PublishTemplateResult, error)
	// list template
	ListTemplate(ctx context.Context, in *ListTemplateParams, opts ...grpc.CallOption) (*ListTemplateResult, error)
	// list template publish record
	ListTemplatePublishRecord(ctx context.Context, in *ListTemplatePublishRecordParams, opts ...grpc.CallOption) (*ListTemplatePublishRecordResult, error)
}

type configurationServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewConfigurationServiceClient(cc grpc.ClientConnInterface) ConfigurationServiceClient {
	return &configurationServiceClient{cc}
}

func (c *configurationServiceClient) PublishTemplate(ctx context.Context, in *PublishTemplateParams, opts ...grpc.CallOption) (*PublishTemplateResult, error) {
	out := new(PublishTemplateResult)
	err := c.cc.Invoke(ctx, "/moego.enterprise.configuration.v1.ConfigurationService/PublishTemplate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *configurationServiceClient) ListTemplate(ctx context.Context, in *ListTemplateParams, opts ...grpc.CallOption) (*ListTemplateResult, error) {
	out := new(ListTemplateResult)
	err := c.cc.Invoke(ctx, "/moego.enterprise.configuration.v1.ConfigurationService/ListTemplate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *configurationServiceClient) ListTemplatePublishRecord(ctx context.Context, in *ListTemplatePublishRecordParams, opts ...grpc.CallOption) (*ListTemplatePublishRecordResult, error) {
	out := new(ListTemplatePublishRecordResult)
	err := c.cc.Invoke(ctx, "/moego.enterprise.configuration.v1.ConfigurationService/ListTemplatePublishRecord", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ConfigurationServiceServer is the server API for ConfigurationService service.
// All implementations must embed UnimplementedConfigurationServiceServer
// for forward compatibility
type ConfigurationServiceServer interface {
	// publish template, apply change to franchisees
	PublishTemplate(context.Context, *PublishTemplateParams) (*PublishTemplateResult, error)
	// list template
	ListTemplate(context.Context, *ListTemplateParams) (*ListTemplateResult, error)
	// list template publish record
	ListTemplatePublishRecord(context.Context, *ListTemplatePublishRecordParams) (*ListTemplatePublishRecordResult, error)
	mustEmbedUnimplementedConfigurationServiceServer()
}

// UnimplementedConfigurationServiceServer must be embedded to have forward compatible implementations.
type UnimplementedConfigurationServiceServer struct {
}

func (UnimplementedConfigurationServiceServer) PublishTemplate(context.Context, *PublishTemplateParams) (*PublishTemplateResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PublishTemplate not implemented")
}
func (UnimplementedConfigurationServiceServer) ListTemplate(context.Context, *ListTemplateParams) (*ListTemplateResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListTemplate not implemented")
}
func (UnimplementedConfigurationServiceServer) ListTemplatePublishRecord(context.Context, *ListTemplatePublishRecordParams) (*ListTemplatePublishRecordResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListTemplatePublishRecord not implemented")
}
func (UnimplementedConfigurationServiceServer) mustEmbedUnimplementedConfigurationServiceServer() {}

// UnsafeConfigurationServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ConfigurationServiceServer will
// result in compilation errors.
type UnsafeConfigurationServiceServer interface {
	mustEmbedUnimplementedConfigurationServiceServer()
}

func RegisterConfigurationServiceServer(s grpc.ServiceRegistrar, srv ConfigurationServiceServer) {
	s.RegisterService(&ConfigurationService_ServiceDesc, srv)
}

func _ConfigurationService_PublishTemplate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PublishTemplateParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConfigurationServiceServer).PublishTemplate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.enterprise.configuration.v1.ConfigurationService/PublishTemplate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConfigurationServiceServer).PublishTemplate(ctx, req.(*PublishTemplateParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _ConfigurationService_ListTemplate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListTemplateParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConfigurationServiceServer).ListTemplate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.enterprise.configuration.v1.ConfigurationService/ListTemplate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConfigurationServiceServer).ListTemplate(ctx, req.(*ListTemplateParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _ConfigurationService_ListTemplatePublishRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListTemplatePublishRecordParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConfigurationServiceServer).ListTemplatePublishRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.enterprise.configuration.v1.ConfigurationService/ListTemplatePublishRecord",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConfigurationServiceServer).ListTemplatePublishRecord(ctx, req.(*ListTemplatePublishRecordParams))
	}
	return interceptor(ctx, in, info, handler)
}

// ConfigurationService_ServiceDesc is the grpc.ServiceDesc for ConfigurationService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var ConfigurationService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.enterprise.configuration.v1.ConfigurationService",
	HandlerType: (*ConfigurationServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "PublishTemplate",
			Handler:    _ConfigurationService_PublishTemplate_Handler,
		},
		{
			MethodName: "ListTemplate",
			Handler:    _ConfigurationService_ListTemplate_Handler,
		},
		{
			MethodName: "ListTemplatePublishRecord",
			Handler:    _ConfigurationService_ListTemplatePublishRecord_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/enterprise/configuration/v1/configuration_api.proto",
}
