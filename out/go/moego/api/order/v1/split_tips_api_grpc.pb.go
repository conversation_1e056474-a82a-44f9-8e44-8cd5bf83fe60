// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/api/order/v1/split_tips_api.proto

package orderapipb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// SplitTipsServiceClient is the client API for SplitTipsService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type SplitTipsServiceClient interface {
	// save tip split api
	SaveTipSplit(ctx context.Context, in *SaveSplitTipsRequest, opts ...grpc.CallOption) (*SaveSplitTipsResponse, error)
	// get tips details for source type
	GetTipsSplitDetails(ctx context.Context, in *GetTipsSplitParams, opts ...grpc.CallOption) (*GetTipsSplitResult, error)
	// preview edit staff and split tips
	PreviewEditTipsSplit(ctx context.Context, in *EditStaffAndTipsSplitParams, opts ...grpc.CallOption) (*PreviewEditTipsSplitResult, error)
	// update edit staff and split tips
	EditTipsSplit(ctx context.Context, in *EditStaffAndTipsSplitParams, opts ...grpc.CallOption) (*EditTipsSplitResult, error)
	// get tips split changed status
	GetTipsSplitChangedStatus(ctx context.Context, in *GetTipsSplitChangedStatusParams, opts ...grpc.CallOption) (*GetTipsSplitChangedStatusResult, error)
	// clear tips split changed status
	ClearTipsSplitChangedStatus(ctx context.Context, in *ClearTipsSplitChangedStatusParams, opts ...grpc.CallOption) (*ClearTipsSplitChangedStatusResult, error)
}

type splitTipsServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewSplitTipsServiceClient(cc grpc.ClientConnInterface) SplitTipsServiceClient {
	return &splitTipsServiceClient{cc}
}

func (c *splitTipsServiceClient) SaveTipSplit(ctx context.Context, in *SaveSplitTipsRequest, opts ...grpc.CallOption) (*SaveSplitTipsResponse, error) {
	out := new(SaveSplitTipsResponse)
	err := c.cc.Invoke(ctx, "/moego.api.order.v1.SplitTipsService/SaveTipSplit", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *splitTipsServiceClient) GetTipsSplitDetails(ctx context.Context, in *GetTipsSplitParams, opts ...grpc.CallOption) (*GetTipsSplitResult, error) {
	out := new(GetTipsSplitResult)
	err := c.cc.Invoke(ctx, "/moego.api.order.v1.SplitTipsService/GetTipsSplitDetails", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *splitTipsServiceClient) PreviewEditTipsSplit(ctx context.Context, in *EditStaffAndTipsSplitParams, opts ...grpc.CallOption) (*PreviewEditTipsSplitResult, error) {
	out := new(PreviewEditTipsSplitResult)
	err := c.cc.Invoke(ctx, "/moego.api.order.v1.SplitTipsService/PreviewEditTipsSplit", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *splitTipsServiceClient) EditTipsSplit(ctx context.Context, in *EditStaffAndTipsSplitParams, opts ...grpc.CallOption) (*EditTipsSplitResult, error) {
	out := new(EditTipsSplitResult)
	err := c.cc.Invoke(ctx, "/moego.api.order.v1.SplitTipsService/EditTipsSplit", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *splitTipsServiceClient) GetTipsSplitChangedStatus(ctx context.Context, in *GetTipsSplitChangedStatusParams, opts ...grpc.CallOption) (*GetTipsSplitChangedStatusResult, error) {
	out := new(GetTipsSplitChangedStatusResult)
	err := c.cc.Invoke(ctx, "/moego.api.order.v1.SplitTipsService/GetTipsSplitChangedStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *splitTipsServiceClient) ClearTipsSplitChangedStatus(ctx context.Context, in *ClearTipsSplitChangedStatusParams, opts ...grpc.CallOption) (*ClearTipsSplitChangedStatusResult, error) {
	out := new(ClearTipsSplitChangedStatusResult)
	err := c.cc.Invoke(ctx, "/moego.api.order.v1.SplitTipsService/ClearTipsSplitChangedStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// SplitTipsServiceServer is the server API for SplitTipsService service.
// All implementations must embed UnimplementedSplitTipsServiceServer
// for forward compatibility
type SplitTipsServiceServer interface {
	// save tip split api
	SaveTipSplit(context.Context, *SaveSplitTipsRequest) (*SaveSplitTipsResponse, error)
	// get tips details for source type
	GetTipsSplitDetails(context.Context, *GetTipsSplitParams) (*GetTipsSplitResult, error)
	// preview edit staff and split tips
	PreviewEditTipsSplit(context.Context, *EditStaffAndTipsSplitParams) (*PreviewEditTipsSplitResult, error)
	// update edit staff and split tips
	EditTipsSplit(context.Context, *EditStaffAndTipsSplitParams) (*EditTipsSplitResult, error)
	// get tips split changed status
	GetTipsSplitChangedStatus(context.Context, *GetTipsSplitChangedStatusParams) (*GetTipsSplitChangedStatusResult, error)
	// clear tips split changed status
	ClearTipsSplitChangedStatus(context.Context, *ClearTipsSplitChangedStatusParams) (*ClearTipsSplitChangedStatusResult, error)
	mustEmbedUnimplementedSplitTipsServiceServer()
}

// UnimplementedSplitTipsServiceServer must be embedded to have forward compatible implementations.
type UnimplementedSplitTipsServiceServer struct {
}

func (UnimplementedSplitTipsServiceServer) SaveTipSplit(context.Context, *SaveSplitTipsRequest) (*SaveSplitTipsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SaveTipSplit not implemented")
}
func (UnimplementedSplitTipsServiceServer) GetTipsSplitDetails(context.Context, *GetTipsSplitParams) (*GetTipsSplitResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetTipsSplitDetails not implemented")
}
func (UnimplementedSplitTipsServiceServer) PreviewEditTipsSplit(context.Context, *EditStaffAndTipsSplitParams) (*PreviewEditTipsSplitResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PreviewEditTipsSplit not implemented")
}
func (UnimplementedSplitTipsServiceServer) EditTipsSplit(context.Context, *EditStaffAndTipsSplitParams) (*EditTipsSplitResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method EditTipsSplit not implemented")
}
func (UnimplementedSplitTipsServiceServer) GetTipsSplitChangedStatus(context.Context, *GetTipsSplitChangedStatusParams) (*GetTipsSplitChangedStatusResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetTipsSplitChangedStatus not implemented")
}
func (UnimplementedSplitTipsServiceServer) ClearTipsSplitChangedStatus(context.Context, *ClearTipsSplitChangedStatusParams) (*ClearTipsSplitChangedStatusResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ClearTipsSplitChangedStatus not implemented")
}
func (UnimplementedSplitTipsServiceServer) mustEmbedUnimplementedSplitTipsServiceServer() {}

// UnsafeSplitTipsServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to SplitTipsServiceServer will
// result in compilation errors.
type UnsafeSplitTipsServiceServer interface {
	mustEmbedUnimplementedSplitTipsServiceServer()
}

func RegisterSplitTipsServiceServer(s grpc.ServiceRegistrar, srv SplitTipsServiceServer) {
	s.RegisterService(&SplitTipsService_ServiceDesc, srv)
}

func _SplitTipsService_SaveTipSplit_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SaveSplitTipsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SplitTipsServiceServer).SaveTipSplit(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.order.v1.SplitTipsService/SaveTipSplit",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SplitTipsServiceServer).SaveTipSplit(ctx, req.(*SaveSplitTipsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SplitTipsService_GetTipsSplitDetails_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTipsSplitParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SplitTipsServiceServer).GetTipsSplitDetails(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.order.v1.SplitTipsService/GetTipsSplitDetails",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SplitTipsServiceServer).GetTipsSplitDetails(ctx, req.(*GetTipsSplitParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _SplitTipsService_PreviewEditTipsSplit_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(EditStaffAndTipsSplitParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SplitTipsServiceServer).PreviewEditTipsSplit(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.order.v1.SplitTipsService/PreviewEditTipsSplit",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SplitTipsServiceServer).PreviewEditTipsSplit(ctx, req.(*EditStaffAndTipsSplitParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _SplitTipsService_EditTipsSplit_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(EditStaffAndTipsSplitParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SplitTipsServiceServer).EditTipsSplit(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.order.v1.SplitTipsService/EditTipsSplit",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SplitTipsServiceServer).EditTipsSplit(ctx, req.(*EditStaffAndTipsSplitParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _SplitTipsService_GetTipsSplitChangedStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTipsSplitChangedStatusParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SplitTipsServiceServer).GetTipsSplitChangedStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.order.v1.SplitTipsService/GetTipsSplitChangedStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SplitTipsServiceServer).GetTipsSplitChangedStatus(ctx, req.(*GetTipsSplitChangedStatusParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _SplitTipsService_ClearTipsSplitChangedStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ClearTipsSplitChangedStatusParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SplitTipsServiceServer).ClearTipsSplitChangedStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.order.v1.SplitTipsService/ClearTipsSplitChangedStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SplitTipsServiceServer).ClearTipsSplitChangedStatus(ctx, req.(*ClearTipsSplitChangedStatusParams))
	}
	return interceptor(ctx, in, info, handler)
}

// SplitTipsService_ServiceDesc is the grpc.ServiceDesc for SplitTipsService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var SplitTipsService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.api.order.v1.SplitTipsService",
	HandlerType: (*SplitTipsServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SaveTipSplit",
			Handler:    _SplitTipsService_SaveTipSplit_Handler,
		},
		{
			MethodName: "GetTipsSplitDetails",
			Handler:    _SplitTipsService_GetTipsSplitDetails_Handler,
		},
		{
			MethodName: "PreviewEditTipsSplit",
			Handler:    _SplitTipsService_PreviewEditTipsSplit_Handler,
		},
		{
			MethodName: "EditTipsSplit",
			Handler:    _SplitTipsService_EditTipsSplit_Handler,
		},
		{
			MethodName: "GetTipsSplitChangedStatus",
			Handler:    _SplitTipsService_GetTipsSplitChangedStatus_Handler,
		},
		{
			MethodName: "ClearTipsSplitChangedStatus",
			Handler:    _SplitTipsService_ClearTipsSplitChangedStatus_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/api/order/v1/split_tips_api.proto",
}
