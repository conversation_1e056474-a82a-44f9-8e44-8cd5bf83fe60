// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/api/order/v1/split_tips_api.proto

package orderapipb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/order/v1"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	decimal "google.golang.org/genproto/googleapis/type/decimal"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// customized tip config
type CustomizedTipConfigRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// staff id
	StaffId int64 `protobuf:"varint,1,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
	// tips amount
	Amount *float64 `protobuf:"fixed64,2,opt,name=amount,proto3,oneof" json:"amount,omitempty"`
	// tips percentage
	Percentage *int32 `protobuf:"varint,3,opt,name=percentage,proto3,oneof" json:"percentage,omitempty"`
}

func (x *CustomizedTipConfigRequest) Reset() {
	*x = CustomizedTipConfigRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_order_v1_split_tips_api_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CustomizedTipConfigRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomizedTipConfigRequest) ProtoMessage() {}

func (x *CustomizedTipConfigRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_order_v1_split_tips_api_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomizedTipConfigRequest.ProtoReflect.Descriptor instead.
func (*CustomizedTipConfigRequest) Descriptor() ([]byte, []int) {
	return file_moego_api_order_v1_split_tips_api_proto_rawDescGZIP(), []int{0}
}

func (x *CustomizedTipConfigRequest) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

func (x *CustomizedTipConfigRequest) GetAmount() float64 {
	if x != nil && x.Amount != nil {
		return *x.Amount
	}
	return 0
}

func (x *CustomizedTipConfigRequest) GetPercentage() int32 {
	if x != nil && x.Percentage != nil {
		return *x.Percentage
	}
	return 0
}

// save tip split request
type SaveSplitTipsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// order id
	OrderId int64 `protobuf:"varint,1,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	// tip split method
	SplitMethod v1.SplitTipsMethod `protobuf:"varint,2,opt,name=split_method,json=splitMethod,proto3,enum=moego.models.order.v1.SplitTipsMethod" json:"split_method,omitempty"`
	// customized type: amount/percentage
	CustomizedType *v1.CustomizedTipType `protobuf:"varint,3,opt,name=customized_type,json=customizedType,proto3,enum=moego.models.order.v1.CustomizedTipType,oneof" json:"customized_type,omitempty"`
	// customized tip config list
	CustomizedConfig []*CustomizedTipConfigRequest `protobuf:"bytes,4,rep,name=customized_config,json=customizedConfig,proto3" json:"customized_config,omitempty"`
	// tips to business.
	BusinessTipAmount *decimal.Decimal `protobuf:"bytes,5,opt,name=business_tip_amount,json=businessTipAmount,proto3,oneof" json:"business_tip_amount,omitempty"`
}

func (x *SaveSplitTipsRequest) Reset() {
	*x = SaveSplitTipsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_order_v1_split_tips_api_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SaveSplitTipsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SaveSplitTipsRequest) ProtoMessage() {}

func (x *SaveSplitTipsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_order_v1_split_tips_api_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SaveSplitTipsRequest.ProtoReflect.Descriptor instead.
func (*SaveSplitTipsRequest) Descriptor() ([]byte, []int) {
	return file_moego_api_order_v1_split_tips_api_proto_rawDescGZIP(), []int{1}
}

func (x *SaveSplitTipsRequest) GetOrderId() int64 {
	if x != nil {
		return x.OrderId
	}
	return 0
}

func (x *SaveSplitTipsRequest) GetSplitMethod() v1.SplitTipsMethod {
	if x != nil {
		return x.SplitMethod
	}
	return v1.SplitTipsMethod(0)
}

func (x *SaveSplitTipsRequest) GetCustomizedType() v1.CustomizedTipType {
	if x != nil && x.CustomizedType != nil {
		return *x.CustomizedType
	}
	return v1.CustomizedTipType(0)
}

func (x *SaveSplitTipsRequest) GetCustomizedConfig() []*CustomizedTipConfigRequest {
	if x != nil {
		return x.CustomizedConfig
	}
	return nil
}

func (x *SaveSplitTipsRequest) GetBusinessTipAmount() *decimal.Decimal {
	if x != nil {
		return x.BusinessTipAmount
	}
	return nil
}

// save tip split response
type SaveSplitTipsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// save result
	Success bool `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
}

func (x *SaveSplitTipsResponse) Reset() {
	*x = SaveSplitTipsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_order_v1_split_tips_api_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SaveSplitTipsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SaveSplitTipsResponse) ProtoMessage() {}

func (x *SaveSplitTipsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_order_v1_split_tips_api_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SaveSplitTipsResponse.ProtoReflect.Descriptor instead.
func (*SaveSplitTipsResponse) Descriptor() ([]byte, []int) {
	return file_moego_api_order_v1_split_tips_api_proto_rawDescGZIP(), []int{2}
}

func (x *SaveSplitTipsResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

// GetTipsSplitParams
type GetTipsSplitParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// source id
	SourceId int64 `protobuf:"varint,1,opt,name=source_id,json=sourceId,proto3" json:"source_id,omitempty"`
	// source type
	SourceType v1.OrderSourceType `protobuf:"varint,2,opt,name=source_type,json=sourceType,proto3,enum=moego.models.order.v1.OrderSourceType" json:"source_type,omitempty"`
	// business id
	BusinessId int64 `protobuf:"varint,3,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
}

func (x *GetTipsSplitParams) Reset() {
	*x = GetTipsSplitParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_order_v1_split_tips_api_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTipsSplitParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTipsSplitParams) ProtoMessage() {}

func (x *GetTipsSplitParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_order_v1_split_tips_api_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTipsSplitParams.ProtoReflect.Descriptor instead.
func (*GetTipsSplitParams) Descriptor() ([]byte, []int) {
	return file_moego_api_order_v1_split_tips_api_proto_rawDescGZIP(), []int{3}
}

func (x *GetTipsSplitParams) GetSourceId() int64 {
	if x != nil {
		return x.SourceId
	}
	return 0
}

func (x *GetTipsSplitParams) GetSourceType() v1.OrderSourceType {
	if x != nil {
		return x.SourceType
	}
	return v1.OrderSourceType(0)
}

func (x *GetTipsSplitParams) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

// GetTipsSplitResult
type GetTipsSplitResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// total collected tips from completed orders
	TipsSplit *v1.TipsSplitModel `protobuf:"bytes,1,opt,name=tips_split,json=tipsSplit,proto3" json:"tips_split,omitempty"`
	// tips split records for each staff
	TipsSplitDetail []*v1.TipsSplitDetailView `protobuf:"bytes,2,rep,name=tips_split_detail,json=tipsSplitDetail,proto3" json:"tips_split_detail,omitempty"`
	// completed order list
	Orders []*v1.OrderModelV1 `protobuf:"bytes,3,rep,name=orders,proto3" json:"orders,omitempty"`
	// Refunded orders.
	RefundOrders []*v1.RefundOrderModel `protobuf:"bytes,4,rep,name=refund_orders,json=refundOrders,proto3" json:"refund_orders,omitempty"`
}

func (x *GetTipsSplitResult) Reset() {
	*x = GetTipsSplitResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_order_v1_split_tips_api_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTipsSplitResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTipsSplitResult) ProtoMessage() {}

func (x *GetTipsSplitResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_order_v1_split_tips_api_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTipsSplitResult.ProtoReflect.Descriptor instead.
func (*GetTipsSplitResult) Descriptor() ([]byte, []int) {
	return file_moego_api_order_v1_split_tips_api_proto_rawDescGZIP(), []int{4}
}

func (x *GetTipsSplitResult) GetTipsSplit() *v1.TipsSplitModel {
	if x != nil {
		return x.TipsSplit
	}
	return nil
}

func (x *GetTipsSplitResult) GetTipsSplitDetail() []*v1.TipsSplitDetailView {
	if x != nil {
		return x.TipsSplitDetail
	}
	return nil
}

func (x *GetTipsSplitResult) GetOrders() []*v1.OrderModelV1 {
	if x != nil {
		return x.Orders
	}
	return nil
}

func (x *GetTipsSplitResult) GetRefundOrders() []*v1.RefundOrderModel {
	if x != nil {
		return x.RefundOrders
	}
	return nil
}

// EditStaffAndTipsSplitParams
type EditStaffAndTipsSplitParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// source id
	SourceId int64 `protobuf:"varint,1,opt,name=source_id,json=sourceId,proto3" json:"source_id,omitempty"`
	// source type
	SourceType v1.OrderSourceType `protobuf:"varint,2,opt,name=source_type,json=sourceType,proto3,enum=moego.models.order.v1.OrderSourceType" json:"source_type,omitempty"`
	// edit staff
	EditStaffs []*v1.EditStaffDef `protobuf:"bytes,3,rep,name=edit_staffs,json=editStaffs,proto3" json:"edit_staffs,omitempty"`
	// tips split config
	TipsSplitConfig *v1.TipsSplitModel_TipsSplitConfig `protobuf:"bytes,4,opt,name=tips_split_config,json=tipsSplitConfig,proto3,oneof" json:"tips_split_config,omitempty"`
	// business id
	BusinessId int64 `protobuf:"varint,5,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
}

func (x *EditStaffAndTipsSplitParams) Reset() {
	*x = EditStaffAndTipsSplitParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_order_v1_split_tips_api_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EditStaffAndTipsSplitParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EditStaffAndTipsSplitParams) ProtoMessage() {}

func (x *EditStaffAndTipsSplitParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_order_v1_split_tips_api_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EditStaffAndTipsSplitParams.ProtoReflect.Descriptor instead.
func (*EditStaffAndTipsSplitParams) Descriptor() ([]byte, []int) {
	return file_moego_api_order_v1_split_tips_api_proto_rawDescGZIP(), []int{5}
}

func (x *EditStaffAndTipsSplitParams) GetSourceId() int64 {
	if x != nil {
		return x.SourceId
	}
	return 0
}

func (x *EditStaffAndTipsSplitParams) GetSourceType() v1.OrderSourceType {
	if x != nil {
		return x.SourceType
	}
	return v1.OrderSourceType(0)
}

func (x *EditStaffAndTipsSplitParams) GetEditStaffs() []*v1.EditStaffDef {
	if x != nil {
		return x.EditStaffs
	}
	return nil
}

func (x *EditStaffAndTipsSplitParams) GetTipsSplitConfig() *v1.TipsSplitModel_TipsSplitConfig {
	if x != nil {
		return x.TipsSplitConfig
	}
	return nil
}

func (x *EditStaffAndTipsSplitParams) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

// PreviewEditApptLevelTipsSplitResult
type PreviewEditTipsSplitResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// split config
	SplitConfig *v1.TipsSplitModel_TipsSplitConfig `protobuf:"bytes,1,opt,name=split_config,json=splitConfig,proto3" json:"split_config,omitempty"`
}

func (x *PreviewEditTipsSplitResult) Reset() {
	*x = PreviewEditTipsSplitResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_order_v1_split_tips_api_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PreviewEditTipsSplitResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PreviewEditTipsSplitResult) ProtoMessage() {}

func (x *PreviewEditTipsSplitResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_order_v1_split_tips_api_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PreviewEditTipsSplitResult.ProtoReflect.Descriptor instead.
func (*PreviewEditTipsSplitResult) Descriptor() ([]byte, []int) {
	return file_moego_api_order_v1_split_tips_api_proto_rawDescGZIP(), []int{6}
}

func (x *PreviewEditTipsSplitResult) GetSplitConfig() *v1.TipsSplitModel_TipsSplitConfig {
	if x != nil {
		return x.SplitConfig
	}
	return nil
}

// EditTipsSplitResult
type EditTipsSplitResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// tips split records for each staff
	TipsSplitDetails []*v1.TipsSplitDetailModel `protobuf:"bytes,1,rep,name=tips_split_details,json=tipsSplitDetails,proto3" json:"tips_split_details,omitempty"`
}

func (x *EditTipsSplitResult) Reset() {
	*x = EditTipsSplitResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_order_v1_split_tips_api_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EditTipsSplitResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EditTipsSplitResult) ProtoMessage() {}

func (x *EditTipsSplitResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_order_v1_split_tips_api_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EditTipsSplitResult.ProtoReflect.Descriptor instead.
func (*EditTipsSplitResult) Descriptor() ([]byte, []int) {
	return file_moego_api_order_v1_split_tips_api_proto_rawDescGZIP(), []int{7}
}

func (x *EditTipsSplitResult) GetTipsSplitDetails() []*v1.TipsSplitDetailModel {
	if x != nil {
		return x.TipsSplitDetails
	}
	return nil
}

// GetTipsSplitChangedStatusParams
type GetTipsSplitChangedStatusParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// source id
	SourceId int64 `protobuf:"varint,1,opt,name=source_id,json=sourceId,proto3" json:"source_id,omitempty"`
	// source type
	SourceType v1.OrderSourceType `protobuf:"varint,2,opt,name=source_type,json=sourceType,proto3,enum=moego.models.order.v1.OrderSourceType" json:"source_type,omitempty"`
}

func (x *GetTipsSplitChangedStatusParams) Reset() {
	*x = GetTipsSplitChangedStatusParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_order_v1_split_tips_api_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTipsSplitChangedStatusParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTipsSplitChangedStatusParams) ProtoMessage() {}

func (x *GetTipsSplitChangedStatusParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_order_v1_split_tips_api_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTipsSplitChangedStatusParams.ProtoReflect.Descriptor instead.
func (*GetTipsSplitChangedStatusParams) Descriptor() ([]byte, []int) {
	return file_moego_api_order_v1_split_tips_api_proto_rawDescGZIP(), []int{8}
}

func (x *GetTipsSplitChangedStatusParams) GetSourceId() int64 {
	if x != nil {
		return x.SourceId
	}
	return 0
}

func (x *GetTipsSplitChangedStatusParams) GetSourceType() v1.OrderSourceType {
	if x != nil {
		return x.SourceType
	}
	return v1.OrderSourceType(0)
}

// GetTipsSplitChangedStatusResult
type GetTipsSplitChangedStatusResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// indicate tips split changed status
	IsChanged bool `protobuf:"varint,1,opt,name=is_changed,json=isChanged,proto3" json:"is_changed,omitempty"`
}

func (x *GetTipsSplitChangedStatusResult) Reset() {
	*x = GetTipsSplitChangedStatusResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_order_v1_split_tips_api_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTipsSplitChangedStatusResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTipsSplitChangedStatusResult) ProtoMessage() {}

func (x *GetTipsSplitChangedStatusResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_order_v1_split_tips_api_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTipsSplitChangedStatusResult.ProtoReflect.Descriptor instead.
func (*GetTipsSplitChangedStatusResult) Descriptor() ([]byte, []int) {
	return file_moego_api_order_v1_split_tips_api_proto_rawDescGZIP(), []int{9}
}

func (x *GetTipsSplitChangedStatusResult) GetIsChanged() bool {
	if x != nil {
		return x.IsChanged
	}
	return false
}

// ClearTipsSplitChangedStatusParams
type ClearTipsSplitChangedStatusParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// source id
	SourceId int64 `protobuf:"varint,1,opt,name=source_id,json=sourceId,proto3" json:"source_id,omitempty"`
	// source type
	SourceType v1.OrderSourceType `protobuf:"varint,2,opt,name=source_type,json=sourceType,proto3,enum=moego.models.order.v1.OrderSourceType" json:"source_type,omitempty"`
}

func (x *ClearTipsSplitChangedStatusParams) Reset() {
	*x = ClearTipsSplitChangedStatusParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_order_v1_split_tips_api_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ClearTipsSplitChangedStatusParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClearTipsSplitChangedStatusParams) ProtoMessage() {}

func (x *ClearTipsSplitChangedStatusParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_order_v1_split_tips_api_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClearTipsSplitChangedStatusParams.ProtoReflect.Descriptor instead.
func (*ClearTipsSplitChangedStatusParams) Descriptor() ([]byte, []int) {
	return file_moego_api_order_v1_split_tips_api_proto_rawDescGZIP(), []int{10}
}

func (x *ClearTipsSplitChangedStatusParams) GetSourceId() int64 {
	if x != nil {
		return x.SourceId
	}
	return 0
}

func (x *ClearTipsSplitChangedStatusParams) GetSourceType() v1.OrderSourceType {
	if x != nil {
		return x.SourceType
	}
	return v1.OrderSourceType(0)
}

// ClearTipsSplitChangedStatusResult
type ClearTipsSplitChangedStatusResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ClearTipsSplitChangedStatusResult) Reset() {
	*x = ClearTipsSplitChangedStatusResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_order_v1_split_tips_api_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ClearTipsSplitChangedStatusResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClearTipsSplitChangedStatusResult) ProtoMessage() {}

func (x *ClearTipsSplitChangedStatusResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_order_v1_split_tips_api_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClearTipsSplitChangedStatusResult.ProtoReflect.Descriptor instead.
func (*ClearTipsSplitChangedStatusResult) Descriptor() ([]byte, []int) {
	return file_moego_api_order_v1_split_tips_api_proto_rawDescGZIP(), []int{11}
}

var File_moego_api_order_v1_split_tips_api_proto protoreflect.FileDescriptor

var file_moego_api_order_v1_split_tips_api_proto_rawDesc = []byte{
	0x0a, 0x27, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x70, 0x6c, 0x69, 0x74, 0x5f, 0x74, 0x69, 0x70, 0x73, 0x5f,
	0x61, 0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x12, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x1a, 0x19, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x64, 0x65, 0x63, 0x69, 0x6d,
	0x61, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x27, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x28, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2f, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2f,
	0x76, 0x31, 0x2f, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x5f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2b, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72,
	0x2f, 0x76, 0x31, 0x2f, 0x73, 0x70, 0x6c, 0x69, 0x74, 0x5f, 0x74, 0x69, 0x70, 0x73, 0x5f, 0x64,
	0x65, 0x66, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2b, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2f, 0x76, 0x31,
	0x2f, 0x73, 0x70, 0x6c, 0x69, 0x74, 0x5f, 0x74, 0x69, 0x70, 0x73, 0x5f, 0x65, 0x6e, 0x75, 0x6d,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2d, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x70,
	0x6c, 0x69, 0x74, 0x5f, 0x74, 0x69, 0x70, 0x73, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f,
	0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xae,
	0x01, 0x0a, 0x1a, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x69, 0x7a, 0x65, 0x64, 0x54, 0x69, 0x70,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x19, 0x0a,
	0x08, 0x73, 0x74, 0x61, 0x66, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x07, 0x73, 0x74, 0x61, 0x66, 0x66, 0x49, 0x64, 0x12, 0x2b, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75,
	0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x01, 0x42, 0x0e, 0xfa, 0x42, 0x0b, 0x12, 0x09, 0x29,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x48, 0x00, 0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75,
	0x6e, 0x74, 0x88, 0x01, 0x01, 0x12, 0x2e, 0x0a, 0x0a, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74,
	0x61, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x1a, 0x04,
	0x18, 0x64, 0x28, 0x00, 0x48, 0x01, 0x52, 0x0a, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x61,
	0x67, 0x65, 0x88, 0x01, 0x01, 0x42, 0x09, 0x0a, 0x07, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74,
	0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x61, 0x67, 0x65, 0x22,
	0xc0, 0x03, 0x0a, 0x14, 0x53, 0x61, 0x76, 0x65, 0x53, 0x70, 0x6c, 0x69, 0x74, 0x54, 0x69, 0x70,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x49, 0x64, 0x12, 0x55, 0x0a, 0x0c, 0x73, 0x70, 0x6c, 0x69, 0x74, 0x5f, 0x6d, 0x65, 0x74,
	0x68, 0x6f, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x26, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76,
	0x31, 0x2e, 0x53, 0x70, 0x6c, 0x69, 0x74, 0x54, 0x69, 0x70, 0x73, 0x4d, 0x65, 0x74, 0x68, 0x6f,
	0x64, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x52, 0x0b, 0x73,
	0x70, 0x6c, 0x69, 0x74, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x12, 0x62, 0x0a, 0x0f, 0x63, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x69, 0x7a, 0x65, 0x64, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x28, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x69, 0x7a, 0x65, 0x64, 0x54, 0x69, 0x70, 0x54, 0x79, 0x70, 0x65, 0x42, 0x0a, 0xfa,
	0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x48, 0x00, 0x52, 0x0e, 0x63, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x69, 0x7a, 0x65, 0x64, 0x54, 0x79, 0x70, 0x65, 0x88, 0x01, 0x01, 0x12, 0x5b,
	0x0a, 0x11, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x69, 0x7a, 0x65, 0x64, 0x5f, 0x63, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x43,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x69, 0x7a, 0x65, 0x64, 0x54, 0x69, 0x70, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x10, 0x63, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x69, 0x7a, 0x65, 0x64, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x49, 0x0a, 0x13, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x74, 0x69, 0x70, 0x5f, 0x61, 0x6d, 0x6f, 0x75,
	0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x65, 0x63, 0x69, 0x6d, 0x61, 0x6c, 0x48, 0x01,
	0x52, 0x11, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x54, 0x69, 0x70, 0x41, 0x6d, 0x6f,
	0x75, 0x6e, 0x74, 0x88, 0x01, 0x01, 0x42, 0x12, 0x0a, 0x10, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x69, 0x7a, 0x65, 0x64, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x42, 0x16, 0x0a, 0x14, 0x5f, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x74, 0x69, 0x70, 0x5f, 0x61, 0x6d, 0x6f, 0x75,
	0x6e, 0x74, 0x22, 0x31, 0x0a, 0x15, 0x53, 0x61, 0x76, 0x65, 0x53, 0x70, 0x6c, 0x69, 0x74, 0x54,
	0x69, 0x70, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x73,
	0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x73, 0x75,
	0x63, 0x63, 0x65, 0x73, 0x73, 0x22, 0xbd, 0x01, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x54, 0x69, 0x70,
	0x73, 0x53, 0x70, 0x6c, 0x69, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x24, 0x0a, 0x09,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x08, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x49, 0x64, 0x12, 0x57, 0x0a, 0x0b, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x26, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e,
	0x4f, 0x72, 0x64, 0x65, 0x72, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x42,
	0x0e, 0xfa, 0x42, 0x0b, 0x82, 0x01, 0x08, 0x10, 0x01, 0x20, 0x00, 0x20, 0x01, 0x20, 0x03, 0x52,
	0x0a, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x28, 0x0a, 0x0b, 0x62,
	0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x49, 0x64, 0x22, 0xbd, 0x02, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x54, 0x69, 0x70,
	0x73, 0x53, 0x70, 0x6c, 0x69, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x44, 0x0a, 0x0a,
	0x74, 0x69, 0x70, 0x73, 0x5f, 0x73, 0x70, 0x6c, 0x69, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x25, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x69, 0x70, 0x73, 0x53, 0x70, 0x6c,
	0x69, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x09, 0x74, 0x69, 0x70, 0x73, 0x53, 0x70, 0x6c,
	0x69, 0x74, 0x12, 0x56, 0x0a, 0x11, 0x74, 0x69, 0x70, 0x73, 0x5f, 0x73, 0x70, 0x6c, 0x69, 0x74,
	0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2a, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x69, 0x70, 0x73, 0x53, 0x70, 0x6c, 0x69, 0x74, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x56, 0x69, 0x65, 0x77, 0x52, 0x0f, 0x74, 0x69, 0x70, 0x73, 0x53,
	0x70, 0x6c, 0x69, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x3b, 0x0a, 0x06, 0x6f, 0x72,
	0x64, 0x65, 0x72, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e,
	0x76, 0x31, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x56, 0x31, 0x52,
	0x06, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x12, 0x4c, 0x0a, 0x0d, 0x72, 0x65, 0x66, 0x75, 0x6e,
	0x64, 0x5f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x27,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72,
	0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x4f, 0x72, 0x64,
	0x65, 0x72, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x0c, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x4f,
	0x72, 0x64, 0x65, 0x72, 0x73, 0x22, 0x8a, 0x03, 0x0a, 0x1b, 0x45, 0x64, 0x69, 0x74, 0x53, 0x74,
	0x61, 0x66, 0x66, 0x41, 0x6e, 0x64, 0x54, 0x69, 0x70, 0x73, 0x53, 0x70, 0x6c, 0x69, 0x74, 0x50,
	0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x24, 0x0a, 0x09, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20,
	0x00, 0x52, 0x08, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x49, 0x64, 0x12, 0x57, 0x0a, 0x0b, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x26, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x53, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x42, 0x0e, 0xfa, 0x42, 0x0b, 0x82, 0x01, 0x08,
	0x10, 0x01, 0x20, 0x00, 0x20, 0x01, 0x20, 0x03, 0x52, 0x0a, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x44, 0x0a, 0x0b, 0x65, 0x64, 0x69, 0x74, 0x5f, 0x73, 0x74, 0x61,
	0x66, 0x66, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76,
	0x31, 0x2e, 0x45, 0x64, 0x69, 0x74, 0x53, 0x74, 0x61, 0x66, 0x66, 0x44, 0x65, 0x66, 0x52, 0x0a,
	0x65, 0x64, 0x69, 0x74, 0x53, 0x74, 0x61, 0x66, 0x66, 0x73, 0x12, 0x66, 0x0a, 0x11, 0x74, 0x69,
	0x70, 0x73, 0x5f, 0x73, 0x70, 0x6c, 0x69, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x69,
	0x70, 0x73, 0x53, 0x70, 0x6c, 0x69, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x54, 0x69, 0x70,
	0x73, 0x53, 0x70, 0x6c, 0x69, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x48, 0x00, 0x52, 0x0f,
	0x74, 0x69, 0x70, 0x73, 0x53, 0x70, 0x6c, 0x69, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x88,
	0x01, 0x01, 0x12, 0x28, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69,
	0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00,
	0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x42, 0x14, 0x0a, 0x12,
	0x5f, 0x74, 0x69, 0x70, 0x73, 0x5f, 0x73, 0x70, 0x6c, 0x69, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x22, 0x76, 0x0a, 0x1a, 0x50, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x45, 0x64, 0x69,
	0x74, 0x54, 0x69, 0x70, 0x73, 0x53, 0x70, 0x6c, 0x69, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x12, 0x58, 0x0a, 0x0c, 0x73, 0x70, 0x6c, 0x69, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x54,
	0x69, 0x70, 0x73, 0x53, 0x70, 0x6c, 0x69, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x54, 0x69,
	0x70, 0x73, 0x53, 0x70, 0x6c, 0x69, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x0b, 0x73,
	0x70, 0x6c, 0x69, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x22, 0x70, 0x0a, 0x13, 0x45, 0x64,
	0x69, 0x74, 0x54, 0x69, 0x70, 0x73, 0x53, 0x70, 0x6c, 0x69, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x12, 0x59, 0x0a, 0x12, 0x74, 0x69, 0x70, 0x73, 0x5f, 0x73, 0x70, 0x6c, 0x69, 0x74, 0x5f,
	0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2b, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x69, 0x70, 0x73, 0x53, 0x70, 0x6c, 0x69, 0x74, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x10, 0x74, 0x69, 0x70, 0x73,
	0x53, 0x70, 0x6c, 0x69, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x22, 0x9c, 0x01, 0x0a,
	0x1f, 0x47, 0x65, 0x74, 0x54, 0x69, 0x70, 0x73, 0x53, 0x70, 0x6c, 0x69, 0x74, 0x43, 0x68, 0x61,
	0x6e, 0x67, 0x65, 0x64, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73,
	0x12, 0x24, 0x0a, 0x09, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x08, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x49, 0x64, 0x12, 0x53, 0x0a, 0x0b, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x26, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72,
	0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54,
	0x79, 0x70, 0x65, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x52,
	0x0a, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x22, 0x40, 0x0a, 0x1f, 0x47,
	0x65, 0x74, 0x54, 0x69, 0x70, 0x73, 0x53, 0x70, 0x6c, 0x69, 0x74, 0x43, 0x68, 0x61, 0x6e, 0x67,
	0x65, 0x64, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x1d,
	0x0a, 0x0a, 0x69, 0x73, 0x5f, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x09, 0x69, 0x73, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x64, 0x22, 0x9e, 0x01,
	0x0a, 0x21, 0x43, 0x6c, 0x65, 0x61, 0x72, 0x54, 0x69, 0x70, 0x73, 0x53, 0x70, 0x6c, 0x69, 0x74,
	0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x64, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x50, 0x61, 0x72,
	0x61, 0x6d, 0x73, 0x12, 0x24, 0x0a, 0x09, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52,
	0x08, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x49, 0x64, 0x12, 0x53, 0x0a, 0x0b, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x26,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72,
	0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x53, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01,
	0x20, 0x00, 0x52, 0x0a, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x22, 0x23,
	0x0a, 0x21, 0x43, 0x6c, 0x65, 0x61, 0x72, 0x54, 0x69, 0x70, 0x73, 0x53, 0x70, 0x6c, 0x69, 0x74,
	0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x64, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x32, 0xd8, 0x05, 0x0a, 0x10, 0x53, 0x70, 0x6c, 0x69, 0x74, 0x54, 0x69, 0x70,
	0x73, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x63, 0x0a, 0x0c, 0x53, 0x61, 0x76, 0x65,
	0x54, 0x69, 0x70, 0x53, 0x70, 0x6c, 0x69, 0x74, 0x12, 0x28, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x61,
	0x76, 0x65, 0x53, 0x70, 0x6c, 0x69, 0x74, 0x54, 0x69, 0x70, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x61, 0x76, 0x65, 0x53, 0x70, 0x6c, 0x69,
	0x74, 0x54, 0x69, 0x70, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x65, 0x0a,
	0x13, 0x47, 0x65, 0x74, 0x54, 0x69, 0x70, 0x73, 0x53, 0x70, 0x6c, 0x69, 0x74, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x73, 0x12, 0x26, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x54, 0x69, 0x70,
	0x73, 0x53, 0x70, 0x6c, 0x69, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x26, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76,
	0x31, 0x2e, 0x47, 0x65, 0x74, 0x54, 0x69, 0x70, 0x73, 0x53, 0x70, 0x6c, 0x69, 0x74, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x12, 0x77, 0x0a, 0x14, 0x50, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x45,
	0x64, 0x69, 0x74, 0x54, 0x69, 0x70, 0x73, 0x53, 0x70, 0x6c, 0x69, 0x74, 0x12, 0x2f, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76,
	0x31, 0x2e, 0x45, 0x64, 0x69, 0x74, 0x53, 0x74, 0x61, 0x66, 0x66, 0x41, 0x6e, 0x64, 0x54, 0x69,
	0x70, 0x73, 0x53, 0x70, 0x6c, 0x69, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x2e, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e,
	0x76, 0x31, 0x2e, 0x50, 0x72, 0x65, 0x76, 0x69, 0x65, 0x77, 0x45, 0x64, 0x69, 0x74, 0x54, 0x69,
	0x70, 0x73, 0x53, 0x70, 0x6c, 0x69, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x69, 0x0a,
	0x0d, 0x45, 0x64, 0x69, 0x74, 0x54, 0x69, 0x70, 0x73, 0x53, 0x70, 0x6c, 0x69, 0x74, 0x12, 0x2f,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72,
	0x2e, 0x76, 0x31, 0x2e, 0x45, 0x64, 0x69, 0x74, 0x53, 0x74, 0x61, 0x66, 0x66, 0x41, 0x6e, 0x64,
	0x54, 0x69, 0x70, 0x73, 0x53, 0x70, 0x6c, 0x69, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a,
	0x27, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x64, 0x69, 0x74, 0x54, 0x69, 0x70, 0x73, 0x53, 0x70, 0x6c,
	0x69, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x85, 0x01, 0x0a, 0x19, 0x47, 0x65, 0x74,
	0x54, 0x69, 0x70, 0x73, 0x53, 0x70, 0x6c, 0x69, 0x74, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x64,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x54,
	0x69, 0x70, 0x73, 0x53, 0x70, 0x6c, 0x69, 0x74, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x64, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x33, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31,
	0x2e, 0x47, 0x65, 0x74, 0x54, 0x69, 0x70, 0x73, 0x53, 0x70, 0x6c, 0x69, 0x74, 0x43, 0x68, 0x61,
	0x6e, 0x67, 0x65, 0x64, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x12, 0x8b, 0x01, 0x0a, 0x1b, 0x43, 0x6c, 0x65, 0x61, 0x72, 0x54, 0x69, 0x70, 0x73, 0x53, 0x70,
	0x6c, 0x69, 0x74, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x64, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6c, 0x65, 0x61, 0x72, 0x54, 0x69, 0x70, 0x73, 0x53,
	0x70, 0x6c, 0x69, 0x74, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x64, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6c, 0x65,
	0x61, 0x72, 0x54, 0x69, 0x70, 0x73, 0x53, 0x70, 0x6c, 0x69, 0x74, 0x43, 0x68, 0x61, 0x6e, 0x67,
	0x65, 0x64, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x42, 0x72,
	0x0a, 0x1a, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x52,
	0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f,
	0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70,
	0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75,
	0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x3b, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x61, 0x70, 0x69,
	0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_api_order_v1_split_tips_api_proto_rawDescOnce sync.Once
	file_moego_api_order_v1_split_tips_api_proto_rawDescData = file_moego_api_order_v1_split_tips_api_proto_rawDesc
)

func file_moego_api_order_v1_split_tips_api_proto_rawDescGZIP() []byte {
	file_moego_api_order_v1_split_tips_api_proto_rawDescOnce.Do(func() {
		file_moego_api_order_v1_split_tips_api_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_api_order_v1_split_tips_api_proto_rawDescData)
	})
	return file_moego_api_order_v1_split_tips_api_proto_rawDescData
}

var file_moego_api_order_v1_split_tips_api_proto_msgTypes = make([]protoimpl.MessageInfo, 12)
var file_moego_api_order_v1_split_tips_api_proto_goTypes = []interface{}{
	(*CustomizedTipConfigRequest)(nil),        // 0: moego.api.order.v1.CustomizedTipConfigRequest
	(*SaveSplitTipsRequest)(nil),              // 1: moego.api.order.v1.SaveSplitTipsRequest
	(*SaveSplitTipsResponse)(nil),             // 2: moego.api.order.v1.SaveSplitTipsResponse
	(*GetTipsSplitParams)(nil),                // 3: moego.api.order.v1.GetTipsSplitParams
	(*GetTipsSplitResult)(nil),                // 4: moego.api.order.v1.GetTipsSplitResult
	(*EditStaffAndTipsSplitParams)(nil),       // 5: moego.api.order.v1.EditStaffAndTipsSplitParams
	(*PreviewEditTipsSplitResult)(nil),        // 6: moego.api.order.v1.PreviewEditTipsSplitResult
	(*EditTipsSplitResult)(nil),               // 7: moego.api.order.v1.EditTipsSplitResult
	(*GetTipsSplitChangedStatusParams)(nil),   // 8: moego.api.order.v1.GetTipsSplitChangedStatusParams
	(*GetTipsSplitChangedStatusResult)(nil),   // 9: moego.api.order.v1.GetTipsSplitChangedStatusResult
	(*ClearTipsSplitChangedStatusParams)(nil), // 10: moego.api.order.v1.ClearTipsSplitChangedStatusParams
	(*ClearTipsSplitChangedStatusResult)(nil), // 11: moego.api.order.v1.ClearTipsSplitChangedStatusResult
	(v1.SplitTipsMethod)(0),                   // 12: moego.models.order.v1.SplitTipsMethod
	(v1.CustomizedTipType)(0),                 // 13: moego.models.order.v1.CustomizedTipType
	(*decimal.Decimal)(nil),                   // 14: google.type.Decimal
	(v1.OrderSourceType)(0),                   // 15: moego.models.order.v1.OrderSourceType
	(*v1.TipsSplitModel)(nil),                 // 16: moego.models.order.v1.TipsSplitModel
	(*v1.TipsSplitDetailView)(nil),            // 17: moego.models.order.v1.TipsSplitDetailView
	(*v1.OrderModelV1)(nil),                   // 18: moego.models.order.v1.OrderModelV1
	(*v1.RefundOrderModel)(nil),               // 19: moego.models.order.v1.RefundOrderModel
	(*v1.EditStaffDef)(nil),                   // 20: moego.models.order.v1.EditStaffDef
	(*v1.TipsSplitModel_TipsSplitConfig)(nil), // 21: moego.models.order.v1.TipsSplitModel.TipsSplitConfig
	(*v1.TipsSplitDetailModel)(nil),           // 22: moego.models.order.v1.TipsSplitDetailModel
}
var file_moego_api_order_v1_split_tips_api_proto_depIdxs = []int32{
	12, // 0: moego.api.order.v1.SaveSplitTipsRequest.split_method:type_name -> moego.models.order.v1.SplitTipsMethod
	13, // 1: moego.api.order.v1.SaveSplitTipsRequest.customized_type:type_name -> moego.models.order.v1.CustomizedTipType
	0,  // 2: moego.api.order.v1.SaveSplitTipsRequest.customized_config:type_name -> moego.api.order.v1.CustomizedTipConfigRequest
	14, // 3: moego.api.order.v1.SaveSplitTipsRequest.business_tip_amount:type_name -> google.type.Decimal
	15, // 4: moego.api.order.v1.GetTipsSplitParams.source_type:type_name -> moego.models.order.v1.OrderSourceType
	16, // 5: moego.api.order.v1.GetTipsSplitResult.tips_split:type_name -> moego.models.order.v1.TipsSplitModel
	17, // 6: moego.api.order.v1.GetTipsSplitResult.tips_split_detail:type_name -> moego.models.order.v1.TipsSplitDetailView
	18, // 7: moego.api.order.v1.GetTipsSplitResult.orders:type_name -> moego.models.order.v1.OrderModelV1
	19, // 8: moego.api.order.v1.GetTipsSplitResult.refund_orders:type_name -> moego.models.order.v1.RefundOrderModel
	15, // 9: moego.api.order.v1.EditStaffAndTipsSplitParams.source_type:type_name -> moego.models.order.v1.OrderSourceType
	20, // 10: moego.api.order.v1.EditStaffAndTipsSplitParams.edit_staffs:type_name -> moego.models.order.v1.EditStaffDef
	21, // 11: moego.api.order.v1.EditStaffAndTipsSplitParams.tips_split_config:type_name -> moego.models.order.v1.TipsSplitModel.TipsSplitConfig
	21, // 12: moego.api.order.v1.PreviewEditTipsSplitResult.split_config:type_name -> moego.models.order.v1.TipsSplitModel.TipsSplitConfig
	22, // 13: moego.api.order.v1.EditTipsSplitResult.tips_split_details:type_name -> moego.models.order.v1.TipsSplitDetailModel
	15, // 14: moego.api.order.v1.GetTipsSplitChangedStatusParams.source_type:type_name -> moego.models.order.v1.OrderSourceType
	15, // 15: moego.api.order.v1.ClearTipsSplitChangedStatusParams.source_type:type_name -> moego.models.order.v1.OrderSourceType
	1,  // 16: moego.api.order.v1.SplitTipsService.SaveTipSplit:input_type -> moego.api.order.v1.SaveSplitTipsRequest
	3,  // 17: moego.api.order.v1.SplitTipsService.GetTipsSplitDetails:input_type -> moego.api.order.v1.GetTipsSplitParams
	5,  // 18: moego.api.order.v1.SplitTipsService.PreviewEditTipsSplit:input_type -> moego.api.order.v1.EditStaffAndTipsSplitParams
	5,  // 19: moego.api.order.v1.SplitTipsService.EditTipsSplit:input_type -> moego.api.order.v1.EditStaffAndTipsSplitParams
	8,  // 20: moego.api.order.v1.SplitTipsService.GetTipsSplitChangedStatus:input_type -> moego.api.order.v1.GetTipsSplitChangedStatusParams
	10, // 21: moego.api.order.v1.SplitTipsService.ClearTipsSplitChangedStatus:input_type -> moego.api.order.v1.ClearTipsSplitChangedStatusParams
	2,  // 22: moego.api.order.v1.SplitTipsService.SaveTipSplit:output_type -> moego.api.order.v1.SaveSplitTipsResponse
	4,  // 23: moego.api.order.v1.SplitTipsService.GetTipsSplitDetails:output_type -> moego.api.order.v1.GetTipsSplitResult
	6,  // 24: moego.api.order.v1.SplitTipsService.PreviewEditTipsSplit:output_type -> moego.api.order.v1.PreviewEditTipsSplitResult
	7,  // 25: moego.api.order.v1.SplitTipsService.EditTipsSplit:output_type -> moego.api.order.v1.EditTipsSplitResult
	9,  // 26: moego.api.order.v1.SplitTipsService.GetTipsSplitChangedStatus:output_type -> moego.api.order.v1.GetTipsSplitChangedStatusResult
	11, // 27: moego.api.order.v1.SplitTipsService.ClearTipsSplitChangedStatus:output_type -> moego.api.order.v1.ClearTipsSplitChangedStatusResult
	22, // [22:28] is the sub-list for method output_type
	16, // [16:22] is the sub-list for method input_type
	16, // [16:16] is the sub-list for extension type_name
	16, // [16:16] is the sub-list for extension extendee
	0,  // [0:16] is the sub-list for field type_name
}

func init() { file_moego_api_order_v1_split_tips_api_proto_init() }
func file_moego_api_order_v1_split_tips_api_proto_init() {
	if File_moego_api_order_v1_split_tips_api_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_api_order_v1_split_tips_api_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CustomizedTipConfigRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_order_v1_split_tips_api_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SaveSplitTipsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_order_v1_split_tips_api_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SaveSplitTipsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_order_v1_split_tips_api_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTipsSplitParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_order_v1_split_tips_api_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTipsSplitResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_order_v1_split_tips_api_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EditStaffAndTipsSplitParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_order_v1_split_tips_api_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PreviewEditTipsSplitResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_order_v1_split_tips_api_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EditTipsSplitResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_order_v1_split_tips_api_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTipsSplitChangedStatusParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_order_v1_split_tips_api_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTipsSplitChangedStatusResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_order_v1_split_tips_api_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ClearTipsSplitChangedStatusParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_order_v1_split_tips_api_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ClearTipsSplitChangedStatusResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_api_order_v1_split_tips_api_proto_msgTypes[0].OneofWrappers = []interface{}{}
	file_moego_api_order_v1_split_tips_api_proto_msgTypes[1].OneofWrappers = []interface{}{}
	file_moego_api_order_v1_split_tips_api_proto_msgTypes[5].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_api_order_v1_split_tips_api_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   12,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_api_order_v1_split_tips_api_proto_goTypes,
		DependencyIndexes: file_moego_api_order_v1_split_tips_api_proto_depIdxs,
		MessageInfos:      file_moego_api_order_v1_split_tips_api_proto_msgTypes,
	}.Build()
	File_moego_api_order_v1_split_tips_api_proto = out.File
	file_moego_api_order_v1_split_tips_api_proto_rawDesc = nil
	file_moego_api_order_v1_split_tips_api_proto_goTypes = nil
	file_moego_api_order_v1_split_tips_api_proto_depIdxs = nil
}
