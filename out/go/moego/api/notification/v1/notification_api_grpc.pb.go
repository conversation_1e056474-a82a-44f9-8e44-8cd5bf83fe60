// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/api/notification/v1/notification_api.proto

package notificationapipb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// NotificationServiceClient is the client API for NotificationService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type NotificationServiceClient interface {
	// get notification with token staff id and token company id
	GetNotifications(ctx context.Context, in *GetNotificationsParams, opts ...grpc.CallOption) (*GetNotificationsResult, error)
	// read notification
	NotificationRead(ctx context.Context, in *NotificationReadParams, opts ...grpc.CallOption) (*NotificationReadResult, error)
	// dismiss notification
	NotificationDismiss(ctx context.Context, in *NotificationDismissParams, opts ...grpc.CallOption) (*NotificationDismissResult, error)
	// read all notification
	NotificationReadAll(ctx context.Context, in *NotificationReadAllParams, opts ...grpc.CallOption) (*NotificationReadAllResult, error)
	// get notification unread count
	GetNotificationUnreadCount(ctx context.Context, in *GetNotificationUnreadCountParams, opts ...grpc.CallOption) (*GetNotificationUnreadCountResult, error)
}

type notificationServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewNotificationServiceClient(cc grpc.ClientConnInterface) NotificationServiceClient {
	return &notificationServiceClient{cc}
}

func (c *notificationServiceClient) GetNotifications(ctx context.Context, in *GetNotificationsParams, opts ...grpc.CallOption) (*GetNotificationsResult, error) {
	out := new(GetNotificationsResult)
	err := c.cc.Invoke(ctx, "/moego.api.notification.v1.NotificationService/GetNotifications", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *notificationServiceClient) NotificationRead(ctx context.Context, in *NotificationReadParams, opts ...grpc.CallOption) (*NotificationReadResult, error) {
	out := new(NotificationReadResult)
	err := c.cc.Invoke(ctx, "/moego.api.notification.v1.NotificationService/NotificationRead", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *notificationServiceClient) NotificationDismiss(ctx context.Context, in *NotificationDismissParams, opts ...grpc.CallOption) (*NotificationDismissResult, error) {
	out := new(NotificationDismissResult)
	err := c.cc.Invoke(ctx, "/moego.api.notification.v1.NotificationService/NotificationDismiss", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *notificationServiceClient) NotificationReadAll(ctx context.Context, in *NotificationReadAllParams, opts ...grpc.CallOption) (*NotificationReadAllResult, error) {
	out := new(NotificationReadAllResult)
	err := c.cc.Invoke(ctx, "/moego.api.notification.v1.NotificationService/NotificationReadAll", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *notificationServiceClient) GetNotificationUnreadCount(ctx context.Context, in *GetNotificationUnreadCountParams, opts ...grpc.CallOption) (*GetNotificationUnreadCountResult, error) {
	out := new(GetNotificationUnreadCountResult)
	err := c.cc.Invoke(ctx, "/moego.api.notification.v1.NotificationService/GetNotificationUnreadCount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// NotificationServiceServer is the server API for NotificationService service.
// All implementations must embed UnimplementedNotificationServiceServer
// for forward compatibility
type NotificationServiceServer interface {
	// get notification with token staff id and token company id
	GetNotifications(context.Context, *GetNotificationsParams) (*GetNotificationsResult, error)
	// read notification
	NotificationRead(context.Context, *NotificationReadParams) (*NotificationReadResult, error)
	// dismiss notification
	NotificationDismiss(context.Context, *NotificationDismissParams) (*NotificationDismissResult, error)
	// read all notification
	NotificationReadAll(context.Context, *NotificationReadAllParams) (*NotificationReadAllResult, error)
	// get notification unread count
	GetNotificationUnreadCount(context.Context, *GetNotificationUnreadCountParams) (*GetNotificationUnreadCountResult, error)
	mustEmbedUnimplementedNotificationServiceServer()
}

// UnimplementedNotificationServiceServer must be embedded to have forward compatible implementations.
type UnimplementedNotificationServiceServer struct {
}

func (UnimplementedNotificationServiceServer) GetNotifications(context.Context, *GetNotificationsParams) (*GetNotificationsResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetNotifications not implemented")
}
func (UnimplementedNotificationServiceServer) NotificationRead(context.Context, *NotificationReadParams) (*NotificationReadResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method NotificationRead not implemented")
}
func (UnimplementedNotificationServiceServer) NotificationDismiss(context.Context, *NotificationDismissParams) (*NotificationDismissResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method NotificationDismiss not implemented")
}
func (UnimplementedNotificationServiceServer) NotificationReadAll(context.Context, *NotificationReadAllParams) (*NotificationReadAllResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method NotificationReadAll not implemented")
}
func (UnimplementedNotificationServiceServer) GetNotificationUnreadCount(context.Context, *GetNotificationUnreadCountParams) (*GetNotificationUnreadCountResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetNotificationUnreadCount not implemented")
}
func (UnimplementedNotificationServiceServer) mustEmbedUnimplementedNotificationServiceServer() {}

// UnsafeNotificationServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to NotificationServiceServer will
// result in compilation errors.
type UnsafeNotificationServiceServer interface {
	mustEmbedUnimplementedNotificationServiceServer()
}

func RegisterNotificationServiceServer(s grpc.ServiceRegistrar, srv NotificationServiceServer) {
	s.RegisterService(&NotificationService_ServiceDesc, srv)
}

func _NotificationService_GetNotifications_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetNotificationsParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NotificationServiceServer).GetNotifications(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.notification.v1.NotificationService/GetNotifications",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NotificationServiceServer).GetNotifications(ctx, req.(*GetNotificationsParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _NotificationService_NotificationRead_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(NotificationReadParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NotificationServiceServer).NotificationRead(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.notification.v1.NotificationService/NotificationRead",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NotificationServiceServer).NotificationRead(ctx, req.(*NotificationReadParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _NotificationService_NotificationDismiss_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(NotificationDismissParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NotificationServiceServer).NotificationDismiss(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.notification.v1.NotificationService/NotificationDismiss",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NotificationServiceServer).NotificationDismiss(ctx, req.(*NotificationDismissParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _NotificationService_NotificationReadAll_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(NotificationReadAllParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NotificationServiceServer).NotificationReadAll(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.notification.v1.NotificationService/NotificationReadAll",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NotificationServiceServer).NotificationReadAll(ctx, req.(*NotificationReadAllParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _NotificationService_GetNotificationUnreadCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetNotificationUnreadCountParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NotificationServiceServer).GetNotificationUnreadCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.notification.v1.NotificationService/GetNotificationUnreadCount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NotificationServiceServer).GetNotificationUnreadCount(ctx, req.(*GetNotificationUnreadCountParams))
	}
	return interceptor(ctx, in, info, handler)
}

// NotificationService_ServiceDesc is the grpc.ServiceDesc for NotificationService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var NotificationService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.api.notification.v1.NotificationService",
	HandlerType: (*NotificationServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetNotifications",
			Handler:    _NotificationService_GetNotifications_Handler,
		},
		{
			MethodName: "NotificationRead",
			Handler:    _NotificationService_NotificationRead_Handler,
		},
		{
			MethodName: "NotificationDismiss",
			Handler:    _NotificationService_NotificationDismiss_Handler,
		},
		{
			MethodName: "NotificationReadAll",
			Handler:    _NotificationService_NotificationReadAll_Handler,
		},
		{
			MethodName: "GetNotificationUnreadCount",
			Handler:    _NotificationService_GetNotificationUnreadCount_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/api/notification/v1/notification_api.proto",
}
