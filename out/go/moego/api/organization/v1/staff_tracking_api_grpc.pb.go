// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/api/organization/v1/staff_tracking_api.proto

package organizationapipb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// StaffTrackingServiceClient is the client API for StaffTrackingService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type StaffTrackingServiceClient interface {
	// upload staff location
	CreateStaffTracking(ctx context.Context, in *CreateStaffTrackingParams, opts ...grpc.CallOption) (*CreateStaffTrackingResult, error)
	// staff location list
	ListStaffTracking(ctx context.Context, in *ListStaffTrackingParams, opts ...grpc.CallOption) (*ListStaffTrackingResult, error)
	// batch create staff tracking
	BatchCreateStaffTracking(ctx context.Context, in *BatchCreateStaffTrackingParams, opts ...grpc.CallOption) (*BatchCreateStaffTrackingResult, error)
}

type staffTrackingServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewStaffTrackingServiceClient(cc grpc.ClientConnInterface) StaffTrackingServiceClient {
	return &staffTrackingServiceClient{cc}
}

func (c *staffTrackingServiceClient) CreateStaffTracking(ctx context.Context, in *CreateStaffTrackingParams, opts ...grpc.CallOption) (*CreateStaffTrackingResult, error) {
	out := new(CreateStaffTrackingResult)
	err := c.cc.Invoke(ctx, "/moego.api.organization.v1.StaffTrackingService/CreateStaffTracking", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *staffTrackingServiceClient) ListStaffTracking(ctx context.Context, in *ListStaffTrackingParams, opts ...grpc.CallOption) (*ListStaffTrackingResult, error) {
	out := new(ListStaffTrackingResult)
	err := c.cc.Invoke(ctx, "/moego.api.organization.v1.StaffTrackingService/ListStaffTracking", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *staffTrackingServiceClient) BatchCreateStaffTracking(ctx context.Context, in *BatchCreateStaffTrackingParams, opts ...grpc.CallOption) (*BatchCreateStaffTrackingResult, error) {
	out := new(BatchCreateStaffTrackingResult)
	err := c.cc.Invoke(ctx, "/moego.api.organization.v1.StaffTrackingService/BatchCreateStaffTracking", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// StaffTrackingServiceServer is the server API for StaffTrackingService service.
// All implementations must embed UnimplementedStaffTrackingServiceServer
// for forward compatibility
type StaffTrackingServiceServer interface {
	// upload staff location
	CreateStaffTracking(context.Context, *CreateStaffTrackingParams) (*CreateStaffTrackingResult, error)
	// staff location list
	ListStaffTracking(context.Context, *ListStaffTrackingParams) (*ListStaffTrackingResult, error)
	// batch create staff tracking
	BatchCreateStaffTracking(context.Context, *BatchCreateStaffTrackingParams) (*BatchCreateStaffTrackingResult, error)
	mustEmbedUnimplementedStaffTrackingServiceServer()
}

// UnimplementedStaffTrackingServiceServer must be embedded to have forward compatible implementations.
type UnimplementedStaffTrackingServiceServer struct {
}

func (UnimplementedStaffTrackingServiceServer) CreateStaffTracking(context.Context, *CreateStaffTrackingParams) (*CreateStaffTrackingResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateStaffTracking not implemented")
}
func (UnimplementedStaffTrackingServiceServer) ListStaffTracking(context.Context, *ListStaffTrackingParams) (*ListStaffTrackingResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListStaffTracking not implemented")
}
func (UnimplementedStaffTrackingServiceServer) BatchCreateStaffTracking(context.Context, *BatchCreateStaffTrackingParams) (*BatchCreateStaffTrackingResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchCreateStaffTracking not implemented")
}
func (UnimplementedStaffTrackingServiceServer) mustEmbedUnimplementedStaffTrackingServiceServer() {}

// UnsafeStaffTrackingServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to StaffTrackingServiceServer will
// result in compilation errors.
type UnsafeStaffTrackingServiceServer interface {
	mustEmbedUnimplementedStaffTrackingServiceServer()
}

func RegisterStaffTrackingServiceServer(s grpc.ServiceRegistrar, srv StaffTrackingServiceServer) {
	s.RegisterService(&StaffTrackingService_ServiceDesc, srv)
}

func _StaffTrackingService_CreateStaffTracking_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateStaffTrackingParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StaffTrackingServiceServer).CreateStaffTracking(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.organization.v1.StaffTrackingService/CreateStaffTracking",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StaffTrackingServiceServer).CreateStaffTracking(ctx, req.(*CreateStaffTrackingParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _StaffTrackingService_ListStaffTracking_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListStaffTrackingParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StaffTrackingServiceServer).ListStaffTracking(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.organization.v1.StaffTrackingService/ListStaffTracking",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StaffTrackingServiceServer).ListStaffTracking(ctx, req.(*ListStaffTrackingParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _StaffTrackingService_BatchCreateStaffTracking_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchCreateStaffTrackingParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StaffTrackingServiceServer).BatchCreateStaffTracking(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.organization.v1.StaffTrackingService/BatchCreateStaffTracking",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StaffTrackingServiceServer).BatchCreateStaffTracking(ctx, req.(*BatchCreateStaffTrackingParams))
	}
	return interceptor(ctx, in, info, handler)
}

// StaffTrackingService_ServiceDesc is the grpc.ServiceDesc for StaffTrackingService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var StaffTrackingService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.api.organization.v1.StaffTrackingService",
	HandlerType: (*StaffTrackingServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateStaffTracking",
			Handler:    _StaffTrackingService_CreateStaffTracking_Handler,
		},
		{
			MethodName: "ListStaffTracking",
			Handler:    _StaffTrackingService_ListStaffTracking_Handler,
		},
		{
			MethodName: "BatchCreateStaffTracking",
			Handler:    _StaffTrackingService_BatchCreateStaffTracking_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/api/organization/v1/staff_tracking_api.proto",
}
