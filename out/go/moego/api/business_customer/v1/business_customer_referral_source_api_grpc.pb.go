// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/api/business_customer/v1/business_customer_referral_source_api.proto

package businesscustomerapipb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// BusinessCustomerReferralSourceServiceClient is the client API for BusinessCustomerReferralSourceService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type BusinessCustomerReferralSourceServiceClient interface {
	// List customer referral source template
	ListCustomerReferralSourceTemplate(ctx context.Context, in *ListCustomerReferralSourceTemplateParams, opts ...grpc.CallOption) (*ListCustomerReferralSourceTemplateResult, error)
	// List customer referral sources of current company
	ListCustomerReferralSource(ctx context.Context, in *ListCustomerReferralSourceParams, opts ...grpc.CallOption) (*ListCustomerReferralSourceResult, error)
	// Create a customer referral source
	CreateCustomerReferralSource(ctx context.Context, in *CreateCustomerReferralSourceParams, opts ...grpc.CallOption) (*CreateCustomerReferralSourceResult, error)
	// Update a customer referral source
	UpdateCustomerReferralSource(ctx context.Context, in *UpdateCustomerReferralSourceParams, opts ...grpc.CallOption) (*UpdateCustomerReferralSourceResult, error)
	// Sort customer referral sources
	SortCustomerReferralSource(ctx context.Context, in *SortCustomerReferralSourceParams, opts ...grpc.CallOption) (*SortCustomerReferralSourceResult, error)
	// Delete a customer referral source
	DeleteCustomerReferralSource(ctx context.Context, in *DeleteCustomerReferralSourceParams, opts ...grpc.CallOption) (*DeleteCustomerReferralSourceResult, error)
}

type businessCustomerReferralSourceServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewBusinessCustomerReferralSourceServiceClient(cc grpc.ClientConnInterface) BusinessCustomerReferralSourceServiceClient {
	return &businessCustomerReferralSourceServiceClient{cc}
}

func (c *businessCustomerReferralSourceServiceClient) ListCustomerReferralSourceTemplate(ctx context.Context, in *ListCustomerReferralSourceTemplateParams, opts ...grpc.CallOption) (*ListCustomerReferralSourceTemplateResult, error) {
	out := new(ListCustomerReferralSourceTemplateResult)
	err := c.cc.Invoke(ctx, "/moego.api.business_customer.v1.BusinessCustomerReferralSourceService/ListCustomerReferralSourceTemplate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessCustomerReferralSourceServiceClient) ListCustomerReferralSource(ctx context.Context, in *ListCustomerReferralSourceParams, opts ...grpc.CallOption) (*ListCustomerReferralSourceResult, error) {
	out := new(ListCustomerReferralSourceResult)
	err := c.cc.Invoke(ctx, "/moego.api.business_customer.v1.BusinessCustomerReferralSourceService/ListCustomerReferralSource", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessCustomerReferralSourceServiceClient) CreateCustomerReferralSource(ctx context.Context, in *CreateCustomerReferralSourceParams, opts ...grpc.CallOption) (*CreateCustomerReferralSourceResult, error) {
	out := new(CreateCustomerReferralSourceResult)
	err := c.cc.Invoke(ctx, "/moego.api.business_customer.v1.BusinessCustomerReferralSourceService/CreateCustomerReferralSource", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessCustomerReferralSourceServiceClient) UpdateCustomerReferralSource(ctx context.Context, in *UpdateCustomerReferralSourceParams, opts ...grpc.CallOption) (*UpdateCustomerReferralSourceResult, error) {
	out := new(UpdateCustomerReferralSourceResult)
	err := c.cc.Invoke(ctx, "/moego.api.business_customer.v1.BusinessCustomerReferralSourceService/UpdateCustomerReferralSource", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessCustomerReferralSourceServiceClient) SortCustomerReferralSource(ctx context.Context, in *SortCustomerReferralSourceParams, opts ...grpc.CallOption) (*SortCustomerReferralSourceResult, error) {
	out := new(SortCustomerReferralSourceResult)
	err := c.cc.Invoke(ctx, "/moego.api.business_customer.v1.BusinessCustomerReferralSourceService/SortCustomerReferralSource", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessCustomerReferralSourceServiceClient) DeleteCustomerReferralSource(ctx context.Context, in *DeleteCustomerReferralSourceParams, opts ...grpc.CallOption) (*DeleteCustomerReferralSourceResult, error) {
	out := new(DeleteCustomerReferralSourceResult)
	err := c.cc.Invoke(ctx, "/moego.api.business_customer.v1.BusinessCustomerReferralSourceService/DeleteCustomerReferralSource", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// BusinessCustomerReferralSourceServiceServer is the server API for BusinessCustomerReferralSourceService service.
// All implementations must embed UnimplementedBusinessCustomerReferralSourceServiceServer
// for forward compatibility
type BusinessCustomerReferralSourceServiceServer interface {
	// List customer referral source template
	ListCustomerReferralSourceTemplate(context.Context, *ListCustomerReferralSourceTemplateParams) (*ListCustomerReferralSourceTemplateResult, error)
	// List customer referral sources of current company
	ListCustomerReferralSource(context.Context, *ListCustomerReferralSourceParams) (*ListCustomerReferralSourceResult, error)
	// Create a customer referral source
	CreateCustomerReferralSource(context.Context, *CreateCustomerReferralSourceParams) (*CreateCustomerReferralSourceResult, error)
	// Update a customer referral source
	UpdateCustomerReferralSource(context.Context, *UpdateCustomerReferralSourceParams) (*UpdateCustomerReferralSourceResult, error)
	// Sort customer referral sources
	SortCustomerReferralSource(context.Context, *SortCustomerReferralSourceParams) (*SortCustomerReferralSourceResult, error)
	// Delete a customer referral source
	DeleteCustomerReferralSource(context.Context, *DeleteCustomerReferralSourceParams) (*DeleteCustomerReferralSourceResult, error)
	mustEmbedUnimplementedBusinessCustomerReferralSourceServiceServer()
}

// UnimplementedBusinessCustomerReferralSourceServiceServer must be embedded to have forward compatible implementations.
type UnimplementedBusinessCustomerReferralSourceServiceServer struct {
}

func (UnimplementedBusinessCustomerReferralSourceServiceServer) ListCustomerReferralSourceTemplate(context.Context, *ListCustomerReferralSourceTemplateParams) (*ListCustomerReferralSourceTemplateResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListCustomerReferralSourceTemplate not implemented")
}
func (UnimplementedBusinessCustomerReferralSourceServiceServer) ListCustomerReferralSource(context.Context, *ListCustomerReferralSourceParams) (*ListCustomerReferralSourceResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListCustomerReferralSource not implemented")
}
func (UnimplementedBusinessCustomerReferralSourceServiceServer) CreateCustomerReferralSource(context.Context, *CreateCustomerReferralSourceParams) (*CreateCustomerReferralSourceResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateCustomerReferralSource not implemented")
}
func (UnimplementedBusinessCustomerReferralSourceServiceServer) UpdateCustomerReferralSource(context.Context, *UpdateCustomerReferralSourceParams) (*UpdateCustomerReferralSourceResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateCustomerReferralSource not implemented")
}
func (UnimplementedBusinessCustomerReferralSourceServiceServer) SortCustomerReferralSource(context.Context, *SortCustomerReferralSourceParams) (*SortCustomerReferralSourceResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SortCustomerReferralSource not implemented")
}
func (UnimplementedBusinessCustomerReferralSourceServiceServer) DeleteCustomerReferralSource(context.Context, *DeleteCustomerReferralSourceParams) (*DeleteCustomerReferralSourceResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteCustomerReferralSource not implemented")
}
func (UnimplementedBusinessCustomerReferralSourceServiceServer) mustEmbedUnimplementedBusinessCustomerReferralSourceServiceServer() {
}

// UnsafeBusinessCustomerReferralSourceServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to BusinessCustomerReferralSourceServiceServer will
// result in compilation errors.
type UnsafeBusinessCustomerReferralSourceServiceServer interface {
	mustEmbedUnimplementedBusinessCustomerReferralSourceServiceServer()
}

func RegisterBusinessCustomerReferralSourceServiceServer(s grpc.ServiceRegistrar, srv BusinessCustomerReferralSourceServiceServer) {
	s.RegisterService(&BusinessCustomerReferralSourceService_ServiceDesc, srv)
}

func _BusinessCustomerReferralSourceService_ListCustomerReferralSourceTemplate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListCustomerReferralSourceTemplateParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessCustomerReferralSourceServiceServer).ListCustomerReferralSourceTemplate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.business_customer.v1.BusinessCustomerReferralSourceService/ListCustomerReferralSourceTemplate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessCustomerReferralSourceServiceServer).ListCustomerReferralSourceTemplate(ctx, req.(*ListCustomerReferralSourceTemplateParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessCustomerReferralSourceService_ListCustomerReferralSource_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListCustomerReferralSourceParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessCustomerReferralSourceServiceServer).ListCustomerReferralSource(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.business_customer.v1.BusinessCustomerReferralSourceService/ListCustomerReferralSource",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessCustomerReferralSourceServiceServer).ListCustomerReferralSource(ctx, req.(*ListCustomerReferralSourceParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessCustomerReferralSourceService_CreateCustomerReferralSource_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateCustomerReferralSourceParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessCustomerReferralSourceServiceServer).CreateCustomerReferralSource(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.business_customer.v1.BusinessCustomerReferralSourceService/CreateCustomerReferralSource",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessCustomerReferralSourceServiceServer).CreateCustomerReferralSource(ctx, req.(*CreateCustomerReferralSourceParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessCustomerReferralSourceService_UpdateCustomerReferralSource_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateCustomerReferralSourceParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessCustomerReferralSourceServiceServer).UpdateCustomerReferralSource(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.business_customer.v1.BusinessCustomerReferralSourceService/UpdateCustomerReferralSource",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessCustomerReferralSourceServiceServer).UpdateCustomerReferralSource(ctx, req.(*UpdateCustomerReferralSourceParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessCustomerReferralSourceService_SortCustomerReferralSource_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SortCustomerReferralSourceParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessCustomerReferralSourceServiceServer).SortCustomerReferralSource(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.business_customer.v1.BusinessCustomerReferralSourceService/SortCustomerReferralSource",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessCustomerReferralSourceServiceServer).SortCustomerReferralSource(ctx, req.(*SortCustomerReferralSourceParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessCustomerReferralSourceService_DeleteCustomerReferralSource_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteCustomerReferralSourceParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessCustomerReferralSourceServiceServer).DeleteCustomerReferralSource(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.business_customer.v1.BusinessCustomerReferralSourceService/DeleteCustomerReferralSource",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessCustomerReferralSourceServiceServer).DeleteCustomerReferralSource(ctx, req.(*DeleteCustomerReferralSourceParams))
	}
	return interceptor(ctx, in, info, handler)
}

// BusinessCustomerReferralSourceService_ServiceDesc is the grpc.ServiceDesc for BusinessCustomerReferralSourceService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var BusinessCustomerReferralSourceService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.api.business_customer.v1.BusinessCustomerReferralSourceService",
	HandlerType: (*BusinessCustomerReferralSourceServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ListCustomerReferralSourceTemplate",
			Handler:    _BusinessCustomerReferralSourceService_ListCustomerReferralSourceTemplate_Handler,
		},
		{
			MethodName: "ListCustomerReferralSource",
			Handler:    _BusinessCustomerReferralSourceService_ListCustomerReferralSource_Handler,
		},
		{
			MethodName: "CreateCustomerReferralSource",
			Handler:    _BusinessCustomerReferralSourceService_CreateCustomerReferralSource_Handler,
		},
		{
			MethodName: "UpdateCustomerReferralSource",
			Handler:    _BusinessCustomerReferralSourceService_UpdateCustomerReferralSource_Handler,
		},
		{
			MethodName: "SortCustomerReferralSource",
			Handler:    _BusinessCustomerReferralSourceService_SortCustomerReferralSource_Handler,
		},
		{
			MethodName: "DeleteCustomerReferralSource",
			Handler:    _BusinessCustomerReferralSourceService_DeleteCustomerReferralSource_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/api/business_customer/v1/business_customer_referral_source_api.proto",
}
