// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/api/business_customer/v1/business_pet_metadata_api.proto

package businesscustomerapipb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// BusinessPetMetadataServiceClient is the client API for BusinessPetMetadataService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type BusinessPetMetadataServiceClient interface {
	// Create pet metadata. Below are some examples of common metadata values:
	// Feeding/Medication schedule: 540 (09:00 AM) / 1080 (06:00 PM)
	// Feeding/Medication unit: Cup / Oz
	// Feeding type: Wet food / Dry food
	// Feeding Source: Owner provide / House provide
	// Feeding Instruction: Free feed / Feed individually
	// Display rules: {Feeding schedule} {Feeding type} {Feeding amount} {Feeding unit} {Feeding instruction}
	CreatePetMetadata(ctx context.Context, in *CreatePetMetadataParams, opts ...grpc.CallOption) (*CreatePetMetadataResult, error)
	// Update pet metadata
	UpdatePetMetadata(ctx context.Context, in *UpdatePetMetadataParams, opts ...grpc.CallOption) (*UpdatePetMetadataResult, error)
	// Delete pet metadata
	// The metadata deleted cannot be added again, but it will not affect the original display
	DeletePetMetadata(ctx context.Context, in *DeletePetMetadataParams, opts ...grpc.CallOption) (*DeletePetMetadataResult, error)
	// List pet metadata
	ListPetMetadata(ctx context.Context, in *ListPetMetadataParams, opts ...grpc.CallOption) (*ListPetMetadataResult, error)
	// Sort pet metadata
	SortPetMetadata(ctx context.Context, in *SortPetMetadataParams, opts ...grpc.CallOption) (*SortPetMetadataResult, error)
}

type businessPetMetadataServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewBusinessPetMetadataServiceClient(cc grpc.ClientConnInterface) BusinessPetMetadataServiceClient {
	return &businessPetMetadataServiceClient{cc}
}

func (c *businessPetMetadataServiceClient) CreatePetMetadata(ctx context.Context, in *CreatePetMetadataParams, opts ...grpc.CallOption) (*CreatePetMetadataResult, error) {
	out := new(CreatePetMetadataResult)
	err := c.cc.Invoke(ctx, "/moego.api.business_customer.v1.BusinessPetMetadataService/CreatePetMetadata", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessPetMetadataServiceClient) UpdatePetMetadata(ctx context.Context, in *UpdatePetMetadataParams, opts ...grpc.CallOption) (*UpdatePetMetadataResult, error) {
	out := new(UpdatePetMetadataResult)
	err := c.cc.Invoke(ctx, "/moego.api.business_customer.v1.BusinessPetMetadataService/UpdatePetMetadata", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessPetMetadataServiceClient) DeletePetMetadata(ctx context.Context, in *DeletePetMetadataParams, opts ...grpc.CallOption) (*DeletePetMetadataResult, error) {
	out := new(DeletePetMetadataResult)
	err := c.cc.Invoke(ctx, "/moego.api.business_customer.v1.BusinessPetMetadataService/DeletePetMetadata", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessPetMetadataServiceClient) ListPetMetadata(ctx context.Context, in *ListPetMetadataParams, opts ...grpc.CallOption) (*ListPetMetadataResult, error) {
	out := new(ListPetMetadataResult)
	err := c.cc.Invoke(ctx, "/moego.api.business_customer.v1.BusinessPetMetadataService/ListPetMetadata", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *businessPetMetadataServiceClient) SortPetMetadata(ctx context.Context, in *SortPetMetadataParams, opts ...grpc.CallOption) (*SortPetMetadataResult, error) {
	out := new(SortPetMetadataResult)
	err := c.cc.Invoke(ctx, "/moego.api.business_customer.v1.BusinessPetMetadataService/SortPetMetadata", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// BusinessPetMetadataServiceServer is the server API for BusinessPetMetadataService service.
// All implementations must embed UnimplementedBusinessPetMetadataServiceServer
// for forward compatibility
type BusinessPetMetadataServiceServer interface {
	// Create pet metadata. Below are some examples of common metadata values:
	// Feeding/Medication schedule: 540 (09:00 AM) / 1080 (06:00 PM)
	// Feeding/Medication unit: Cup / Oz
	// Feeding type: Wet food / Dry food
	// Feeding Source: Owner provide / House provide
	// Feeding Instruction: Free feed / Feed individually
	// Display rules: {Feeding schedule} {Feeding type} {Feeding amount} {Feeding unit} {Feeding instruction}
	CreatePetMetadata(context.Context, *CreatePetMetadataParams) (*CreatePetMetadataResult, error)
	// Update pet metadata
	UpdatePetMetadata(context.Context, *UpdatePetMetadataParams) (*UpdatePetMetadataResult, error)
	// Delete pet metadata
	// The metadata deleted cannot be added again, but it will not affect the original display
	DeletePetMetadata(context.Context, *DeletePetMetadataParams) (*DeletePetMetadataResult, error)
	// List pet metadata
	ListPetMetadata(context.Context, *ListPetMetadataParams) (*ListPetMetadataResult, error)
	// Sort pet metadata
	SortPetMetadata(context.Context, *SortPetMetadataParams) (*SortPetMetadataResult, error)
	mustEmbedUnimplementedBusinessPetMetadataServiceServer()
}

// UnimplementedBusinessPetMetadataServiceServer must be embedded to have forward compatible implementations.
type UnimplementedBusinessPetMetadataServiceServer struct {
}

func (UnimplementedBusinessPetMetadataServiceServer) CreatePetMetadata(context.Context, *CreatePetMetadataParams) (*CreatePetMetadataResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreatePetMetadata not implemented")
}
func (UnimplementedBusinessPetMetadataServiceServer) UpdatePetMetadata(context.Context, *UpdatePetMetadataParams) (*UpdatePetMetadataResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdatePetMetadata not implemented")
}
func (UnimplementedBusinessPetMetadataServiceServer) DeletePetMetadata(context.Context, *DeletePetMetadataParams) (*DeletePetMetadataResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeletePetMetadata not implemented")
}
func (UnimplementedBusinessPetMetadataServiceServer) ListPetMetadata(context.Context, *ListPetMetadataParams) (*ListPetMetadataResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListPetMetadata not implemented")
}
func (UnimplementedBusinessPetMetadataServiceServer) SortPetMetadata(context.Context, *SortPetMetadataParams) (*SortPetMetadataResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SortPetMetadata not implemented")
}
func (UnimplementedBusinessPetMetadataServiceServer) mustEmbedUnimplementedBusinessPetMetadataServiceServer() {
}

// UnsafeBusinessPetMetadataServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to BusinessPetMetadataServiceServer will
// result in compilation errors.
type UnsafeBusinessPetMetadataServiceServer interface {
	mustEmbedUnimplementedBusinessPetMetadataServiceServer()
}

func RegisterBusinessPetMetadataServiceServer(s grpc.ServiceRegistrar, srv BusinessPetMetadataServiceServer) {
	s.RegisterService(&BusinessPetMetadataService_ServiceDesc, srv)
}

func _BusinessPetMetadataService_CreatePetMetadata_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreatePetMetadataParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessPetMetadataServiceServer).CreatePetMetadata(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.business_customer.v1.BusinessPetMetadataService/CreatePetMetadata",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessPetMetadataServiceServer).CreatePetMetadata(ctx, req.(*CreatePetMetadataParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessPetMetadataService_UpdatePetMetadata_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdatePetMetadataParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessPetMetadataServiceServer).UpdatePetMetadata(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.business_customer.v1.BusinessPetMetadataService/UpdatePetMetadata",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessPetMetadataServiceServer).UpdatePetMetadata(ctx, req.(*UpdatePetMetadataParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessPetMetadataService_DeletePetMetadata_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeletePetMetadataParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessPetMetadataServiceServer).DeletePetMetadata(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.business_customer.v1.BusinessPetMetadataService/DeletePetMetadata",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessPetMetadataServiceServer).DeletePetMetadata(ctx, req.(*DeletePetMetadataParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessPetMetadataService_ListPetMetadata_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListPetMetadataParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessPetMetadataServiceServer).ListPetMetadata(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.business_customer.v1.BusinessPetMetadataService/ListPetMetadata",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessPetMetadataServiceServer).ListPetMetadata(ctx, req.(*ListPetMetadataParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _BusinessPetMetadataService_SortPetMetadata_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SortPetMetadataParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BusinessPetMetadataServiceServer).SortPetMetadata(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.api.business_customer.v1.BusinessPetMetadataService/SortPetMetadata",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BusinessPetMetadataServiceServer).SortPetMetadata(ctx, req.(*SortPetMetadataParams))
	}
	return interceptor(ctx, in, info, handler)
}

// BusinessPetMetadataService_ServiceDesc is the grpc.ServiceDesc for BusinessPetMetadataService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var BusinessPetMetadataService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.api.business_customer.v1.BusinessPetMetadataService",
	HandlerType: (*BusinessPetMetadataServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreatePetMetadata",
			Handler:    _BusinessPetMetadataService_CreatePetMetadata_Handler,
		},
		{
			MethodName: "UpdatePetMetadata",
			Handler:    _BusinessPetMetadataService_UpdatePetMetadata_Handler,
		},
		{
			MethodName: "DeletePetMetadata",
			Handler:    _BusinessPetMetadataService_DeletePetMetadata_Handler,
		},
		{
			MethodName: "ListPetMetadata",
			Handler:    _BusinessPetMetadataService_ListPetMetadata_Handler,
		},
		{
			MethodName: "SortPetMetadata",
			Handler:    _BusinessPetMetadataService_SortPetMetadata_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/api/business_customer/v1/business_pet_metadata_api.proto",
}
