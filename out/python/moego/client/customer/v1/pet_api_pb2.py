# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: moego/client/customer/v1/pet_api.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'moego/client/customer/v1/pet_api.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from moego.models.customer.v1 import customer_pet_models_pb2 as moego_dot_models_dot_customer_dot_v1_dot_customer__pet__models__pb2
from moego.models.customer.v1 import customer_pet_vaccine_models_pb2 as moego_dot_models_dot_customer_dot_v1_dot_customer__pet__vaccine__models__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n&moego/client/customer/v1/pet_api.proto\x12\x18moego.client.customer.v1\x1a\x32moego/models/customer/v1/customer_pet_models.proto\x1a:moego/models/customer/v1/customer_pet_vaccine_models.proto\"\x13\n\x11GetPetListRequest\"R\n\x12GetPetListResponse\x12<\n\x04pets\x18\x01 \x03(\x0b\x32(.moego.client.customer.v1.GetPetResponseR\x04pets\"\x9a\x01\n\x0eGetPetResponse\x12<\n\x03pet\x18\x01 \x01(\x0b\x32*.moego.models.customer.v1.CustomerPetModelR\x03pet\x12J\n\x08vaccines\x18\x02 \x03(\x0b\x32..moego.models.customer.v1.PetVaccineSimpleViewR\x08vaccines2u\n\nPetService\x12g\n\nGetPetList\x12+.moego.client.customer.v1.GetPetListRequest\x1a,.moego.client.customer.v1.GetPetListResponseB\x81\x01\n com.moego.idl.client.customer.v1P\x01Z[github.com/MoeGolibrary/moego-api-definitions/out/go/moego/client/customer/v1;customerapipbb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'moego.client.customer.v1.pet_api_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n com.moego.idl.client.customer.v1P\001Z[github.com/MoeGolibrary/moego-api-definitions/out/go/moego/client/customer/v1;customerapipb'
  _globals['_GETPETLISTREQUEST']._serialized_start=180
  _globals['_GETPETLISTREQUEST']._serialized_end=199
  _globals['_GETPETLISTRESPONSE']._serialized_start=201
  _globals['_GETPETLISTRESPONSE']._serialized_end=283
  _globals['_GETPETRESPONSE']._serialized_start=286
  _globals['_GETPETRESPONSE']._serialized_end=440
  _globals['_PETSERVICE']._serialized_start=442
  _globals['_PETSERVICE']._serialized_end=559
# @@protoc_insertion_point(module_scope)
