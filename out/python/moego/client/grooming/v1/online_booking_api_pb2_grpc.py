# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

from moego.client.grooming.v1 import online_booking_api_pb2 as moego_dot_client_dot_grooming_dot_v1_dot_online__booking__api__pb2


class BookingServiceStub(object):
    """booking service, required c-side account session
    """

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.GetAvailablePetList = channel.unary_unary(
                '/moego.client.grooming.v1.BookingService/GetAvailablePetList',
                request_serializer=moego_dot_client_dot_grooming_dot_v1_dot_online__booking__api__pb2.GetAvailablePetListRequest.SerializeToString,
                response_deserializer=moego_dot_client_dot_grooming_dot_v1_dot_online__booking__api__pb2.GetAvailablePetListResponse.FromString,
                _registered_method=True)
        self.GetAvailableServiceList = channel.unary_unary(
                '/moego.client.grooming.v1.BookingService/GetAvailableServiceList',
                request_serializer=moego_dot_client_dot_grooming_dot_v1_dot_online__booking__api__pb2.GetAvailableServiceListRequest.SerializeToString,
                response_deserializer=moego_dot_client_dot_grooming_dot_v1_dot_online__booking__api__pb2.GetAvailableServiceListResponse.FromString,
                _registered_method=True)
        self.GetAvailableStaffList = channel.unary_unary(
                '/moego.client.grooming.v1.BookingService/GetAvailableStaffList',
                request_serializer=moego_dot_client_dot_grooming_dot_v1_dot_online__booking__api__pb2.GetAvailableStaffListRequest.SerializeToString,
                response_deserializer=moego_dot_client_dot_grooming_dot_v1_dot_online__booking__api__pb2.GetAvailableStaffListResponse.FromString,
                _registered_method=True)
        self.GetFirstAvailableDate = channel.unary_unary(
                '/moego.client.grooming.v1.BookingService/GetFirstAvailableDate',
                request_serializer=moego_dot_client_dot_grooming_dot_v1_dot_online__booking__api__pb2.GetFirstAvailableDateRequest.SerializeToString,
                response_deserializer=moego_dot_client_dot_grooming_dot_v1_dot_online__booking__api__pb2.GetFirstAvailableDateResponse.FromString,
                _registered_method=True)
        self.GetAvailableDateList = channel.unary_unary(
                '/moego.client.grooming.v1.BookingService/GetAvailableDateList',
                request_serializer=moego_dot_client_dot_grooming_dot_v1_dot_online__booking__api__pb2.GetAvailableDateListRequest.SerializeToString,
                response_deserializer=moego_dot_client_dot_grooming_dot_v1_dot_online__booking__api__pb2.GetAvailableDateListResponse.FromString,
                _registered_method=True)
        self.GetAvailableTimeslot = channel.unary_unary(
                '/moego.client.grooming.v1.BookingService/GetAvailableTimeslot',
                request_serializer=moego_dot_client_dot_grooming_dot_v1_dot_online__booking__api__pb2.GetAvailableTimeslotRequest.SerializeToString,
                response_deserializer=moego_dot_client_dot_grooming_dot_v1_dot_online__booking__api__pb2.GetAvailableTimeslotResponse.FromString,
                _registered_method=True)
        self.CanBookOnline = channel.unary_unary(
                '/moego.client.grooming.v1.BookingService/CanBookOnline',
                request_serializer=moego_dot_client_dot_grooming_dot_v1_dot_online__booking__api__pb2.CanBookOnlineRequest.SerializeToString,
                response_deserializer=moego_dot_client_dot_grooming_dot_v1_dot_online__booking__api__pb2.CanBookOnlineResponse.FromString,
                _registered_method=True)
        self.GetStaffWorkingHourRangeList = channel.unary_unary(
                '/moego.client.grooming.v1.BookingService/GetStaffWorkingHourRangeList',
                request_serializer=moego_dot_client_dot_grooming_dot_v1_dot_online__booking__api__pb2.GetStaffWorkingHourRangeListRequest.SerializeToString,
                response_deserializer=moego_dot_client_dot_grooming_dot_v1_dot_online__booking__api__pb2.GetStaffWorkingHourRangeListResponse.FromString,
                _registered_method=True)
        self.GetBookOnlineAddress = channel.unary_unary(
                '/moego.client.grooming.v1.BookingService/GetBookOnlineAddress',
                request_serializer=moego_dot_client_dot_grooming_dot_v1_dot_online__booking__api__pb2.GetBookOnlineAddressRequest.SerializeToString,
                response_deserializer=moego_dot_client_dot_grooming_dot_v1_dot_online__booking__api__pb2.GetBookOnlineAddressResponse.FromString,
                _registered_method=True)
        self.ListBookOnlineAddresses = channel.unary_unary(
                '/moego.client.grooming.v1.BookingService/ListBookOnlineAddresses',
                request_serializer=moego_dot_client_dot_grooming_dot_v1_dot_online__booking__api__pb2.ListBookOnlineAddressesParams.SerializeToString,
                response_deserializer=moego_dot_client_dot_grooming_dot_v1_dot_online__booking__api__pb2.ListBookOnlineAddressesResult.FromString,
                _registered_method=True)
        self.CheckBookOnlineAddress = channel.unary_unary(
                '/moego.client.grooming.v1.BookingService/CheckBookOnlineAddress',
                request_serializer=moego_dot_client_dot_grooming_dot_v1_dot_online__booking__api__pb2.CheckBookOnlineAddressParams.SerializeToString,
                response_deserializer=moego_dot_client_dot_grooming_dot_v1_dot_online__booking__api__pb2.CheckBookOnlineAddressResult.FromString,
                _registered_method=True)
        self.GetBookingStatus = channel.unary_unary(
                '/moego.client.grooming.v1.BookingService/GetBookingStatus',
                request_serializer=moego_dot_client_dot_grooming_dot_v1_dot_online__booking__api__pb2.GetBookingStatusParams.SerializeToString,
                response_deserializer=moego_dot_client_dot_grooming_dot_v1_dot_online__booking__api__pb2.GetBookingStatusResult.FromString,
                _registered_method=True)
        self.GetEstimatedPaymentInfo = channel.unary_unary(
                '/moego.client.grooming.v1.BookingService/GetEstimatedPaymentInfo',
                request_serializer=moego_dot_client_dot_grooming_dot_v1_dot_online__booking__api__pb2.GetEstimatedPaymentInfoParams.SerializeToString,
                response_deserializer=moego_dot_client_dot_grooming_dot_v1_dot_online__booking__api__pb2.GetEstimatedPaymentInfoResult.FromString,
                _registered_method=True)


class BookingServiceServicer(object):
    """booking service, required c-side account session
    """

    def GetAvailablePetList(self, request, context):
        """select pet list, filter deleted and passed away pet
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetAvailableServiceList(self, request, context):
        """select service list
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetAvailableStaffList(self, request, context):
        """select professional list
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetFirstAvailableDate(self, request, context):
        """get the first available date
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetAvailableDateList(self, request, context):
        """get available date list
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetAvailableTimeslot(self, request, context):
        """get the timeslot on a specified day
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def CanBookOnline(self, request, context):
        """can book online
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetStaffWorkingHourRangeList(self, request, context):
        """select staff working hour range list
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetBookOnlineAddress(self, request, context):
        """get book online address, primary address
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ListBookOnlineAddresses(self, request, context):
        """list book online addresses
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def CheckBookOnlineAddress(self, request, context):
        """check book online address
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetBookingStatus(self, request, context):
        """Get the status of online booking
        Branded apps always ask for an address on mobile grooming
        If client's address out of service area list: ignore the following two configurations
        Allow all clients to submit request without date and time.
        Allow existing clients to submit request with date and time.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetEstimatedPaymentInfo(self, request, context):
        """Get the estimated payment info
        No Payment:
        Subtotal, Tax and fees, Discount(optional), Total
        CoF:
        Subtotal, Tax and fees, Discount(optional), Total
        Pre-auth:
        Subtotal, Tax and fees, Tip(optional), Discount(optional), Total
        Prepayment(full amount):
        Subtotal, Tax and fees, Tip(optional), Discount(optional), Total
        Prepayment(deposit):
        Subtotal, Tax and fees, Discount(optional), Total, Deposit
        Subtotal = service total + service charge
        Tax and fees = tax + (convenience fee + booking fee)
        Tax = service tax + service charge tax
        Fees = convenience fee + booking fee
        Convenience fee(customizable) = (subtotal + tax) * 3.4% + 100cents
        Booking fee(prepayment only) = 1 to 3
        Tip(customizable) = front-end computing. fixed amount or subtotal percentage
        Discount(customizable) = fixed amount or subtotal percentage
        Total = subtotal + tax + fees - discount
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_BookingServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'GetAvailablePetList': grpc.unary_unary_rpc_method_handler(
                    servicer.GetAvailablePetList,
                    request_deserializer=moego_dot_client_dot_grooming_dot_v1_dot_online__booking__api__pb2.GetAvailablePetListRequest.FromString,
                    response_serializer=moego_dot_client_dot_grooming_dot_v1_dot_online__booking__api__pb2.GetAvailablePetListResponse.SerializeToString,
            ),
            'GetAvailableServiceList': grpc.unary_unary_rpc_method_handler(
                    servicer.GetAvailableServiceList,
                    request_deserializer=moego_dot_client_dot_grooming_dot_v1_dot_online__booking__api__pb2.GetAvailableServiceListRequest.FromString,
                    response_serializer=moego_dot_client_dot_grooming_dot_v1_dot_online__booking__api__pb2.GetAvailableServiceListResponse.SerializeToString,
            ),
            'GetAvailableStaffList': grpc.unary_unary_rpc_method_handler(
                    servicer.GetAvailableStaffList,
                    request_deserializer=moego_dot_client_dot_grooming_dot_v1_dot_online__booking__api__pb2.GetAvailableStaffListRequest.FromString,
                    response_serializer=moego_dot_client_dot_grooming_dot_v1_dot_online__booking__api__pb2.GetAvailableStaffListResponse.SerializeToString,
            ),
            'GetFirstAvailableDate': grpc.unary_unary_rpc_method_handler(
                    servicer.GetFirstAvailableDate,
                    request_deserializer=moego_dot_client_dot_grooming_dot_v1_dot_online__booking__api__pb2.GetFirstAvailableDateRequest.FromString,
                    response_serializer=moego_dot_client_dot_grooming_dot_v1_dot_online__booking__api__pb2.GetFirstAvailableDateResponse.SerializeToString,
            ),
            'GetAvailableDateList': grpc.unary_unary_rpc_method_handler(
                    servicer.GetAvailableDateList,
                    request_deserializer=moego_dot_client_dot_grooming_dot_v1_dot_online__booking__api__pb2.GetAvailableDateListRequest.FromString,
                    response_serializer=moego_dot_client_dot_grooming_dot_v1_dot_online__booking__api__pb2.GetAvailableDateListResponse.SerializeToString,
            ),
            'GetAvailableTimeslot': grpc.unary_unary_rpc_method_handler(
                    servicer.GetAvailableTimeslot,
                    request_deserializer=moego_dot_client_dot_grooming_dot_v1_dot_online__booking__api__pb2.GetAvailableTimeslotRequest.FromString,
                    response_serializer=moego_dot_client_dot_grooming_dot_v1_dot_online__booking__api__pb2.GetAvailableTimeslotResponse.SerializeToString,
            ),
            'CanBookOnline': grpc.unary_unary_rpc_method_handler(
                    servicer.CanBookOnline,
                    request_deserializer=moego_dot_client_dot_grooming_dot_v1_dot_online__booking__api__pb2.CanBookOnlineRequest.FromString,
                    response_serializer=moego_dot_client_dot_grooming_dot_v1_dot_online__booking__api__pb2.CanBookOnlineResponse.SerializeToString,
            ),
            'GetStaffWorkingHourRangeList': grpc.unary_unary_rpc_method_handler(
                    servicer.GetStaffWorkingHourRangeList,
                    request_deserializer=moego_dot_client_dot_grooming_dot_v1_dot_online__booking__api__pb2.GetStaffWorkingHourRangeListRequest.FromString,
                    response_serializer=moego_dot_client_dot_grooming_dot_v1_dot_online__booking__api__pb2.GetStaffWorkingHourRangeListResponse.SerializeToString,
            ),
            'GetBookOnlineAddress': grpc.unary_unary_rpc_method_handler(
                    servicer.GetBookOnlineAddress,
                    request_deserializer=moego_dot_client_dot_grooming_dot_v1_dot_online__booking__api__pb2.GetBookOnlineAddressRequest.FromString,
                    response_serializer=moego_dot_client_dot_grooming_dot_v1_dot_online__booking__api__pb2.GetBookOnlineAddressResponse.SerializeToString,
            ),
            'ListBookOnlineAddresses': grpc.unary_unary_rpc_method_handler(
                    servicer.ListBookOnlineAddresses,
                    request_deserializer=moego_dot_client_dot_grooming_dot_v1_dot_online__booking__api__pb2.ListBookOnlineAddressesParams.FromString,
                    response_serializer=moego_dot_client_dot_grooming_dot_v1_dot_online__booking__api__pb2.ListBookOnlineAddressesResult.SerializeToString,
            ),
            'CheckBookOnlineAddress': grpc.unary_unary_rpc_method_handler(
                    servicer.CheckBookOnlineAddress,
                    request_deserializer=moego_dot_client_dot_grooming_dot_v1_dot_online__booking__api__pb2.CheckBookOnlineAddressParams.FromString,
                    response_serializer=moego_dot_client_dot_grooming_dot_v1_dot_online__booking__api__pb2.CheckBookOnlineAddressResult.SerializeToString,
            ),
            'GetBookingStatus': grpc.unary_unary_rpc_method_handler(
                    servicer.GetBookingStatus,
                    request_deserializer=moego_dot_client_dot_grooming_dot_v1_dot_online__booking__api__pb2.GetBookingStatusParams.FromString,
                    response_serializer=moego_dot_client_dot_grooming_dot_v1_dot_online__booking__api__pb2.GetBookingStatusResult.SerializeToString,
            ),
            'GetEstimatedPaymentInfo': grpc.unary_unary_rpc_method_handler(
                    servicer.GetEstimatedPaymentInfo,
                    request_deserializer=moego_dot_client_dot_grooming_dot_v1_dot_online__booking__api__pb2.GetEstimatedPaymentInfoParams.FromString,
                    response_serializer=moego_dot_client_dot_grooming_dot_v1_dot_online__booking__api__pb2.GetEstimatedPaymentInfoResult.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'moego.client.grooming.v1.BookingService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('moego.client.grooming.v1.BookingService', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class BookingService(object):
    """booking service, required c-side account session
    """

    @staticmethod
    def GetAvailablePetList(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.client.grooming.v1.BookingService/GetAvailablePetList',
            moego_dot_client_dot_grooming_dot_v1_dot_online__booking__api__pb2.GetAvailablePetListRequest.SerializeToString,
            moego_dot_client_dot_grooming_dot_v1_dot_online__booking__api__pb2.GetAvailablePetListResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetAvailableServiceList(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.client.grooming.v1.BookingService/GetAvailableServiceList',
            moego_dot_client_dot_grooming_dot_v1_dot_online__booking__api__pb2.GetAvailableServiceListRequest.SerializeToString,
            moego_dot_client_dot_grooming_dot_v1_dot_online__booking__api__pb2.GetAvailableServiceListResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetAvailableStaffList(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.client.grooming.v1.BookingService/GetAvailableStaffList',
            moego_dot_client_dot_grooming_dot_v1_dot_online__booking__api__pb2.GetAvailableStaffListRequest.SerializeToString,
            moego_dot_client_dot_grooming_dot_v1_dot_online__booking__api__pb2.GetAvailableStaffListResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetFirstAvailableDate(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.client.grooming.v1.BookingService/GetFirstAvailableDate',
            moego_dot_client_dot_grooming_dot_v1_dot_online__booking__api__pb2.GetFirstAvailableDateRequest.SerializeToString,
            moego_dot_client_dot_grooming_dot_v1_dot_online__booking__api__pb2.GetFirstAvailableDateResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetAvailableDateList(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.client.grooming.v1.BookingService/GetAvailableDateList',
            moego_dot_client_dot_grooming_dot_v1_dot_online__booking__api__pb2.GetAvailableDateListRequest.SerializeToString,
            moego_dot_client_dot_grooming_dot_v1_dot_online__booking__api__pb2.GetAvailableDateListResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetAvailableTimeslot(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.client.grooming.v1.BookingService/GetAvailableTimeslot',
            moego_dot_client_dot_grooming_dot_v1_dot_online__booking__api__pb2.GetAvailableTimeslotRequest.SerializeToString,
            moego_dot_client_dot_grooming_dot_v1_dot_online__booking__api__pb2.GetAvailableTimeslotResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def CanBookOnline(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.client.grooming.v1.BookingService/CanBookOnline',
            moego_dot_client_dot_grooming_dot_v1_dot_online__booking__api__pb2.CanBookOnlineRequest.SerializeToString,
            moego_dot_client_dot_grooming_dot_v1_dot_online__booking__api__pb2.CanBookOnlineResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetStaffWorkingHourRangeList(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.client.grooming.v1.BookingService/GetStaffWorkingHourRangeList',
            moego_dot_client_dot_grooming_dot_v1_dot_online__booking__api__pb2.GetStaffWorkingHourRangeListRequest.SerializeToString,
            moego_dot_client_dot_grooming_dot_v1_dot_online__booking__api__pb2.GetStaffWorkingHourRangeListResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetBookOnlineAddress(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.client.grooming.v1.BookingService/GetBookOnlineAddress',
            moego_dot_client_dot_grooming_dot_v1_dot_online__booking__api__pb2.GetBookOnlineAddressRequest.SerializeToString,
            moego_dot_client_dot_grooming_dot_v1_dot_online__booking__api__pb2.GetBookOnlineAddressResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def ListBookOnlineAddresses(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.client.grooming.v1.BookingService/ListBookOnlineAddresses',
            moego_dot_client_dot_grooming_dot_v1_dot_online__booking__api__pb2.ListBookOnlineAddressesParams.SerializeToString,
            moego_dot_client_dot_grooming_dot_v1_dot_online__booking__api__pb2.ListBookOnlineAddressesResult.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def CheckBookOnlineAddress(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.client.grooming.v1.BookingService/CheckBookOnlineAddress',
            moego_dot_client_dot_grooming_dot_v1_dot_online__booking__api__pb2.CheckBookOnlineAddressParams.SerializeToString,
            moego_dot_client_dot_grooming_dot_v1_dot_online__booking__api__pb2.CheckBookOnlineAddressResult.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetBookingStatus(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.client.grooming.v1.BookingService/GetBookingStatus',
            moego_dot_client_dot_grooming_dot_v1_dot_online__booking__api__pb2.GetBookingStatusParams.SerializeToString,
            moego_dot_client_dot_grooming_dot_v1_dot_online__booking__api__pb2.GetBookingStatusResult.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetEstimatedPaymentInfo(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.client.grooming.v1.BookingService/GetEstimatedPaymentInfo',
            moego_dot_client_dot_grooming_dot_v1_dot_online__booking__api__pb2.GetEstimatedPaymentInfoParams.SerializeToString,
            moego_dot_client_dot_grooming_dot_v1_dot_online__booking__api__pb2.GetEstimatedPaymentInfoResult.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)
