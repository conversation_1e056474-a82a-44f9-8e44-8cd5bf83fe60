# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: moego/client/grooming/v1/review_booster_api.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'moego/client/grooming/v1/review_booster_api.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from moego.models.grooming.v1 import review_booster_record_models_pb2 as moego_dot_models_dot_grooming_dot_v1_dot_review__booster__record__models__pb2
from moego.models.review_booster.v1 import review_booster_config_models_pb2 as moego_dot_models_dot_review__booster_dot_v1_dot_review__booster__config__models__pb2
from validate import validate_pb2 as validate_dot_validate__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n1moego/client/grooming/v1/review_booster_api.proto\x12\x18moego.client.grooming.v1\x1a;moego/models/grooming/v1/review_booster_record_models.proto\x1a\x41moego/models/review_booster/v1/review_booster_config_models.proto\x1a\x17validate/validate.proto\"\xb6\x01\n\x1a\x43reateReviewBoosterRequest\x12.\n\x0e\x61ppointment_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\rappointmentId\x12\x1f\n\x05score\x18\x02 \x01(\x05\x42\t\xfa\x42\x06\x1a\x04\x18\x05(\x01R\x05score\x12\x34\n\x0ereview_content\x18\x03 \x01(\tB\x08\xfa\x42\x05r\x03\x18\xe8\x07H\x00R\rreviewContent\x88\x01\x01\x42\x11\n\x0f_review_content\"\x1d\n\x1b\x43reateReviewBoosterResponse\"M\n\x1bGetReviewBoosterListRequest\x12.\n\x0e\x61ppointment_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\rappointmentId\"v\n\x1cGetReviewBoosterListResponse\x12V\n\x07records\x18\x01 \x03(\x0b\x32<.moego.models.grooming.v1.ReviewBoosterRecordModelClientViewR\x07records\"H\n\x1cGetReviewBoosterConfigParams\x12(\n\x0b\x62usiness_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\nbusinessId\"u\n\x1cGetReviewBoosterConfigResult\x12U\n\x06\x63onfig\x18\x01 \x01(\x0b\x32=.moego.models.review_booster.v1.ReviewBoosterConfigClientViewR\x06\x63onfig2\xae\x03\n\x14ReviewBoosterService\x12\x82\x01\n\x13\x43reateReviewBooster\x12\x34.moego.client.grooming.v1.CreateReviewBoosterRequest\x1a\x35.moego.client.grooming.v1.CreateReviewBoosterResponse\x12\x85\x01\n\x14GetReviewBoosterList\x12\x35.moego.client.grooming.v1.GetReviewBoosterListRequest\x1a\x36.moego.client.grooming.v1.GetReviewBoosterListResponse\x12\x88\x01\n\x16GetReviewBoosterConfig\x12\x36.moego.client.grooming.v1.GetReviewBoosterConfigParams\x1a\x36.moego.client.grooming.v1.GetReviewBoosterConfigResultB\x81\x01\n com.moego.idl.client.grooming.v1P\x01Z[github.com/MoeGolibrary/moego-api-definitions/out/go/moego/client/grooming/v1;groomingapipbb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'moego.client.grooming.v1.review_booster_api_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n com.moego.idl.client.grooming.v1P\001Z[github.com/MoeGolibrary/moego-api-definitions/out/go/moego/client/grooming/v1;groomingapipb'
  _globals['_CREATEREVIEWBOOSTERREQUEST'].fields_by_name['appointment_id']._loaded_options = None
  _globals['_CREATEREVIEWBOOSTERREQUEST'].fields_by_name['appointment_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_CREATEREVIEWBOOSTERREQUEST'].fields_by_name['score']._loaded_options = None
  _globals['_CREATEREVIEWBOOSTERREQUEST'].fields_by_name['score']._serialized_options = b'\372B\006\032\004\030\005(\001'
  _globals['_CREATEREVIEWBOOSTERREQUEST'].fields_by_name['review_content']._loaded_options = None
  _globals['_CREATEREVIEWBOOSTERREQUEST'].fields_by_name['review_content']._serialized_options = b'\372B\005r\003\030\350\007'
  _globals['_GETREVIEWBOOSTERLISTREQUEST'].fields_by_name['appointment_id']._loaded_options = None
  _globals['_GETREVIEWBOOSTERLISTREQUEST'].fields_by_name['appointment_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_GETREVIEWBOOSTERCONFIGPARAMS'].fields_by_name['business_id']._loaded_options = None
  _globals['_GETREVIEWBOOSTERCONFIGPARAMS'].fields_by_name['business_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_CREATEREVIEWBOOSTERREQUEST']._serialized_start=233
  _globals['_CREATEREVIEWBOOSTERREQUEST']._serialized_end=415
  _globals['_CREATEREVIEWBOOSTERRESPONSE']._serialized_start=417
  _globals['_CREATEREVIEWBOOSTERRESPONSE']._serialized_end=446
  _globals['_GETREVIEWBOOSTERLISTREQUEST']._serialized_start=448
  _globals['_GETREVIEWBOOSTERLISTREQUEST']._serialized_end=525
  _globals['_GETREVIEWBOOSTERLISTRESPONSE']._serialized_start=527
  _globals['_GETREVIEWBOOSTERLISTRESPONSE']._serialized_end=645
  _globals['_GETREVIEWBOOSTERCONFIGPARAMS']._serialized_start=647
  _globals['_GETREVIEWBOOSTERCONFIGPARAMS']._serialized_end=719
  _globals['_GETREVIEWBOOSTERCONFIGRESULT']._serialized_start=721
  _globals['_GETREVIEWBOOSTERCONFIGRESULT']._serialized_end=838
  _globals['_REVIEWBOOSTERSERVICE']._serialized_start=841
  _globals['_REVIEWBOOSTERSERVICE']._serialized_end=1271
# @@protoc_insertion_point(module_scope)
