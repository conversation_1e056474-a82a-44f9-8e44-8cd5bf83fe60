from moego.models.online_booking.v1 import business_ob_config_models_pb2 as _business_ob_config_models_pb2
from validate import validate_pb2 as _validate_pb2
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from collections.abc import Mapping as _Mapping
from typing import ClassVar as _ClassVar, Optional as _Optional, Union as _Union

DESCRIPTOR: _descriptor.FileDescriptor

class GetOnlineBookingConfigRequest(_message.Message):
    __slots__ = ("business_id",)
    BUSINESS_ID_FIELD_NUMBER: _ClassVar[int]
    business_id: int
    def __init__(self, business_id: _Optional[int] = ...) -> None: ...

class GetOnlineBookingConfigResponse(_message.Message):
    __slots__ = ("config",)
    CONFIG_FIELD_NUMBER: _ClassVar[int]
    config: _business_ob_config_models_pb2.BusinessOBConfigModelBookingView
    def __init__(self, config: _Optional[_Union[_business_ob_config_models_pb2.BusinessOBConfigModelBookingView, _Mapping]] = ...) -> None: ...
