# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

from moego.client.online_booking.v1 import ob_offering_api_pb2 as moego_dot_client_dot_online__booking_dot_v1_dot_ob__offering__api__pb2


class OBOfferingServiceStub(object):
    """the offering service
    """

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.GetApplicableOfferings = channel.unary_unary(
                '/moego.client.online_booking.v1.OBOfferingService/GetApplicableOfferings',
                request_serializer=moego_dot_client_dot_online__booking_dot_v1_dot_ob__offering__api__pb2.GetApplicableOfferingsRequest.SerializeToString,
                response_deserializer=moego_dot_client_dot_online__booking_dot_v1_dot_ob__offering__api__pb2.GetApplicableOfferingsResponse.FromString,
                _registered_method=True)
        self.ListPricingRule = channel.unary_unary(
                '/moego.client.online_booking.v1.OBOfferingService/ListPricingRule',
                request_serializer=moego_dot_client_dot_online__booking_dot_v1_dot_ob__offering__api__pb2.ListPricingRuleParams.SerializeToString,
                response_deserializer=moego_dot_client_dot_online__booking_dot_v1_dot_ob__offering__api__pb2.ListPricingRuleResult.FromString,
                _registered_method=True)


class OBOfferingServiceServicer(object):
    """the offering service
    """

    def GetApplicableOfferings(self, request, context):
        """get applicable offerings
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ListPricingRule(self, request, context):
        """list pricing rule
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_OBOfferingServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'GetApplicableOfferings': grpc.unary_unary_rpc_method_handler(
                    servicer.GetApplicableOfferings,
                    request_deserializer=moego_dot_client_dot_online__booking_dot_v1_dot_ob__offering__api__pb2.GetApplicableOfferingsRequest.FromString,
                    response_serializer=moego_dot_client_dot_online__booking_dot_v1_dot_ob__offering__api__pb2.GetApplicableOfferingsResponse.SerializeToString,
            ),
            'ListPricingRule': grpc.unary_unary_rpc_method_handler(
                    servicer.ListPricingRule,
                    request_deserializer=moego_dot_client_dot_online__booking_dot_v1_dot_ob__offering__api__pb2.ListPricingRuleParams.FromString,
                    response_serializer=moego_dot_client_dot_online__booking_dot_v1_dot_ob__offering__api__pb2.ListPricingRuleResult.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'moego.client.online_booking.v1.OBOfferingService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('moego.client.online_booking.v1.OBOfferingService', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class OBOfferingService(object):
    """the offering service
    """

    @staticmethod
    def GetApplicableOfferings(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.client.online_booking.v1.OBOfferingService/GetApplicableOfferings',
            moego_dot_client_dot_online__booking_dot_v1_dot_ob__offering__api__pb2.GetApplicableOfferingsRequest.SerializeToString,
            moego_dot_client_dot_online__booking_dot_v1_dot_ob__offering__api__pb2.GetApplicableOfferingsResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def ListPricingRule(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.client.online_booking.v1.OBOfferingService/ListPricingRule',
            moego_dot_client_dot_online__booking_dot_v1_dot_ob__offering__api__pb2.ListPricingRuleParams.SerializeToString,
            moego_dot_client_dot_online__booking_dot_v1_dot_ob__offering__api__pb2.ListPricingRuleResult.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)
