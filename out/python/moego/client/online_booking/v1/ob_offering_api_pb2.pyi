from moego.models.offering.v1 import pricing_rule_defs_pb2 as _pricing_rule_defs_pb2
from moego.models.offering.v1 import pricing_rule_models_pb2 as _pricing_rule_models_pb2
from moego.models.offering.v1 import service_enum_pb2 as _service_enum_pb2
from moego.models.offering.v2 import pricing_rule_models_pb2 as _pricing_rule_models_pb2_1
from moego.models.online_booking.v1 import ob_offering_defs_pb2 as _ob_offering_defs_pb2
from moego.models.online_booking.v1 import ob_offering_models_pb2 as _ob_offering_models_pb2
from validate import validate_pb2 as _validate_pb2
from google.protobuf.internal import containers as _containers
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from collections.abc import Iterable as _Iterable, Mapping as _Mapping
from typing import ClassVar as _ClassVar, Optional as _Optional, Union as _Union

DESCRIPTOR: _descriptor.FileDescriptor

class GetApplicableOfferingsRequest(_message.Message):
    __slots__ = ("name", "domain", "service_item_type")
    NAME_FIELD_NUMBER: _ClassVar[int]
    DOMAIN_FIELD_NUMBER: _ClassVar[int]
    SERVICE_ITEM_TYPE_FIELD_NUMBER: _ClassVar[int]
    name: str
    domain: str
    service_item_type: _service_enum_pb2.ServiceItemType
    def __init__(self, name: _Optional[str] = ..., domain: _Optional[str] = ..., service_item_type: _Optional[_Union[_service_enum_pb2.ServiceItemType, str]] = ...) -> None: ...

class GetApplicableOfferingsResponse(_message.Message):
    __slots__ = ("service_categories", "addon_categories")
    SERVICE_CATEGORIES_FIELD_NUMBER: _ClassVar[int]
    ADDON_CATEGORIES_FIELD_NUMBER: _ClassVar[int]
    service_categories: _containers.RepeatedCompositeFieldContainer[_ob_offering_models_pb2.OBOfferingCategoryView]
    addon_categories: _containers.RepeatedCompositeFieldContainer[_ob_offering_models_pb2.OBOfferingCategoryView]
    def __init__(self, service_categories: _Optional[_Iterable[_Union[_ob_offering_models_pb2.OBOfferingCategoryView, _Mapping]]] = ..., addon_categories: _Optional[_Iterable[_Union[_ob_offering_models_pb2.OBOfferingCategoryView, _Mapping]]] = ...) -> None: ...

class ListPricingRuleParams(_message.Message):
    __slots__ = ("name", "domain", "filter", "date_range", "date_list")
    NAME_FIELD_NUMBER: _ClassVar[int]
    DOMAIN_FIELD_NUMBER: _ClassVar[int]
    FILTER_FIELD_NUMBER: _ClassVar[int]
    DATE_RANGE_FIELD_NUMBER: _ClassVar[int]
    DATE_LIST_FIELD_NUMBER: _ClassVar[int]
    name: str
    domain: str
    filter: _pricing_rule_defs_pb2.ListPricingRuleFilter
    date_range: _ob_offering_defs_pb2.ServiceDateRangeDef
    date_list: _ob_offering_defs_pb2.ServiceDateListDef
    def __init__(self, name: _Optional[str] = ..., domain: _Optional[str] = ..., filter: _Optional[_Union[_pricing_rule_defs_pb2.ListPricingRuleFilter, _Mapping]] = ..., date_range: _Optional[_Union[_ob_offering_defs_pb2.ServiceDateRangeDef, _Mapping]] = ..., date_list: _Optional[_Union[_ob_offering_defs_pb2.ServiceDateListDef, _Mapping]] = ...) -> None: ...

class ListPricingRuleResult(_message.Message):
    __slots__ = ("pricing_rules", "pricing_rules_v2")
    PRICING_RULES_FIELD_NUMBER: _ClassVar[int]
    PRICING_RULES_V2_FIELD_NUMBER: _ClassVar[int]
    pricing_rules: _containers.RepeatedCompositeFieldContainer[_pricing_rule_models_pb2.PricingRuleModel]
    pricing_rules_v2: _containers.RepeatedCompositeFieldContainer[_pricing_rule_models_pb2_1.PricingRule]
    def __init__(self, pricing_rules: _Optional[_Iterable[_Union[_pricing_rule_models_pb2.PricingRuleModel, _Mapping]]] = ..., pricing_rules_v2: _Optional[_Iterable[_Union[_pricing_rule_models_pb2_1.PricingRule, _Mapping]]] = ...) -> None: ...
