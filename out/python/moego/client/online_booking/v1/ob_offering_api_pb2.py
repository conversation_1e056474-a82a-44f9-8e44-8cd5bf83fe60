# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: moego/client/online_booking/v1/ob_offering_api.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'moego/client/online_booking/v1/ob_offering_api.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from moego.models.offering.v1 import pricing_rule_defs_pb2 as moego_dot_models_dot_offering_dot_v1_dot_pricing__rule__defs__pb2
from moego.models.offering.v1 import pricing_rule_models_pb2 as moego_dot_models_dot_offering_dot_v1_dot_pricing__rule__models__pb2
from moego.models.offering.v1 import service_enum_pb2 as moego_dot_models_dot_offering_dot_v1_dot_service__enum__pb2
from moego.models.offering.v2 import pricing_rule_models_pb2 as moego_dot_models_dot_offering_dot_v2_dot_pricing__rule__models__pb2
from moego.models.online_booking.v1 import ob_offering_defs_pb2 as moego_dot_models_dot_online__booking_dot_v1_dot_ob__offering__defs__pb2
from moego.models.online_booking.v1 import ob_offering_models_pb2 as moego_dot_models_dot_online__booking_dot_v1_dot_ob__offering__models__pb2
from validate import validate_pb2 as validate_dot_validate__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n4moego/client/online_booking/v1/ob_offering_api.proto\x12\x1emoego.client.online_booking.v1\x1a\x30moego/models/offering/v1/pricing_rule_defs.proto\x1a\x32moego/models/offering/v1/pricing_rule_models.proto\x1a+moego/models/offering/v1/service_enum.proto\x1a\x32moego/models/offering/v2/pricing_rule_models.proto\x1a\x35moego/models/online_booking/v1/ob_offering_defs.proto\x1a\x37moego/models/online_booking/v1/ob_offering_models.proto\x1a\x17validate/validate.proto\"\xb8\x01\n\x1dGetApplicableOfferingsRequest\x12\x14\n\x04name\x18\x01 \x01(\tH\x00R\x04name\x12\x18\n\x06\x64omain\x18\x02 \x01(\tH\x00R\x06\x64omain\x12U\n\x11service_item_type\x18\x03 \x01(\x0e\x32).moego.models.offering.v1.ServiceItemTypeR\x0fserviceItemTypeB\x10\n\tanonymous\x12\x03\xf8\x42\x01\"\xea\x01\n\x1eGetApplicableOfferingsResponse\x12\x65\n\x12service_categories\x18\x01 \x03(\x0b\x32\x36.moego.models.online_booking.v1.OBOfferingCategoryViewR\x11serviceCategories\x12\x61\n\x10\x61\x64\x64on_categories\x18\x02 \x03(\x0b\x32\x36.moego.models.online_booking.v1.OBOfferingCategoryViewR\x0f\x61\x64\x64onCategories\"\xf2\x02\n\x15ListPricingRuleParams\x12\x14\n\x04name\x18\x01 \x01(\tH\x00R\x04name\x12\x18\n\x06\x64omain\x18\x02 \x01(\tH\x00R\x06\x64omain\x12L\n\x06\x66ilter\x18\x03 \x01(\x0b\x32/.moego.models.offering.v1.ListPricingRuleFilterH\x02R\x06\x66ilter\x88\x01\x01\x12T\n\ndate_range\x18\x0b \x01(\x0b\x32\x33.moego.models.online_booking.v1.ServiceDateRangeDefH\x01R\tdateRange\x12Q\n\tdate_list\x18\x0c \x01(\x0b\x32\x32.moego.models.online_booking.v1.ServiceDateListDefH\x01R\x08\x64\x61teListB\x10\n\tanonymous\x12\x03\xf8\x42\x01\x42\x15\n\x13\x64\x61te_selection_typeB\t\n\x07_filter\"\xbd\x01\n\x15ListPricingRuleResult\x12S\n\rpricing_rules\x18\x01 \x03(\x0b\x32*.moego.models.offering.v1.PricingRuleModelB\x02\x18\x01R\x0cpricingRules\x12O\n\x10pricing_rules_v2\x18\x02 \x03(\x0b\x32%.moego.models.offering.v2.PricingRuleR\x0epricingRulesV22\xae\x02\n\x11OBOfferingService\x12\x97\x01\n\x16GetApplicableOfferings\x12=.moego.client.online_booking.v1.GetApplicableOfferingsRequest\x1a>.moego.client.online_booking.v1.GetApplicableOfferingsResponse\x12\x7f\n\x0fListPricingRule\x12\x35.moego.client.online_booking.v1.ListPricingRuleParams\x1a\x35.moego.client.online_booking.v1.ListPricingRuleResultB\x92\x01\n&com.moego.idl.client.online_booking.v1P\x01Zfgithub.com/MoeGolibrary/moego-api-definitions/out/go/moego/client/online_booking/v1;onlinebookingapipbb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'moego.client.online_booking.v1.ob_offering_api_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n&com.moego.idl.client.online_booking.v1P\001Zfgithub.com/MoeGolibrary/moego-api-definitions/out/go/moego/client/online_booking/v1;onlinebookingapipb'
  _globals['_GETAPPLICABLEOFFERINGSREQUEST'].oneofs_by_name['anonymous']._loaded_options = None
  _globals['_GETAPPLICABLEOFFERINGSREQUEST'].oneofs_by_name['anonymous']._serialized_options = b'\370B\001'
  _globals['_LISTPRICINGRULEPARAMS'].oneofs_by_name['anonymous']._loaded_options = None
  _globals['_LISTPRICINGRULEPARAMS'].oneofs_by_name['anonymous']._serialized_options = b'\370B\001'
  _globals['_LISTPRICINGRULERESULT'].fields_by_name['pricing_rules']._loaded_options = None
  _globals['_LISTPRICINGRULERESULT'].fields_by_name['pricing_rules']._serialized_options = b'\030\001'
  _globals['_GETAPPLICABLEOFFERINGSREQUEST']._serialized_start=425
  _globals['_GETAPPLICABLEOFFERINGSREQUEST']._serialized_end=609
  _globals['_GETAPPLICABLEOFFERINGSRESPONSE']._serialized_start=612
  _globals['_GETAPPLICABLEOFFERINGSRESPONSE']._serialized_end=846
  _globals['_LISTPRICINGRULEPARAMS']._serialized_start=849
  _globals['_LISTPRICINGRULEPARAMS']._serialized_end=1219
  _globals['_LISTPRICINGRULERESULT']._serialized_start=1222
  _globals['_LISTPRICINGRULERESULT']._serialized_end=1411
  _globals['_OBOFFERINGSERVICE']._serialized_start=1414
  _globals['_OBOFFERINGSERVICE']._serialized_end=1716
# @@protoc_insertion_point(module_scope)
