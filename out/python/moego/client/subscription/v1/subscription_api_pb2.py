# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: moego/client/subscription/v1/subscription_api.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'moego/client/subscription/v1/subscription_api.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from validate import validate_pb2 as validate_dot_validate__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n3moego/client/subscription/v1/subscription_api.proto\x12\x1cmoego.client.subscription.v1\x1a\x17validate/validate.proto\"9\n\x0fGetCreditParams\x12&\n\ncompany_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\tcompanyId\")\n\x0fGetCreditResult\x12\x16\n\x06\x63redit\x18\x01 \x01(\x03R\x06\x63redit2\x80\x01\n\x13SubscriptionService\x12i\n\tGetCredit\x12-.moego.client.subscription.v1.GetCreditParams\x1a-.moego.client.subscription.v1.GetCreditResultB\x8d\x01\n$com.moego.idl.client.subscription.v1P\x01Zcgithub.com/MoeGolibrary/moego-api-definitions/out/go/moego/client/subscription/v1;subscriptionapipbb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'moego.client.subscription.v1.subscription_api_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n$com.moego.idl.client.subscription.v1P\001Zcgithub.com/MoeGolibrary/moego-api-definitions/out/go/moego/client/subscription/v1;subscriptionapipb'
  _globals['_GETCREDITPARAMS'].fields_by_name['company_id']._loaded_options = None
  _globals['_GETCREDITPARAMS'].fields_by_name['company_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_GETCREDITPARAMS']._serialized_start=110
  _globals['_GETCREDITPARAMS']._serialized_end=167
  _globals['_GETCREDITRESULT']._serialized_start=169
  _globals['_GETCREDITRESULT']._serialized_end=210
  _globals['_SUBSCRIPTIONSERVICE']._serialized_start=213
  _globals['_SUBSCRIPTIONSERVICE']._serialized_end=341
# @@protoc_insertion_point(module_scope)
