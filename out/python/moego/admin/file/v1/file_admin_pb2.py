# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: moego/admin/file/v1/file_admin.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'moego/admin/file/v1/file_admin.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from moego.models.file.v2 import file_defs_pb2 as moego_dot_models_dot_file_dot_v2_dot_file__defs__pb2
from moego.models.file.v2 import file_models_pb2 as moego_dot_models_dot_file_dot_v2_dot_file__models__pb2
from moego.utils.v2 import condition_messages_pb2 as moego_dot_utils_dot_v2_dot_condition__messages__pb2
from moego.utils.v2 import pagination_messages_pb2 as moego_dot_utils_dot_v2_dot_pagination__messages__pb2
from validate import validate_pb2 as validate_dot_validate__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n$moego/admin/file/v1/file_admin.proto\x12\x13moego.admin.file.v1\x1a$moego/models/file/v2/file_defs.proto\x1a&moego/models/file/v2/file_models.proto\x1a\'moego/utils/v2/condition_messages.proto\x1a(moego/utils/v2/pagination_messages.proto\x1a\x17validate/validate.proto\"\xfc\x04\n\x13\x44\x65scribeFilesParams\x12+\n\ncreator_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02(\x00H\x00R\tcreatorId\x88\x01\x01\x12-\n\x0b\x62usiness_id\x18\x02 \x01(\x03\x42\x07\xfa\x42\x04\"\x02(\x00H\x01R\nbusinessId\x88\x01\x01\x12\x36\n\x0fowner_type_like\x18\x03 \x01(\tB\t\xfa\x42\x06r\x04\x10\x01\x18\x32H\x02R\rownerTypeLike\x88\x01\x01\x12-\n\nusage_like\x18\x04 \x01(\tB\t\xfa\x42\x06r\x04\x10\x01\x18\x32H\x03R\tusageLike\x88\x01\x01\x12\x1c\n\x02id\x18\x05 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00H\x04R\x02id\x88\x01\x01\x12,\n\x0finclude_deleted\x18\x06 \x01(\x08H\x05R\x0eincludeDeleted\x88\x01\x01\x12\'\n\x08owner_id\x18\x07 \x01(\x03\x42\x07\xfa\x42\x04\"\x02(\x00H\x06R\x07ownerId\x88\x01\x01\x12+\n\ncompany_id\x18\x08 \x01(\x03\x42\x07\xfa\x42\x04\"\x02(\x00H\x07R\tcompanyId\x88\x01\x01\x12\x37\n\x08order_by\x18\x0e \x01(\x0b\x32\x17.moego.utils.v2.OrderByH\x08R\x07orderBy\x88\x01\x01\x12\x41\n\npagination\x18\x0f \x01(\x0b\x32!.moego.utils.v2.PaginationRequestR\npaginationB\r\n\x0b_creator_idB\x0e\n\x0c_business_idB\x12\n\x10_owner_type_likeB\r\n\x0b_usage_likeB\x05\n\x03_idB\x12\n\x10_include_deletedB\x0b\n\t_owner_idB\r\n\x0b_company_idB\x0b\n\t_order_by\"\x90\x01\n\x13\x44\x65scribeFilesResult\x12\x35\n\x05\x66iles\x18\x01 \x03(\x0b\x32\x1f.moego.models.file.v2.FileModelR\x05\x66iles\x12\x42\n\npagination\x18\x02 \x01(\x0b\x32\".moego.utils.v2.PaginationResponseR\npagination\"\x83\x03\n\x10UploadFileParams\x12\x45\n\x08platform\x18\x01 \x01(\x0b\x32\'.moego.models.file.v2.PlatformSourceDefH\x00R\x08platform\x12?\n\x06tenant\x18\x02 \x01(\x0b\x32%.moego.models.file.v2.TenantSourceDefH\x00R\x06tenant\x12\x1f\n\x05usage\x18\x03 \x01(\tB\t\xfa\x42\x06r\x04\x10\x01\x18\x32R\x05usage\x12(\n\nowner_type\x18\x04 \x01(\tB\t\xfa\x42\x06r\x04\x10\x01\x18\x32R\townerType\x12\'\n\x08owner_id\x18\x05 \x01(\x03\x42\x07\xfa\x42\x04\"\x02(\x00H\x01R\x07ownerId\x88\x01\x01\x12&\n\tfile_name\x18\x06 \x01(\tB\t\xfa\x42\x06r\x04\x10\x01\x18\x64R\x08\x66ileName\x12/\n\x0c\x66ile_content\x18\x07 \x01(\x0c\x42\x0c\xfa\x42\tz\x07\x10\x01\x18\x80\x80\x80\x32R\x0b\x66ileContentB\r\n\x06source\x12\x03\xf8\x42\x01\x42\x0b\n\t_owner_id\"A\n\x10UploadFileResult\x12\x0e\n\x02id\x18\x01 \x01(\x03R\x02id\x12\x1d\n\naccess_url\x18\x02 \x01(\tR\taccessUrl\"V\n\x18PreSignDownloadUrlParams\x12\x1f\n\x06\x62ucket\x18\x01 \x01(\tB\x07\xfa\x42\x04r\x02\x10\x01R\x06\x62ucket\x12\x19\n\x03key\x18\x02 \x01(\tB\x07\xfa\x42\x04r\x02\x10\x01R\x03key\",\n\x18PreSignDownloadUrlResult\x12\x10\n\x03url\x18\x01 \x01(\tR\x03url2\xc2\x02\n\x0b\x46ileService\x12\x63\n\rDescribeFiles\x12(.moego.admin.file.v1.DescribeFilesParams\x1a(.moego.admin.file.v1.DescribeFilesResult\x12Z\n\nUploadFile\x12%.moego.admin.file.v1.UploadFileParams\x1a%.moego.admin.file.v1.UploadFileResult\x12r\n\x12PreSignDownloadUrl\x12-.moego.admin.file.v1.PreSignDownloadUrlParams\x1a-.moego.admin.file.v1.PreSignDownloadUrlResultBs\n\x1b\x63om.moego.idl.admin.file.v1P\x01ZRgithub.com/MoeGolibrary/moego-api-definitions/out/go/moego/admin/file/v1;fileapipbb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'moego.admin.file.v1.file_admin_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n\033com.moego.idl.admin.file.v1P\001ZRgithub.com/MoeGolibrary/moego-api-definitions/out/go/moego/admin/file/v1;fileapipb'
  _globals['_DESCRIBEFILESPARAMS'].fields_by_name['creator_id']._loaded_options = None
  _globals['_DESCRIBEFILESPARAMS'].fields_by_name['creator_id']._serialized_options = b'\372B\004\"\002(\000'
  _globals['_DESCRIBEFILESPARAMS'].fields_by_name['business_id']._loaded_options = None
  _globals['_DESCRIBEFILESPARAMS'].fields_by_name['business_id']._serialized_options = b'\372B\004\"\002(\000'
  _globals['_DESCRIBEFILESPARAMS'].fields_by_name['owner_type_like']._loaded_options = None
  _globals['_DESCRIBEFILESPARAMS'].fields_by_name['owner_type_like']._serialized_options = b'\372B\006r\004\020\001\0302'
  _globals['_DESCRIBEFILESPARAMS'].fields_by_name['usage_like']._loaded_options = None
  _globals['_DESCRIBEFILESPARAMS'].fields_by_name['usage_like']._serialized_options = b'\372B\006r\004\020\001\0302'
  _globals['_DESCRIBEFILESPARAMS'].fields_by_name['id']._loaded_options = None
  _globals['_DESCRIBEFILESPARAMS'].fields_by_name['id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_DESCRIBEFILESPARAMS'].fields_by_name['owner_id']._loaded_options = None
  _globals['_DESCRIBEFILESPARAMS'].fields_by_name['owner_id']._serialized_options = b'\372B\004\"\002(\000'
  _globals['_DESCRIBEFILESPARAMS'].fields_by_name['company_id']._loaded_options = None
  _globals['_DESCRIBEFILESPARAMS'].fields_by_name['company_id']._serialized_options = b'\372B\004\"\002(\000'
  _globals['_UPLOADFILEPARAMS'].oneofs_by_name['source']._loaded_options = None
  _globals['_UPLOADFILEPARAMS'].oneofs_by_name['source']._serialized_options = b'\370B\001'
  _globals['_UPLOADFILEPARAMS'].fields_by_name['usage']._loaded_options = None
  _globals['_UPLOADFILEPARAMS'].fields_by_name['usage']._serialized_options = b'\372B\006r\004\020\001\0302'
  _globals['_UPLOADFILEPARAMS'].fields_by_name['owner_type']._loaded_options = None
  _globals['_UPLOADFILEPARAMS'].fields_by_name['owner_type']._serialized_options = b'\372B\006r\004\020\001\0302'
  _globals['_UPLOADFILEPARAMS'].fields_by_name['owner_id']._loaded_options = None
  _globals['_UPLOADFILEPARAMS'].fields_by_name['owner_id']._serialized_options = b'\372B\004\"\002(\000'
  _globals['_UPLOADFILEPARAMS'].fields_by_name['file_name']._loaded_options = None
  _globals['_UPLOADFILEPARAMS'].fields_by_name['file_name']._serialized_options = b'\372B\006r\004\020\001\030d'
  _globals['_UPLOADFILEPARAMS'].fields_by_name['file_content']._loaded_options = None
  _globals['_UPLOADFILEPARAMS'].fields_by_name['file_content']._serialized_options = b'\372B\tz\007\020\001\030\200\200\2002'
  _globals['_PRESIGNDOWNLOADURLPARAMS'].fields_by_name['bucket']._loaded_options = None
  _globals['_PRESIGNDOWNLOADURLPARAMS'].fields_by_name['bucket']._serialized_options = b'\372B\004r\002\020\001'
  _globals['_PRESIGNDOWNLOADURLPARAMS'].fields_by_name['key']._loaded_options = None
  _globals['_PRESIGNDOWNLOADURLPARAMS'].fields_by_name['key']._serialized_options = b'\372B\004r\002\020\001'
  _globals['_DESCRIBEFILESPARAMS']._serialized_start=248
  _globals['_DESCRIBEFILESPARAMS']._serialized_end=884
  _globals['_DESCRIBEFILESRESULT']._serialized_start=887
  _globals['_DESCRIBEFILESRESULT']._serialized_end=1031
  _globals['_UPLOADFILEPARAMS']._serialized_start=1034
  _globals['_UPLOADFILEPARAMS']._serialized_end=1421
  _globals['_UPLOADFILERESULT']._serialized_start=1423
  _globals['_UPLOADFILERESULT']._serialized_end=1488
  _globals['_PRESIGNDOWNLOADURLPARAMS']._serialized_start=1490
  _globals['_PRESIGNDOWNLOADURLPARAMS']._serialized_end=1576
  _globals['_PRESIGNDOWNLOADURLRESULT']._serialized_start=1578
  _globals['_PRESIGNDOWNLOADURLRESULT']._serialized_end=1622
  _globals['_FILESERVICE']._serialized_start=1625
  _globals['_FILESERVICE']._serialized_end=1947
# @@protoc_insertion_point(module_scope)
