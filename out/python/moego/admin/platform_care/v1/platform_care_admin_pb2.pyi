from moego.models.agreement.v1 import agreement_models_pb2 as _agreement_models_pb2
from moego.utils.v2 import pagination_messages_pb2 as _pagination_messages_pb2
from validate import validate_pb2 as _validate_pb2
from google.protobuf.internal import containers as _containers
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from collections.abc import Iterable as _Iterable, Mapping as _Mapping
from typing import ClassVar as _ClassVar, Optional as _Optional, Union as _Union

DESCRIPTOR: _descriptor.FileDescriptor

class PlatformCareRecord(_message.Message):
    __slots__ = ("id", "code", "email", "agreement_record_uuid", "account_id", "status", "signed_time", "create_time", "update_time", "is_deleted", "agreement_id", "link", "discount_code", "order_shipping_status", "show_accounting")
    ID_FIELD_NUMBER: _ClassVar[int]
    CODE_FIELD_NUMBER: _ClassVar[int]
    EMAIL_FIELD_NUMBER: _ClassVar[int]
    AGREEMENT_RECORD_UUID_FIELD_NUMBER: _ClassVar[int]
    ACCOUNT_ID_FIELD_NUMBER: _ClassVar[int]
    STATUS_FIELD_NUMBER: _ClassVar[int]
    SIGNED_TIME_FIELD_NUMBER: _ClassVar[int]
    CREATE_TIME_FIELD_NUMBER: _ClassVar[int]
    UPDATE_TIME_FIELD_NUMBER: _ClassVar[int]
    IS_DELETED_FIELD_NUMBER: _ClassVar[int]
    AGREEMENT_ID_FIELD_NUMBER: _ClassVar[int]
    LINK_FIELD_NUMBER: _ClassVar[int]
    DISCOUNT_CODE_FIELD_NUMBER: _ClassVar[int]
    ORDER_SHIPPING_STATUS_FIELD_NUMBER: _ClassVar[int]
    SHOW_ACCOUNTING_FIELD_NUMBER: _ClassVar[int]
    id: int
    code: str
    email: str
    agreement_record_uuid: str
    account_id: int
    status: int
    signed_time: int
    create_time: int
    update_time: int
    is_deleted: bool
    agreement_id: int
    link: str
    discount_code: str
    order_shipping_status: str
    show_accounting: int
    def __init__(self, id: _Optional[int] = ..., code: _Optional[str] = ..., email: _Optional[str] = ..., agreement_record_uuid: _Optional[str] = ..., account_id: _Optional[int] = ..., status: _Optional[int] = ..., signed_time: _Optional[int] = ..., create_time: _Optional[int] = ..., update_time: _Optional[int] = ..., is_deleted: bool = ..., agreement_id: _Optional[int] = ..., link: _Optional[str] = ..., discount_code: _Optional[str] = ..., order_shipping_status: _Optional[str] = ..., show_accounting: _Optional[int] = ...) -> None: ...

class CreatePlatformCareLinkParams(_message.Message):
    __slots__ = ("email", "agreement_id", "show_accounting")
    EMAIL_FIELD_NUMBER: _ClassVar[int]
    AGREEMENT_ID_FIELD_NUMBER: _ClassVar[int]
    SHOW_ACCOUNTING_FIELD_NUMBER: _ClassVar[int]
    email: str
    agreement_id: int
    show_accounting: bool
    def __init__(self, email: _Optional[str] = ..., agreement_id: _Optional[int] = ..., show_accounting: bool = ...) -> None: ...

class CreatePlatformCareLinkResult(_message.Message):
    __slots__ = ("link",)
    LINK_FIELD_NUMBER: _ClassVar[int]
    link: str
    def __init__(self, link: _Optional[str] = ...) -> None: ...

class AddPlatformCareRecordParams(_message.Message):
    __slots__ = ("code", "email", "agreement_record_uuid", "account_id")
    CODE_FIELD_NUMBER: _ClassVar[int]
    EMAIL_FIELD_NUMBER: _ClassVar[int]
    AGREEMENT_RECORD_UUID_FIELD_NUMBER: _ClassVar[int]
    ACCOUNT_ID_FIELD_NUMBER: _ClassVar[int]
    code: str
    email: str
    agreement_record_uuid: str
    account_id: int
    def __init__(self, code: _Optional[str] = ..., email: _Optional[str] = ..., agreement_record_uuid: _Optional[str] = ..., account_id: _Optional[int] = ...) -> None: ...

class AddPlatformCareRecordResult(_message.Message):
    __slots__ = ("id",)
    ID_FIELD_NUMBER: _ClassVar[int]
    id: int
    def __init__(self, id: _Optional[int] = ...) -> None: ...

class GetPlatformCareRecordByCodeParams(_message.Message):
    __slots__ = ("code",)
    CODE_FIELD_NUMBER: _ClassVar[int]
    code: str
    def __init__(self, code: _Optional[str] = ...) -> None: ...

class GetPlatformCareRecordByCodeResult(_message.Message):
    __slots__ = ("platform_care_record",)
    PLATFORM_CARE_RECORD_FIELD_NUMBER: _ClassVar[int]
    platform_care_record: PlatformCareRecord
    def __init__(self, platform_care_record: _Optional[_Union[PlatformCareRecord, _Mapping]] = ...) -> None: ...

class GetPlatformCareRecordListParams(_message.Message):
    __slots__ = ("code", "email", "agreement_uuid", "account_id", "status", "agreement_id", "pagination")
    CODE_FIELD_NUMBER: _ClassVar[int]
    EMAIL_FIELD_NUMBER: _ClassVar[int]
    AGREEMENT_UUID_FIELD_NUMBER: _ClassVar[int]
    ACCOUNT_ID_FIELD_NUMBER: _ClassVar[int]
    STATUS_FIELD_NUMBER: _ClassVar[int]
    AGREEMENT_ID_FIELD_NUMBER: _ClassVar[int]
    PAGINATION_FIELD_NUMBER: _ClassVar[int]
    code: str
    email: str
    agreement_uuid: str
    account_id: int
    status: int
    agreement_id: int
    pagination: _pagination_messages_pb2.PaginationRequest
    def __init__(self, code: _Optional[str] = ..., email: _Optional[str] = ..., agreement_uuid: _Optional[str] = ..., account_id: _Optional[int] = ..., status: _Optional[int] = ..., agreement_id: _Optional[int] = ..., pagination: _Optional[_Union[_pagination_messages_pb2.PaginationRequest, _Mapping]] = ...) -> None: ...

class GetPlatformCareRecordListResult(_message.Message):
    __slots__ = ("platform_care_records", "agreement_map", "status_map", "pagination")
    class AgreementMapEntry(_message.Message):
        __slots__ = ("key", "value")
        KEY_FIELD_NUMBER: _ClassVar[int]
        VALUE_FIELD_NUMBER: _ClassVar[int]
        key: int
        value: _agreement_models_pb2.AgreementModelSimpleView
        def __init__(self, key: _Optional[int] = ..., value: _Optional[_Union[_agreement_models_pb2.AgreementModelSimpleView, _Mapping]] = ...) -> None: ...
    class StatusMapEntry(_message.Message):
        __slots__ = ("key", "value")
        KEY_FIELD_NUMBER: _ClassVar[int]
        VALUE_FIELD_NUMBER: _ClassVar[int]
        key: int
        value: str
        def __init__(self, key: _Optional[int] = ..., value: _Optional[str] = ...) -> None: ...
    PLATFORM_CARE_RECORDS_FIELD_NUMBER: _ClassVar[int]
    AGREEMENT_MAP_FIELD_NUMBER: _ClassVar[int]
    STATUS_MAP_FIELD_NUMBER: _ClassVar[int]
    PAGINATION_FIELD_NUMBER: _ClassVar[int]
    platform_care_records: _containers.RepeatedCompositeFieldContainer[PlatformCareRecord]
    agreement_map: _containers.MessageMap[int, _agreement_models_pb2.AgreementModelSimpleView]
    status_map: _containers.ScalarMap[int, str]
    pagination: _pagination_messages_pb2.PaginationResponse
    def __init__(self, platform_care_records: _Optional[_Iterable[_Union[PlatformCareRecord, _Mapping]]] = ..., agreement_map: _Optional[_Mapping[int, _agreement_models_pb2.AgreementModelSimpleView]] = ..., status_map: _Optional[_Mapping[int, str]] = ..., pagination: _Optional[_Union[_pagination_messages_pb2.PaginationResponse, _Mapping]] = ...) -> None: ...

class UpdatePlatformCareRecordParams(_message.Message):
    __slots__ = ("id", "status")
    ID_FIELD_NUMBER: _ClassVar[int]
    STATUS_FIELD_NUMBER: _ClassVar[int]
    id: int
    status: int
    def __init__(self, id: _Optional[int] = ..., status: _Optional[int] = ...) -> None: ...

class UpdatePlatformCareRecordResult(_message.Message):
    __slots__ = ("id",)
    ID_FIELD_NUMBER: _ClassVar[int]
    id: int
    def __init__(self, id: _Optional[int] = ...) -> None: ...

class DeletePlatformCareRecordParams(_message.Message):
    __slots__ = ("id",)
    ID_FIELD_NUMBER: _ClassVar[int]
    id: int
    def __init__(self, id: _Optional[int] = ...) -> None: ...

class DeletePlatformCareRecordResult(_message.Message):
    __slots__ = ("id",)
    ID_FIELD_NUMBER: _ClassVar[int]
    id: int
    def __init__(self, id: _Optional[int] = ...) -> None: ...
