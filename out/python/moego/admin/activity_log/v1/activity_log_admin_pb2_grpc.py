# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

from moego.admin.activity_log.v1 import activity_log_admin_pb2 as moego_dot_admin_dot_activity__log_dot_v1_dot_activity__log__admin__pb2


class ActivityLogServiceStub(object):
    """Activity log service
    """

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.SearchActivityLogPage = channel.unary_unary(
                '/moego.admin.activity_log.v1.ActivityLogService/SearchActivityLogPage',
                request_serializer=moego_dot_admin_dot_activity__log_dot_v1_dot_activity__log__admin__pb2.SearchActivityLogPageRequest.SerializeToString,
                response_deserializer=moego_dot_admin_dot_activity__log_dot_v1_dot_activity__log__admin__pb2.SearchActivityLogPageResponse.FromString,
                _registered_method=True)
        self.GetActivityLogDetails = channel.unary_unary(
                '/moego.admin.activity_log.v1.ActivityLogService/GetActivityLogDetails',
                request_serializer=moego_dot_admin_dot_activity__log_dot_v1_dot_activity__log__admin__pb2.GetActivityLogDetailsRequest.SerializeToString,
                response_deserializer=moego_dot_admin_dot_activity__log_dot_v1_dot_activity__log__admin__pb2.GetActivityLogDetailsResponse.FromString,
                _registered_method=True)
        self.SearchOperatorPage = channel.unary_unary(
                '/moego.admin.activity_log.v1.ActivityLogService/SearchOperatorPage',
                request_serializer=moego_dot_admin_dot_activity__log_dot_v1_dot_activity__log__admin__pb2.SearchOperatorPageRequest.SerializeToString,
                response_deserializer=moego_dot_admin_dot_activity__log_dot_v1_dot_activity__log__admin__pb2.SearchOperatorPageResponse.FromString,
                _registered_method=True)
        self.SearchResourceTypePage = channel.unary_unary(
                '/moego.admin.activity_log.v1.ActivityLogService/SearchResourceTypePage',
                request_serializer=moego_dot_admin_dot_activity__log_dot_v1_dot_activity__log__admin__pb2.SearchResourceTypePageRequest.SerializeToString,
                response_deserializer=moego_dot_admin_dot_activity__log_dot_v1_dot_activity__log__admin__pb2.SearchResourceTypePageResponse.FromString,
                _registered_method=True)
        self.SearchActionPage = channel.unary_unary(
                '/moego.admin.activity_log.v1.ActivityLogService/SearchActionPage',
                request_serializer=moego_dot_admin_dot_activity__log_dot_v1_dot_activity__log__admin__pb2.SearchActionPageRequest.SerializeToString,
                response_deserializer=moego_dot_admin_dot_activity__log_dot_v1_dot_activity__log__admin__pb2.SearchActionPageResponse.FromString,
                _registered_method=True)
        self.SearchOwnerPage = channel.unary_unary(
                '/moego.admin.activity_log.v1.ActivityLogService/SearchOwnerPage',
                request_serializer=moego_dot_admin_dot_activity__log_dot_v1_dot_activity__log__admin__pb2.SearchOwnerPageRequest.SerializeToString,
                response_deserializer=moego_dot_admin_dot_activity__log_dot_v1_dot_activity__log__admin__pb2.SearchOwnerPageResponse.FromString,
                _registered_method=True)


class ActivityLogServiceServicer(object):
    """Activity log service
    """

    def SearchActivityLogPage(self, request, context):
        """Search activity logs with pagination
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetActivityLogDetails(self, request, context):
        """Get activity log details
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SearchOperatorPage(self, request, context):
        """Search operators with pagination
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SearchResourceTypePage(self, request, context):
        """Search resources with pagination
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SearchActionPage(self, request, context):
        """Search actions with pagination
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SearchOwnerPage(self, request, context):
        """Search owners with pagination
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_ActivityLogServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'SearchActivityLogPage': grpc.unary_unary_rpc_method_handler(
                    servicer.SearchActivityLogPage,
                    request_deserializer=moego_dot_admin_dot_activity__log_dot_v1_dot_activity__log__admin__pb2.SearchActivityLogPageRequest.FromString,
                    response_serializer=moego_dot_admin_dot_activity__log_dot_v1_dot_activity__log__admin__pb2.SearchActivityLogPageResponse.SerializeToString,
            ),
            'GetActivityLogDetails': grpc.unary_unary_rpc_method_handler(
                    servicer.GetActivityLogDetails,
                    request_deserializer=moego_dot_admin_dot_activity__log_dot_v1_dot_activity__log__admin__pb2.GetActivityLogDetailsRequest.FromString,
                    response_serializer=moego_dot_admin_dot_activity__log_dot_v1_dot_activity__log__admin__pb2.GetActivityLogDetailsResponse.SerializeToString,
            ),
            'SearchOperatorPage': grpc.unary_unary_rpc_method_handler(
                    servicer.SearchOperatorPage,
                    request_deserializer=moego_dot_admin_dot_activity__log_dot_v1_dot_activity__log__admin__pb2.SearchOperatorPageRequest.FromString,
                    response_serializer=moego_dot_admin_dot_activity__log_dot_v1_dot_activity__log__admin__pb2.SearchOperatorPageResponse.SerializeToString,
            ),
            'SearchResourceTypePage': grpc.unary_unary_rpc_method_handler(
                    servicer.SearchResourceTypePage,
                    request_deserializer=moego_dot_admin_dot_activity__log_dot_v1_dot_activity__log__admin__pb2.SearchResourceTypePageRequest.FromString,
                    response_serializer=moego_dot_admin_dot_activity__log_dot_v1_dot_activity__log__admin__pb2.SearchResourceTypePageResponse.SerializeToString,
            ),
            'SearchActionPage': grpc.unary_unary_rpc_method_handler(
                    servicer.SearchActionPage,
                    request_deserializer=moego_dot_admin_dot_activity__log_dot_v1_dot_activity__log__admin__pb2.SearchActionPageRequest.FromString,
                    response_serializer=moego_dot_admin_dot_activity__log_dot_v1_dot_activity__log__admin__pb2.SearchActionPageResponse.SerializeToString,
            ),
            'SearchOwnerPage': grpc.unary_unary_rpc_method_handler(
                    servicer.SearchOwnerPage,
                    request_deserializer=moego_dot_admin_dot_activity__log_dot_v1_dot_activity__log__admin__pb2.SearchOwnerPageRequest.FromString,
                    response_serializer=moego_dot_admin_dot_activity__log_dot_v1_dot_activity__log__admin__pb2.SearchOwnerPageResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'moego.admin.activity_log.v1.ActivityLogService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('moego.admin.activity_log.v1.ActivityLogService', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class ActivityLogService(object):
    """Activity log service
    """

    @staticmethod
    def SearchActivityLogPage(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.admin.activity_log.v1.ActivityLogService/SearchActivityLogPage',
            moego_dot_admin_dot_activity__log_dot_v1_dot_activity__log__admin__pb2.SearchActivityLogPageRequest.SerializeToString,
            moego_dot_admin_dot_activity__log_dot_v1_dot_activity__log__admin__pb2.SearchActivityLogPageResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetActivityLogDetails(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.admin.activity_log.v1.ActivityLogService/GetActivityLogDetails',
            moego_dot_admin_dot_activity__log_dot_v1_dot_activity__log__admin__pb2.GetActivityLogDetailsRequest.SerializeToString,
            moego_dot_admin_dot_activity__log_dot_v1_dot_activity__log__admin__pb2.GetActivityLogDetailsResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def SearchOperatorPage(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.admin.activity_log.v1.ActivityLogService/SearchOperatorPage',
            moego_dot_admin_dot_activity__log_dot_v1_dot_activity__log__admin__pb2.SearchOperatorPageRequest.SerializeToString,
            moego_dot_admin_dot_activity__log_dot_v1_dot_activity__log__admin__pb2.SearchOperatorPageResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def SearchResourceTypePage(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.admin.activity_log.v1.ActivityLogService/SearchResourceTypePage',
            moego_dot_admin_dot_activity__log_dot_v1_dot_activity__log__admin__pb2.SearchResourceTypePageRequest.SerializeToString,
            moego_dot_admin_dot_activity__log_dot_v1_dot_activity__log__admin__pb2.SearchResourceTypePageResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def SearchActionPage(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.admin.activity_log.v1.ActivityLogService/SearchActionPage',
            moego_dot_admin_dot_activity__log_dot_v1_dot_activity__log__admin__pb2.SearchActionPageRequest.SerializeToString,
            moego_dot_admin_dot_activity__log_dot_v1_dot_activity__log__admin__pb2.SearchActionPageResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def SearchOwnerPage(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.admin.activity_log.v1.ActivityLogService/SearchOwnerPage',
            moego_dot_admin_dot_activity__log_dot_v1_dot_activity__log__admin__pb2.SearchOwnerPageRequest.SerializeToString,
            moego_dot_admin_dot_activity__log_dot_v1_dot_activity__log__admin__pb2.SearchOwnerPageResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)
