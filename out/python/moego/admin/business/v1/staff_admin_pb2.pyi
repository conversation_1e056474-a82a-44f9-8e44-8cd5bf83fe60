from google.protobuf import struct_pb2 as _struct_pb2
from google.protobuf import timestamp_pb2 as _timestamp_pb2
from moego.models.account.v1 import account_models_pb2 as _account_models_pb2
from moego.models.organization.v1 import van_model_pb2 as _van_model_pb2
from moego.utils.v2 import pagination_messages_pb2 as _pagination_messages_pb2
from validate import validate_pb2 as _validate_pb2
from google.protobuf.internal import containers as _containers
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from collections.abc import Iterable as _Iterable, Mapping as _Mapping
from typing import ClassVar as _ClassVar, Optional as _Optional, Union as _Union

DESCRIPTOR: _descriptor.FileDescriptor

class DescribeStaffsParams(_message.Message):
    __slots__ = ("business_id", "account_id", "include_deleted", "name", "company_id", "pagination")
    BUSINESS_ID_FIELD_NUMBER: _ClassVar[int]
    ACCOUNT_ID_FIELD_NUMBER: _ClassVar[int]
    INCLUDE_DELETED_FIELD_NUMBER: _ClassVar[int]
    NAME_FIELD_NUMBER: _ClassVar[int]
    COMPANY_ID_FIELD_NUMBER: _ClassVar[int]
    PAGINATION_FIELD_NUMBER: _ClassVar[int]
    business_id: int
    account_id: int
    include_deleted: bool
    name: str
    company_id: int
    pagination: _pagination_messages_pb2.PaginationRequest
    def __init__(self, business_id: _Optional[int] = ..., account_id: _Optional[int] = ..., include_deleted: bool = ..., name: _Optional[str] = ..., company_id: _Optional[int] = ..., pagination: _Optional[_Union[_pagination_messages_pb2.PaginationRequest, _Mapping]] = ...) -> None: ...

class DescribeStaffsResult(_message.Message):
    __slots__ = ("deprecated_staffs", "deprecated_business_map", "account_map", "company_van_list_map", "pagination")
    class DeprecatedBusinessMapEntry(_message.Message):
        __slots__ = ("key", "value")
        KEY_FIELD_NUMBER: _ClassVar[int]
        VALUE_FIELD_NUMBER: _ClassVar[int]
        key: int
        value: _struct_pb2.Struct
        def __init__(self, key: _Optional[int] = ..., value: _Optional[_Union[_struct_pb2.Struct, _Mapping]] = ...) -> None: ...
    class AccountMapEntry(_message.Message):
        __slots__ = ("key", "value")
        KEY_FIELD_NUMBER: _ClassVar[int]
        VALUE_FIELD_NUMBER: _ClassVar[int]
        key: int
        value: _account_models_pb2.AccountModelSearchView
        def __init__(self, key: _Optional[int] = ..., value: _Optional[_Union[_account_models_pb2.AccountModelSearchView, _Mapping]] = ...) -> None: ...
    class CompanyVanListMapEntry(_message.Message):
        __slots__ = ("key", "value")
        KEY_FIELD_NUMBER: _ClassVar[int]
        VALUE_FIELD_NUMBER: _ClassVar[int]
        key: int
        value: _van_model_pb2.VanListModel
        def __init__(self, key: _Optional[int] = ..., value: _Optional[_Union[_van_model_pb2.VanListModel, _Mapping]] = ...) -> None: ...
    DEPRECATED_STAFFS_FIELD_NUMBER: _ClassVar[int]
    DEPRECATED_BUSINESS_MAP_FIELD_NUMBER: _ClassVar[int]
    ACCOUNT_MAP_FIELD_NUMBER: _ClassVar[int]
    COMPANY_VAN_LIST_MAP_FIELD_NUMBER: _ClassVar[int]
    PAGINATION_FIELD_NUMBER: _ClassVar[int]
    deprecated_staffs: _containers.RepeatedCompositeFieldContainer[_struct_pb2.Struct]
    deprecated_business_map: _containers.MessageMap[int, _struct_pb2.Struct]
    account_map: _containers.MessageMap[int, _account_models_pb2.AccountModelSearchView]
    company_van_list_map: _containers.MessageMap[int, _van_model_pb2.VanListModel]
    pagination: _pagination_messages_pb2.PaginationResponse
    def __init__(self, deprecated_staffs: _Optional[_Iterable[_Union[_struct_pb2.Struct, _Mapping]]] = ..., deprecated_business_map: _Optional[_Mapping[int, _struct_pb2.Struct]] = ..., account_map: _Optional[_Mapping[int, _account_models_pb2.AccountModelSearchView]] = ..., company_van_list_map: _Optional[_Mapping[int, _van_model_pb2.VanListModel]] = ..., pagination: _Optional[_Union[_pagination_messages_pb2.PaginationResponse, _Mapping]] = ...) -> None: ...

class EditStaffParams(_message.Message):
    __slots__ = ("id", "business_id", "first_name", "last_name", "account_id", "role_id", "hire_date", "note", "avatar_path", "phone_number", "fire_date", "allow_login", "book_online_available", "show_on_calendar", "show_calendar_staff_all", "status")
    ID_FIELD_NUMBER: _ClassVar[int]
    BUSINESS_ID_FIELD_NUMBER: _ClassVar[int]
    FIRST_NAME_FIELD_NUMBER: _ClassVar[int]
    LAST_NAME_FIELD_NUMBER: _ClassVar[int]
    ACCOUNT_ID_FIELD_NUMBER: _ClassVar[int]
    ROLE_ID_FIELD_NUMBER: _ClassVar[int]
    HIRE_DATE_FIELD_NUMBER: _ClassVar[int]
    NOTE_FIELD_NUMBER: _ClassVar[int]
    AVATAR_PATH_FIELD_NUMBER: _ClassVar[int]
    PHONE_NUMBER_FIELD_NUMBER: _ClassVar[int]
    FIRE_DATE_FIELD_NUMBER: _ClassVar[int]
    ALLOW_LOGIN_FIELD_NUMBER: _ClassVar[int]
    BOOK_ONLINE_AVAILABLE_FIELD_NUMBER: _ClassVar[int]
    SHOW_ON_CALENDAR_FIELD_NUMBER: _ClassVar[int]
    SHOW_CALENDAR_STAFF_ALL_FIELD_NUMBER: _ClassVar[int]
    STATUS_FIELD_NUMBER: _ClassVar[int]
    id: int
    business_id: int
    first_name: str
    last_name: str
    account_id: int
    role_id: int
    hire_date: _timestamp_pb2.Timestamp
    note: str
    avatar_path: str
    phone_number: str
    fire_date: _timestamp_pb2.Timestamp
    allow_login: int
    book_online_available: int
    show_on_calendar: int
    show_calendar_staff_all: int
    status: int
    def __init__(self, id: _Optional[int] = ..., business_id: _Optional[int] = ..., first_name: _Optional[str] = ..., last_name: _Optional[str] = ..., account_id: _Optional[int] = ..., role_id: _Optional[int] = ..., hire_date: _Optional[_Union[datetime.datetime, _timestamp_pb2.Timestamp, _Mapping]] = ..., note: _Optional[str] = ..., avatar_path: _Optional[str] = ..., phone_number: _Optional[str] = ..., fire_date: _Optional[_Union[datetime.datetime, _timestamp_pb2.Timestamp, _Mapping]] = ..., allow_login: _Optional[int] = ..., book_online_available: _Optional[int] = ..., show_on_calendar: _Optional[int] = ..., show_calendar_staff_all: _Optional[int] = ..., status: _Optional[int] = ...) -> None: ...

class EditStaffResult(_message.Message):
    __slots__ = ("deprecated_staff",)
    DEPRECATED_STAFF_FIELD_NUMBER: _ClassVar[int]
    deprecated_staff: _struct_pb2.Struct
    def __init__(self, deprecated_staff: _Optional[_Union[_struct_pb2.Struct, _Mapping]] = ...) -> None: ...

class DeleteStaffParams(_message.Message):
    __slots__ = ("id", "business_id")
    ID_FIELD_NUMBER: _ClassVar[int]
    BUSINESS_ID_FIELD_NUMBER: _ClassVar[int]
    id: int
    business_id: int
    def __init__(self, id: _Optional[int] = ..., business_id: _Optional[int] = ...) -> None: ...

class DeleteStaffResult(_message.Message):
    __slots__ = ()
    def __init__(self) -> None: ...

class ForceAssignStaffParams(_message.Message):
    __slots__ = ("staff_id", "van_id")
    STAFF_ID_FIELD_NUMBER: _ClassVar[int]
    VAN_ID_FIELD_NUMBER: _ClassVar[int]
    staff_id: int
    van_id: int
    def __init__(self, staff_id: _Optional[int] = ..., van_id: _Optional[int] = ...) -> None: ...

class ForceAssignStaffResult(_message.Message):
    __slots__ = ()
    def __init__(self) -> None: ...
