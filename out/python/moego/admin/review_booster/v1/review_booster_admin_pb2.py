# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: moego/admin/review_booster/v1/review_booster_admin.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'moego/admin/review_booster/v1/review_booster_admin.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.protobuf import struct_pb2 as google_dot_protobuf_dot_struct__pb2
from moego.models.business_customer.v1 import business_customer_models_pb2 as moego_dot_models_dot_business__customer_dot_v1_dot_business__customer__models__pb2
from moego.utils.v2 import pagination_messages_pb2 as moego_dot_utils_dot_v2_dot_pagination__messages__pb2
from validate import validate_pb2 as validate_dot_validate__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n8moego/admin/review_booster/v1/review_booster_admin.proto\x12\x1dmoego.admin.review_booster.v1\x1a\x1cgoogle/protobuf/struct.proto\x1a@moego/models/business_customer/v1/business_customer_models.proto\x1a(moego/utils/v2/pagination_messages.proto\x1a\x17validate/validate.proto\"\xc2\x01\n\x1dListReviewBoosterRecordParams\x12\x1f\n\x0b\x62usiness_id\x18\x01 \x01(\x03R\nbusinessId\x12-\n\x0b\x63ustomer_id\x18\x02 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00H\x00R\ncustomerId\x88\x01\x01\x12\x41\n\npagination\x18\x0f \x01(\x0b\x32!.moego.utils.v2.PaginationRequestR\npaginationB\x0e\n\x0c_customer_id\"\x9e\x03\n\x1dListReviewBoosterRecordResult\x12M\n\x16review_booster_records\x18\x01 \x03(\x0b\x32\x17.google.protobuf.StructR\x14reviewBoosterRecords\x12i\n\tcustomers\x18\x02 \x03(\x0b\x32K.moego.admin.review_booster.v1.ListReviewBoosterRecordResult.CustomersEntryR\tcustomers\x12\x42\n\npagination\x18\x0f \x01(\x0b\x32\".moego.utils.v2.PaginationResponseR\npagination\x1a\x7f\n\x0e\x43ustomersEntry\x12\x10\n\x03key\x18\x01 \x01(\x03R\x03key\x12W\n\x05value\x18\x02 \x01(\x0b\x32\x41.moego.models.business_customer.v1.BusinessCustomerNameStatusViewR\x05value:\x02\x38\x01\"w\n\x1fUpdateReviewBoosterRecordParams\x12$\n\trecord_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\x08recordId\x12$\n\x05score\x18\x02 \x01(\x03\x42\t\xfa\x42\x06\"\x04\x18\x05(\x00H\x00R\x05score\x88\x01\x01\x42\x08\n\x06_score\"!\n\x1fUpdateReviewBoosterRecordResult2\xd1\x02\n\x19ReviewBoosterAdminService\x12\x95\x01\n\x17ListReviewBoosterRecord\x12<.moego.admin.review_booster.v1.ListReviewBoosterRecordParams\x1a<.moego.admin.review_booster.v1.ListReviewBoosterRecordResult\x12\x9b\x01\n\x19UpdateReviewBoosterRecord\x12>.moego.admin.review_booster.v1.UpdateReviewBoosterRecordParams\x1a>.moego.admin.review_booster.v1.UpdateReviewBoosterRecordParamsB\x90\x01\n%com.moego.idl.admin.review_booster.v1P\x01Zegithub.com/MoeGolibrary/moego-api-definitions/out/go/moego/admin/review_booster/v1;reviewboosterapipbb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'moego.admin.review_booster.v1.review_booster_admin_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n%com.moego.idl.admin.review_booster.v1P\001Zegithub.com/MoeGolibrary/moego-api-definitions/out/go/moego/admin/review_booster/v1;reviewboosterapipb'
  _globals['_LISTREVIEWBOOSTERRECORDPARAMS'].fields_by_name['customer_id']._loaded_options = None
  _globals['_LISTREVIEWBOOSTERRECORDPARAMS'].fields_by_name['customer_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_LISTREVIEWBOOSTERRECORDRESULT_CUSTOMERSENTRY']._loaded_options = None
  _globals['_LISTREVIEWBOOSTERRECORDRESULT_CUSTOMERSENTRY']._serialized_options = b'8\001'
  _globals['_UPDATEREVIEWBOOSTERRECORDPARAMS'].fields_by_name['record_id']._loaded_options = None
  _globals['_UPDATEREVIEWBOOSTERRECORDPARAMS'].fields_by_name['record_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_UPDATEREVIEWBOOSTERRECORDPARAMS'].fields_by_name['score']._loaded_options = None
  _globals['_UPDATEREVIEWBOOSTERRECORDPARAMS'].fields_by_name['score']._serialized_options = b'\372B\006\"\004\030\005(\000'
  _globals['_LISTREVIEWBOOSTERRECORDPARAMS']._serialized_start=255
  _globals['_LISTREVIEWBOOSTERRECORDPARAMS']._serialized_end=449
  _globals['_LISTREVIEWBOOSTERRECORDRESULT']._serialized_start=452
  _globals['_LISTREVIEWBOOSTERRECORDRESULT']._serialized_end=866
  _globals['_LISTREVIEWBOOSTERRECORDRESULT_CUSTOMERSENTRY']._serialized_start=739
  _globals['_LISTREVIEWBOOSTERRECORDRESULT_CUSTOMERSENTRY']._serialized_end=866
  _globals['_UPDATEREVIEWBOOSTERRECORDPARAMS']._serialized_start=868
  _globals['_UPDATEREVIEWBOOSTERRECORDPARAMS']._serialized_end=987
  _globals['_UPDATEREVIEWBOOSTERRECORDRESULT']._serialized_start=989
  _globals['_UPDATEREVIEWBOOSTERRECORDRESULT']._serialized_end=1022
  _globals['_REVIEWBOOSTERADMINSERVICE']._serialized_start=1025
  _globals['_REVIEWBOOSTERADMINSERVICE']._serialized_end=1362
# @@protoc_insertion_point(module_scope)
