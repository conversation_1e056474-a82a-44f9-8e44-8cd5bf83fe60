# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: moego/utils/v1/condition_messages.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'moego/utils/v1/condition_messages.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\'moego/utils/v1/condition_messages.proto\x12\x0emoego.utils.v1\"\xcd\x01\n\x0eInt64Condition\x12\x13\n\x02\x65q\x18\x01 \x01(\x03H\x00R\x02\x65q\x88\x01\x01\x12\x0e\n\x02in\x18\x02 \x03(\x03R\x02in\x12\x15\n\x06not_in\x18\x03 \x03(\x03R\x05notIn\x12\x15\n\x03lte\x18\x04 \x01(\x03H\x01R\x03lte\x88\x01\x01\x12\x15\n\x03gte\x18\x05 \x01(\x03H\x02R\x03gte\x88\x01\x01\x12\x13\n\x02lt\x18\x06 \x01(\x03H\x03R\x02lt\x88\x01\x01\x12\x13\n\x02gt\x18\x07 \x01(\x03H\x04R\x02gt\x88\x01\x01:\x02\x18\x01\x42\x05\n\x03_eqB\x06\n\x04_lteB\x06\n\x04_gteB\x05\n\x03_ltB\x05\n\x03_gt\"\x8b\x04\n\x0fStringCondition\x12\x13\n\x02\x65q\x18\x01 \x01(\tH\x00R\x02\x65q\x88\x01\x01\x12\x0e\n\x02in\x18\x02 \x03(\tR\x02in\x12\x15\n\x06not_in\x18\x03 \x03(\tR\x05notIn\x12\x15\n\x03lte\x18\x04 \x01(\tH\x01R\x03lte\x88\x01\x01\x12\x15\n\x03gte\x18\x05 \x01(\tH\x02R\x03gte\x88\x01\x01\x12\x13\n\x02lt\x18\x06 \x01(\tH\x03R\x02lt\x88\x01\x01\x12\x13\n\x02gt\x18\x07 \x01(\tH\x04R\x02gt\x88\x01\x01\x12\x17\n\x04like\x18\x08 \x01(\tH\x05R\x04like\x88\x01\x01\x12$\n\x0bprefix_like\x18\t \x01(\tH\x06R\nprefixLike\x88\x01\x01\x12$\n\x0bsuffix_like\x18\n \x01(\tH\x07R\nsuffixLike\x88\x01\x01\x12\x1e\n\x08not_like\x18\x0b \x01(\tH\x08R\x07notLike\x88\x01\x01\x12+\n\x0fprefix_not_like\x18\x0c \x01(\tH\tR\rprefixNotLike\x88\x01\x01\x12+\n\x0fsuffix_not_like\x18\r \x01(\tH\nR\rsuffixNotLike\x88\x01\x01:\x02\x18\x01\x42\x05\n\x03_eqB\x06\n\x04_lteB\x06\n\x04_gteB\x05\n\x03_ltB\x05\n\x03_gtB\x07\n\x05_likeB\x0e\n\x0c_prefix_likeB\x0e\n\x0c_suffix_likeB\x0b\n\t_not_likeB\x12\n\x10_prefix_not_likeB\x12\n\x10_suffix_not_likeBg\n\x16\x63om.moego.idl.utils.v1P\x01ZKgithub.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v1;utilsV1b\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'moego.utils.v1.condition_messages_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n\026com.moego.idl.utils.v1P\001ZKgithub.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v1;utilsV1'
  _globals['_INT64CONDITION']._loaded_options = None
  _globals['_INT64CONDITION']._serialized_options = b'\030\001'
  _globals['_STRINGCONDITION']._loaded_options = None
  _globals['_STRINGCONDITION']._serialized_options = b'\030\001'
  _globals['_INT64CONDITION']._serialized_start=60
  _globals['_INT64CONDITION']._serialized_end=265
  _globals['_STRINGCONDITION']._serialized_start=268
  _globals['_STRINGCONDITION']._serialized_end=791
# @@protoc_insertion_point(module_scope)
