from moego.models.enterprise.v1 import enterprise_defs_pb2 as _enterprise_defs_pb2
from moego.models.enterprise.v1 import enterprise_enums_pb2 as _enterprise_enums_pb2
from moego.models.enterprise.v1 import enterprise_models_pb2 as _enterprise_models_pb2
from moego.models.enterprise.v1 import option_models_pb2 as _option_models_pb2
from moego.models.enterprise.v1 import tenant_group_models_pb2 as _tenant_group_models_pb2
from moego.models.enterprise.v1 import tenant_template_defs_pb2 as _tenant_template_defs_pb2
from moego.models.enterprise.v1 import tenant_template_models_pb2 as _tenant_template_models_pb2
from validate import validate_pb2 as _validate_pb2
from google.protobuf.internal import containers as _containers
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from collections.abc import Iterable as _Iterable, Mapping as _Mapping
from typing import ClassVar as _ClassVar, Optional as _Optional, Union as _Union

DESCRIPTOR: _descriptor.FileDescriptor

class GetEnterpriseParams(_message.Message):
    __slots__ = ("id", "need_tenant_template")
    ID_FIELD_NUMBER: _ClassVar[int]
    NEED_TENANT_TEMPLATE_FIELD_NUMBER: _ClassVar[int]
    id: int
    need_tenant_template: bool
    def __init__(self, id: _Optional[int] = ..., need_tenant_template: bool = ...) -> None: ...

class GetEnterpriseResult(_message.Message):
    __slots__ = ("enterprise", "tenant_template")
    ENTERPRISE_FIELD_NUMBER: _ClassVar[int]
    TENANT_TEMPLATE_FIELD_NUMBER: _ClassVar[int]
    enterprise: _enterprise_models_pb2.EnterpriseModel
    tenant_template: _containers.RepeatedCompositeFieldContainer[_tenant_template_models_pb2.TenantTemplateModel]
    def __init__(self, enterprise: _Optional[_Union[_enterprise_models_pb2.EnterpriseModel, _Mapping]] = ..., tenant_template: _Optional[_Iterable[_Union[_tenant_template_models_pb2.TenantTemplateModel, _Mapping]]] = ...) -> None: ...

class CreateEnterpriseParams(_message.Message):
    __slots__ = ("enterprise", "tenant_template")
    ENTERPRISE_FIELD_NUMBER: _ClassVar[int]
    TENANT_TEMPLATE_FIELD_NUMBER: _ClassVar[int]
    enterprise: _enterprise_defs_pb2.CreateEnterpriseDef
    tenant_template: _tenant_template_defs_pb2.CreateTenantTemplateDef
    def __init__(self, enterprise: _Optional[_Union[_enterprise_defs_pb2.CreateEnterpriseDef, _Mapping]] = ..., tenant_template: _Optional[_Union[_tenant_template_defs_pb2.CreateTenantTemplateDef, _Mapping]] = ...) -> None: ...

class CreateEnterpriseResult(_message.Message):
    __slots__ = ("enterprise",)
    ENTERPRISE_FIELD_NUMBER: _ClassVar[int]
    enterprise: _enterprise_models_pb2.EnterpriseModel
    def __init__(self, enterprise: _Optional[_Union[_enterprise_models_pb2.EnterpriseModel, _Mapping]] = ...) -> None: ...

class UpdateEnterpriseParams(_message.Message):
    __slots__ = ("id", "enterprise", "tenant_template")
    ID_FIELD_NUMBER: _ClassVar[int]
    ENTERPRISE_FIELD_NUMBER: _ClassVar[int]
    TENANT_TEMPLATE_FIELD_NUMBER: _ClassVar[int]
    id: int
    enterprise: _enterprise_defs_pb2.UpdateEnterpriseDef
    tenant_template: _tenant_template_defs_pb2.UpdateTenantTemplateDef
    def __init__(self, id: _Optional[int] = ..., enterprise: _Optional[_Union[_enterprise_defs_pb2.UpdateEnterpriseDef, _Mapping]] = ..., tenant_template: _Optional[_Union[_tenant_template_defs_pb2.UpdateTenantTemplateDef, _Mapping]] = ...) -> None: ...

class UpdateEnterpriseResult(_message.Message):
    __slots__ = ("enterprise", "tenant_template")
    ENTERPRISE_FIELD_NUMBER: _ClassVar[int]
    TENANT_TEMPLATE_FIELD_NUMBER: _ClassVar[int]
    enterprise: _enterprise_models_pb2.EnterpriseModel
    tenant_template: _tenant_template_models_pb2.TenantTemplateModel
    def __init__(self, enterprise: _Optional[_Union[_enterprise_models_pb2.EnterpriseModel, _Mapping]] = ..., tenant_template: _Optional[_Union[_tenant_template_models_pb2.TenantTemplateModel, _Mapping]] = ...) -> None: ...

class ListTenantTemplateParams(_message.Message):
    __slots__ = ("enterprise_id", "filter")
    class Filter(_message.Message):
        __slots__ = ("tenant_id", "status", "type")
        TENANT_ID_FIELD_NUMBER: _ClassVar[int]
        STATUS_FIELD_NUMBER: _ClassVar[int]
        TYPE_FIELD_NUMBER: _ClassVar[int]
        tenant_id: _containers.RepeatedScalarFieldContainer[int]
        status: _containers.RepeatedScalarFieldContainer[_tenant_template_models_pb2.TenantTemplateModel.Status]
        type: _containers.RepeatedScalarFieldContainer[_tenant_template_models_pb2.TenantTemplateModel.Type]
        def __init__(self, tenant_id: _Optional[_Iterable[int]] = ..., status: _Optional[_Iterable[_Union[_tenant_template_models_pb2.TenantTemplateModel.Status, str]]] = ..., type: _Optional[_Iterable[_Union[_tenant_template_models_pb2.TenantTemplateModel.Type, str]]] = ...) -> None: ...
    ENTERPRISE_ID_FIELD_NUMBER: _ClassVar[int]
    FILTER_FIELD_NUMBER: _ClassVar[int]
    enterprise_id: int
    filter: ListTenantTemplateParams.Filter
    def __init__(self, enterprise_id: _Optional[int] = ..., filter: _Optional[_Union[ListTenantTemplateParams.Filter, _Mapping]] = ...) -> None: ...

class ListTenantTemplateResult(_message.Message):
    __slots__ = ("tenant_templates",)
    TENANT_TEMPLATES_FIELD_NUMBER: _ClassVar[int]
    tenant_templates: _containers.RepeatedCompositeFieldContainer[_tenant_template_models_pb2.TenantTemplateModel]
    def __init__(self, tenant_templates: _Optional[_Iterable[_Union[_tenant_template_models_pb2.TenantTemplateModel, _Mapping]]] = ...) -> None: ...

class ListTenantGroupParams(_message.Message):
    __slots__ = ("enterprise_id", "filter")
    class Filter(_message.Message):
        __slots__ = ("tenant_id",)
        TENANT_ID_FIELD_NUMBER: _ClassVar[int]
        tenant_id: _containers.RepeatedScalarFieldContainer[int]
        def __init__(self, tenant_id: _Optional[_Iterable[int]] = ...) -> None: ...
    ENTERPRISE_ID_FIELD_NUMBER: _ClassVar[int]
    FILTER_FIELD_NUMBER: _ClassVar[int]
    enterprise_id: int
    filter: ListTenantGroupParams.Filter
    def __init__(self, enterprise_id: _Optional[int] = ..., filter: _Optional[_Union[ListTenantGroupParams.Filter, _Mapping]] = ...) -> None: ...

class ListTenantGroupResult(_message.Message):
    __slots__ = ("tenant_groups",)
    TENANT_GROUPS_FIELD_NUMBER: _ClassVar[int]
    tenant_groups: _containers.RepeatedCompositeFieldContainer[_tenant_group_models_pb2.TenantGroupModel]
    def __init__(self, tenant_groups: _Optional[_Iterable[_Union[_tenant_group_models_pb2.TenantGroupModel, _Mapping]]] = ...) -> None: ...

class GetOptionParams(_message.Message):
    __slots__ = ()
    def __init__(self) -> None: ...

class GetOptionResult(_message.Message):
    __slots__ = ("option",)
    OPTION_FIELD_NUMBER: _ClassVar[int]
    option: _option_models_pb2.OptionModel
    def __init__(self, option: _Optional[_Union[_option_models_pb2.OptionModel, _Mapping]] = ...) -> None: ...

class SyncFranchiseeParams(_message.Message):
    __slots__ = ()
    def __init__(self) -> None: ...

class SyncFranchiseeResult(_message.Message):
    __slots__ = ()
    def __init__(self) -> None: ...

class GetEnterprisePreferenceSettingParams(_message.Message):
    __slots__ = ()
    def __init__(self) -> None: ...

class GetEnterprisePreferenceSettingResult(_message.Message):
    __slots__ = ("tenant_text_mappings",)
    class TenantTextMapping(_message.Message):
        __slots__ = ("type", "value")
        TYPE_FIELD_NUMBER: _ClassVar[int]
        VALUE_FIELD_NUMBER: _ClassVar[int]
        type: _enterprise_enums_pb2.TenantTextType
        value: str
        def __init__(self, type: _Optional[_Union[_enterprise_enums_pb2.TenantTextType, str]] = ..., value: _Optional[str] = ...) -> None: ...
    TENANT_TEXT_MAPPINGS_FIELD_NUMBER: _ClassVar[int]
    tenant_text_mappings: _containers.RepeatedCompositeFieldContainer[GetEnterprisePreferenceSettingResult.TenantTextMapping]
    def __init__(self, tenant_text_mappings: _Optional[_Iterable[_Union[GetEnterprisePreferenceSettingResult.TenantTextMapping, _Mapping]]] = ...) -> None: ...
