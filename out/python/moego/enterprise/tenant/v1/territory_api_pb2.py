# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: moego/enterprise/tenant/v1/territory_api.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'moego/enterprise/tenant/v1/territory_api.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from moego.models.enterprise.v1 import territory_models_pb2 as moego_dot_models_dot_enterprise_dot_v1_dot_territory__models__pb2
from validate import validate_pb2 as validate_dot_validate__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n.moego/enterprise/tenant/v1/territory_api.proto\x12\x1amoego.enterprise.tenant.v1\x1a\x31moego/models/enterprise/v1/territory_models.proto\x1a\x17validate/validate.proto\"\xca\x01\n\x15\x43reateTerritoryParams\x12,\n\renterprise_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\x0c\x65nterpriseId\x12\x32\n\tzip_codes\x18\x02 \x03(\tB\x15\xfa\x42\x12\x92\x01\x0f\x08\x01\x10\xe8\x07\x18\x01\"\x06r\x04\x10\x01\x18\x64R\x08zipCodes\x12O\n\x04type\x18\x03 \x01(\x0e\x32/.moego.models.enterprise.v1.TerritoryModel.TypeB\n\xfa\x42\x07\x82\x01\x04\x10\x01 \x00R\x04type\"a\n\x15\x43reateTerritoryResult\x12H\n\tterritory\x18\x01 \x01(\x0b\x32*.moego.models.enterprise.v1.TerritoryModelR\tterritory\"\xb7\x01\n\x15UpdateTerritoryParams\x12\x17\n\x02id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\x02id\x12\x32\n\tzip_codes\x18\x02 \x03(\tB\x15\xfa\x42\x12\x92\x01\x0f\x08\x01\x10\xe8\x07\x18\x01\"\x06r\x04\x10\x01\x18\x64R\x08zipCodes\x12H\n\x04type\x18\x03 \x01(\x0e\x32/.moego.models.enterprise.v1.TerritoryModel.TypeH\x00R\x04type\x88\x01\x01\x42\x07\n\x05_type\"a\n\x15UpdateTerritoryResult\x12H\n\tterritory\x18\x01 \x01(\x0b\x32*.moego.models.enterprise.v1.TerritoryModelR\tterritory\"\'\n\x15\x44\x65leteTerritoryParams\x12\x0e\n\x02id\x18\x01 \x01(\x03R\x02id\"\x17\n\x15\x44\x65leteTerritoryResult\"$\n\x12GetTerritoryParams\x12\x0e\n\x02id\x18\x01 \x01(\x03R\x02id\"^\n\x12GetTerritoryResult\x12H\n\tterritory\x18\x01 \x01(\x0b\x32*.moego.models.enterprise.v1.TerritoryModelR\tterritory\"\xbf\x02\n\x15ListTerritoriesParams\x12U\n\x06\x66ilter\x18\x01 \x01(\x0b\x32\x38.moego.enterprise.tenant.v1.ListTerritoriesParams.FilterH\x00R\x06\x66ilter\x88\x01\x01\x1a\xc3\x01\n\x06\x46ilter\x12#\n\rterritory_ids\x18\x01 \x03(\x03R\x0cterritoryIds\x12\x45\n\x05types\x18\x02 \x03(\x0e\x32/.moego.models.enterprise.v1.TerritoryModel.TypeR\x05types\x12M\n\x08statuses\x18\x03 \x03(\x0e\x32\x31.moego.models.enterprise.v1.TerritoryModel.StatusR\x08statusesB\t\n\x07_filter\"e\n\x15ListTerritoriesResult\x12L\n\x0bterritories\x18\x01 \x03(\x0b\x32*.moego.models.enterprise.v1.TerritoryModelR\x0bterritories2\xe6\x04\n\x10TerritoryService\x12w\n\x0f\x43reateTerritory\x12\x31.moego.enterprise.tenant.v1.CreateTerritoryParams\x1a\x31.moego.enterprise.tenant.v1.CreateTerritoryResult\x12w\n\x0fUpdateTerritory\x12\x31.moego.enterprise.tenant.v1.UpdateTerritoryParams\x1a\x31.moego.enterprise.tenant.v1.UpdateTerritoryResult\x12w\n\x0f\x44\x65leteTerritory\x12\x31.moego.enterprise.tenant.v1.DeleteTerritoryParams\x1a\x31.moego.enterprise.tenant.v1.DeleteTerritoryResult\x12n\n\x0cGetTerritory\x12..moego.enterprise.tenant.v1.GetTerritoryParams\x1a..moego.enterprise.tenant.v1.GetTerritoryResult\x12w\n\x0fListTerritories\x12\x31.moego.enterprise.tenant.v1.ListTerritoriesParams\x1a\x31.moego.enterprise.tenant.v1.ListTerritoriesResultB\x83\x01\n\"com.moego.idl.enterprise.tenant.v1P\x01Z[github.com/MoeGolibrary/moego-api-definitions/out/go/moego/enterprise/tenant/v1;tenantapipbb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'moego.enterprise.tenant.v1.territory_api_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n\"com.moego.idl.enterprise.tenant.v1P\001Z[github.com/MoeGolibrary/moego-api-definitions/out/go/moego/enterprise/tenant/v1;tenantapipb'
  _globals['_CREATETERRITORYPARAMS'].fields_by_name['enterprise_id']._loaded_options = None
  _globals['_CREATETERRITORYPARAMS'].fields_by_name['enterprise_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_CREATETERRITORYPARAMS'].fields_by_name['zip_codes']._loaded_options = None
  _globals['_CREATETERRITORYPARAMS'].fields_by_name['zip_codes']._serialized_options = b'\372B\022\222\001\017\010\001\020\350\007\030\001\"\006r\004\020\001\030d'
  _globals['_CREATETERRITORYPARAMS'].fields_by_name['type']._loaded_options = None
  _globals['_CREATETERRITORYPARAMS'].fields_by_name['type']._serialized_options = b'\372B\007\202\001\004\020\001 \000'
  _globals['_UPDATETERRITORYPARAMS'].fields_by_name['id']._loaded_options = None
  _globals['_UPDATETERRITORYPARAMS'].fields_by_name['id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_UPDATETERRITORYPARAMS'].fields_by_name['zip_codes']._loaded_options = None
  _globals['_UPDATETERRITORYPARAMS'].fields_by_name['zip_codes']._serialized_options = b'\372B\022\222\001\017\010\001\020\350\007\030\001\"\006r\004\020\001\030d'
  _globals['_CREATETERRITORYPARAMS']._serialized_start=155
  _globals['_CREATETERRITORYPARAMS']._serialized_end=357
  _globals['_CREATETERRITORYRESULT']._serialized_start=359
  _globals['_CREATETERRITORYRESULT']._serialized_end=456
  _globals['_UPDATETERRITORYPARAMS']._serialized_start=459
  _globals['_UPDATETERRITORYPARAMS']._serialized_end=642
  _globals['_UPDATETERRITORYRESULT']._serialized_start=644
  _globals['_UPDATETERRITORYRESULT']._serialized_end=741
  _globals['_DELETETERRITORYPARAMS']._serialized_start=743
  _globals['_DELETETERRITORYPARAMS']._serialized_end=782
  _globals['_DELETETERRITORYRESULT']._serialized_start=784
  _globals['_DELETETERRITORYRESULT']._serialized_end=807
  _globals['_GETTERRITORYPARAMS']._serialized_start=809
  _globals['_GETTERRITORYPARAMS']._serialized_end=845
  _globals['_GETTERRITORYRESULT']._serialized_start=847
  _globals['_GETTERRITORYRESULT']._serialized_end=941
  _globals['_LISTTERRITORIESPARAMS']._serialized_start=944
  _globals['_LISTTERRITORIESPARAMS']._serialized_end=1263
  _globals['_LISTTERRITORIESPARAMS_FILTER']._serialized_start=1057
  _globals['_LISTTERRITORIESPARAMS_FILTER']._serialized_end=1252
  _globals['_LISTTERRITORIESRESULT']._serialized_start=1265
  _globals['_LISTTERRITORIESRESULT']._serialized_end=1366
  _globals['_TERRITORYSERVICE']._serialized_start=1369
  _globals['_TERRITORYSERVICE']._serialized_end=1983
# @@protoc_insertion_point(module_scope)
