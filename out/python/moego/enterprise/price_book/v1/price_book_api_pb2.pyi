from google.protobuf import duration_pb2 as _duration_pb2
from google.protobuf import timestamp_pb2 as _timestamp_pb2
from google.type import money_pb2 as _money_pb2
from moego.models.enterprise.v1 import price_book_models_pb2 as _price_book_models_pb2
from moego.models.enterprise.v1 import tenant_models_pb2 as _tenant_models_pb2
from moego.models.offering.v1 import service_enum_pb2 as _service_enum_pb2
from moego.service.enterprise.v1 import price_book_service_pb2 as _price_book_service_pb2
from moego.utils.v2 import pagination_messages_pb2 as _pagination_messages_pb2
from validate import validate_pb2 as _validate_pb2
from google.protobuf.internal import containers as _containers
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from collections.abc import Iterable as _Iterable, Mapping as _Mapping
from typing import ClassVar as _ClassVar, Optional as _Optional, Union as _Union

DESCRIPTOR: _descriptor.FileDescriptor

class ListPriceBooksParams(_message.Message):
    __slots__ = ()
    def __init__(self) -> None: ...

class ListPriceBooksResult(_message.Message):
    __slots__ = ("price_books",)
    PRICE_BOOKS_FIELD_NUMBER: _ClassVar[int]
    price_books: _containers.RepeatedCompositeFieldContainer[_price_book_models_pb2.PriceBook]
    def __init__(self, price_books: _Optional[_Iterable[_Union[_price_book_models_pb2.PriceBook, _Mapping]]] = ...) -> None: ...

class SaveServiceCategoriesParams(_message.Message):
    __slots__ = ("categories", "service_type", "service_item_type")
    CATEGORIES_FIELD_NUMBER: _ClassVar[int]
    SERVICE_TYPE_FIELD_NUMBER: _ClassVar[int]
    SERVICE_ITEM_TYPE_FIELD_NUMBER: _ClassVar[int]
    categories: _containers.RepeatedCompositeFieldContainer[_price_book_models_pb2.ServiceCategory]
    service_type: _service_enum_pb2.ServiceType
    service_item_type: _service_enum_pb2.ServiceItemType
    def __init__(self, categories: _Optional[_Iterable[_Union[_price_book_models_pb2.ServiceCategory, _Mapping]]] = ..., service_type: _Optional[_Union[_service_enum_pb2.ServiceType, str]] = ..., service_item_type: _Optional[_Union[_service_enum_pb2.ServiceItemType, str]] = ...) -> None: ...

class SaveServiceCategoriesResult(_message.Message):
    __slots__ = ()
    def __init__(self) -> None: ...

class ListServiceCategoriesParams(_message.Message):
    __slots__ = ("pagination", "filter")
    PAGINATION_FIELD_NUMBER: _ClassVar[int]
    FILTER_FIELD_NUMBER: _ClassVar[int]
    pagination: _pagination_messages_pb2.PaginationRequest
    filter: _price_book_service_pb2.ListServiceCategoriesRequest.Filter
    def __init__(self, pagination: _Optional[_Union[_pagination_messages_pb2.PaginationRequest, _Mapping]] = ..., filter: _Optional[_Union[_price_book_service_pb2.ListServiceCategoriesRequest.Filter, _Mapping]] = ...) -> None: ...

class ListServiceCategoriesResult(_message.Message):
    __slots__ = ("service_categories",)
    SERVICE_CATEGORIES_FIELD_NUMBER: _ClassVar[int]
    service_categories: _containers.RepeatedCompositeFieldContainer[_price_book_models_pb2.ServiceCategory]
    def __init__(self, service_categories: _Optional[_Iterable[_Union[_price_book_models_pb2.ServiceCategory, _Mapping]]] = ...) -> None: ...

class ListPetBreedsParams(_message.Message):
    __slots__ = ()
    def __init__(self) -> None: ...

class ListPetBreedsResult(_message.Message):
    __slots__ = ("pet_breeds",)
    PET_BREEDS_FIELD_NUMBER: _ClassVar[int]
    pet_breeds: _containers.RepeatedCompositeFieldContainer[_price_book_models_pb2.PetBreed]
    def __init__(self, pet_breeds: _Optional[_Iterable[_Union[_price_book_models_pb2.PetBreed, _Mapping]]] = ...) -> None: ...

class ListPetTypesParams(_message.Message):
    __slots__ = ()
    def __init__(self) -> None: ...

class ListPetTypesResult(_message.Message):
    __slots__ = ("pet_types",)
    PET_TYPES_FIELD_NUMBER: _ClassVar[int]
    pet_types: _containers.RepeatedCompositeFieldContainer[_price_book_models_pb2.PetType]
    def __init__(self, pet_types: _Optional[_Iterable[_Union[_price_book_models_pb2.PetType, _Mapping]]] = ...) -> None: ...

class CreateServiceParams(_message.Message):
    __slots__ = ("price_book", "name", "service_item_type", "category", "description", "inactive", "color", "sort", "price", "service_price_unit", "tax_rate", "duration", "max_duration", "limitation", "service_type", "images")
    PRICE_BOOK_FIELD_NUMBER: _ClassVar[int]
    NAME_FIELD_NUMBER: _ClassVar[int]
    SERVICE_ITEM_TYPE_FIELD_NUMBER: _ClassVar[int]
    CATEGORY_FIELD_NUMBER: _ClassVar[int]
    DESCRIPTION_FIELD_NUMBER: _ClassVar[int]
    INACTIVE_FIELD_NUMBER: _ClassVar[int]
    COLOR_FIELD_NUMBER: _ClassVar[int]
    SORT_FIELD_NUMBER: _ClassVar[int]
    PRICE_FIELD_NUMBER: _ClassVar[int]
    SERVICE_PRICE_UNIT_FIELD_NUMBER: _ClassVar[int]
    TAX_RATE_FIELD_NUMBER: _ClassVar[int]
    DURATION_FIELD_NUMBER: _ClassVar[int]
    MAX_DURATION_FIELD_NUMBER: _ClassVar[int]
    LIMITATION_FIELD_NUMBER: _ClassVar[int]
    SERVICE_TYPE_FIELD_NUMBER: _ClassVar[int]
    IMAGES_FIELD_NUMBER: _ClassVar[int]
    price_book: _price_book_models_pb2.PriceBook
    name: str
    service_item_type: _service_enum_pb2.ServiceItemType
    category: _price_book_models_pb2.ServiceCategory
    description: str
    inactive: bool
    color: str
    sort: int
    price: _money_pb2.Money
    service_price_unit: _service_enum_pb2.ServicePriceUnit
    tax_rate: int
    duration: _duration_pb2.Duration
    max_duration: _duration_pb2.Duration
    limitation: _price_book_models_pb2.Service.Limitation
    service_type: _service_enum_pb2.ServiceType
    images: _containers.RepeatedScalarFieldContainer[str]
    def __init__(self, price_book: _Optional[_Union[_price_book_models_pb2.PriceBook, _Mapping]] = ..., name: _Optional[str] = ..., service_item_type: _Optional[_Union[_service_enum_pb2.ServiceItemType, str]] = ..., category: _Optional[_Union[_price_book_models_pb2.ServiceCategory, _Mapping]] = ..., description: _Optional[str] = ..., inactive: bool = ..., color: _Optional[str] = ..., sort: _Optional[int] = ..., price: _Optional[_Union[_money_pb2.Money, _Mapping]] = ..., service_price_unit: _Optional[_Union[_service_enum_pb2.ServicePriceUnit, str]] = ..., tax_rate: _Optional[int] = ..., duration: _Optional[_Union[datetime.timedelta, _duration_pb2.Duration, _Mapping]] = ..., max_duration: _Optional[_Union[datetime.timedelta, _duration_pb2.Duration, _Mapping]] = ..., limitation: _Optional[_Union[_price_book_models_pb2.Service.Limitation, _Mapping]] = ..., service_type: _Optional[_Union[_service_enum_pb2.ServiceType, str]] = ..., images: _Optional[_Iterable[str]] = ...) -> None: ...

class CreateServiceResult(_message.Message):
    __slots__ = ("service",)
    SERVICE_FIELD_NUMBER: _ClassVar[int]
    service: _price_book_models_pb2.Service
    def __init__(self, service: _Optional[_Union[_price_book_models_pb2.Service, _Mapping]] = ...) -> None: ...

class GetServiceParams(_message.Message):
    __slots__ = ("id",)
    ID_FIELD_NUMBER: _ClassVar[int]
    id: int
    def __init__(self, id: _Optional[int] = ...) -> None: ...

class GetServiceResult(_message.Message):
    __slots__ = ("service",)
    SERVICE_FIELD_NUMBER: _ClassVar[int]
    service: _price_book_models_pb2.Service
    def __init__(self, service: _Optional[_Union[_price_book_models_pb2.Service, _Mapping]] = ...) -> None: ...

class ListServicesParams(_message.Message):
    __slots__ = ("pagination", "filter")
    PAGINATION_FIELD_NUMBER: _ClassVar[int]
    FILTER_FIELD_NUMBER: _ClassVar[int]
    pagination: _pagination_messages_pb2.PaginationRequest
    filter: _price_book_service_pb2.ListServicesRequest.Filter
    def __init__(self, pagination: _Optional[_Union[_pagination_messages_pb2.PaginationRequest, _Mapping]] = ..., filter: _Optional[_Union[_price_book_service_pb2.ListServicesRequest.Filter, _Mapping]] = ...) -> None: ...

class ListServicesResult(_message.Message):
    __slots__ = ("pagination", "services")
    PAGINATION_FIELD_NUMBER: _ClassVar[int]
    SERVICES_FIELD_NUMBER: _ClassVar[int]
    pagination: _pagination_messages_pb2.PaginationResponse
    services: _containers.RepeatedCompositeFieldContainer[_price_book_models_pb2.Service]
    def __init__(self, pagination: _Optional[_Union[_pagination_messages_pb2.PaginationResponse, _Mapping]] = ..., services: _Optional[_Iterable[_Union[_price_book_models_pb2.Service, _Mapping]]] = ...) -> None: ...

class UpdateServiceParams(_message.Message):
    __slots__ = ("id", "name", "service_category", "description", "inactive", "color", "price", "service_price_unit", "tax_rate", "duration", "max_duration", "limitation", "images")
    ID_FIELD_NUMBER: _ClassVar[int]
    NAME_FIELD_NUMBER: _ClassVar[int]
    SERVICE_CATEGORY_FIELD_NUMBER: _ClassVar[int]
    DESCRIPTION_FIELD_NUMBER: _ClassVar[int]
    INACTIVE_FIELD_NUMBER: _ClassVar[int]
    COLOR_FIELD_NUMBER: _ClassVar[int]
    PRICE_FIELD_NUMBER: _ClassVar[int]
    SERVICE_PRICE_UNIT_FIELD_NUMBER: _ClassVar[int]
    TAX_RATE_FIELD_NUMBER: _ClassVar[int]
    DURATION_FIELD_NUMBER: _ClassVar[int]
    MAX_DURATION_FIELD_NUMBER: _ClassVar[int]
    LIMITATION_FIELD_NUMBER: _ClassVar[int]
    IMAGES_FIELD_NUMBER: _ClassVar[int]
    id: int
    name: str
    service_category: _price_book_models_pb2.ServiceCategory
    description: str
    inactive: bool
    color: str
    price: _money_pb2.Money
    service_price_unit: _service_enum_pb2.ServicePriceUnit
    tax_rate: int
    duration: _duration_pb2.Duration
    max_duration: _duration_pb2.Duration
    limitation: _price_book_models_pb2.Service.Limitation
    images: _containers.RepeatedScalarFieldContainer[str]
    def __init__(self, id: _Optional[int] = ..., name: _Optional[str] = ..., service_category: _Optional[_Union[_price_book_models_pb2.ServiceCategory, _Mapping]] = ..., description: _Optional[str] = ..., inactive: bool = ..., color: _Optional[str] = ..., price: _Optional[_Union[_money_pb2.Money, _Mapping]] = ..., service_price_unit: _Optional[_Union[_service_enum_pb2.ServicePriceUnit, str]] = ..., tax_rate: _Optional[int] = ..., duration: _Optional[_Union[datetime.timedelta, _duration_pb2.Duration, _Mapping]] = ..., max_duration: _Optional[_Union[datetime.timedelta, _duration_pb2.Duration, _Mapping]] = ..., limitation: _Optional[_Union[_price_book_models_pb2.Service.Limitation, _Mapping]] = ..., images: _Optional[_Iterable[str]] = ...) -> None: ...

class UpdateServiceResult(_message.Message):
    __slots__ = ("service",)
    SERVICE_FIELD_NUMBER: _ClassVar[int]
    service: _price_book_models_pb2.Service
    def __init__(self, service: _Optional[_Union[_price_book_models_pb2.Service, _Mapping]] = ...) -> None: ...

class DeleteServiceParams(_message.Message):
    __slots__ = ("id",)
    ID_FIELD_NUMBER: _ClassVar[int]
    id: int
    def __init__(self, id: _Optional[int] = ...) -> None: ...

class DeleteServiceResult(_message.Message):
    __slots__ = ()
    def __init__(self) -> None: ...

class SortServicesParams(_message.Message):
    __slots__ = ("service_category_sorts",)
    SERVICE_CATEGORY_SORTS_FIELD_NUMBER: _ClassVar[int]
    service_category_sorts: _containers.RepeatedCompositeFieldContainer[_price_book_service_pb2.SortServicesRequest.ServiceCategorySort]
    def __init__(self, service_category_sorts: _Optional[_Iterable[_Union[_price_book_service_pb2.SortServicesRequest.ServiceCategorySort, _Mapping]]] = ...) -> None: ...

class SortServicesResult(_message.Message):
    __slots__ = ()
    def __init__(self) -> None: ...

class ListServiceChangeHistoriesParams(_message.Message):
    __slots__ = ("pagination", "filter", "order_by")
    PAGINATION_FIELD_NUMBER: _ClassVar[int]
    FILTER_FIELD_NUMBER: _ClassVar[int]
    ORDER_BY_FIELD_NUMBER: _ClassVar[int]
    pagination: _pagination_messages_pb2.PaginationRequest
    filter: _price_book_service_pb2.ListServiceChangeHistoriesRequest.Filter
    order_by: _price_book_service_pb2.ListServiceChangeHistoriesRequest.OrderBy
    def __init__(self, pagination: _Optional[_Union[_pagination_messages_pb2.PaginationRequest, _Mapping]] = ..., filter: _Optional[_Union[_price_book_service_pb2.ListServiceChangeHistoriesRequest.Filter, _Mapping]] = ..., order_by: _Optional[_Union[_price_book_service_pb2.ListServiceChangeHistoriesRequest.OrderBy, _Mapping]] = ...) -> None: ...

class ListServiceChangeHistoriesResult(_message.Message):
    __slots__ = ("pagination", "service_change_histories")
    PAGINATION_FIELD_NUMBER: _ClassVar[int]
    SERVICE_CHANGE_HISTORIES_FIELD_NUMBER: _ClassVar[int]
    pagination: _pagination_messages_pb2.PaginationResponse
    service_change_histories: _containers.RepeatedCompositeFieldContainer[_price_book_models_pb2.ServiceChangeHistory]
    def __init__(self, pagination: _Optional[_Union[_pagination_messages_pb2.PaginationResponse, _Mapping]] = ..., service_change_histories: _Optional[_Iterable[_Union[_price_book_models_pb2.ServiceChangeHistory, _Mapping]]] = ...) -> None: ...

class ListServiceChangesParams(_message.Message):
    __slots__ = ("pagination", "filter")
    PAGINATION_FIELD_NUMBER: _ClassVar[int]
    FILTER_FIELD_NUMBER: _ClassVar[int]
    pagination: _pagination_messages_pb2.PaginationRequest
    filter: _price_book_service_pb2.ListServiceChangesRequest.Filter
    def __init__(self, pagination: _Optional[_Union[_pagination_messages_pb2.PaginationRequest, _Mapping]] = ..., filter: _Optional[_Union[_price_book_service_pb2.ListServiceChangesRequest.Filter, _Mapping]] = ...) -> None: ...

class ListServiceChangesResult(_message.Message):
    __slots__ = ("pagination", "service_changes")
    PAGINATION_FIELD_NUMBER: _ClassVar[int]
    SERVICE_CHANGES_FIELD_NUMBER: _ClassVar[int]
    pagination: _pagination_messages_pb2.PaginationResponse
    service_changes: _containers.RepeatedCompositeFieldContainer[_price_book_models_pb2.ServiceChange]
    def __init__(self, pagination: _Optional[_Union[_pagination_messages_pb2.PaginationResponse, _Mapping]] = ..., service_changes: _Optional[_Iterable[_Union[_price_book_models_pb2.ServiceChange, _Mapping]]] = ...) -> None: ...

class PushServiceChangesParams(_message.Message):
    __slots__ = ("service_ids", "targets", "effective_date", "apply_to_booked_services")
    SERVICE_IDS_FIELD_NUMBER: _ClassVar[int]
    TARGETS_FIELD_NUMBER: _ClassVar[int]
    EFFECTIVE_DATE_FIELD_NUMBER: _ClassVar[int]
    APPLY_TO_BOOKED_SERVICES_FIELD_NUMBER: _ClassVar[int]
    service_ids: _containers.RepeatedScalarFieldContainer[int]
    targets: _containers.RepeatedCompositeFieldContainer[_tenant_models_pb2.TenantObject]
    effective_date: _timestamp_pb2.Timestamp
    apply_to_booked_services: bool
    def __init__(self, service_ids: _Optional[_Iterable[int]] = ..., targets: _Optional[_Iterable[_Union[_tenant_models_pb2.TenantObject, _Mapping]]] = ..., effective_date: _Optional[_Union[datetime.datetime, _timestamp_pb2.Timestamp, _Mapping]] = ..., apply_to_booked_services: bool = ...) -> None: ...

class PushServiceChangesResult(_message.Message):
    __slots__ = ("success_company_ids", "failed_company_ids")
    SUCCESS_COMPANY_IDS_FIELD_NUMBER: _ClassVar[int]
    FAILED_COMPANY_IDS_FIELD_NUMBER: _ClassVar[int]
    success_company_ids: _containers.RepeatedScalarFieldContainer[int]
    failed_company_ids: _containers.RepeatedScalarFieldContainer[int]
    def __init__(self, success_company_ids: _Optional[_Iterable[int]] = ..., failed_company_ids: _Optional[_Iterable[int]] = ...) -> None: ...
