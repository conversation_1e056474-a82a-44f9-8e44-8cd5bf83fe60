from google.protobuf import timestamp_pb2 as _timestamp_pb2
from moego.models.notification.v1 import notification_defs_pb2 as _notification_defs_pb2
from moego.models.notification.v1 import notification_enums_pb2 as _notification_enums_pb2
from moego.models.notification.v1 import notification_extra_defs_pb2 as _notification_extra_defs_pb2
from moego.models.notification.v1 import notification_models_pb2 as _notification_models_pb2
from moego.utils.v2 import pagination_messages_pb2 as _pagination_messages_pb2
from validate import validate_pb2 as _validate_pb2
from google.protobuf.internal import containers as _containers
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from collections.abc import Iterable as _Iterable, Mapping as _Mapping
from typing import ClassVar as _ClassVar, Optional as _Optional, Union as _Union

DESCRIPTOR: _descriptor.FileDescriptor

class CreateInboxNotificationRequest(_message.Message):
    __slots__ = ("source", "sender_id", "receiver_id", "title", "content", "method", "type", "extra", "app_push")
    SOURCE_FIELD_NUMBER: _ClassVar[int]
    SENDER_ID_FIELD_NUMBER: _ClassVar[int]
    RECEIVER_ID_FIELD_NUMBER: _ClassVar[int]
    TITLE_FIELD_NUMBER: _ClassVar[int]
    CONTENT_FIELD_NUMBER: _ClassVar[int]
    METHOD_FIELD_NUMBER: _ClassVar[int]
    TYPE_FIELD_NUMBER: _ClassVar[int]
    EXTRA_FIELD_NUMBER: _ClassVar[int]
    APP_PUSH_FIELD_NUMBER: _ClassVar[int]
    source: _notification_enums_pb2.NotificationSource
    sender_id: int
    receiver_id: int
    title: str
    content: str
    method: _notification_enums_pb2.NotificationMethod
    type: _notification_enums_pb2.NotificationType
    extra: _notification_extra_defs_pb2.NotificationExtraDef
    app_push: _notification_defs_pb2.AppPushDef
    def __init__(self, source: _Optional[_Union[_notification_enums_pb2.NotificationSource, str]] = ..., sender_id: _Optional[int] = ..., receiver_id: _Optional[int] = ..., title: _Optional[str] = ..., content: _Optional[str] = ..., method: _Optional[_Union[_notification_enums_pb2.NotificationMethod, str]] = ..., type: _Optional[_Union[_notification_enums_pb2.NotificationType, str]] = ..., extra: _Optional[_Union[_notification_extra_defs_pb2.NotificationExtraDef, _Mapping]] = ..., app_push: _Optional[_Union[_notification_defs_pb2.AppPushDef, _Mapping]] = ...) -> None: ...

class CreateInboxNotificationResponse(_message.Message):
    __slots__ = ("notification_id",)
    NOTIFICATION_ID_FIELD_NUMBER: _ClassVar[int]
    notification_id: int
    def __init__(self, notification_id: _Optional[int] = ...) -> None: ...

class ReadInboxNotificationRequest(_message.Message):
    __slots__ = ("notification_id",)
    NOTIFICATION_ID_FIELD_NUMBER: _ClassVar[int]
    notification_id: int
    def __init__(self, notification_id: _Optional[int] = ...) -> None: ...

class ReadInboxNotificationResponse(_message.Message):
    __slots__ = ("read_time",)
    READ_TIME_FIELD_NUMBER: _ClassVar[int]
    read_time: _timestamp_pb2.Timestamp
    def __init__(self, read_time: _Optional[_Union[datetime.datetime, _timestamp_pb2.Timestamp, _Mapping]] = ...) -> None: ...

class DeleteInboxNotificationRequest(_message.Message):
    __slots__ = ("notification_id",)
    NOTIFICATION_ID_FIELD_NUMBER: _ClassVar[int]
    notification_id: int
    def __init__(self, notification_id: _Optional[int] = ...) -> None: ...

class DeleteInboxNotificationResponse(_message.Message):
    __slots__ = ("deleted_time",)
    DELETED_TIME_FIELD_NUMBER: _ClassVar[int]
    deleted_time: _timestamp_pb2.Timestamp
    def __init__(self, deleted_time: _Optional[_Union[datetime.datetime, _timestamp_pb2.Timestamp, _Mapping]] = ...) -> None: ...

class GetNotificationListRequest(_message.Message):
    __slots__ = ("types", "pagination", "sorts", "sources", "methods", "receiver_id")
    TYPES_FIELD_NUMBER: _ClassVar[int]
    PAGINATION_FIELD_NUMBER: _ClassVar[int]
    SORTS_FIELD_NUMBER: _ClassVar[int]
    SOURCES_FIELD_NUMBER: _ClassVar[int]
    METHODS_FIELD_NUMBER: _ClassVar[int]
    RECEIVER_ID_FIELD_NUMBER: _ClassVar[int]
    types: _containers.RepeatedScalarFieldContainer[_notification_enums_pb2.NotificationType]
    pagination: _pagination_messages_pb2.PaginationRequest
    sorts: _containers.RepeatedCompositeFieldContainer[_notification_defs_pb2.NotificationSortDef]
    sources: _containers.RepeatedScalarFieldContainer[_notification_enums_pb2.NotificationSource]
    methods: _containers.RepeatedScalarFieldContainer[_notification_enums_pb2.NotificationMethod]
    receiver_id: int
    def __init__(self, types: _Optional[_Iterable[_Union[_notification_enums_pb2.NotificationType, str]]] = ..., pagination: _Optional[_Union[_pagination_messages_pb2.PaginationRequest, _Mapping]] = ..., sorts: _Optional[_Iterable[_Union[_notification_defs_pb2.NotificationSortDef, _Mapping]]] = ..., sources: _Optional[_Iterable[_Union[_notification_enums_pb2.NotificationSource, str]]] = ..., methods: _Optional[_Iterable[_Union[_notification_enums_pb2.NotificationMethod, str]]] = ..., receiver_id: _Optional[int] = ...) -> None: ...

class GetNotificationListResponse(_message.Message):
    __slots__ = ("notifications", "pagination")
    NOTIFICATIONS_FIELD_NUMBER: _ClassVar[int]
    PAGINATION_FIELD_NUMBER: _ClassVar[int]
    notifications: _containers.RepeatedCompositeFieldContainer[_notification_models_pb2.NotificationModel]
    pagination: _pagination_messages_pb2.PaginationResponse
    def __init__(self, notifications: _Optional[_Iterable[_Union[_notification_models_pb2.NotificationModel, _Mapping]]] = ..., pagination: _Optional[_Union[_pagination_messages_pb2.PaginationResponse, _Mapping]] = ...) -> None: ...
