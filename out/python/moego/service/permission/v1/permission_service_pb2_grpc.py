# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

from moego.service.permission.v1 import permission_service_pb2 as moego_dot_service_dot_permission_dot_v1_dot_permission__service__pb2


class PermissionServiceStub(object):
    """permission service
    """

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.GetRoleList = channel.unary_unary(
                '/moego.service.permission.v1.PermissionService/GetRoleList',
                request_serializer=moego_dot_service_dot_permission_dot_v1_dot_permission__service__pb2.GetRoleListRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_permission_dot_v1_dot_permission__service__pb2.GetRoleListResponse.FromString,
                _registered_method=True)
        self.GetRoleDetail = channel.unary_unary(
                '/moego.service.permission.v1.PermissionService/GetRoleDetail',
                request_serializer=moego_dot_service_dot_permission_dot_v1_dot_permission__service__pb2.GetRoleDetailRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_permission_dot_v1_dot_permission__service__pb2.GetRoleDetailResponse.FromString,
                _registered_method=True)
        self.ListRoleDetails = channel.unary_unary(
                '/moego.service.permission.v1.PermissionService/ListRoleDetails',
                request_serializer=moego_dot_service_dot_permission_dot_v1_dot_permission__service__pb2.ListRoleDetailsRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_permission_dot_v1_dot_permission__service__pb2.ListRoleDetailsResponse.FromString,
                _registered_method=True)
        self.CreateRole = channel.unary_unary(
                '/moego.service.permission.v1.PermissionService/CreateRole',
                request_serializer=moego_dot_service_dot_permission_dot_v1_dot_permission__service__pb2.CreateRoleRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_permission_dot_v1_dot_permission__service__pb2.CreateRoleResponse.FromString,
                _registered_method=True)
        self.UpdateRole = channel.unary_unary(
                '/moego.service.permission.v1.PermissionService/UpdateRole',
                request_serializer=moego_dot_service_dot_permission_dot_v1_dot_permission__service__pb2.UpdateRoleRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_permission_dot_v1_dot_permission__service__pb2.UpdateRoleResponse.FromString,
                _registered_method=True)
        self.DeleteRole = channel.unary_unary(
                '/moego.service.permission.v1.PermissionService/DeleteRole',
                request_serializer=moego_dot_service_dot_permission_dot_v1_dot_permission__service__pb2.DeleteRoleRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_permission_dot_v1_dot_permission__service__pb2.DeleteRoleResponse.FromString,
                _registered_method=True)
        self.DuplicateRole = channel.unary_unary(
                '/moego.service.permission.v1.PermissionService/DuplicateRole',
                request_serializer=moego_dot_service_dot_permission_dot_v1_dot_permission__service__pb2.DuplicateRoleRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_permission_dot_v1_dot_permission__service__pb2.DuplicateRoleResponse.FromString,
                _registered_method=True)
        self.EditPermissions = channel.unary_unary(
                '/moego.service.permission.v1.PermissionService/EditPermissions',
                request_serializer=moego_dot_service_dot_permission_dot_v1_dot_permission__service__pb2.EditPermissionsRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_permission_dot_v1_dot_permission__service__pb2.EditPermissionsResponse.FromString,
                _registered_method=True)
        self.CheckPermission = channel.unary_unary(
                '/moego.service.permission.v1.PermissionService/CheckPermission',
                request_serializer=moego_dot_service_dot_permission_dot_v1_dot_permission__service__pb2.CheckPermissionRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_permission_dot_v1_dot_permission__service__pb2.CheckPermissionResponse.FromString,
                _registered_method=True)
        self.InitRole = channel.unary_unary(
                '/moego.service.permission.v1.PermissionService/InitRole',
                request_serializer=moego_dot_service_dot_permission_dot_v1_dot_permission__service__pb2.InitRoleRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_permission_dot_v1_dot_permission__service__pb2.InitRoleResponse.FromString,
                _registered_method=True)
        self.PermissionMapping = channel.unary_unary(
                '/moego.service.permission.v1.PermissionService/PermissionMapping',
                request_serializer=moego_dot_service_dot_permission_dot_v1_dot_permission__service__pb2.PermissionMappingRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_permission_dot_v1_dot_permission__service__pb2.PermissionMappingResponse.FromString,
                _registered_method=True)
        self.GetOwnerPermission = channel.unary_unary(
                '/moego.service.permission.v1.PermissionService/GetOwnerPermission',
                request_serializer=moego_dot_service_dot_permission_dot_v1_dot_permission__service__pb2.GetOwnerPermissionRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_permission_dot_v1_dot_permission__service__pb2.GetOwnerPermissionResponse.FromString,
                _registered_method=True)
        self.InitOwnerRoleForCompanies = channel.unary_unary(
                '/moego.service.permission.v1.PermissionService/InitOwnerRoleForCompanies',
                request_serializer=moego_dot_service_dot_permission_dot_v1_dot_permission__service__pb2.InitOwnerRoleForCompaniesRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_permission_dot_v1_dot_permission__service__pb2.InitOwnerRoleForCompaniesResponse.FromString,
                _registered_method=True)
        self.InitEnterpriseRole = channel.unary_unary(
                '/moego.service.permission.v1.PermissionService/InitEnterpriseRole',
                request_serializer=moego_dot_service_dot_permission_dot_v1_dot_permission__service__pb2.InitEnterpriseRoleRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_permission_dot_v1_dot_permission__service__pb2.InitEnterpriseRoleResponse.FromString,
                _registered_method=True)
        self.CopyPermissions = channel.unary_unary(
                '/moego.service.permission.v1.PermissionService/CopyPermissions',
                request_serializer=moego_dot_service_dot_permission_dot_v1_dot_permission__service__pb2.CopyPermissionsRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_permission_dot_v1_dot_permission__service__pb2.CopyPermissionsResponse.FromString,
                _registered_method=True)
        self.RetainRolePermissions = channel.unary_unary(
                '/moego.service.permission.v1.PermissionService/RetainRolePermissions',
                request_serializer=moego_dot_service_dot_permission_dot_v1_dot_permission__service__pb2.RetainRolePermissionsRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_permission_dot_v1_dot_permission__service__pb2.RetainRolePermissionsResponse.FromString,
                _registered_method=True)


class PermissionServiceServicer(object):
    """permission service
    """

    def GetRoleList(self, request, context):
        """get role list
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetRoleDetail(self, request, context):
        """get role detail
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ListRoleDetails(self, request, context):
        """list role details
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def CreateRole(self, request, context):
        """create role
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def UpdateRole(self, request, context):
        """update role
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DeleteRole(self, request, context):
        """delete role
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DuplicateRole(self, request, context):
        """duplicate role
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def EditPermissions(self, request, context):
        """edit permissions
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def CheckPermission(self, request, context):
        """check permission
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def InitRole(self, request, context):
        """init role
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def PermissionMapping(self, request, context):
        """permission mapping
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetOwnerPermission(self, request, context):
        """get owner permission
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def InitOwnerRoleForCompanies(self, request, context):
        """Init owner role for companies
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def InitEnterpriseRole(self, request, context):
        """init enterprise role
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def CopyPermissions(self, request, context):
        """copy permissions from one role to another
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def RetainRolePermissions(self, request, context):
        """Remove any permissions not in the given list
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_PermissionServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'GetRoleList': grpc.unary_unary_rpc_method_handler(
                    servicer.GetRoleList,
                    request_deserializer=moego_dot_service_dot_permission_dot_v1_dot_permission__service__pb2.GetRoleListRequest.FromString,
                    response_serializer=moego_dot_service_dot_permission_dot_v1_dot_permission__service__pb2.GetRoleListResponse.SerializeToString,
            ),
            'GetRoleDetail': grpc.unary_unary_rpc_method_handler(
                    servicer.GetRoleDetail,
                    request_deserializer=moego_dot_service_dot_permission_dot_v1_dot_permission__service__pb2.GetRoleDetailRequest.FromString,
                    response_serializer=moego_dot_service_dot_permission_dot_v1_dot_permission__service__pb2.GetRoleDetailResponse.SerializeToString,
            ),
            'ListRoleDetails': grpc.unary_unary_rpc_method_handler(
                    servicer.ListRoleDetails,
                    request_deserializer=moego_dot_service_dot_permission_dot_v1_dot_permission__service__pb2.ListRoleDetailsRequest.FromString,
                    response_serializer=moego_dot_service_dot_permission_dot_v1_dot_permission__service__pb2.ListRoleDetailsResponse.SerializeToString,
            ),
            'CreateRole': grpc.unary_unary_rpc_method_handler(
                    servicer.CreateRole,
                    request_deserializer=moego_dot_service_dot_permission_dot_v1_dot_permission__service__pb2.CreateRoleRequest.FromString,
                    response_serializer=moego_dot_service_dot_permission_dot_v1_dot_permission__service__pb2.CreateRoleResponse.SerializeToString,
            ),
            'UpdateRole': grpc.unary_unary_rpc_method_handler(
                    servicer.UpdateRole,
                    request_deserializer=moego_dot_service_dot_permission_dot_v1_dot_permission__service__pb2.UpdateRoleRequest.FromString,
                    response_serializer=moego_dot_service_dot_permission_dot_v1_dot_permission__service__pb2.UpdateRoleResponse.SerializeToString,
            ),
            'DeleteRole': grpc.unary_unary_rpc_method_handler(
                    servicer.DeleteRole,
                    request_deserializer=moego_dot_service_dot_permission_dot_v1_dot_permission__service__pb2.DeleteRoleRequest.FromString,
                    response_serializer=moego_dot_service_dot_permission_dot_v1_dot_permission__service__pb2.DeleteRoleResponse.SerializeToString,
            ),
            'DuplicateRole': grpc.unary_unary_rpc_method_handler(
                    servicer.DuplicateRole,
                    request_deserializer=moego_dot_service_dot_permission_dot_v1_dot_permission__service__pb2.DuplicateRoleRequest.FromString,
                    response_serializer=moego_dot_service_dot_permission_dot_v1_dot_permission__service__pb2.DuplicateRoleResponse.SerializeToString,
            ),
            'EditPermissions': grpc.unary_unary_rpc_method_handler(
                    servicer.EditPermissions,
                    request_deserializer=moego_dot_service_dot_permission_dot_v1_dot_permission__service__pb2.EditPermissionsRequest.FromString,
                    response_serializer=moego_dot_service_dot_permission_dot_v1_dot_permission__service__pb2.EditPermissionsResponse.SerializeToString,
            ),
            'CheckPermission': grpc.unary_unary_rpc_method_handler(
                    servicer.CheckPermission,
                    request_deserializer=moego_dot_service_dot_permission_dot_v1_dot_permission__service__pb2.CheckPermissionRequest.FromString,
                    response_serializer=moego_dot_service_dot_permission_dot_v1_dot_permission__service__pb2.CheckPermissionResponse.SerializeToString,
            ),
            'InitRole': grpc.unary_unary_rpc_method_handler(
                    servicer.InitRole,
                    request_deserializer=moego_dot_service_dot_permission_dot_v1_dot_permission__service__pb2.InitRoleRequest.FromString,
                    response_serializer=moego_dot_service_dot_permission_dot_v1_dot_permission__service__pb2.InitRoleResponse.SerializeToString,
            ),
            'PermissionMapping': grpc.unary_unary_rpc_method_handler(
                    servicer.PermissionMapping,
                    request_deserializer=moego_dot_service_dot_permission_dot_v1_dot_permission__service__pb2.PermissionMappingRequest.FromString,
                    response_serializer=moego_dot_service_dot_permission_dot_v1_dot_permission__service__pb2.PermissionMappingResponse.SerializeToString,
            ),
            'GetOwnerPermission': grpc.unary_unary_rpc_method_handler(
                    servicer.GetOwnerPermission,
                    request_deserializer=moego_dot_service_dot_permission_dot_v1_dot_permission__service__pb2.GetOwnerPermissionRequest.FromString,
                    response_serializer=moego_dot_service_dot_permission_dot_v1_dot_permission__service__pb2.GetOwnerPermissionResponse.SerializeToString,
            ),
            'InitOwnerRoleForCompanies': grpc.unary_unary_rpc_method_handler(
                    servicer.InitOwnerRoleForCompanies,
                    request_deserializer=moego_dot_service_dot_permission_dot_v1_dot_permission__service__pb2.InitOwnerRoleForCompaniesRequest.FromString,
                    response_serializer=moego_dot_service_dot_permission_dot_v1_dot_permission__service__pb2.InitOwnerRoleForCompaniesResponse.SerializeToString,
            ),
            'InitEnterpriseRole': grpc.unary_unary_rpc_method_handler(
                    servicer.InitEnterpriseRole,
                    request_deserializer=moego_dot_service_dot_permission_dot_v1_dot_permission__service__pb2.InitEnterpriseRoleRequest.FromString,
                    response_serializer=moego_dot_service_dot_permission_dot_v1_dot_permission__service__pb2.InitEnterpriseRoleResponse.SerializeToString,
            ),
            'CopyPermissions': grpc.unary_unary_rpc_method_handler(
                    servicer.CopyPermissions,
                    request_deserializer=moego_dot_service_dot_permission_dot_v1_dot_permission__service__pb2.CopyPermissionsRequest.FromString,
                    response_serializer=moego_dot_service_dot_permission_dot_v1_dot_permission__service__pb2.CopyPermissionsResponse.SerializeToString,
            ),
            'RetainRolePermissions': grpc.unary_unary_rpc_method_handler(
                    servicer.RetainRolePermissions,
                    request_deserializer=moego_dot_service_dot_permission_dot_v1_dot_permission__service__pb2.RetainRolePermissionsRequest.FromString,
                    response_serializer=moego_dot_service_dot_permission_dot_v1_dot_permission__service__pb2.RetainRolePermissionsResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'moego.service.permission.v1.PermissionService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('moego.service.permission.v1.PermissionService', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class PermissionService(object):
    """permission service
    """

    @staticmethod
    def GetRoleList(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.permission.v1.PermissionService/GetRoleList',
            moego_dot_service_dot_permission_dot_v1_dot_permission__service__pb2.GetRoleListRequest.SerializeToString,
            moego_dot_service_dot_permission_dot_v1_dot_permission__service__pb2.GetRoleListResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetRoleDetail(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.permission.v1.PermissionService/GetRoleDetail',
            moego_dot_service_dot_permission_dot_v1_dot_permission__service__pb2.GetRoleDetailRequest.SerializeToString,
            moego_dot_service_dot_permission_dot_v1_dot_permission__service__pb2.GetRoleDetailResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def ListRoleDetails(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.permission.v1.PermissionService/ListRoleDetails',
            moego_dot_service_dot_permission_dot_v1_dot_permission__service__pb2.ListRoleDetailsRequest.SerializeToString,
            moego_dot_service_dot_permission_dot_v1_dot_permission__service__pb2.ListRoleDetailsResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def CreateRole(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.permission.v1.PermissionService/CreateRole',
            moego_dot_service_dot_permission_dot_v1_dot_permission__service__pb2.CreateRoleRequest.SerializeToString,
            moego_dot_service_dot_permission_dot_v1_dot_permission__service__pb2.CreateRoleResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def UpdateRole(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.permission.v1.PermissionService/UpdateRole',
            moego_dot_service_dot_permission_dot_v1_dot_permission__service__pb2.UpdateRoleRequest.SerializeToString,
            moego_dot_service_dot_permission_dot_v1_dot_permission__service__pb2.UpdateRoleResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def DeleteRole(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.permission.v1.PermissionService/DeleteRole',
            moego_dot_service_dot_permission_dot_v1_dot_permission__service__pb2.DeleteRoleRequest.SerializeToString,
            moego_dot_service_dot_permission_dot_v1_dot_permission__service__pb2.DeleteRoleResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def DuplicateRole(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.permission.v1.PermissionService/DuplicateRole',
            moego_dot_service_dot_permission_dot_v1_dot_permission__service__pb2.DuplicateRoleRequest.SerializeToString,
            moego_dot_service_dot_permission_dot_v1_dot_permission__service__pb2.DuplicateRoleResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def EditPermissions(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.permission.v1.PermissionService/EditPermissions',
            moego_dot_service_dot_permission_dot_v1_dot_permission__service__pb2.EditPermissionsRequest.SerializeToString,
            moego_dot_service_dot_permission_dot_v1_dot_permission__service__pb2.EditPermissionsResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def CheckPermission(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.permission.v1.PermissionService/CheckPermission',
            moego_dot_service_dot_permission_dot_v1_dot_permission__service__pb2.CheckPermissionRequest.SerializeToString,
            moego_dot_service_dot_permission_dot_v1_dot_permission__service__pb2.CheckPermissionResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def InitRole(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.permission.v1.PermissionService/InitRole',
            moego_dot_service_dot_permission_dot_v1_dot_permission__service__pb2.InitRoleRequest.SerializeToString,
            moego_dot_service_dot_permission_dot_v1_dot_permission__service__pb2.InitRoleResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def PermissionMapping(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.permission.v1.PermissionService/PermissionMapping',
            moego_dot_service_dot_permission_dot_v1_dot_permission__service__pb2.PermissionMappingRequest.SerializeToString,
            moego_dot_service_dot_permission_dot_v1_dot_permission__service__pb2.PermissionMappingResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetOwnerPermission(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.permission.v1.PermissionService/GetOwnerPermission',
            moego_dot_service_dot_permission_dot_v1_dot_permission__service__pb2.GetOwnerPermissionRequest.SerializeToString,
            moego_dot_service_dot_permission_dot_v1_dot_permission__service__pb2.GetOwnerPermissionResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def InitOwnerRoleForCompanies(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.permission.v1.PermissionService/InitOwnerRoleForCompanies',
            moego_dot_service_dot_permission_dot_v1_dot_permission__service__pb2.InitOwnerRoleForCompaniesRequest.SerializeToString,
            moego_dot_service_dot_permission_dot_v1_dot_permission__service__pb2.InitOwnerRoleForCompaniesResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def InitEnterpriseRole(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.permission.v1.PermissionService/InitEnterpriseRole',
            moego_dot_service_dot_permission_dot_v1_dot_permission__service__pb2.InitEnterpriseRoleRequest.SerializeToString,
            moego_dot_service_dot_permission_dot_v1_dot_permission__service__pb2.InitEnterpriseRoleResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def CopyPermissions(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.permission.v1.PermissionService/CopyPermissions',
            moego_dot_service_dot_permission_dot_v1_dot_permission__service__pb2.CopyPermissionsRequest.SerializeToString,
            moego_dot_service_dot_permission_dot_v1_dot_permission__service__pb2.CopyPermissionsResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def RetainRolePermissions(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.permission.v1.PermissionService/RetainRolePermissions',
            moego_dot_service_dot_permission_dot_v1_dot_permission__service__pb2.RetainRolePermissionsRequest.SerializeToString,
            moego_dot_service_dot_permission_dot_v1_dot_permission__service__pb2.RetainRolePermissionsResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)
