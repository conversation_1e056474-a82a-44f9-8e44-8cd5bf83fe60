# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

from moego.service.open_platform.v1 import obfuscate_service_pb2 as moego_dot_service_dot_open__platform_dot_v1_dot_obfuscate__service__pb2


class ObfuscateServiceStub(object):
    """ObfuscateService
    """

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.BatchEncodeID = channel.unary_unary(
                '/moego.service.open_platform.v1.ObfuscateService/BatchEncodeID',
                request_serializer=moego_dot_service_dot_open__platform_dot_v1_dot_obfuscate__service__pb2.BatchEncodeIDRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_open__platform_dot_v1_dot_obfuscate__service__pb2.BatchEncodeIDResponse.FromString,
                _registered_method=True)
        self.BatchDecodeID = channel.unary_unary(
                '/moego.service.open_platform.v1.ObfuscateService/BatchDecodeID',
                request_serializer=moego_dot_service_dot_open__platform_dot_v1_dot_obfuscate__service__pb2.BatchDecodeIDRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_open__platform_dot_v1_dot_obfuscate__service__pb2.BatchDecodeIDResponse.FromString,
                _registered_method=True)


class ObfuscateServiceServicer(object):
    """ObfuscateService
    """

    def BatchEncodeID(self, request, context):
        """batch encode id
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def BatchDecodeID(self, request, context):
        """batch decode id
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_ObfuscateServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'BatchEncodeID': grpc.unary_unary_rpc_method_handler(
                    servicer.BatchEncodeID,
                    request_deserializer=moego_dot_service_dot_open__platform_dot_v1_dot_obfuscate__service__pb2.BatchEncodeIDRequest.FromString,
                    response_serializer=moego_dot_service_dot_open__platform_dot_v1_dot_obfuscate__service__pb2.BatchEncodeIDResponse.SerializeToString,
            ),
            'BatchDecodeID': grpc.unary_unary_rpc_method_handler(
                    servicer.BatchDecodeID,
                    request_deserializer=moego_dot_service_dot_open__platform_dot_v1_dot_obfuscate__service__pb2.BatchDecodeIDRequest.FromString,
                    response_serializer=moego_dot_service_dot_open__platform_dot_v1_dot_obfuscate__service__pb2.BatchDecodeIDResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'moego.service.open_platform.v1.ObfuscateService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('moego.service.open_platform.v1.ObfuscateService', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class ObfuscateService(object):
    """ObfuscateService
    """

    @staticmethod
    def BatchEncodeID(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.open_platform.v1.ObfuscateService/BatchEncodeID',
            moego_dot_service_dot_open__platform_dot_v1_dot_obfuscate__service__pb2.BatchEncodeIDRequest.SerializeToString,
            moego_dot_service_dot_open__platform_dot_v1_dot_obfuscate__service__pb2.BatchEncodeIDResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def BatchDecodeID(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.open_platform.v1.ObfuscateService/BatchDecodeID',
            moego_dot_service_dot_open__platform_dot_v1_dot_obfuscate__service__pb2.BatchDecodeIDRequest.SerializeToString,
            moego_dot_service_dot_open__platform_dot_v1_dot_obfuscate__service__pb2.BatchDecodeIDResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)
