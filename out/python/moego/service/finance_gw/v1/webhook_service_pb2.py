# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: moego/service/finance_gw/v1/webhook_service.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'moego/service/finance_gw/v1/webhook_service.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from moego.models.finance_gw.v1 import webhook_models_pb2 as moego_dot_models_dot_finance__gw_dot_v1_dot_webhook__models__pb2
from validate import validate_pb2 as validate_dot_validate__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n1moego/service/finance_gw/v1/webhook_service.proto\x12\x1bmoego.service.finance_gw.v1\x1a/moego/models/finance_gw/v1/webhook_models.proto\x1a\x17validate/validate.proto\"}\n\x15HandleRawEventRequest\x12\x64\n\x12http_webhook_event\x18\x01 \x01(\x0b\x32,.moego.models.finance_gw.v1.HttpWebhookEventB\x08\xfa\x42\x05\x8a\x01\x02\x10\x01R\x10httpWebhookEvent\"\x18\n\x16HandleRawEventResponse2\x8b\x01\n\x0eWebhookService\x12y\n\x0eHandleRawEvent\x12\x32.moego.service.finance_gw.v1.HandleRawEventRequest\x1a\x33.moego.service.finance_gw.v1.HandleRawEventResponseB\x88\x01\n#com.moego.idl.service.finance_gw.v1P\x01Z_github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/finance_gw/v1;financegwsvcpbb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'moego.service.finance_gw.v1.webhook_service_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n#com.moego.idl.service.finance_gw.v1P\001Z_github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/finance_gw/v1;financegwsvcpb'
  _globals['_HANDLERAWEVENTREQUEST'].fields_by_name['http_webhook_event']._loaded_options = None
  _globals['_HANDLERAWEVENTREQUEST'].fields_by_name['http_webhook_event']._serialized_options = b'\372B\005\212\001\002\020\001'
  _globals['_HANDLERAWEVENTREQUEST']._serialized_start=156
  _globals['_HANDLERAWEVENTREQUEST']._serialized_end=281
  _globals['_HANDLERAWEVENTRESPONSE']._serialized_start=283
  _globals['_HANDLERAWEVENTRESPONSE']._serialized_end=307
  _globals['_WEBHOOKSERVICE']._serialized_start=310
  _globals['_WEBHOOKSERVICE']._serialized_end=449
# @@protoc_insertion_point(module_scope)
