# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: moego/service/engagement/v1/twilio_hook_service.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'moego/service/engagement/v1/twilio_hook_service.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from validate import validate_pb2 as validate_dot_validate__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n5moego/service/engagement/v1/twilio_hook_service.proto\x12\x1bmoego.service.engagement.v1\x1a\x17validate/validate.proto\"\x85\x02\n\x11TwilioHookRequest\x12\x18\n\x07\x63ontent\x18\x01 \x01(\tR\x07\x63ontent\x12\x1d\n\ncompany_id\x18\x02 \x01(\x03R\tcompanyId\x12\x1f\n\x0b\x62usiness_id\x18\x03 \x01(\x03R\nbusinessId\x12Y\n\tquery_map\x18\x04 \x03(\x0b\x32<.moego.service.engagement.v1.TwilioHookRequest.QueryMapEntryR\x08queryMap\x1a;\n\rQueryMapEntry\x12\x10\n\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n\x05value\x18\x02 \x01(\tR\x05value:\x02\x38\x01\".\n\x12TwilioHookResponse\x12\x18\n\x07\x63ontent\x18\x01 \x01(\tR\x07\x63ontent\"\xbf\x01\n\x15\x46orwardMessageRequest\x12\x30\n\x0freceiver_number\x18\x01 \x01(\tB\x07\xfa\x42\x04r\x02\x10\x01R\x0ereceiverNumber\x12,\n\rsender_number\x18\x02 \x01(\tB\x07\xfa\x42\x04r\x02\x10\x01R\x0csenderNumber\x12\x18\n\x07\x63ontent\x18\x03 \x01(\tR\x07\x63ontent\x12,\n\x12\x64isable_auto_reply\x18\x04 \x01(\x08R\x10\x64isableAutoReply\";\n\x16\x46orwardMessageResponse\x12!\n\x0credirect_url\x18\x01 \x01(\tR\x0bredirectUrl2\x8b\t\n\x11TwilioHookService\x12g\n\x04\x43\x61ll\x12..moego.service.engagement.v1.TwilioHookRequest\x1a/.moego.service.engagement.v1.TwilioHookResponse\x12n\n\x0b\x46orwardCall\x12..moego.service.engagement.v1.TwilioHookRequest\x1a/.moego.service.engagement.v1.TwilioHookResponse\x12k\n\x08\x46\x61llback\x12..moego.service.engagement.v1.TwilioHookRequest\x1a/.moego.service.engagement.v1.TwilioHookResponse\x12i\n\x06Status\x12..moego.service.engagement.v1.TwilioHookRequest\x1a/.moego.service.engagement.v1.TwilioHookResponse\x12m\n\nDialAction\x12..moego.service.engagement.v1.TwilioHookRequest\x1a/.moego.service.engagement.v1.TwilioHookResponse\x12o\n\x0cRecordStatus\x12..moego.service.engagement.v1.TwilioHookRequest\x1a/.moego.service.engagement.v1.TwilioHookResponse\x12v\n\x13TranscriptionStatus\x12..moego.service.engagement.v1.TwilioHookRequest\x1a/.moego.service.engagement.v1.TwilioHookResponse\x12u\n\x12MaskedOutgoingCall\x12..moego.service.engagement.v1.TwilioHookRequest\x1a/.moego.service.engagement.v1.TwilioHookResponse\x12{\n\x18MaskedOutgoingCallGather\x12..moego.service.engagement.v1.TwilioHookRequest\x1a/.moego.service.engagement.v1.TwilioHookResponse\x12y\n\x0e\x46orwardMessage\x12\x32.moego.service.engagement.v1.ForwardMessageRequest\x1a\x33.moego.service.engagement.v1.ForwardMessageResponseB\x89\x01\n#com.moego.idl.service.engagement.v1P\x01Z`github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/engagement/v1;engagementsvcpbb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'moego.service.engagement.v1.twilio_hook_service_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n#com.moego.idl.service.engagement.v1P\001Z`github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/engagement/v1;engagementsvcpb'
  _globals['_TWILIOHOOKREQUEST_QUERYMAPENTRY']._loaded_options = None
  _globals['_TWILIOHOOKREQUEST_QUERYMAPENTRY']._serialized_options = b'8\001'
  _globals['_FORWARDMESSAGEREQUEST'].fields_by_name['receiver_number']._loaded_options = None
  _globals['_FORWARDMESSAGEREQUEST'].fields_by_name['receiver_number']._serialized_options = b'\372B\004r\002\020\001'
  _globals['_FORWARDMESSAGEREQUEST'].fields_by_name['sender_number']._loaded_options = None
  _globals['_FORWARDMESSAGEREQUEST'].fields_by_name['sender_number']._serialized_options = b'\372B\004r\002\020\001'
  _globals['_TWILIOHOOKREQUEST']._serialized_start=112
  _globals['_TWILIOHOOKREQUEST']._serialized_end=373
  _globals['_TWILIOHOOKREQUEST_QUERYMAPENTRY']._serialized_start=314
  _globals['_TWILIOHOOKREQUEST_QUERYMAPENTRY']._serialized_end=373
  _globals['_TWILIOHOOKRESPONSE']._serialized_start=375
  _globals['_TWILIOHOOKRESPONSE']._serialized_end=421
  _globals['_FORWARDMESSAGEREQUEST']._serialized_start=424
  _globals['_FORWARDMESSAGEREQUEST']._serialized_end=615
  _globals['_FORWARDMESSAGERESPONSE']._serialized_start=617
  _globals['_FORWARDMESSAGERESPONSE']._serialized_end=676
  _globals['_TWILIOHOOKSERVICE']._serialized_start=679
  _globals['_TWILIOHOOKSERVICE']._serialized_end=1842
# @@protoc_insertion_point(module_scope)
