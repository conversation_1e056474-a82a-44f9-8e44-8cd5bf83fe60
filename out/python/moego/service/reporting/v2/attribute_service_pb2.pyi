from moego.models.reporting.v2 import attribute_def_pb2 as _attribute_def_pb2
from moego.models.reporting.v2 import common_model_pb2 as _common_model_pb2
from moego.models.reporting.v2 import report_meta_def_pb2 as _report_meta_def_pb2
from google.protobuf.internal import containers as _containers
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from collections.abc import Iterable as _Iterable, Mapping as _Mapping
from typing import ClassVar as _ClassVar, Optional as _Optional, Union as _Union

DESCRIPTOR: _descriptor.FileDescriptor

class GetDimensionsRequest(_message.Message):
    __slots__ = ("diagram_id", "scene")
    DIAGRAM_ID_FIELD_NUMBER: _ClassVar[int]
    SCENE_FIELD_NUMBER: _ClassVar[int]
    diagram_id: str
    scene: _common_model_pb2.ReportingScene
    def __init__(self, diagram_id: _Optional[str] = ..., scene: _Optional[_Union[_common_model_pb2.ReportingScene, str]] = ...) -> None: ...

class GetDimensionsResponse(_message.Message):
    __slots__ = ("dimensions",)
    DIMENSIONS_FIELD_NUMBER: _ClassVar[int]
    dimensions: _containers.RepeatedCompositeFieldContainer[_report_meta_def_pb2.DimensionField]
    def __init__(self, dimensions: _Optional[_Iterable[_Union[_report_meta_def_pb2.DimensionField, _Mapping]]] = ...) -> None: ...

class GetMetricsCategoriesRequest(_message.Message):
    __slots__ = ("diagram_id", "scene")
    DIAGRAM_ID_FIELD_NUMBER: _ClassVar[int]
    SCENE_FIELD_NUMBER: _ClassVar[int]
    diagram_id: str
    scene: _common_model_pb2.ReportingScene
    def __init__(self, diagram_id: _Optional[str] = ..., scene: _Optional[_Union[_common_model_pb2.ReportingScene, str]] = ...) -> None: ...

class GetMetricsCategoriesResponse(_message.Message):
    __slots__ = ("categories",)
    CATEGORIES_FIELD_NUMBER: _ClassVar[int]
    categories: _containers.RepeatedCompositeFieldContainer[_attribute_def_pb2.MetricsCategoryDef]
    def __init__(self, categories: _Optional[_Iterable[_Union[_attribute_def_pb2.MetricsCategoryDef, _Mapping]]] = ...) -> None: ...
