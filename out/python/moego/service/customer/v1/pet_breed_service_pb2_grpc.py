# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

from google.protobuf import empty_pb2 as google_dot_protobuf_dot_empty__pb2
from moego.service.customer.v1 import pet_breed_service_pb2 as moego_dot_service_dot_customer_dot_v1_dot_pet__breed__service__pb2


class PetBreedServiceStub(object):
    """pet breed service
    """

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.GetPetBreedList = channel.unary_unary(
                '/moego.service.customer.v1.PetBreedService/GetPetBreedList',
                request_serializer=google_dot_protobuf_dot_empty__pb2.Empty.SerializeToString,
                response_deserializer=moego_dot_service_dot_customer_dot_v1_dot_pet__breed__service__pb2.PetBreedListOutput.FromString,
                _registered_method=True)


class PetBreedServiceServicer(object):
    """pet breed service
    """

    def GetPetBreedList(self, request, context):
        """get pet breed list
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_PetBreedServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'GetPetBreedList': grpc.unary_unary_rpc_method_handler(
                    servicer.GetPetBreedList,
                    request_deserializer=google_dot_protobuf_dot_empty__pb2.Empty.FromString,
                    response_serializer=moego_dot_service_dot_customer_dot_v1_dot_pet__breed__service__pb2.PetBreedListOutput.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'moego.service.customer.v1.PetBreedService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('moego.service.customer.v1.PetBreedService', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class PetBreedService(object):
    """pet breed service
    """

    @staticmethod
    def GetPetBreedList(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.customer.v1.PetBreedService/GetPetBreedList',
            google_dot_protobuf_dot_empty__pb2.Empty.SerializeToString,
            moego_dot_service_dot_customer_dot_v1_dot_pet__breed__service__pb2.PetBreedListOutput.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)
