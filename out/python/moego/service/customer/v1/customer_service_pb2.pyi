from moego.models.customer.v1 import customer_models_pb2 as _customer_models_pb2
from google.protobuf.internal import containers as _containers
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from collections.abc import Iterable as _Iterable, Mapping as _Mapping
from typing import ClassVar as _ClassVar, Optional as _Optional, Union as _Union

DESCRIPTOR: _descriptor.FileDescriptor

class GetCustomerRequest(_message.Message):
    __slots__ = ("business_id", "customer_id")
    BUSINESS_ID_FIELD_NUMBER: _ClassVar[int]
    CUSTOMER_ID_FIELD_NUMBER: _ClassVar[int]
    business_id: int
    customer_id: int
    def __init__(self, business_id: _Optional[int] = ..., customer_id: _Optional[int] = ...) -> None: ...

class GetCustomerListRequest(_message.Message):
    __slots__ = ("business_id", "ids")
    BUSINESS_ID_FIELD_NUMBER: _ClassVar[int]
    IDS_FIELD_NUMBER: _ClassVar[int]
    business_id: int
    ids: _containers.RepeatedScalarFieldContainer[int]
    def __init__(self, business_id: _Optional[int] = ..., ids: _Optional[_Iterable[int]] = ...) -> None: ...

class GetCustomerNameViewListResponse(_message.Message):
    __slots__ = ("customer_name_view",)
    CUSTOMER_NAME_VIEW_FIELD_NUMBER: _ClassVar[int]
    customer_name_view: _containers.RepeatedCompositeFieldContainer[_customer_models_pb2.CustomerModelNameView]
    def __init__(self, customer_name_view: _Optional[_Iterable[_Union[_customer_models_pb2.CustomerModelNameView, _Mapping]]] = ...) -> None: ...
