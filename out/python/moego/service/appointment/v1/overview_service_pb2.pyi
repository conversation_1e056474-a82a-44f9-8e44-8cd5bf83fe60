from google.type import date_pb2 as _date_pb2
from moego.models.appointment.v1 import appointment_enums_pb2 as _appointment_enums_pb2
from moego.models.appointment.v1 import appointment_models_pb2 as _appointment_models_pb2
from moego.models.appointment.v1 import overview_enums_pb2 as _overview_enums_pb2
from moego.models.offering.v1 import service_enum_pb2 as _service_enum_pb2
from validate import validate_pb2 as _validate_pb2
from google.protobuf.internal import containers as _containers
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from collections.abc import Iterable as _Iterable, Mapping as _Mapping
from typing import ClassVar as _ClassVar, Optional as _Optional, Union as _Union

DESCRIPTOR: _descriptor.FileDescriptor

class GetOverviewListRequest(_message.Message):
    __slots__ = ("business_id", "date", "keyword", "service_item_types", "date_type", "company_id")
    BUSINESS_ID_FIELD_NUMBER: _ClassVar[int]
    DATE_FIELD_NUMBER: _ClassVar[int]
    KEYWORD_FIELD_NUMBER: _ClassVar[int]
    SERVICE_ITEM_TYPES_FIELD_NUMBER: _ClassVar[int]
    DATE_TYPE_FIELD_NUMBER: _ClassVar[int]
    COMPANY_ID_FIELD_NUMBER: _ClassVar[int]
    business_id: int
    date: str
    keyword: str
    service_item_types: _containers.RepeatedScalarFieldContainer[_service_enum_pb2.ServiceItemType]
    date_type: _overview_enums_pb2.OverviewDateType
    company_id: int
    def __init__(self, business_id: _Optional[int] = ..., date: _Optional[str] = ..., keyword: _Optional[str] = ..., service_item_types: _Optional[_Iterable[_Union[_service_enum_pb2.ServiceItemType, str]]] = ..., date_type: _Optional[_Union[_overview_enums_pb2.OverviewDateType, str]] = ..., company_id: _Optional[int] = ...) -> None: ...

class OverviewStatusEntry(_message.Message):
    __slots__ = ("status", "count", "appointment_overviews")
    STATUS_FIELD_NUMBER: _ClassVar[int]
    COUNT_FIELD_NUMBER: _ClassVar[int]
    APPOINTMENT_OVERVIEWS_FIELD_NUMBER: _ClassVar[int]
    status: _overview_enums_pb2.OverviewStatus
    count: int
    appointment_overviews: _containers.RepeatedCompositeFieldContainer[_appointment_models_pb2.AppointmentOverview]
    def __init__(self, status: _Optional[_Union[_overview_enums_pb2.OverviewStatus, str]] = ..., count: _Optional[int] = ..., appointment_overviews: _Optional[_Iterable[_Union[_appointment_models_pb2.AppointmentOverview, _Mapping]]] = ...) -> None: ...

class GetOverviewListResponse(_message.Message):
    __slots__ = ("entries",)
    ENTRIES_FIELD_NUMBER: _ClassVar[int]
    entries: _containers.RepeatedCompositeFieldContainer[OverviewStatusEntry]
    def __init__(self, entries: _Optional[_Iterable[_Union[OverviewStatusEntry, _Mapping]]] = ...) -> None: ...

class ListOverviewAppointmentRequest(_message.Message):
    __slots__ = ("business_id", "company_id", "date", "date_type", "overview_status", "service_item_types", "appointment_statuses", "customer_ids")
    BUSINESS_ID_FIELD_NUMBER: _ClassVar[int]
    COMPANY_ID_FIELD_NUMBER: _ClassVar[int]
    DATE_FIELD_NUMBER: _ClassVar[int]
    DATE_TYPE_FIELD_NUMBER: _ClassVar[int]
    OVERVIEW_STATUS_FIELD_NUMBER: _ClassVar[int]
    SERVICE_ITEM_TYPES_FIELD_NUMBER: _ClassVar[int]
    APPOINTMENT_STATUSES_FIELD_NUMBER: _ClassVar[int]
    CUSTOMER_IDS_FIELD_NUMBER: _ClassVar[int]
    business_id: int
    company_id: int
    date: _date_pb2.Date
    date_type: _overview_enums_pb2.OverviewDateType
    overview_status: _overview_enums_pb2.OverviewStatus
    service_item_types: _containers.RepeatedScalarFieldContainer[_service_enum_pb2.ServiceItemType]
    appointment_statuses: _containers.RepeatedScalarFieldContainer[_appointment_enums_pb2.AppointmentStatus]
    customer_ids: _containers.RepeatedScalarFieldContainer[int]
    def __init__(self, business_id: _Optional[int] = ..., company_id: _Optional[int] = ..., date: _Optional[_Union[_date_pb2.Date, _Mapping]] = ..., date_type: _Optional[_Union[_overview_enums_pb2.OverviewDateType, str]] = ..., overview_status: _Optional[_Union[_overview_enums_pb2.OverviewStatus, str]] = ..., service_item_types: _Optional[_Iterable[_Union[_service_enum_pb2.ServiceItemType, str]]] = ..., appointment_statuses: _Optional[_Iterable[_Union[_appointment_enums_pb2.AppointmentStatus, str]]] = ..., customer_ids: _Optional[_Iterable[int]] = ...) -> None: ...

class ListOverviewAppointmentResponse(_message.Message):
    __slots__ = ("appointments",)
    APPOINTMENTS_FIELD_NUMBER: _ClassVar[int]
    appointments: _containers.RepeatedCompositeFieldContainer[_appointment_models_pb2.AppointmentOverview]
    def __init__(self, appointments: _Optional[_Iterable[_Union[_appointment_models_pb2.AppointmentOverview, _Mapping]]] = ...) -> None: ...
