# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: moego/service/order/v1/split_tips_service.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'moego/service/order/v1/split_tips_service.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.type import decimal_pb2 as google_dot_type_dot_decimal__pb2
from moego.models.order.v1 import split_tips_enum_pb2 as moego_dot_models_dot_order_dot_v1_dot_split__tips__enum__pb2
from moego.models.order.v1 import split_tips_models_pb2 as moego_dot_models_dot_order_dot_v1_dot_split__tips__models__pb2
from validate import validate_pb2 as validate_dot_validate__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n/moego/service/order/v1/split_tips_service.proto\x12\x16moego.service.order.v1\x1a\x19google/type/decimal.proto\x1a+moego/models/order/v1/split_tips_enum.proto\x1a-moego/models/order/v1/split_tips_models.proto\x1a\x17validate/validate.proto\"O\n\x11GetSplitTipsInput\x12\x1f\n\x0b\x62usiness_id\x18\x01 \x01(\x03R\nbusinessId\x12\x19\n\x08order_id\x18\x02 \x01(\x03R\x07orderId\"\x99\x01\n\x12GetSplitTipsOutput\x12\x14\n\x05\x65xist\x18\x01 \x01(\x08R\x05\x65xist\x12W\n\x11split_tips_record\x18\x02 \x01(\x0b\x32&.moego.models.order.v1.SplitTipsRecordH\x00R\x0fsplitTipsRecord\x88\x01\x01\x42\x14\n\x12_split_tips_record\"v\n\x15GetSplitTipsListInput\x12\x1f\n\x0b\x62usiness_id\x18\x01 \x01(\x03R\nbusinessId\x12\x19\n\x08order_id\x18\x02 \x03(\x03R\x07orderId\x12!\n\x0c\x62usiness_ids\x18\x03 \x03(\x03R\x0b\x62usinessIds\"l\n\x16GetSplitTipsListOutput\x12R\n\x11split_tips_record\x18\x01 \x03(\x0b\x32&.moego.models.order.v1.SplitTipsRecordR\x0fsplitTipsRecord\"\xac\x01\n\x18\x43ustomizedTipConfigInput\x12\x19\n\x08staff_id\x18\x01 \x01(\x03R\x07staffId\x12+\n\x06\x61mount\x18\x02 \x01(\x01\x42\x0e\xfa\x42\x0b\x12\t)\x00\x00\x00\x00\x00\x00\x00\x00H\x00R\x06\x61mount\x88\x01\x01\x12.\n\npercentage\x18\x03 \x01(\x05\x42\t\xfa\x42\x06\x1a\x04\x18\x64(\x00H\x01R\npercentage\x88\x01\x01\x42\t\n\x07_amountB\r\n\x0b_percentage\"\xd7\x04\n\x12SaveSplitTipsInput\x12\x1f\n\x0b\x62usiness_id\x18\x01 \x01(\x03R\nbusinessId\x12\x19\n\x08order_id\x18\x02 \x01(\x03R\x07orderId\x12Z\n\x0csplit_method\x18\x03 \x01(\x0e\x32&.moego.models.order.v1.SplitTipsMethodB\n\xfa\x42\x07\x82\x01\x04\x10\x01 \x00H\x00R\x0bsplitMethod\x88\x01\x01\x12\x62\n\x0f\x63ustomized_type\x18\x04 \x01(\x0e\x32(.moego.models.order.v1.CustomizedTipTypeB\n\xfa\x42\x07\x82\x01\x04\x10\x01 \x00H\x01R\x0e\x63ustomizedType\x88\x01\x01\x12]\n\x11\x63ustomized_config\x18\x05 \x03(\x0b\x32\x30.moego.service.order.v1.CustomizedTipConfigInputR\x10\x63ustomizedConfig\x12\x1e\n\x08\x61pply_by\x18\x06 \x01(\x03H\x02R\x07\x61pplyBy\x88\x01\x01\x12\"\n\nis_deleted\x18\x07 \x01(\x08H\x03R\tisDeleted\x88\x01\x01\x12I\n\x13\x62usiness_tip_amount\x18\x08 \x01(\x0b\x32\x14.google.type.DecimalH\x04R\x11\x62usinessTipAmount\x88\x01\x01\x42\x0f\n\r_split_methodB\x12\n\x10_customized_typeB\x0b\n\t_apply_byB\r\n\x0b_is_deletedB\x16\n\x14_business_tip_amount\"/\n\x13SaveSplitTipsOutput\x12\x18\n\x07success\x18\x01 \x01(\x08R\x07success2\xe8\x02\n\x10SplitTipsService\x12k\n\x12GetSplitTipsRecord\x12).moego.service.order.v1.GetSplitTipsInput\x1a*.moego.service.order.v1.GetSplitTipsOutput\x12w\n\x16GetSplitTipsListRecord\x12-.moego.service.order.v1.GetSplitTipsListInput\x1a..moego.service.order.v1.GetSplitTipsListOutput\x12n\n\x13SaveSplitTipsRecord\x12*.moego.service.order.v1.SaveSplitTipsInput\x1a+.moego.service.order.v1.SaveSplitTipsOutputBz\n\x1e\x63om.moego.idl.service.order.v1P\x01ZVgithub.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/order/v1;ordersvcpbb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'moego.service.order.v1.split_tips_service_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n\036com.moego.idl.service.order.v1P\001ZVgithub.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/order/v1;ordersvcpb'
  _globals['_CUSTOMIZEDTIPCONFIGINPUT'].fields_by_name['amount']._loaded_options = None
  _globals['_CUSTOMIZEDTIPCONFIGINPUT'].fields_by_name['amount']._serialized_options = b'\372B\013\022\t)\000\000\000\000\000\000\000\000'
  _globals['_CUSTOMIZEDTIPCONFIGINPUT'].fields_by_name['percentage']._loaded_options = None
  _globals['_CUSTOMIZEDTIPCONFIGINPUT'].fields_by_name['percentage']._serialized_options = b'\372B\006\032\004\030d(\000'
  _globals['_SAVESPLITTIPSINPUT'].fields_by_name['split_method']._loaded_options = None
  _globals['_SAVESPLITTIPSINPUT'].fields_by_name['split_method']._serialized_options = b'\372B\007\202\001\004\020\001 \000'
  _globals['_SAVESPLITTIPSINPUT'].fields_by_name['customized_type']._loaded_options = None
  _globals['_SAVESPLITTIPSINPUT'].fields_by_name['customized_type']._serialized_options = b'\372B\007\202\001\004\020\001 \000'
  _globals['_GETSPLITTIPSINPUT']._serialized_start=219
  _globals['_GETSPLITTIPSINPUT']._serialized_end=298
  _globals['_GETSPLITTIPSOUTPUT']._serialized_start=301
  _globals['_GETSPLITTIPSOUTPUT']._serialized_end=454
  _globals['_GETSPLITTIPSLISTINPUT']._serialized_start=456
  _globals['_GETSPLITTIPSLISTINPUT']._serialized_end=574
  _globals['_GETSPLITTIPSLISTOUTPUT']._serialized_start=576
  _globals['_GETSPLITTIPSLISTOUTPUT']._serialized_end=684
  _globals['_CUSTOMIZEDTIPCONFIGINPUT']._serialized_start=687
  _globals['_CUSTOMIZEDTIPCONFIGINPUT']._serialized_end=859
  _globals['_SAVESPLITTIPSINPUT']._serialized_start=862
  _globals['_SAVESPLITTIPSINPUT']._serialized_end=1461
  _globals['_SAVESPLITTIPSOUTPUT']._serialized_start=1463
  _globals['_SAVESPLITTIPSOUTPUT']._serialized_end=1510
  _globals['_SPLITTIPSSERVICE']._serialized_start=1513
  _globals['_SPLITTIPSSERVICE']._serialized_end=1873
# @@protoc_insertion_point(module_scope)
