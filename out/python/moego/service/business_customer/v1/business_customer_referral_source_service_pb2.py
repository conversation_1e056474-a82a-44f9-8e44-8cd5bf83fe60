# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: moego/service/business_customer/v1/business_customer_referral_source_service.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'moego/service/business_customer/v1/business_customer_referral_source_service.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from moego.models.business_customer.v1 import business_customer_referral_source_defs_pb2 as moego_dot_models_dot_business__customer_dot_v1_dot_business__customer__referral__source__defs__pb2
from moego.models.business_customer.v1 import business_customer_referral_source_models_pb2 as moego_dot_models_dot_business__customer_dot_v1_dot_business__customer__referral__source__models__pb2
from validate import validate_pb2 as validate_dot_validate__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\nRmoego/service/business_customer/v1/business_customer_referral_source_service.proto\x12\"moego.service.business_customer.v1\x1aNmoego/models/business_customer/v1/business_customer_referral_source_defs.proto\x1aPmoego/models/business_customer/v1/business_customer_referral_source_models.proto\x1a\x17validate/validate.proto\"\xa2\x01\n GetCustomerReferralSourceRequest\x12\x17\n\x02id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\x02id\x12&\n\ncompany_id\x18\x02 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\tcompanyId\x12-\n\x0b\x62usiness_id\x18\x03 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00H\x00R\nbusinessId\x88\x01\x01\x42\x0e\n\x0c_business_id\"\x94\x01\n!GetCustomerReferralSourceResponse\x12o\n\x0freferral_source\x18\x01 \x01(\x0b\x32\x46.moego.models.business_customer.v1.BusinessCustomerReferralSourceModelR\x0ereferralSource\"\x8a\x01\n!ListCustomerReferralSourceRequest\x12&\n\ncompany_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\tcompanyId\x12-\n\x0b\x62usiness_id\x18\x02 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00H\x00R\nbusinessId\x88\x01\x01\x42\x0e\n\x0c_business_id\"\x97\x01\n\"ListCustomerReferralSourceResponse\x12q\n\x10referral_sources\x18\x01 \x03(\x0b\x32\x46.moego.models.business_customer.v1.BusinessCustomerReferralSourceModelR\x0freferralSources\"+\n)ListCustomerReferralSourceTemplateRequest\"\x9f\x01\n*ListCustomerReferralSourceTemplateResponse\x12q\n\x10referral_sources\x18\x01 \x03(\x0b\x32\x46.moego.models.business_customer.v1.BusinessCustomerReferralSourceModelR\x0freferralSources\"\x8b\x02\n#CreateCustomerReferralSourceRequest\x12&\n\ncompany_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\tcompanyId\x12-\n\x0b\x62usiness_id\x18\x02 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00H\x00R\nbusinessId\x88\x01\x01\x12}\n\x0freferral_source\x18\x03 \x01(\x0b\x32J.moego.models.business_customer.v1.BusinessCustomerReferralSourceCreateDefB\x08\xfa\x42\x05\x8a\x01\x02\x10\x01R\x0ereferralSourceB\x0e\n\x0c_business_id\"\x97\x01\n$CreateCustomerReferralSourceResponse\x12o\n\x0freferral_source\x18\x01 \x01(\x0b\x32\x46.moego.models.business_customer.v1.BusinessCustomerReferralSourceModelR\x0ereferralSource\"\xa4\x02\n#UpdateCustomerReferralSourceRequest\x12\x17\n\x02id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\x02id\x12&\n\ncompany_id\x18\x02 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\tcompanyId\x12-\n\x0b\x62usiness_id\x18\x03 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00H\x00R\nbusinessId\x88\x01\x01\x12}\n\x0freferral_source\x18\x04 \x01(\x0b\x32J.moego.models.business_customer.v1.BusinessCustomerReferralSourceUpdateDefB\x08\xfa\x42\x05\x8a\x01\x02\x10\x01R\x0ereferralSourceB\x0e\n\x0c_business_id\"&\n$UpdateCustomerReferralSourceResponse\"\xac\x01\n!SortCustomerReferralSourceRequest\x12 \n\x03ids\x18\x01 \x03(\x03\x42\x0e\xfa\x42\x0b\x92\x01\x08\x18\x01\"\x04\"\x02 \x00R\x03ids\x12&\n\ncompany_id\x18\x02 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\tcompanyId\x12-\n\x0b\x62usiness_id\x18\x03 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00H\x00R\nbusinessId\x88\x01\x01\x42\x0e\n\x0c_business_id\"$\n\"SortCustomerReferralSourceResponse\"\xa5\x01\n#DeleteCustomerReferralSourceRequest\x12\x17\n\x02id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\x02id\x12&\n\ncompany_id\x18\x02 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\tcompanyId\x12-\n\x0b\x62usiness_id\x18\x03 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00H\x00R\nbusinessId\x88\x01\x01\x42\x0e\n\x0c_business_id\"&\n$DeleteCustomerReferralSourceResponse2\x90\n\n%BusinessCustomerReferralSourceService\x12\xa8\x01\n\x19GetCustomerReferralSource\x12\x44.moego.service.business_customer.v1.GetCustomerReferralSourceRequest\x1a\x45.moego.service.business_customer.v1.GetCustomerReferralSourceResponse\x12\xab\x01\n\x1aListCustomerReferralSource\x12\x45.moego.service.business_customer.v1.ListCustomerReferralSourceRequest\x1a\x46.moego.service.business_customer.v1.ListCustomerReferralSourceResponse\x12\xc3\x01\n\"ListCustomerReferralSourceTemplate\x12M.moego.service.business_customer.v1.ListCustomerReferralSourceTemplateRequest\x1aN.moego.service.business_customer.v1.ListCustomerReferralSourceTemplateResponse\x12\xb1\x01\n\x1c\x43reateCustomerReferralSource\x12G.moego.service.business_customer.v1.CreateCustomerReferralSourceRequest\x1aH.moego.service.business_customer.v1.CreateCustomerReferralSourceResponse\x12\xb1\x01\n\x1cUpdateCustomerReferralSource\x12G.moego.service.business_customer.v1.UpdateCustomerReferralSourceRequest\x1aH.moego.service.business_customer.v1.UpdateCustomerReferralSourceResponse\x12\xab\x01\n\x1aSortCustomerReferralSource\x12\x45.moego.service.business_customer.v1.SortCustomerReferralSourceRequest\x1a\x46.moego.service.business_customer.v1.SortCustomerReferralSourceResponse\x12\xb1\x01\n\x1c\x44\x65leteCustomerReferralSource\x12G.moego.service.business_customer.v1.DeleteCustomerReferralSourceRequest\x1aH.moego.service.business_customer.v1.DeleteCustomerReferralSourceResponseB\x9d\x01\n*com.moego.idl.service.business_customer.v1P\x01Zmgithub.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/business_customer/v1;businesscustomersvcpbb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'moego.service.business_customer.v1.business_customer_referral_source_service_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n*com.moego.idl.service.business_customer.v1P\001Zmgithub.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/business_customer/v1;businesscustomersvcpb'
  _globals['_GETCUSTOMERREFERRALSOURCEREQUEST'].fields_by_name['id']._loaded_options = None
  _globals['_GETCUSTOMERREFERRALSOURCEREQUEST'].fields_by_name['id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_GETCUSTOMERREFERRALSOURCEREQUEST'].fields_by_name['company_id']._loaded_options = None
  _globals['_GETCUSTOMERREFERRALSOURCEREQUEST'].fields_by_name['company_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_GETCUSTOMERREFERRALSOURCEREQUEST'].fields_by_name['business_id']._loaded_options = None
  _globals['_GETCUSTOMERREFERRALSOURCEREQUEST'].fields_by_name['business_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_LISTCUSTOMERREFERRALSOURCEREQUEST'].fields_by_name['company_id']._loaded_options = None
  _globals['_LISTCUSTOMERREFERRALSOURCEREQUEST'].fields_by_name['company_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_LISTCUSTOMERREFERRALSOURCEREQUEST'].fields_by_name['business_id']._loaded_options = None
  _globals['_LISTCUSTOMERREFERRALSOURCEREQUEST'].fields_by_name['business_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_CREATECUSTOMERREFERRALSOURCEREQUEST'].fields_by_name['company_id']._loaded_options = None
  _globals['_CREATECUSTOMERREFERRALSOURCEREQUEST'].fields_by_name['company_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_CREATECUSTOMERREFERRALSOURCEREQUEST'].fields_by_name['business_id']._loaded_options = None
  _globals['_CREATECUSTOMERREFERRALSOURCEREQUEST'].fields_by_name['business_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_CREATECUSTOMERREFERRALSOURCEREQUEST'].fields_by_name['referral_source']._loaded_options = None
  _globals['_CREATECUSTOMERREFERRALSOURCEREQUEST'].fields_by_name['referral_source']._serialized_options = b'\372B\005\212\001\002\020\001'
  _globals['_UPDATECUSTOMERREFERRALSOURCEREQUEST'].fields_by_name['id']._loaded_options = None
  _globals['_UPDATECUSTOMERREFERRALSOURCEREQUEST'].fields_by_name['id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_UPDATECUSTOMERREFERRALSOURCEREQUEST'].fields_by_name['company_id']._loaded_options = None
  _globals['_UPDATECUSTOMERREFERRALSOURCEREQUEST'].fields_by_name['company_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_UPDATECUSTOMERREFERRALSOURCEREQUEST'].fields_by_name['business_id']._loaded_options = None
  _globals['_UPDATECUSTOMERREFERRALSOURCEREQUEST'].fields_by_name['business_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_UPDATECUSTOMERREFERRALSOURCEREQUEST'].fields_by_name['referral_source']._loaded_options = None
  _globals['_UPDATECUSTOMERREFERRALSOURCEREQUEST'].fields_by_name['referral_source']._serialized_options = b'\372B\005\212\001\002\020\001'
  _globals['_SORTCUSTOMERREFERRALSOURCEREQUEST'].fields_by_name['ids']._loaded_options = None
  _globals['_SORTCUSTOMERREFERRALSOURCEREQUEST'].fields_by_name['ids']._serialized_options = b'\372B\013\222\001\010\030\001\"\004\"\002 \000'
  _globals['_SORTCUSTOMERREFERRALSOURCEREQUEST'].fields_by_name['company_id']._loaded_options = None
  _globals['_SORTCUSTOMERREFERRALSOURCEREQUEST'].fields_by_name['company_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_SORTCUSTOMERREFERRALSOURCEREQUEST'].fields_by_name['business_id']._loaded_options = None
  _globals['_SORTCUSTOMERREFERRALSOURCEREQUEST'].fields_by_name['business_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_DELETECUSTOMERREFERRALSOURCEREQUEST'].fields_by_name['id']._loaded_options = None
  _globals['_DELETECUSTOMERREFERRALSOURCEREQUEST'].fields_by_name['id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_DELETECUSTOMERREFERRALSOURCEREQUEST'].fields_by_name['company_id']._loaded_options = None
  _globals['_DELETECUSTOMERREFERRALSOURCEREQUEST'].fields_by_name['company_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_DELETECUSTOMERREFERRALSOURCEREQUEST'].fields_by_name['business_id']._loaded_options = None
  _globals['_DELETECUSTOMERREFERRALSOURCEREQUEST'].fields_by_name['business_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_GETCUSTOMERREFERRALSOURCEREQUEST']._serialized_start=310
  _globals['_GETCUSTOMERREFERRALSOURCEREQUEST']._serialized_end=472
  _globals['_GETCUSTOMERREFERRALSOURCERESPONSE']._serialized_start=475
  _globals['_GETCUSTOMERREFERRALSOURCERESPONSE']._serialized_end=623
  _globals['_LISTCUSTOMERREFERRALSOURCEREQUEST']._serialized_start=626
  _globals['_LISTCUSTOMERREFERRALSOURCEREQUEST']._serialized_end=764
  _globals['_LISTCUSTOMERREFERRALSOURCERESPONSE']._serialized_start=767
  _globals['_LISTCUSTOMERREFERRALSOURCERESPONSE']._serialized_end=918
  _globals['_LISTCUSTOMERREFERRALSOURCETEMPLATEREQUEST']._serialized_start=920
  _globals['_LISTCUSTOMERREFERRALSOURCETEMPLATEREQUEST']._serialized_end=963
  _globals['_LISTCUSTOMERREFERRALSOURCETEMPLATERESPONSE']._serialized_start=966
  _globals['_LISTCUSTOMERREFERRALSOURCETEMPLATERESPONSE']._serialized_end=1125
  _globals['_CREATECUSTOMERREFERRALSOURCEREQUEST']._serialized_start=1128
  _globals['_CREATECUSTOMERREFERRALSOURCEREQUEST']._serialized_end=1395
  _globals['_CREATECUSTOMERREFERRALSOURCERESPONSE']._serialized_start=1398
  _globals['_CREATECUSTOMERREFERRALSOURCERESPONSE']._serialized_end=1549
  _globals['_UPDATECUSTOMERREFERRALSOURCEREQUEST']._serialized_start=1552
  _globals['_UPDATECUSTOMERREFERRALSOURCEREQUEST']._serialized_end=1844
  _globals['_UPDATECUSTOMERREFERRALSOURCERESPONSE']._serialized_start=1846
  _globals['_UPDATECUSTOMERREFERRALSOURCERESPONSE']._serialized_end=1884
  _globals['_SORTCUSTOMERREFERRALSOURCEREQUEST']._serialized_start=1887
  _globals['_SORTCUSTOMERREFERRALSOURCEREQUEST']._serialized_end=2059
  _globals['_SORTCUSTOMERREFERRALSOURCERESPONSE']._serialized_start=2061
  _globals['_SORTCUSTOMERREFERRALSOURCERESPONSE']._serialized_end=2097
  _globals['_DELETECUSTOMERREFERRALSOURCEREQUEST']._serialized_start=2100
  _globals['_DELETECUSTOMERREFERRALSOURCEREQUEST']._serialized_end=2265
  _globals['_DELETECUSTOMERREFERRALSOURCERESPONSE']._serialized_start=2267
  _globals['_DELETECUSTOMERREFERRALSOURCERESPONSE']._serialized_end=2305
  _globals['_BUSINESSCUSTOMERREFERRALSOURCESERVICE']._serialized_start=2308
  _globals['_BUSINESSCUSTOMERREFERRALSOURCESERVICE']._serialized_end=3604
# @@protoc_insertion_point(module_scope)
