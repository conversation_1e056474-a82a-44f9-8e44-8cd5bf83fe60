# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: moego/service/business_customer/v1/business_customer_address_service.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'moego/service/business_customer/v1/business_customer_address_service.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from moego.models.business_customer.v1 import business_customer_address_defs_pb2 as moego_dot_models_dot_business__customer_dot_v1_dot_business__customer__address__defs__pb2
from moego.models.business_customer.v1 import business_customer_address_models_pb2 as moego_dot_models_dot_business__customer_dot_v1_dot_business__customer__address__models__pb2
from moego.models.organization.v1 import tenant_pb2 as moego_dot_models_dot_organization_dot_v1_dot_tenant__pb2
from validate import validate_pb2 as validate_dot_validate__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\nJmoego/service/business_customer/v1/business_customer_address_service.proto\x12\"moego.service.business_customer.v1\x1a\x46moego/models/business_customer/v1/business_customer_address_defs.proto\x1aHmoego/models/business_customer/v1/business_customer_address_models.proto\x1a)moego/models/organization/v1/tenant.proto\x1a\x17validate/validate.proto\"\xac\x01\n\x19GetCustomerAddressRequest\x12(\n\ncompany_id\x18\x01 \x01(\x03\x42\t\x18\x01\xfa\x42\x04\"\x02(\x00R\tcompanyId\x12\x41\n\x06tenant\x18\x03 \x01(\x0b\x32$.moego.models.organization.v1.TenantH\x00R\x06tenant\x88\x01\x01\x12\x17\n\x02id\x18\x02 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\x02idB\t\n\x07_tenant\"w\n\x1aGetCustomerAddressResponse\x12Y\n\x07\x61\x64\x64ress\x18\x01 \x01(\x0b\x32?.moego.models.business_customer.v1.BusinessCustomerAddressModelR\x07\x61\x64\x64ress\"\x94\x01\n\x1e\x42\x61tchGetCustomerAddressRequest\x12\x41\n\x06tenant\x18\x01 \x01(\x0b\x32$.moego.models.organization.v1.TenantH\x00R\x06tenant\x88\x01\x01\x12$\n\x03ids\x18\x02 \x03(\x03\x42\x12\xfa\x42\x0f\x92\x01\x0c\x08\x01\x10\x64\x18\x01\"\x04\"\x02 \x00R\x03idsB\t\n\x07_tenant\"\x92\x02\n\x1f\x42\x61tchGetCustomerAddressResponse\x12p\n\taddresses\x18\x01 \x03(\x0b\x32R.moego.service.business_customer.v1.BatchGetCustomerAddressResponse.AddressesEntryR\taddresses\x1a}\n\x0e\x41\x64\x64ressesEntry\x12\x10\n\x03key\x18\x01 \x01(\x03R\x03key\x12U\n\x05value\x18\x02 \x01(\x0b\x32?.moego.models.business_customer.v1.BusinessCustomerAddressModelR\x05value:\x02\x38\x01\"\xc4\x01\n GetCustomerPrimaryAddressRequest\x12(\n\ncompany_id\x18\x01 \x01(\x03\x42\t\x18\x01\xfa\x42\x04\"\x02(\x00R\tcompanyId\x12\x41\n\x06tenant\x18\x03 \x01(\x0b\x32$.moego.models.organization.v1.TenantH\x00R\x06tenant\x88\x01\x01\x12(\n\x0b\x63ustomer_id\x18\x02 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\ncustomerIdB\t\n\x07_tenant\"\x8f\x01\n!GetCustomerPrimaryAddressResponse\x12^\n\x07\x61\x64\x64ress\x18\x01 \x01(\x0b\x32?.moego.models.business_customer.v1.BusinessCustomerAddressModelH\x00R\x07\x61\x64\x64ress\x88\x01\x01\x42\n\n\x08_address\"\xd4\x01\n%BatchGetCustomerPrimaryAddressRequest\x12(\n\ncompany_id\x18\x01 \x01(\x03\x42\t\x18\x01\xfa\x42\x04\"\x02(\x00R\tcompanyId\x12\x41\n\x06tenant\x18\x03 \x01(\x0b\x32$.moego.models.organization.v1.TenantH\x00R\x06tenant\x88\x01\x01\x12\x33\n\x0c\x63ustomer_ids\x18\x02 \x03(\x03\x42\x10\xfa\x42\r\x92\x01\n\x08\x01\x18\x01\"\x04\"\x02 \x00R\x0b\x63ustomerIdsB\t\n\x07_tenant\"\xa0\x02\n&BatchGetCustomerPrimaryAddressResponse\x12w\n\taddresses\x18\x01 \x03(\x0b\x32Y.moego.service.business_customer.v1.BatchGetCustomerPrimaryAddressResponse.AddressesEntryR\taddresses\x1a}\n\x0e\x41\x64\x64ressesEntry\x12\x10\n\x03key\x18\x01 \x01(\x03R\x03key\x12U\n\x05value\x18\x02 \x01(\x0b\x32?.moego.models.business_customer.v1.BusinessCustomerAddressModelR\x05value:\x02\x38\x01\"\xbe\x01\n\x1aListCustomerAddressRequest\x12(\n\ncompany_id\x18\x01 \x01(\x03\x42\t\x18\x01\xfa\x42\x04\"\x02(\x00R\tcompanyId\x12\x41\n\x06tenant\x18\x03 \x01(\x0b\x32$.moego.models.organization.v1.TenantH\x00R\x06tenant\x88\x01\x01\x12(\n\x0b\x63ustomer_id\x18\x02 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\ncustomerIdB\t\n\x07_tenant\"|\n\x1bListCustomerAddressResponse\x12]\n\taddresses\x18\x01 \x03(\x0b\x32?.moego.models.business_customer.v1.BusinessCustomerAddressModelR\taddresses\"\xff\x01\n\x1c\x43reateCustomerAddressRequest\x12\x41\n\x06tenant\x18\x01 \x01(\x0b\x32$.moego.models.organization.v1.TenantH\x00R\x06tenant\x88\x01\x01\x12(\n\x0b\x63ustomer_id\x18\x03 \x01(\x03\x42\x07\xfa\x42\x04\"\x02(\x00R\ncustomerId\x12g\n\x07\x61\x64\x64ress\x18\x02 \x01(\x0b\x32\x43.moego.models.business_customer.v1.BusinessCustomerAddressCreateDefB\x08\xfa\x42\x05\x8a\x01\x02\x10\x01R\x07\x61\x64\x64ressB\t\n\x07_tenant\"z\n\x1d\x43reateCustomerAddressResponse\x12Y\n\x07\x61\x64\x64ress\x18\x01 \x01(\x0b\x32?.moego.models.business_customer.v1.BusinessCustomerAddressModelR\x07\x61\x64\x64ress\"\xee\x01\n\x1cUpdateCustomerAddressRequest\x12\x41\n\x06tenant\x18\x01 \x01(\x0b\x32$.moego.models.organization.v1.TenantH\x00R\x06tenant\x88\x01\x01\x12\x17\n\x02id\x18\x03 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\x02id\x12g\n\x07\x61\x64\x64ress\x18\x02 \x01(\x0b\x32\x43.moego.models.business_customer.v1.BusinessCustomerAddressUpdateDefB\x08\xfa\x42\x05\x8a\x01\x02\x10\x01R\x07\x61\x64\x64ressB\t\n\x07_tenant\"z\n\x1dUpdateCustomerAddressResponse\x12Y\n\x07\x61\x64\x64ress\x18\x01 \x01(\x0b\x32?.moego.models.business_customer.v1.BusinessCustomerAddressModelR\x07\x61\x64\x64ress\"\x85\x01\n\x1c\x44\x65leteCustomerAddressRequest\x12\x41\n\x06tenant\x18\x01 \x01(\x0b\x32$.moego.models.organization.v1.TenantH\x00R\x06tenant\x88\x01\x01\x12\x17\n\x02id\x18\x02 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\x02idB\t\n\x07_tenant\"\x1f\n\x1d\x44\x65leteCustomerAddressResponse2\xb6\n\n\x1e\x42usinessCustomerAddressService\x12\x93\x01\n\x12GetCustomerAddress\x12=.moego.service.business_customer.v1.GetCustomerAddressRequest\x1a>.moego.service.business_customer.v1.GetCustomerAddressResponse\x12\xa2\x01\n\x17\x42\x61tchGetCustomerAddress\x12\x42.moego.service.business_customer.v1.BatchGetCustomerAddressRequest\x1a\x43.moego.service.business_customer.v1.BatchGetCustomerAddressResponse\x12\xa8\x01\n\x19GetCustomerPrimaryAddress\x12\x44.moego.service.business_customer.v1.GetCustomerPrimaryAddressRequest\x1a\x45.moego.service.business_customer.v1.GetCustomerPrimaryAddressResponse\x12\xb7\x01\n\x1e\x42\x61tchGetCustomerPrimaryAddress\x12I.moego.service.business_customer.v1.BatchGetCustomerPrimaryAddressRequest\x1aJ.moego.service.business_customer.v1.BatchGetCustomerPrimaryAddressResponse\x12\x96\x01\n\x13ListCustomerAddress\x12>.moego.service.business_customer.v1.ListCustomerAddressRequest\x1a?.moego.service.business_customer.v1.ListCustomerAddressResponse\x12\x9c\x01\n\x15\x43reateCustomerAddress\<EMAIL>.business_customer.v1.CreateCustomerAddressRequest\x1a\x41.moego.service.business_customer.v1.CreateCustomerAddressResponse\x12\x9c\x01\n\x15UpdateCustomerAddress\<EMAIL>.business_customer.v1.UpdateCustomerAddressRequest\x1a\x41.moego.service.business_customer.v1.UpdateCustomerAddressResponse\x12\x9c\x01\n\x15\x44\x65leteCustomerAddress\<EMAIL>.business_customer.v1.DeleteCustomerAddressRequest\x1a\x41.moego.service.business_customer.v1.DeleteCustomerAddressResponseB\x9d\x01\n*com.moego.idl.service.business_customer.v1P\x01Zmgithub.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/business_customer/v1;businesscustomersvcpbb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'moego.service.business_customer.v1.business_customer_address_service_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n*com.moego.idl.service.business_customer.v1P\001Zmgithub.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/business_customer/v1;businesscustomersvcpb'
  _globals['_GETCUSTOMERADDRESSREQUEST'].fields_by_name['company_id']._loaded_options = None
  _globals['_GETCUSTOMERADDRESSREQUEST'].fields_by_name['company_id']._serialized_options = b'\030\001\372B\004\"\002(\000'
  _globals['_GETCUSTOMERADDRESSREQUEST'].fields_by_name['id']._loaded_options = None
  _globals['_GETCUSTOMERADDRESSREQUEST'].fields_by_name['id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_BATCHGETCUSTOMERADDRESSREQUEST'].fields_by_name['ids']._loaded_options = None
  _globals['_BATCHGETCUSTOMERADDRESSREQUEST'].fields_by_name['ids']._serialized_options = b'\372B\017\222\001\014\010\001\020d\030\001\"\004\"\002 \000'
  _globals['_BATCHGETCUSTOMERADDRESSRESPONSE_ADDRESSESENTRY']._loaded_options = None
  _globals['_BATCHGETCUSTOMERADDRESSRESPONSE_ADDRESSESENTRY']._serialized_options = b'8\001'
  _globals['_GETCUSTOMERPRIMARYADDRESSREQUEST'].fields_by_name['company_id']._loaded_options = None
  _globals['_GETCUSTOMERPRIMARYADDRESSREQUEST'].fields_by_name['company_id']._serialized_options = b'\030\001\372B\004\"\002(\000'
  _globals['_GETCUSTOMERPRIMARYADDRESSREQUEST'].fields_by_name['customer_id']._loaded_options = None
  _globals['_GETCUSTOMERPRIMARYADDRESSREQUEST'].fields_by_name['customer_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_BATCHGETCUSTOMERPRIMARYADDRESSREQUEST'].fields_by_name['company_id']._loaded_options = None
  _globals['_BATCHGETCUSTOMERPRIMARYADDRESSREQUEST'].fields_by_name['company_id']._serialized_options = b'\030\001\372B\004\"\002(\000'
  _globals['_BATCHGETCUSTOMERPRIMARYADDRESSREQUEST'].fields_by_name['customer_ids']._loaded_options = None
  _globals['_BATCHGETCUSTOMERPRIMARYADDRESSREQUEST'].fields_by_name['customer_ids']._serialized_options = b'\372B\r\222\001\n\010\001\030\001\"\004\"\002 \000'
  _globals['_BATCHGETCUSTOMERPRIMARYADDRESSRESPONSE_ADDRESSESENTRY']._loaded_options = None
  _globals['_BATCHGETCUSTOMERPRIMARYADDRESSRESPONSE_ADDRESSESENTRY']._serialized_options = b'8\001'
  _globals['_LISTCUSTOMERADDRESSREQUEST'].fields_by_name['company_id']._loaded_options = None
  _globals['_LISTCUSTOMERADDRESSREQUEST'].fields_by_name['company_id']._serialized_options = b'\030\001\372B\004\"\002(\000'
  _globals['_LISTCUSTOMERADDRESSREQUEST'].fields_by_name['customer_id']._loaded_options = None
  _globals['_LISTCUSTOMERADDRESSREQUEST'].fields_by_name['customer_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_CREATECUSTOMERADDRESSREQUEST'].fields_by_name['customer_id']._loaded_options = None
  _globals['_CREATECUSTOMERADDRESSREQUEST'].fields_by_name['customer_id']._serialized_options = b'\372B\004\"\002(\000'
  _globals['_CREATECUSTOMERADDRESSREQUEST'].fields_by_name['address']._loaded_options = None
  _globals['_CREATECUSTOMERADDRESSREQUEST'].fields_by_name['address']._serialized_options = b'\372B\005\212\001\002\020\001'
  _globals['_UPDATECUSTOMERADDRESSREQUEST'].fields_by_name['id']._loaded_options = None
  _globals['_UPDATECUSTOMERADDRESSREQUEST'].fields_by_name['id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_UPDATECUSTOMERADDRESSREQUEST'].fields_by_name['address']._loaded_options = None
  _globals['_UPDATECUSTOMERADDRESSREQUEST'].fields_by_name['address']._serialized_options = b'\372B\005\212\001\002\020\001'
  _globals['_DELETECUSTOMERADDRESSREQUEST'].fields_by_name['id']._loaded_options = None
  _globals['_DELETECUSTOMERADDRESSREQUEST'].fields_by_name['id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_GETCUSTOMERADDRESSREQUEST']._serialized_start=329
  _globals['_GETCUSTOMERADDRESSREQUEST']._serialized_end=501
  _globals['_GETCUSTOMERADDRESSRESPONSE']._serialized_start=503
  _globals['_GETCUSTOMERADDRESSRESPONSE']._serialized_end=622
  _globals['_BATCHGETCUSTOMERADDRESSREQUEST']._serialized_start=625
  _globals['_BATCHGETCUSTOMERADDRESSREQUEST']._serialized_end=773
  _globals['_BATCHGETCUSTOMERADDRESSRESPONSE']._serialized_start=776
  _globals['_BATCHGETCUSTOMERADDRESSRESPONSE']._serialized_end=1050
  _globals['_BATCHGETCUSTOMERADDRESSRESPONSE_ADDRESSESENTRY']._serialized_start=925
  _globals['_BATCHGETCUSTOMERADDRESSRESPONSE_ADDRESSESENTRY']._serialized_end=1050
  _globals['_GETCUSTOMERPRIMARYADDRESSREQUEST']._serialized_start=1053
  _globals['_GETCUSTOMERPRIMARYADDRESSREQUEST']._serialized_end=1249
  _globals['_GETCUSTOMERPRIMARYADDRESSRESPONSE']._serialized_start=1252
  _globals['_GETCUSTOMERPRIMARYADDRESSRESPONSE']._serialized_end=1395
  _globals['_BATCHGETCUSTOMERPRIMARYADDRESSREQUEST']._serialized_start=1398
  _globals['_BATCHGETCUSTOMERPRIMARYADDRESSREQUEST']._serialized_end=1610
  _globals['_BATCHGETCUSTOMERPRIMARYADDRESSRESPONSE']._serialized_start=1613
  _globals['_BATCHGETCUSTOMERPRIMARYADDRESSRESPONSE']._serialized_end=1901
  _globals['_BATCHGETCUSTOMERPRIMARYADDRESSRESPONSE_ADDRESSESENTRY']._serialized_start=925
  _globals['_BATCHGETCUSTOMERPRIMARYADDRESSRESPONSE_ADDRESSESENTRY']._serialized_end=1050
  _globals['_LISTCUSTOMERADDRESSREQUEST']._serialized_start=1904
  _globals['_LISTCUSTOMERADDRESSREQUEST']._serialized_end=2094
  _globals['_LISTCUSTOMERADDRESSRESPONSE']._serialized_start=2096
  _globals['_LISTCUSTOMERADDRESSRESPONSE']._serialized_end=2220
  _globals['_CREATECUSTOMERADDRESSREQUEST']._serialized_start=2223
  _globals['_CREATECUSTOMERADDRESSREQUEST']._serialized_end=2478
  _globals['_CREATECUSTOMERADDRESSRESPONSE']._serialized_start=2480
  _globals['_CREATECUSTOMERADDRESSRESPONSE']._serialized_end=2602
  _globals['_UPDATECUSTOMERADDRESSREQUEST']._serialized_start=2605
  _globals['_UPDATECUSTOMERADDRESSREQUEST']._serialized_end=2843
  _globals['_UPDATECUSTOMERADDRESSRESPONSE']._serialized_start=2845
  _globals['_UPDATECUSTOMERADDRESSRESPONSE']._serialized_end=2967
  _globals['_DELETECUSTOMERADDRESSREQUEST']._serialized_start=2970
  _globals['_DELETECUSTOMERADDRESSREQUEST']._serialized_end=3103
  _globals['_DELETECUSTOMERADDRESSRESPONSE']._serialized_start=3105
  _globals['_DELETECUSTOMERADDRESSRESPONSE']._serialized_end=3136
  _globals['_BUSINESSCUSTOMERADDRESSSERVICE']._serialized_start=3139
  _globals['_BUSINESSCUSTOMERADDRESSSERVICE']._serialized_end=4473
# @@protoc_insertion_point(module_scope)
