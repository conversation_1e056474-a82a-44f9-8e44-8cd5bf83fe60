from moego.models.business_customer.v1 import business_customer_referral_source_defs_pb2 as _business_customer_referral_source_defs_pb2
from moego.models.business_customer.v1 import business_customer_referral_source_models_pb2 as _business_customer_referral_source_models_pb2
from validate import validate_pb2 as _validate_pb2
from google.protobuf.internal import containers as _containers
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from collections.abc import Iterable as _Iterable, Mapping as _Mapping
from typing import ClassVar as _ClassVar, Optional as _Optional, Union as _Union

DESCRIPTOR: _descriptor.FileDescriptor

class GetCustomerReferralSourceRequest(_message.Message):
    __slots__ = ("id", "company_id", "business_id")
    ID_FIELD_NUMBER: _ClassVar[int]
    COMPANY_ID_FIELD_NUMBER: _ClassVar[int]
    BUSINESS_ID_FIELD_NUMBER: _ClassVar[int]
    id: int
    company_id: int
    business_id: int
    def __init__(self, id: _Optional[int] = ..., company_id: _Optional[int] = ..., business_id: _Optional[int] = ...) -> None: ...

class GetCustomerReferralSourceResponse(_message.Message):
    __slots__ = ("referral_source",)
    REFERRAL_SOURCE_FIELD_NUMBER: _ClassVar[int]
    referral_source: _business_customer_referral_source_models_pb2.BusinessCustomerReferralSourceModel
    def __init__(self, referral_source: _Optional[_Union[_business_customer_referral_source_models_pb2.BusinessCustomerReferralSourceModel, _Mapping]] = ...) -> None: ...

class ListCustomerReferralSourceRequest(_message.Message):
    __slots__ = ("company_id", "business_id")
    COMPANY_ID_FIELD_NUMBER: _ClassVar[int]
    BUSINESS_ID_FIELD_NUMBER: _ClassVar[int]
    company_id: int
    business_id: int
    def __init__(self, company_id: _Optional[int] = ..., business_id: _Optional[int] = ...) -> None: ...

class ListCustomerReferralSourceResponse(_message.Message):
    __slots__ = ("referral_sources",)
    REFERRAL_SOURCES_FIELD_NUMBER: _ClassVar[int]
    referral_sources: _containers.RepeatedCompositeFieldContainer[_business_customer_referral_source_models_pb2.BusinessCustomerReferralSourceModel]
    def __init__(self, referral_sources: _Optional[_Iterable[_Union[_business_customer_referral_source_models_pb2.BusinessCustomerReferralSourceModel, _Mapping]]] = ...) -> None: ...

class ListCustomerReferralSourceTemplateRequest(_message.Message):
    __slots__ = ()
    def __init__(self) -> None: ...

class ListCustomerReferralSourceTemplateResponse(_message.Message):
    __slots__ = ("referral_sources",)
    REFERRAL_SOURCES_FIELD_NUMBER: _ClassVar[int]
    referral_sources: _containers.RepeatedCompositeFieldContainer[_business_customer_referral_source_models_pb2.BusinessCustomerReferralSourceModel]
    def __init__(self, referral_sources: _Optional[_Iterable[_Union[_business_customer_referral_source_models_pb2.BusinessCustomerReferralSourceModel, _Mapping]]] = ...) -> None: ...

class CreateCustomerReferralSourceRequest(_message.Message):
    __slots__ = ("company_id", "business_id", "referral_source")
    COMPANY_ID_FIELD_NUMBER: _ClassVar[int]
    BUSINESS_ID_FIELD_NUMBER: _ClassVar[int]
    REFERRAL_SOURCE_FIELD_NUMBER: _ClassVar[int]
    company_id: int
    business_id: int
    referral_source: _business_customer_referral_source_defs_pb2.BusinessCustomerReferralSourceCreateDef
    def __init__(self, company_id: _Optional[int] = ..., business_id: _Optional[int] = ..., referral_source: _Optional[_Union[_business_customer_referral_source_defs_pb2.BusinessCustomerReferralSourceCreateDef, _Mapping]] = ...) -> None: ...

class CreateCustomerReferralSourceResponse(_message.Message):
    __slots__ = ("referral_source",)
    REFERRAL_SOURCE_FIELD_NUMBER: _ClassVar[int]
    referral_source: _business_customer_referral_source_models_pb2.BusinessCustomerReferralSourceModel
    def __init__(self, referral_source: _Optional[_Union[_business_customer_referral_source_models_pb2.BusinessCustomerReferralSourceModel, _Mapping]] = ...) -> None: ...

class UpdateCustomerReferralSourceRequest(_message.Message):
    __slots__ = ("id", "company_id", "business_id", "referral_source")
    ID_FIELD_NUMBER: _ClassVar[int]
    COMPANY_ID_FIELD_NUMBER: _ClassVar[int]
    BUSINESS_ID_FIELD_NUMBER: _ClassVar[int]
    REFERRAL_SOURCE_FIELD_NUMBER: _ClassVar[int]
    id: int
    company_id: int
    business_id: int
    referral_source: _business_customer_referral_source_defs_pb2.BusinessCustomerReferralSourceUpdateDef
    def __init__(self, id: _Optional[int] = ..., company_id: _Optional[int] = ..., business_id: _Optional[int] = ..., referral_source: _Optional[_Union[_business_customer_referral_source_defs_pb2.BusinessCustomerReferralSourceUpdateDef, _Mapping]] = ...) -> None: ...

class UpdateCustomerReferralSourceResponse(_message.Message):
    __slots__ = ()
    def __init__(self) -> None: ...

class SortCustomerReferralSourceRequest(_message.Message):
    __slots__ = ("ids", "company_id", "business_id")
    IDS_FIELD_NUMBER: _ClassVar[int]
    COMPANY_ID_FIELD_NUMBER: _ClassVar[int]
    BUSINESS_ID_FIELD_NUMBER: _ClassVar[int]
    ids: _containers.RepeatedScalarFieldContainer[int]
    company_id: int
    business_id: int
    def __init__(self, ids: _Optional[_Iterable[int]] = ..., company_id: _Optional[int] = ..., business_id: _Optional[int] = ...) -> None: ...

class SortCustomerReferralSourceResponse(_message.Message):
    __slots__ = ()
    def __init__(self) -> None: ...

class DeleteCustomerReferralSourceRequest(_message.Message):
    __slots__ = ("id", "company_id", "business_id")
    ID_FIELD_NUMBER: _ClassVar[int]
    COMPANY_ID_FIELD_NUMBER: _ClassVar[int]
    BUSINESS_ID_FIELD_NUMBER: _ClassVar[int]
    id: int
    company_id: int
    business_id: int
    def __init__(self, id: _Optional[int] = ..., company_id: _Optional[int] = ..., business_id: _Optional[int] = ...) -> None: ...

class DeleteCustomerReferralSourceResponse(_message.Message):
    __slots__ = ()
    def __init__(self) -> None: ...
