# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: moego/service/business_customer/v1/business_customer_preferred_frequency_service.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'moego/service/business_customer/v1/business_customer_preferred_frequency_service.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from moego.models.organization.v1 import tenant_pb2 as moego_dot_models_dot_organization_dot_v1_dot_tenant__pb2
from moego.utils.v1 import time_period_pb2 as moego_dot_utils_dot_v1_dot_time__period__pb2
from validate import validate_pb2 as validate_dot_validate__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\nVmoego/service/business_customer/v1/business_customer_preferred_frequency_service.proto\x12\"moego.service.business_customer.v1\x1a)moego/models/organization/v1/tenant.proto\x1a moego/utils/v1/time_period.proto\x1a\x17validate/validate.proto\"\xce\x01\n#GetCustomerGroomingFrequencyRequest\x12(\n\ncompany_id\x18\x01 \x01(\x03\x42\t\x18\x01\xfa\x42\x04\"\x02(\x00R\tcompanyId\x12/\n\x0b\x62usiness_id\x18\x02 \x01(\x03\x42\t\x18\x01\xfa\x42\x04\"\x02(\x00H\x00R\nbusinessId\x88\x01\x01\x12<\n\x06tenant\x18\x03 \x01(\x0b\x32$.moego.models.organization.v1.TenantR\x06tenantB\x0e\n\x0c_business_id\"q\n$GetCustomerGroomingFrequencyResponse\x12I\n\x12grooming_frequency\x18\x01 \x01(\x0b\x32\x1a.moego.utils.v1.TimePeriodR\x11groomingFrequency\"\xdb\x02\n&UpsertCustomerGroomingFrequencyRequest\x12(\n\ncompany_id\x18\x01 \x01(\x03\x42\t\x18\x01\xfa\x42\x04\"\x02(\x00R\tcompanyId\x12/\n\x0b\x62usiness_id\x18\x02 \x01(\x03\x42\t\x18\x01\xfa\x42\x04\"\x02(\x00H\x00R\nbusinessId\x88\x01\x01\x12<\n\x06tenant\x18\x05 \x01(\x0b\x32$.moego.models.organization.v1.TenantR\x06tenant\x12S\n\x12grooming_frequency\x18\x03 \x01(\x0b\x32\x1a.moego.utils.v1.TimePeriodB\x08\xfa\x42\x05\x8a\x01\x02\x10\x01R\x11groomingFrequency\x12\x33\n\x16\x61pply_to_all_customers\x18\x04 \x01(\x08R\x13\x61pplyToAllCustomersB\x0e\n\x0c_business_id\")\n\'UpsertCustomerGroomingFrequencyResponse2\x9c\x03\n)BusinessCustomerPreferredFrequencyService\x12\xb1\x01\n\x1cGetCustomerGroomingFrequency\x12G.moego.service.business_customer.v1.GetCustomerGroomingFrequencyRequest\x1aH.moego.service.business_customer.v1.GetCustomerGroomingFrequencyResponse\x12\xba\x01\n\x1fUpsertCustomerGroomingFrequency\x12J.moego.service.business_customer.v1.UpsertCustomerGroomingFrequencyRequest\x1aK.moego.service.business_customer.v1.UpsertCustomerGroomingFrequencyResponseB\x9d\x01\n*com.moego.idl.service.business_customer.v1P\x01Zmgithub.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/business_customer/v1;businesscustomersvcpbb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'moego.service.business_customer.v1.business_customer_preferred_frequency_service_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n*com.moego.idl.service.business_customer.v1P\001Zmgithub.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/business_customer/v1;businesscustomersvcpb'
  _globals['_GETCUSTOMERGROOMINGFREQUENCYREQUEST'].fields_by_name['company_id']._loaded_options = None
  _globals['_GETCUSTOMERGROOMINGFREQUENCYREQUEST'].fields_by_name['company_id']._serialized_options = b'\030\001\372B\004\"\002(\000'
  _globals['_GETCUSTOMERGROOMINGFREQUENCYREQUEST'].fields_by_name['business_id']._loaded_options = None
  _globals['_GETCUSTOMERGROOMINGFREQUENCYREQUEST'].fields_by_name['business_id']._serialized_options = b'\030\001\372B\004\"\002(\000'
  _globals['_UPSERTCUSTOMERGROOMINGFREQUENCYREQUEST'].fields_by_name['company_id']._loaded_options = None
  _globals['_UPSERTCUSTOMERGROOMINGFREQUENCYREQUEST'].fields_by_name['company_id']._serialized_options = b'\030\001\372B\004\"\002(\000'
  _globals['_UPSERTCUSTOMERGROOMINGFREQUENCYREQUEST'].fields_by_name['business_id']._loaded_options = None
  _globals['_UPSERTCUSTOMERGROOMINGFREQUENCYREQUEST'].fields_by_name['business_id']._serialized_options = b'\030\001\372B\004\"\002(\000'
  _globals['_UPSERTCUSTOMERGROOMINGFREQUENCYREQUEST'].fields_by_name['grooming_frequency']._loaded_options = None
  _globals['_UPSERTCUSTOMERGROOMINGFREQUENCYREQUEST'].fields_by_name['grooming_frequency']._serialized_options = b'\372B\005\212\001\002\020\001'
  _globals['_GETCUSTOMERGROOMINGFREQUENCYREQUEST']._serialized_start=229
  _globals['_GETCUSTOMERGROOMINGFREQUENCYREQUEST']._serialized_end=435
  _globals['_GETCUSTOMERGROOMINGFREQUENCYRESPONSE']._serialized_start=437
  _globals['_GETCUSTOMERGROOMINGFREQUENCYRESPONSE']._serialized_end=550
  _globals['_UPSERTCUSTOMERGROOMINGFREQUENCYREQUEST']._serialized_start=553
  _globals['_UPSERTCUSTOMERGROOMINGFREQUENCYREQUEST']._serialized_end=900
  _globals['_UPSERTCUSTOMERGROOMINGFREQUENCYRESPONSE']._serialized_start=902
  _globals['_UPSERTCUSTOMERGROOMINGFREQUENCYRESPONSE']._serialized_end=943
  _globals['_BUSINESSCUSTOMERPREFERREDFREQUENCYSERVICE']._serialized_start=946
  _globals['_BUSINESSCUSTOMERPREFERREDFREQUENCYSERVICE']._serialized_end=1358
# @@protoc_insertion_point(module_scope)
