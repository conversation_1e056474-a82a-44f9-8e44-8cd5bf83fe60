# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: moego/service/llm/v1/conversation_service.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'moego/service/llm/v1/conversation_service.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from moego.models.llm.v1 import conversation_defs_pb2 as moego_dot_models_dot_llm_dot_v1_dot_conversation__defs__pb2
from moego.models.llm.v1 import conversation_models_pb2 as moego_dot_models_dot_llm_dot_v1_dot_conversation__models__pb2
from validate import validate_pb2 as validate_dot_validate__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n/moego/service/llm/v1/conversation_service.proto\x12\x14moego.service.llm.v1\x1a+moego/models/llm/v1/conversation_defs.proto\x1a-moego/models/llm/v1/conversation_models.proto\x1a\x17validate/validate.proto\"\xab\x02\n\x1b\x43ompleteConversationRequest\x12\x35\n\x0b\x61pplication\x18\x01 \x01(\tB\x13\xfa\x42\x10r\x0eR\x0c\x61i-assistantR\x0b\x61pplication\x12\x30\n\x0f\x63onversation_id\x18\x02 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\x0e\x63onversationId\x12>\n\x0btemperature\x18\x03 \x01(\x01\x42\x17\xfa\x42\x14\x12\x12\x19\x00\x00\x00\x00\x00\x00\x00@)\x00\x00\x00\x00\x00\x00\x00\x00H\x00R\x0btemperature\x88\x01\x01\x12S\n\x08messages\x18\x04 \x03(\x0b\x32+.moego.models.llm.v1.ConversationMessageDefB\n\xfa\x42\x07\x92\x01\x04\x08\x01\x10\x32R\x08messagesB\x0e\n\x0c_temperature2\x92\x01\n\x13\x43onversationService\x12{\n\x14\x43ompleteConversation\x12\x31.moego.service.llm.v1.CompleteConversationRequest\x1a\x30.moego.models.llm.v1.ConversationCompletionModelBt\n\x1c\x63om.moego.idl.service.llm.v1P\x01ZRgithub.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/llm/v1;llmsvcpbb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'moego.service.llm.v1.conversation_service_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n\034com.moego.idl.service.llm.v1P\001ZRgithub.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/llm/v1;llmsvcpb'
  _globals['_COMPLETECONVERSATIONREQUEST'].fields_by_name['application']._loaded_options = None
  _globals['_COMPLETECONVERSATIONREQUEST'].fields_by_name['application']._serialized_options = b'\372B\020r\016R\014ai-assistant'
  _globals['_COMPLETECONVERSATIONREQUEST'].fields_by_name['conversation_id']._loaded_options = None
  _globals['_COMPLETECONVERSATIONREQUEST'].fields_by_name['conversation_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_COMPLETECONVERSATIONREQUEST'].fields_by_name['temperature']._loaded_options = None
  _globals['_COMPLETECONVERSATIONREQUEST'].fields_by_name['temperature']._serialized_options = b'\372B\024\022\022\031\000\000\000\000\000\000\000@)\000\000\000\000\000\000\000\000'
  _globals['_COMPLETECONVERSATIONREQUEST'].fields_by_name['messages']._loaded_options = None
  _globals['_COMPLETECONVERSATIONREQUEST'].fields_by_name['messages']._serialized_options = b'\372B\007\222\001\004\010\001\0202'
  _globals['_COMPLETECONVERSATIONREQUEST']._serialized_start=191
  _globals['_COMPLETECONVERSATIONREQUEST']._serialized_end=490
  _globals['_CONVERSATIONSERVICE']._serialized_start=493
  _globals['_CONVERSATIONSERVICE']._serialized_end=639
# @@protoc_insertion_point(module_scope)
