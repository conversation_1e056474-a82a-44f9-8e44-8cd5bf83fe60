# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: moego/service/online_booking/v1/medication_service.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'moego/service/online_booking/v1/medication_service.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2
from moego.models.appointment.v1 import appointment_pet_medication_schedule_defs_pb2 as moego_dot_models_dot_appointment_dot_v1_dot_appointment__pet__medication__schedule__defs__pb2
from moego.models.online_booking.v1 import medication_models_pb2 as moego_dot_models_dot_online__booking_dot_v1_dot_medication__models__pb2
from validate import validate_pb2 as validate_dot_validate__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n8moego/service/online_booking/v1/medication_service.proto\x12\x1fmoego.service.online_booking.v1\x1a\x1fgoogle/protobuf/timestamp.proto\x1aJmoego/models/appointment/v1/appointment_pet_medication_schedule_defs.proto\x1a\x36moego/models/online_booking/v1/medication_models.proto\x1a\x17validate/validate.proto\"\xad\x05\n\x17\x43reateMedicationRequest\x12V\n\x04time\x18\x04 \x03(\x0b\x32\x42.moego.models.online_booking.v1.MedicationModel.MedicationScheduleR\x04time\x12\x1f\n\x06\x61mount\x18\x05 \x01(\x01\x42\x02\x18\x01H\x00R\x06\x61mount\x88\x01\x01\x12!\n\x04unit\x18\x06 \x01(\tB\x08\xfa\x42\x05r\x03\x18\x80\x10H\x01R\x04unit\x88\x01\x01\x12\x36\n\x0fmedication_name\x18\x07 \x01(\tB\x08\xfa\x42\x05r\x03\x18\x80\x10H\x02R\x0emedicationName\x88\x01\x01\x12#\n\x05notes\x18\x08 \x01(\tB\x08\xfa\x42\x05r\x03\x18\x80\x10H\x03R\x05notes\x88\x01\x01\x12>\n\ncreated_at\x18\t \x01(\x0b\x32\x1a.google.protobuf.TimestampH\x04R\tcreatedAt\x88\x01\x01\x12>\n\nupdated_at\x18\n \x01(\x0b\x32\x1a.google.protobuf.TimestampH\x05R\tupdatedAt\x88\x01\x01\x12,\n\namount_str\x18\x0b \x01(\tB\x08\xfa\x42\x05r\x03\x18\xff\x01H\x06R\tamountStr\x88\x01\x01\x12z\n\rselected_date\x18\x0c \x01(\x0b\x32P.moego.models.appointment.v1.AppointmentPetMedicationScheduleDef.SelectedDateDefH\x07R\x0cselectedDate\x88\x01\x01\x42\t\n\x07_amountB\x07\n\x05_unitB\x12\n\x10_medication_nameB\x08\n\x06_notesB\r\n\x0b_created_atB\r\n\x0b_updated_atB\r\n\x0b_amount_strB\x10\n\x0e_selected_date\"o\n\x1b\x43reateMedicationRequestList\x12P\n\x06values\x18\x01 \x03(\x0b\x32\x38.moego.service.online_booking.v1.CreateMedicationRequestR\x06values2\x13\n\x11MedicationServiceB\x94\x01\n\'com.moego.idl.service.online_booking.v1P\x01Zggithub.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/online_booking/v1;onlinebookingsvcpbb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'moego.service.online_booking.v1.medication_service_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n\'com.moego.idl.service.online_booking.v1P\001Zggithub.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/online_booking/v1;onlinebookingsvcpb'
  _globals['_CREATEMEDICATIONREQUEST'].fields_by_name['amount']._loaded_options = None
  _globals['_CREATEMEDICATIONREQUEST'].fields_by_name['amount']._serialized_options = b'\030\001'
  _globals['_CREATEMEDICATIONREQUEST'].fields_by_name['unit']._loaded_options = None
  _globals['_CREATEMEDICATIONREQUEST'].fields_by_name['unit']._serialized_options = b'\372B\005r\003\030\200\020'
  _globals['_CREATEMEDICATIONREQUEST'].fields_by_name['medication_name']._loaded_options = None
  _globals['_CREATEMEDICATIONREQUEST'].fields_by_name['medication_name']._serialized_options = b'\372B\005r\003\030\200\020'
  _globals['_CREATEMEDICATIONREQUEST'].fields_by_name['notes']._loaded_options = None
  _globals['_CREATEMEDICATIONREQUEST'].fields_by_name['notes']._serialized_options = b'\372B\005r\003\030\200\020'
  _globals['_CREATEMEDICATIONREQUEST'].fields_by_name['amount_str']._loaded_options = None
  _globals['_CREATEMEDICATIONREQUEST'].fields_by_name['amount_str']._serialized_options = b'\372B\005r\003\030\377\001'
  _globals['_CREATEMEDICATIONREQUEST']._serialized_start=284
  _globals['_CREATEMEDICATIONREQUEST']._serialized_end=969
  _globals['_CREATEMEDICATIONREQUESTLIST']._serialized_start=971
  _globals['_CREATEMEDICATIONREQUESTLIST']._serialized_end=1082
  _globals['_MEDICATIONSERVICE']._serialized_start=1084
  _globals['_MEDICATIONSERVICE']._serialized_end=1103
# @@protoc_insertion_point(module_scope)
