# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc



class EvaluationTestDetailServiceStub(object):
    """EvaluationTestDetail service
    """

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """


class EvaluationTestDetailServiceServicer(object):
    """EvaluationTestDetail service
    """


def add_EvaluationTestDetailServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'moego.service.online_booking.v1.EvaluationTestDetailService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('moego.service.online_booking.v1.EvaluationTestDetailService', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class EvaluationTestDetailService(object):
    """EvaluationTestDetail service
    """
