# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

from moego.service.enterprise.v1 import staff_service_pb2 as moego_dot_service_dot_enterprise_dot_v1_dot_staff__service__pb2


class StaffServiceStub(object):
    """staff access service
    """

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.ListStaffs = channel.unary_unary(
                '/moego.service.enterprise.v1.StaffService/ListStaffs',
                request_serializer=moego_dot_service_dot_enterprise_dot_v1_dot_staff__service__pb2.ListStaffsRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_enterprise_dot_v1_dot_staff__service__pb2.ListStaffsResponse.FromString,
                _registered_method=True)
        self.CreateStaff = channel.unary_unary(
                '/moego.service.enterprise.v1.StaffService/CreateStaff',
                request_serializer=moego_dot_service_dot_enterprise_dot_v1_dot_staff__service__pb2.CreateStaffRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_enterprise_dot_v1_dot_staff__service__pb2.CreateStaffResponse.FromString,
                _registered_method=True)
        self.UpdateStaff = channel.unary_unary(
                '/moego.service.enterprise.v1.StaffService/UpdateStaff',
                request_serializer=moego_dot_service_dot_enterprise_dot_v1_dot_staff__service__pb2.UpdateStaffRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_enterprise_dot_v1_dot_staff__service__pb2.UpdateStaffResponse.FromString,
                _registered_method=True)
        self.DeleteStaff = channel.unary_unary(
                '/moego.service.enterprise.v1.StaffService/DeleteStaff',
                request_serializer=moego_dot_service_dot_enterprise_dot_v1_dot_staff__service__pb2.DeleteStaffRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_enterprise_dot_v1_dot_staff__service__pb2.DeleteStaffResponse.FromString,
                _registered_method=True)


class StaffServiceServicer(object):
    """staff access service
    """

    def ListStaffs(self, request, context):
        """list staff access tenant
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def CreateStaff(self, request, context):
        """create  staff
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def UpdateStaff(self, request, context):
        """update  staff
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DeleteStaff(self, request, context):
        """delete  staff
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_StaffServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'ListStaffs': grpc.unary_unary_rpc_method_handler(
                    servicer.ListStaffs,
                    request_deserializer=moego_dot_service_dot_enterprise_dot_v1_dot_staff__service__pb2.ListStaffsRequest.FromString,
                    response_serializer=moego_dot_service_dot_enterprise_dot_v1_dot_staff__service__pb2.ListStaffsResponse.SerializeToString,
            ),
            'CreateStaff': grpc.unary_unary_rpc_method_handler(
                    servicer.CreateStaff,
                    request_deserializer=moego_dot_service_dot_enterprise_dot_v1_dot_staff__service__pb2.CreateStaffRequest.FromString,
                    response_serializer=moego_dot_service_dot_enterprise_dot_v1_dot_staff__service__pb2.CreateStaffResponse.SerializeToString,
            ),
            'UpdateStaff': grpc.unary_unary_rpc_method_handler(
                    servicer.UpdateStaff,
                    request_deserializer=moego_dot_service_dot_enterprise_dot_v1_dot_staff__service__pb2.UpdateStaffRequest.FromString,
                    response_serializer=moego_dot_service_dot_enterprise_dot_v1_dot_staff__service__pb2.UpdateStaffResponse.SerializeToString,
            ),
            'DeleteStaff': grpc.unary_unary_rpc_method_handler(
                    servicer.DeleteStaff,
                    request_deserializer=moego_dot_service_dot_enterprise_dot_v1_dot_staff__service__pb2.DeleteStaffRequest.FromString,
                    response_serializer=moego_dot_service_dot_enterprise_dot_v1_dot_staff__service__pb2.DeleteStaffResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'moego.service.enterprise.v1.StaffService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('moego.service.enterprise.v1.StaffService', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class StaffService(object):
    """staff access service
    """

    @staticmethod
    def ListStaffs(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.enterprise.v1.StaffService/ListStaffs',
            moego_dot_service_dot_enterprise_dot_v1_dot_staff__service__pb2.ListStaffsRequest.SerializeToString,
            moego_dot_service_dot_enterprise_dot_v1_dot_staff__service__pb2.ListStaffsResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def CreateStaff(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.enterprise.v1.StaffService/CreateStaff',
            moego_dot_service_dot_enterprise_dot_v1_dot_staff__service__pb2.CreateStaffRequest.SerializeToString,
            moego_dot_service_dot_enterprise_dot_v1_dot_staff__service__pb2.CreateStaffResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def UpdateStaff(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.enterprise.v1.StaffService/UpdateStaff',
            moego_dot_service_dot_enterprise_dot_v1_dot_staff__service__pb2.UpdateStaffRequest.SerializeToString,
            moego_dot_service_dot_enterprise_dot_v1_dot_staff__service__pb2.UpdateStaffResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def DeleteStaff(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.enterprise.v1.StaffService/DeleteStaff',
            moego_dot_service_dot_enterprise_dot_v1_dot_staff__service__pb2.DeleteStaffRequest.SerializeToString,
            moego_dot_service_dot_enterprise_dot_v1_dot_staff__service__pb2.DeleteStaffResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)
