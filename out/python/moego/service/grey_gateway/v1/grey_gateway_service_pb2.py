# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: moego/service/grey_gateway/v1/grey_gateway_service.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'moego/service/grey_gateway/v1/grey_gateway_service.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.protobuf import empty_pb2 as google_dot_protobuf_dot_empty__pb2
from google.protobuf import wrappers_pb2 as google_dot_protobuf_dot_wrappers__pb2
from moego.models.grey_gateway.v1 import grey_gateway_models_pb2 as moego_dot_models_dot_grey__gateway_dot_v1_dot_grey__gateway__models__pb2
from moego.utils.v1 import id_messages_pb2 as moego_dot_utils_dot_v1_dot_id__messages__pb2
from validate import validate_pb2 as validate_dot_validate__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n8moego/service/grey_gateway/v1/grey_gateway_service.proto\x12\x1dmoego.service.grey_gateway.v1\x1a\x1bgoogle/protobuf/empty.proto\x1a\x1egoogle/protobuf/wrappers.proto\x1a\x36moego/models/grey_gateway/v1/grey_gateway_models.proto\x1a moego/utils/v1/id_messages.proto\x1a\x17validate/validate.proto\"\xc4\x02\n\x1bGetServiceBranchMapResponse\x12x\n\x10svc_branches_map\x18\x01 \x03(\x0b\x32N.moego.service.grey_gateway.v1.GetServiceBranchMapResponse.SvcBranchesMapEntryR\x0esvcBranchesMap\x1a \n\nBranchList\x12\x12\n\x04name\x18\x01 \x03(\tR\x04name\x1a\x88\x01\n\x13SvcBranchesMapEntry\x12\x10\n\x03key\x18\x01 \x01(\tR\x03key\x12[\n\x05value\x18\x02 \x01(\x0b\x32\x45.moego.service.grey_gateway.v1.GetServiceBranchMapResponse.BranchListR\x05value:\x02\x38\x01\"E\n\x1aGetServiceBranchMapRequest\x12\'\n\tnamespace\x18\x01 \x01(\tB\t\xfa\x42\x06r\x04 \x01(\x1eR\tnamespace\"A\n\x16GetGreyItemListRequest\x12\'\n\tnamespace\x18\x01 \x01(\tB\t\xfa\x42\x06r\x04 \x01(\x1eR\tnamespace\"\x89\x03\n\x15InsertGreyItemRequest\x12\x1d\n\x04name\x18\x02 \x01(\tB\t\xfa\x42\x06r\x04 \x01(\x1eR\x04name\x12\'\n\tnamespace\x18\x03 \x01(\tB\t\xfa\x42\x06r\x04 \x01(\x1eR\tnamespace\x12>\n\x0b\x64\x65scription\x18\x05 \x01(\x0b\x32\x1c.google.protobuf.StringValueR\x0b\x64\x65scription\x12-\n\x0cjira_tickets\x18\n \x03(\tB\n\xfa\x42\x07\x92\x01\x04\x08\x00(\x01R\x0bjiraTickets\x12x\n\x0esvc_branch_map\x18\x0f \x03(\x0b\x32\x46.moego.service.grey_gateway.v1.InsertGreyItemRequest.SvcBranchMapEntryB\n\xfa\x42\x07\x9a\x01\x04\x08\x01\x30\x01R\x0csvcBranchMap\x1a?\n\x11SvcBranchMapEntry\x12\x10\n\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n\x05value\x18\x02 \x01(\tR\x05value:\x02\x38\x01\"\xf9\x02\n\x15UpdateGreyItemRequest\x12\x17\n\x02id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\x02id\x12\x1d\n\x04name\x18\x02 \x01(\tB\t\xfa\x42\x06r\x04 \x01(\x1eR\x04name\x12>\n\x0b\x64\x65scription\x18\x05 \x01(\x0b\x32\x1c.google.protobuf.StringValueR\x0b\x64\x65scription\x12-\n\x0cjira_tickets\x18\n \x03(\tB\n\xfa\x42\x07\x92\x01\x04\x08\x00(\x01R\x0bjiraTickets\x12x\n\x0esvc_branch_map\x18\x0f \x03(\x0b\x32\x46.moego.service.grey_gateway.v1.UpdateGreyItemRequest.SvcBranchMapEntryB\n\xfa\x42\x07\x9a\x01\x04\x08\x00\x30\x01R\x0csvcBranchMap\x1a?\n\x11SvcBranchMapEntry\x12\x10\n\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n\x05value\x18\x02 \x01(\tR\x05value:\x02\x38\x01\"\\\n\x17GetGreyItemListResponse\x12\x41\n\x05items\x18\x01 \x03(\x0b\x32+.moego.models.grey_gateway.v1.GreyItemModelR\x05items\"9\n\x18GetGreyItemByNameRequest\x12\x1d\n\x04name\x18\x01 \x01(\tB\t\xfa\x42\x06r\x04 \x01(\x1eR\x04name2\x99\x06\n\x12GreyGatewayService\x12\x8c\x01\n\x13GetServiceBranchMap\x12\x39.moego.service.grey_gateway.v1.GetServiceBranchMapRequest\x1a:.moego.service.grey_gateway.v1.GetServiceBranchMapResponse\x12\x80\x01\n\x0fGetGreyItemList\x12\x35.moego.service.grey_gateway.v1.GetGreyItemListRequest\x1a\x36.moego.service.grey_gateway.v1.GetGreyItemListResponse\x12N\n\x0bGetGreyItem\x12\x12.moego.utils.v1.Id\x1a+.moego.models.grey_gateway.v1.GreyItemModel\x12y\n\x11GetGreyItemByName\x12\x37.moego.service.grey_gateway.v1.GetGreyItemByNameRequest\x1a+.moego.models.grey_gateway.v1.GreyItemModel\x12s\n\x0eInsertGreyItem\x12\x34.moego.service.grey_gateway.v1.InsertGreyItemRequest\x1a+.moego.models.grey_gateway.v1.GreyItemModel\x12s\n\x0eUpdateGreyItem\x12\x34.moego.service.grey_gateway.v1.UpdateGreyItemRequest\x1a+.moego.models.grey_gateway.v1.GreyItemModel\x12<\n\x0e\x44\x65leteGreyItem\x12\x12.moego.utils.v1.Id\x1a\x16.google.protobuf.EmptyB\x8e\x01\n%com.moego.idl.service.grey_gateway.v1P\x01Zcgithub.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/grey_gateway/v1;greygatewaysvcpbb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'moego.service.grey_gateway.v1.grey_gateway_service_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n%com.moego.idl.service.grey_gateway.v1P\001Zcgithub.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/grey_gateway/v1;greygatewaysvcpb'
  _globals['_GETSERVICEBRANCHMAPRESPONSE_SVCBRANCHESMAPENTRY']._loaded_options = None
  _globals['_GETSERVICEBRANCHMAPRESPONSE_SVCBRANCHESMAPENTRY']._serialized_options = b'8\001'
  _globals['_GETSERVICEBRANCHMAPREQUEST'].fields_by_name['namespace']._loaded_options = None
  _globals['_GETSERVICEBRANCHMAPREQUEST'].fields_by_name['namespace']._serialized_options = b'\372B\006r\004 \001(\036'
  _globals['_GETGREYITEMLISTREQUEST'].fields_by_name['namespace']._loaded_options = None
  _globals['_GETGREYITEMLISTREQUEST'].fields_by_name['namespace']._serialized_options = b'\372B\006r\004 \001(\036'
  _globals['_INSERTGREYITEMREQUEST_SVCBRANCHMAPENTRY']._loaded_options = None
  _globals['_INSERTGREYITEMREQUEST_SVCBRANCHMAPENTRY']._serialized_options = b'8\001'
  _globals['_INSERTGREYITEMREQUEST'].fields_by_name['name']._loaded_options = None
  _globals['_INSERTGREYITEMREQUEST'].fields_by_name['name']._serialized_options = b'\372B\006r\004 \001(\036'
  _globals['_INSERTGREYITEMREQUEST'].fields_by_name['namespace']._loaded_options = None
  _globals['_INSERTGREYITEMREQUEST'].fields_by_name['namespace']._serialized_options = b'\372B\006r\004 \001(\036'
  _globals['_INSERTGREYITEMREQUEST'].fields_by_name['jira_tickets']._loaded_options = None
  _globals['_INSERTGREYITEMREQUEST'].fields_by_name['jira_tickets']._serialized_options = b'\372B\007\222\001\004\010\000(\001'
  _globals['_INSERTGREYITEMREQUEST'].fields_by_name['svc_branch_map']._loaded_options = None
  _globals['_INSERTGREYITEMREQUEST'].fields_by_name['svc_branch_map']._serialized_options = b'\372B\007\232\001\004\010\0010\001'
  _globals['_UPDATEGREYITEMREQUEST_SVCBRANCHMAPENTRY']._loaded_options = None
  _globals['_UPDATEGREYITEMREQUEST_SVCBRANCHMAPENTRY']._serialized_options = b'8\001'
  _globals['_UPDATEGREYITEMREQUEST'].fields_by_name['id']._loaded_options = None
  _globals['_UPDATEGREYITEMREQUEST'].fields_by_name['id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_UPDATEGREYITEMREQUEST'].fields_by_name['name']._loaded_options = None
  _globals['_UPDATEGREYITEMREQUEST'].fields_by_name['name']._serialized_options = b'\372B\006r\004 \001(\036'
  _globals['_UPDATEGREYITEMREQUEST'].fields_by_name['jira_tickets']._loaded_options = None
  _globals['_UPDATEGREYITEMREQUEST'].fields_by_name['jira_tickets']._serialized_options = b'\372B\007\222\001\004\010\000(\001'
  _globals['_UPDATEGREYITEMREQUEST'].fields_by_name['svc_branch_map']._loaded_options = None
  _globals['_UPDATEGREYITEMREQUEST'].fields_by_name['svc_branch_map']._serialized_options = b'\372B\007\232\001\004\010\0000\001'
  _globals['_GETGREYITEMBYNAMEREQUEST'].fields_by_name['name']._loaded_options = None
  _globals['_GETGREYITEMBYNAMEREQUEST'].fields_by_name['name']._serialized_options = b'\372B\006r\004 \001(\036'
  _globals['_GETSERVICEBRANCHMAPRESPONSE']._serialized_start=268
  _globals['_GETSERVICEBRANCHMAPRESPONSE']._serialized_end=592
  _globals['_GETSERVICEBRANCHMAPRESPONSE_BRANCHLIST']._serialized_start=421
  _globals['_GETSERVICEBRANCHMAPRESPONSE_BRANCHLIST']._serialized_end=453
  _globals['_GETSERVICEBRANCHMAPRESPONSE_SVCBRANCHESMAPENTRY']._serialized_start=456
  _globals['_GETSERVICEBRANCHMAPRESPONSE_SVCBRANCHESMAPENTRY']._serialized_end=592
  _globals['_GETSERVICEBRANCHMAPREQUEST']._serialized_start=594
  _globals['_GETSERVICEBRANCHMAPREQUEST']._serialized_end=663
  _globals['_GETGREYITEMLISTREQUEST']._serialized_start=665
  _globals['_GETGREYITEMLISTREQUEST']._serialized_end=730
  _globals['_INSERTGREYITEMREQUEST']._serialized_start=733
  _globals['_INSERTGREYITEMREQUEST']._serialized_end=1126
  _globals['_INSERTGREYITEMREQUEST_SVCBRANCHMAPENTRY']._serialized_start=1063
  _globals['_INSERTGREYITEMREQUEST_SVCBRANCHMAPENTRY']._serialized_end=1126
  _globals['_UPDATEGREYITEMREQUEST']._serialized_start=1129
  _globals['_UPDATEGREYITEMREQUEST']._serialized_end=1506
  _globals['_UPDATEGREYITEMREQUEST_SVCBRANCHMAPENTRY']._serialized_start=1063
  _globals['_UPDATEGREYITEMREQUEST_SVCBRANCHMAPENTRY']._serialized_end=1126
  _globals['_GETGREYITEMLISTRESPONSE']._serialized_start=1508
  _globals['_GETGREYITEMLISTRESPONSE']._serialized_end=1600
  _globals['_GETGREYITEMBYNAMEREQUEST']._serialized_start=1602
  _globals['_GETGREYITEMBYNAMEREQUEST']._serialized_end=1659
  _globals['_GREYGATEWAYSERVICE']._serialized_start=1662
  _globals['_GREYGATEWAYSERVICE']._serialized_end=2455
# @@protoc_insertion_point(module_scope)
