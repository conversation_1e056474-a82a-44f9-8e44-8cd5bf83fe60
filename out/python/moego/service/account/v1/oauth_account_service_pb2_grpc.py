# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

from google.protobuf import empty_pb2 as google_dot_protobuf_dot_empty__pb2
from moego.models.account.v1 import oauth_account_models_pb2 as moego_dot_models_dot_account_dot_v1_dot_oauth__account__models__pb2
from moego.service.account.v1 import oauth_account_service_pb2 as moego_dot_service_dot_account_dot_v1_dot_oauth__account__service__pb2


class OauthAccountServiceStub(object):
    """oauth account service
    """

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.GetOauthAccountListByAccountId = channel.unary_unary(
                '/moego.service.account.v1.OauthAccountService/GetOauthAccountListByAccountId',
                request_serializer=moego_dot_service_dot_account_dot_v1_dot_oauth__account__service__pb2.GetOauthAccountListByAccountIdRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_account_dot_v1_dot_oauth__account__service__pb2.GetOauthAccountListByAccountIdResponse.FromString,
                _registered_method=True)
        self.UpsertOauthAccount = channel.unary_unary(
                '/moego.service.account.v1.OauthAccountService/UpsertOauthAccount',
                request_serializer=moego_dot_service_dot_account_dot_v1_dot_oauth__account__service__pb2.UpsertOauthAccountRequest.SerializeToString,
                response_deserializer=moego_dot_models_dot_account_dot_v1_dot_oauth__account__models__pb2.OauthAccountModel.FromString,
                _registered_method=True)
        self.DeleteOauthAccount = channel.unary_unary(
                '/moego.service.account.v1.OauthAccountService/DeleteOauthAccount',
                request_serializer=moego_dot_service_dot_account_dot_v1_dot_oauth__account__service__pb2.DeleteOauthAccountRequest.SerializeToString,
                response_deserializer=google_dot_protobuf_dot_empty__pb2.Empty.FromString,
                _registered_method=True)


class OauthAccountServiceServicer(object):
    """oauth account service
    """

    def GetOauthAccountListByAccountId(self, request, context):
        """get oauth account list by account id
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def UpsertOauthAccount(self, request, context):
        """update or insert oauth account request
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DeleteOauthAccount(self, request, context):
        """delete oauth account
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_OauthAccountServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'GetOauthAccountListByAccountId': grpc.unary_unary_rpc_method_handler(
                    servicer.GetOauthAccountListByAccountId,
                    request_deserializer=moego_dot_service_dot_account_dot_v1_dot_oauth__account__service__pb2.GetOauthAccountListByAccountIdRequest.FromString,
                    response_serializer=moego_dot_service_dot_account_dot_v1_dot_oauth__account__service__pb2.GetOauthAccountListByAccountIdResponse.SerializeToString,
            ),
            'UpsertOauthAccount': grpc.unary_unary_rpc_method_handler(
                    servicer.UpsertOauthAccount,
                    request_deserializer=moego_dot_service_dot_account_dot_v1_dot_oauth__account__service__pb2.UpsertOauthAccountRequest.FromString,
                    response_serializer=moego_dot_models_dot_account_dot_v1_dot_oauth__account__models__pb2.OauthAccountModel.SerializeToString,
            ),
            'DeleteOauthAccount': grpc.unary_unary_rpc_method_handler(
                    servicer.DeleteOauthAccount,
                    request_deserializer=moego_dot_service_dot_account_dot_v1_dot_oauth__account__service__pb2.DeleteOauthAccountRequest.FromString,
                    response_serializer=google_dot_protobuf_dot_empty__pb2.Empty.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'moego.service.account.v1.OauthAccountService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('moego.service.account.v1.OauthAccountService', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class OauthAccountService(object):
    """oauth account service
    """

    @staticmethod
    def GetOauthAccountListByAccountId(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.account.v1.OauthAccountService/GetOauthAccountListByAccountId',
            moego_dot_service_dot_account_dot_v1_dot_oauth__account__service__pb2.GetOauthAccountListByAccountIdRequest.SerializeToString,
            moego_dot_service_dot_account_dot_v1_dot_oauth__account__service__pb2.GetOauthAccountListByAccountIdResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def UpsertOauthAccount(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.account.v1.OauthAccountService/UpsertOauthAccount',
            moego_dot_service_dot_account_dot_v1_dot_oauth__account__service__pb2.UpsertOauthAccountRequest.SerializeToString,
            moego_dot_models_dot_account_dot_v1_dot_oauth__account__models__pb2.OauthAccountModel.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def DeleteOauthAccount(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.account.v1.OauthAccountService/DeleteOauthAccount',
            moego_dot_service_dot_account_dot_v1_dot_oauth__account__service__pb2.DeleteOauthAccountRequest.SerializeToString,
            google_dot_protobuf_dot_empty__pb2.Empty.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)
