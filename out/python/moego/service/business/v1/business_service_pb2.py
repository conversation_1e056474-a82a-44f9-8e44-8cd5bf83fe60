# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: moego/service/business/v1/business_service.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'moego/service/business/v1/business_service.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from moego.models.business.v1 import business_models_pb2 as moego_dot_models_dot_business_dot_v1_dot_business__models__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n0moego/service/business/v1/business_service.proto\x12\x19moego.service.business.v1\x1a.moego/models/business/v1/business_models.proto\"$\n\x12GetBusinessRequest\x12\x0e\n\x02id\x18\x01 \x01(\x03R\x02id2\xf3\x01\n\x0f\x42usinessService\x12y\n\x15GetBusinessPublicView\x12-.moego.service.business.v1.GetBusinessRequest\x1a\x31.moego.models.business.v1.BusinessModelPublicView\x12\x65\n\x0bGetBusiness\x12-.moego.service.business.v1.GetBusinessRequest\x1a\'.moego.models.business.v1.BusinessModelB\x83\x01\n!com.moego.idl.service.business.v1P\x01Z\\github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/business/v1;businesssvcpbb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'moego.service.business.v1.business_service_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n!com.moego.idl.service.business.v1P\001Z\\github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/business/v1;businesssvcpb'
  _globals['_GETBUSINESSREQUEST']._serialized_start=127
  _globals['_GETBUSINESSREQUEST']._serialized_end=163
  _globals['_BUSINESSSERVICE']._serialized_start=166
  _globals['_BUSINESSSERVICE']._serialized_end=409
# @@protoc_insertion_point(module_scope)
