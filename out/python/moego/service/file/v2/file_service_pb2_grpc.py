# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

from moego.service.file.v2 import file_service_pb2 as moego_dot_service_dot_file_dot_v2_dot_file__service__pb2


class FileServiceStub(object):
    """FileService
    """

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.GetUploadPresignedUrl = channel.unary_unary(
                '/moego.service.file.v2.FileService/GetUploadPresignedUrl',
                request_serializer=moego_dot_service_dot_file_dot_v2_dot_file__service__pb2.GetUploadPresignedUrlRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_file_dot_v2_dot_file__service__pb2.GetUploadPresignedUrlResponse.FromString,
                _registered_method=True)
        self.CreateExportFile = channel.unary_unary(
                '/moego.service.file.v2.FileService/CreateExportFile',
                request_serializer=moego_dot_service_dot_file_dot_v2_dot_file__service__pb2.CreateExportFileRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_file_dot_v2_dot_file__service__pb2.CreateExportFileResponse.FromString,
                _registered_method=True)
        self.QueryFile = channel.unary_unary(
                '/moego.service.file.v2.FileService/QueryFile',
                request_serializer=moego_dot_service_dot_file_dot_v2_dot_file__service__pb2.QueryFileRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_file_dot_v2_dot_file__service__pb2.QueryFileResponse.FromString,
                _registered_method=True)
        self.UploadExportFile = channel.unary_unary(
                '/moego.service.file.v2.FileService/UploadExportFile',
                request_serializer=moego_dot_service_dot_file_dot_v2_dot_file__service__pb2.UploadExportFileRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_file_dot_v2_dot_file__service__pb2.UploadExportFileResponse.FromString,
                _registered_method=True)
        self.UploadFile = channel.unary_unary(
                '/moego.service.file.v2.FileService/UploadFile',
                request_serializer=moego_dot_service_dot_file_dot_v2_dot_file__service__pb2.UploadFileRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_file_dot_v2_dot_file__service__pb2.UploadFileResponse.FromString,
                _registered_method=True)
        self.UpdateFileStatus = channel.unary_unary(
                '/moego.service.file.v2.FileService/UpdateFileStatus',
                request_serializer=moego_dot_service_dot_file_dot_v2_dot_file__service__pb2.UpdateFileStatusRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_file_dot_v2_dot_file__service__pb2.UpdateFileStatusResponse.FromString,
                _registered_method=True)
        self.FlushFile = channel.unary_unary(
                '/moego.service.file.v2.FileService/FlushFile',
                request_serializer=moego_dot_service_dot_file_dot_v2_dot_file__service__pb2.FlushFileRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_file_dot_v2_dot_file__service__pb2.FlushFileResponse.FromString,
                _registered_method=True)


class FileServiceServicer(object):
    """FileService
    """

    def GetUploadPresignedUrl(self, request, context):
        """get presigned url for upload file
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def CreateExportFile(self, request, context):
        """CreateExportFile: create a file record before upload it to s3,should use UploadExportFile to upload the file
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def QueryFile(self, request, context):
        """QueryFile
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def UploadExportFile(self, request, context):
        """UploadExportFile: usually used after CreateExportFile
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def UploadFile(self, request, context):
        """UploadFile
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def UpdateFileStatus(self, request, context):
        """UpdateFileStatus: usually used after upload file with presigned URL, deprecated, please use `FlushFile`
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def FlushFile(self, request, context):
        """flush file status
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_FileServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'GetUploadPresignedUrl': grpc.unary_unary_rpc_method_handler(
                    servicer.GetUploadPresignedUrl,
                    request_deserializer=moego_dot_service_dot_file_dot_v2_dot_file__service__pb2.GetUploadPresignedUrlRequest.FromString,
                    response_serializer=moego_dot_service_dot_file_dot_v2_dot_file__service__pb2.GetUploadPresignedUrlResponse.SerializeToString,
            ),
            'CreateExportFile': grpc.unary_unary_rpc_method_handler(
                    servicer.CreateExportFile,
                    request_deserializer=moego_dot_service_dot_file_dot_v2_dot_file__service__pb2.CreateExportFileRequest.FromString,
                    response_serializer=moego_dot_service_dot_file_dot_v2_dot_file__service__pb2.CreateExportFileResponse.SerializeToString,
            ),
            'QueryFile': grpc.unary_unary_rpc_method_handler(
                    servicer.QueryFile,
                    request_deserializer=moego_dot_service_dot_file_dot_v2_dot_file__service__pb2.QueryFileRequest.FromString,
                    response_serializer=moego_dot_service_dot_file_dot_v2_dot_file__service__pb2.QueryFileResponse.SerializeToString,
            ),
            'UploadExportFile': grpc.unary_unary_rpc_method_handler(
                    servicer.UploadExportFile,
                    request_deserializer=moego_dot_service_dot_file_dot_v2_dot_file__service__pb2.UploadExportFileRequest.FromString,
                    response_serializer=moego_dot_service_dot_file_dot_v2_dot_file__service__pb2.UploadExportFileResponse.SerializeToString,
            ),
            'UploadFile': grpc.unary_unary_rpc_method_handler(
                    servicer.UploadFile,
                    request_deserializer=moego_dot_service_dot_file_dot_v2_dot_file__service__pb2.UploadFileRequest.FromString,
                    response_serializer=moego_dot_service_dot_file_dot_v2_dot_file__service__pb2.UploadFileResponse.SerializeToString,
            ),
            'UpdateFileStatus': grpc.unary_unary_rpc_method_handler(
                    servicer.UpdateFileStatus,
                    request_deserializer=moego_dot_service_dot_file_dot_v2_dot_file__service__pb2.UpdateFileStatusRequest.FromString,
                    response_serializer=moego_dot_service_dot_file_dot_v2_dot_file__service__pb2.UpdateFileStatusResponse.SerializeToString,
            ),
            'FlushFile': grpc.unary_unary_rpc_method_handler(
                    servicer.FlushFile,
                    request_deserializer=moego_dot_service_dot_file_dot_v2_dot_file__service__pb2.FlushFileRequest.FromString,
                    response_serializer=moego_dot_service_dot_file_dot_v2_dot_file__service__pb2.FlushFileResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'moego.service.file.v2.FileService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('moego.service.file.v2.FileService', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class FileService(object):
    """FileService
    """

    @staticmethod
    def GetUploadPresignedUrl(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.file.v2.FileService/GetUploadPresignedUrl',
            moego_dot_service_dot_file_dot_v2_dot_file__service__pb2.GetUploadPresignedUrlRequest.SerializeToString,
            moego_dot_service_dot_file_dot_v2_dot_file__service__pb2.GetUploadPresignedUrlResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def CreateExportFile(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.file.v2.FileService/CreateExportFile',
            moego_dot_service_dot_file_dot_v2_dot_file__service__pb2.CreateExportFileRequest.SerializeToString,
            moego_dot_service_dot_file_dot_v2_dot_file__service__pb2.CreateExportFileResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def QueryFile(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.file.v2.FileService/QueryFile',
            moego_dot_service_dot_file_dot_v2_dot_file__service__pb2.QueryFileRequest.SerializeToString,
            moego_dot_service_dot_file_dot_v2_dot_file__service__pb2.QueryFileResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def UploadExportFile(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.file.v2.FileService/UploadExportFile',
            moego_dot_service_dot_file_dot_v2_dot_file__service__pb2.UploadExportFileRequest.SerializeToString,
            moego_dot_service_dot_file_dot_v2_dot_file__service__pb2.UploadExportFileResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def UploadFile(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.file.v2.FileService/UploadFile',
            moego_dot_service_dot_file_dot_v2_dot_file__service__pb2.UploadFileRequest.SerializeToString,
            moego_dot_service_dot_file_dot_v2_dot_file__service__pb2.UploadFileResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def UpdateFileStatus(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.file.v2.FileService/UpdateFileStatus',
            moego_dot_service_dot_file_dot_v2_dot_file__service__pb2.UpdateFileStatusRequest.SerializeToString,
            moego_dot_service_dot_file_dot_v2_dot_file__service__pb2.UpdateFileStatusResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def FlushFile(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.file.v2.FileService/FlushFile',
            moego_dot_service_dot_file_dot_v2_dot_file__service__pb2.FlushFileRequest.SerializeToString,
            moego_dot_service_dot_file_dot_v2_dot_file__service__pb2.FlushFileResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)
