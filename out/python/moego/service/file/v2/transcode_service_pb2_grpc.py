# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

from moego.service.file.v2 import transcode_service_pb2 as moego_dot_service_dot_file_dot_v2_dot_transcode__service__pb2


class TranscodeServiceStub(object):
    """TranscodeService
    """

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.GetJob = channel.unary_unary(
                '/moego.service.file.v2.TranscodeService/GetJob',
                request_serializer=moego_dot_service_dot_file_dot_v2_dot_transcode__service__pb2.GetJobRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_file_dot_v2_dot_transcode__service__pb2.GetJobResponse.FromString,
                _registered_method=True)
        self.CreateJob = channel.unary_unary(
                '/moego.service.file.v2.TranscodeService/CreateJob',
                request_serializer=moego_dot_service_dot_file_dot_v2_dot_transcode__service__pb2.CreateJobRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_file_dot_v2_dot_transcode__service__pb2.CreateJobResponse.FromString,
                _registered_method=True)
        self.UpdateJob = channel.unary_unary(
                '/moego.service.file.v2.TranscodeService/UpdateJob',
                request_serializer=moego_dot_service_dot_file_dot_v2_dot_transcode__service__pb2.UpdateJobRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_file_dot_v2_dot_transcode__service__pb2.UpdateJobResponse.FromString,
                _registered_method=True)
        self.CancelJob = channel.unary_unary(
                '/moego.service.file.v2.TranscodeService/CancelJob',
                request_serializer=moego_dot_service_dot_file_dot_v2_dot_transcode__service__pb2.CancelJobRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_file_dot_v2_dot_transcode__service__pb2.CancelJobResponse.FromString,
                _registered_method=True)


class TranscodeServiceServicer(object):
    """TranscodeService
    """

    def GetJob(self, request, context):
        """get job
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def CreateJob(self, request, context):
        """create job
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def UpdateJob(self, request, context):
        """update job
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def CancelJob(self, request, context):
        """cancel job
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_TranscodeServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'GetJob': grpc.unary_unary_rpc_method_handler(
                    servicer.GetJob,
                    request_deserializer=moego_dot_service_dot_file_dot_v2_dot_transcode__service__pb2.GetJobRequest.FromString,
                    response_serializer=moego_dot_service_dot_file_dot_v2_dot_transcode__service__pb2.GetJobResponse.SerializeToString,
            ),
            'CreateJob': grpc.unary_unary_rpc_method_handler(
                    servicer.CreateJob,
                    request_deserializer=moego_dot_service_dot_file_dot_v2_dot_transcode__service__pb2.CreateJobRequest.FromString,
                    response_serializer=moego_dot_service_dot_file_dot_v2_dot_transcode__service__pb2.CreateJobResponse.SerializeToString,
            ),
            'UpdateJob': grpc.unary_unary_rpc_method_handler(
                    servicer.UpdateJob,
                    request_deserializer=moego_dot_service_dot_file_dot_v2_dot_transcode__service__pb2.UpdateJobRequest.FromString,
                    response_serializer=moego_dot_service_dot_file_dot_v2_dot_transcode__service__pb2.UpdateJobResponse.SerializeToString,
            ),
            'CancelJob': grpc.unary_unary_rpc_method_handler(
                    servicer.CancelJob,
                    request_deserializer=moego_dot_service_dot_file_dot_v2_dot_transcode__service__pb2.CancelJobRequest.FromString,
                    response_serializer=moego_dot_service_dot_file_dot_v2_dot_transcode__service__pb2.CancelJobResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'moego.service.file.v2.TranscodeService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('moego.service.file.v2.TranscodeService', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class TranscodeService(object):
    """TranscodeService
    """

    @staticmethod
    def GetJob(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.file.v2.TranscodeService/GetJob',
            moego_dot_service_dot_file_dot_v2_dot_transcode__service__pb2.GetJobRequest.SerializeToString,
            moego_dot_service_dot_file_dot_v2_dot_transcode__service__pb2.GetJobResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def CreateJob(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.file.v2.TranscodeService/CreateJob',
            moego_dot_service_dot_file_dot_v2_dot_transcode__service__pb2.CreateJobRequest.SerializeToString,
            moego_dot_service_dot_file_dot_v2_dot_transcode__service__pb2.CreateJobResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def UpdateJob(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.file.v2.TranscodeService/UpdateJob',
            moego_dot_service_dot_file_dot_v2_dot_transcode__service__pb2.UpdateJobRequest.SerializeToString,
            moego_dot_service_dot_file_dot_v2_dot_transcode__service__pb2.UpdateJobResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def CancelJob(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.file.v2.TranscodeService/CancelJob',
            moego_dot_service_dot_file_dot_v2_dot_transcode__service__pb2.CancelJobRequest.SerializeToString,
            moego_dot_service_dot_file_dot_v2_dot_transcode__service__pb2.CancelJobResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)
