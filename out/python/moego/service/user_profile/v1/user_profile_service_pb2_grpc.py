# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

from moego.service.user_profile.v1 import user_profile_service_pb2 as moego_dot_service_dot_user__profile_dot_v1_dot_user__profile__service__pb2


class UserProfileServiceStub(object):
    """UserProfileService 用户画像服务
    """

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.GetUserProfiles = channel.unary_unary(
                '/moego.service.user_profile.v1.UserProfileService/GetUserProfiles',
                request_serializer=moego_dot_service_dot_user__profile_dot_v1_dot_user__profile__service__pb2.GetUserProfilesRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_user__profile_dot_v1_dot_user__profile__service__pb2.GetUserProfilesResponse.FromString,
                _registered_method=True)


class UserProfileServiceServicer(object):
    """UserProfileService 用户画像服务
    """

    def GetUserProfiles(self, request, context):
        """GetUserProfile 获取用户画像
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_UserProfileServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'GetUserProfiles': grpc.unary_unary_rpc_method_handler(
                    servicer.GetUserProfiles,
                    request_deserializer=moego_dot_service_dot_user__profile_dot_v1_dot_user__profile__service__pb2.GetUserProfilesRequest.FromString,
                    response_serializer=moego_dot_service_dot_user__profile_dot_v1_dot_user__profile__service__pb2.GetUserProfilesResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'moego.service.user_profile.v1.UserProfileService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('moego.service.user_profile.v1.UserProfileService', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class UserProfileService(object):
    """UserProfileService 用户画像服务
    """

    @staticmethod
    def GetUserProfiles(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.user_profile.v1.UserProfileService/GetUserProfiles',
            moego_dot_service_dot_user__profile_dot_v1_dot_user__profile__service__pb2.GetUserProfilesRequest.SerializeToString,
            moego_dot_service_dot_user__profile_dot_v1_dot_user__profile__service__pb2.GetUserProfilesResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)
