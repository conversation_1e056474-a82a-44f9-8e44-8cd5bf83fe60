# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

from moego.service.offering.v2 import pricing_rule_service_pb2 as moego_dot_service_dot_offering_dot_v2_dot_pricing__rule__service__pb2


class PricingRuleServiceStub(object):
    """pricing rule service
    """

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.UpsertPricingRule = channel.unary_unary(
                '/moego.service.offering.v2.PricingRuleService/UpsertPricingRule',
                request_serializer=moego_dot_service_dot_offering_dot_v2_dot_pricing__rule__service__pb2.UpsertPricingRuleRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_offering_dot_v2_dot_pricing__rule__service__pb2.UpsertPricingRuleResponse.FromString,
                _registered_method=True)
        self.GetPricingRule = channel.unary_unary(
                '/moego.service.offering.v2.PricingRuleService/GetPricingRule',
                request_serializer=moego_dot_service_dot_offering_dot_v2_dot_pricing__rule__service__pb2.GetPricingRuleRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_offering_dot_v2_dot_pricing__rule__service__pb2.GetPricingRuleResponse.FromString,
                _registered_method=True)
        self.ListPricingRules = channel.unary_unary(
                '/moego.service.offering.v2.PricingRuleService/ListPricingRules',
                request_serializer=moego_dot_service_dot_offering_dot_v2_dot_pricing__rule__service__pb2.ListPricingRulesRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_offering_dot_v2_dot_pricing__rule__service__pb2.ListPricingRulesResponse.FromString,
                _registered_method=True)
        self.CalculatePricingRule = channel.unary_unary(
                '/moego.service.offering.v2.PricingRuleService/CalculatePricingRule',
                request_serializer=moego_dot_service_dot_offering_dot_v2_dot_pricing__rule__service__pb2.CalculatePricingRuleRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_offering_dot_v2_dot_pricing__rule__service__pb2.CalculatePricingRuleResponse.FromString,
                _registered_method=True)
        self.DeletePricingRule = channel.unary_unary(
                '/moego.service.offering.v2.PricingRuleService/DeletePricingRule',
                request_serializer=moego_dot_service_dot_offering_dot_v2_dot_pricing__rule__service__pb2.DeletePricingRuleRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_offering_dot_v2_dot_pricing__rule__service__pb2.DeletePricingRuleResponse.FromString,
                _registered_method=True)
        self.GetDiscountSetting = channel.unary_unary(
                '/moego.service.offering.v2.PricingRuleService/GetDiscountSetting',
                request_serializer=moego_dot_service_dot_offering_dot_v2_dot_pricing__rule__service__pb2.GetDiscountSettingRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_offering_dot_v2_dot_pricing__rule__service__pb2.GetDiscountSettingResponse.FromString,
                _registered_method=True)
        self.UpdateDiscountSetting = channel.unary_unary(
                '/moego.service.offering.v2.PricingRuleService/UpdateDiscountSetting',
                request_serializer=moego_dot_service_dot_offering_dot_v2_dot_pricing__rule__service__pb2.UpdateDiscountSettingRequest.SerializeToString,
                response_deserializer=moego_dot_service_dot_offering_dot_v2_dot_pricing__rule__service__pb2.UpdateDiscountSettingResponse.FromString,
                _registered_method=True)


class PricingRuleServiceServicer(object):
    """pricing rule service
    """

    def UpsertPricingRule(self, request, context):
        """upsert pricing rule
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetPricingRule(self, request, context):
        """get pricing rule
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ListPricingRules(self, request, context):
        """list pricing rule
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def CalculatePricingRule(self, request, context):
        """calculate pricing rule
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DeletePricingRule(self, request, context):
        """delete pricing rule
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetDiscountSetting(self, request, context):
        """get discount setting
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def UpdateDiscountSetting(self, request, context):
        """update discount setting
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_PricingRuleServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'UpsertPricingRule': grpc.unary_unary_rpc_method_handler(
                    servicer.UpsertPricingRule,
                    request_deserializer=moego_dot_service_dot_offering_dot_v2_dot_pricing__rule__service__pb2.UpsertPricingRuleRequest.FromString,
                    response_serializer=moego_dot_service_dot_offering_dot_v2_dot_pricing__rule__service__pb2.UpsertPricingRuleResponse.SerializeToString,
            ),
            'GetPricingRule': grpc.unary_unary_rpc_method_handler(
                    servicer.GetPricingRule,
                    request_deserializer=moego_dot_service_dot_offering_dot_v2_dot_pricing__rule__service__pb2.GetPricingRuleRequest.FromString,
                    response_serializer=moego_dot_service_dot_offering_dot_v2_dot_pricing__rule__service__pb2.GetPricingRuleResponse.SerializeToString,
            ),
            'ListPricingRules': grpc.unary_unary_rpc_method_handler(
                    servicer.ListPricingRules,
                    request_deserializer=moego_dot_service_dot_offering_dot_v2_dot_pricing__rule__service__pb2.ListPricingRulesRequest.FromString,
                    response_serializer=moego_dot_service_dot_offering_dot_v2_dot_pricing__rule__service__pb2.ListPricingRulesResponse.SerializeToString,
            ),
            'CalculatePricingRule': grpc.unary_unary_rpc_method_handler(
                    servicer.CalculatePricingRule,
                    request_deserializer=moego_dot_service_dot_offering_dot_v2_dot_pricing__rule__service__pb2.CalculatePricingRuleRequest.FromString,
                    response_serializer=moego_dot_service_dot_offering_dot_v2_dot_pricing__rule__service__pb2.CalculatePricingRuleResponse.SerializeToString,
            ),
            'DeletePricingRule': grpc.unary_unary_rpc_method_handler(
                    servicer.DeletePricingRule,
                    request_deserializer=moego_dot_service_dot_offering_dot_v2_dot_pricing__rule__service__pb2.DeletePricingRuleRequest.FromString,
                    response_serializer=moego_dot_service_dot_offering_dot_v2_dot_pricing__rule__service__pb2.DeletePricingRuleResponse.SerializeToString,
            ),
            'GetDiscountSetting': grpc.unary_unary_rpc_method_handler(
                    servicer.GetDiscountSetting,
                    request_deserializer=moego_dot_service_dot_offering_dot_v2_dot_pricing__rule__service__pb2.GetDiscountSettingRequest.FromString,
                    response_serializer=moego_dot_service_dot_offering_dot_v2_dot_pricing__rule__service__pb2.GetDiscountSettingResponse.SerializeToString,
            ),
            'UpdateDiscountSetting': grpc.unary_unary_rpc_method_handler(
                    servicer.UpdateDiscountSetting,
                    request_deserializer=moego_dot_service_dot_offering_dot_v2_dot_pricing__rule__service__pb2.UpdateDiscountSettingRequest.FromString,
                    response_serializer=moego_dot_service_dot_offering_dot_v2_dot_pricing__rule__service__pb2.UpdateDiscountSettingResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'moego.service.offering.v2.PricingRuleService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('moego.service.offering.v2.PricingRuleService', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class PricingRuleService(object):
    """pricing rule service
    """

    @staticmethod
    def UpsertPricingRule(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.offering.v2.PricingRuleService/UpsertPricingRule',
            moego_dot_service_dot_offering_dot_v2_dot_pricing__rule__service__pb2.UpsertPricingRuleRequest.SerializeToString,
            moego_dot_service_dot_offering_dot_v2_dot_pricing__rule__service__pb2.UpsertPricingRuleResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetPricingRule(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.offering.v2.PricingRuleService/GetPricingRule',
            moego_dot_service_dot_offering_dot_v2_dot_pricing__rule__service__pb2.GetPricingRuleRequest.SerializeToString,
            moego_dot_service_dot_offering_dot_v2_dot_pricing__rule__service__pb2.GetPricingRuleResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def ListPricingRules(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.offering.v2.PricingRuleService/ListPricingRules',
            moego_dot_service_dot_offering_dot_v2_dot_pricing__rule__service__pb2.ListPricingRulesRequest.SerializeToString,
            moego_dot_service_dot_offering_dot_v2_dot_pricing__rule__service__pb2.ListPricingRulesResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def CalculatePricingRule(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.offering.v2.PricingRuleService/CalculatePricingRule',
            moego_dot_service_dot_offering_dot_v2_dot_pricing__rule__service__pb2.CalculatePricingRuleRequest.SerializeToString,
            moego_dot_service_dot_offering_dot_v2_dot_pricing__rule__service__pb2.CalculatePricingRuleResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def DeletePricingRule(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.offering.v2.PricingRuleService/DeletePricingRule',
            moego_dot_service_dot_offering_dot_v2_dot_pricing__rule__service__pb2.DeletePricingRuleRequest.SerializeToString,
            moego_dot_service_dot_offering_dot_v2_dot_pricing__rule__service__pb2.DeletePricingRuleResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetDiscountSetting(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.offering.v2.PricingRuleService/GetDiscountSetting',
            moego_dot_service_dot_offering_dot_v2_dot_pricing__rule__service__pb2.GetDiscountSettingRequest.SerializeToString,
            moego_dot_service_dot_offering_dot_v2_dot_pricing__rule__service__pb2.GetDiscountSettingResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def UpdateDiscountSetting(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.service.offering.v2.PricingRuleService/UpdateDiscountSetting',
            moego_dot_service_dot_offering_dot_v2_dot_pricing__rule__service__pb2.UpdateDiscountSettingRequest.SerializeToString,
            moego_dot_service_dot_offering_dot_v2_dot_pricing__rule__service__pb2.UpdateDiscountSettingResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)
