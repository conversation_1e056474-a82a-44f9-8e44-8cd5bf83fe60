# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: moego/service/offering/v1/service_service.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'moego/service/offering/v1/service_service.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from moego.models.customer.v1 import customer_pet_enums_pb2 as moego_dot_models_dot_customer_dot_v1_dot_customer__pet__enums__pb2
from moego.models.offering.v1 import category_models_pb2 as moego_dot_models_dot_offering_dot_v1_dot_category__models__pb2
from moego.models.offering.v1 import service_bundle_sale_mapping_models_pb2 as moego_dot_models_dot_offering_dot_v1_dot_service__bundle__sale__mapping__models__pb2
from moego.models.offering.v1 import service_defs_pb2 as moego_dot_models_dot_offering_dot_v1_dot_service__defs__pb2
from moego.models.offering.v1 import service_enum_pb2 as moego_dot_models_dot_offering_dot_v1_dot_service__enum__pb2
from moego.models.offering.v1 import service_models_pb2 as moego_dot_models_dot_offering_dot_v1_dot_service__models__pb2
from moego.utils.v2 import list_pb2 as moego_dot_utils_dot_v2_dot_list__pb2
from moego.utils.v2 import pagination_messages_pb2 as moego_dot_utils_dot_v2_dot_pagination__messages__pb2
from validate import validate_pb2 as validate_dot_validate__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n/moego/service/offering/v1/service_service.proto\x12\x19moego.service.offering.v1\x1a\x31moego/models/customer/v1/customer_pet_enums.proto\x1a.moego/models/offering/v1/category_models.proto\x1a\x41moego/models/offering/v1/service_bundle_sale_mapping_models.proto\x1a+moego/models/offering/v1/service_defs.proto\x1a+moego/models/offering/v1/service_enum.proto\x1a-moego/models/offering/v1/service_models.proto\x1a\x19moego/utils/v2/list.proto\x1a(moego/utils/v2/pagination_messages.proto\x1a\x17validate/validate.proto\"\xad\x02\n\x14\x43reateServiceRequest\x12\x62\n\x12\x63reate_service_def\x18\x01 \x01(\x0b\x32*.moego.models.offering.v1.CreateServiceDefB\x08\xfa\x42\x05\x8a\x01\x02\x10\x01R\x10\x63reateServiceDef\x12\x31\n\x10token_company_id\x18\x02 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\x0etokenCompanyId\x12/\n\x0etoken_staff_id\x18\x03 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00H\x00R\x0ctokenStaffId\x12\x39\n\x11internal_operator\x18\x04 \x01(\tB\n\xfa\x42\x07r\x05\x10\x01\x18\xff\x01H\x00R\x10internalOperatorB\x12\n\x0boperator_id\x12\x03\xf8\x42\x01\"c\n\x15\x43reateServiceResponse\x12J\n\x07service\x18\x01 \x01(\x0b\x32&.moego.models.offering.v1.ServiceModelB\x08\xfa\x42\x05\x8a\x01\x02\x10\x01R\x07service\"\xad\x02\n\x14UpdateServiceRequest\x12\x62\n\x12update_service_def\x18\x01 \x01(\x0b\x32*.moego.models.offering.v1.UpdateServiceDefB\x08\xfa\x42\x05\x8a\x01\x02\x10\x01R\x10updateServiceDef\x12\x31\n\x10token_company_id\x18\x02 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\x0etokenCompanyId\x12/\n\x0etoken_staff_id\x18\x03 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00H\x00R\x0ctokenStaffId\x12\x39\n\x11internal_operator\x18\x04 \x01(\tB\n\xfa\x42\x07r\x05\x10\x01\x18\xff\x01H\x00R\x10internalOperatorB\x12\n\x0boperator_id\x12\x03\xf8\x42\x01\"c\n\x15UpdateServiceResponse\x12J\n\x07service\x18\x01 \x01(\x0b\x32&.moego.models.offering.v1.ServiceModelB\x08\xfa\x42\x05\x8a\x01\x02\x10\x01R\x07service\"\x9a\x04\n\x15GetServiceListRequest\x12Z\n\x11service_item_type\x18\x01 \x01(\x0e\x32).moego.models.offering.v1.ServiceItemTypeH\x00R\x0fserviceItemType\x88\x01\x01\x12\x46\n\npagination\x18\x02 \x01(\x0b\x32!.moego.utils.v2.PaginationRequestH\x01R\npagination\x88\x01\x01\x12!\n\x0c\x62usiness_ids\x18\x03 \x03(\x03R\x0b\x62usinessIds\x12\x1f\n\x08inactive\x18\x04 \x01(\x08H\x02R\x08inactive\x88\x01\x01\x12M\n\x0cservice_type\x18\x05 \x01(\x0e\x32%.moego.models.offering.v1.ServiceTypeH\x03R\x0bserviceType\x88\x01\x01\x12\x31\n\x10token_company_id\x18\x06 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\x0etokenCompanyId\x12\'\n\x07keyword\x18\x07 \x01(\tB\x08\xfa\x42\x05r\x03\x18\xff\x01H\x04R\x07keyword\x88\x01\x01\x12\x1f\n\x0bservice_ids\x18\x08 \x03(\x03R\nserviceIdsB\x14\n\x12_service_item_typeB\r\n\x0b_paginationB\x0b\n\t_inactiveB\x0f\n\r_service_typeB\n\n\x08_keyword\"\xb1\x01\n\x16GetServiceListResponse\x12S\n\rcategory_list\x18\x01 \x03(\x0b\x32..moego.models.offering.v1.ServiceCategoryModelR\x0c\x63\x61tegoryList\x12\x42\n\npagination\x18\x02 \x01(\x0b\x32\".moego.utils.v2.PaginationResponseR\npagination\"y\n\x1aGetServiceListByIdsRequest\x12+\n\ncompany_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00H\x00R\tcompanyId\x88\x01\x01\x12\x1f\n\x0bservice_ids\x18\x02 \x03(\x03R\nserviceIdsB\r\n\x0b_company_id\"e\n\x1bGetServiceListByIdsResponse\x12\x46\n\x08services\x18\x01 \x03(\x0b\x32*.moego.models.offering.v1.ServiceBriefViewR\x08services\"\x8a\x03\n\"GetServiceByPetAndServiceIdRequest\x12&\n\ncompany_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\tcompanyId\x12-\n\x0b\x62usiness_id\x18\x02 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00H\x00R\nbusinessId\x88\x01\x01\x12\x96\x01\n\x1bpet_id_with_service_id_list\x18\x03 \x03(\x0b\x32Y.moego.service.offering.v1.GetServiceByPetAndServiceIdRequest.PetIdWithServiceIdListEntryR\x16petIdWithServiceIdList\x1a\x64\n\x1bPetIdWithServiceIdListEntry\x12\x10\n\x03key\x18\x01 \x01(\x03R\x03key\x12/\n\x05value\x18\x02 \x01(\x0b\x32\x19.moego.utils.v2.Int64ListR\x05value:\x02\x38\x01\x42\x0e\n\x0c_business_id\"\xdc\x02\n#GetServiceByPetAndServiceIdResponse\x12\xac\x01\n\"pet_id_with_available_service_list\x18\x01 \x03(\x0b\x32\x61.moego.service.offering.v1.GetServiceByPetAndServiceIdResponse.PetIdWithAvailableServiceListEntryR\x1dpetIdWithAvailableServiceList\x1a\x85\x01\n\"PetIdWithAvailableServiceListEntry\x12\x10\n\x03key\x18\x01 \x01(\x03R\x03key\x12I\n\x05value\x18\x02 \x01(\x0b\x32\x33.moego.models.offering.v1.CustomizedServiceViewListR\x05value:\x02\x38\x01\"\xee\x01\n\x1f\x43ustomizedServiceQueryCondition\x12&\n\nservice_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\tserviceId\x12-\n\x0b\x62usiness_id\x18\x02 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00H\x00R\nbusinessId\x88\x01\x01\x12#\n\x06pet_id\x18\x03 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00H\x01R\x05petId\x88\x01\x01\x12\'\n\x08staff_id\x18\x04 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00H\x02R\x07staffId\x88\x01\x01\x42\x0e\n\x0c_business_idB\t\n\x07_pet_idB\x0b\n\t_staff_id\"\xb8\x01\n BatchGetCustomizedServiceRequest\x12&\n\ncompany_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\tcompanyId\x12l\n\x14query_condition_list\x18\x02 \x03(\x0b\x32:.moego.service.offering.v1.CustomizedServiceQueryConditionR\x12queryConditionList\"\x97\x03\n!BatchGetCustomizedServiceResponse\x12\x8e\x01\n\x17\x63ustomized_service_list\x18\x01 \x03(\x0b\x32V.moego.service.offering.v1.BatchGetCustomizedServiceResponse.ServiceWithCustomizedInfoR\x15\x63ustomizedServiceList\x1a\xe0\x01\n\x19ServiceWithCustomizedInfo\x12\x63\n\x0fquery_condition\x18\x01 \x01(\x0b\x32:.moego.service.offering.v1.CustomizedServiceQueryConditionR\x0equeryCondition\x12^\n\x12\x63ustomized_service\x18\x02 \x01(\x0b\x32/.moego.models.offering.v1.CustomizedServiceViewR\x11\x63ustomizedService\"\xce\x02\n\x16OverrideServiceRequest\x12&\n\ncompany_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\tcompanyId\x12\x8f\x01\n\x1coverride_rules_by_service_id\x18\x02 \x03(\x0b\x32O.moego.service.offering.v1.OverrideServiceRequest.OverrideRulesByServiceIdEntryR\x18overrideRulesByServiceId\x1az\n\x1dOverrideRulesByServiceIdEntry\x12\x10\n\x03key\x18\x01 \x01(\x03R\x03key\x12\x43\n\x05value\x18\x02 \x01(\x0b\x32-.moego.models.offering.v1.ServiceOverrideRuleR\x05value:\x02\x38\x01\"\x19\n\x17OverrideServiceResponse\"\xa7\x05\n\x1fGetApplicableServiceListRequest\x12&\n\ncompany_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\tcompanyId\x12Z\n\x11service_item_type\x18\x02 \x01(\x0e\x32).moego.models.offering.v1.ServiceItemTypeH\x00R\x0fserviceItemType\x88\x01\x01\x12-\n\x0b\x62usiness_id\x18\x03 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00H\x01R\nbusinessId\x88\x01\x01\x12%\n\x0eonly_available\x18\x04 \x01(\x08R\ronlyAvailable\x12\x1a\n\x06pet_id\x18\x05 \x01(\x03H\x02R\x05petId\x88\x01\x01\x12H\n\x0cservice_type\x18\x06 \x01(\x0e\x32%.moego.models.offering.v1.ServiceTypeR\x0bserviceType\x12N\n\x06\x66ilter\x18\x07 \x01(\x0b\x32\x31.moego.models.offering.v1.ServiceApplicableFilterH\x03R\x06\x66ilter\x88\x01\x01\x12\'\n\x07keyword\x18\x08 \x01(\tB\x08\xfa\x42\x05r\x03\x18\xff\x01H\x04R\x07keyword\x88\x01\x01\x12\x46\n\npagination\x18\t \x01(\x0b\x32!.moego.utils.v2.PaginationRequestH\x05R\npagination\x88\x01\x01\x12\x1f\n\x08inactive\x18\n \x01(\x08H\x06R\x08inactive\x88\x01\x01\x42\x14\n\x12_service_item_typeB\x0e\n\x0c_business_idB\t\n\x07_pet_idB\t\n\x07_filterB\n\n\x08_keywordB\r\n\x0b_paginationB\x0b\n\t_inactive\"\xc4\x01\n GetApplicableServiceListResponse\x12\\\n\rcategory_list\x18\x01 \x03(\x0b\x32\x37.moego.models.offering.v1.CustomizedServiceCategoryViewR\x0c\x63\x61tegoryList\x12\x42\n\npagination\x18\x02 \x01(\x0b\x32\".moego.utils.v2.PaginationResponseR\npagination\"g\n\x1d\x43ustomizedServiceByPetRequest\x12&\n\ncompany_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\tcompanyId\x12\x1e\n\x06pet_id\x18\x02 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\x05petId\"{\n\x1e\x43ustomizedServiceByPetResponse\x12Y\n\x0cservice_list\x18\x01 \x03(\x0b\x32\x36.moego.models.offering.v1.ServiceWithPetCustomizedInfoR\x0bserviceList\"}\n\x17GetServiceDetailRequest\x12+\n\ncompany_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00H\x00R\tcompanyId\x88\x01\x01\x12&\n\nservice_id\x18\x02 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\tserviceIdB\r\n\x0b_company_id\"f\n\x18GetServiceDetailResponse\x12J\n\x07service\x18\x01 \x01(\x0b\x32&.moego.models.offering.v1.ServiceModelB\x08\xfa\x42\x05\x8a\x01\x02\x10\x01R\x07service\"\x8a\x03\n\x1aRemoveServiceFilterRequest\x12&\n\ncompany_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\tcompanyId\x12>\n\x08pet_type\x18\x02 \x01(\x0e\x32!.moego.models.customer.v1.PetTypeH\x00R\x07petType\x12\x1d\n\tpet_breed\x18\x03 \x01(\tH\x00R\x08petBreed\x12 \n\x0bpet_size_id\x18\x04 \x01(\x03H\x00R\tpetSizeId\x12)\n\x10pet_coat_type_id\x18\x05 \x01(\x03H\x00R\rpetCoatTypeId\x12\x1f\n\nservice_id\x18\x06 \x01(\x03H\x00R\tserviceId\x12(\n\x0flodging_type_id\x18\x07 \x01(\x03H\x00R\rlodgingTypeId\x12!\n\x0b\x62usiness_id\x18\x08 \x01(\x03H\x00R\nbusinessId\x12\x1b\n\x08staff_id\x18\t \x01(\x03H\x00R\x07staffIdB\r\n\x06\x66ilter\x12\x03\xf8\x42\x01\"\x1d\n\x1bRemoveServiceFilterResponse\"D\n\x1aGetServiceItemTypesRequest\x12&\n\ncompany_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\tcompanyId\"v\n\x1bGetServiceItemTypesResponse\x12W\n\x12service_item_types\x18\x01 \x03(\x0e\x32).moego.models.offering.v1.ServiceItemTypeR\x10serviceItemTypes\"\xdf\x06\n\x12ListServiceRequest\x12h\n\x11service_item_type\x18\x01 \x01(\x0e\x32).moego.models.offering.v1.ServiceItemTypeB\x0c\x18\x01\xfa\x42\x07\x82\x01\x04\x10\x01 \x00H\x00R\x0fserviceItemType\x88\x01\x01\x12K\n\npagination\x18\x02 \x01(\x0b\x32!.moego.utils.v2.PaginationRequestB\x08\xfa\x42\x05\x8a\x01\x02\x10\x01R\npagination\x12\x33\n\x0c\x62usiness_ids\x18\x03 \x03(\x03\x42\x10\xfa\x42\r\x92\x01\n\x10\x64\x18\x01\"\x04\"\x02 \x00R\x0b\x62usinessIds\x12Y\n\x0cservice_type\x18\x04 \x01(\x0e\x32%.moego.models.offering.v1.ServiceTypeB\n\xfa\x42\x07\x82\x01\x04\x10\x01 \x00H\x01R\x0bserviceType\x88\x01\x01\x12\x31\n\x10token_company_id\x18\x05 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\x0etokenCompanyId\x12X\n\x08order_by\x18\x06 \x01(\x0e\x32,.moego.models.offering.v1.ServiceOrderByTypeB\n\xfa\x42\x07\x82\x01\x04\x10\x01 \x00H\x02R\x07orderBy\x88\x01\x01\x12\x1f\n\x08inactive\x18\x07 \x01(\x08H\x03R\x08inactive\x88\x01\x01\x12h\n\x12service_item_types\x18\x08 \x03(\x0e\x32).moego.models.offering.v1.ServiceItemTypeB\x0f\xfa\x42\x0c\x92\x01\t\"\x07\x82\x01\x04\x10\x01 \x00R\x10serviceItemTypes\x12\x44\n\x16prerequisite_class_ids\x18\t \x03(\x03\x42\x0e\xfa\x42\x0b\x92\x01\x08\x10\x64\"\x04\"\x02 \x00R\x14prerequisiteClassIds\x12\x43\n\x1b\x66ilter_prerequisite_classes\x18\n \x01(\x08H\x04R\x19\x66ilterPrerequisiteClasses\x88\x01\x01\x42\x14\n\x12_service_item_typeB\x0f\n\r_service_typeB\x0b\n\t_order_byB\x0b\n\t_inactiveB\x1e\n\x1c_filter_prerequisite_classes\"\x9d\x01\n\x13ListServiceResponse\x12\x42\n\x08services\x18\x01 \x03(\x0b\x32&.moego.models.offering.v1.ServiceModelR\x08services\x12\x42\n\npagination\x18\x02 \x01(\x0b\x32\".moego.utils.v2.PaginationResponseR\npagination\"\xa1\x01\n\x1bListAvailableStaffIdRequest\x12&\n\ncompany_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\tcompanyId\x12(\n\x0b\x62usiness_id\x18\x02 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\nbusinessId\x12\x30\n\x0bservice_ids\x18\x03 \x03(\x03\x42\x0f\xfa\x42\x0c\x92\x01\t\x10\xd0\x0f\"\x04\"\x02 \x00R\nserviceIds\"\xfd\x02\n\x1cListAvailableStaffIdResponse\x12\x86\x01\n\x17service_id_to_staff_ids\x18\x01 \x03(\x0b\x32P.moego.service.offering.v1.ListAvailableStaffIdResponse.ServiceIdToStaffIdsEntryR\x13serviceIdToStaffIds\x1a\x88\x01\n\x18ServiceIdToStaffIdsEntry\x12\x10\n\x03key\x18\x01 \x01(\x03R\x03key\x12V\n\x05value\x18\x02 \x01(\x0b\<EMAIL>\x05value:\x02\x38\x01\x1aI\n\x08StaffIds\x12 \n\x0cis_all_staff\x18\x01 \x01(\x08R\nisAllStaff\x12\x1b\n\tstaff_ids\x18\x02 \x03(\x03R\x08staffIds\"w\n\x19ListBundleServicesRequest\x12&\n\ncompany_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\tcompanyId\x12\x32\n\x0bservice_ids\x18\x02 \x03(\x03\x42\x11\xfa\x42\x0e\x92\x01\x0b\x10\xd0\x0f\x18\x01\"\x04\"\x02 \x00R\nserviceIds\"~\n\x1aListBundleServicesResponse\x12`\n\x0f\x62undle_services\x18\x01 \x03(\x0b\x32\x37.moego.models.offering.v1.ServiceBundleSaleMappingModelR\x0e\x62undleServices\"\x82\x03\n\x15ListCategoriesRequest\x12&\n\ncompany_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\tcompanyId\x12O\n\x06\x66ilter\x18\x02 \x01(\x0b\x32\x37.moego.service.offering.v1.ListCategoriesRequest.FilterR\x06\x66ilter\x1a\xef\x01\n\x06\x46ilter\x12[\n\rservice_types\x18\x01 \x03(\x0e\x32%.moego.models.offering.v1.ServiceTypeB\x0f\xfa\x42\x0c\x92\x01\t\"\x07\x82\x01\x04\x10\x01 \x00R\x0cserviceTypes\x12h\n\x12service_item_types\x18\x02 \x03(\x0e\x32).moego.models.offering.v1.ServiceItemTypeB\x0f\xfa\x42\x0c\x92\x01\t\"\x07\x82\x01\x04\x10\x01 \x00R\x10serviceItemTypes\x12\x1e\n\x03ids\x18\x03 \x03(\x03\x42\x0c\xfa\x42\t\x92\x01\x06\"\x04\"\x02 \x00R\x03ids\"a\n\x16ListCategoriesResponse\x12G\n\ncategories\x18\x01 \x03(\x0b\x32\'.moego.models.offering.v1.CategoryModelR\ncategories\"\x8a\x01\n\x17\x43reateCategoriesRequest\x12&\n\ncompany_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\tcompanyId\x12G\n\ncategories\x18\x02 \x03(\x0b\x32\'.moego.models.offering.v1.CategoryModelR\ncategories\"c\n\x18\x43reateCategoriesResponse\x12G\n\ncategories\x18\x01 \x03(\x0b\x32\'.moego.models.offering.v1.CategoryModelR\ncategories2\xf5\x11\n\x18ServiceManagementService\x12t\n\rCreateService\x12/.moego.service.offering.v1.CreateServiceRequest\x1a\x30.moego.service.offering.v1.CreateServiceResponse\"\x00\x12t\n\rUpdateService\x12/.moego.service.offering.v1.UpdateServiceRequest\x1a\x30.moego.service.offering.v1.UpdateServiceResponse\"\x00\x12w\n\x0eGetServiceList\x12\x30.moego.service.offering.v1.GetServiceListRequest\x1a\x31.moego.service.offering.v1.GetServiceListResponse\"\x00\x12\x9e\x01\n\x1bGetServiceByPetAndServiceId\x12=.moego.service.offering.v1.GetServiceByPetAndServiceIdRequest\x1a>.moego.service.offering.v1.GetServiceByPetAndServiceIdResponse\"\x00\x12z\n\x0fOverrideService\x12\x31.moego.service.offering.v1.OverrideServiceRequest\x1a\x32.moego.service.offering.v1.OverrideServiceResponse\"\x00\x12\x95\x01\n\x18GetApplicableServiceList\x12:.moego.service.offering.v1.GetApplicableServiceListRequest\x1a;.moego.service.offering.v1.GetApplicableServiceListResponse\"\x00\x12\x8f\x01\n\x16\x43ustomizedServiceByPet\x12\x38.moego.service.offering.v1.CustomizedServiceByPetRequest\x1a\x39.moego.service.offering.v1.CustomizedServiceByPetResponse\"\x00\x12}\n\x10GetServiceDetail\x12\x32.moego.service.offering.v1.GetServiceDetailRequest\x1a\x33.moego.service.offering.v1.GetServiceDetailResponse\"\x00\x12\x86\x01\n\x13GetServiceListByIds\x12\x35.moego.service.offering.v1.GetServiceListByIdsRequest\x1a\x36.moego.service.offering.v1.GetServiceListByIdsResponse\"\x00\x12\x86\x01\n\x13RemoveServiceFilter\x12\x35.moego.service.offering.v1.RemoveServiceFilterRequest\x1a\x36.moego.service.offering.v1.RemoveServiceFilterResponse\"\x00\x12\x86\x01\n\x13GetServiceItemTypes\x12\x35.moego.service.offering.v1.GetServiceItemTypesRequest\x1a\x36.moego.service.offering.v1.GetServiceItemTypesResponse\"\x00\x12n\n\x0bListService\x12-.moego.service.offering.v1.ListServiceRequest\x1a..moego.service.offering.v1.ListServiceResponse\"\x00\x12\x98\x01\n\x19\x42\x61tchGetCustomizedService\x12;.moego.service.offering.v1.BatchGetCustomizedServiceRequest\x1a<.moego.service.offering.v1.BatchGetCustomizedServiceResponse\"\x00\x12\x89\x01\n\x14ListAvailableStaffId\x12\x36.moego.service.offering.v1.ListAvailableStaffIdRequest\x1a\x37.moego.service.offering.v1.ListAvailableStaffIdResponse\"\x00\x12\x83\x01\n\x12ListBundleServices\x12\x34.moego.service.offering.v1.ListBundleServicesRequest\x1a\x35.moego.service.offering.v1.ListBundleServicesResponse\"\x00\x12w\n\x0eListCategories\x12\x30.moego.service.offering.v1.ListCategoriesRequest\x1a\x31.moego.service.offering.v1.ListCategoriesResponse\"\x00\x12}\n\x10\x43reateCategories\x12\x32.moego.service.offering.v1.CreateCategoriesRequest\x1a\x33.moego.service.offering.v1.CreateCategoriesResponse\"\x00\x42\x83\x01\n!com.moego.idl.service.offering.v1P\x01Z\\github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/offering/v1;offeringsvcpbb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'moego.service.offering.v1.service_service_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n!com.moego.idl.service.offering.v1P\001Z\\github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/offering/v1;offeringsvcpb'
  _globals['_CREATESERVICEREQUEST'].oneofs_by_name['operator_id']._loaded_options = None
  _globals['_CREATESERVICEREQUEST'].oneofs_by_name['operator_id']._serialized_options = b'\370B\001'
  _globals['_CREATESERVICEREQUEST'].fields_by_name['create_service_def']._loaded_options = None
  _globals['_CREATESERVICEREQUEST'].fields_by_name['create_service_def']._serialized_options = b'\372B\005\212\001\002\020\001'
  _globals['_CREATESERVICEREQUEST'].fields_by_name['token_company_id']._loaded_options = None
  _globals['_CREATESERVICEREQUEST'].fields_by_name['token_company_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_CREATESERVICEREQUEST'].fields_by_name['token_staff_id']._loaded_options = None
  _globals['_CREATESERVICEREQUEST'].fields_by_name['token_staff_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_CREATESERVICEREQUEST'].fields_by_name['internal_operator']._loaded_options = None
  _globals['_CREATESERVICEREQUEST'].fields_by_name['internal_operator']._serialized_options = b'\372B\007r\005\020\001\030\377\001'
  _globals['_CREATESERVICERESPONSE'].fields_by_name['service']._loaded_options = None
  _globals['_CREATESERVICERESPONSE'].fields_by_name['service']._serialized_options = b'\372B\005\212\001\002\020\001'
  _globals['_UPDATESERVICEREQUEST'].oneofs_by_name['operator_id']._loaded_options = None
  _globals['_UPDATESERVICEREQUEST'].oneofs_by_name['operator_id']._serialized_options = b'\370B\001'
  _globals['_UPDATESERVICEREQUEST'].fields_by_name['update_service_def']._loaded_options = None
  _globals['_UPDATESERVICEREQUEST'].fields_by_name['update_service_def']._serialized_options = b'\372B\005\212\001\002\020\001'
  _globals['_UPDATESERVICEREQUEST'].fields_by_name['token_company_id']._loaded_options = None
  _globals['_UPDATESERVICEREQUEST'].fields_by_name['token_company_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_UPDATESERVICEREQUEST'].fields_by_name['token_staff_id']._loaded_options = None
  _globals['_UPDATESERVICEREQUEST'].fields_by_name['token_staff_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_UPDATESERVICEREQUEST'].fields_by_name['internal_operator']._loaded_options = None
  _globals['_UPDATESERVICEREQUEST'].fields_by_name['internal_operator']._serialized_options = b'\372B\007r\005\020\001\030\377\001'
  _globals['_UPDATESERVICERESPONSE'].fields_by_name['service']._loaded_options = None
  _globals['_UPDATESERVICERESPONSE'].fields_by_name['service']._serialized_options = b'\372B\005\212\001\002\020\001'
  _globals['_GETSERVICELISTREQUEST'].fields_by_name['token_company_id']._loaded_options = None
  _globals['_GETSERVICELISTREQUEST'].fields_by_name['token_company_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_GETSERVICELISTREQUEST'].fields_by_name['keyword']._loaded_options = None
  _globals['_GETSERVICELISTREQUEST'].fields_by_name['keyword']._serialized_options = b'\372B\005r\003\030\377\001'
  _globals['_GETSERVICELISTBYIDSREQUEST'].fields_by_name['company_id']._loaded_options = None
  _globals['_GETSERVICELISTBYIDSREQUEST'].fields_by_name['company_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_GETSERVICEBYPETANDSERVICEIDREQUEST_PETIDWITHSERVICEIDLISTENTRY']._loaded_options = None
  _globals['_GETSERVICEBYPETANDSERVICEIDREQUEST_PETIDWITHSERVICEIDLISTENTRY']._serialized_options = b'8\001'
  _globals['_GETSERVICEBYPETANDSERVICEIDREQUEST'].fields_by_name['company_id']._loaded_options = None
  _globals['_GETSERVICEBYPETANDSERVICEIDREQUEST'].fields_by_name['company_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_GETSERVICEBYPETANDSERVICEIDREQUEST'].fields_by_name['business_id']._loaded_options = None
  _globals['_GETSERVICEBYPETANDSERVICEIDREQUEST'].fields_by_name['business_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_GETSERVICEBYPETANDSERVICEIDRESPONSE_PETIDWITHAVAILABLESERVICELISTENTRY']._loaded_options = None
  _globals['_GETSERVICEBYPETANDSERVICEIDRESPONSE_PETIDWITHAVAILABLESERVICELISTENTRY']._serialized_options = b'8\001'
  _globals['_CUSTOMIZEDSERVICEQUERYCONDITION'].fields_by_name['service_id']._loaded_options = None
  _globals['_CUSTOMIZEDSERVICEQUERYCONDITION'].fields_by_name['service_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_CUSTOMIZEDSERVICEQUERYCONDITION'].fields_by_name['business_id']._loaded_options = None
  _globals['_CUSTOMIZEDSERVICEQUERYCONDITION'].fields_by_name['business_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_CUSTOMIZEDSERVICEQUERYCONDITION'].fields_by_name['pet_id']._loaded_options = None
  _globals['_CUSTOMIZEDSERVICEQUERYCONDITION'].fields_by_name['pet_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_CUSTOMIZEDSERVICEQUERYCONDITION'].fields_by_name['staff_id']._loaded_options = None
  _globals['_CUSTOMIZEDSERVICEQUERYCONDITION'].fields_by_name['staff_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_BATCHGETCUSTOMIZEDSERVICEREQUEST'].fields_by_name['company_id']._loaded_options = None
  _globals['_BATCHGETCUSTOMIZEDSERVICEREQUEST'].fields_by_name['company_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_OVERRIDESERVICEREQUEST_OVERRIDERULESBYSERVICEIDENTRY']._loaded_options = None
  _globals['_OVERRIDESERVICEREQUEST_OVERRIDERULESBYSERVICEIDENTRY']._serialized_options = b'8\001'
  _globals['_OVERRIDESERVICEREQUEST'].fields_by_name['company_id']._loaded_options = None
  _globals['_OVERRIDESERVICEREQUEST'].fields_by_name['company_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_GETAPPLICABLESERVICELISTREQUEST'].fields_by_name['company_id']._loaded_options = None
  _globals['_GETAPPLICABLESERVICELISTREQUEST'].fields_by_name['company_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_GETAPPLICABLESERVICELISTREQUEST'].fields_by_name['business_id']._loaded_options = None
  _globals['_GETAPPLICABLESERVICELISTREQUEST'].fields_by_name['business_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_GETAPPLICABLESERVICELISTREQUEST'].fields_by_name['keyword']._loaded_options = None
  _globals['_GETAPPLICABLESERVICELISTREQUEST'].fields_by_name['keyword']._serialized_options = b'\372B\005r\003\030\377\001'
  _globals['_CUSTOMIZEDSERVICEBYPETREQUEST'].fields_by_name['company_id']._loaded_options = None
  _globals['_CUSTOMIZEDSERVICEBYPETREQUEST'].fields_by_name['company_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_CUSTOMIZEDSERVICEBYPETREQUEST'].fields_by_name['pet_id']._loaded_options = None
  _globals['_CUSTOMIZEDSERVICEBYPETREQUEST'].fields_by_name['pet_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_GETSERVICEDETAILREQUEST'].fields_by_name['company_id']._loaded_options = None
  _globals['_GETSERVICEDETAILREQUEST'].fields_by_name['company_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_GETSERVICEDETAILREQUEST'].fields_by_name['service_id']._loaded_options = None
  _globals['_GETSERVICEDETAILREQUEST'].fields_by_name['service_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_GETSERVICEDETAILRESPONSE'].fields_by_name['service']._loaded_options = None
  _globals['_GETSERVICEDETAILRESPONSE'].fields_by_name['service']._serialized_options = b'\372B\005\212\001\002\020\001'
  _globals['_REMOVESERVICEFILTERREQUEST'].oneofs_by_name['filter']._loaded_options = None
  _globals['_REMOVESERVICEFILTERREQUEST'].oneofs_by_name['filter']._serialized_options = b'\370B\001'
  _globals['_REMOVESERVICEFILTERREQUEST'].fields_by_name['company_id']._loaded_options = None
  _globals['_REMOVESERVICEFILTERREQUEST'].fields_by_name['company_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_GETSERVICEITEMTYPESREQUEST'].fields_by_name['company_id']._loaded_options = None
  _globals['_GETSERVICEITEMTYPESREQUEST'].fields_by_name['company_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_LISTSERVICEREQUEST'].fields_by_name['service_item_type']._loaded_options = None
  _globals['_LISTSERVICEREQUEST'].fields_by_name['service_item_type']._serialized_options = b'\030\001\372B\007\202\001\004\020\001 \000'
  _globals['_LISTSERVICEREQUEST'].fields_by_name['pagination']._loaded_options = None
  _globals['_LISTSERVICEREQUEST'].fields_by_name['pagination']._serialized_options = b'\372B\005\212\001\002\020\001'
  _globals['_LISTSERVICEREQUEST'].fields_by_name['business_ids']._loaded_options = None
  _globals['_LISTSERVICEREQUEST'].fields_by_name['business_ids']._serialized_options = b'\372B\r\222\001\n\020d\030\001\"\004\"\002 \000'
  _globals['_LISTSERVICEREQUEST'].fields_by_name['service_type']._loaded_options = None
  _globals['_LISTSERVICEREQUEST'].fields_by_name['service_type']._serialized_options = b'\372B\007\202\001\004\020\001 \000'
  _globals['_LISTSERVICEREQUEST'].fields_by_name['token_company_id']._loaded_options = None
  _globals['_LISTSERVICEREQUEST'].fields_by_name['token_company_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_LISTSERVICEREQUEST'].fields_by_name['order_by']._loaded_options = None
  _globals['_LISTSERVICEREQUEST'].fields_by_name['order_by']._serialized_options = b'\372B\007\202\001\004\020\001 \000'
  _globals['_LISTSERVICEREQUEST'].fields_by_name['service_item_types']._loaded_options = None
  _globals['_LISTSERVICEREQUEST'].fields_by_name['service_item_types']._serialized_options = b'\372B\014\222\001\t\"\007\202\001\004\020\001 \000'
  _globals['_LISTSERVICEREQUEST'].fields_by_name['prerequisite_class_ids']._loaded_options = None
  _globals['_LISTSERVICEREQUEST'].fields_by_name['prerequisite_class_ids']._serialized_options = b'\372B\013\222\001\010\020d\"\004\"\002 \000'
  _globals['_LISTAVAILABLESTAFFIDREQUEST'].fields_by_name['company_id']._loaded_options = None
  _globals['_LISTAVAILABLESTAFFIDREQUEST'].fields_by_name['company_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_LISTAVAILABLESTAFFIDREQUEST'].fields_by_name['business_id']._loaded_options = None
  _globals['_LISTAVAILABLESTAFFIDREQUEST'].fields_by_name['business_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_LISTAVAILABLESTAFFIDREQUEST'].fields_by_name['service_ids']._loaded_options = None
  _globals['_LISTAVAILABLESTAFFIDREQUEST'].fields_by_name['service_ids']._serialized_options = b'\372B\014\222\001\t\020\320\017\"\004\"\002 \000'
  _globals['_LISTAVAILABLESTAFFIDRESPONSE_SERVICEIDTOSTAFFIDSENTRY']._loaded_options = None
  _globals['_LISTAVAILABLESTAFFIDRESPONSE_SERVICEIDTOSTAFFIDSENTRY']._serialized_options = b'8\001'
  _globals['_LISTBUNDLESERVICESREQUEST'].fields_by_name['company_id']._loaded_options = None
  _globals['_LISTBUNDLESERVICESREQUEST'].fields_by_name['company_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_LISTBUNDLESERVICESREQUEST'].fields_by_name['service_ids']._loaded_options = None
  _globals['_LISTBUNDLESERVICESREQUEST'].fields_by_name['service_ids']._serialized_options = b'\372B\016\222\001\013\020\320\017\030\001\"\004\"\002 \000'
  _globals['_LISTCATEGORIESREQUEST_FILTER'].fields_by_name['service_types']._loaded_options = None
  _globals['_LISTCATEGORIESREQUEST_FILTER'].fields_by_name['service_types']._serialized_options = b'\372B\014\222\001\t\"\007\202\001\004\020\001 \000'
  _globals['_LISTCATEGORIESREQUEST_FILTER'].fields_by_name['service_item_types']._loaded_options = None
  _globals['_LISTCATEGORIESREQUEST_FILTER'].fields_by_name['service_item_types']._serialized_options = b'\372B\014\222\001\t\"\007\202\001\004\020\001 \000'
  _globals['_LISTCATEGORIESREQUEST_FILTER'].fields_by_name['ids']._loaded_options = None
  _globals['_LISTCATEGORIESREQUEST_FILTER'].fields_by_name['ids']._serialized_options = b'\372B\t\222\001\006\"\004\"\002 \000'
  _globals['_LISTCATEGORIESREQUEST'].fields_by_name['company_id']._loaded_options = None
  _globals['_LISTCATEGORIESREQUEST'].fields_by_name['company_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_CREATECATEGORIESREQUEST'].fields_by_name['company_id']._loaded_options = None
  _globals['_CREATECATEGORIESREQUEST'].fields_by_name['company_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_CREATESERVICEREQUEST']._serialized_start=476
  _globals['_CREATESERVICEREQUEST']._serialized_end=777
  _globals['_CREATESERVICERESPONSE']._serialized_start=779
  _globals['_CREATESERVICERESPONSE']._serialized_end=878
  _globals['_UPDATESERVICEREQUEST']._serialized_start=881
  _globals['_UPDATESERVICEREQUEST']._serialized_end=1182
  _globals['_UPDATESERVICERESPONSE']._serialized_start=1184
  _globals['_UPDATESERVICERESPONSE']._serialized_end=1283
  _globals['_GETSERVICELISTREQUEST']._serialized_start=1286
  _globals['_GETSERVICELISTREQUEST']._serialized_end=1824
  _globals['_GETSERVICELISTRESPONSE']._serialized_start=1827
  _globals['_GETSERVICELISTRESPONSE']._serialized_end=2004
  _globals['_GETSERVICELISTBYIDSREQUEST']._serialized_start=2006
  _globals['_GETSERVICELISTBYIDSREQUEST']._serialized_end=2127
  _globals['_GETSERVICELISTBYIDSRESPONSE']._serialized_start=2129
  _globals['_GETSERVICELISTBYIDSRESPONSE']._serialized_end=2230
  _globals['_GETSERVICEBYPETANDSERVICEIDREQUEST']._serialized_start=2233
  _globals['_GETSERVICEBYPETANDSERVICEIDREQUEST']._serialized_end=2627
  _globals['_GETSERVICEBYPETANDSERVICEIDREQUEST_PETIDWITHSERVICEIDLISTENTRY']._serialized_start=2511
  _globals['_GETSERVICEBYPETANDSERVICEIDREQUEST_PETIDWITHSERVICEIDLISTENTRY']._serialized_end=2611
  _globals['_GETSERVICEBYPETANDSERVICEIDRESPONSE']._serialized_start=2630
  _globals['_GETSERVICEBYPETANDSERVICEIDRESPONSE']._serialized_end=2978
  _globals['_GETSERVICEBYPETANDSERVICEIDRESPONSE_PETIDWITHAVAILABLESERVICELISTENTRY']._serialized_start=2845
  _globals['_GETSERVICEBYPETANDSERVICEIDRESPONSE_PETIDWITHAVAILABLESERVICELISTENTRY']._serialized_end=2978
  _globals['_CUSTOMIZEDSERVICEQUERYCONDITION']._serialized_start=2981
  _globals['_CUSTOMIZEDSERVICEQUERYCONDITION']._serialized_end=3219
  _globals['_BATCHGETCUSTOMIZEDSERVICEREQUEST']._serialized_start=3222
  _globals['_BATCHGETCUSTOMIZEDSERVICEREQUEST']._serialized_end=3406
  _globals['_BATCHGETCUSTOMIZEDSERVICERESPONSE']._serialized_start=3409
  _globals['_BATCHGETCUSTOMIZEDSERVICERESPONSE']._serialized_end=3816
  _globals['_BATCHGETCUSTOMIZEDSERVICERESPONSE_SERVICEWITHCUSTOMIZEDINFO']._serialized_start=3592
  _globals['_BATCHGETCUSTOMIZEDSERVICERESPONSE_SERVICEWITHCUSTOMIZEDINFO']._serialized_end=3816
  _globals['_OVERRIDESERVICEREQUEST']._serialized_start=3819
  _globals['_OVERRIDESERVICEREQUEST']._serialized_end=4153
  _globals['_OVERRIDESERVICEREQUEST_OVERRIDERULESBYSERVICEIDENTRY']._serialized_start=4031
  _globals['_OVERRIDESERVICEREQUEST_OVERRIDERULESBYSERVICEIDENTRY']._serialized_end=4153
  _globals['_OVERRIDESERVICERESPONSE']._serialized_start=4155
  _globals['_OVERRIDESERVICERESPONSE']._serialized_end=4180
  _globals['_GETAPPLICABLESERVICELISTREQUEST']._serialized_start=4183
  _globals['_GETAPPLICABLESERVICELISTREQUEST']._serialized_end=4862
  _globals['_GETAPPLICABLESERVICELISTRESPONSE']._serialized_start=4865
  _globals['_GETAPPLICABLESERVICELISTRESPONSE']._serialized_end=5061
  _globals['_CUSTOMIZEDSERVICEBYPETREQUEST']._serialized_start=5063
  _globals['_CUSTOMIZEDSERVICEBYPETREQUEST']._serialized_end=5166
  _globals['_CUSTOMIZEDSERVICEBYPETRESPONSE']._serialized_start=5168
  _globals['_CUSTOMIZEDSERVICEBYPETRESPONSE']._serialized_end=5291
  _globals['_GETSERVICEDETAILREQUEST']._serialized_start=5293
  _globals['_GETSERVICEDETAILREQUEST']._serialized_end=5418
  _globals['_GETSERVICEDETAILRESPONSE']._serialized_start=5420
  _globals['_GETSERVICEDETAILRESPONSE']._serialized_end=5522
  _globals['_REMOVESERVICEFILTERREQUEST']._serialized_start=5525
  _globals['_REMOVESERVICEFILTERREQUEST']._serialized_end=5919
  _globals['_REMOVESERVICEFILTERRESPONSE']._serialized_start=5921
  _globals['_REMOVESERVICEFILTERRESPONSE']._serialized_end=5950
  _globals['_GETSERVICEITEMTYPESREQUEST']._serialized_start=5952
  _globals['_GETSERVICEITEMTYPESREQUEST']._serialized_end=6020
  _globals['_GETSERVICEITEMTYPESRESPONSE']._serialized_start=6022
  _globals['_GETSERVICEITEMTYPESRESPONSE']._serialized_end=6140
  _globals['_LISTSERVICEREQUEST']._serialized_start=6143
  _globals['_LISTSERVICEREQUEST']._serialized_end=7006
  _globals['_LISTSERVICERESPONSE']._serialized_start=7009
  _globals['_LISTSERVICERESPONSE']._serialized_end=7166
  _globals['_LISTAVAILABLESTAFFIDREQUEST']._serialized_start=7169
  _globals['_LISTAVAILABLESTAFFIDREQUEST']._serialized_end=7330
  _globals['_LISTAVAILABLESTAFFIDRESPONSE']._serialized_start=7333
  _globals['_LISTAVAILABLESTAFFIDRESPONSE']._serialized_end=7714
  _globals['_LISTAVAILABLESTAFFIDRESPONSE_SERVICEIDTOSTAFFIDSENTRY']._serialized_start=7503
  _globals['_LISTAVAILABLESTAFFIDRESPONSE_SERVICEIDTOSTAFFIDSENTRY']._serialized_end=7639
  _globals['_LISTAVAILABLESTAFFIDRESPONSE_STAFFIDS']._serialized_start=7641
  _globals['_LISTAVAILABLESTAFFIDRESPONSE_STAFFIDS']._serialized_end=7714
  _globals['_LISTBUNDLESERVICESREQUEST']._serialized_start=7716
  _globals['_LISTBUNDLESERVICESREQUEST']._serialized_end=7835
  _globals['_LISTBUNDLESERVICESRESPONSE']._serialized_start=7837
  _globals['_LISTBUNDLESERVICESRESPONSE']._serialized_end=7963
  _globals['_LISTCATEGORIESREQUEST']._serialized_start=7966
  _globals['_LISTCATEGORIESREQUEST']._serialized_end=8352
  _globals['_LISTCATEGORIESREQUEST_FILTER']._serialized_start=8113
  _globals['_LISTCATEGORIESREQUEST_FILTER']._serialized_end=8352
  _globals['_LISTCATEGORIESRESPONSE']._serialized_start=8354
  _globals['_LISTCATEGORIESRESPONSE']._serialized_end=8451
  _globals['_CREATECATEGORIESREQUEST']._serialized_start=8454
  _globals['_CREATECATEGORIESREQUEST']._serialized_end=8592
  _globals['_CREATECATEGORIESRESPONSE']._serialized_start=8594
  _globals['_CREATECATEGORIESRESPONSE']._serialized_end=8693
  _globals['_SERVICEMANAGEMENTSERVICE']._serialized_start=8696
  _globals['_SERVICEMANAGEMENTSERVICE']._serialized_end=10989
# @@protoc_insertion_point(module_scope)
