# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: moego/models/pay_ops/v1/business_dispute_models.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'moego/models/pay_ops/v1/business_dispute_models.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n5moego/models/pay_ops/v1/business_dispute_models.proto\x12\x17moego.models.pay_ops.v1\"\xbd\x04\n\x12StripeDisputeModel\x12\x0e\n\x02id\x18\x01 \x01(\x05R\x02id\x12\x1f\n\x0b\x62usiness_id\x18\x02 \x01(\x05R\nbusinessId\x12\x1d\n\ndispute_id\x18\x03 \x01(\tR\tdisputeId\x12\x1d\n\npayment_id\x18\x04 \x01(\x05R\tpaymentId\x12*\n\x11payment_intent_id\x18\x05 \x01(\tR\x0fpaymentIntentId\x12%\n\x0epayment_method\x18\x06 \x01(\tR\rpaymentMethod\x12\x16\n\x06\x61mount\x18\x07 \x01(\x03R\x06\x61mount\x12\x1a\n\x08\x63urrency\x18\x08 \x01(\tR\x08\x63urrency\x12\x16\n\x06status\x18\t \x01(\tR\x06status\x12\x16\n\x06reason\x18\n \x01(\tR\x06reason\x12\x1a\n\x08\x63ustomer\x18\x0b \x01(\tR\x08\x63ustomer\x12\x1d\n\ncharged_on\x18\x0c \x01(\x03R\tchargedOn\x12\x1f\n\x0b\x64isputed_on\x18\r \x01(\x03R\ndisputedOn\x12!\n\x0cresponded_on\x18\x0e \x01(\x03R\x0brespondedOn\x12\x1d\n\ncreated_at\x18\x0f \x01(\x03R\tcreatedAt\x12\x1d\n\nupdated_at\x18\x10 \x01(\x03R\tupdatedAt\x12\x1d\n\ndeleted_at\x18\x11 \x01(\x03R\tdeletedAt\x12%\n\x0e\x62usiness_email\x18\x12 \x01(\tR\rbusinessEmail\"\xfd\x02\n\x16StripeDisputeInfoModel\x12\x0e\n\x02id\x18\x01 \x01(\tR\x02id\x12\x16\n\x06\x61mount\x18\x02 \x01(\x03R\x06\x61mount\x12\x1b\n\tcharge_id\x18\x03 \x01(\tR\x08\x63hargeId\x12\x1a\n\x08\x63urrency\x18\x04 \x01(\tR\x08\x63urrency\x12\x1a\n\x08metadata\x18\x05 \x01(\tR\x08metadata\x12\x16\n\x06reason\x18\x06 \x01(\tR\x06reason\x12\x16\n\x06status\x18\x07 \x01(\tR\x06status\x12\x18\n\x07\x63reated\x18\x08 \x01(\x03R\x07\x63reated\x12\x42\n\x08\x65vidence\x18\t \x01(\x0b\x32&.moego.models.pay_ops.v1.EvidenceModelR\x08\x65vidence\x12X\n\x10\x65vidence_details\x18\n \x01(\x0b\x32-.moego.models.pay_ops.v1.EvidenceDetailsModelR\x0f\x65videnceDetails\"\x96\x01\n\x14\x45videnceDetailsModel\x12\x15\n\x06\x64ue_by\x18\x01 \x01(\x03R\x05\x64ueBy\x12!\n\x0chas_evidence\x18\x02 \x01(\x08R\x0bhasEvidence\x12\x19\n\x08past_due\x18\x03 \x01(\x08R\x07pastDue\x12)\n\x10submission_count\x18\x04 \x01(\x03R\x0fsubmissionCount\"\xc9\n\n\rEvidenceModel\x12.\n\x13\x61\x63\x63\x65ss_activity_log\x18\x01 \x01(\tR\x11\x61\x63\x63\x65ssActivityLog\x12\'\n\x0f\x62illing_address\x18\x02 \x01(\tR\x0e\x62illingAddress\x12/\n\x13\x63\x61ncellation_policy\x18\x03 \x01(\tR\x12\x63\x61ncellationPolicy\x12\x44\n\x1e\x63\x61ncellation_policy_disclosure\x18\x04 \x01(\tR\x1c\x63\x61ncellationPolicyDisclosure\x12\x33\n\x15\x63\x61ncellation_rebuttal\x18\x05 \x01(\tR\x14\x63\x61ncellationRebuttal\x12\x35\n\x16\x63ustomer_communication\x18\x06 \x01(\tR\x15\x63ustomerCommunication\x12\x34\n\x16\x63ustomer_email_address\x18\x07 \x01(\tR\x14\x63ustomerEmailAddress\x12#\n\rcustomer_name\x18\x08 \x01(\tR\x0c\x63ustomerName\x12\x30\n\x14\x63ustomer_purchase_ip\x18\t \x01(\tR\x12\x63ustomerPurchaseIp\x12-\n\x12\x63ustomer_signature\x18\n \x01(\tR\x11\x63ustomerSignature\x12\x44\n\x1e\x64uplicate_charge_documentation\x18\x0b \x01(\tR\x1c\x64uplicateChargeDocumentation\x12@\n\x1c\x64uplicate_charge_explanation\x18\x0c \x01(\tR\x1a\x64uplicateChargeExplanation\x12.\n\x13\x64uplicate_charge_id\x18\r \x01(\tR\x11\x64uplicateChargeId\x12/\n\x13product_description\x18\x0e \x01(\tR\x12productDescription\x12\x18\n\x07receipt\x18\x0f \x01(\tR\x07receipt\x12#\n\rrefund_policy\x18\x10 \x01(\tR\x0crefundPolicy\x12\x38\n\x18refund_policy_disclosure\x18\x11 \x01(\tR\x16refundPolicyDisclosure\x12<\n\x1arefund_refusal_explanation\x18\x12 \x01(\tR\x18refundRefusalExplanation\x12!\n\x0cservice_date\x18\x13 \x01(\tR\x0bserviceDate\x12\x33\n\x15service_documentation\x18\x14 \x01(\tR\x14serviceDocumentation\x12)\n\x10shipping_address\x18\x15 \x01(\tR\x0fshippingAddress\x12)\n\x10shipping_carrier\x18\x16 \x01(\tR\x0fshippingCarrier\x12#\n\rshipping_date\x18\x17 \x01(\tR\x0cshippingDate\x12\x35\n\x16shipping_documentation\x18\x18 \x01(\tR\x15shippingDocumentation\x12\x38\n\x18shipping_tracking_number\x18\x19 \x01(\tR\x16shippingTrackingNumber\x12-\n\x12uncategorized_file\x18\x1a \x01(\tR\x11uncategorizedFile\x12-\n\x12uncategorized_text\x18\x1b \x01(\tR\x11uncategorizedTextBz\n\x1f\x63om.moego.idl.models.pay_ops.v1P\x01ZUgithub.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/pay_ops/v1;payopspbb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'moego.models.pay_ops.v1.business_dispute_models_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n\037com.moego.idl.models.pay_ops.v1P\001ZUgithub.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/pay_ops/v1;payopspb'
  _globals['_STRIPEDISPUTEMODEL']._serialized_start=83
  _globals['_STRIPEDISPUTEMODEL']._serialized_end=656
  _globals['_STRIPEDISPUTEINFOMODEL']._serialized_start=659
  _globals['_STRIPEDISPUTEINFOMODEL']._serialized_end=1040
  _globals['_EVIDENCEDETAILSMODEL']._serialized_start=1043
  _globals['_EVIDENCEDETAILSMODEL']._serialized_end=1193
  _globals['_EVIDENCEMODEL']._serialized_start=1196
  _globals['_EVIDENCEMODEL']._serialized_end=2549
# @@protoc_insertion_point(module_scope)
