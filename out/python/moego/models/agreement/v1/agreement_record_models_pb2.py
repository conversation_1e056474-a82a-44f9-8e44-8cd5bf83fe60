# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: moego/models/agreement/v1/agreement_record_models.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'moego/models/agreement/v1/agreement_record_models.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from moego.models.agreement.v1 import agreement_enums_pb2 as moego_dot_models_dot_agreement_dot_v1_dot_agreement__enums__pb2
from moego.utils.v1 import status_messages_pb2 as moego_dot_utils_dot_v1_dot_status__messages__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n7moego/models/agreement/v1/agreement_record_models.proto\x12\x19moego.models.agreement.v1\x1a/moego/models/agreement/v1/agreement_enums.proto\x1a$moego/utils/v1/status_messages.proto\"\xc8\x07\n\x14\x41greementRecordModel\x12\x0e\n\x02id\x18\x01 \x01(\x03R\x02id\x12\x12\n\x04uuid\x18\x02 \x01(\tR\x04uuid\x12&\n\x0c\x61greement_id\x18\x03 \x01(\x03H\x00R\x0b\x61greementId\x88\x01\x01\x12\x1f\n\x0b\x62usiness_id\x18\x04 \x01(\x03R\nbusinessId\x12\x1f\n\x0b\x63ustomer_id\x18\x05 \x01(\x03R\ncustomerId\x12 \n\ttarget_id\x18\x06 \x01(\x03H\x01R\x08targetId\x88\x01\x01\x12.\n\x06status\x18\x07 \x01(\x0e\x32\x16.moego.utils.v1.StatusR\x06status\x12#\n\rservice_types\x18\x08 \x01(\x05R\x0cserviceTypes\x12L\n\rsigned_status\x18\t \x01(\x0e\x32\'.moego.models.agreement.v1.SignedStatusR\x0csignedStatus\x12K\n\x0bsigned_type\x18\n \x01(\x0e\x32%.moego.models.agreement.v1.SignedTypeH\x02R\nsignedType\x88\x01\x01\x12\x46\n\x0bsource_type\x18\x0b \x01(\x0e\x32%.moego.models.agreement.v1.SourceTypeR\nsourceType\x12%\n\x0e\x61greement_link\x18\x0c \x01(\tR\ragreementLink\x12\'\n\x0f\x61greement_title\x18\r \x01(\tR\x0e\x61greementTitle\x12\x30\n\x11\x61greement_content\x18\x0e \x01(\tH\x03R\x10\x61greementContent\x88\x01\x01\x12!\n\x0cupload_files\x18\x0f \x03(\tR\x0buploadFiles\x12!\n\tsignature\x18\x10 \x01(\tH\x04R\tsignature\x88\x01\x01\x12$\n\x0bsigned_time\x18\x11 \x01(\x03H\x05R\nsignedTime\x88\x01\x01\x12\x1f\n\x0b\x63reate_time\x18\x12 \x01(\x03R\ncreateTime\x12\x1f\n\x0bupdate_time\x18\x13 \x01(\x03R\nupdateTime\x12\x1d\n\ncompany_id\x18\x14 \x01(\x03R\tcompanyId\x12\x16\n\x06inputs\x18\x15 \x03(\tR\x06inputsB\x0f\n\r_agreement_idB\x0c\n\n_target_idB\x0e\n\x0c_signed_typeB\x14\n\x12_agreement_contentB\x0c\n\n_signatureB\x0e\n\x0c_signed_time\"\xaa\x07\n\x19\x41greementRecordSimpleView\x12\x0e\n\x02id\x18\x01 \x01(\x03R\x02id\x12\x12\n\x04uuid\x18\x02 \x01(\tR\x04uuid\x12&\n\x0c\x61greement_id\x18\x03 \x01(\x03H\x00R\x0b\x61greementId\x88\x01\x01\x12\x1f\n\x0b\x62usiness_id\x18\x04 \x01(\x03R\nbusinessId\x12\x1f\n\x0b\x63ustomer_id\x18\x05 \x01(\x03R\ncustomerId\x12 \n\ttarget_id\x18\x06 \x01(\x03H\x01R\x08targetId\x88\x01\x01\x12.\n\x06status\x18\x07 \x01(\x0e\x32\x16.moego.utils.v1.StatusR\x06status\x12#\n\rservice_types\x18\x08 \x01(\x05R\x0cserviceTypes\x12L\n\rsigned_status\x18\t \x01(\x0e\x32\'.moego.models.agreement.v1.SignedStatusR\x0csignedStatus\x12K\n\x0bsigned_type\x18\n \x01(\x0e\x32%.moego.models.agreement.v1.SignedTypeH\x02R\nsignedType\x88\x01\x01\x12\x46\n\x0bsource_type\x18\x0b \x01(\x0e\x32%.moego.models.agreement.v1.SourceTypeR\nsourceType\x12%\n\x0e\x61greement_link\x18\x0c \x01(\tR\ragreementLink\x12\'\n\x0f\x61greement_title\x18\r \x01(\tR\x0e\x61greementTitle\x12$\n\x0bsigned_time\x18\x0e \x01(\x03H\x03R\nsignedTime\x88\x01\x01\x12\x1f\n\x0b\x63reate_time\x18\x0f \x01(\x03R\ncreateTime\x12\x1f\n\x0bupdate_time\x18\x10 \x01(\x03R\nupdateTime\x12!\n\tsignature\x18\x11 \x01(\tH\x04R\tsignature\x88\x01\x01\x12\x30\n\x11\x61greement_content\x18\x12 \x01(\tH\x05R\x10\x61greementContent\x88\x01\x01\x12\x1d\n\ncompany_id\x18\x13 \x01(\x03R\tcompanyId\x12\x16\n\x06inputs\x18\x14 \x03(\tR\x06inputsB\x0f\n\r_agreement_idB\x0c\n\n_target_idB\x0e\n\x0c_signed_typeB\x0e\n\x0c_signed_timeB\x0c\n\n_signatureB\x14\n\x12_agreement_content\"\xd1\x02\n\x1e\x41greementWithRecentRecordsView\x12\x1f\n\x0b\x62usiness_id\x18\x01 \x01(\x03R\nbusinessId\x12!\n\x0c\x61greement_id\x18\x02 \x01(\x03R\x0b\x61greementId\x12\'\n\x0f\x61greement_title\x18\x03 \x01(\tR\x0e\x61greementTitle\x12,\n\x12recent_signed_time\x18\x04 \x01(\x03R\x10recentSignedTime\x12u\n\x1c\x61greement_record_simple_view\x18\x05 \x03(\x0b\x32\x34.moego.models.agreement.v1.AgreementRecordSimpleViewR\x19\x61greementRecordSimpleView\x12\x1d\n\ncompany_id\x18\x06 \x01(\x03R\tcompanyId\"w\n\"AgreementWithRecentRecordsViewList\x12Q\n\x06values\x18\x01 \x03(\x0b\x32\x39.moego.models.agreement.v1.AgreementWithRecentRecordsViewR\x06values\"\x98\x01\n\x1fUnsignedAgreementRecordListView\x12u\n\x1c\x61greement_record_simple_view\x18\x01 \x03(\x0b\x32\x34.moego.models.agreement.v1.AgreementRecordSimpleViewR\x19\x61greementRecordSimpleViewB\x81\x01\n!com.moego.idl.models.agreement.v1P\x01ZZgithub.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/agreement/v1;agreementpbb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'moego.models.agreement.v1.agreement_record_models_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n!com.moego.idl.models.agreement.v1P\001ZZgithub.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/agreement/v1;agreementpb'
  _globals['_AGREEMENTRECORDMODEL']._serialized_start=174
  _globals['_AGREEMENTRECORDMODEL']._serialized_end=1142
  _globals['_AGREEMENTRECORDSIMPLEVIEW']._serialized_start=1145
  _globals['_AGREEMENTRECORDSIMPLEVIEW']._serialized_end=2083
  _globals['_AGREEMENTWITHRECENTRECORDSVIEW']._serialized_start=2086
  _globals['_AGREEMENTWITHRECENTRECORDSVIEW']._serialized_end=2423
  _globals['_AGREEMENTWITHRECENTRECORDSVIEWLIST']._serialized_start=2425
  _globals['_AGREEMENTWITHRECENTRECORDSVIEWLIST']._serialized_end=2544
  _globals['_UNSIGNEDAGREEMENTRECORDLISTVIEW']._serialized_start=2547
  _globals['_UNSIGNEDAGREEMENTRECORDLISTVIEW']._serialized_end=2699
# @@protoc_insertion_point(module_scope)
