# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: moego/models/message/v1/message_enums.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'moego/models/message/v1/message_enums.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from validate import validate_pb2 as validate_dot_validate__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n+moego/models/message/v1/message_enums.proto\x12\x17moego.models.message.v1\x1a\x17validate/validate.proto\"o\n\x0fMessageTypeList\x12\\\n\rmessage_types\x18\x01 \x03(\x0e\x32$.moego.models.message.v1.MessageTypeB\x11\xfa\x42\x0e\x92\x01\x0b\x18\x01\"\x07\x82\x01\x04\x10\x01 \x00R\x0cmessageTypes*\x86\x01\n\x0bMessageType\x12\x1c\n\x18MESSAGE_TYPE_UNSPECIFIED\x10\x00\x12\x14\n\x10MESSAGE_TYPE_SMS\x10\x01\x12\x16\n\x12MESSAGE_TYPE_EMAIL\x10\x02\x12\x15\n\x11MESSAGE_TYPE_CALL\x10\x03\x12\x14\n\x10MESSAGE_TYPE_APP\x10\x04*\x83\t\n\nTargetType\x12\x1b\n\x17TARGET_TYPE_UNSPECIFIED\x10\x00\x12\x16\n\x12TARGET_TYPE_THREAD\x10\x01\x12\x15\n\x11TARGET_TYPE_BATCH\x10\x02\x12\x1b\n\x17TARGET_TYPE_AUTO_PICKUP\x10\x03\x12\x1e\n\x1aTARGET_TYPE_AUTO_AGREEMENT\x10\x04\x12\x19\n\x15TARGET_TYPE_AGREEMENT\x10\x05\x12\x16\n\x12TARGET_TYPE_REVIEW\x10\x06\x12\x1c\n\x18TARGET_TYPE_REVIEW_REPLY\x10\x07\x12*\n&TARGET_TYPE_REMINDER_APPOINTMENT_FIRST\x10\x08\x12+\n\'TARGET_TYPE_REMINDER_APPOINTMENT_SECOND\x10\t\x12+\n\'TARGET_TYPE_REMINDER_APPOINTMENT_REMIND\x10\n\x12%\n!TARGET_TYPE_REMINDER_PET_BIRTHDAY\x10\x0b\x12\x1f\n\x1bTARGET_TYPE_REMINDER_REBOOK\x10\x0c\x12%\n!TARGET_TYPE_AUTO_APPOINTMENT_BOOK\x10\r\x12,\n(TARGET_TYPE_AUTO_APPOINTMENT_RESCHEDULED\x10\x0e\x12*\n&TARGET_TYPE_AUTO_APPOINTMENT_CANCELLED\x10\x0f\x12\x1f\n\x1bTARGET_TYPE_FORGET_PASSWORD\x10\x10\x12\x1e\n\x1aTARGET_TYPE_BUSINESS_DAILY\x10\x11\x12!\n\x1dTARGET_TYPE_VERIFICATION_CODE\x10\x12\x12!\n\x1dTARGET_TYPE_OB_BUSINESS_EMAIL\x10\x13\x12\x1f\n\x1bTARGET_TYPE_OB_CLIENT_EMAIL\x10\x14\x12!\n\x1dTARGET_TYPE_OB_CLIENT_MESSAGE\x10\x15\x12!\n\x1dTARGET_TYPE_CALENDAR_REMINDER\x10\x16\x12\x34\n0TARGET_TYPE_AUTO_APPOINTMENT_CONFIRMED_BY_CLIENT\x10\x17\x12\x34\n0TARGET_TYPE_AUTO_APPOINTMENT_CANCELLED_BY_CLIENT\x10\x18\x12\x1c\n\x18TARGET_TYPE_AUTO_RECEIPT\x10\x19\x12\x1a\n\x16TARGET_TYPE_PAY_ONLINE\x10\x1a\x12\x18\n\x14TARGET_TYPE_COF_LINK\x10\x1b\x12\x33\n/TARGET_TYPE_AUTO_APPOINTMENT_MOVED_TO_WAIT_LIST\x10\x1c\x12\x1f\n\x1bTARGET_TYPE_GROOMING_REPORT\x10\x1d\x12*\n&TARGET_TYPE_ABANDONED_SCHEDULE_MESSAGE\x10\x1e\x12\x1c\n\x18TARGET_TYPE_DAILY_REPORT\x10\x1f*]\n\nSenderType\x12\x1b\n\x17SENDER_TYPE_UNSPECIFIED\x10\x00\x12\x18\n\x14SENDER_TYPE_BUSINESS\x10\x01\x12\x18\n\x14SENDER_TYPE_CUSTOMER\x10\x02*\x99\x02\n\x0f\x41utoMessageType\x12!\n\x1d\x41UTO_MESSAGE_TYPE_UNSPECIFIED\x10\x00\x12\x0f\n\x0b\x41PPT_BOOKED\x10\x01\x12\x14\n\x10\x41PPT_RESCHEDULED\x10\x02\x12\x12\n\x0e\x41PPT_CANCELLED\x10\x03\x12\x15\n\x11READY_FOR_PICK_UP\x10\x04\x12\x0c\n\x08SEND_ETA\x10\x05\x12\x1c\n\x18\x41PPT_CONFIRMED_BY_CLIENT\x10\x06\x12\x1c\n\x18\x41PPT_CANCELLED_BY_CLIENT\x10\x07\x12\x1b\n\x17SEND_FULLY_PAID_RECEIPT\x10\x08\x12\x0e\n\nPAY_ONLINE\x10\t\x12\x1a\n\x16\x41PPT_MOVED_TO_WAITLIST\x10\n*\x97\x01\n\x15ScheduleMessageStatus\x12,\n(SCHEDULE_MESSAGE_SENT_STATUS_UNSPECIFIED\x10\x00\x12\r\n\tSCHEDULED\x10\x01\x12\x15\n\x11SENT_SUCCESSFULLY\x10\x02\x12\x0f\n\x0bSENT_FAILED\x10\x03\x12\x0b\n\x07\x44\x45LETED\x10\x04\x12\x0c\n\x08PREPARED\x10\x05*i\n\x06Method\x12\x16\n\x12METHOD_UNSPECIFIED\x10\x00\x12\x0e\n\nMETHOD_SMS\x10\x01\x12\x10\n\x0cMETHOD_EMAIL\x10\x02\x12\x0f\n\x0bMETHOD_CALL\x10\x04\x12\x0e\n\nMETHOD_APP\x10\x05\"\x04\x08\x03\x10\x03\x42{\n\x1f\x63om.moego.idl.models.message.v1P\x01ZVgithub.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/message/v1;messagepbb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'moego.models.message.v1.message_enums_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n\037com.moego.idl.models.message.v1P\001ZVgithub.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/message/v1;messagepb'
  _globals['_MESSAGETYPELIST'].fields_by_name['message_types']._loaded_options = None
  _globals['_MESSAGETYPELIST'].fields_by_name['message_types']._serialized_options = b'\372B\016\222\001\013\030\001\"\007\202\001\004\020\001 \000'
  _globals['_MESSAGETYPE']._serialized_start=211
  _globals['_MESSAGETYPE']._serialized_end=345
  _globals['_TARGETTYPE']._serialized_start=348
  _globals['_TARGETTYPE']._serialized_end=1503
  _globals['_SENDERTYPE']._serialized_start=1505
  _globals['_SENDERTYPE']._serialized_end=1598
  _globals['_AUTOMESSAGETYPE']._serialized_start=1601
  _globals['_AUTOMESSAGETYPE']._serialized_end=1882
  _globals['_SCHEDULEMESSAGESTATUS']._serialized_start=1885
  _globals['_SCHEDULEMESSAGESTATUS']._serialized_end=2036
  _globals['_METHOD']._serialized_start=2038
  _globals['_METHOD']._serialized_end=2143
  _globals['_MESSAGETYPELIST']._serialized_start=97
  _globals['_MESSAGETYPELIST']._serialized_end=208
# @@protoc_insertion_point(module_scope)
