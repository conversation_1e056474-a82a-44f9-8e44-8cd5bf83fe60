# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: moego/models/message/v1/business_twilio_model.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'moego/models/message/v1/business_twilio_model.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from moego.models.message.v1 import twilio_enums_pb2 as moego_dot_models_dot_message_dot_v1_dot_twilio__enums__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n3moego/models/message/v1/business_twilio_model.proto\x12\x17moego.models.message.v1\x1a*moego/models/message/v1/twilio_enums.proto\"\xb1\x04\n\x13\x42usinessTwilioModel\x12\x1f\n\x0b\x62usiness_id\x18\x01 \x01(\x03R\nbusinessId\x12\x1d\n\ntwilio_sid\x18\x02 \x01(\tR\ttwilioSid\x12!\n\x0ctwilio_token\x18\x03 \x01(\tR\x0btwilioToken\x12#\n\rtwilio_number\x18\x04 \x01(\tR\x0ctwilioNumber\x12#\n\rfriendly_name\x18\x05 \x01(\tR\x0c\x66riendlyName\x12(\n\x10\x63\x61ll_handle_type\x18\x06 \x01(\x08R\x0e\x63\x61llHandleType\x12(\n\x10\x63\x61n_phone_number\x18\x07 \x01(\x08R\x0e\x63\x61nPhoneNumber\x12#\n\rreply_message\x18\x08 \x01(\tR\x0creplyMessage\x12V\n\rassign_statue\x18\t \x01(\x0e\x32\x31.moego.models.message.v1.TwilioNumberAssignStatusR\x0c\x61ssignStatue\x12M\n\nuse_status\x18\n \x01(\x0e\x32..moego.models.message.v1.TwilioNumberUseStatusR\tuseStatus\x12\x14\n\x05share\x18\x0b \x01(\x08R\x05share\x12\x1f\n\x0b\x63reate_time\x18\x0c \x01(\x03R\ncreateTime\x12\x16\n\x06remark\x18\r \x01(\tR\x06remark\"`\n\x18\x42usinessTwilioNumberView\x12\x1f\n\x0b\x62usiness_id\x18\x01 \x01(\x03R\nbusinessId\x12#\n\rtwilio_number\x18\x02 \x01(\tR\x0ctwilioNumberB{\n\x1f\x63om.moego.idl.models.message.v1P\x01ZVgithub.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/message/v1;messagepbb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'moego.models.message.v1.business_twilio_model_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n\037com.moego.idl.models.message.v1P\001ZVgithub.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/message/v1;messagepb'
  _globals['_BUSINESSTWILIOMODEL']._serialized_start=125
  _globals['_BUSINESSTWILIOMODEL']._serialized_end=686
  _globals['_BUSINESSTWILIONUMBERVIEW']._serialized_start=688
  _globals['_BUSINESSTWILIONUMBERVIEW']._serialized_end=784
# @@protoc_insertion_point(module_scope)
