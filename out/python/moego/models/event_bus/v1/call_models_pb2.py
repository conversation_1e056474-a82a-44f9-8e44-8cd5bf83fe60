# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: moego/models/event_bus/v1/call_models.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'moego/models/event_bus/v1/call_models.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2
from moego.models.engagement.v1 import calling_log_models_pb2 as moego_dot_models_dot_engagement_dot_v1_dot_calling__log__models__pb2
from moego.models.engagement.v1 import voice_models_pb2 as moego_dot_models_dot_engagement_dot_v1_dot_voice__models__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n+moego/models/event_bus/v1/call_models.proto\x12\x19moego.models.event_bus.v1\x1a\x1fgoogle/protobuf/timestamp.proto\x1a\x33moego/models/engagement/v1/calling_log_models.proto\x1a-moego/models/engagement/v1/voice_models.proto\"\xb4\x03\n\x16\x43\x61llUpdatedStatusEvent\x12\x1e\n\x0b\x63\x61ll_log_id\x18\x01 \x01(\x03R\tcallLogId\x12\x1f\n\x0b\x63ustomer_id\x18\x02 \x01(\x03R\ncustomerId\x12\x19\n\x08staff_id\x18\x03 \x01(\x03R\x07staffId\x12&\n\x0ftwilio_call_sid\x18\x04 \x01(\tR\rtwilioCallSid\x12\x32\n\x15twilio_conference_sid\x18\x05 \x01(\tR\x13twilioConferenceSid\x12!\n\x0cphone_number\x18\x06 \x01(\tR\x0bphoneNumber\x12J\n\tdirection\x18\x32 \x01(\x0e\x32,.moego.models.engagement.v1.CallingDirectionR\tdirection\x12:\n\x06status\x18\x33 \x01(\x0e\x32\".moego.models.engagement.v1.StatusR\x06status\x12\x37\n\tinit_time\x18\x34 \x01(\x0b\x32\x1a.google.protobuf.TimestampR\x08initTimeB\x80\x01\n!com.moego.idl.models.event_bus.v1P\x01ZYgithub.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/event_bus/v1;eventbuspbb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'moego.models.event_bus.v1.call_models_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n!com.moego.idl.models.event_bus.v1P\001ZYgithub.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/event_bus/v1;eventbuspb'
  _globals['_CALLUPDATEDSTATUSEVENT']._serialized_start=208
  _globals['_CALLUPDATEDSTATUSEVENT']._serialized_end=644
# @@protoc_insertion_point(module_scope)
