from google.protobuf.internal import enum_type_wrapper as _enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from typing import ClassVar as _ClassVar

DESCRIPTOR: _descriptor.FileDescriptor

class PetGender(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
    __slots__ = ()
    PET_GENDER_UNSPECIFIED: _ClassVar[PetGender]
    PET_GENDER_MALE: _ClassVar[PetGender]
    PET_GENDER_FEMALE: _ClassVar[PetGender]
    PET_GENDER_UNKNOWN: _ClassVar[PetGender]

class PetType(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
    __slots__ = ()
    PET_TYPE_UNSPECIFIED: _ClassVar[PetType]
    PET_TYPE_DOG: _ClassVar[PetType]
    PET_TYPE_CAT: _ClassVar[PetType]
    PET_TYPE_BIRD: _ClassVar[PetType]
    PET_TYPE_RABBIT: _ClassVar[PetType]
    PET_TYPE_GUINEA_PIG: _ClassVar[PetType]
    PET_TYPE_HORSE: _ClassVar[PetType]
    PET_TYPE_RAT: _ClassVar[PetType]
    PET_TYPE_MOUSE: _ClassVar[PetType]
    PET_TYPE_HAMSTER: _ClassVar[PetType]
    PET_TYPE_CHINCHILLA: _ClassVar[PetType]
    PET_TYPE_OTHER: _ClassVar[PetType]

class EvaluationStatus(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
    __slots__ = ()
    EVALUATION_STATUS_UNSPECIFIED: _ClassVar[EvaluationStatus]
    PASS: _ClassVar[EvaluationStatus]
    FAIL: _ClassVar[EvaluationStatus]
PET_GENDER_UNSPECIFIED: PetGender
PET_GENDER_MALE: PetGender
PET_GENDER_FEMALE: PetGender
PET_GENDER_UNKNOWN: PetGender
PET_TYPE_UNSPECIFIED: PetType
PET_TYPE_DOG: PetType
PET_TYPE_CAT: PetType
PET_TYPE_BIRD: PetType
PET_TYPE_RABBIT: PetType
PET_TYPE_GUINEA_PIG: PetType
PET_TYPE_HORSE: PetType
PET_TYPE_RAT: PetType
PET_TYPE_MOUSE: PetType
PET_TYPE_HAMSTER: PetType
PET_TYPE_CHINCHILLA: PetType
PET_TYPE_OTHER: PetType
EVALUATION_STATUS_UNSPECIFIED: EvaluationStatus
PASS: EvaluationStatus
FAIL: EvaluationStatus
