# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: moego/models/customer/v1/customer_preference_models.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'moego/models/customer/v1/customer_preference_models.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2
from moego.models.organization.v1 import company_enums_pb2 as moego_dot_models_dot_organization_dot_v1_dot_company__enums__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n9moego/models/customer/v1/customer_preference_models.proto\x12\x18moego.models.customer.v1\x1a\x1fgoogle/protobuf/timestamp.proto\x1a\x30moego/models/organization/v1/company_enums.proto\"\xe1\x04\n\x17\x43ustomerPreferenceModel\x12\x0e\n\x02id\x18\x01 \x01(\x03R\x02id\x12\x1d\n\naccount_id\x18\x02 \x01(\x03R\taccountId\x12!\n\x0c\x63ountry_code\x18\x03 \x01(\tR\x0b\x63ountryCode\x12#\n\rcurrency_code\x18\x04 \x01(\tR\x0c\x63urrencyCode\x12\'\n\x0f\x63urrency_symbol\x18\x05 \x01(\tR\x0e\x63urrencySymbol\x12I\n\x0b\x64\x61te_format\x18\x06 \x01(\x0e\x32(.moego.models.organization.v1.DateFormatR\ndateFormat\x12I\n\x0btime_format\x18\x07 \x01(\x0e\x32(.moego.models.organization.v1.TimeFormatR\ntimeFormat\x12I\n\x0bweight_unit\x18\x08 \x01(\x0e\x32(.moego.models.organization.v1.WeightUnitR\nweightUnit\x12O\n\rdistance_unit\x18\t \x01(\x0e\x32*.moego.models.organization.v1.DistanceUnitR\x0c\x64istanceUnit\x12\x39\n\ncreated_at\x18\x14 \x01(\x0b\x32\x1a.google.protobuf.TimestampR\tcreatedAt\x12\x39\n\nupdated_at\x18\x15 \x01(\x0b\x32\x1a.google.protobuf.TimestampR\tupdatedAt\"\xbb\x03\n\x16\x43ustomerPreferenceView\x12!\n\x0c\x63ountry_code\x18\x03 \x01(\tR\x0b\x63ountryCode\x12#\n\rcurrency_code\x18\x04 \x01(\tR\x0c\x63urrencyCode\x12\'\n\x0f\x63urrency_symbol\x18\x05 \x01(\tR\x0e\x63urrencySymbol\x12I\n\x0b\x64\x61te_format\x18\x06 \x01(\x0e\x32(.moego.models.organization.v1.DateFormatR\ndateFormat\x12I\n\x0btime_format\x18\x07 \x01(\x0e\x32(.moego.models.organization.v1.TimeFormatR\ntimeFormat\x12I\n\x0bweight_unit\x18\x08 \x01(\x0e\x32(.moego.models.organization.v1.WeightUnitR\nweightUnit\x12O\n\rdistance_unit\x18\t \x01(\x0e\x32*.moego.models.organization.v1.DistanceUnitR\x0c\x64istanceUnitB~\n com.moego.idl.models.customer.v1P\x01ZXgithub.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/customer/v1;customerpbb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'moego.models.customer.v1.customer_preference_models_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n com.moego.idl.models.customer.v1P\001ZXgithub.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/customer/v1;customerpb'
  _globals['_CUSTOMERPREFERENCEMODEL']._serialized_start=171
  _globals['_CUSTOMERPREFERENCEMODEL']._serialized_end=780
  _globals['_CUSTOMERPREFERENCEVIEW']._serialized_start=783
  _globals['_CUSTOMERPREFERENCEVIEW']._serialized_end=1226
# @@protoc_insertion_point(module_scope)
