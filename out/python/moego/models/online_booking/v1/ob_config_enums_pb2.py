# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: moego/models/online_booking/v1/ob_config_enums.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'moego/models/online_booking/v1/ob_config_enums.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n4moego/models/online_booking/v1/ob_config_enums.proto\x12\x1emoego.models.online_booking.v1*\x90\x01\n\x10\x41\x63\x63\x65ptClientType\x12\"\n\x1e\x41\x43\x43\x45PT_CLIENT_TYPE_UNSPECIFIED\x10\x00\x12\x1a\n\x16\x41\x43\x43\x45PT_CLIENT_TYPE_NEW\x10\x01\x12\x1f\n\x1b\x41\x43\x43\x45PT_CLIENT_TYPE_EXISTING\x10\x02\x12\x1b\n\x17\x41\x43\x43\x45PT_CLIENT_TYPE_BOTH\x10\x03*\x85\x01\n\x10\x41vailabilityType\x12&\n\"AVAILABILITY_TYPE_BY_WORKING_HOURS\x10\x00\x12\x1e\n\x1a\x41VAILABILITY_TYPE_BY_SLOTS\x10\x01\x12)\n%AVAILABILITY_TYPE_DISABLE_SELECT_TIME\x10\x02*\x9a\x01\n\x0eTimeSlotFormat\x12 \n\x1cTIME_SLOT_FORMAT_UNSPECIFIED\x10\x00\x12 \n\x1cTIME_SLOT_FORMAT_EXACT_TIMES\x10\x01\x12$\n TIME_SLOT_FORMAT_ARRIVAL_WINDOWS\x10\x02\x12\x1e\n\x1aTIME_SLOT_FORMAT_DATE_ONLY\x10\x03*B\n\nPrepayType\x12\x1b\n\x17PREPAY_TYPE_FULL_AMOUNT\x10\x00\x12\x17\n\x13PREPAY_TYPE_DEPOSIT\x10\x01*]\n\x11PrepayDepositType\x12$\n PREPAY_DEPOSIT_TYPE_FIXED_AMOUNT\x10\x00\x12\"\n\x1ePREPAY_DEPOSIT_TYPE_PERCENTAGE\x10\x01*\x87\x01\n\x13\x42ookingRangeEndType\x12&\n\"BOOKING_RANGE_END_TYPE_UNSPECIFIED\x10\x00\x12#\n\x1f\x42OOKING_RANGE_END_TYPE_RELATIVE\x10\x01\x12#\n\x1f\x42OOKING_RANGE_END_TYPE_ABSOLUTE\x10\x02*z\n\x0bPaymentType\x12\x18\n\x14PAYMENT_TYPE_DISABLE\x10\x00\x12\x1d\n\x19PAYMENT_TYPE_CARD_ON_FILE\x10\x01\x12\x17\n\x13PAYMENT_TYPE_PREPAY\x10\x02\x12\x19\n\x15PAYMENT_TYPE_PRE_AUTH\x10\x03\x42\x8f\x01\n&com.moego.idl.models.online_booking.v1P\x01Zcgithub.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/online_booking/v1;onlinebookingpbb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'moego.models.online_booking.v1.ob_config_enums_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n&com.moego.idl.models.online_booking.v1P\001Zcgithub.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/online_booking/v1;onlinebookingpb'
  _globals['_ACCEPTCLIENTTYPE']._serialized_start=89
  _globals['_ACCEPTCLIENTTYPE']._serialized_end=233
  _globals['_AVAILABILITYTYPE']._serialized_start=236
  _globals['_AVAILABILITYTYPE']._serialized_end=369
  _globals['_TIMESLOTFORMAT']._serialized_start=372
  _globals['_TIMESLOTFORMAT']._serialized_end=526
  _globals['_PREPAYTYPE']._serialized_start=528
  _globals['_PREPAYTYPE']._serialized_end=594
  _globals['_PREPAYDEPOSITTYPE']._serialized_start=596
  _globals['_PREPAYDEPOSITTYPE']._serialized_end=689
  _globals['_BOOKINGRANGEENDTYPE']._serialized_start=692
  _globals['_BOOKINGRANGEENDTYPE']._serialized_end=827
  _globals['_PAYMENTTYPE']._serialized_start=829
  _globals['_PAYMENTTYPE']._serialized_end=951
# @@protoc_insertion_point(module_scope)
