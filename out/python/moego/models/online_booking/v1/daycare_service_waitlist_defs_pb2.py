# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: moego/models/online_booking/v1/daycare_service_waitlist_defs.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'moego/models/online_booking/v1/daycare_service_waitlist_defs.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.type import date_pb2 as google_dot_type_dot_date__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\nBmoego/models/online_booking/v1/daycare_service_waitlist_defs.proto\x12\x1emoego.models.online_booking.v1\x1a\x16google/type/date.proto\"K\n\x0f\x44\x61ycareWaitlist\x12\x38\n\x0especific_dates\x18\x01 \x03(\x0b\x32\x11.google.type.DateR\rspecificDatesB\x8f\x01\n&com.moego.idl.models.online_booking.v1P\x01Zcgithub.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/online_booking/v1;onlinebookingpbb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'moego.models.online_booking.v1.daycare_service_waitlist_defs_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n&com.moego.idl.models.online_booking.v1P\001Zcgithub.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/online_booking/v1;onlinebookingpb'
  _globals['_DAYCAREWAITLIST']._serialized_start=126
  _globals['_DAYCAREWAITLIST']._serialized_end=201
# @@protoc_insertion_point(module_scope)
