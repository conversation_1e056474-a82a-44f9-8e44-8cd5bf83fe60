# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: moego/models/online_booking/v1/automation_enums.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'moego/models/online_booking/v1/automation_enums.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n5moego/models/online_booking/v1/automation_enums.proto\x12\x1emoego.models.online_booking.v1*\x91\x01\n\x16ProfileUpdateCondition\x12(\n$PROFILE_UPDATE_CONDITION_UNSPECIFIED\x10\x00\x12 \n\x1cPROFILE_UPDATE_CONDITION_ALL\x10\x01\x12+\n\'PROFILE_UPDATE_CONDITION_WITHOUT_UPDATE\x10\x02*\x98\x01\n\x16VaccineStatusCondition\x12(\n$VACCINE_STATUS_CONDITION_UNSPECIFIED\x10\x00\x12 \n\x1cVACCINE_STATUS_CONDITION_ALL\x10\x01\x12\x32\n.VACCINE_STATUS_CONDITION_NO_MISSING_OR_EXPIRED\x10\x02\x42\x8f\x01\n&com.moego.idl.models.online_booking.v1P\x01Zcgithub.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/online_booking/v1;onlinebookingpbb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'moego.models.online_booking.v1.automation_enums_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n&com.moego.idl.models.online_booking.v1P\001Zcgithub.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/online_booking/v1;onlinebookingpb'
  _globals['_PROFILEUPDATECONDITION']._serialized_start=90
  _globals['_PROFILEUPDATECONDITION']._serialized_end=235
  _globals['_VACCINESTATUSCONDITION']._serialized_start=238
  _globals['_VACCINESTATUSCONDITION']._serialized_end=390
# @@protoc_insertion_point(module_scope)
