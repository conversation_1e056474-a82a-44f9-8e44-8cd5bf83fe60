from google.protobuf.internal import containers as _containers
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from collections.abc import Iterable as _Iterable, Mapping as _Mapping
from typing import ClassVar as _ClassVar, Optional as _Optional, Union as _Union

DESCRIPTOR: _descriptor.FileDescriptor

class WeekTimeRangeModel(_message.Message):
    __slots__ = ("time_range", "is_selected")
    TIME_RANGE_FIELD_NUMBER: _ClassVar[int]
    IS_SELECTED_FIELD_NUMBER: _ClassVar[int]
    time_range: _containers.RepeatedCompositeFieldContainer[TimeRangeModel]
    is_selected: bool
    def __init__(self, time_range: _Optional[_Iterable[_Union[TimeRangeModel, _Mapping]]] = ..., is_selected: bool = ...) -> None: ...

class TimeRangeModel(_message.Message):
    __slots__ = ("start_time", "end_time")
    START_TIME_FIELD_NUMBER: _ClassVar[int]
    END_TIME_FIELD_NUMBER: _ClassVar[int]
    start_time: int
    end_time: int
    def __init__(self, start_time: _Optional[int] = ..., end_time: _Optional[int] = ...) -> None: ...
