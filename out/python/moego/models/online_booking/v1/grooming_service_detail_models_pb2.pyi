from google.protobuf import timestamp_pb2 as _timestamp_pb2
from moego.models.offering.v1 import service_enum_pb2 as _service_enum_pb2
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from collections.abc import Mapping as _Mapping
from typing import ClassVar as _ClassVar, Optional as _Optional, Union as _Union

DESCRIPTOR: _descriptor.FileDescriptor

class GroomingServiceDetailModel(_message.Message):
    __slots__ = ("id", "booking_request_id", "pet_id", "staff_id", "service_id", "service_time", "service_price", "scope_type_price", "scope_type_time", "start_date", "start_time", "end_date", "end_time", "created_at", "updated_at", "price_override_type", "duration_override_type")
    ID_FIELD_NUMBER: _ClassVar[int]
    BOOKING_REQUEST_ID_FIELD_NUMBER: _ClassVar[int]
    PET_ID_FIELD_NUMBER: _ClassVar[int]
    STAFF_ID_FIELD_NUMBER: _ClassVar[int]
    SERVICE_ID_FIELD_NUMBER: _ClassVar[int]
    SERVICE_TIME_FIELD_NUMBER: _ClassVar[int]
    SERVICE_PRICE_FIELD_NUMBER: _ClassVar[int]
    SCOPE_TYPE_PRICE_FIELD_NUMBER: _ClassVar[int]
    SCOPE_TYPE_TIME_FIELD_NUMBER: _ClassVar[int]
    START_DATE_FIELD_NUMBER: _ClassVar[int]
    START_TIME_FIELD_NUMBER: _ClassVar[int]
    END_DATE_FIELD_NUMBER: _ClassVar[int]
    END_TIME_FIELD_NUMBER: _ClassVar[int]
    CREATED_AT_FIELD_NUMBER: _ClassVar[int]
    UPDATED_AT_FIELD_NUMBER: _ClassVar[int]
    PRICE_OVERRIDE_TYPE_FIELD_NUMBER: _ClassVar[int]
    DURATION_OVERRIDE_TYPE_FIELD_NUMBER: _ClassVar[int]
    id: int
    booking_request_id: int
    pet_id: int
    staff_id: int
    service_id: int
    service_time: int
    service_price: float
    scope_type_price: _service_enum_pb2.ServiceScopeType
    scope_type_time: _service_enum_pb2.ServiceScopeType
    start_date: str
    start_time: int
    end_date: str
    end_time: int
    created_at: _timestamp_pb2.Timestamp
    updated_at: _timestamp_pb2.Timestamp
    price_override_type: _service_enum_pb2.ServiceOverrideType
    duration_override_type: _service_enum_pb2.ServiceOverrideType
    def __init__(self, id: _Optional[int] = ..., booking_request_id: _Optional[int] = ..., pet_id: _Optional[int] = ..., staff_id: _Optional[int] = ..., service_id: _Optional[int] = ..., service_time: _Optional[int] = ..., service_price: _Optional[float] = ..., scope_type_price: _Optional[_Union[_service_enum_pb2.ServiceScopeType, str]] = ..., scope_type_time: _Optional[_Union[_service_enum_pb2.ServiceScopeType, str]] = ..., start_date: _Optional[str] = ..., start_time: _Optional[int] = ..., end_date: _Optional[str] = ..., end_time: _Optional[int] = ..., created_at: _Optional[_Union[datetime.datetime, _timestamp_pb2.Timestamp, _Mapping]] = ..., updated_at: _Optional[_Union[datetime.datetime, _timestamp_pb2.Timestamp, _Mapping]] = ..., price_override_type: _Optional[_Union[_service_enum_pb2.ServiceOverrideType, str]] = ..., duration_override_type: _Optional[_Union[_service_enum_pb2.ServiceOverrideType, str]] = ...) -> None: ...
