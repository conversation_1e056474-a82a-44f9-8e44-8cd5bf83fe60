from validate import validate_pb2 as _validate_pb2
from google.protobuf.internal import containers as _containers
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from collections.abc import Iterable as _Iterable, Mapping as _Mapping
from typing import ClassVar as _ClassVar, Optional as _Optional, Union as _Union

DESCRIPTOR: _descriptor.FileDescriptor

class WorkingHoursDef(_message.Message):
    __slots__ = ("monday", "tuesday", "wednesday", "thursday", "friday", "saturday", "sunday")
    MONDAY_FIELD_NUMBER: _ClassVar[int]
    TUESDAY_FIELD_NUMBER: _ClassVar[int]
    WEDNESDAY_FIELD_NUMBER: _ClassVar[int]
    THURSDAY_FIELD_NUMBER: _ClassVar[int]
    FRIDAY_FIELD_NUMBER: _ClassVar[int]
    SATURDAY_FIELD_NUMBER: _ClassVar[int]
    SUNDAY_FIELD_NUMBER: _ClassVar[int]
    monday: _containers.RepeatedCompositeFieldContainer[TimeRangeDef]
    tuesday: _containers.RepeatedCompositeFieldContainer[TimeRangeDef]
    wednesday: _containers.RepeatedCompositeFieldContainer[TimeRangeDef]
    thursday: _containers.RepeatedCompositeFieldContainer[TimeRangeDef]
    friday: _containers.RepeatedCompositeFieldContainer[TimeRangeDef]
    saturday: _containers.RepeatedCompositeFieldContainer[TimeRangeDef]
    sunday: _containers.RepeatedCompositeFieldContainer[TimeRangeDef]
    def __init__(self, monday: _Optional[_Iterable[_Union[TimeRangeDef, _Mapping]]] = ..., tuesday: _Optional[_Iterable[_Union[TimeRangeDef, _Mapping]]] = ..., wednesday: _Optional[_Iterable[_Union[TimeRangeDef, _Mapping]]] = ..., thursday: _Optional[_Iterable[_Union[TimeRangeDef, _Mapping]]] = ..., friday: _Optional[_Iterable[_Union[TimeRangeDef, _Mapping]]] = ..., saturday: _Optional[_Iterable[_Union[TimeRangeDef, _Mapping]]] = ..., sunday: _Optional[_Iterable[_Union[TimeRangeDef, _Mapping]]] = ...) -> None: ...

class TimeRangeDef(_message.Message):
    __slots__ = ("start_time", "end_time")
    START_TIME_FIELD_NUMBER: _ClassVar[int]
    END_TIME_FIELD_NUMBER: _ClassVar[int]
    start_time: int
    end_time: int
    def __init__(self, start_time: _Optional[int] = ..., end_time: _Optional[int] = ...) -> None: ...
