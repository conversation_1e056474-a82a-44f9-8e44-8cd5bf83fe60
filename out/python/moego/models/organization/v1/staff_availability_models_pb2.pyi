from google.type import dayofweek_pb2 as _dayofweek_pb2
from google.protobuf.internal import containers as _containers
from google.protobuf.internal import enum_type_wrapper as _enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from collections.abc import Iterable as _Iterable, Mapping as _Mapping
from typing import ClassVar as _ClassVar, Optional as _Optional, Union as _Union

DESCRIPTOR: _descriptor.FileDescriptor

class ScheduleType(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
    __slots__ = ()
    SCHEDULE_TYPE_UNSPECIFIED: _ClassVar[ScheduleType]
    ONE_WEEK: _ClassVar[ScheduleType]
    TWO_WEEK: _ClassVar[ScheduleType]
    THREE_WEEK: _ClassVar[ScheduleType]
    FOUR_WEEK: _ClassVar[ScheduleType]

class LimitType(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
    __slots__ = ()
    LIMIT_TYPE_UNSPECIFIED: _ClassVar[LimitType]
    SERVICE_LIMIT: _ClassVar[LimitType]
    PET_SIZE_LIMIT: _ClassVar[LimitType]
    PET_BREED_LIMIT: _ClassVar[LimitType]
SCHEDULE_TYPE_UNSPECIFIED: ScheduleType
ONE_WEEK: ScheduleType
TWO_WEEK: ScheduleType
THREE_WEEK: ScheduleType
FOUR_WEEK: ScheduleType
LIMIT_TYPE_UNSPECIFIED: LimitType
SERVICE_LIMIT: LimitType
PET_SIZE_LIMIT: LimitType
PET_BREED_LIMIT: LimitType

class StaffAvailability(_message.Message):
    __slots__ = ("staff_id", "is_available", "schedule_type", "slot_availability_day_list", "time_availability_day_list")
    STAFF_ID_FIELD_NUMBER: _ClassVar[int]
    IS_AVAILABLE_FIELD_NUMBER: _ClassVar[int]
    SCHEDULE_TYPE_FIELD_NUMBER: _ClassVar[int]
    SLOT_AVAILABILITY_DAY_LIST_FIELD_NUMBER: _ClassVar[int]
    TIME_AVAILABILITY_DAY_LIST_FIELD_NUMBER: _ClassVar[int]
    staff_id: int
    is_available: bool
    schedule_type: ScheduleType
    slot_availability_day_list: _containers.RepeatedCompositeFieldContainer[SlotAvailabilityDay]
    time_availability_day_list: _containers.RepeatedCompositeFieldContainer[TimeAvailabilityDay]
    def __init__(self, staff_id: _Optional[int] = ..., is_available: bool = ..., schedule_type: _Optional[_Union[ScheduleType, str]] = ..., slot_availability_day_list: _Optional[_Iterable[_Union[SlotAvailabilityDay, _Mapping]]] = ..., time_availability_day_list: _Optional[_Iterable[_Union[TimeAvailabilityDay, _Mapping]]] = ...) -> None: ...

class SlotAvailabilityDay(_message.Message):
    __slots__ = ("day_of_week", "is_available", "schedule_type", "slot_daily_setting", "slot_hour_setting_list")
    DAY_OF_WEEK_FIELD_NUMBER: _ClassVar[int]
    IS_AVAILABLE_FIELD_NUMBER: _ClassVar[int]
    SCHEDULE_TYPE_FIELD_NUMBER: _ClassVar[int]
    SLOT_DAILY_SETTING_FIELD_NUMBER: _ClassVar[int]
    SLOT_HOUR_SETTING_LIST_FIELD_NUMBER: _ClassVar[int]
    day_of_week: _dayofweek_pb2.DayOfWeek
    is_available: bool
    schedule_type: ScheduleType
    slot_daily_setting: SlotDailySetting
    slot_hour_setting_list: _containers.RepeatedCompositeFieldContainer[SlotHourSetting]
    def __init__(self, day_of_week: _Optional[_Union[_dayofweek_pb2.DayOfWeek, str]] = ..., is_available: bool = ..., schedule_type: _Optional[_Union[ScheduleType, str]] = ..., slot_daily_setting: _Optional[_Union[SlotDailySetting, _Mapping]] = ..., slot_hour_setting_list: _Optional[_Iterable[_Union[SlotHourSetting, _Mapping]]] = ...) -> None: ...

class TimeAvailabilityDay(_message.Message):
    __slots__ = ("day_of_week", "is_available", "schedule_type", "time_daily_setting", "time_hour_setting_list")
    DAY_OF_WEEK_FIELD_NUMBER: _ClassVar[int]
    IS_AVAILABLE_FIELD_NUMBER: _ClassVar[int]
    SCHEDULE_TYPE_FIELD_NUMBER: _ClassVar[int]
    TIME_DAILY_SETTING_FIELD_NUMBER: _ClassVar[int]
    TIME_HOUR_SETTING_LIST_FIELD_NUMBER: _ClassVar[int]
    day_of_week: _dayofweek_pb2.DayOfWeek
    is_available: bool
    schedule_type: ScheduleType
    time_daily_setting: TimeDailySetting
    time_hour_setting_list: _containers.RepeatedCompositeFieldContainer[TimeHourSetting]
    def __init__(self, day_of_week: _Optional[_Union[_dayofweek_pb2.DayOfWeek, str]] = ..., is_available: bool = ..., schedule_type: _Optional[_Union[ScheduleType, str]] = ..., time_daily_setting: _Optional[_Union[TimeDailySetting, _Mapping]] = ..., time_hour_setting_list: _Optional[_Iterable[_Union[TimeHourSetting, _Mapping]]] = ...) -> None: ...

class SlotDailySetting(_message.Message):
    __slots__ = ("start_time", "end_time", "capacity", "limit")
    START_TIME_FIELD_NUMBER: _ClassVar[int]
    END_TIME_FIELD_NUMBER: _ClassVar[int]
    CAPACITY_FIELD_NUMBER: _ClassVar[int]
    LIMIT_FIELD_NUMBER: _ClassVar[int]
    start_time: int
    end_time: int
    capacity: int
    limit: BookingLimitation
    def __init__(self, start_time: _Optional[int] = ..., end_time: _Optional[int] = ..., capacity: _Optional[int] = ..., limit: _Optional[_Union[BookingLimitation, _Mapping]] = ...) -> None: ...

class TimeDailySetting(_message.Message):
    __slots__ = ("limit",)
    LIMIT_FIELD_NUMBER: _ClassVar[int]
    limit: BookingLimitation
    def __init__(self, limit: _Optional[_Union[BookingLimitation, _Mapping]] = ...) -> None: ...

class SlotHourSetting(_message.Message):
    __slots__ = ("start_time", "capacity", "limit")
    START_TIME_FIELD_NUMBER: _ClassVar[int]
    CAPACITY_FIELD_NUMBER: _ClassVar[int]
    LIMIT_FIELD_NUMBER: _ClassVar[int]
    start_time: int
    capacity: int
    limit: BookingLimitation
    def __init__(self, start_time: _Optional[int] = ..., capacity: _Optional[int] = ..., limit: _Optional[_Union[BookingLimitation, _Mapping]] = ...) -> None: ...

class TimeHourSetting(_message.Message):
    __slots__ = ("start_time", "end_time")
    START_TIME_FIELD_NUMBER: _ClassVar[int]
    END_TIME_FIELD_NUMBER: _ClassVar[int]
    start_time: int
    end_time: int
    def __init__(self, start_time: _Optional[int] = ..., end_time: _Optional[int] = ...) -> None: ...

class BookingLimitation(_message.Message):
    __slots__ = ("service_limits", "pet_size_limits", "pet_breed_limits")
    class ServiceLimitation(_message.Message):
        __slots__ = ("service_ids", "is_all_service", "capacity")
        SERVICE_IDS_FIELD_NUMBER: _ClassVar[int]
        IS_ALL_SERVICE_FIELD_NUMBER: _ClassVar[int]
        CAPACITY_FIELD_NUMBER: _ClassVar[int]
        service_ids: _containers.RepeatedScalarFieldContainer[int]
        is_all_service: bool
        capacity: int
        def __init__(self, service_ids: _Optional[_Iterable[int]] = ..., is_all_service: bool = ..., capacity: _Optional[int] = ...) -> None: ...
    class PetSizeLimitation(_message.Message):
        __slots__ = ("pet_size_ids", "is_all_size", "capacity")
        PET_SIZE_IDS_FIELD_NUMBER: _ClassVar[int]
        IS_ALL_SIZE_FIELD_NUMBER: _ClassVar[int]
        CAPACITY_FIELD_NUMBER: _ClassVar[int]
        pet_size_ids: _containers.RepeatedScalarFieldContainer[int]
        is_all_size: bool
        capacity: int
        def __init__(self, pet_size_ids: _Optional[_Iterable[int]] = ..., is_all_size: bool = ..., capacity: _Optional[int] = ...) -> None: ...
    class PetBreedLimitation(_message.Message):
        __slots__ = ("pet_type_id", "is_all_breed", "breed_ids", "capacity")
        PET_TYPE_ID_FIELD_NUMBER: _ClassVar[int]
        IS_ALL_BREED_FIELD_NUMBER: _ClassVar[int]
        BREED_IDS_FIELD_NUMBER: _ClassVar[int]
        CAPACITY_FIELD_NUMBER: _ClassVar[int]
        pet_type_id: int
        is_all_breed: bool
        breed_ids: _containers.RepeatedScalarFieldContainer[int]
        capacity: int
        def __init__(self, pet_type_id: _Optional[int] = ..., is_all_breed: bool = ..., breed_ids: _Optional[_Iterable[int]] = ..., capacity: _Optional[int] = ...) -> None: ...
    SERVICE_LIMITS_FIELD_NUMBER: _ClassVar[int]
    PET_SIZE_LIMITS_FIELD_NUMBER: _ClassVar[int]
    PET_BREED_LIMITS_FIELD_NUMBER: _ClassVar[int]
    service_limits: _containers.RepeatedCompositeFieldContainer[BookingLimitation.ServiceLimitation]
    pet_size_limits: _containers.RepeatedCompositeFieldContainer[BookingLimitation.PetSizeLimitation]
    pet_breed_limits: _containers.RepeatedCompositeFieldContainer[BookingLimitation.PetBreedLimitation]
    def __init__(self, service_limits: _Optional[_Iterable[_Union[BookingLimitation.ServiceLimitation, _Mapping]]] = ..., pet_size_limits: _Optional[_Iterable[_Union[BookingLimitation.PetSizeLimitation, _Mapping]]] = ..., pet_breed_limits: _Optional[_Iterable[_Union[BookingLimitation.PetBreedLimitation, _Mapping]]] = ...) -> None: ...
