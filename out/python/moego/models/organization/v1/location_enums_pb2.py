# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: moego/models/organization/v1/location_enums.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'moego/models/organization/v1/location_enums.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n1moego/models/organization/v1/location_enums.proto\x12\x1cmoego.models.organization.v1*1\n\x0c\x42usinessType\x12\n\n\x06MOBILE\x10\x00\x12\t\n\x05SALON\x10\x01\x12\n\n\x06HYBRID\x10\x02*\xa2\x01\n\x16\x42usinessSourceFromType\x12$\n BUSINESS_SOURCE_FROM_UNSPECIFIED\x10\x00\x12\x0f\n\x0b\x41PP_ANDROID\x10\x01\x12\x0b\n\x07\x41PP_IOS\x10\x02\x12\x0f\n\x0bWEB_ANDROID\x10\x03\x12\x0b\n\x07WEB_IOS\x10\x04\x12\x0f\n\x0bWEB_DESKTOP\x10\x05\x12\x15\n\x11\x45NTERPRISE_TENANT\x10\x06*{\n\nSourceType\x12\x1b\n\x17SOURCE_TYPE_UNSPECIFIED\x10\x00\x12\t\n\x05OTHER\x10\x01\x12\x0c\n\x08\x46\x41\x43\x45\x42OOK\x10\x02\x12\x13\n\x0fINTERNET_SEARCH\x10\x03\x12\x0c\n\x08\x43\x41PTERRA\x10\x04\x12\n\n\x06SQUARE\x10\x05\x12\x08\n\x04\x45XPO\x10\x06\x42\x8a\x01\n$com.moego.idl.models.organization.v1P\x01Z`github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/organization/v1;organizationpbb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'moego.models.organization.v1.location_enums_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n$com.moego.idl.models.organization.v1P\001Z`github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/organization/v1;organizationpb'
  _globals['_BUSINESSTYPE']._serialized_start=83
  _globals['_BUSINESSTYPE']._serialized_end=132
  _globals['_BUSINESSSOURCEFROMTYPE']._serialized_start=135
  _globals['_BUSINESSSOURCEFROMTYPE']._serialized_end=297
  _globals['_SOURCETYPE']._serialized_start=299
  _globals['_SOURCETYPE']._serialized_end=422
# @@protoc_insertion_point(module_scope)
