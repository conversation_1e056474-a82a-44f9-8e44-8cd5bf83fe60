# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: moego/models/organization/v1/working_hour_defs.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'moego/models/organization/v1/working_hour_defs.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from validate import validate_pb2 as validate_dot_validate__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n4moego/models/organization/v1/working_hour_defs.proto\x12\x1cmoego.models.organization.v1\x1a\x17validate/validate.proto\"\xfd\x03\n\x0fWorkingHoursDef\x12\x42\n\x06monday\x18\x01 \x03(\x0b\x32*.moego.models.organization.v1.TimeRangeDefR\x06monday\x12\x44\n\x07tuesday\x18\x02 \x03(\x0b\x32*.moego.models.organization.v1.TimeRangeDefR\x07tuesday\x12H\n\twednesday\x18\x03 \x03(\x0b\x32*.moego.models.organization.v1.TimeRangeDefR\twednesday\x12\x46\n\x08thursday\x18\x04 \x03(\x0b\x32*.moego.models.organization.v1.TimeRangeDefR\x08thursday\x12\x42\n\x06\x66riday\x18\x05 \x03(\x0b\x32*.moego.models.organization.v1.TimeRangeDefR\x06\x66riday\x12\x46\n\x08saturday\x18\x06 \x03(\x0b\x32*.moego.models.organization.v1.TimeRangeDefR\x08saturday\x12\x42\n\x06sunday\x18\x07 \x03(\x0b\x32*.moego.models.organization.v1.TimeRangeDefR\x06sunday\"`\n\x0cTimeRangeDef\x12)\n\nstart_time\x18\x01 \x01(\x05\x42\n\xfa\x42\x07\x1a\x05\x10\xa0\x0b(\x00R\tstartTime\x12%\n\x08\x65nd_time\x18\x02 \x01(\x05\x42\n\xfa\x42\x07\x1a\x05\x10\xa0\x0b(\x00R\x07\x65ndTimeB\x8a\x01\n$com.moego.idl.models.organization.v1P\x01Z`github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/organization/v1;organizationpbb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'moego.models.organization.v1.working_hour_defs_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n$com.moego.idl.models.organization.v1P\001Z`github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/organization/v1;organizationpb'
  _globals['_TIMERANGEDEF'].fields_by_name['start_time']._loaded_options = None
  _globals['_TIMERANGEDEF'].fields_by_name['start_time']._serialized_options = b'\372B\007\032\005\020\240\013(\000'
  _globals['_TIMERANGEDEF'].fields_by_name['end_time']._loaded_options = None
  _globals['_TIMERANGEDEF'].fields_by_name['end_time']._serialized_options = b'\372B\007\032\005\020\240\013(\000'
  _globals['_WORKINGHOURSDEF']._serialized_start=112
  _globals['_WORKINGHOURSDEF']._serialized_end=621
  _globals['_TIMERANGEDEF']._serialized_start=623
  _globals['_TIMERANGEDEF']._serialized_end=719
# @@protoc_insertion_point(module_scope)
