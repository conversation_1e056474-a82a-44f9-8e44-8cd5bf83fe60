# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: moego/models/marketing/v1/discount_code_models.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'moego/models/marketing/v1/discount_code_models.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2
from moego.models.marketing.v1 import discount_code_enums_pb2 as moego_dot_models_dot_marketing_dot_v1_dot_discount__code__enums__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n4moego/models/marketing/v1/discount_code_models.proto\x12\x19moego.models.marketing.v1\x1a\x1fgoogle/protobuf/timestamp.proto\x1a\x33moego/models/marketing/v1/discount_code_enums.proto\"\x82\x0b\n\x11\x44iscountCodeModel\x12\x0e\n\x02id\x18\x01 \x01(\x03R\x02id\x12\x1f\n\x0b\x62usiness_id\x18\x02 \x01(\x03R\nbusinessId\x12#\n\rdiscount_code\x18\x03 \x01(\tR\x0c\x64iscountCode\x12 \n\x0b\x64\x65scription\x18\x04 \x01(\tR\x0b\x64\x65scription\x12\x16\n\x06\x61mount\x18\x05 \x01(\x01R\x06\x61mount\x12?\n\x04type\x18\x06 \x01(\x0e\x32+.moego.models.marketing.v1.DiscountCodeTypeR\x04type\x12\x1d\n\nstart_date\x18\x07 \x01(\tR\tstartDate\x12\x19\n\x08\x65nd_date\x18\x08 \x01(\tR\x07\x65ndDate\x12*\n\x11\x61llowed_all_thing\x18\t \x01(\x08R\x0f\x61llowedAllThing\x12\x30\n\x14\x61llowed_all_services\x18\n \x01(\x08R\x12\x61llowedAllServices\x12\x1f\n\x0bservice_ids\x18\x0b \x03(\x03R\nserviceIds\x12\x1c\n\nadd_on_ids\x18\x0c \x03(\x03R\x08\x61\x64\x64OnIds\x12\x30\n\x14\x61llowed_all_products\x18\r \x01(\x08R\x12\x61llowedAllProducts\x12\x1f\n\x0bproduct_ids\x18\x0e \x03(\x03R\nproductIds\x12.\n\x13\x61llowed_all_clients\x18\x0f \x01(\x08R\x11\x61llowedAllClients\x12.\n\x13\x61llowed_new_clients\x18\x10 \x01(\x08R\x11\x61llowedNewClients\x12#\n\rclients_group\x18\x11 \x01(\tR\x0c\x63lientsGroup\x12\x1d\n\nclient_ids\x18\x12 \x03(\x03R\tclientIds\x12\x1f\n\x0blimit_usage\x18\x13 \x01(\x05R\nlimitUsage\x12\x35\n\x17limit_number_per_client\x18\x14 \x01(\x05R\x14limitNumberPerClient\x12!\n\x0climit_budget\x18\x15 \x01(\x05R\x0blimitBudget\x12\x34\n\x16\x61uto_apply_association\x18\x16 \x01(\x08R\x14\x61utoApplyAssociation\x12\x32\n\x15\x65nable_online_booking\x18\x17 \x01(\x08R\x13\x65nableOnlineBooking\x12%\n\x0e\x64iscount_sales\x18\x18 \x01(\x01R\rdiscountSales\x12\x1f\n\x0btotal_usage\x18\x19 \x01(\x05R\ntotalUsage\x12\x45\n\x06status\x18\x1a \x01(\x0e\x32-.moego.models.marketing.v1.DiscountCodeStatusR\x06status\x12\x1b\n\tcreate_by\x18\x1b \x01(\x05R\x08\x63reateBy\x12\x1b\n\tupdate_by\x18\x1c \x01(\x05R\x08updateBy\x12;\n\x0b\x63reate_time\x18\x1d \x01(\x0b\x32\x1a.google.protobuf.TimestampR\ncreateTime\x12;\n\x0bupdate_time\x18\x1e \x01(\x0b\x32\x1a.google.protobuf.TimestampR\nupdateTime\x12!\n\x0clocation_ids\x18\x1f \x03(\x03R\x0blocationIds\x12\x46\n\x0b\x65xpiry_type\x18  \x01(\x0e\x32%.moego.models.marketing.v1.ExpiryTypeR\nexpiryType\x12;\n\x0b\x65xpiry_time\x18! \x01(\x0b\x32\x1a.google.protobuf.TimestampR\nexpiryTime\"\xb4\x02\n\x14\x44iscountCodeLogModel\x12\x0e\n\x02id\x18\x01 \x01(\x03R\x02id\x12\x17\n\x07\x63ode_id\x18\x02 \x01(\x03R\x06\x63odeId\x12\x46\n\x0bredeem_type\x18\x03 \x01(\x0e\x32%.moego.models.marketing.v1.RedeemTypeR\nredeemType\x12\x1b\n\tclient_id\x18\x04 \x01(\x03R\x08\x63lientId\x12\x17\n\x07pet_ids\x18\x05 \x03(\x03R\x06petIds\x12\x1b\n\tcreate_by\x18\x06 \x01(\x05R\x08\x63reateBy\x12;\n\x0bredeem_time\x18\x07 \x01(\x0b\x32\x1a.google.protobuf.TimestampR\nredeemTime\x12\x1b\n\tredeem_by\x18\x08 \x01(\x03R\x08redeemBy\"\x8c\x03\n\x1c\x44iscountCodeLogCompositeView\x12\x0e\n\x02id\x18\x01 \x01(\x03R\x02id\x12\x46\n\x0bredeem_type\x18\x02 \x01(\x0e\x32%.moego.models.marketing.v1.RedeemTypeR\nredeemType\x12\x1f\n\x0b\x63lient_name\x18\x03 \x01(\tR\nclientName\x12\x19\n\x08pet_name\x18\x04 \x01(\tR\x07petName\x12\x1d\n\nstaff_name\x18\x05 \x01(\tR\tstaffName\x12;\n\x0bredeem_time\x18\x06 \x01(\x0b\x32\x1a.google.protobuf.TimestampR\nredeemTime\x12\x1b\n\tredeem_id\x18\x07 \x01(\x03R\x08redeemId\x12#\n\rlocation_name\x18\x08 \x01(\tR\x0clocationName\x12\x1f\n\x0b\x62usiness_id\x18\t \x01(\x03R\nbusinessId\x12\x19\n\x08order_id\x18\n \x01(\x03R\x07orderId\"\xd4\x01\n\"DiscountCodeModelOnlineBookingView\x12\x0e\n\x02id\x18\x01 \x01(\x03R\x02id\x12#\n\rdiscount_code\x18\x02 \x01(\tR\x0c\x64iscountCode\x12 \n\x0b\x64\x65scription\x18\x03 \x01(\tR\x0b\x64\x65scription\x12\x16\n\x06\x61mount\x18\x04 \x01(\x01R\x06\x61mount\x12?\n\x04type\x18\x05 \x01(\x0e\x32+.moego.models.marketing.v1.DiscountCodeTypeR\x04type\"\xa0\n\n\x19\x44iscountCodeCompositeView\x12\x0e\n\x02id\x18\x01 \x01(\x03R\x02id\x12\x1f\n\x0b\x62usiness_id\x18\x02 \x01(\x03R\nbusinessId\x12#\n\rdiscount_code\x18\x03 \x01(\tR\x0c\x64iscountCode\x12 \n\x0b\x64\x65scription\x18\x04 \x01(\tR\x0b\x64\x65scription\x12\x16\n\x06\x61mount\x18\x05 \x01(\x01R\x06\x61mount\x12?\n\x04type\x18\x06 \x01(\x0e\x32+.moego.models.marketing.v1.DiscountCodeTypeR\x04type\x12\x1d\n\nstart_date\x18\x07 \x01(\tR\tstartDate\x12\x19\n\x08\x65nd_date\x18\x08 \x01(\tR\x07\x65ndDate\x12*\n\x11\x61llowed_all_thing\x18\t \x01(\x08R\x0f\x61llowedAllThing\x12\x30\n\x14\x61llowed_all_services\x18\n \x01(\x08R\x12\x61llowedAllServices\x12\x1f\n\x0bservice_ids\x18\x0b \x03(\x03R\nserviceIds\x12\x1c\n\nadd_on_ids\x18\x0c \x03(\x03R\x08\x61\x64\x64OnIds\x12\x30\n\x14\x61llowed_all_products\x18\r \x01(\x08R\x12\x61llowedAllProducts\x12\x1f\n\x0bproduct_ids\x18\x0e \x03(\x03R\nproductIds\x12.\n\x13\x61llowed_all_clients\x18\x0f \x01(\x08R\x11\x61llowedAllClients\x12.\n\x13\x61llowed_new_clients\x18\x10 \x01(\x08R\x11\x61llowedNewClients\x12#\n\rclients_group\x18\x11 \x01(\tR\x0c\x63lientsGroup\x12\x1d\n\nclient_ids\x18\x12 \x03(\x03R\tclientIds\x12\x1f\n\x0blimit_usage\x18\x13 \x01(\x05R\nlimitUsage\x12\x35\n\x17limit_number_per_client\x18\x14 \x01(\x05R\x14limitNumberPerClient\x12!\n\x0climit_budget\x18\x15 \x01(\x05R\x0blimitBudget\x12\x34\n\x16\x61uto_apply_association\x18\x16 \x01(\x08R\x14\x61utoApplyAssociation\x12\x32\n\x15\x65nable_online_booking\x18\x17 \x01(\x08R\x13\x65nableOnlineBooking\x12%\n\x0e\x64iscount_sales\x18\x18 \x01(\x01R\rdiscountSales\x12\x1f\n\x0btotal_usage\x18\x19 \x01(\x05R\ntotalUsage\x12\x45\n\x06status\x18\x1a \x01(\x0e\x32-.moego.models.marketing.v1.DiscountCodeStatusR\x06status\x12#\n\rservice_names\x18\x1b \x03(\tR\x0cserviceNames\x12#\n\rproduct_names\x18\x1c \x03(\tR\x0cproductNames\x12!\n\x0clocation_ids\x18\x1d \x03(\x03R\x0blocationIds\x12\x46\n\x0b\x65xpiry_type\x18\x1e \x01(\x0e\x32%.moego.models.marketing.v1.ExpiryTypeR\nexpiryType\x12;\n\x0b\x65xpiry_time\x18\x1f \x01(\x0b\x32\x1a.google.protobuf.TimestampR\nexpiryTimeB\x81\x01\n!com.moego.idl.models.marketing.v1P\x01ZZgithub.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/marketing/v1;marketingpbb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'moego.models.marketing.v1.discount_code_models_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n!com.moego.idl.models.marketing.v1P\001ZZgithub.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/marketing/v1;marketingpb'
  _globals['_DISCOUNTCODEMODEL']._serialized_start=170
  _globals['_DISCOUNTCODEMODEL']._serialized_end=1580
  _globals['_DISCOUNTCODELOGMODEL']._serialized_start=1583
  _globals['_DISCOUNTCODELOGMODEL']._serialized_end=1891
  _globals['_DISCOUNTCODELOGCOMPOSITEVIEW']._serialized_start=1894
  _globals['_DISCOUNTCODELOGCOMPOSITEVIEW']._serialized_end=2290
  _globals['_DISCOUNTCODEMODELONLINEBOOKINGVIEW']._serialized_start=2293
  _globals['_DISCOUNTCODEMODELONLINEBOOKINGVIEW']._serialized_end=2505
  _globals['_DISCOUNTCODECOMPOSITEVIEW']._serialized_start=2508
  _globals['_DISCOUNTCODECOMPOSITEVIEW']._serialized_end=3820
# @@protoc_insertion_point(module_scope)
