# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: moego/models/permission/v1/permission_defs.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'moego/models/permission/v1/permission_defs.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from validate import validate_pb2 as validate_dot_validate__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n0moego/models/permission/v1/permission_defs.proto\x12\x1amoego.models.permission.v1\x1a\x17validate/validate.proto\"\x9f\x01\n EditPermissionScopeExtraParamDef\x12/\n\nbefore_day\x18\x01 \x01(\rB\x0b\xfa\x42\x08*\x06\x18\x1e(\x00@\x01H\x00R\tbeforeDay\x88\x01\x01\x12-\n\tafter_day\x18\x02 \x01(\rB\x0b\xfa\x42\x08*\x06\x18\x1e(\x00@\x01H\x01R\x08\x61\x66terDay\x88\x01\x01\x42\r\n\x0b_before_dayB\x0c\n\n_after_day\"\xf8\x03\n\x11\x45\x64itPermissionDef\x12,\n\rpermission_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\x0cpermissionId\x12\x1b\n\tis_active\x18\x02 \x01(\x08R\x08isActive\x12>\n\x14selected_scope_index\x18\x03 \x01(\x03\x42\x07\xfa\x42\x04\"\x02(\x00H\x00R\x12selectedScopeIndex\x88\x01\x01\x12V\n\x0fsub_permissions\x18\x04 \x03(\x0b\x32-.moego.models.permission.v1.EditPermissionDefR\x0esubPermissions\x12\x45\n\x18selected_sub_scope_index\x18\x05 \x01(\x03\x42\x07\xfa\x42\x04\"\x02(\x00H\x01R\x15selectedSubScopeIndex\x88\x01\x01\x12m\n\x11scope_extra_param\x18\x06 \x01(\x0b\x32<.moego.models.permission.v1.EditPermissionScopeExtraParamDefH\x02R\x0fscopeExtraParam\x88\x01\x01\x42\x17\n\x15_selected_scope_indexB\x1b\n\x19_selected_sub_scope_indexB\x14\n\x12_scope_extra_param\"\x96\x01\n\x19\x45\x64itCategoryPermissionDef\x12(\n\x0b\x63\x61tegory_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\ncategoryId\x12O\n\x0bpermissions\x18\x02 \x03(\x0b\x32-.moego.models.permission.v1.EditPermissionDefR\x0bpermissionsB\x84\x01\n\"com.moego.idl.models.permission.v1P\x01Z\\github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/permission/v1;permissionpbb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'moego.models.permission.v1.permission_defs_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n\"com.moego.idl.models.permission.v1P\001Z\\github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/permission/v1;permissionpb'
  _globals['_EDITPERMISSIONSCOPEEXTRAPARAMDEF'].fields_by_name['before_day']._loaded_options = None
  _globals['_EDITPERMISSIONSCOPEEXTRAPARAMDEF'].fields_by_name['before_day']._serialized_options = b'\372B\010*\006\030\036(\000@\001'
  _globals['_EDITPERMISSIONSCOPEEXTRAPARAMDEF'].fields_by_name['after_day']._loaded_options = None
  _globals['_EDITPERMISSIONSCOPEEXTRAPARAMDEF'].fields_by_name['after_day']._serialized_options = b'\372B\010*\006\030\036(\000@\001'
  _globals['_EDITPERMISSIONDEF'].fields_by_name['permission_id']._loaded_options = None
  _globals['_EDITPERMISSIONDEF'].fields_by_name['permission_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_EDITPERMISSIONDEF'].fields_by_name['selected_scope_index']._loaded_options = None
  _globals['_EDITPERMISSIONDEF'].fields_by_name['selected_scope_index']._serialized_options = b'\372B\004\"\002(\000'
  _globals['_EDITPERMISSIONDEF'].fields_by_name['selected_sub_scope_index']._loaded_options = None
  _globals['_EDITPERMISSIONDEF'].fields_by_name['selected_sub_scope_index']._serialized_options = b'\372B\004\"\002(\000'
  _globals['_EDITCATEGORYPERMISSIONDEF'].fields_by_name['category_id']._loaded_options = None
  _globals['_EDITCATEGORYPERMISSIONDEF'].fields_by_name['category_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_EDITPERMISSIONSCOPEEXTRAPARAMDEF']._serialized_start=106
  _globals['_EDITPERMISSIONSCOPEEXTRAPARAMDEF']._serialized_end=265
  _globals['_EDITPERMISSIONDEF']._serialized_start=268
  _globals['_EDITPERMISSIONDEF']._serialized_end=772
  _globals['_EDITCATEGORYPERMISSIONDEF']._serialized_start=775
  _globals['_EDITCATEGORYPERMISSIONDEF']._serialized_end=925
# @@protoc_insertion_point(module_scope)
