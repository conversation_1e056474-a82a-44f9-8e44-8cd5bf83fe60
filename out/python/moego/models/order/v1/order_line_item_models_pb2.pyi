from google.type import decimal_pb2 as _decimal_pb2
from google.type import money_pb2 as _money_pb2
from moego.models.order.v1 import order_line_discount_models_pb2 as _order_line_discount_models_pb2
from moego.models.order.v1 import order_line_extra_fee_models_pb2 as _order_line_extra_fee_models_pb2
from moego.models.order.v1 import order_line_tax_models_pb2 as _order_line_tax_models_pb2
from google.protobuf.internal import containers as _containers
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from collections.abc import Iterable as _Iterable, Mapping as _Mapping
from typing import ClassVar as _ClassVar, Optional as _Optional, Union as _Union

DESCRIPTOR: _descriptor.FileDescriptor

class OrderLineItemModel(_message.Message):
    __slots__ = ("id", "business_id", "object_id", "type", "name", "unit_price", "quantity", "staff_id", "order_id", "is_deleted", "description", "purchased_quantity", "tips_amount", "tax_amount", "discount_amount", "extra_fee_amount", "sub_total_amount", "total_amount", "create_time", "update_time", "line_taxes", "line_discounts", "line_extra_fees", "pet_id", "pet_detail_id", "tax_id", "tax_rate", "tax_name", "currency_code", "refunded_quantity", "refunded_amount", "refunded_tax_amount", "refunded_discount_amount")
    ID_FIELD_NUMBER: _ClassVar[int]
    BUSINESS_ID_FIELD_NUMBER: _ClassVar[int]
    OBJECT_ID_FIELD_NUMBER: _ClassVar[int]
    TYPE_FIELD_NUMBER: _ClassVar[int]
    NAME_FIELD_NUMBER: _ClassVar[int]
    UNIT_PRICE_FIELD_NUMBER: _ClassVar[int]
    QUANTITY_FIELD_NUMBER: _ClassVar[int]
    STAFF_ID_FIELD_NUMBER: _ClassVar[int]
    ORDER_ID_FIELD_NUMBER: _ClassVar[int]
    IS_DELETED_FIELD_NUMBER: _ClassVar[int]
    DESCRIPTION_FIELD_NUMBER: _ClassVar[int]
    PURCHASED_QUANTITY_FIELD_NUMBER: _ClassVar[int]
    TIPS_AMOUNT_FIELD_NUMBER: _ClassVar[int]
    TAX_AMOUNT_FIELD_NUMBER: _ClassVar[int]
    DISCOUNT_AMOUNT_FIELD_NUMBER: _ClassVar[int]
    EXTRA_FEE_AMOUNT_FIELD_NUMBER: _ClassVar[int]
    SUB_TOTAL_AMOUNT_FIELD_NUMBER: _ClassVar[int]
    TOTAL_AMOUNT_FIELD_NUMBER: _ClassVar[int]
    CREATE_TIME_FIELD_NUMBER: _ClassVar[int]
    UPDATE_TIME_FIELD_NUMBER: _ClassVar[int]
    LINE_TAXES_FIELD_NUMBER: _ClassVar[int]
    LINE_DISCOUNTS_FIELD_NUMBER: _ClassVar[int]
    LINE_EXTRA_FEES_FIELD_NUMBER: _ClassVar[int]
    PET_ID_FIELD_NUMBER: _ClassVar[int]
    PET_DETAIL_ID_FIELD_NUMBER: _ClassVar[int]
    TAX_ID_FIELD_NUMBER: _ClassVar[int]
    TAX_RATE_FIELD_NUMBER: _ClassVar[int]
    TAX_NAME_FIELD_NUMBER: _ClassVar[int]
    CURRENCY_CODE_FIELD_NUMBER: _ClassVar[int]
    REFUNDED_QUANTITY_FIELD_NUMBER: _ClassVar[int]
    REFUNDED_AMOUNT_FIELD_NUMBER: _ClassVar[int]
    REFUNDED_TAX_AMOUNT_FIELD_NUMBER: _ClassVar[int]
    REFUNDED_DISCOUNT_AMOUNT_FIELD_NUMBER: _ClassVar[int]
    id: int
    business_id: int
    object_id: int
    type: str
    name: str
    unit_price: float
    quantity: int
    staff_id: int
    order_id: int
    is_deleted: bool
    description: str
    purchased_quantity: int
    tips_amount: float
    tax_amount: float
    discount_amount: float
    extra_fee_amount: float
    sub_total_amount: float
    total_amount: float
    create_time: int
    update_time: int
    line_taxes: _containers.RepeatedCompositeFieldContainer[_order_line_tax_models_pb2.OrderLineTaxModel]
    line_discounts: _containers.RepeatedCompositeFieldContainer[_order_line_discount_models_pb2.OrderLineDiscountModel]
    line_extra_fees: _containers.RepeatedCompositeFieldContainer[_order_line_extra_fee_models_pb2.OrderLineExtraFeeModel]
    pet_id: int
    pet_detail_id: int
    tax_id: int
    tax_rate: _decimal_pb2.Decimal
    tax_name: str
    currency_code: str
    refunded_quantity: int
    refunded_amount: _money_pb2.Money
    refunded_tax_amount: _money_pb2.Money
    refunded_discount_amount: _money_pb2.Money
    def __init__(self, id: _Optional[int] = ..., business_id: _Optional[int] = ..., object_id: _Optional[int] = ..., type: _Optional[str] = ..., name: _Optional[str] = ..., unit_price: _Optional[float] = ..., quantity: _Optional[int] = ..., staff_id: _Optional[int] = ..., order_id: _Optional[int] = ..., is_deleted: bool = ..., description: _Optional[str] = ..., purchased_quantity: _Optional[int] = ..., tips_amount: _Optional[float] = ..., tax_amount: _Optional[float] = ..., discount_amount: _Optional[float] = ..., extra_fee_amount: _Optional[float] = ..., sub_total_amount: _Optional[float] = ..., total_amount: _Optional[float] = ..., create_time: _Optional[int] = ..., update_time: _Optional[int] = ..., line_taxes: _Optional[_Iterable[_Union[_order_line_tax_models_pb2.OrderLineTaxModel, _Mapping]]] = ..., line_discounts: _Optional[_Iterable[_Union[_order_line_discount_models_pb2.OrderLineDiscountModel, _Mapping]]] = ..., line_extra_fees: _Optional[_Iterable[_Union[_order_line_extra_fee_models_pb2.OrderLineExtraFeeModel, _Mapping]]] = ..., pet_id: _Optional[int] = ..., pet_detail_id: _Optional[int] = ..., tax_id: _Optional[int] = ..., tax_rate: _Optional[_Union[_decimal_pb2.Decimal, _Mapping]] = ..., tax_name: _Optional[str] = ..., currency_code: _Optional[str] = ..., refunded_quantity: _Optional[int] = ..., refunded_amount: _Optional[_Union[_money_pb2.Money, _Mapping]] = ..., refunded_tax_amount: _Optional[_Union[_money_pb2.Money, _Mapping]] = ..., refunded_discount_amount: _Optional[_Union[_money_pb2.Money, _Mapping]] = ...) -> None: ...

class OrderItemModel(_message.Message):
    __slots__ = ("id", "business_id", "object_id", "type", "name", "unit_price", "quantity", "staff_id", "order_id", "is_deleted", "description", "purchased_quantity", "tips_amount", "tax_amount", "discount_amount", "sub_total_amount", "total_amount", "create_time", "update_time", "line_discounts", "pet_id", "pet_detail_id", "tax_id", "tax_rate", "tax_name", "currency_code", "refunded_quantity", "refunded_amount", "refunded_tax_amount", "refunded_discount_amount", "staff_ids")
    ID_FIELD_NUMBER: _ClassVar[int]
    BUSINESS_ID_FIELD_NUMBER: _ClassVar[int]
    OBJECT_ID_FIELD_NUMBER: _ClassVar[int]
    TYPE_FIELD_NUMBER: _ClassVar[int]
    NAME_FIELD_NUMBER: _ClassVar[int]
    UNIT_PRICE_FIELD_NUMBER: _ClassVar[int]
    QUANTITY_FIELD_NUMBER: _ClassVar[int]
    STAFF_ID_FIELD_NUMBER: _ClassVar[int]
    ORDER_ID_FIELD_NUMBER: _ClassVar[int]
    IS_DELETED_FIELD_NUMBER: _ClassVar[int]
    DESCRIPTION_FIELD_NUMBER: _ClassVar[int]
    PURCHASED_QUANTITY_FIELD_NUMBER: _ClassVar[int]
    TIPS_AMOUNT_FIELD_NUMBER: _ClassVar[int]
    TAX_AMOUNT_FIELD_NUMBER: _ClassVar[int]
    DISCOUNT_AMOUNT_FIELD_NUMBER: _ClassVar[int]
    SUB_TOTAL_AMOUNT_FIELD_NUMBER: _ClassVar[int]
    TOTAL_AMOUNT_FIELD_NUMBER: _ClassVar[int]
    CREATE_TIME_FIELD_NUMBER: _ClassVar[int]
    UPDATE_TIME_FIELD_NUMBER: _ClassVar[int]
    LINE_DISCOUNTS_FIELD_NUMBER: _ClassVar[int]
    PET_ID_FIELD_NUMBER: _ClassVar[int]
    PET_DETAIL_ID_FIELD_NUMBER: _ClassVar[int]
    TAX_ID_FIELD_NUMBER: _ClassVar[int]
    TAX_RATE_FIELD_NUMBER: _ClassVar[int]
    TAX_NAME_FIELD_NUMBER: _ClassVar[int]
    CURRENCY_CODE_FIELD_NUMBER: _ClassVar[int]
    REFUNDED_QUANTITY_FIELD_NUMBER: _ClassVar[int]
    REFUNDED_AMOUNT_FIELD_NUMBER: _ClassVar[int]
    REFUNDED_TAX_AMOUNT_FIELD_NUMBER: _ClassVar[int]
    REFUNDED_DISCOUNT_AMOUNT_FIELD_NUMBER: _ClassVar[int]
    STAFF_IDS_FIELD_NUMBER: _ClassVar[int]
    id: int
    business_id: int
    object_id: int
    type: str
    name: str
    unit_price: _money_pb2.Money
    quantity: int
    staff_id: int
    order_id: int
    is_deleted: bool
    description: str
    purchased_quantity: int
    tips_amount: _money_pb2.Money
    tax_amount: _money_pb2.Money
    discount_amount: _money_pb2.Money
    sub_total_amount: _money_pb2.Money
    total_amount: _money_pb2.Money
    create_time: int
    update_time: int
    line_discounts: _containers.RepeatedCompositeFieldContainer[_order_line_discount_models_pb2.OrderLineDiscountModelV1]
    pet_id: int
    pet_detail_id: int
    tax_id: int
    tax_rate: _decimal_pb2.Decimal
    tax_name: str
    currency_code: str
    refunded_quantity: int
    refunded_amount: _money_pb2.Money
    refunded_tax_amount: _money_pb2.Money
    refunded_discount_amount: _money_pb2.Money
    staff_ids: _containers.RepeatedScalarFieldContainer[int]
    def __init__(self, id: _Optional[int] = ..., business_id: _Optional[int] = ..., object_id: _Optional[int] = ..., type: _Optional[str] = ..., name: _Optional[str] = ..., unit_price: _Optional[_Union[_money_pb2.Money, _Mapping]] = ..., quantity: _Optional[int] = ..., staff_id: _Optional[int] = ..., order_id: _Optional[int] = ..., is_deleted: bool = ..., description: _Optional[str] = ..., purchased_quantity: _Optional[int] = ..., tips_amount: _Optional[_Union[_money_pb2.Money, _Mapping]] = ..., tax_amount: _Optional[_Union[_money_pb2.Money, _Mapping]] = ..., discount_amount: _Optional[_Union[_money_pb2.Money, _Mapping]] = ..., sub_total_amount: _Optional[_Union[_money_pb2.Money, _Mapping]] = ..., total_amount: _Optional[_Union[_money_pb2.Money, _Mapping]] = ..., create_time: _Optional[int] = ..., update_time: _Optional[int] = ..., line_discounts: _Optional[_Iterable[_Union[_order_line_discount_models_pb2.OrderLineDiscountModelV1, _Mapping]]] = ..., pet_id: _Optional[int] = ..., pet_detail_id: _Optional[int] = ..., tax_id: _Optional[int] = ..., tax_rate: _Optional[_Union[_decimal_pb2.Decimal, _Mapping]] = ..., tax_name: _Optional[str] = ..., currency_code: _Optional[str] = ..., refunded_quantity: _Optional[int] = ..., refunded_amount: _Optional[_Union[_money_pb2.Money, _Mapping]] = ..., refunded_tax_amount: _Optional[_Union[_money_pb2.Money, _Mapping]] = ..., refunded_discount_amount: _Optional[_Union[_money_pb2.Money, _Mapping]] = ..., staff_ids: _Optional[_Iterable[int]] = ...) -> None: ...
