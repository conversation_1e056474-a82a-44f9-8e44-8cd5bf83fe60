# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: moego/models/order/v1/order_enums.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'moego/models/order/v1/order_enums.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\'moego/models/order/v1/order_enums.proto\x12\x15moego.models.order.v1*F\n\x0bOrderStatus\x12\x0b\n\x07\x43REATED\x10\x00\x12\x0e\n\nPROCESSING\x10\x01\x12\r\n\tCOMPLETED\x10\x02\x12\x0b\n\x07REMOVED\x10\x03*\x9f\x01\n\x0fOrderSourceType\x12!\n\x1dORDER_SOURCE_TYPE_UNSPECIFIED\x10\x00\x12\x0b\n\x07UNKNOWN\x10\x01\x12\x0f\n\x0b\x41PPOINTMENT\x10\x02\x12\x0b\n\x07NO_SHOW\x10\x03\x12\x0b\n\x07PRODUCT\x10\x04\x12\x0b\n\x07PACKAGE\x10\x05\x12\x13\n\x0f\x42OOKING_REQUEST\x10\x06\x12\x0f\n\x0b\x46ULFILLMENT\x10\x07*m\n\tEventType\x12\x1a\n\x16\x45VENT_TYPE_UNSPECIFIED\x10\x00\x12\x13\n\x0fORDER_COMPLETED\x10\x01\x12\x1a\n\x16REFUND_ORDER_COMPLETED\x10\x02\x12\x13\n\x0fORDER_CANCELLED\x10\x03*m\n\x13\x45ventDeliveryStatus\x12%\n!EVENT_DELIVERY_STATUS_UNSPECIFIED\x10\x00\x12\x0b\n\x07PENDING\x10\x01\x12\x08\n\x04SENT\x10\x02\x12\n\n\x06\x46\x41ILED\x10\x03\x12\x0c\n\x08\x43\x41NCELED\x10\x04*\xf1\x01\n\x12OrderPaymentStatus\x12$\n ORDER_PAYMENT_STATUS_UNSPECIFIED\x10\x00\x12 \n\x1cORDER_PAYMENT_STATUS_CREATED\x10\x64\x12-\n(ORDER_PAYMENT_STATUS_TRANSACTION_CREATED\x10\xc8\x01\x12\x1e\n\x19ORDER_PAYMENT_STATUS_PAID\x10\xac\x02\x12 \n\x1bORDER_PAYMENT_STATUS_FAILED\x10\x90\x03\x12\"\n\x1dORDER_PAYMENT_STATUS_CANCELED\x10\xf4\x03\x42u\n\x1d\x63om.moego.idl.models.order.v1P\x01ZRgithub.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/order/v1;orderpbb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'moego.models.order.v1.order_enums_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n\035com.moego.idl.models.order.v1P\001ZRgithub.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/order/v1;orderpb'
  _globals['_ORDERSTATUS']._serialized_start=66
  _globals['_ORDERSTATUS']._serialized_end=136
  _globals['_ORDERSOURCETYPE']._serialized_start=139
  _globals['_ORDERSOURCETYPE']._serialized_end=298
  _globals['_EVENTTYPE']._serialized_start=300
  _globals['_EVENTTYPE']._serialized_end=409
  _globals['_EVENTDELIVERYSTATUS']._serialized_start=411
  _globals['_EVENTDELIVERYSTATUS']._serialized_end=520
  _globals['_ORDERPAYMENTSTATUS']._serialized_start=523
  _globals['_ORDERPAYMENTSTATUS']._serialized_end=764
# @@protoc_insertion_point(module_scope)
