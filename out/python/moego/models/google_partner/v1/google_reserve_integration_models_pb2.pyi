from google.protobuf import timestamp_pb2 as _timestamp_pb2
from moego.models.google_partner.v1 import google_reserve_integration_enums_pb2 as _google_reserve_integration_enums_pb2
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from collections.abc import Mapping as _Mapping
from typing import ClassVar as _ClassVar, Optional as _Optional, Union as _Union

DESCRIPTOR: _descriptor.FileDescriptor

class GoogleReserveIntegrationModel(_message.Message):
    __slots__ = ("id", "business_id", "enabled", "status", "create_time", "update_time")
    ID_FIELD_NUMBER: _ClassVar[int]
    BUSINESS_ID_FIELD_NUMBER: _ClassVar[int]
    ENABLED_FIELD_NUMBER: _ClassVar[int]
    STATUS_FIELD_NUMBER: _ClassVar[int]
    CREATE_TIME_FIELD_NUMBER: _ClassVar[int]
    UPDATE_TIME_FIELD_NUMBER: _ClassVar[int]
    id: int
    business_id: int
    enabled: bool
    status: _google_reserve_integration_enums_pb2.GoogleReserveIntegrationStatus
    create_time: _timestamp_pb2.Timestamp
    update_time: _timestamp_pb2.Timestamp
    def __init__(self, id: _Optional[int] = ..., business_id: _Optional[int] = ..., enabled: bool = ..., status: _Optional[_Union[_google_reserve_integration_enums_pb2.GoogleReserveIntegrationStatus, str]] = ..., create_time: _Optional[_Union[datetime.datetime, _timestamp_pb2.Timestamp, _Mapping]] = ..., update_time: _Optional[_Union[datetime.datetime, _timestamp_pb2.Timestamp, _Mapping]] = ...) -> None: ...
