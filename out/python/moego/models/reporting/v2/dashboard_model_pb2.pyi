from moego.models.reporting.v2 import common_model_pb2 as _common_model_pb2
from moego.models.reporting.v2 import diagram_model_pb2 as _diagram_model_pb2
from moego.models.reporting.v2 import field_model_pb2 as _field_model_pb2
from moego.models.reporting.v2 import filter_model_pb2 as _filter_model_pb2
from google.protobuf.internal import containers as _containers
from google.protobuf.internal import enum_type_wrapper as _enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from collections.abc import Iterable as _Iterable, Mapping as _Mapping
from typing import ClassVar as _ClassVar, Optional as _Optional, Union as _Union

DESCRIPTOR: _descriptor.FileDescriptor

class DashboardPage(_message.Message):
    __slots__ = ("tab", "title", "groups", "permission_code")
    class Tab(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
        __slots__ = ()
        TAB_UNSPECIFIED: _ClassVar[DashboardPage.Tab]
        OVERVIEW: _ClassVar[DashboardPage.Tab]
        SALES: _ClassVar[DashboardPage.Tab]
        PETS: _ClassVar[DashboardPage.Tab]
        STAFF: _ClassVar[DashboardPage.Tab]
        OPERATION: _ClassVar[DashboardPage.Tab]
        APP_PERSONAL_PERFORMANCE: _ClassVar[DashboardPage.Tab]
        APP_OVERVIEW: _ClassVar[DashboardPage.Tab]
        APP_STAFF_PERFORMANCE: _ClassVar[DashboardPage.Tab]
        APP_HISTORY: _ClassVar[DashboardPage.Tab]
        APP_REPORT: _ClassVar[DashboardPage.Tab]
        PAYROLL: _ClassVar[DashboardPage.Tab]
    TAB_UNSPECIFIED: DashboardPage.Tab
    OVERVIEW: DashboardPage.Tab
    SALES: DashboardPage.Tab
    PETS: DashboardPage.Tab
    STAFF: DashboardPage.Tab
    OPERATION: DashboardPage.Tab
    APP_PERSONAL_PERFORMANCE: DashboardPage.Tab
    APP_OVERVIEW: DashboardPage.Tab
    APP_STAFF_PERFORMANCE: DashboardPage.Tab
    APP_HISTORY: DashboardPage.Tab
    APP_REPORT: DashboardPage.Tab
    PAYROLL: DashboardPage.Tab
    TAB_FIELD_NUMBER: _ClassVar[int]
    TITLE_FIELD_NUMBER: _ClassVar[int]
    GROUPS_FIELD_NUMBER: _ClassVar[int]
    PERMISSION_CODE_FIELD_NUMBER: _ClassVar[int]
    tab: DashboardPage.Tab
    title: str
    groups: _containers.RepeatedCompositeFieldContainer[DashBoardGroup]
    permission_code: str
    def __init__(self, tab: _Optional[_Union[DashboardPage.Tab, str]] = ..., title: _Optional[str] = ..., groups: _Optional[_Iterable[_Union[DashBoardGroup, _Mapping]]] = ..., permission_code: _Optional[str] = ...) -> None: ...

class DashBoardGroup(_message.Message):
    __slots__ = ("diagram_id", "title", "description", "drill_config", "diagrams", "permission_code")
    DIAGRAM_ID_FIELD_NUMBER: _ClassVar[int]
    TITLE_FIELD_NUMBER: _ClassVar[int]
    DESCRIPTION_FIELD_NUMBER: _ClassVar[int]
    DRILL_CONFIG_FIELD_NUMBER: _ClassVar[int]
    DIAGRAMS_FIELD_NUMBER: _ClassVar[int]
    PERMISSION_CODE_FIELD_NUMBER: _ClassVar[int]
    diagram_id: str
    title: str
    description: str
    drill_config: _common_model_pb2.DrillConfig
    diagrams: _containers.RepeatedCompositeFieldContainer[DashBoardDiagram]
    permission_code: str
    def __init__(self, diagram_id: _Optional[str] = ..., title: _Optional[str] = ..., description: _Optional[str] = ..., drill_config: _Optional[_Union[_common_model_pb2.DrillConfig, _Mapping]] = ..., diagrams: _Optional[_Iterable[_Union[DashBoardDiagram, _Mapping]]] = ..., permission_code: _Optional[str] = ...) -> None: ...

class DashBoardDiagram(_message.Message):
    __slots__ = ("diagram_id", "diagram_type", "table_meta", "drill_config", "permission_code", "associated_diagram_id", "default_group_by_field_keys", "fields", "filters")
    DIAGRAM_ID_FIELD_NUMBER: _ClassVar[int]
    DIAGRAM_TYPE_FIELD_NUMBER: _ClassVar[int]
    TABLE_META_FIELD_NUMBER: _ClassVar[int]
    DRILL_CONFIG_FIELD_NUMBER: _ClassVar[int]
    PERMISSION_CODE_FIELD_NUMBER: _ClassVar[int]
    ASSOCIATED_DIAGRAM_ID_FIELD_NUMBER: _ClassVar[int]
    DEFAULT_GROUP_BY_FIELD_KEYS_FIELD_NUMBER: _ClassVar[int]
    FIELDS_FIELD_NUMBER: _ClassVar[int]
    FILTERS_FIELD_NUMBER: _ClassVar[int]
    diagram_id: str
    diagram_type: _diagram_model_pb2.DiagramType
    table_meta: _diagram_model_pb2.TableMeta
    drill_config: _common_model_pb2.DrillConfig
    permission_code: str
    associated_diagram_id: str
    default_group_by_field_keys: _containers.RepeatedScalarFieldContainer[str]
    fields: _containers.RepeatedCompositeFieldContainer[_field_model_pb2.Field]
    filters: _containers.RepeatedCompositeFieldContainer[_filter_model_pb2.Filter]
    def __init__(self, diagram_id: _Optional[str] = ..., diagram_type: _Optional[_Union[_diagram_model_pb2.DiagramType, str]] = ..., table_meta: _Optional[_Union[_diagram_model_pb2.TableMeta, _Mapping]] = ..., drill_config: _Optional[_Union[_common_model_pb2.DrillConfig, _Mapping]] = ..., permission_code: _Optional[str] = ..., associated_diagram_id: _Optional[str] = ..., default_group_by_field_keys: _Optional[_Iterable[str]] = ..., fields: _Optional[_Iterable[_Union[_field_model_pb2.Field, _Mapping]]] = ..., filters: _Optional[_Iterable[_Union[_filter_model_pb2.Filter, _Mapping]]] = ...) -> None: ...
