# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: moego/models/reporting/v2/dashboard_model.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'moego/models/reporting/v2/dashboard_model.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from moego.models.reporting.v2 import common_model_pb2 as moego_dot_models_dot_reporting_dot_v2_dot_common__model__pb2
from moego.models.reporting.v2 import diagram_model_pb2 as moego_dot_models_dot_reporting_dot_v2_dot_diagram__model__pb2
from moego.models.reporting.v2 import field_model_pb2 as moego_dot_models_dot_reporting_dot_v2_dot_field__model__pb2
from moego.models.reporting.v2 import filter_model_pb2 as moego_dot_models_dot_reporting_dot_v2_dot_filter__model__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n/moego/models/reporting/v2/dashboard_model.proto\x12\x19moego.models.reporting.v2\x1a,moego/models/reporting/v2/common_model.proto\x1a-moego/models/reporting/v2/diagram_model.proto\x1a+moego/models/reporting/v2/field_model.proto\x1a,moego/models/reporting/v2/filter_model.proto\"\xa4\x03\n\rDashboardPage\x12>\n\x03tab\x18\x01 \x01(\x0e\x32,.moego.models.reporting.v2.DashboardPage.TabR\x03tab\x12\x14\n\x05title\x18\x02 \x01(\tR\x05title\x12\x41\n\x06groups\x18\x03 \x03(\x0b\x32).moego.models.reporting.v2.DashBoardGroupR\x06groups\x12\'\n\x0fpermission_code\x18\x04 \x01(\tR\x0epermissionCode\"\xd0\x01\n\x03Tab\x12\x13\n\x0fTAB_UNSPECIFIED\x10\x00\x12\x0c\n\x08OVERVIEW\x10\x01\x12\t\n\x05SALES\x10\x02\x12\x08\n\x04PETS\x10\x03\x12\t\n\x05STAFF\x10\x04\x12\r\n\tOPERATION\x10\x05\x12\x1c\n\x18\x41PP_PERSONAL_PERFORMANCE\x10\x06\x12\x10\n\x0c\x41PP_OVERVIEW\x10\x07\x12\x19\n\x15\x41PP_STAFF_PERFORMANCE\x10\x08\x12\x0f\n\x0b\x41PP_HISTORY\x10\t\x12\x0e\n\nAPP_REPORT\x10\n\x12\x0b\n\x07PAYROLL\x10\x0b\"\xa4\x02\n\x0e\x44\x61shBoardGroup\x12\x1d\n\ndiagram_id\x18\x01 \x01(\tR\tdiagramId\x12\x14\n\x05title\x18\x02 \x01(\tR\x05title\x12 \n\x0b\x64\x65scription\x18\x03 \x01(\tR\x0b\x64\x65scription\x12I\n\x0c\x64rill_config\x18\x04 \x01(\x0b\x32&.moego.models.reporting.v2.DrillConfigR\x0b\x64rillConfig\x12G\n\x08\x64iagrams\x18\x05 \x03(\x0b\x32+.moego.models.reporting.v2.DashBoardDiagramR\x08\x64iagrams\x12\'\n\x0fpermission_code\x18\x06 \x01(\tR\x0epermissionCode\"\xe7\x04\n\x10\x44\x61shBoardDiagram\x12\x1d\n\ndiagram_id\x18\x01 \x01(\tR\tdiagramId\x12I\n\x0c\x64iagram_type\x18\x02 \x01(\x0e\x32&.moego.models.reporting.v2.DiagramTypeR\x0b\x64iagramType\x12H\n\ntable_meta\x18\x03 \x01(\x0b\x32$.moego.models.reporting.v2.TableMetaH\x00R\ttableMeta\x88\x01\x01\x12N\n\x0c\x64rill_config\x18\x04 \x01(\x0b\x32&.moego.models.reporting.v2.DrillConfigH\x01R\x0b\x64rillConfig\x88\x01\x01\x12\'\n\x0fpermission_code\x18\x05 \x01(\tR\x0epermissionCode\x12\x37\n\x15\x61ssociated_diagram_id\x18\x06 \x01(\tH\x02R\x13\x61ssociatedDiagramId\x88\x01\x01\x12<\n\x1b\x64\x65\x66\x61ult_group_by_field_keys\x18\x07 \x03(\tR\x17\x64\x65\x66\x61ultGroupByFieldKeys\x12\x38\n\x06\x66ields\x18\x08 \x03(\x0b\x32 .moego.models.reporting.v2.FieldR\x06\x66ields\x12;\n\x07\x66ilters\x18\t \x03(\x0b\x32!.moego.models.reporting.v2.FilterR\x07\x66iltersB\r\n\x0b_table_metaB\x0f\n\r_drill_configB\x18\n\x16_associated_diagram_idB\x81\x01\n!com.moego.idl.models.reporting.v2P\x01ZZgithub.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/reporting/v2;reportingpbb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'moego.models.reporting.v2.dashboard_model_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n!com.moego.idl.models.reporting.v2P\001ZZgithub.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/reporting/v2;reportingpb'
  _globals['_DASHBOARDPAGE']._serialized_start=263
  _globals['_DASHBOARDPAGE']._serialized_end=683
  _globals['_DASHBOARDPAGE_TAB']._serialized_start=475
  _globals['_DASHBOARDPAGE_TAB']._serialized_end=683
  _globals['_DASHBOARDGROUP']._serialized_start=686
  _globals['_DASHBOARDGROUP']._serialized_end=978
  _globals['_DASHBOARDDIAGRAM']._serialized_start=981
  _globals['_DASHBOARDDIAGRAM']._serialized_end=1596
# @@protoc_insertion_point(module_scope)
