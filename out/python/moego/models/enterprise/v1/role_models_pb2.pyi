from moego.models.permission.v1 import permission_enums_pb2 as _permission_enums_pb2
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from typing import ClassVar as _ClassVar, Optional as _Optional, Union as _Union

DESCRIPTOR: _descriptor.FileDescriptor

class RoleModel(_message.Message):
    __slots__ = ("id", "name", "description", "enterprise_id", "type")
    ID_FIELD_NUMBER: _ClassVar[int]
    NAME_FIELD_NUMBER: _ClassVar[int]
    DESCRIPTION_FIELD_NUMBER: _ClassVar[int]
    ENTERPRISE_ID_FIELD_NUMBER: _ClassVar[int]
    TYPE_FIELD_NUMBER: _ClassVar[int]
    id: int
    name: str
    description: str
    enterprise_id: int
    type: _permission_enums_pb2.RoleType
    def __init__(self, id: _Optional[int] = ..., name: _Optional[str] = ..., description: _Optional[str] = ..., enterprise_id: _Optional[int] = ..., type: _Optional[_Union[_permission_enums_pb2.RoleType, str]] = ...) -> None: ...
