from google.protobuf.internal import enum_type_wrapper as _enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from typing import ClassVar as _ClassVar, Optional as _Optional, Union as _Union

DESCRIPTOR: _descriptor.FileDescriptor

class TenantGroupModel(_message.Message):
    __slots__ = ("id", "name", "status")
    class Status(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
        __slots__ = ()
        TENANT_GROUP_STATUS_UNSPECIFIED: _ClassVar[TenantGroupModel.Status]
        NORMAL: _ClassVar[TenantGroupModel.Status]
        DELETE: _ClassVar[TenantGroupModel.Status]
    TENANT_GROUP_STATUS_UNSPECIFIED: TenantGroupModel.Status
    NORMAL: TenantGroupModel.Status
    DELETE: TenantGroupModel.Status
    ID_FIELD_NUMBER: _ClassVar[int]
    NAME_FIELD_NUMBER: _ClassVar[int]
    STATUS_FIELD_NUMBER: _ClassVar[int]
    id: int
    name: str
    status: TenantGroupModel.Status
    def __init__(self, id: _Optional[int] = ..., name: _Optional[str] = ..., status: _Optional[_Union[TenantGroupModel.Status, str]] = ...) -> None: ...
