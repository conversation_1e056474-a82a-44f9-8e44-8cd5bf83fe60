# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: moego/models/enterprise/v1/tenant_template_models.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'moego/models/enterprise/v1/tenant_template_models.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from moego.models.organization.v1 import company_models_pb2 as moego_dot_models_dot_organization_dot_v1_dot_company__models__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n7moego/models/enterprise/v1/tenant_template_models.proto\x12\x1amoego.models.enterprise.v1\x1a\x31moego/models/organization/v1/company_models.proto\"\xfc\x05\n\x13TenantTemplateModel\x12\x0e\n\x02id\x18\x01 \x01(\x03R\x02id\x12#\n\renterprise_id\x18\x02 \x01(\x03R\x0c\x65nterpriseId\x12\x12\n\x04name\x18\x03 \x01(\tR\x04name\x12N\n\x06status\x18\x04 \x01(\x0e\x32\x36.moego.models.enterprise.v1.TenantTemplateModel.StatusR\x06status\x12H\n\x04type\x18\x05 \x01(\x0e\x32\x34.moego.models.enterprise.v1.TenantTemplateModel.TypeR\x04type\x12\x1b\n\x06\x63onfig\x18\x06 \x01(\tH\x00R\x06\x63onfig\x88\x01\x01\x12\x1e\n\x0bmin_van_num\x18\x07 \x01(\x03R\tminVanNum\x12(\n\x10min_location_num\x18\x08 \x01(\x03R\x0eminLocationNum\x12u\n\x11\x63ompany_type_info\x18\t \x01(\x0b\x32\x44.moego.models.enterprise.v1.TenantTemplateModel.CompanyTypeInfoModelH\x01R\x0f\x63ompanyTypeInfo\x88\x01\x01\x1ar\n\x14\x43ompanyTypeInfoModel\x12\x0e\n\x02id\x18\x01 \x01(\x03R\x02id\x12J\n\x04type\x18\x02 \x01(\x0e\x32\x36.moego.models.organization.v1.CompanyModel.CompanyTypeR\x04type\"H\n\x06Status\x12&\n\"TENANT_TEMPLATE_STATUS_UNSPECIFIED\x10\x00\x12\n\n\x06NORMAL\x10\x01\x12\n\n\x06\x44\x45LETE\x10\x02\"E\n\x04Type\x12$\n TENANT_TEMPLATE_TYPE_UNSPECIFIED\x10\x00\x12\x0b\n\x07\x43OMPANY\x10\x01\x12\n\n\x06\x43ONFIG\x10\x02\x42\t\n\x07_configB\x14\n\x12_company_type_infoB\x84\x01\n\"com.moego.idl.models.enterprise.v1P\x01Z\\github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/enterprise/v1;enterprisepbb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'moego.models.enterprise.v1.tenant_template_models_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n\"com.moego.idl.models.enterprise.v1P\001Z\\github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/enterprise/v1;enterprisepb'
  _globals['_TENANTTEMPLATEMODEL']._serialized_start=139
  _globals['_TENANTTEMPLATEMODEL']._serialized_end=903
  _globals['_TENANTTEMPLATEMODEL_COMPANYTYPEINFOMODEL']._serialized_start=611
  _globals['_TENANTTEMPLATEMODEL_COMPANYTYPEINFOMODEL']._serialized_end=725
  _globals['_TENANTTEMPLATEMODEL_STATUS']._serialized_start=727
  _globals['_TENANTTEMPLATEMODEL_STATUS']._serialized_end=799
  _globals['_TENANTTEMPLATEMODEL_TYPE']._serialized_start=801
  _globals['_TENANTTEMPLATEMODEL_TYPE']._serialized_end=870
# @@protoc_insertion_point(module_scope)
