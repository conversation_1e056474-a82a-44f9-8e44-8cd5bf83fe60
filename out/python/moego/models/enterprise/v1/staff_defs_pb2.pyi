from google.protobuf import timestamp_pb2 as _timestamp_pb2
from validate import validate_pb2 as _validate_pb2
from google.protobuf.internal import containers as _containers
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from collections.abc import Iterable as _Iterable, Mapping as _Mapping
from typing import ClassVar as _ClassVar, Optional as _Optional, Union as _Union

DESCRIPTOR: _descriptor.FileDescriptor

class ResourceDef(_message.Message):
    __slots__ = ("is_all", "tenant_ids", "group_ids")
    IS_ALL_FIELD_NUMBER: _ClassVar[int]
    TENANT_IDS_FIELD_NUMBER: _ClassVar[int]
    GROUP_IDS_FIELD_NUMBER: _ClassVar[int]
    is_all: bool
    tenant_ids: _containers.RepeatedScalarFieldContainer[int]
    group_ids: _containers.RepeatedScalarFieldContainer[int]
    def __init__(self, is_all: bool = ..., tenant_ids: _Optional[_Iterable[int]] = ..., group_ids: _Optional[_Iterable[int]] = ...) -> None: ...

class CreateStaffProfile(_message.Message):
    __slots__ = ("avatar_path", "first_name", "last_name", "role_id", "hire_time", "color_code", "note", "profile_email")
    AVATAR_PATH_FIELD_NUMBER: _ClassVar[int]
    FIRST_NAME_FIELD_NUMBER: _ClassVar[int]
    LAST_NAME_FIELD_NUMBER: _ClassVar[int]
    ROLE_ID_FIELD_NUMBER: _ClassVar[int]
    HIRE_TIME_FIELD_NUMBER: _ClassVar[int]
    COLOR_CODE_FIELD_NUMBER: _ClassVar[int]
    NOTE_FIELD_NUMBER: _ClassVar[int]
    PROFILE_EMAIL_FIELD_NUMBER: _ClassVar[int]
    avatar_path: str
    first_name: str
    last_name: str
    role_id: int
    hire_time: _timestamp_pb2.Timestamp
    color_code: str
    note: str
    profile_email: str
    def __init__(self, avatar_path: _Optional[str] = ..., first_name: _Optional[str] = ..., last_name: _Optional[str] = ..., role_id: _Optional[int] = ..., hire_time: _Optional[_Union[datetime.datetime, _timestamp_pb2.Timestamp, _Mapping]] = ..., color_code: _Optional[str] = ..., note: _Optional[str] = ..., profile_email: _Optional[str] = ...) -> None: ...

class UpdateStaffProfile(_message.Message):
    __slots__ = ("avatar_path", "first_name", "last_name", "role_id", "hire_time", "color_code", "note", "profile_email")
    AVATAR_PATH_FIELD_NUMBER: _ClassVar[int]
    FIRST_NAME_FIELD_NUMBER: _ClassVar[int]
    LAST_NAME_FIELD_NUMBER: _ClassVar[int]
    ROLE_ID_FIELD_NUMBER: _ClassVar[int]
    HIRE_TIME_FIELD_NUMBER: _ClassVar[int]
    COLOR_CODE_FIELD_NUMBER: _ClassVar[int]
    NOTE_FIELD_NUMBER: _ClassVar[int]
    PROFILE_EMAIL_FIELD_NUMBER: _ClassVar[int]
    avatar_path: str
    first_name: str
    last_name: str
    role_id: int
    hire_time: _timestamp_pb2.Timestamp
    color_code: str
    note: str
    profile_email: str
    def __init__(self, avatar_path: _Optional[str] = ..., first_name: _Optional[str] = ..., last_name: _Optional[str] = ..., role_id: _Optional[int] = ..., hire_time: _Optional[_Union[datetime.datetime, _timestamp_pb2.Timestamp, _Mapping]] = ..., color_code: _Optional[str] = ..., note: _Optional[str] = ..., profile_email: _Optional[str] = ...) -> None: ...
