from moego.models.enterprise.v1 import option_defs_pb2 as _option_defs_pb2
from google.protobuf.internal import containers as _containers
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from collections.abc import Iterable as _Iterable, Mapping as _Mapping
from typing import ClassVar as _ClassVar, Optional as _Optional, Union as _Union

DESCRIPTOR: _descriptor.FileDescriptor

class OptionModel(_message.Message):
    __slots__ = ("calendar_formats", "currencies", "countries", "date_formats", "number_formats", "time_formats", "units_of_weight", "units_of_distance")
    CALENDAR_FORMATS_FIELD_NUMBER: _ClassVar[int]
    CURRENCIES_FIELD_NUMBER: _ClassVar[int]
    COUNTRIES_FIELD_NUMBER: _ClassVar[int]
    DATE_FORMATS_FIELD_NUMBER: _ClassVar[int]
    NUMBER_FORMATS_FIELD_NUMBER: _ClassVar[int]
    TIME_FORMATS_FIELD_NUMBER: _ClassVar[int]
    UNITS_OF_WEIGHT_FIELD_NUMBER: _ClassVar[int]
    UNITS_OF_DISTANCE_FIELD_NUMBER: _ClassVar[int]
    calendar_formats: _containers.RepeatedCompositeFieldContainer[_option_defs_pb2.LabelTypeDef]
    currencies: _containers.RepeatedCompositeFieldContainer[_option_defs_pb2.CurrencyDef]
    countries: _containers.RepeatedScalarFieldContainer[str]
    date_formats: _containers.RepeatedCompositeFieldContainer[_option_defs_pb2.LabelTypeDef]
    number_formats: _containers.RepeatedCompositeFieldContainer[_option_defs_pb2.LabelTypeDef]
    time_formats: _containers.RepeatedCompositeFieldContainer[_option_defs_pb2.LabelTypeDef]
    units_of_weight: _containers.RepeatedCompositeFieldContainer[_option_defs_pb2.LabelTypeDef]
    units_of_distance: _containers.RepeatedCompositeFieldContainer[_option_defs_pb2.LabelTypeDef]
    def __init__(self, calendar_formats: _Optional[_Iterable[_Union[_option_defs_pb2.LabelTypeDef, _Mapping]]] = ..., currencies: _Optional[_Iterable[_Union[_option_defs_pb2.CurrencyDef, _Mapping]]] = ..., countries: _Optional[_Iterable[str]] = ..., date_formats: _Optional[_Iterable[_Union[_option_defs_pb2.LabelTypeDef, _Mapping]]] = ..., number_formats: _Optional[_Iterable[_Union[_option_defs_pb2.LabelTypeDef, _Mapping]]] = ..., time_formats: _Optional[_Iterable[_Union[_option_defs_pb2.LabelTypeDef, _Mapping]]] = ..., units_of_weight: _Optional[_Iterable[_Union[_option_defs_pb2.LabelTypeDef, _Mapping]]] = ..., units_of_distance: _Optional[_Iterable[_Union[_option_defs_pb2.LabelTypeDef, _Mapping]]] = ...) -> None: ...
