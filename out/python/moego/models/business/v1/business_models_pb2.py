# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: moego/models/business/v1/business_models.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'moego/models/business/v1/business_models.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.type import latlng_pb2 as google_dot_type_dot_latlng__pb2
from moego.models.business.v1 import business_enums_pb2 as moego_dot_models_dot_business_dot_v1_dot_business__enums__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n.moego/models/business/v1/business_models.proto\x12\x18moego.models.business.v1\x1a\x18google/type/latlng.proto\x1a-moego/models/business/v1/business_enums.proto\"\xcf\t\n\rBusinessModel\x12\x0e\n\x02id\x18\x01 \x01(\x03R\x02id\x12#\n\rbusiness_name\x18\x02 \x01(\tR\x0c\x62usinessName\x12\x1f\n\x0b\x61vatar_path\x18\x03 \x01(\tR\navatarPath\x12!\n\x0cphone_number\x18\x04 \x01(\tR\x0bphoneNumber\x12\x1f\n\x0bowner_email\x18\x05 \x01(\tR\nownerEmail\x12#\n\rcurrency_code\x18\x14 \x01(\tR\x0c\x63urrencyCode\x12\'\n\x0f\x63urrency_symbol\x18\x15 \x01(\tR\x0e\x63urrencySymbol\x12\'\n\x0f\x63\x61lendar_format\x18\x16 \x01(\tR\x0e\x63\x61lendarFormat\x12\x30\n\x14\x63\x61lendar_format_type\x18\x17 \x01(\x05R\x12\x63\x61lendarFormatType\x12\x1f\n\x0b\x64\x61te_format\x18\x18 \x01(\tR\ndateFormat\x12(\n\x10\x64\x61te_format_type\x18\x19 \x01(\x05R\x0e\x64\x61teFormatType\x12\x1f\n\x0btime_format\x18\x1a \x01(\tR\ntimeFormat\x12(\n\x10time_format_type\x18\x1b \x01(\x05R\x0etimeFormatType\x12#\n\rtimezone_name\x18\x1c \x01(\tR\x0ctimezoneName\x12#\n\rnumber_format\x18\x1d \x01(\tR\x0cnumberFormat\x12,\n\x12number_format_type\x18\x1e \x01(\x05R\x10numberFormatType\x12$\n\x0eunit_of_weight\x18\x1f \x01(\tR\x0cunitOfWeight\x12-\n\x13unit_of_weight_type\x18  \x01(\x05R\x10unitOfWeightType\x12#\n\rbusiness_mode\x18\x32 \x01(\x05R\x0c\x62usinessMode\x12\x18\n\x07\x61\x64\x64ress\x18\x33 \x01(\tR\x07\x61\x64\x64ress\x12\x1d\n\ncompany_id\x18\x34 \x01(\x03R\tcompanyId\x12\x44\n\x08\x61pp_type\x18\x35 \x01(\x0e\x32).moego.models.business.v1.BusinessAppTypeR\x07\x61ppType\x12\x1a\n\x08\x61\x64\x64ress1\x18\x36 \x01(\tR\x08\x61\x64\x64ress1\x12\x1a\n\x08\x61\x64\x64ress2\x18\x37 \x01(\tR\x08\x61\x64\x64ress2\x12!\n\x0c\x61\x64\x64ress_city\x18\x38 \x01(\tR\x0b\x61\x64\x64ressCity\x12#\n\raddress_state\x18\x39 \x01(\tR\x0c\x61\x64\x64ressState\x12\'\n\x0f\x61\x64\x64ress_zipcode\x18: \x01(\tR\x0e\x61\x64\x64ressZipcode\x12\'\n\x0f\x61\x64\x64ress_country\x18; \x01(\tR\x0e\x61\x64\x64ressCountry\x12\x33\n\ncoordinate\x18< \x01(\x0b\x32\x13.google.type.LatLngR\ncoordinate\x12\x31\n\x15unit_of_distance_type\x18= \x01(\x05R\x12unitOfDistanceType\x12.\n\x13\x63ountry_alpha2_code\x18> \x01(\tR\x11\x63ountryAlpha2CodeJ\x04\x08\x06\x10\x14J\x04\x08!\x10\x32\"t\n\x16\x42usinessModelBasicView\x12\x0e\n\x02id\x18\x01 \x01(\x03R\x02id\x12#\n\rbusiness_name\x18\x02 \x01(\tR\x0c\x62usinessName\x12\x1f\n\x0b\x61vatar_path\x18\x03 \x01(\tR\navatarPathJ\x04\x08\x04\x10\x14\"\x87\x05\n\x17\x42usinessModelPublicView\x12\x0e\n\x02id\x18\x01 \x01(\x03R\x02id\x12#\n\rbusiness_name\x18\x02 \x01(\tR\x0c\x62usinessName\x12\x1f\n\x0b\x61vatar_path\x18\x03 \x01(\tR\navatarPath\x12#\n\rcurrency_code\x18\x14 \x01(\tR\x0c\x63urrencyCode\x12\'\n\x0f\x63urrency_symbol\x18\x15 \x01(\tR\x0e\x63urrencySymbol\x12\'\n\x0f\x63\x61lendar_format\x18\x16 \x01(\tR\x0e\x63\x61lendarFormat\x12\x30\n\x14\x63\x61lendar_format_type\x18\x17 \x01(\x05R\x12\x63\x61lendarFormatType\x12\x1f\n\x0b\x64\x61te_format\x18\x18 \x01(\tR\ndateFormat\x12(\n\x10\x64\x61te_format_type\x18\x19 \x01(\x05R\x0e\x64\x61teFormatType\x12\x1f\n\x0btime_format\x18\x1a \x01(\tR\ntimeFormat\x12(\n\x10time_format_type\x18\x1b \x01(\x05R\x0etimeFormatType\x12#\n\rtimezone_name\x18\x1c \x01(\tR\x0ctimezoneName\x12#\n\rnumber_format\x18\x1d \x01(\tR\x0cnumberFormat\x12,\n\x12number_format_type\x18\x1e \x01(\x05R\x10numberFormatType\x12$\n\x0eunit_of_weight\x18\x1f \x01(\tR\x0cunitOfWeight\x12-\n\x13unit_of_weight_type\x18  \x01(\x05R\x10unitOfWeightTypeJ\x04\x08\x04\x10\x14J\x04\x08!\x10\x32\"\xb9\x01\n\x1b\x42usinessModelClientListView\x12\x0e\n\x02id\x18\x01 \x01(\x03R\x02id\x12#\n\rbusiness_name\x18\x02 \x01(\tR\x0c\x62usinessName\x12\x1f\n\x0b\x61vatar_path\x18\x03 \x01(\tR\navatarPath\x12\x44\n\x08\x61pp_type\x18\x04 \x01(\x0e\x32).moego.models.business.v1.BusinessAppTypeR\x07\x61ppType\"\xb2\x05\n\x17\x42usinessModelClientView\x12\x0e\n\x02id\x18\x01 \x01(\x03R\x02id\x12#\n\rbusiness_name\x18\x02 \x01(\tR\x0c\x62usinessName\x12\x1f\n\x0b\x61vatar_path\x18\x03 \x01(\tR\navatarPath\x12!\n\x0cphone_number\x18\x04 \x01(\tR\x0bphoneNumber\x12\x1a\n\x08\x61\x64\x64ress1\x18\x05 \x01(\tR\x08\x61\x64\x64ress1\x12\x1a\n\x08\x61\x64\x64ress2\x18\x06 \x01(\tR\x08\x61\x64\x64ress2\x12!\n\x0c\x61\x64\x64ress_city\x18\x07 \x01(\tR\x0b\x61\x64\x64ressCity\x12#\n\raddress_state\x18\x08 \x01(\tR\x0c\x61\x64\x64ressState\x12\'\n\x0f\x61\x64\x64ress_zipcode\x18\t \x01(\tR\x0e\x61\x64\x64ressZipcode\x12\'\n\x0f\x61\x64\x64ress_country\x18\n \x01(\tR\x0e\x61\x64\x64ressCountry\x12\x44\n\x08\x61pp_type\x18\x0b \x01(\x0e\x32).moego.models.business.v1.BusinessAppTypeR\x07\x61ppType\x12S\n\x10primary_pay_type\x18\x0c \x01(\x0e\x32).moego.models.business.v1.BusinessPayTypeR\x0eprimaryPayType\x12\x33\n\ncoordinate\x18\r \x01(\x0b\x32\x13.google.type.LatLngR\ncoordinate\x12#\n\rcurrency_code\x18\x0e \x01(\tR\x0c\x63urrencyCode\x12\'\n\x0f\x63urrency_symbol\x18\x0f \x01(\tR\x0e\x63urrencySymbol\x12.\n\x13\x63ountry_alpha2_code\x18\x10 \x01(\tR\x11\x63ountryAlpha2CodeB~\n com.moego.idl.models.business.v1P\x01ZXgithub.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/business/v1;businesspbb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'moego.models.business.v1.business_models_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n com.moego.idl.models.business.v1P\001ZXgithub.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/business/v1;businesspb'
  _globals['_BUSINESSMODEL']._serialized_start=150
  _globals['_BUSINESSMODEL']._serialized_end=1381
  _globals['_BUSINESSMODELBASICVIEW']._serialized_start=1383
  _globals['_BUSINESSMODELBASICVIEW']._serialized_end=1499
  _globals['_BUSINESSMODELPUBLICVIEW']._serialized_start=1502
  _globals['_BUSINESSMODELPUBLICVIEW']._serialized_end=2149
  _globals['_BUSINESSMODELCLIENTLISTVIEW']._serialized_start=2152
  _globals['_BUSINESSMODELCLIENTLISTVIEW']._serialized_end=2337
  _globals['_BUSINESSMODELCLIENTVIEW']._serialized_start=2340
  _globals['_BUSINESSMODELCLIENTVIEW']._serialized_end=3030
# @@protoc_insertion_point(module_scope)
