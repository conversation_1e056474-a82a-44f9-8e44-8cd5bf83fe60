# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: moego/models/business_customer/v1/business_customer_referral_source_defs.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'moego/models/business_customer/v1/business_customer_referral_source_defs.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from validate import validate_pb2 as validate_dot_validate__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\nNmoego/models/business_customer/v1/business_customer_referral_source_defs.proto\x12!moego.models.business_customer.v1\x1a\x17validate/validate.proto\"R\n\'BusinessCustomerReferralSourceCreateDef\x12\'\n\x04name\x18\x03 \x01(\tB\x13\xfa\x42\x10r\x0e\x10\x01\x18\xff\x01\x32\x07.*\\S+.*R\x04name\"`\n\'BusinessCustomerReferralSourceUpdateDef\x12,\n\x04name\x18\x04 \x01(\tB\x13\xfa\x42\x10r\x0e\x10\x01\x18\xff\x01\x32\x07.*\\S+.*H\x00R\x04name\x88\x01\x01\x42\x07\n\x05_nameB\x98\x01\n)com.moego.idl.models.business_customer.v1P\x01Zigithub.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/business_customer/v1;businesscustomerpbb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'moego.models.business_customer.v1.business_customer_referral_source_defs_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n)com.moego.idl.models.business_customer.v1P\001Zigithub.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/business_customer/v1;businesscustomerpb'
  _globals['_BUSINESSCUSTOMERREFERRALSOURCECREATEDEF'].fields_by_name['name']._loaded_options = None
  _globals['_BUSINESSCUSTOMERREFERRALSOURCECREATEDEF'].fields_by_name['name']._serialized_options = b'\372B\020r\016\020\001\030\377\0012\007.*\\S+.*'
  _globals['_BUSINESSCUSTOMERREFERRALSOURCEUPDATEDEF'].fields_by_name['name']._loaded_options = None
  _globals['_BUSINESSCUSTOMERREFERRALSOURCEUPDATEDEF'].fields_by_name['name']._serialized_options = b'\372B\020r\016\020\001\030\377\0012\007.*\\S+.*'
  _globals['_BUSINESSCUSTOMERREFERRALSOURCECREATEDEF']._serialized_start=142
  _globals['_BUSINESSCUSTOMERREFERRALSOURCECREATEDEF']._serialized_end=224
  _globals['_BUSINESSCUSTOMERREFERRALSOURCEUPDATEDEF']._serialized_start=226
  _globals['_BUSINESSCUSTOMERREFERRALSOURCEUPDATEDEF']._serialized_end=322
# @@protoc_insertion_point(module_scope)
