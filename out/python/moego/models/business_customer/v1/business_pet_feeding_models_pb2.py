# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: moego/models/business_customer/v1/business_pet_feeding_models.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'moego/models/business_customer/v1/business_pet_feeding_models.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\nCmoego/models/business_customer/v1/business_pet_feeding_models.proto\x12!moego.models.business_customer.v1\x1a\x1fgoogle/protobuf/timestamp.proto\"\xf8\x03\n\x17\x42usinessPetFeedingModel\x12\x0e\n\x02id\x18\x01 \x01(\x03R\x02id\x12\x1d\n\ncompany_id\x18\x02 \x01(\x03R\tcompanyId\x12\x15\n\x06pet_id\x18\x03 \x01(\x03R\x05petId\x12%\n\x0e\x66\x65\x65\x64ing_amount\x18\x04 \x01(\tR\rfeedingAmount\x12!\n\x0c\x66\x65\x65\x64ing_unit\x18\x05 \x01(\tR\x0b\x66\x65\x65\x64ingUnit\x12!\n\x0c\x66\x65\x65\x64ing_type\x18\x06 \x01(\tR\x0b\x66\x65\x65\x64ingType\x12%\n\x0e\x66\x65\x65\x64ing_source\x18\x07 \x01(\tR\rfeedingSource\x12/\n\x13\x66\x65\x65\x64ing_instruction\x18\x08 \x01(\tR\x12\x66\x65\x65\x64ingInstruction\x12\x39\n\ncreated_at\x18\t \x01(\x0b\x32\x1a.google.protobuf.TimestampR\tcreatedAt\x12\x39\n\nupdated_at\x18\n \x01(\x0b\x32\x1a.google.protobuf.TimestampR\tupdatedAt\x12\x39\n\ndeleted_at\x18\x0b \x01(\x0b\x32\x1a.google.protobuf.TimestampR\tdeletedAt\x12!\n\x0c\x66\x65\x65\x64ing_note\x18\x0c \x01(\tR\x0b\x66\x65\x65\x64ingNoteB\x98\x01\n)com.moego.idl.models.business_customer.v1P\x01Zigithub.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/business_customer/v1;businesscustomerpbb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'moego.models.business_customer.v1.business_pet_feeding_models_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n)com.moego.idl.models.business_customer.v1P\001Zigithub.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/business_customer/v1;businesscustomerpb'
  _globals['_BUSINESSPETFEEDINGMODEL']._serialized_start=140
  _globals['_BUSINESSPETFEEDINGMODEL']._serialized_end=644
# @@protoc_insertion_point(module_scope)
