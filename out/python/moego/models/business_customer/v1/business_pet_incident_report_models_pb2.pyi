from google.protobuf import timestamp_pb2 as _timestamp_pb2
from google.protobuf.internal import containers as _containers
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from collections.abc import Iterable as _Iterable, Mapping as _Mapping
from typing import ClassVar as _ClassVar, Optional as _Optional, Union as _Union

DESCRIPTOR: _descriptor.FileDescriptor

class BusinessPetIncidentReportModel(_message.Message):
    __slots__ = ("id", "pet_ids", "incident_time", "incident_type_id", "description", "attachment_files", "business_id", "is_staff_injured", "is_pet_injured", "is_vet_visited")
    ID_FIELD_NUMBER: _ClassVar[int]
    PET_IDS_FIELD_NUMBER: _ClassVar[int]
    INCIDENT_TIME_FIELD_NUMBER: _ClassVar[int]
    INCIDENT_TYPE_ID_FIELD_NUMBER: _ClassVar[int]
    DESCRIPTION_FIELD_NUMBER: _ClassVar[int]
    ATTACHMENT_FILES_FIELD_NUMBER: _ClassVar[int]
    BUSINESS_ID_FIELD_NUMBER: _ClassVar[int]
    IS_STAFF_INJURED_FIELD_NUMBER: _ClassVar[int]
    IS_PET_INJURED_FIELD_NUMBER: _ClassVar[int]
    IS_VET_VISITED_FIELD_NUMBER: _ClassVar[int]
    id: int
    pet_ids: _containers.RepeatedScalarFieldContainer[int]
    incident_time: _timestamp_pb2.Timestamp
    incident_type_id: int
    description: str
    attachment_files: _containers.RepeatedCompositeFieldContainer[PetIncidentAttachment]
    business_id: int
    is_staff_injured: bool
    is_pet_injured: bool
    is_vet_visited: bool
    def __init__(self, id: _Optional[int] = ..., pet_ids: _Optional[_Iterable[int]] = ..., incident_time: _Optional[_Union[datetime.datetime, _timestamp_pb2.Timestamp, _Mapping]] = ..., incident_type_id: _Optional[int] = ..., description: _Optional[str] = ..., attachment_files: _Optional[_Iterable[_Union[PetIncidentAttachment, _Mapping]]] = ..., business_id: _Optional[int] = ..., is_staff_injured: bool = ..., is_pet_injured: bool = ..., is_vet_visited: bool = ...) -> None: ...

class PetIncidentAttachment(_message.Message):
    __slots__ = ("url", "name")
    URL_FIELD_NUMBER: _ClassVar[int]
    NAME_FIELD_NUMBER: _ClassVar[int]
    url: str
    name: str
    def __init__(self, url: _Optional[str] = ..., name: _Optional[str] = ...) -> None: ...
