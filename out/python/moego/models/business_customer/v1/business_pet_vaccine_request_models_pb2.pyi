from google.protobuf import timestamp_pb2 as _timestamp_pb2
from google.type import date_pb2 as _date_pb2
from google.protobuf.internal import containers as _containers
from google.protobuf.internal import enum_type_wrapper as _enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from collections.abc import Iterable as _Iterable, Mapping as _Mapping
from typing import ClassVar as _ClassVar, Optional as _Optional, Union as _Union

DESCRIPTOR: _descriptor.FileDescriptor

class BusinessPetVaccineRequestModel(_message.Message):
    __slots__ = ("id", "vaccine_record_id", "pet_id", "vaccine_id", "expiration_date", "document_urls", "status", "create_time")
    class Status(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
        __slots__ = ()
        STATUS_UNSPECIFIED: _ClassVar[BusinessPetVaccineRequestModel.Status]
        PENDING: _ClassVar[BusinessPetVaccineRequestModel.Status]
        APPROVED: _ClassVar[BusinessPetVaccineRequestModel.Status]
        DECLINED: _ClassVar[BusinessPetVaccineRequestModel.Status]
    STATUS_UNSPECIFIED: BusinessPetVaccineRequestModel.Status
    PENDING: BusinessPetVaccineRequestModel.Status
    APPROVED: BusinessPetVaccineRequestModel.Status
    DECLINED: BusinessPetVaccineRequestModel.Status
    ID_FIELD_NUMBER: _ClassVar[int]
    VACCINE_RECORD_ID_FIELD_NUMBER: _ClassVar[int]
    PET_ID_FIELD_NUMBER: _ClassVar[int]
    VACCINE_ID_FIELD_NUMBER: _ClassVar[int]
    EXPIRATION_DATE_FIELD_NUMBER: _ClassVar[int]
    DOCUMENT_URLS_FIELD_NUMBER: _ClassVar[int]
    STATUS_FIELD_NUMBER: _ClassVar[int]
    CREATE_TIME_FIELD_NUMBER: _ClassVar[int]
    id: int
    vaccine_record_id: int
    pet_id: int
    vaccine_id: int
    expiration_date: _date_pb2.Date
    document_urls: _containers.RepeatedScalarFieldContainer[str]
    status: BusinessPetVaccineRequestModel.Status
    create_time: _timestamp_pb2.Timestamp
    def __init__(self, id: _Optional[int] = ..., vaccine_record_id: _Optional[int] = ..., pet_id: _Optional[int] = ..., vaccine_id: _Optional[int] = ..., expiration_date: _Optional[_Union[_date_pb2.Date, _Mapping]] = ..., document_urls: _Optional[_Iterable[str]] = ..., status: _Optional[_Union[BusinessPetVaccineRequestModel.Status, str]] = ..., create_time: _Optional[_Union[datetime.datetime, _timestamp_pb2.Timestamp, _Mapping]] = ...) -> None: ...

class BusinessPetVaccineRequestBindingModel(_message.Message):
    __slots__ = ("vaccine_record_id", "requests")
    VACCINE_RECORD_ID_FIELD_NUMBER: _ClassVar[int]
    REQUESTS_FIELD_NUMBER: _ClassVar[int]
    vaccine_record_id: int
    requests: _containers.RepeatedCompositeFieldContainer[BusinessPetVaccineRequestModel]
    def __init__(self, vaccine_record_id: _Optional[int] = ..., requests: _Optional[_Iterable[_Union[BusinessPetVaccineRequestModel, _Mapping]]] = ...) -> None: ...
