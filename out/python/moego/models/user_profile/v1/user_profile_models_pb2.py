# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: moego/models/user_profile/v1/user_profile_models.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'moego/models/user_profile/v1/user_profile_models.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.protobuf import struct_pb2 as google_dot_protobuf_dot_struct__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n6moego/models/user_profile/v1/user_profile_models.proto\x12\x1cmoego.models.user_profile.v1\x1a\x1cgoogle/protobuf/struct.proto\"\xaa\x01\n\x04User\x12\x0e\n\x02id\x18\x01 \x01(\x03R\x02id\x12;\n\x04type\x18\x02 \x01(\x0e\x32\'.moego.models.user_profile.v1.User.TypeR\x04type\"U\n\x04Type\x12\x14\n\x10TYPE_UNSPECIFIED\x10\x00\x12\x0c\n\x08\x42USINESS\x10\x01\x12\x0b\n\x07\x43OMPANY\x10\x02\x12\x0e\n\nENTERPRISE\x10\x03\x12\x0c\n\x08\x43USTOMER\x10\x04\"r\n\x0bUserProfile\x12\x36\n\x04user\x18\x01 \x01(\x0b\x32\".moego.models.user_profile.v1.UserR\x04user\x12+\n\x04tags\x18\x02 \x01(\x0b\x32\x17.google.protobuf.StructR\x04tagsB\x89\x01\n$com.moego.idl.models.user_profile.v1P\x01Z_github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/user_profile/v1;userprofilepbb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'moego.models.user_profile.v1.user_profile_models_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n$com.moego.idl.models.user_profile.v1P\001Z_github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/user_profile/v1;userprofilepb'
  _globals['_USER']._serialized_start=119
  _globals['_USER']._serialized_end=289
  _globals['_USER_TYPE']._serialized_start=204
  _globals['_USER_TYPE']._serialized_end=289
  _globals['_USERPROFILE']._serialized_start=291
  _globals['_USERPROFILE']._serialized_end=405
# @@protoc_insertion_point(module_scope)
