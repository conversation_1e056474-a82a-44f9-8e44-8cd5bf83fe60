from google.protobuf import timestamp_pb2 as _timestamp_pb2
from moego.models.offering.v1 import pricing_rule_defs_pb2 as _pricing_rule_defs_pb2
from moego.models.offering.v1 import pricing_rule_enums_pb2 as _pricing_rule_enums_pb2
from moego.models.offering.v1 import service_enum_pb2 as _service_enum_pb2
from google.protobuf.internal import containers as _containers
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from collections.abc import Iterable as _Iterable, Mapping as _Mapping
from typing import ClassVar as _ClassVar, Optional as _Optional, Union as _Union

DESCRIPTOR: _descriptor.FileDescriptor

class PricingRuleModel(_message.Message):
    __slots__ = ("id", "company_id", "service_item_type", "service_type", "rule_name", "is_all_service_applicable", "selected_services", "rule_configuration", "is_active", "updated_by", "created_at", "updated_at", "deleted_at", "rule_group_type", "rule_apply_choice_type")
    ID_FIELD_NUMBER: _ClassVar[int]
    COMPANY_ID_FIELD_NUMBER: _ClassVar[int]
    SERVICE_ITEM_TYPE_FIELD_NUMBER: _ClassVar[int]
    SERVICE_TYPE_FIELD_NUMBER: _ClassVar[int]
    RULE_NAME_FIELD_NUMBER: _ClassVar[int]
    IS_ALL_SERVICE_APPLICABLE_FIELD_NUMBER: _ClassVar[int]
    SELECTED_SERVICES_FIELD_NUMBER: _ClassVar[int]
    RULE_CONFIGURATION_FIELD_NUMBER: _ClassVar[int]
    IS_ACTIVE_FIELD_NUMBER: _ClassVar[int]
    UPDATED_BY_FIELD_NUMBER: _ClassVar[int]
    CREATED_AT_FIELD_NUMBER: _ClassVar[int]
    UPDATED_AT_FIELD_NUMBER: _ClassVar[int]
    DELETED_AT_FIELD_NUMBER: _ClassVar[int]
    RULE_GROUP_TYPE_FIELD_NUMBER: _ClassVar[int]
    RULE_APPLY_CHOICE_TYPE_FIELD_NUMBER: _ClassVar[int]
    id: int
    company_id: int
    service_item_type: _service_enum_pb2.ServiceItemType
    service_type: _service_enum_pb2.ServiceType
    rule_name: str
    is_all_service_applicable: bool
    selected_services: _containers.RepeatedScalarFieldContainer[int]
    rule_configuration: _pricing_rule_defs_pb2.PricingRuleConfigurationDef
    is_active: bool
    updated_by: int
    created_at: _timestamp_pb2.Timestamp
    updated_at: _timestamp_pb2.Timestamp
    deleted_at: _timestamp_pb2.Timestamp
    rule_group_type: _pricing_rule_enums_pb2.RuleGroupType
    rule_apply_choice_type: _pricing_rule_enums_pb2.RuleApplyChoiceType
    def __init__(self, id: _Optional[int] = ..., company_id: _Optional[int] = ..., service_item_type: _Optional[_Union[_service_enum_pb2.ServiceItemType, str]] = ..., service_type: _Optional[_Union[_service_enum_pb2.ServiceType, str]] = ..., rule_name: _Optional[str] = ..., is_all_service_applicable: bool = ..., selected_services: _Optional[_Iterable[int]] = ..., rule_configuration: _Optional[_Union[_pricing_rule_defs_pb2.PricingRuleConfigurationDef, _Mapping]] = ..., is_active: bool = ..., updated_by: _Optional[int] = ..., created_at: _Optional[_Union[datetime.datetime, _timestamp_pb2.Timestamp, _Mapping]] = ..., updated_at: _Optional[_Union[datetime.datetime, _timestamp_pb2.Timestamp, _Mapping]] = ..., deleted_at: _Optional[_Union[datetime.datetime, _timestamp_pb2.Timestamp, _Mapping]] = ..., rule_group_type: _Optional[_Union[_pricing_rule_enums_pb2.RuleGroupType, str]] = ..., rule_apply_choice_type: _Optional[_Union[_pricing_rule_enums_pb2.RuleApplyChoiceType, str]] = ...) -> None: ...
