from validate import validate_pb2 as _validate_pb2
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from typing import ClassVar as _ClassVar, Optional as _Optional

DESCRIPTOR: _descriptor.FileDescriptor

class CreatePlaygroupDef(_message.Message):
    __slots__ = ("name", "color_code", "max_pet_capacity", "description")
    NAME_FIELD_NUMBER: _ClassVar[int]
    COLOR_CODE_FIELD_NUMBER: _ClassVar[int]
    MAX_PET_CAPACITY_FIELD_NUMBER: _ClassVar[int]
    DESCRIPTION_FIELD_NUMBER: _ClassVar[int]
    name: str
    color_code: str
    max_pet_capacity: int
    description: str
    def __init__(self, name: _Optional[str] = ..., color_code: _Optional[str] = ..., max_pet_capacity: _Optional[int] = ..., description: _Optional[str] = ...) -> None: ...

class UpdatePlaygroupDef(_message.Message):
    __slots__ = ("id", "name", "color_code", "max_pet_capacity", "description")
    ID_FIELD_NUMBER: _ClassVar[int]
    NAME_FIELD_NUMBER: _ClassVar[int]
    COLOR_CODE_FIELD_NUMBER: _ClassVar[int]
    MAX_PET_CAPACITY_FIELD_NUMBER: _ClassVar[int]
    DESCRIPTION_FIELD_NUMBER: _ClassVar[int]
    id: int
    name: str
    color_code: str
    max_pet_capacity: int
    description: str
    def __init__(self, id: _Optional[int] = ..., name: _Optional[str] = ..., color_code: _Optional[str] = ..., max_pet_capacity: _Optional[int] = ..., description: _Optional[str] = ...) -> None: ...
