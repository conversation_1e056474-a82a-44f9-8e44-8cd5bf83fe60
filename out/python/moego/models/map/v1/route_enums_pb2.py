# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: moego/models/map/v1/route_enums.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'moego/models/map/v1/route_enums.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n%moego/models/map/v1/route_enums.proto\x12\x13moego.models.map.v1*p\n\nTravelMode\x12\x1b\n\x17TRAVEL_MODE_UNSPECIFIED\x10\x00\x12\x0b\n\x07\x44RIVING\x10\x01\x12\x0b\n\x07WALKING\x10\x02\x12\r\n\tBICYCLING\x10\x03\x12\x0f\n\x0bTWO_WHEELER\x10\x04\x12\x0b\n\x07TRANSIT\x10\x05*^\n\x0cTrafficModel\x12\x1d\n\x19TRAFFIC_MODEL_UNSPECIFIED\x10\x00\x12\x0e\n\nBEST_GUESS\x10\x01\x12\x0f\n\x0bPESSIMISTIC\x10\x02\x12\x0e\n\nOPTIMISTIC\x10\x03*_\n\nRouteLabel\x12\x1b\n\x17ROUTE_LABEL_UNSPECIFIED\x10\x00\x12\x11\n\rDEFAULT_ROUTE\x10\x01\x12\r\n\tALTERNATE\x10\x02\x12\x12\n\x0e\x46UEL_EFFICIENT\x10\x03*v\n\x0fRoutePreference\x12 \n\x1cROUTE_PREFERENCE_UNSPECIFIED\x10\x00\x12\x13\n\x0fTRAFFIC_UNAWARE\x10\x01\x12\x11\n\rTRAFFIC_AWARE\x10\x02\x12\x19\n\x15TRAFFIC_AWARE_OPTIMAL\x10\x03*\x8c\x01\n\x10\x45xtraComputation\x12!\n\x1d\x45XTRA_COMPUTATION_UNSPECIFIED\x10\x00\x12\t\n\x05TOLLS\x10\x01\x12\x14\n\x10\x46UEL_CONSUMPTION\x10\x02\x12\x17\n\x13TRAFFIC_ON_POLYLINE\x10\x03\x12\x1b\n\x17NAVIGATION_INSTRUCTIONS\x10\x04*h\n\x11TransitTravelMode\x12#\n\x1fTRANSIT_TRAVEL_MODE_UNSPECIFIED\x10\x00\x12\x07\n\x03\x42US\x10\x01\x12\n\n\x06SUBWAY\x10\x02\x12\t\n\x05TRAIN\x10\x03\x12\x0e\n\nLIGHT_RAIL\x10\x04*i\n\x16TransitRoutePreference\x12(\n$TRANSIT_ROUTE_PREFERENCE_UNSPECIFIED\x10\x00\x12\x10\n\x0cLESS_WALKING\x10\x01\x12\x13\n\x0f\x46\x45WER_TRANSFERS\x10\x02*p\n\x13VehicleEmissionType\x12%\n!VEHICLE_EMISSION_TYPE_UNSPECIFIED\x10\x00\x12\x0c\n\x08GASOLINE\x10\x01\x12\n\n\x06\x44IESEL\x10\x02\x12\x0c\n\x08\x45LECTRIC\x10\x03\x12\n\n\x06HYBRID\x10\x04*\\\n\x10TrafficCondition\x12!\n\x1dTRAFFIC_CONDITION_UNSPECIFIED\x10\x00\x12\n\n\x06NORMAL\x10\x01\x12\x08\n\x04SLOW\x10\x02\x12\x0f\n\x0bTRAFFIC_JAM\x10\x03*S\n\x0fPolylineQuality\x12 \n\x1cPOLYLINE_QUALITY_UNSPECIFIED\x10\x00\x12\x10\n\x0cHIGH_QUALITY\x10\x01\x12\x0c\n\x08OVERVIEW\x10\x02*d\n\x10PolylineEncoding\x12!\n\x1dPOLYLINE_ENCODING_UNSPECIFIED\x10\x00\x12\x14\n\x10\x45NCODED_POLYLINE\x10\x01\x12\x17\n\x13GEO_JSON_LINESTRING\x10\x02*8\n\x05Units\x12\x15\n\x11UNITS_UNSPECIFIED\x10\x00\x12\n\n\x06METRIC\x10\x01\x12\x0c\n\x08IMPERIAL\x10\x02*Y\n\x0e\x46\x61llbackReason\x12\x1f\n\x1b\x46\x41LLBACK_REASON_UNSPECIFIED\x10\x00\x12\x10\n\x0cSERVER_ERROR\x10\x01\x12\x14\n\x10LATENCY_EXCEEDED\x10\x02*v\n\x13\x46\x61llbackRoutingMode\x12%\n!FALLBACK_ROUTING_MODE_UNSPECIFIED\x10\x00\x12\x1c\n\x18\x46\x41LLBACK_TRAFFIC_UNAWARE\x10\x01\x12\x1a\n\x16\x46\x41LLBACK_TRAFFIC_AWARE\x10\x02\x42o\n\x1b\x63om.moego.idl.models.map.v1P\x01ZNgithub.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/map/v1;mappbb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'moego.models.map.v1.route_enums_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n\033com.moego.idl.models.map.v1P\001ZNgithub.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/map/v1;mappb'
  _globals['_TRAVELMODE']._serialized_start=62
  _globals['_TRAVELMODE']._serialized_end=174
  _globals['_TRAFFICMODEL']._serialized_start=176
  _globals['_TRAFFICMODEL']._serialized_end=270
  _globals['_ROUTELABEL']._serialized_start=272
  _globals['_ROUTELABEL']._serialized_end=367
  _globals['_ROUTEPREFERENCE']._serialized_start=369
  _globals['_ROUTEPREFERENCE']._serialized_end=487
  _globals['_EXTRACOMPUTATION']._serialized_start=490
  _globals['_EXTRACOMPUTATION']._serialized_end=630
  _globals['_TRANSITTRAVELMODE']._serialized_start=632
  _globals['_TRANSITTRAVELMODE']._serialized_end=736
  _globals['_TRANSITROUTEPREFERENCE']._serialized_start=738
  _globals['_TRANSITROUTEPREFERENCE']._serialized_end=843
  _globals['_VEHICLEEMISSIONTYPE']._serialized_start=845
  _globals['_VEHICLEEMISSIONTYPE']._serialized_end=957
  _globals['_TRAFFICCONDITION']._serialized_start=959
  _globals['_TRAFFICCONDITION']._serialized_end=1051
  _globals['_POLYLINEQUALITY']._serialized_start=1053
  _globals['_POLYLINEQUALITY']._serialized_end=1136
  _globals['_POLYLINEENCODING']._serialized_start=1138
  _globals['_POLYLINEENCODING']._serialized_end=1238
  _globals['_UNITS']._serialized_start=1240
  _globals['_UNITS']._serialized_end=1296
  _globals['_FALLBACKREASON']._serialized_start=1298
  _globals['_FALLBACKREASON']._serialized_end=1387
  _globals['_FALLBACKROUTINGMODE']._serialized_start=1389
  _globals['_FALLBACKROUTINGMODE']._serialized_end=1507
# @@protoc_insertion_point(module_scope)
