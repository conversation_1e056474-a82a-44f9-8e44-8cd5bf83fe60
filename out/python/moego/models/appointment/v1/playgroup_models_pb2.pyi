from google.type import date_pb2 as _date_pb2
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from collections.abc import Mapping as _Mapping
from typing import ClassVar as _ClassVar, Optional as _Optional, Union as _Union

DESCRIPTOR: _descriptor.FileDescriptor

class PetPlaygroupModel(_message.Message):
    __slots__ = ("id", "pet_id", "playgroup_id", "appointment_id", "date", "sort")
    ID_FIELD_NUMBER: _ClassVar[int]
    PET_ID_FIELD_NUMBER: _ClassVar[int]
    PLAYGROUP_ID_FIELD_NUMBER: _ClassVar[int]
    APPOINTMENT_ID_FIELD_NUMBER: _ClassVar[int]
    DATE_FIELD_NUMBER: _ClassVar[int]
    SORT_FIELD_NUMBER: _ClassVar[int]
    id: int
    pet_id: int
    playgroup_id: int
    appointment_id: int
    date: _date_pb2.Date
    sort: int
    def __init__(self, id: _Optional[int] = ..., pet_id: _Optional[int] = ..., playgroup_id: _Optional[int] = ..., appointment_id: _Optional[int] = ..., date: _Optional[_Union[_date_pb2.Date, _Mapping]] = ..., sort: _Optional[int] = ...) -> None: ...
