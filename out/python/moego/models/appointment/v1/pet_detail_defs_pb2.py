# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: moego/models/appointment/v1/pet_detail_defs.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'moego/models/appointment/v1/pet_detail_defs.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from moego.models.appointment.v1 import appointment_pet_feeding_schedule_defs_pb2 as moego_dot_models_dot_appointment_dot_v1_dot_appointment__pet__feeding__schedule__defs__pb2
from moego.models.appointment.v1 import appointment_pet_medication_schedule_defs_pb2 as moego_dot_models_dot_appointment_dot_v1_dot_appointment__pet__medication__schedule__defs__pb2
from moego.models.appointment.v1 import boarding_split_lodging_defs_pb2 as moego_dot_models_dot_appointment_dot_v1_dot_boarding__split__lodging__defs__pb2
from moego.models.appointment.v1 import pet_detail_enums_pb2 as moego_dot_models_dot_appointment_dot_v1_dot_pet__detail__enums__pb2
from moego.models.appointment.v1 import service_operation_defs_pb2 as moego_dot_models_dot_appointment_dot_v1_dot_service__operation__defs__pb2
from moego.models.offering.v1 import service_enum_pb2 as moego_dot_models_dot_offering_dot_v1_dot_service__enum__pb2
from validate import validate_pb2 as validate_dot_validate__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n1moego/models/appointment/v1/pet_detail_defs.proto\x12\x1bmoego.models.appointment.v1\x1aGmoego/models/appointment/v1/appointment_pet_feeding_schedule_defs.proto\x1aJmoego/models/appointment/v1/appointment_pet_medication_schedule_defs.proto\x1a=moego/models/appointment/v1/boarding_split_lodging_defs.proto\x1a\x32moego/models/appointment/v1/pet_detail_enums.proto\x1a\x38moego/models/appointment/v1/service_operation_defs.proto\x1a+moego/models/offering/v1/service_enum.proto\x1a\x17validate/validate.proto\"\xcc\x02\n\x0cPetDetailDef\x12\x1e\n\x06pet_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\x05petId\x12\\\n\x08services\x18\x02 \x03(\x0b\x32/.moego.models.appointment.v1.SelectedServiceDefB\x0f\xfa\x42\x0c\x92\x01\t\x10\x64\"\x05\x8a\x01\x02\x10\x01R\x08services\x12W\n\x07\x61\x64\x64_ons\x18\x03 \x03(\x0b\x32-.moego.models.appointment.v1.SelectedAddOnDefB\x0f\xfa\x42\x0c\x92\x01\t\x10\x64\"\x05\x8a\x01\x02\x10\x01R\x06\x61\x64\x64Ons\x12\x65\n\x0b\x65valuations\x18\x04 \x03(\x0b\x32\x32.moego.models.appointment.v1.SelectedEvaluationDefB\x0f\xfa\x42\x0c\x92\x01\t\x10\x64\"\x05\x8a\x01\x02\x10\x01R\x0b\x65valuations\"\xc8\x0e\n\x12SelectedServiceDef\x12&\n\nservice_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\tserviceId\x12<\n\nstart_date\x18\x02 \x01(\tB\x1d\xfa\x42\x1ar\x18\x32\x13^\\d{4}-\\d{2}-\\d{2}$\xd0\x01\x01R\tstartDate\x12:\n\x08\x65nd_date\x18\x03 \x01(\tB\x1a\xfa\x42\x17r\x15\x32\x13^\\d{4}-\\d{2}-\\d{2}$H\x00R\x07\x65ndDate\x88\x01\x01\x12.\n\nstart_time\x18\x04 \x01(\x05\x42\n\xfa\x42\x07\x1a\x05\x18\xa0\x0b(\x00H\x01R\tstartTime\x88\x01\x01\x12*\n\x08\x65nd_time\x18\x05 \x01(\x05\x42\n\xfa\x42\x07\x1a\x05\x18\xa0\x0b(\x00H\x02R\x07\x65ndTime\x88\x01\x01\x12+\n\nlodging_id\x18\x07 \x01(\x03\x42\x07\xfa\x42\x04\"\x02(\x00H\x03R\tlodgingId\x88\x01\x01\x12\'\n\x08staff_id\x18\x08 \x01(\x03\x42\x07\xfa\x42\x04\"\x02(\x00H\x04R\x07staffId\x88\x01\x01\x12/\n\x0cservice_time\x18\t \x01(\x05\x42\x07\xfa\x42\x04\x1a\x02(\x00H\x05R\x0bserviceTime\x88\x01\x01\x12(\n\rservice_price\x18\n \x01(\x01H\x06R\x0cservicePrice\x88\x01\x01\x12\x65\n\x10scope_type_price\x18\x0b \x01(\x0e\x32*.moego.models.offering.v1.ServiceScopeTypeB\n\xfa\x42\x07\x82\x01\x04\x10\x01 \x00H\x07R\x0escopeTypePrice\x88\x01\x01\x12\x63\n\x0fscope_type_time\x18\x0c \x01(\x0e\x32*.moego.models.offering.v1.ServiceScopeTypeB\n\xfa\x42\x07\x82\x01\x04\x10\x01 \x00H\x08R\rscopeTypeTime\x88\x01\x01\x12.\n\x10\x65nable_operation\x18\r \x01(\x08H\tR\x0f\x65nableOperation\x88\x01\x01\x12Q\n\twork_mode\x18\x0e \x01(\x0e\x32%.moego.models.appointment.v1.WorkModeB\x08\xfa\x42\x05\x82\x01\x02\x10\x01H\nR\x08workMode\x88\x01\x01\x12\x61\n\noperations\x18\x0f \x03(\x0b\x32\x30.moego.models.appointment.v1.ServiceOperationDefB\x0f\xfa\x42\x0c\x92\x01\t\x10\x64\"\x05\x8a\x01\x02\x10\x01R\noperations\x12l\n\x08\x66\x65\x65\x64ings\x18\x10 \x03(\x0b\x32=.moego.models.appointment.v1.AppointmentPetFeedingScheduleDefB\x11\xfa\x42\x0e\x92\x01\x0b\x08\x00\x10\x64\"\x05\x8a\x01\x02\x10\x01R\x08\x66\x65\x65\x64ings\x12u\n\x0bmedications\x18\x11 \x03(\x0b\<EMAIL>\x11\xfa\x42\x0e\x92\x01\x0b\x08\x00\x10\x64\"\x05\x8a\x01\x02\x10\x01R\x0bmedications\x12\\\n\tdate_type\x18\x12 \x01(\x0e\x32..moego.models.appointment.v1.PetDetailDateTypeB\n\xfa\x42\x07\x82\x01\x04\x10\x01 \x00H\x0bR\x08\x64\x61teType\x88\x01\x01\x12H\n\x0especific_dates\x18\x13 \x03(\tB!\xfa\x42\x1e\x92\x01\x1b\x10\x64\"\x17r\x15\x32\x13^\\d{4}-\\d{2}-\\d{2}$R\rspecificDates\x12l\n\x13price_override_type\x18\x15 \x01(\x0e\x32-.moego.models.offering.v1.ServiceOverrideTypeB\x08\xfa\x42\x05\x82\x01\x02\x10\x01H\x0cR\x11priceOverrideType\x88\x01\x01\x12r\n\x16\x64uration_override_type\x18\x16 \x01(\x0e\x32-.moego.models.offering.v1.ServiceOverrideTypeB\x08\xfa\x42\x05\x82\x01\x02\x10\x01H\rR\x14\x64urationOverrideType\x88\x01\x01\x12m\n\x0esplit_lodgings\x18\x17 \x03(\x0b\x32<.moego.models.appointment.v1.BoardingSplitLodgingScheduleDefB\x08\xfa\x42\x05\x92\x01\x02\x10\x64R\rsplitLodgingsB\x0b\n\t_end_dateB\r\n\x0b_start_timeB\x0b\n\t_end_timeB\r\n\x0b_lodging_idB\x0b\n\t_staff_idB\x0f\n\r_service_timeB\x10\n\x0e_service_priceB\x13\n\x11_scope_type_priceB\x12\n\x10_scope_type_timeB\x13\n\x11_enable_operationB\x0c\n\n_work_modeB\x0c\n\n_date_typeB\x16\n\x14_price_override_typeB\x19\n\x17_duration_override_type\"\xdc\x0c\n\x10SelectedAddOnDef\x12#\n\tadd_on_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\x07\x61\x64\x64OnId\x12P\n\tdate_type\x18\x02 \x01(\x0e\x32*.moego.models.appointment.v1.AddOnDateTypeB\x02\x18\x01H\x00R\x08\x64\x61teType\x88\x01\x01\x12>\n\nstart_date\x18\x03 \x01(\tB\x1a\xfa\x42\x17r\x15\x32\x13^\\d{4}-\\d{2}-\\d{2}$H\x01R\tstartDate\x88\x01\x01\x12H\n\x0especific_dates\x18\x04 \x03(\tB!\xfa\x42\x1e\x92\x01\x1b\x10\x64\"\x17r\x15\x32\x13^\\d{4}-\\d{2}-\\d{2}$R\rspecificDates\x12.\n\nstart_time\x18\x05 \x01(\x05\x42\n\xfa\x42\x07\x1a\x05\x18\xa0\x0b(\x00H\x02R\tstartTime\x88\x01\x01\x12\'\n\x08staff_id\x18\x06 \x01(\x03\x42\x07\xfa\x42\x04\"\x02(\x00H\x03R\x07staffId\x88\x01\x01\x12/\n\x0cservice_time\x18\x07 \x01(\x05\x42\x07\xfa\x42\x04\x1a\x02(\x00H\x04R\x0bserviceTime\x88\x01\x01\x12(\n\rservice_price\x18\x08 \x01(\x01H\x05R\x0cservicePrice\x88\x01\x01\x12\x65\n\x10scope_type_price\x18\t \x01(\x0e\x32*.moego.models.offering.v1.ServiceScopeTypeB\n\xfa\x42\x07\x82\x01\x04\x10\x01 \x00H\x06R\x0escopeTypePrice\x88\x01\x01\x12\x63\n\x0fscope_type_time\x18\n \x01(\x0e\x32*.moego.models.offering.v1.ServiceScopeTypeB\n\xfa\x42\x07\x82\x01\x04\x10\x01 \x00H\x07R\rscopeTypeTime\x88\x01\x01\x12.\n\x10\x65nable_operation\x18\x0b \x01(\x08H\x08R\x0f\x65nableOperation\x88\x01\x01\x12Q\n\twork_mode\x18\x0c \x01(\x0e\x32%.moego.models.appointment.v1.WorkModeB\x08\xfa\x42\x05\x82\x01\x02\x10\x01H\tR\x08workMode\x88\x01\x01\x12\x61\n\noperations\x18\r \x03(\x0b\x32\x30.moego.models.appointment.v1.ServiceOperationDefB\x0f\xfa\x42\x0c\x92\x01\t\x10\x64\"\x05\x8a\x01\x02\x10\x01R\noperations\x12@\n\x15\x61ssociated_service_id\x18\x0e \x01(\x03\x42\x07\xfa\x42\x04\"\x02(\x00H\nR\x13\x61ssociatedServiceId\x88\x01\x01\x12l\n\x13price_override_type\x18\x0f \x01(\x0e\x32-.moego.models.offering.v1.ServiceOverrideTypeB\x08\xfa\x42\x05\x82\x01\x02\x10\x01H\x0bR\x11priceOverrideType\x88\x01\x01\x12r\n\x16\x64uration_override_type\x18\x10 \x01(\x0e\x32-.moego.models.offering.v1.ServiceOverrideTypeB\x08\xfa\x42\x05\x82\x01\x02\x10\x01H\x0cR\x14\x64urationOverrideType\x88\x01\x01\x12\x36\n\x10quantity_per_day\x18\x11 \x01(\x05\x42\x07\xfa\x42\x04\x1a\x02 \x00H\rR\x0equantityPerDay\x88\x01\x01\x12g\n\x0f\x61\x64\x64on_date_type\x18\x12 \x01(\x0e\x32..moego.models.appointment.v1.PetDetailDateTypeB\n\xfa\x42\x07\x82\x01\x04\x10\x01 \x00H\x0eR\raddonDateType\x88\x01\x01\x42\x0c\n\n_date_typeB\r\n\x0b_start_dateB\r\n\x0b_start_timeB\x0b\n\t_staff_idB\x0f\n\r_service_timeB\x10\n\x0e_service_priceB\x13\n\x11_scope_type_priceB\x12\n\x10_scope_type_timeB\x13\n\x11_enable_operationB\x0c\n\n_work_modeB\x18\n\x16_associated_service_idB\x16\n\x14_price_override_typeB\x19\n\x17_duration_override_typeB\x13\n\x11_quantity_per_dayB\x12\n\x10_addon_date_type\"\xf2\x03\n\x15SelectedEvaluationDef\x12&\n\nservice_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\tserviceId\x12\x39\n\nstart_date\x18\x02 \x01(\tB\x1a\xfa\x42\x17r\x15\x32\x13^\\d{4}-\\d{2}-\\d{2}$R\tstartDate\x12.\n\nstart_time\x18\x03 \x01(\x05\x42\n\xfa\x42\x07\x1a\x05\x18\xa0\x0b(\x00H\x00R\tstartTime\x88\x01\x01\x12/\n\x0cservice_time\x18\x04 \x01(\x05\x42\x07\xfa\x42\x04\x1a\x02(\x00H\x01R\x0bserviceTime\x88\x01\x01\x12\x38\n\rservice_price\x18\x05 \x01(\x01\x42\x0e\xfa\x42\x0b\x12\t)\x00\x00\x00\x00\x00\x00\x00\x00H\x02R\x0cservicePrice\x88\x01\x01\x12\'\n\x08staff_id\x18\x06 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00H\x03R\x07staffId\x88\x01\x01\x12+\n\nlodging_id\x18\x07 \x01(\x03\x42\x07\xfa\x42\x04\"\x02(\x00H\x04R\tlodgingId\x88\x01\x01\x12*\n\x08\x65nd_time\x18\x08 \x01(\x05\x42\n\xfa\x42\x07\x1a\x05\x10\xa0\x0b(\x00H\x05R\x07\x65ndTime\x88\x01\x01\x42\r\n\x0b_start_timeB\x0f\n\r_service_timeB\x10\n\x0e_service_priceB\x0b\n\t_staff_idB\r\n\x0b_lodging_idB\x0b\n\t_end_time\"\xbf\x01\n\x0c\x42lockTimeDef\x12\"\n\x08staff_id\x18\x02 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\x07staffId\x12\x39\n\nstart_date\x18\x0b \x01(\tB\x1a\xfa\x42\x17r\x15\x32\x13^\\d{4}-\\d{2}-\\d{2}$R\tstartDate\x12)\n\nstart_time\x18\r \x01(\x05\x42\n\xfa\x42\x07\x1a\x05\x18\xa0\x0b(\x00R\tstartTime\x12%\n\x08\x65nd_time\x18\x0e \x01(\x05\x42\n\xfa\x42\x07\x1a\x05\x18\xa0\x0b(\x00R\x07\x65ndTime\"\x90\x02\n\x15PetServiceScheduleDef\x12\x1e\n\x06pet_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\x05petId\x12m\n\x11service_schedules\x18\x02 \x03(\x0b\x32/.moego.models.appointment.v1.ServiceScheduleDefB\x0f\xfa\x42\x0c\x92\x01\t\x10\x64\"\x05\x8a\x01\x02\x10\x01R\x10serviceSchedules\x12h\n\x10\x61\x64\x64_on_schedules\x18\x03 \x03(\x0b\x32-.moego.models.appointment.v1.AddOnScheduleDefB\x0f\xfa\x42\x0c\x92\x01\t\x10\x64\"\x05\x8a\x01\x02\x10\x01R\x0e\x61\x64\x64OnSchedules\"\xe1\x04\n\x12ServiceScheduleDef\x12&\n\nservice_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\tserviceId\x12<\n\nstart_date\x18\x02 \x01(\tB\x1d\xfa\x42\x1ar\x18\x32\x13^\\d{4}-\\d{2}-\\d{2}$\xd0\x01\x01R\tstartDate\x12:\n\x08\x65nd_date\x18\x03 \x01(\tB\x1a\xfa\x42\x17r\x15\x32\x13^\\d{4}-\\d{2}-\\d{2}$H\x00R\x07\x65ndDate\x88\x01\x01\x12.\n\nstart_time\x18\x04 \x01(\x05\x42\n\xfa\x42\x07\x1a\x05\x18\xa0\x0b(\x00H\x01R\tstartTime\x88\x01\x01\x12*\n\x08\x65nd_time\x18\x05 \x01(\x05\x42\n\xfa\x42\x07\x1a\x05\x18\xa0\x0b(\x00H\x02R\x07\x65ndTime\x88\x01\x01\x12\"\n\x05order\x18\x06 \x01(\x05\x42\x07\xfa\x42\x04\x1a\x02(\x00H\x03R\x05order\x88\x01\x01\x12/\n\x0cservice_time\x18\x07 \x01(\x05\x42\x07\xfa\x42\x04\x1a\x02(\x00H\x04R\x0bserviceTime\x88\x01\x01\x12\\\n\tdate_type\x18\x08 \x01(\x0e\x32..moego.models.appointment.v1.PetDetailDateTypeB\n\xfa\x42\x07\x82\x01\x04\x10\x01 \x00H\x05R\x08\x64\x61teType\x88\x01\x01\x12H\n\x0especific_dates\x18\t \x03(\tB!\xfa\x42\x1e\x92\x01\x1b\x10\x64\"\x17r\x15\x32\x13^\\d{4}-\\d{2}-\\d{2}$R\rspecificDatesB\x0b\n\t_end_dateB\r\n\x0b_start_timeB\x0b\n\t_end_timeB\x08\n\x06_orderB\x0f\n\r_service_timeB\x0c\n\n_date_type\"\xba\x06\n\x10\x41\x64\x64OnScheduleDef\x12#\n\tadd_on_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\x07\x61\x64\x64OnId\x12P\n\tdate_type\x18\x02 \x01(\x0e\x32*.moego.models.appointment.v1.AddOnDateTypeB\x02\x18\x01H\x00R\x08\x64\x61teType\x88\x01\x01\x12>\n\nstart_date\x18\x03 \x01(\tB\x1a\xfa\x42\x17r\x15\x32\x13^\\d{4}-\\d{2}-\\d{2}$H\x01R\tstartDate\x88\x01\x01\x12H\n\x0especific_dates\x18\x04 \x03(\tB!\xfa\x42\x1e\x92\x01\x1b\x10\x64\"\x17r\x15\x32\x13^\\d{4}-\\d{2}-\\d{2}$R\rspecificDates\x12.\n\nstart_time\x18\x05 \x01(\x05\x42\n\xfa\x42\x07\x1a\x05\x18\xa0\x0b(\x00H\x02R\tstartTime\x88\x01\x01\x12\"\n\x05order\x18\x06 \x01(\x05\x42\x07\xfa\x42\x04\x1a\x02(\x00H\x03R\x05order\x88\x01\x01\x12:\n\x08\x65nd_date\x18\x07 \x01(\tB\x1a\xfa\x42\x17r\x15\x32\x13^\\d{4}-\\d{2}-\\d{2}$H\x04R\x07\x65ndDate\x88\x01\x01\x12*\n\x08\x65nd_time\x18\x08 \x01(\x05\x42\n\xfa\x42\x07\x1a\x05\x18\xa0\x0b(\x00H\x05R\x07\x65ndTime\x88\x01\x01\x12/\n\x0cservice_time\x18\t \x01(\x05\x42\x07\xfa\x42\x04\x1a\x02(\x00H\x06R\x0bserviceTime\x88\x01\x01\x12@\n\x15\x61ssociated_service_id\x18\n \x01(\x03\x42\x07\xfa\x42\x04\"\x02(\x00H\x07R\x13\x61ssociatedServiceId\x88\x01\x01\x12g\n\x0f\x61\x64\x64on_date_type\x18\x0b \x01(\x0e\x32..moego.models.appointment.v1.PetDetailDateTypeB\n\xfa\x42\x07\x82\x01\x04\x10\x01 \x00H\x08R\raddonDateType\x88\x01\x01\x42\x0c\n\n_date_typeB\r\n\x0b_start_dateB\r\n\x0b_start_timeB\x08\n\x06_orderB\x0b\n\t_end_dateB\x0b\n\t_end_timeB\x0f\n\r_service_timeB\x18\n\x16_associated_service_idB\x12\n\x10_addon_date_type\"}\n\x17ServiceLodgingAssignDef\x12:\n\x15pet_service_detail_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\x12petServiceDetailId\x12&\n\nlodging_id\x18\x02 \x01(\x03\x42\x07\xfa\x42\x04\"\x02(\x00R\tlodgingId\"\xa8\x02\n\x1a\x42oardingServiceScheduleDef\x12\x1e\n\x06pet_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\x05petId\x12&\n\nlodging_id\x18\x04 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\tlodgingId\x12\x39\n\nstart_date\x18\x05 \x01(\tB\x1a\xfa\x42\x17r\x15\x32\x13^\\d{4}-\\d{2}-\\d{2}$R\tstartDate\x12\x35\n\x08\x65nd_date\x18\x06 \x01(\tB\x1a\xfa\x42\x17r\x15\x32\x13^\\d{4}-\\d{2}-\\d{2}$R\x07\x65ndDate\x12)\n\nstart_time\x18\x07 \x01(\x05\x42\n\xfa\x42\x07\x1a\x05\x18\xa0\x0b(\x00R\tstartTime\x12%\n\x08\x65nd_time\x18\x08 \x01(\x05\x42\n\xfa\x42\x07\x1a\x05\x18\xa0\x0b(\x00R\x07\x65ndTime\"\xdd\x02\n\x1c\x45valuationServiceScheduleDef\x12,\n\revaluation_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\x0c\x65valuationId\x12\x39\n\nstart_date\x18\x02 \x01(\tB\x1a\xfa\x42\x17r\x15\x32\x13^\\d{4}-\\d{2}-\\d{2}$R\tstartDate\x12)\n\nstart_time\x18\x03 \x01(\x05\x42\n\xfa\x42\x07\x1a\x05\x18\xa0\x0b(\x00R\tstartTime\x12\'\n\x08staff_id\x18\x04 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00H\x00R\x07staffId\x88\x01\x01\x12*\n\x08\x65nd_time\x18\x05 \x01(\x05\x42\n\xfa\x42\x07\x1a\x05\x18\xa0\x0b(\x00H\x01R\x07\x65ndTime\x88\x01\x01\x12+\n\nlodging_id\x18\x06 \x01(\x03\x42\x07\xfa\x42\x04\"\x02(\x00H\x02R\tlodgingId\x88\x01\x01\x42\x0b\n\t_staff_idB\x0b\n\t_end_timeB\r\n\x0b_lodging_id\"\xae\x01\n\x15PetServiceCalendarDef\x12\x1e\n\x06pet_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\x05petId\x12u\n\x11grooming_services\x18\x02 \x03(\x0b\x32\x37.moego.models.appointment.v1.GroomingServiceCalendarDefB\x0f\xfa\x42\x0c\x92\x01\t\x10\x64\"\x05\x8a\x01\x02\x10\x01R\x10groomingServices\"\xd5\x04\n\x1aGroomingServiceCalendarDef\x12&\n\nservice_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\tserviceId\x12\"\n\x08staff_id\x18\x02 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\x07staffId\x12*\n\x0cservice_time\x18\x03 \x01(\x05\x42\x07\xfa\x42\x04\x1a\x02(\x00R\x0bserviceTime\x12R\n\x0cservice_type\x18\x04 \x01(\x0e\x32%.moego.models.offering.v1.ServiceTypeB\x08\xfa\x42\x05\x82\x01\x02\x10\x01R\x0bserviceType\x12@\n\x15\x61ssociated_service_id\x18\x05 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00H\x00R\x13\x61ssociatedServiceId\x88\x01\x01\x12.\n\x10\x65nable_operation\x18\x06 \x01(\x08H\x01R\x0f\x65nableOperation\x88\x01\x01\x12Q\n\twork_mode\x18\x07 \x01(\x0e\x32%.moego.models.appointment.v1.WorkModeB\x08\xfa\x42\x05\x82\x01\x02\x10\x01H\x02R\x08workMode\x88\x01\x01\x12i\n\noperations\x18\x08 \x03(\x0b\x32\x38.moego.models.appointment.v1.ServiceOperationCalendarDefB\x0f\xfa\x42\x0c\x92\x01\t\x10\x64\"\x05\x8a\x01\x02\x10\x01R\noperationsB\x18\n\x16_associated_service_idB\x13\n\x11_enable_operationB\x0c\n\n_work_mode\"\xd0\x01\n\x1dPetServiceCalendarScheduleDef\x12\x1e\n\x06pet_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\x05petId\x12\x8e\x01\n\x1agrooming_service_schedules\x18\x02 \x03(\x0b\x32?.moego.models.appointment.v1.GroomingServiceCalendarScheduleDefB\x0f\xfa\x42\x0c\x92\x01\t\x10\x64\"\x05\x8a\x01\x02\x10\x01R\x18groomingServiceSchedules\"\xbb\x06\n\"GroomingServiceCalendarScheduleDef\x12&\n\nservice_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\tserviceId\x12\"\n\x08staff_id\x18\x02 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\x07staffId\x12*\n\x0cservice_time\x18\x03 \x01(\x05\x42\x07\xfa\x42\x04\x1a\x02(\x00R\x0bserviceTime\x12R\n\x0cservice_type\x18\x04 \x01(\x0e\x32%.moego.models.offering.v1.ServiceTypeB\x08\xfa\x42\x05\x82\x01\x02\x10\x01R\x0bserviceType\x12@\n\x15\x61ssociated_service_id\x18\x05 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00H\x00R\x13\x61ssociatedServiceId\x88\x01\x01\x12.\n\x10\x65nable_operation\x18\x06 \x01(\x08H\x01R\x0f\x65nableOperation\x88\x01\x01\x12Q\n\twork_mode\x18\x07 \x01(\x0e\x32%.moego.models.appointment.v1.WorkModeB\x08\xfa\x42\x05\x82\x01\x02\x10\x01H\x02R\x08workMode\x88\x01\x01\x12\x82\x01\n\x13operation_schedules\x18\x08 \x03(\x0b\<EMAIL>\x0f\xfa\x42\x0c\x92\x01\t\x10\x64\"\x05\x8a\x01\x02\x10\x01R\x12operationSchedules\x12\x39\n\nstart_date\x18\x0b \x01(\tB\x1a\xfa\x42\x17r\x15\x32\x13^\\d{4}-\\d{2}-\\d{2}$R\tstartDate\x12\x35\n\x08\x65nd_date\x18\x0c \x01(\tB\x1a\xfa\x42\x17r\x15\x32\x13^\\d{4}-\\d{2}-\\d{2}$R\x07\x65ndDate\x12)\n\nstart_time\x18\r \x01(\x05\x42\n\xfa\x42\x07\x1a\x05\x18\xa0\x0b(\x00R\tstartTime\x12%\n\x08\x65nd_time\x18\x0e \x01(\x05\x42\n\xfa\x42\x07\x1a\x05\x18\xa0\x0b(\x00R\x07\x65ndTimeB\x18\n\x16_associated_service_idB\x13\n\x11_enable_operationB\x0c\n\n_work_mode\"\xc2\x04\n\x14PetDetailScheduleDef\x12\x17\n\x02id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\x02id\x12>\n\nstart_date\x18\x02 \x01(\tB\x1a\xfa\x42\x17r\x15\x32\x13^\\d{4}-\\d{2}-\\d{2}$H\x00R\tstartDate\x88\x01\x01\x12:\n\x08\x65nd_date\x18\x03 \x01(\tB\x1a\xfa\x42\x17r\x15\x32\x13^\\d{4}-\\d{2}-\\d{2}$H\x01R\x07\x65ndDate\x88\x01\x01\x12.\n\nstart_time\x18\x04 \x01(\x05\x42\n\xfa\x42\x07\x1a\x05\x18\xa0\x0b(\x00H\x02R\tstartTime\x88\x01\x01\x12*\n\x08\x65nd_time\x18\x05 \x01(\x05\x42\n\xfa\x42\x07\x1a\x05\x18\xa0\x0b(\x00H\x03R\x07\x65ndTime\x88\x01\x01\x12\\\n\tdate_type\x18\x06 \x01(\x0e\x32..moego.models.appointment.v1.PetDetailDateTypeB\n\xfa\x42\x07\x82\x01\x04\x10\x01 \x00H\x04R\x08\x64\x61teType\x88\x01\x01\x12H\n\x0especific_dates\x18\x07 \x03(\tB!\xfa\x42\x1e\x92\x01\x1b\x10\x64\"\x17r\x15\x32\x13^\\d{4}-\\d{2}-\\d{2}$R\rspecificDates\x12\x36\n\x10quantity_per_day\x18\x08 \x01(\x05\x42\x07\xfa\x42\x04\x1a\x02 \x00H\x05R\x0equantityPerDay\x88\x01\x01\x42\r\n\x0b_start_dateB\x0b\n\t_end_dateB\r\n\x0b_start_timeB\x0b\n\t_end_timeB\x0c\n\n_date_typeB\x13\n\x11_quantity_per_day\"\xa9\x04\n\x12UpdatePetDetailDef\x12\x17\n\x02id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\x02id\x12\\\n\tdate_type\x18\x02 \x01(\x0e\x32..moego.models.appointment.v1.PetDetailDateTypeB\n\xfa\x42\x07\x82\x01\x04\x10\x01 \x00H\x00R\x08\x64\x61teType\x88\x01\x01\x12>\n\nstart_date\x18\x03 \x01(\tB\x1a\xfa\x42\x17r\x15\x32\x13^\\d{4}-\\d{2}-\\d{2}$H\x01R\tstartDate\x88\x01\x01\x12.\n\nstart_time\x18\x04 \x01(\x05\x42\n\xfa\x42\x07\x1a\x05\x10\xa0\x0b(\x00H\x02R\tstartTime\x88\x01\x01\x12:\n\x08\x65nd_date\x18\x05 \x01(\tB\x1a\xfa\x42\x17r\x15\x32\x13^\\d{4}-\\d{2}-\\d{2}$H\x03R\x07\x65ndDate\x88\x01\x01\x12*\n\x08\x65nd_time\x18\x06 \x01(\x05\x42\n\xfa\x42\x07\x1a\x05\x10\xa0\x0b(\x00H\x04R\x07\x65ndTime\x88\x01\x01\x12\x31\n\x0especific_dates\x18\x07 \x03(\tB\n\xfa\x42\x07\x92\x01\x04\x10\x64\x18\x01R\rspecificDates\x12\x36\n\x10quantity_per_day\x18\x08 \x01(\x05\x42\x07\xfa\x42\x04\x1a\x02 \x00H\x05R\x0equantityPerDay\x88\x01\x01\x42\x0c\n\n_date_typeB\r\n\x0b_start_dateB\r\n\x0b_start_timeB\x0b\n\t_end_dateB\x0b\n\t_end_timeB\x13\n\x11_quantity_per_dayB\x87\x01\n#com.moego.idl.models.appointment.v1P\x01Z^github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/appointment/v1;appointmentpbb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'moego.models.appointment.v1.pet_detail_defs_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n#com.moego.idl.models.appointment.v1P\001Z^github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/appointment/v1;appointmentpb'
  _globals['_PETDETAILDEF'].fields_by_name['pet_id']._loaded_options = None
  _globals['_PETDETAILDEF'].fields_by_name['pet_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_PETDETAILDEF'].fields_by_name['services']._loaded_options = None
  _globals['_PETDETAILDEF'].fields_by_name['services']._serialized_options = b'\372B\014\222\001\t\020d\"\005\212\001\002\020\001'
  _globals['_PETDETAILDEF'].fields_by_name['add_ons']._loaded_options = None
  _globals['_PETDETAILDEF'].fields_by_name['add_ons']._serialized_options = b'\372B\014\222\001\t\020d\"\005\212\001\002\020\001'
  _globals['_PETDETAILDEF'].fields_by_name['evaluations']._loaded_options = None
  _globals['_PETDETAILDEF'].fields_by_name['evaluations']._serialized_options = b'\372B\014\222\001\t\020d\"\005\212\001\002\020\001'
  _globals['_SELECTEDSERVICEDEF'].fields_by_name['service_id']._loaded_options = None
  _globals['_SELECTEDSERVICEDEF'].fields_by_name['service_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_SELECTEDSERVICEDEF'].fields_by_name['start_date']._loaded_options = None
  _globals['_SELECTEDSERVICEDEF'].fields_by_name['start_date']._serialized_options = b'\372B\032r\0302\023^\\d{4}-\\d{2}-\\d{2}$\320\001\001'
  _globals['_SELECTEDSERVICEDEF'].fields_by_name['end_date']._loaded_options = None
  _globals['_SELECTEDSERVICEDEF'].fields_by_name['end_date']._serialized_options = b'\372B\027r\0252\023^\\d{4}-\\d{2}-\\d{2}$'
  _globals['_SELECTEDSERVICEDEF'].fields_by_name['start_time']._loaded_options = None
  _globals['_SELECTEDSERVICEDEF'].fields_by_name['start_time']._serialized_options = b'\372B\007\032\005\030\240\013(\000'
  _globals['_SELECTEDSERVICEDEF'].fields_by_name['end_time']._loaded_options = None
  _globals['_SELECTEDSERVICEDEF'].fields_by_name['end_time']._serialized_options = b'\372B\007\032\005\030\240\013(\000'
  _globals['_SELECTEDSERVICEDEF'].fields_by_name['lodging_id']._loaded_options = None
  _globals['_SELECTEDSERVICEDEF'].fields_by_name['lodging_id']._serialized_options = b'\372B\004\"\002(\000'
  _globals['_SELECTEDSERVICEDEF'].fields_by_name['staff_id']._loaded_options = None
  _globals['_SELECTEDSERVICEDEF'].fields_by_name['staff_id']._serialized_options = b'\372B\004\"\002(\000'
  _globals['_SELECTEDSERVICEDEF'].fields_by_name['service_time']._loaded_options = None
  _globals['_SELECTEDSERVICEDEF'].fields_by_name['service_time']._serialized_options = b'\372B\004\032\002(\000'
  _globals['_SELECTEDSERVICEDEF'].fields_by_name['scope_type_price']._loaded_options = None
  _globals['_SELECTEDSERVICEDEF'].fields_by_name['scope_type_price']._serialized_options = b'\372B\007\202\001\004\020\001 \000'
  _globals['_SELECTEDSERVICEDEF'].fields_by_name['scope_type_time']._loaded_options = None
  _globals['_SELECTEDSERVICEDEF'].fields_by_name['scope_type_time']._serialized_options = b'\372B\007\202\001\004\020\001 \000'
  _globals['_SELECTEDSERVICEDEF'].fields_by_name['work_mode']._loaded_options = None
  _globals['_SELECTEDSERVICEDEF'].fields_by_name['work_mode']._serialized_options = b'\372B\005\202\001\002\020\001'
  _globals['_SELECTEDSERVICEDEF'].fields_by_name['operations']._loaded_options = None
  _globals['_SELECTEDSERVICEDEF'].fields_by_name['operations']._serialized_options = b'\372B\014\222\001\t\020d\"\005\212\001\002\020\001'
  _globals['_SELECTEDSERVICEDEF'].fields_by_name['feedings']._loaded_options = None
  _globals['_SELECTEDSERVICEDEF'].fields_by_name['feedings']._serialized_options = b'\372B\016\222\001\013\010\000\020d\"\005\212\001\002\020\001'
  _globals['_SELECTEDSERVICEDEF'].fields_by_name['medications']._loaded_options = None
  _globals['_SELECTEDSERVICEDEF'].fields_by_name['medications']._serialized_options = b'\372B\016\222\001\013\010\000\020d\"\005\212\001\002\020\001'
  _globals['_SELECTEDSERVICEDEF'].fields_by_name['date_type']._loaded_options = None
  _globals['_SELECTEDSERVICEDEF'].fields_by_name['date_type']._serialized_options = b'\372B\007\202\001\004\020\001 \000'
  _globals['_SELECTEDSERVICEDEF'].fields_by_name['specific_dates']._loaded_options = None
  _globals['_SELECTEDSERVICEDEF'].fields_by_name['specific_dates']._serialized_options = b'\372B\036\222\001\033\020d\"\027r\0252\023^\\d{4}-\\d{2}-\\d{2}$'
  _globals['_SELECTEDSERVICEDEF'].fields_by_name['price_override_type']._loaded_options = None
  _globals['_SELECTEDSERVICEDEF'].fields_by_name['price_override_type']._serialized_options = b'\372B\005\202\001\002\020\001'
  _globals['_SELECTEDSERVICEDEF'].fields_by_name['duration_override_type']._loaded_options = None
  _globals['_SELECTEDSERVICEDEF'].fields_by_name['duration_override_type']._serialized_options = b'\372B\005\202\001\002\020\001'
  _globals['_SELECTEDSERVICEDEF'].fields_by_name['split_lodgings']._loaded_options = None
  _globals['_SELECTEDSERVICEDEF'].fields_by_name['split_lodgings']._serialized_options = b'\372B\005\222\001\002\020d'
  _globals['_SELECTEDADDONDEF'].fields_by_name['add_on_id']._loaded_options = None
  _globals['_SELECTEDADDONDEF'].fields_by_name['add_on_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_SELECTEDADDONDEF'].fields_by_name['date_type']._loaded_options = None
  _globals['_SELECTEDADDONDEF'].fields_by_name['date_type']._serialized_options = b'\030\001'
  _globals['_SELECTEDADDONDEF'].fields_by_name['start_date']._loaded_options = None
  _globals['_SELECTEDADDONDEF'].fields_by_name['start_date']._serialized_options = b'\372B\027r\0252\023^\\d{4}-\\d{2}-\\d{2}$'
  _globals['_SELECTEDADDONDEF'].fields_by_name['specific_dates']._loaded_options = None
  _globals['_SELECTEDADDONDEF'].fields_by_name['specific_dates']._serialized_options = b'\372B\036\222\001\033\020d\"\027r\0252\023^\\d{4}-\\d{2}-\\d{2}$'
  _globals['_SELECTEDADDONDEF'].fields_by_name['start_time']._loaded_options = None
  _globals['_SELECTEDADDONDEF'].fields_by_name['start_time']._serialized_options = b'\372B\007\032\005\030\240\013(\000'
  _globals['_SELECTEDADDONDEF'].fields_by_name['staff_id']._loaded_options = None
  _globals['_SELECTEDADDONDEF'].fields_by_name['staff_id']._serialized_options = b'\372B\004\"\002(\000'
  _globals['_SELECTEDADDONDEF'].fields_by_name['service_time']._loaded_options = None
  _globals['_SELECTEDADDONDEF'].fields_by_name['service_time']._serialized_options = b'\372B\004\032\002(\000'
  _globals['_SELECTEDADDONDEF'].fields_by_name['scope_type_price']._loaded_options = None
  _globals['_SELECTEDADDONDEF'].fields_by_name['scope_type_price']._serialized_options = b'\372B\007\202\001\004\020\001 \000'
  _globals['_SELECTEDADDONDEF'].fields_by_name['scope_type_time']._loaded_options = None
  _globals['_SELECTEDADDONDEF'].fields_by_name['scope_type_time']._serialized_options = b'\372B\007\202\001\004\020\001 \000'
  _globals['_SELECTEDADDONDEF'].fields_by_name['work_mode']._loaded_options = None
  _globals['_SELECTEDADDONDEF'].fields_by_name['work_mode']._serialized_options = b'\372B\005\202\001\002\020\001'
  _globals['_SELECTEDADDONDEF'].fields_by_name['operations']._loaded_options = None
  _globals['_SELECTEDADDONDEF'].fields_by_name['operations']._serialized_options = b'\372B\014\222\001\t\020d\"\005\212\001\002\020\001'
  _globals['_SELECTEDADDONDEF'].fields_by_name['associated_service_id']._loaded_options = None
  _globals['_SELECTEDADDONDEF'].fields_by_name['associated_service_id']._serialized_options = b'\372B\004\"\002(\000'
  _globals['_SELECTEDADDONDEF'].fields_by_name['price_override_type']._loaded_options = None
  _globals['_SELECTEDADDONDEF'].fields_by_name['price_override_type']._serialized_options = b'\372B\005\202\001\002\020\001'
  _globals['_SELECTEDADDONDEF'].fields_by_name['duration_override_type']._loaded_options = None
  _globals['_SELECTEDADDONDEF'].fields_by_name['duration_override_type']._serialized_options = b'\372B\005\202\001\002\020\001'
  _globals['_SELECTEDADDONDEF'].fields_by_name['quantity_per_day']._loaded_options = None
  _globals['_SELECTEDADDONDEF'].fields_by_name['quantity_per_day']._serialized_options = b'\372B\004\032\002 \000'
  _globals['_SELECTEDADDONDEF'].fields_by_name['addon_date_type']._loaded_options = None
  _globals['_SELECTEDADDONDEF'].fields_by_name['addon_date_type']._serialized_options = b'\372B\007\202\001\004\020\001 \000'
  _globals['_SELECTEDEVALUATIONDEF'].fields_by_name['service_id']._loaded_options = None
  _globals['_SELECTEDEVALUATIONDEF'].fields_by_name['service_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_SELECTEDEVALUATIONDEF'].fields_by_name['start_date']._loaded_options = None
  _globals['_SELECTEDEVALUATIONDEF'].fields_by_name['start_date']._serialized_options = b'\372B\027r\0252\023^\\d{4}-\\d{2}-\\d{2}$'
  _globals['_SELECTEDEVALUATIONDEF'].fields_by_name['start_time']._loaded_options = None
  _globals['_SELECTEDEVALUATIONDEF'].fields_by_name['start_time']._serialized_options = b'\372B\007\032\005\030\240\013(\000'
  _globals['_SELECTEDEVALUATIONDEF'].fields_by_name['service_time']._loaded_options = None
  _globals['_SELECTEDEVALUATIONDEF'].fields_by_name['service_time']._serialized_options = b'\372B\004\032\002(\000'
  _globals['_SELECTEDEVALUATIONDEF'].fields_by_name['service_price']._loaded_options = None
  _globals['_SELECTEDEVALUATIONDEF'].fields_by_name['service_price']._serialized_options = b'\372B\013\022\t)\000\000\000\000\000\000\000\000'
  _globals['_SELECTEDEVALUATIONDEF'].fields_by_name['staff_id']._loaded_options = None
  _globals['_SELECTEDEVALUATIONDEF'].fields_by_name['staff_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_SELECTEDEVALUATIONDEF'].fields_by_name['lodging_id']._loaded_options = None
  _globals['_SELECTEDEVALUATIONDEF'].fields_by_name['lodging_id']._serialized_options = b'\372B\004\"\002(\000'
  _globals['_SELECTEDEVALUATIONDEF'].fields_by_name['end_time']._loaded_options = None
  _globals['_SELECTEDEVALUATIONDEF'].fields_by_name['end_time']._serialized_options = b'\372B\007\032\005\020\240\013(\000'
  _globals['_BLOCKTIMEDEF'].fields_by_name['staff_id']._loaded_options = None
  _globals['_BLOCKTIMEDEF'].fields_by_name['staff_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_BLOCKTIMEDEF'].fields_by_name['start_date']._loaded_options = None
  _globals['_BLOCKTIMEDEF'].fields_by_name['start_date']._serialized_options = b'\372B\027r\0252\023^\\d{4}-\\d{2}-\\d{2}$'
  _globals['_BLOCKTIMEDEF'].fields_by_name['start_time']._loaded_options = None
  _globals['_BLOCKTIMEDEF'].fields_by_name['start_time']._serialized_options = b'\372B\007\032\005\030\240\013(\000'
  _globals['_BLOCKTIMEDEF'].fields_by_name['end_time']._loaded_options = None
  _globals['_BLOCKTIMEDEF'].fields_by_name['end_time']._serialized_options = b'\372B\007\032\005\030\240\013(\000'
  _globals['_PETSERVICESCHEDULEDEF'].fields_by_name['pet_id']._loaded_options = None
  _globals['_PETSERVICESCHEDULEDEF'].fields_by_name['pet_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_PETSERVICESCHEDULEDEF'].fields_by_name['service_schedules']._loaded_options = None
  _globals['_PETSERVICESCHEDULEDEF'].fields_by_name['service_schedules']._serialized_options = b'\372B\014\222\001\t\020d\"\005\212\001\002\020\001'
  _globals['_PETSERVICESCHEDULEDEF'].fields_by_name['add_on_schedules']._loaded_options = None
  _globals['_PETSERVICESCHEDULEDEF'].fields_by_name['add_on_schedules']._serialized_options = b'\372B\014\222\001\t\020d\"\005\212\001\002\020\001'
  _globals['_SERVICESCHEDULEDEF'].fields_by_name['service_id']._loaded_options = None
  _globals['_SERVICESCHEDULEDEF'].fields_by_name['service_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_SERVICESCHEDULEDEF'].fields_by_name['start_date']._loaded_options = None
  _globals['_SERVICESCHEDULEDEF'].fields_by_name['start_date']._serialized_options = b'\372B\032r\0302\023^\\d{4}-\\d{2}-\\d{2}$\320\001\001'
  _globals['_SERVICESCHEDULEDEF'].fields_by_name['end_date']._loaded_options = None
  _globals['_SERVICESCHEDULEDEF'].fields_by_name['end_date']._serialized_options = b'\372B\027r\0252\023^\\d{4}-\\d{2}-\\d{2}$'
  _globals['_SERVICESCHEDULEDEF'].fields_by_name['start_time']._loaded_options = None
  _globals['_SERVICESCHEDULEDEF'].fields_by_name['start_time']._serialized_options = b'\372B\007\032\005\030\240\013(\000'
  _globals['_SERVICESCHEDULEDEF'].fields_by_name['end_time']._loaded_options = None
  _globals['_SERVICESCHEDULEDEF'].fields_by_name['end_time']._serialized_options = b'\372B\007\032\005\030\240\013(\000'
  _globals['_SERVICESCHEDULEDEF'].fields_by_name['order']._loaded_options = None
  _globals['_SERVICESCHEDULEDEF'].fields_by_name['order']._serialized_options = b'\372B\004\032\002(\000'
  _globals['_SERVICESCHEDULEDEF'].fields_by_name['service_time']._loaded_options = None
  _globals['_SERVICESCHEDULEDEF'].fields_by_name['service_time']._serialized_options = b'\372B\004\032\002(\000'
  _globals['_SERVICESCHEDULEDEF'].fields_by_name['date_type']._loaded_options = None
  _globals['_SERVICESCHEDULEDEF'].fields_by_name['date_type']._serialized_options = b'\372B\007\202\001\004\020\001 \000'
  _globals['_SERVICESCHEDULEDEF'].fields_by_name['specific_dates']._loaded_options = None
  _globals['_SERVICESCHEDULEDEF'].fields_by_name['specific_dates']._serialized_options = b'\372B\036\222\001\033\020d\"\027r\0252\023^\\d{4}-\\d{2}-\\d{2}$'
  _globals['_ADDONSCHEDULEDEF'].fields_by_name['add_on_id']._loaded_options = None
  _globals['_ADDONSCHEDULEDEF'].fields_by_name['add_on_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_ADDONSCHEDULEDEF'].fields_by_name['date_type']._loaded_options = None
  _globals['_ADDONSCHEDULEDEF'].fields_by_name['date_type']._serialized_options = b'\030\001'
  _globals['_ADDONSCHEDULEDEF'].fields_by_name['start_date']._loaded_options = None
  _globals['_ADDONSCHEDULEDEF'].fields_by_name['start_date']._serialized_options = b'\372B\027r\0252\023^\\d{4}-\\d{2}-\\d{2}$'
  _globals['_ADDONSCHEDULEDEF'].fields_by_name['specific_dates']._loaded_options = None
  _globals['_ADDONSCHEDULEDEF'].fields_by_name['specific_dates']._serialized_options = b'\372B\036\222\001\033\020d\"\027r\0252\023^\\d{4}-\\d{2}-\\d{2}$'
  _globals['_ADDONSCHEDULEDEF'].fields_by_name['start_time']._loaded_options = None
  _globals['_ADDONSCHEDULEDEF'].fields_by_name['start_time']._serialized_options = b'\372B\007\032\005\030\240\013(\000'
  _globals['_ADDONSCHEDULEDEF'].fields_by_name['order']._loaded_options = None
  _globals['_ADDONSCHEDULEDEF'].fields_by_name['order']._serialized_options = b'\372B\004\032\002(\000'
  _globals['_ADDONSCHEDULEDEF'].fields_by_name['end_date']._loaded_options = None
  _globals['_ADDONSCHEDULEDEF'].fields_by_name['end_date']._serialized_options = b'\372B\027r\0252\023^\\d{4}-\\d{2}-\\d{2}$'
  _globals['_ADDONSCHEDULEDEF'].fields_by_name['end_time']._loaded_options = None
  _globals['_ADDONSCHEDULEDEF'].fields_by_name['end_time']._serialized_options = b'\372B\007\032\005\030\240\013(\000'
  _globals['_ADDONSCHEDULEDEF'].fields_by_name['service_time']._loaded_options = None
  _globals['_ADDONSCHEDULEDEF'].fields_by_name['service_time']._serialized_options = b'\372B\004\032\002(\000'
  _globals['_ADDONSCHEDULEDEF'].fields_by_name['associated_service_id']._loaded_options = None
  _globals['_ADDONSCHEDULEDEF'].fields_by_name['associated_service_id']._serialized_options = b'\372B\004\"\002(\000'
  _globals['_ADDONSCHEDULEDEF'].fields_by_name['addon_date_type']._loaded_options = None
  _globals['_ADDONSCHEDULEDEF'].fields_by_name['addon_date_type']._serialized_options = b'\372B\007\202\001\004\020\001 \000'
  _globals['_SERVICELODGINGASSIGNDEF'].fields_by_name['pet_service_detail_id']._loaded_options = None
  _globals['_SERVICELODGINGASSIGNDEF'].fields_by_name['pet_service_detail_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_SERVICELODGINGASSIGNDEF'].fields_by_name['lodging_id']._loaded_options = None
  _globals['_SERVICELODGINGASSIGNDEF'].fields_by_name['lodging_id']._serialized_options = b'\372B\004\"\002(\000'
  _globals['_BOARDINGSERVICESCHEDULEDEF'].fields_by_name['pet_id']._loaded_options = None
  _globals['_BOARDINGSERVICESCHEDULEDEF'].fields_by_name['pet_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_BOARDINGSERVICESCHEDULEDEF'].fields_by_name['lodging_id']._loaded_options = None
  _globals['_BOARDINGSERVICESCHEDULEDEF'].fields_by_name['lodging_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_BOARDINGSERVICESCHEDULEDEF'].fields_by_name['start_date']._loaded_options = None
  _globals['_BOARDINGSERVICESCHEDULEDEF'].fields_by_name['start_date']._serialized_options = b'\372B\027r\0252\023^\\d{4}-\\d{2}-\\d{2}$'
  _globals['_BOARDINGSERVICESCHEDULEDEF'].fields_by_name['end_date']._loaded_options = None
  _globals['_BOARDINGSERVICESCHEDULEDEF'].fields_by_name['end_date']._serialized_options = b'\372B\027r\0252\023^\\d{4}-\\d{2}-\\d{2}$'
  _globals['_BOARDINGSERVICESCHEDULEDEF'].fields_by_name['start_time']._loaded_options = None
  _globals['_BOARDINGSERVICESCHEDULEDEF'].fields_by_name['start_time']._serialized_options = b'\372B\007\032\005\030\240\013(\000'
  _globals['_BOARDINGSERVICESCHEDULEDEF'].fields_by_name['end_time']._loaded_options = None
  _globals['_BOARDINGSERVICESCHEDULEDEF'].fields_by_name['end_time']._serialized_options = b'\372B\007\032\005\030\240\013(\000'
  _globals['_EVALUATIONSERVICESCHEDULEDEF'].fields_by_name['evaluation_id']._loaded_options = None
  _globals['_EVALUATIONSERVICESCHEDULEDEF'].fields_by_name['evaluation_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_EVALUATIONSERVICESCHEDULEDEF'].fields_by_name['start_date']._loaded_options = None
  _globals['_EVALUATIONSERVICESCHEDULEDEF'].fields_by_name['start_date']._serialized_options = b'\372B\027r\0252\023^\\d{4}-\\d{2}-\\d{2}$'
  _globals['_EVALUATIONSERVICESCHEDULEDEF'].fields_by_name['start_time']._loaded_options = None
  _globals['_EVALUATIONSERVICESCHEDULEDEF'].fields_by_name['start_time']._serialized_options = b'\372B\007\032\005\030\240\013(\000'
  _globals['_EVALUATIONSERVICESCHEDULEDEF'].fields_by_name['staff_id']._loaded_options = None
  _globals['_EVALUATIONSERVICESCHEDULEDEF'].fields_by_name['staff_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_EVALUATIONSERVICESCHEDULEDEF'].fields_by_name['end_time']._loaded_options = None
  _globals['_EVALUATIONSERVICESCHEDULEDEF'].fields_by_name['end_time']._serialized_options = b'\372B\007\032\005\030\240\013(\000'
  _globals['_EVALUATIONSERVICESCHEDULEDEF'].fields_by_name['lodging_id']._loaded_options = None
  _globals['_EVALUATIONSERVICESCHEDULEDEF'].fields_by_name['lodging_id']._serialized_options = b'\372B\004\"\002(\000'
  _globals['_PETSERVICECALENDARDEF'].fields_by_name['pet_id']._loaded_options = None
  _globals['_PETSERVICECALENDARDEF'].fields_by_name['pet_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_PETSERVICECALENDARDEF'].fields_by_name['grooming_services']._loaded_options = None
  _globals['_PETSERVICECALENDARDEF'].fields_by_name['grooming_services']._serialized_options = b'\372B\014\222\001\t\020d\"\005\212\001\002\020\001'
  _globals['_GROOMINGSERVICECALENDARDEF'].fields_by_name['service_id']._loaded_options = None
  _globals['_GROOMINGSERVICECALENDARDEF'].fields_by_name['service_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_GROOMINGSERVICECALENDARDEF'].fields_by_name['staff_id']._loaded_options = None
  _globals['_GROOMINGSERVICECALENDARDEF'].fields_by_name['staff_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_GROOMINGSERVICECALENDARDEF'].fields_by_name['service_time']._loaded_options = None
  _globals['_GROOMINGSERVICECALENDARDEF'].fields_by_name['service_time']._serialized_options = b'\372B\004\032\002(\000'
  _globals['_GROOMINGSERVICECALENDARDEF'].fields_by_name['service_type']._loaded_options = None
  _globals['_GROOMINGSERVICECALENDARDEF'].fields_by_name['service_type']._serialized_options = b'\372B\005\202\001\002\020\001'
  _globals['_GROOMINGSERVICECALENDARDEF'].fields_by_name['associated_service_id']._loaded_options = None
  _globals['_GROOMINGSERVICECALENDARDEF'].fields_by_name['associated_service_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_GROOMINGSERVICECALENDARDEF'].fields_by_name['work_mode']._loaded_options = None
  _globals['_GROOMINGSERVICECALENDARDEF'].fields_by_name['work_mode']._serialized_options = b'\372B\005\202\001\002\020\001'
  _globals['_GROOMINGSERVICECALENDARDEF'].fields_by_name['operations']._loaded_options = None
  _globals['_GROOMINGSERVICECALENDARDEF'].fields_by_name['operations']._serialized_options = b'\372B\014\222\001\t\020d\"\005\212\001\002\020\001'
  _globals['_PETSERVICECALENDARSCHEDULEDEF'].fields_by_name['pet_id']._loaded_options = None
  _globals['_PETSERVICECALENDARSCHEDULEDEF'].fields_by_name['pet_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_PETSERVICECALENDARSCHEDULEDEF'].fields_by_name['grooming_service_schedules']._loaded_options = None
  _globals['_PETSERVICECALENDARSCHEDULEDEF'].fields_by_name['grooming_service_schedules']._serialized_options = b'\372B\014\222\001\t\020d\"\005\212\001\002\020\001'
  _globals['_GROOMINGSERVICECALENDARSCHEDULEDEF'].fields_by_name['service_id']._loaded_options = None
  _globals['_GROOMINGSERVICECALENDARSCHEDULEDEF'].fields_by_name['service_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_GROOMINGSERVICECALENDARSCHEDULEDEF'].fields_by_name['staff_id']._loaded_options = None
  _globals['_GROOMINGSERVICECALENDARSCHEDULEDEF'].fields_by_name['staff_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_GROOMINGSERVICECALENDARSCHEDULEDEF'].fields_by_name['service_time']._loaded_options = None
  _globals['_GROOMINGSERVICECALENDARSCHEDULEDEF'].fields_by_name['service_time']._serialized_options = b'\372B\004\032\002(\000'
  _globals['_GROOMINGSERVICECALENDARSCHEDULEDEF'].fields_by_name['service_type']._loaded_options = None
  _globals['_GROOMINGSERVICECALENDARSCHEDULEDEF'].fields_by_name['service_type']._serialized_options = b'\372B\005\202\001\002\020\001'
  _globals['_GROOMINGSERVICECALENDARSCHEDULEDEF'].fields_by_name['associated_service_id']._loaded_options = None
  _globals['_GROOMINGSERVICECALENDARSCHEDULEDEF'].fields_by_name['associated_service_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_GROOMINGSERVICECALENDARSCHEDULEDEF'].fields_by_name['work_mode']._loaded_options = None
  _globals['_GROOMINGSERVICECALENDARSCHEDULEDEF'].fields_by_name['work_mode']._serialized_options = b'\372B\005\202\001\002\020\001'
  _globals['_GROOMINGSERVICECALENDARSCHEDULEDEF'].fields_by_name['operation_schedules']._loaded_options = None
  _globals['_GROOMINGSERVICECALENDARSCHEDULEDEF'].fields_by_name['operation_schedules']._serialized_options = b'\372B\014\222\001\t\020d\"\005\212\001\002\020\001'
  _globals['_GROOMINGSERVICECALENDARSCHEDULEDEF'].fields_by_name['start_date']._loaded_options = None
  _globals['_GROOMINGSERVICECALENDARSCHEDULEDEF'].fields_by_name['start_date']._serialized_options = b'\372B\027r\0252\023^\\d{4}-\\d{2}-\\d{2}$'
  _globals['_GROOMINGSERVICECALENDARSCHEDULEDEF'].fields_by_name['end_date']._loaded_options = None
  _globals['_GROOMINGSERVICECALENDARSCHEDULEDEF'].fields_by_name['end_date']._serialized_options = b'\372B\027r\0252\023^\\d{4}-\\d{2}-\\d{2}$'
  _globals['_GROOMINGSERVICECALENDARSCHEDULEDEF'].fields_by_name['start_time']._loaded_options = None
  _globals['_GROOMINGSERVICECALENDARSCHEDULEDEF'].fields_by_name['start_time']._serialized_options = b'\372B\007\032\005\030\240\013(\000'
  _globals['_GROOMINGSERVICECALENDARSCHEDULEDEF'].fields_by_name['end_time']._loaded_options = None
  _globals['_GROOMINGSERVICECALENDARSCHEDULEDEF'].fields_by_name['end_time']._serialized_options = b'\372B\007\032\005\030\240\013(\000'
  _globals['_PETDETAILSCHEDULEDEF'].fields_by_name['id']._loaded_options = None
  _globals['_PETDETAILSCHEDULEDEF'].fields_by_name['id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_PETDETAILSCHEDULEDEF'].fields_by_name['start_date']._loaded_options = None
  _globals['_PETDETAILSCHEDULEDEF'].fields_by_name['start_date']._serialized_options = b'\372B\027r\0252\023^\\d{4}-\\d{2}-\\d{2}$'
  _globals['_PETDETAILSCHEDULEDEF'].fields_by_name['end_date']._loaded_options = None
  _globals['_PETDETAILSCHEDULEDEF'].fields_by_name['end_date']._serialized_options = b'\372B\027r\0252\023^\\d{4}-\\d{2}-\\d{2}$'
  _globals['_PETDETAILSCHEDULEDEF'].fields_by_name['start_time']._loaded_options = None
  _globals['_PETDETAILSCHEDULEDEF'].fields_by_name['start_time']._serialized_options = b'\372B\007\032\005\030\240\013(\000'
  _globals['_PETDETAILSCHEDULEDEF'].fields_by_name['end_time']._loaded_options = None
  _globals['_PETDETAILSCHEDULEDEF'].fields_by_name['end_time']._serialized_options = b'\372B\007\032\005\030\240\013(\000'
  _globals['_PETDETAILSCHEDULEDEF'].fields_by_name['date_type']._loaded_options = None
  _globals['_PETDETAILSCHEDULEDEF'].fields_by_name['date_type']._serialized_options = b'\372B\007\202\001\004\020\001 \000'
  _globals['_PETDETAILSCHEDULEDEF'].fields_by_name['specific_dates']._loaded_options = None
  _globals['_PETDETAILSCHEDULEDEF'].fields_by_name['specific_dates']._serialized_options = b'\372B\036\222\001\033\020d\"\027r\0252\023^\\d{4}-\\d{2}-\\d{2}$'
  _globals['_PETDETAILSCHEDULEDEF'].fields_by_name['quantity_per_day']._loaded_options = None
  _globals['_PETDETAILSCHEDULEDEF'].fields_by_name['quantity_per_day']._serialized_options = b'\372B\004\032\002 \000'
  _globals['_UPDATEPETDETAILDEF'].fields_by_name['id']._loaded_options = None
  _globals['_UPDATEPETDETAILDEF'].fields_by_name['id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_UPDATEPETDETAILDEF'].fields_by_name['date_type']._loaded_options = None
  _globals['_UPDATEPETDETAILDEF'].fields_by_name['date_type']._serialized_options = b'\372B\007\202\001\004\020\001 \000'
  _globals['_UPDATEPETDETAILDEF'].fields_by_name['start_date']._loaded_options = None
  _globals['_UPDATEPETDETAILDEF'].fields_by_name['start_date']._serialized_options = b'\372B\027r\0252\023^\\d{4}-\\d{2}-\\d{2}$'
  _globals['_UPDATEPETDETAILDEF'].fields_by_name['start_time']._loaded_options = None
  _globals['_UPDATEPETDETAILDEF'].fields_by_name['start_time']._serialized_options = b'\372B\007\032\005\020\240\013(\000'
  _globals['_UPDATEPETDETAILDEF'].fields_by_name['end_date']._loaded_options = None
  _globals['_UPDATEPETDETAILDEF'].fields_by_name['end_date']._serialized_options = b'\372B\027r\0252\023^\\d{4}-\\d{2}-\\d{2}$'
  _globals['_UPDATEPETDETAILDEF'].fields_by_name['end_time']._loaded_options = None
  _globals['_UPDATEPETDETAILDEF'].fields_by_name['end_time']._serialized_options = b'\372B\007\032\005\020\240\013(\000'
  _globals['_UPDATEPETDETAILDEF'].fields_by_name['specific_dates']._loaded_options = None
  _globals['_UPDATEPETDETAILDEF'].fields_by_name['specific_dates']._serialized_options = b'\372B\007\222\001\004\020d\030\001'
  _globals['_UPDATEPETDETAILDEF'].fields_by_name['quantity_per_day']._loaded_options = None
  _globals['_UPDATEPETDETAILDEF'].fields_by_name['quantity_per_day']._serialized_options = b'\372B\004\032\002 \000'
  _globals['_PETDETAILDEF']._serialized_start=475
  _globals['_PETDETAILDEF']._serialized_end=807
  _globals['_SELECTEDSERVICEDEF']._serialized_start=810
  _globals['_SELECTEDSERVICEDEF']._serialized_end=2674
  _globals['_SELECTEDADDONDEF']._serialized_start=2677
  _globals['_SELECTEDADDONDEF']._serialized_end=4305
  _globals['_SELECTEDEVALUATIONDEF']._serialized_start=4308
  _globals['_SELECTEDEVALUATIONDEF']._serialized_end=4806
  _globals['_BLOCKTIMEDEF']._serialized_start=4809
  _globals['_BLOCKTIMEDEF']._serialized_end=5000
  _globals['_PETSERVICESCHEDULEDEF']._serialized_start=5003
  _globals['_PETSERVICESCHEDULEDEF']._serialized_end=5275
  _globals['_SERVICESCHEDULEDEF']._serialized_start=5278
  _globals['_SERVICESCHEDULEDEF']._serialized_end=5887
  _globals['_ADDONSCHEDULEDEF']._serialized_start=5890
  _globals['_ADDONSCHEDULEDEF']._serialized_end=6716
  _globals['_SERVICELODGINGASSIGNDEF']._serialized_start=6718
  _globals['_SERVICELODGINGASSIGNDEF']._serialized_end=6843
  _globals['_BOARDINGSERVICESCHEDULEDEF']._serialized_start=6846
  _globals['_BOARDINGSERVICESCHEDULEDEF']._serialized_end=7142
  _globals['_EVALUATIONSERVICESCHEDULEDEF']._serialized_start=7145
  _globals['_EVALUATIONSERVICESCHEDULEDEF']._serialized_end=7494
  _globals['_PETSERVICECALENDARDEF']._serialized_start=7497
  _globals['_PETSERVICECALENDARDEF']._serialized_end=7671
  _globals['_GROOMINGSERVICECALENDARDEF']._serialized_start=7674
  _globals['_GROOMINGSERVICECALENDARDEF']._serialized_end=8271
  _globals['_PETSERVICECALENDARSCHEDULEDEF']._serialized_start=8274
  _globals['_PETSERVICECALENDARSCHEDULEDEF']._serialized_end=8482
  _globals['_GROOMINGSERVICECALENDARSCHEDULEDEF']._serialized_start=8485
  _globals['_GROOMINGSERVICECALENDARSCHEDULEDEF']._serialized_end=9312
  _globals['_PETDETAILSCHEDULEDEF']._serialized_start=9315
  _globals['_PETDETAILSCHEDULEDEF']._serialized_end=9893
  _globals['_UPDATEPETDETAILDEF']._serialized_start=9896
  _globals['_UPDATEPETDETAILDEF']._serialized_end=10449
# @@protoc_insertion_point(module_scope)
