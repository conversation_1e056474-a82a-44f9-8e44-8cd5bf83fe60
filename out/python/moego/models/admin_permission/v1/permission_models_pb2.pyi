from google.protobuf.internal import containers as _containers
from google.protobuf.internal import enum_type_wrapper as _enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from collections.abc import Iterable as _Iterable, Mapping as _Mapping
from typing import ClassVar as _ClassVar, Optional as _Optional, Union as _Union

DESCRIPTOR: _descriptor.FileDescriptor

class PermissionType(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
    __slots__ = ()
    PERMISSION_TYPE_UNSPECIFIED: _ClassVar[PermissionType]
    PERMISSION_TYPE_GROUP: _ClassVar[PermissionType]
    PERMISSION_TYPE_SUB_GROUP: _ClassVar[PermissionType]
    PERMISSION_TYPE_POINT: _ClassVar[PermissionType]
PERMISSION_TYPE_UNSPECIFIED: PermissionType
PERMISSION_TYPE_GROUP: PermissionType
PERMISSION_TYPE_SUB_GROUP: PermissionType
PERMISSION_TYPE_POINT: PermissionType

class PermissionModel(_message.Message):
    __slots__ = ("value", "type", "label", "short_label", "parent", "children", "disabled")
    VALUE_FIELD_NUMBER: _ClassVar[int]
    TYPE_FIELD_NUMBER: _ClassVar[int]
    LABEL_FIELD_NUMBER: _ClassVar[int]
    SHORT_LABEL_FIELD_NUMBER: _ClassVar[int]
    PARENT_FIELD_NUMBER: _ClassVar[int]
    CHILDREN_FIELD_NUMBER: _ClassVar[int]
    DISABLED_FIELD_NUMBER: _ClassVar[int]
    value: str
    type: PermissionType
    label: str
    short_label: str
    parent: str
    children: _containers.RepeatedCompositeFieldContainer[PermissionModel]
    disabled: bool
    def __init__(self, value: _Optional[str] = ..., type: _Optional[_Union[PermissionType, str]] = ..., label: _Optional[str] = ..., short_label: _Optional[str] = ..., parent: _Optional[str] = ..., children: _Optional[_Iterable[_Union[PermissionModel, _Mapping]]] = ..., disabled: bool = ...) -> None: ...
