# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: moego/models/finance_tools/v1/cash_drawer_enums.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'moego/models/finance_tools/v1/cash_drawer_enums.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n5moego/models/finance_tools/v1/cash_drawer_enums.proto\x12\x1dmoego.models.finance_tools.v1*X\n\x18\x43\x61shDrawerAdjustmentType\x12+\n\'CASH_DRAWER_ADJUSTMENT_TYPE_UNSPECIFIED\x10\x00\x12\x06\n\x02IN\x10\x01\x12\x07\n\x03OUT\x10\x02\x42\x8c\x01\n%com.moego.idl.models.finance_tools.v1P\x01Zagithub.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/finance_tools/v1;financetoolspbb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'moego.models.finance_tools.v1.cash_drawer_enums_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n%com.moego.idl.models.finance_tools.v1P\001Zagithub.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/finance_tools/v1;financetoolspb'
  _globals['_CASHDRAWERADJUSTMENTTYPE']._serialized_start=88
  _globals['_CASHDRAWERADJUSTMENTTYPE']._serialized_end=176
# @@protoc_insertion_point(module_scope)
