# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: moego/models/finance_tools/v1/cash_drawer_models.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'moego/models/finance_tools/v1/cash_drawer_models.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2
from google.type import interval_pb2 as google_dot_type_dot_interval__pb2
from google.type import money_pb2 as google_dot_type_dot_money__pb2
from moego.models.finance_tools.v1 import cash_drawer_enums_pb2 as moego_dot_models_dot_finance__tools_dot_v1_dot_cash__drawer__enums__pb2
from validate import validate_pb2 as validate_dot_validate__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n6moego/models/finance_tools/v1/cash_drawer_models.proto\x12\x1dmoego.models.finance_tools.v1\x1a\x1fgoogle/protobuf/timestamp.proto\x1a\x1agoogle/type/interval.proto\x1a\x17google/type/money.proto\x1a\x35moego/models/finance_tools/v1/cash_drawer_enums.proto\x1a\x17validate/validate.proto\"\x96\x04\n\x10\x43\x61shDrawerReport\x12\x17\n\x02id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02(\x00R\x02id\x12\"\n\x08staff_id\x18\x02 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\x07staffId\x12+\n\x05range\x18\x03 \x01(\x0b\x32\x15.google.type.IntervalR\x05range\x12\x37\n\rstart_balance\x18\x04 \x01(\x0b\x32\x12.google.type.MoneyR\x0cstartBalance\x12\x39\n\x0epayments_total\x18\x05 \x01(\x0b\x32\x12.google.type.MoneyR\rpaymentsTotal\x12?\n\x11\x61\x64justments_total\x18\x06 \x01(\x0b\x32\x12.google.type.MoneyR\x10\x61\x64justmentsTotal\x12=\n\x10\x65xpected_balance\x18\x07 \x01(\x0b\x32\x12.google.type.MoneyR\x0f\x65xpectedBalance\x12;\n\x0f\x63ounted_balance\x18\x08 \x01(\x0b\x32\x12.google.type.MoneyR\x0e\x63ountedBalance\x12\x32\n\ndifference\x18\t \x01(\x0b\x32\x12.google.type.MoneyR\ndifference\x12\'\n\x07\x63omment\x18\n \x01(\tB\x08\xfa\x42\x05r\x03\x18\xc8\x01H\x00R\x07\x63omment\x88\x01\x01\x42\n\n\x08_comment\"\x80\x03\n\x14\x43\x61shDrawerAdjustment\x12\x17\n\x02id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02(\x00R\x02id\x12)\n\treport_id\x18\x02 \x01(\x03\x42\x07\xfa\x42\x04\"\x02(\x00H\x00R\x08reportId\x88\x01\x01\x12W\n\x04type\x18\x03 \x01(\x0e\x32\x37.moego.models.finance_tools.v1.CashDrawerAdjustmentTypeB\n\xfa\x42\x07\x82\x01\x04\x10\x01 \x00R\x04type\x12\x38\n\x04time\x18\x04 \x01(\x0b\x32\x1a.google.protobuf.TimestampB\x08\xfa\x42\x05\xb2\x01\x02*\x00R\x04time\x12\"\n\x08staff_id\x18\x05 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\x07staffId\x12*\n\x06\x61mount\x18\x06 \x01(\x0b\x32\x12.google.type.MoneyR\x06\x61mount\x12\'\n\x07\x63omment\x18\x07 \x01(\tB\x08\xfa\x42\x05r\x03\x18\xc8\x01H\x01R\x07\x63omment\x88\x01\x01\x42\x0c\n\n_report_idB\n\n\x08_commentB\x8c\x01\n%com.moego.idl.models.finance_tools.v1P\x01Zagithub.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/finance_tools/v1;financetoolspbb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'moego.models.finance_tools.v1.cash_drawer_models_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n%com.moego.idl.models.finance_tools.v1P\001Zagithub.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/finance_tools/v1;financetoolspb'
  _globals['_CASHDRAWERREPORT'].fields_by_name['id']._loaded_options = None
  _globals['_CASHDRAWERREPORT'].fields_by_name['id']._serialized_options = b'\372B\004\"\002(\000'
  _globals['_CASHDRAWERREPORT'].fields_by_name['staff_id']._loaded_options = None
  _globals['_CASHDRAWERREPORT'].fields_by_name['staff_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_CASHDRAWERREPORT'].fields_by_name['comment']._loaded_options = None
  _globals['_CASHDRAWERREPORT'].fields_by_name['comment']._serialized_options = b'\372B\005r\003\030\310\001'
  _globals['_CASHDRAWERADJUSTMENT'].fields_by_name['id']._loaded_options = None
  _globals['_CASHDRAWERADJUSTMENT'].fields_by_name['id']._serialized_options = b'\372B\004\"\002(\000'
  _globals['_CASHDRAWERADJUSTMENT'].fields_by_name['report_id']._loaded_options = None
  _globals['_CASHDRAWERADJUSTMENT'].fields_by_name['report_id']._serialized_options = b'\372B\004\"\002(\000'
  _globals['_CASHDRAWERADJUSTMENT'].fields_by_name['type']._loaded_options = None
  _globals['_CASHDRAWERADJUSTMENT'].fields_by_name['type']._serialized_options = b'\372B\007\202\001\004\020\001 \000'
  _globals['_CASHDRAWERADJUSTMENT'].fields_by_name['time']._loaded_options = None
  _globals['_CASHDRAWERADJUSTMENT'].fields_by_name['time']._serialized_options = b'\372B\005\262\001\002*\000'
  _globals['_CASHDRAWERADJUSTMENT'].fields_by_name['staff_id']._loaded_options = None
  _globals['_CASHDRAWERADJUSTMENT'].fields_by_name['staff_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_CASHDRAWERADJUSTMENT'].fields_by_name['comment']._loaded_options = None
  _globals['_CASHDRAWERADJUSTMENT'].fields_by_name['comment']._serialized_options = b'\372B\005r\003\030\310\001'
  _globals['_CASHDRAWERREPORT']._serialized_start=256
  _globals['_CASHDRAWERREPORT']._serialized_end=790
  _globals['_CASHDRAWERADJUSTMENT']._serialized_start=793
  _globals['_CASHDRAWERADJUSTMENT']._serialized_end=1177
# @@protoc_insertion_point(module_scope)
