# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: moego/models/account/v1/session_enums.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'moego/models/account/v1/session_enums.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n+moego/models/account/v1/session_enums.proto\x12\x17moego.models.account.v1*f\n\rSessionStatus\x12\x1e\n\x1aSESSION_STATUS_UNSPECIFIED\x10\x00\x12\x19\n\x15SESSION_STATUS_ACTIVE\x10\x01\x12\x1a\n\x16SESSION_STATUS_DELETED\x10\x02\x42{\n\x1f\x63om.moego.idl.models.account.v1P\x01ZVgithub.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/account/v1;accountpbb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'moego.models.account.v1.session_enums_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n\037com.moego.idl.models.account.v1P\001ZVgithub.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/account/v1;accountpb'
  _globals['_SESSIONSTATUS']._serialized_start=72
  _globals['_SESSIONSTATUS']._serialized_end=174
# @@protoc_insertion_point(module_scope)
