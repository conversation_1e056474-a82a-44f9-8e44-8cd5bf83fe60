from google.protobuf import duration_pb2 as _duration_pb2
from google.protobuf import timestamp_pb2 as _timestamp_pb2
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from collections.abc import Mapping as _Mapping
from typing import ClassVar as _ClassVar, Optional as _Optional, Union as _Union

DESCRIPTOR: _descriptor.FileDescriptor

class AccountImpersonateApprovalInstanceModel(_message.Message):
    __slots__ = ("instance_code", "impersonator", "source", "target_account_id", "target_account_email", "max_age", "status", "created_at", "approved_at")
    INSTANCE_CODE_FIELD_NUMBER: _ClassVar[int]
    IMPERSONATOR_FIELD_NUMBER: _ClassVar[int]
    SOURCE_FIELD_NUMBER: _ClassVar[int]
    TARGET_ACCOUNT_ID_FIELD_NUMBER: _ClassVar[int]
    TARGET_ACCOUNT_EMAIL_FIELD_NUMBER: _ClassVar[int]
    MAX_AGE_FIELD_NUMBER: _ClassVar[int]
    STATUS_FIELD_NUMBER: _ClassVar[int]
    CREATED_AT_FIELD_NUMBER: _ClassVar[int]
    APPROVED_AT_FIELD_NUMBER: _ClassVar[int]
    instance_code: str
    impersonator: str
    source: str
    target_account_id: int
    target_account_email: str
    max_age: _duration_pb2.Duration
    status: str
    created_at: _timestamp_pb2.Timestamp
    approved_at: _timestamp_pb2.Timestamp
    def __init__(self, instance_code: _Optional[str] = ..., impersonator: _Optional[str] = ..., source: _Optional[str] = ..., target_account_id: _Optional[int] = ..., target_account_email: _Optional[str] = ..., max_age: _Optional[_Union[datetime.timedelta, _duration_pb2.Duration, _Mapping]] = ..., status: _Optional[str] = ..., created_at: _Optional[_Union[datetime.datetime, _timestamp_pb2.Timestamp, _Mapping]] = ..., approved_at: _Optional[_Union[datetime.datetime, _timestamp_pb2.Timestamp, _Mapping]] = ...) -> None: ...
