# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: moego/models/auto_message/v1/auto_message_task_defs.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'moego/models/auto_message/v1/auto_message_task_defs.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2
from moego.models.auto_message.v1 import auto_message_task_enums_pb2 as moego_dot_models_dot_auto__message_dot_v1_dot_auto__message__task__enums__pb2
from validate import validate_pb2 as validate_dot_validate__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n9moego/models/auto_message/v1/auto_message_task_defs.proto\x12\x1cmoego.models.auto_message.v1\x1a\x1fgoogle/protobuf/timestamp.proto\x1a:moego/models/auto_message/v1/auto_message_task_enums.proto\x1a\x17validate/validate.proto\"\xda\x04\n\x18\x41utoMessageTaskUpsertDef\x12&\n\ncompany_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\tcompanyId\x12(\n\x0b\x62usiness_id\x18\x02 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\nbusinessId\x12\x34\n\x12\x61uto_msg_config_id\x18\x03 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\x0f\x61utoMsgConfigId\x12$\n\tobject_id\x18\x04 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\x08objectId\x12\x62\n\x0bobject_type\x18\x05 \x01(\x0e\x32\x37.moego.models.auto_message.v1.AutoMessageTaskObjectTypeB\x08\xfa\x42\x05\x82\x01\x02\x10\x01R\nobjectType\x12(\n\x0breceiver_id\x18\x06 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\nreceiverId\x12h\n\rreceiver_type\x18\x07 \x01(\x0e\x32\x39.moego.models.auto_message.v1.AutoMessageTaskReceiverTypeB\x08\xfa\x42\x05\x82\x01\x02\x10\x01R\x0creceiverType\x12\x41\n\tsend_time\x18\x08 \x01(\x0b\x32\x1a.google.protobuf.TimestampB\x08\xfa\x42\x05\xb2\x01\x02*\x00R\x08sendTime\x12U\n\x06status\x18\t \x01(\x0e\x32\x33.moego.models.auto_message.v1.AutoMessageTaskStatusB\x08\xfa\x42\x05\x82\x01\x02\x10\x01R\x06statusB\x89\x01\n$com.moego.idl.models.auto_message.v1P\x01Z_github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/auto_message/v1;automessagepbb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'moego.models.auto_message.v1.auto_message_task_defs_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n$com.moego.idl.models.auto_message.v1P\001Z_github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/auto_message/v1;automessagepb'
  _globals['_AUTOMESSAGETASKUPSERTDEF'].fields_by_name['company_id']._loaded_options = None
  _globals['_AUTOMESSAGETASKUPSERTDEF'].fields_by_name['company_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_AUTOMESSAGETASKUPSERTDEF'].fields_by_name['business_id']._loaded_options = None
  _globals['_AUTOMESSAGETASKUPSERTDEF'].fields_by_name['business_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_AUTOMESSAGETASKUPSERTDEF'].fields_by_name['auto_msg_config_id']._loaded_options = None
  _globals['_AUTOMESSAGETASKUPSERTDEF'].fields_by_name['auto_msg_config_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_AUTOMESSAGETASKUPSERTDEF'].fields_by_name['object_id']._loaded_options = None
  _globals['_AUTOMESSAGETASKUPSERTDEF'].fields_by_name['object_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_AUTOMESSAGETASKUPSERTDEF'].fields_by_name['object_type']._loaded_options = None
  _globals['_AUTOMESSAGETASKUPSERTDEF'].fields_by_name['object_type']._serialized_options = b'\372B\005\202\001\002\020\001'
  _globals['_AUTOMESSAGETASKUPSERTDEF'].fields_by_name['receiver_id']._loaded_options = None
  _globals['_AUTOMESSAGETASKUPSERTDEF'].fields_by_name['receiver_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_AUTOMESSAGETASKUPSERTDEF'].fields_by_name['receiver_type']._loaded_options = None
  _globals['_AUTOMESSAGETASKUPSERTDEF'].fields_by_name['receiver_type']._serialized_options = b'\372B\005\202\001\002\020\001'
  _globals['_AUTOMESSAGETASKUPSERTDEF'].fields_by_name['send_time']._loaded_options = None
  _globals['_AUTOMESSAGETASKUPSERTDEF'].fields_by_name['send_time']._serialized_options = b'\372B\005\262\001\002*\000'
  _globals['_AUTOMESSAGETASKUPSERTDEF'].fields_by_name['status']._loaded_options = None
  _globals['_AUTOMESSAGETASKUPSERTDEF'].fields_by_name['status']._serialized_options = b'\372B\005\202\001\002\020\001'
  _globals['_AUTOMESSAGETASKUPSERTDEF']._serialized_start=210
  _globals['_AUTOMESSAGETASKUPSERTDEF']._serialized_end=812
# @@protoc_insertion_point(module_scope)
