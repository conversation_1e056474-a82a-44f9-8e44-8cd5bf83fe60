# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: moego/models/auto_message/v1/auto_message_task_enums.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'moego/models/auto_message/v1/auto_message_task_enums.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n:moego/models/auto_message/v1/auto_message_task_enums.proto\x12\x1cmoego.models.auto_message.v1*\xcc\x01\n\x19\x41utoMessageTaskObjectType\x12-\n)AUTO_MESSAGE_TASK_OBJECT_TYPE_UNSPECIFIED\x10\x00\x12-\n)AUTO_MESSAGE_TASK_OBJECT_TYPE_APPOINTMENT\x10\x01\x12%\n!AUTO_MESSAGE_TASK_OBJECT_TYPE_PET\x10\x02\x12*\n&AUTO_MESSAGE_TASK_OBJECT_TYPE_CUSTOMER\x10\x03*|\n\x1b\x41utoMessageTaskReceiverType\x12/\n+AUTO_MESSAGE_TASK_RECEIVER_TYPE_UNSPECIFIED\x10\x00\x12,\n(AUTO_MESSAGE_TASK_RECEIVER_TYPE_CUSTOMER\x10\x01*\xb8\x02\n\x15\x41utoMessageTaskStatus\x12(\n$AUTO_MESSAGE_TASK_STATUS_UNSPECIFIED\x10\x00\x12!\n\x1d\x41UTO_MESSAGE_TASK_STATUS_INIT\x10\x01\x12$\n AUTO_MESSAGE_TASK_STATUS_SENDING\x10\x02\x12$\n AUTO_MESSAGE_TASK_STATUS_SUCCEED\x10\x03\x12#\n\x1f\x41UTO_MESSAGE_TASK_STATUS_FAILED\x10\x04\x12/\n+AUTO_MESSAGE_TASK_STATUS_CANCELLED_BY_STAFF\x10\x05\x12\x30\n,AUTO_MESSAGE_TASK_STATUS_CANCELLED_BY_SYSTEM\x10\x06\x42\x89\x01\n$com.moego.idl.models.auto_message.v1P\x01Z_github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/auto_message/v1;automessagepbb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'moego.models.auto_message.v1.auto_message_task_enums_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n$com.moego.idl.models.auto_message.v1P\001Z_github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/auto_message/v1;automessagepb'
  _globals['_AUTOMESSAGETASKOBJECTTYPE']._serialized_start=93
  _globals['_AUTOMESSAGETASKOBJECTTYPE']._serialized_end=297
  _globals['_AUTOMESSAGETASKRECEIVERTYPE']._serialized_start=299
  _globals['_AUTOMESSAGETASKRECEIVERTYPE']._serialized_end=423
  _globals['_AUTOMESSAGETASKSTATUS']._serialized_start=426
  _globals['_AUTOMESSAGETASKSTATUS']._serialized_end=738
# @@protoc_insertion_point(module_scope)
