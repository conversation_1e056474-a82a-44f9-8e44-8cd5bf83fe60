from moego.models.risk_control.v1 import recaptcha_defs_pb2 as _recaptcha_defs_pb2
from validate import validate_pb2 as _validate_pb2
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from collections.abc import Mapping as _Mapping
from typing import ClassVar as _ClassVar, Optional as _Optional, Union as _Union

DESCRIPTOR: _descriptor.FileDescriptor

class RecaptchaChallengeRequest(_message.Message):
    __slots__ = ("recaptcha",)
    RECAPTCHA_FIELD_NUMBER: _ClassVar[int]
    recaptcha: _recaptcha_defs_pb2.RecaptchaDef
    def __init__(self, recaptcha: _Optional[_Union[_recaptcha_defs_pb2.RecaptchaDef, _Mapping]] = ...) -> None: ...

class RecaptchaChallengeResponse(_message.Message):
    __slots__ = ()
    def __init__(self) -> None: ...
