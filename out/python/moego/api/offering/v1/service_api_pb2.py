# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: moego/api/offering/v1/service_api.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'moego/api/offering/v1/service_api.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from moego.models.offering.v1 import service_defs_pb2 as moego_dot_models_dot_offering_dot_v1_dot_service__defs__pb2
from moego.models.offering.v1 import service_enum_pb2 as moego_dot_models_dot_offering_dot_v1_dot_service__enum__pb2
from moego.models.offering.v1 import service_models_pb2 as moego_dot_models_dot_offering_dot_v1_dot_service__models__pb2
from moego.utils.v2 import pagination_messages_pb2 as moego_dot_utils_dot_v2_dot_pagination__messages__pb2
from validate import validate_pb2 as validate_dot_validate__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\'moego/api/offering/v1/service_api.proto\x12\x15moego.api.offering.v1\x1a+moego/models/offering/v1/service_defs.proto\x1a+moego/models/offering/v1/service_enum.proto\x1a-moego/models/offering/v1/service_models.proto\x1a(moego/utils/v2/pagination_messages.proto\x1a\x17validate/validate.proto\"[\n\x13\x43reateServiceParams\x12\x44\n\x07service\x18\x01 \x01(\x0b\x32*.moego.models.offering.v1.CreateServiceDefR\x07service\"W\n\x13\x43reateServiceResult\x12@\n\x07service\x18\x01 \x01(\x0b\x32&.moego.models.offering.v1.ServiceModelR\x07service\"\xa8\x01\n\x13UpdateServiceParams\x12\x44\n\x07service\x18\x01 \x01(\x0b\x32*.moego.models.offering.v1.UpdateServiceDefR\x07service\x12\x33\n\x13\x61pply_upcoming_appt\x18\x02 \x01(\x08H\x00R\x11\x61pplyUpcomingAppt\x88\x01\x01\x42\x16\n\x14_apply_upcoming_appt\"W\n\x13UpdateServiceResult\x12@\n\x07service\x18\x01 \x01(\x0b\x32&.moego.models.offering.v1.ServiceModelR\x07service\"\xe2\x03\n\x14GetServiceListParams\x12Z\n\x11service_item_type\x18\x01 \x01(\x0e\x32).moego.models.offering.v1.ServiceItemTypeH\x00R\x0fserviceItemType\x88\x01\x01\x12\x46\n\npagination\x18\x02 \x01(\x0b\x32!.moego.utils.v2.PaginationRequestH\x01R\npagination\x88\x01\x01\x12!\n\x0c\x62usiness_ids\x18\x03 \x03(\x03R\x0b\x62usinessIds\x12\x1f\n\x08inactive\x18\x04 \x01(\x08H\x02R\x08inactive\x88\x01\x01\x12M\n\x0cservice_type\x18\x05 \x01(\x0e\x32%.moego.models.offering.v1.ServiceTypeH\x03R\x0bserviceType\x88\x01\x01\x12\'\n\x07keyword\x18\x06 \x01(\tB\x08\xfa\x42\x05r\x03\x18\xff\x01H\x04R\x07keyword\x88\x01\x01\x12\x1b\n\tstaff_ids\x18\x07 \x03(\x03R\x08staffIdsB\x14\n\x12_service_item_typeB\r\n\x0b_paginationB\x0b\n\t_inactiveB\x0f\n\r_service_typeB\n\n\x08_keyword\"\xc3\x01\n\x14GetServiceListResult\x12S\n\rcategory_list\x18\x01 \x03(\x0b\x32..moego.models.offering.v1.ServiceCategoryModelR\x0c\x63\x61tegoryList\x12G\n\npagination\x18\x02 \x01(\x0b\x32\".moego.utils.v2.PaginationResponseH\x00R\npagination\x88\x01\x01\x42\r\n\x0b_pagination\"\xe7\x06\n\x1eGetApplicableServiceListParams\x12Z\n\x11service_item_type\x18\x01 \x01(\x0e\x32).moego.models.offering.v1.ServiceItemTypeH\x00R\x0fserviceItemType\x88\x01\x01\x12-\n\x0b\x62usiness_id\x18\x02 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00H\x01R\nbusinessId\x88\x01\x01\x12%\n\x0eonly_available\x18\x03 \x01(\x08R\ronlyAvailable\x12\x1a\n\x06pet_id\x18\x04 \x01(\x03H\x02R\x05petId\x88\x01\x01\x12H\n\x0cservice_type\x18\x05 \x01(\x0e\x32%.moego.models.offering.v1.ServiceTypeR\x0bserviceType\x12\x30\n\x14selected_service_ids\x18\x06 \x03(\x03R\x12selectedServiceIds\x12<\n\x18selected_lodging_unit_id\x18\x07 \x01(\x03H\x03R\x15selectedLodgingUnitId\x88\x01\x01\x12\'\n\x07keyword\x18\x08 \x01(\tB\x08\xfa\x42\x05r\x03\x18\xff\x01H\x04R\x07keyword\x88\x01\x01\x12\x46\n\npagination\x18\t \x01(\x0b\x32!.moego.utils.v2.PaginationRequestH\x05R\npagination\x88\x01\x01\x12k\n\x1aselected_service_item_type\x18\n \x01(\x0e\x32).moego.models.offering.v1.ServiceItemTypeH\x06R\x17selectedServiceItemType\x88\x01\x01\x12\x1f\n\x08inactive\x18\x0b \x01(\x08H\x07R\x08inactive\x88\x01\x01\x12)\n\x07pet_ids\x18\x0c \x03(\x03\x42\x10\xfa\x42\r\x92\x01\n\x10\x64\x18\x01\"\x04\"\x02 \x00R\x06petIdsB\x14\n\x12_service_item_typeB\x0e\n\x0c_business_idB\t\n\x07_pet_idB\x1b\n\x19_selected_lodging_unit_idB\n\n\x08_keywordB\r\n\x0b_paginationB\x1d\n\x1b_selected_service_item_typeB\x0b\n\t_inactive\"\x87\x03\n\x1eGetApplicableServiceListResult\x12\\\n\rcategory_list\x18\x01 \x03(\x0b\x32\x37.moego.models.offering.v1.CustomizedServiceCategoryViewR\x0c\x63\x61tegoryList\x12G\n\npagination\x18\x02 \x01(\x0b\x32\".moego.utils.v2.PaginationResponseH\x00R\npagination\x88\x01\x01\x12P\n\x0cpet_services\x18\x03 \x03(\x0b\x32-.moego.api.offering.v1.CustomizedServiceByPetR\x0bpetServices\x12]\n\x11\x63ommon_categories\x18\x04 \x03(\x0b\x32\x30.moego.api.offering.v1.CommonServiceCategoryViewR\x10\x63ommonCategoriesB\r\n\x0b_pagination\"\x88\x01\n\x16\x43ustomizedServiceByPet\x12\x15\n\x06pet_id\x18\x01 \x01(\x03R\x05petId\x12W\n\ncategories\x18\x02 \x03(\x0b\x32\x37.moego.models.offering.v1.CustomizedServiceCategoryViewR\ncategories\"\xa3\x01\n\x19\x43ommonServiceCategoryView\x12\x1f\n\x0b\x63\x61tegory_id\x18\x01 \x01(\x03R\ncategoryId\x12\x12\n\x04name\x18\x02 \x01(\tR\x04name\x12Q\n\x0f\x63ommon_services\x18\x03 \x03(\x0b\x32(.moego.api.offering.v1.CommonServiceViewR\x0e\x63ommonServices\"\xe9\x0c\n\x11\x43ommonServiceView\x12\x0e\n\x02id\x18\x01 \x01(\x03R\x02id\x12\x12\n\x04name\x18\x02 \x01(\tR\x04name\x12i\n\x13pet_specific_prices\x18\x03 \x03(\x0b\x32\x39.moego.api.offering.v1.CommonServiceView.PetSpecificPriceR\x11petSpecificPrices\x12I\n\nprice_unit\x18\x04 \x01(\x0e\x32*.moego.models.offering.v1.ServicePriceUnitR\tpriceUnit\x12r\n\x16pet_specific_durations\x18\x05 \x03(\x0b\x32<.moego.api.offering.v1.CommonServiceView.PetSpecificDurationR\x14petSpecificDurations\x12\x39\n\x04type\x18\x06 \x01(\x0e\x32%.moego.models.offering.v1.ServiceTypeR\x04type\x12\x1f\n\x0b\x63\x61tegory_id\x18\x07 \x01(\x03R\ncategoryId\x12\x15\n\x06tax_id\x18\n \x01(\x03R\x05taxId\x12U\n\x11service_item_type\x18\x0b \x01(\x0e\x32).moego.models.offering.v1.ServiceItemTypeR\x0fserviceItemType\x12 \n\x0b\x64\x65scription\x18\x0c \x01(\tR\x0b\x64\x65scription\x12\x36\n\x17require_dedicated_staff\x18\r \x01(\x08R\x15requireDedicatedStaff\x12:\n\x19require_dedicated_lodging\x18\x0e \x01(\x08R\x17requireDedicatedLodging\x12\x1a\n\x08inactive\x18\x0f \x01(\x08R\x08inactive\x12!\n\x0cmax_duration\x18\x10 \x01(\x05R\x0bmaxDuration\x12\x16\n\x06images\x18\x11 \x03(\tR\x06images\x12[\n\x13staff_override_list\x18\x12 \x03(\x0b\x32+.moego.models.offering.v1.StaffOverrideRuleR\x11staffOverrideList\x12\x63\n\x10\x61vailable_staffs\x18\x13 \x01(\x0b\x32\x38.moego.api.offering.v1.CommonServiceView.AvailableStaffsR\x0f\x61vailableStaffs\x12%\n\x0elodging_filter\x18\x14 \x01(\x08R\rlodgingFilter\x12/\n\x13\x63ustomized_lodgings\x18\x15 \x03(\x03R\x12\x63ustomizedLodgings\x12,\n\x12\x62undle_service_ids\x18\x16 \x03(\x03R\x10\x62undleServiceIds\x12g\n\x17\x61\x64\x64itional_service_rule\x18\x17 \x01(\x0b\x32/.moego.models.offering.v1.AdditionalServiceRuleR\x15\x61\x64\x64itionalServiceRule\x1aM\n\x0f\x41vailableStaffs\x12(\n\x10is_all_available\x18\x01 \x01(\x08R\x0eisAllAvailable\x12\x10\n\x03ids\x18\x02 \x03(\x03R\x03ids\x1a\x9e\x01\n\x10PetSpecificPrice\x12\x15\n\x06pet_id\x18\x01 \x01(\x03R\x05petId\x12\x14\n\x05price\x18\x02 \x01(\x01R\x05price\x12]\n\x13price_override_type\x18\x03 \x01(\x0e\x32-.moego.models.offering.v1.ServiceOverrideTypeR\x11priceOverrideType\x1a\xad\x01\n\x13PetSpecificDuration\x12\x15\n\x06pet_id\x18\x01 \x01(\x03R\x05petId\x12\x1a\n\x08\x64uration\x18\x02 \x01(\x05R\x08\x64uration\x12\x63\n\x16\x64uration_override_type\x18\x03 \x01(\x0e\x32-.moego.models.offering.v1.ServiceOverrideTypeR\x14\x64urationOverrideType\">\n\x1c\x43ustomizedServiceByPetParams\x12\x1e\n\x06pet_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\x05petId\"y\n\x1c\x43ustomizedServiceByPetResult\x12Y\n\x0cservice_list\x18\x01 \x03(\x0b\x32\x36.moego.models.offering.v1.ServiceWithPetCustomizedInfoR\x0bserviceList\"H\n\x1eGetServiceEditableDetailParams\x12&\n\nservice_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\tserviceId\"k\n\x1eGetServiceEditableDetailResult\x12I\n!required_dedicated_staff_editable\x18\x01 \x01(\x08R\x1erequiredDedicatedStaffEditable\"\x95\x04\n\x12ListServicesParams\x12\x33\n\x0c\x62usiness_ids\x18\x01 \x03(\x03\x42\x10\xfa\x42\r\x92\x01\n\x10\x64\x18\x01\"\x04\"\x02 \x00R\x0b\x62usinessIds\x12\x66\n\x11service_item_type\x18\x02 \x01(\x0e\x32).moego.models.offering.v1.ServiceItemTypeB\n\xfa\x42\x07\x82\x01\x04\x10\x01 \x00H\x00R\x0fserviceItemType\x88\x01\x01\x12Y\n\x0cservice_type\x18\x03 \x01(\x0e\x32%.moego.models.offering.v1.ServiceTypeB\n\xfa\x42\x07\x82\x01\x04\x10\x01 \x00H\x01R\x0bserviceType\x88\x01\x01\x12K\n\npagination\x18\x04 \x01(\x0b\x32!.moego.utils.v2.PaginationRequestB\x08\xfa\x42\x05\x8a\x01\x02\x10\x01R\npagination\x12X\n\x08order_by\x18\x05 \x01(\x0e\x32,.moego.models.offering.v1.ServiceOrderByTypeB\n\xfa\x42\x07\x82\x01\x04\x10\x01 \x00H\x02R\x07orderBy\x88\x01\x01\x12\x1f\n\x08inactive\x18\x06 \x01(\x08H\x03R\x08inactive\x88\x01\x01\x42\x14\n\x12_service_item_typeB\x0f\n\r_service_typeB\x0b\n\t_order_byB\x0b\n\t_inactive\"\xa0\x01\n\x12ListServicesResult\x12\x46\n\x08services\x18\x01 \x03(\x0b\x32*.moego.models.offering.v1.ServiceBriefViewR\x08services\x12\x42\n\npagination\x18\x02 \x01(\x0b\x32\".moego.utils.v2.PaginationResponseR\npagination2\xd9\x06\n\x18ServiceManagementService\x12g\n\rCreateService\x12*.moego.api.offering.v1.CreateServiceParams\x1a*.moego.api.offering.v1.CreateServiceResult\x12g\n\rUpdateService\x12*.moego.api.offering.v1.UpdateServiceParams\x1a*.moego.api.offering.v1.UpdateServiceResult\x12j\n\x0eGetServiceList\x12+.moego.api.offering.v1.GetServiceListParams\x1a+.moego.api.offering.v1.GetServiceListResult\x12\x88\x01\n\x18GetApplicableServiceList\x12\x35.moego.api.offering.v1.GetApplicableServiceListParams\x1a\x35.moego.api.offering.v1.GetApplicableServiceListResult\x12\x82\x01\n\x16\x43ustomizedServiceByPet\x12\x33.moego.api.offering.v1.CustomizedServiceByPetParams\x1a\x33.moego.api.offering.v1.CustomizedServiceByPetResult\x12\x88\x01\n\x18GetServiceEditableDetail\x12\x35.moego.api.offering.v1.GetServiceEditableDetailParams\x1a\x35.moego.api.offering.v1.GetServiceEditableDetailResult\x12\x64\n\x0cListServices\x12).moego.api.offering.v1.ListServicesParams\x1a).moego.api.offering.v1.ListServicesResultB{\n\x1d\x63om.moego.idl.api.offering.v1P\x01ZXgithub.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/offering/v1;offeringapipbb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'moego.api.offering.v1.service_api_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n\035com.moego.idl.api.offering.v1P\001ZXgithub.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/offering/v1;offeringapipb'
  _globals['_GETSERVICELISTPARAMS'].fields_by_name['keyword']._loaded_options = None
  _globals['_GETSERVICELISTPARAMS'].fields_by_name['keyword']._serialized_options = b'\372B\005r\003\030\377\001'
  _globals['_GETAPPLICABLESERVICELISTPARAMS'].fields_by_name['business_id']._loaded_options = None
  _globals['_GETAPPLICABLESERVICELISTPARAMS'].fields_by_name['business_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_GETAPPLICABLESERVICELISTPARAMS'].fields_by_name['keyword']._loaded_options = None
  _globals['_GETAPPLICABLESERVICELISTPARAMS'].fields_by_name['keyword']._serialized_options = b'\372B\005r\003\030\377\001'
  _globals['_GETAPPLICABLESERVICELISTPARAMS'].fields_by_name['pet_ids']._loaded_options = None
  _globals['_GETAPPLICABLESERVICELISTPARAMS'].fields_by_name['pet_ids']._serialized_options = b'\372B\r\222\001\n\020d\030\001\"\004\"\002 \000'
  _globals['_CUSTOMIZEDSERVICEBYPETPARAMS'].fields_by_name['pet_id']._loaded_options = None
  _globals['_CUSTOMIZEDSERVICEBYPETPARAMS'].fields_by_name['pet_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_GETSERVICEEDITABLEDETAILPARAMS'].fields_by_name['service_id']._loaded_options = None
  _globals['_GETSERVICEEDITABLEDETAILPARAMS'].fields_by_name['service_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_LISTSERVICESPARAMS'].fields_by_name['business_ids']._loaded_options = None
  _globals['_LISTSERVICESPARAMS'].fields_by_name['business_ids']._serialized_options = b'\372B\r\222\001\n\020d\030\001\"\004\"\002 \000'
  _globals['_LISTSERVICESPARAMS'].fields_by_name['service_item_type']._loaded_options = None
  _globals['_LISTSERVICESPARAMS'].fields_by_name['service_item_type']._serialized_options = b'\372B\007\202\001\004\020\001 \000'
  _globals['_LISTSERVICESPARAMS'].fields_by_name['service_type']._loaded_options = None
  _globals['_LISTSERVICESPARAMS'].fields_by_name['service_type']._serialized_options = b'\372B\007\202\001\004\020\001 \000'
  _globals['_LISTSERVICESPARAMS'].fields_by_name['pagination']._loaded_options = None
  _globals['_LISTSERVICESPARAMS'].fields_by_name['pagination']._serialized_options = b'\372B\005\212\001\002\020\001'
  _globals['_LISTSERVICESPARAMS'].fields_by_name['order_by']._loaded_options = None
  _globals['_LISTSERVICESPARAMS'].fields_by_name['order_by']._serialized_options = b'\372B\007\202\001\004\020\001 \000'
  _globals['_CREATESERVICEPARAMS']._serialized_start=270
  _globals['_CREATESERVICEPARAMS']._serialized_end=361
  _globals['_CREATESERVICERESULT']._serialized_start=363
  _globals['_CREATESERVICERESULT']._serialized_end=450
  _globals['_UPDATESERVICEPARAMS']._serialized_start=453
  _globals['_UPDATESERVICEPARAMS']._serialized_end=621
  _globals['_UPDATESERVICERESULT']._serialized_start=623
  _globals['_UPDATESERVICERESULT']._serialized_end=710
  _globals['_GETSERVICELISTPARAMS']._serialized_start=713
  _globals['_GETSERVICELISTPARAMS']._serialized_end=1195
  _globals['_GETSERVICELISTRESULT']._serialized_start=1198
  _globals['_GETSERVICELISTRESULT']._serialized_end=1393
  _globals['_GETAPPLICABLESERVICELISTPARAMS']._serialized_start=1396
  _globals['_GETAPPLICABLESERVICELISTPARAMS']._serialized_end=2267
  _globals['_GETAPPLICABLESERVICELISTRESULT']._serialized_start=2270
  _globals['_GETAPPLICABLESERVICELISTRESULT']._serialized_end=2661
  _globals['_CUSTOMIZEDSERVICEBYPET']._serialized_start=2664
  _globals['_CUSTOMIZEDSERVICEBYPET']._serialized_end=2800
  _globals['_COMMONSERVICECATEGORYVIEW']._serialized_start=2803
  _globals['_COMMONSERVICECATEGORYVIEW']._serialized_end=2966
  _globals['_COMMONSERVICEVIEW']._serialized_start=2969
  _globals['_COMMONSERVICEVIEW']._serialized_end=4610
  _globals['_COMMONSERVICEVIEW_AVAILABLESTAFFS']._serialized_start=4196
  _globals['_COMMONSERVICEVIEW_AVAILABLESTAFFS']._serialized_end=4273
  _globals['_COMMONSERVICEVIEW_PETSPECIFICPRICE']._serialized_start=4276
  _globals['_COMMONSERVICEVIEW_PETSPECIFICPRICE']._serialized_end=4434
  _globals['_COMMONSERVICEVIEW_PETSPECIFICDURATION']._serialized_start=4437
  _globals['_COMMONSERVICEVIEW_PETSPECIFICDURATION']._serialized_end=4610
  _globals['_CUSTOMIZEDSERVICEBYPETPARAMS']._serialized_start=4612
  _globals['_CUSTOMIZEDSERVICEBYPETPARAMS']._serialized_end=4674
  _globals['_CUSTOMIZEDSERVICEBYPETRESULT']._serialized_start=4676
  _globals['_CUSTOMIZEDSERVICEBYPETRESULT']._serialized_end=4797
  _globals['_GETSERVICEEDITABLEDETAILPARAMS']._serialized_start=4799
  _globals['_GETSERVICEEDITABLEDETAILPARAMS']._serialized_end=4871
  _globals['_GETSERVICEEDITABLEDETAILRESULT']._serialized_start=4873
  _globals['_GETSERVICEEDITABLEDETAILRESULT']._serialized_end=4980
  _globals['_LISTSERVICESPARAMS']._serialized_start=4983
  _globals['_LISTSERVICESPARAMS']._serialized_end=5516
  _globals['_LISTSERVICESRESULT']._serialized_start=5519
  _globals['_LISTSERVICESRESULT']._serialized_end=5679
  _globals['_SERVICEMANAGEMENTSERVICE']._serialized_start=5682
  _globals['_SERVICEMANAGEMENTSERVICE']._serialized_end=6539
# @@protoc_insertion_point(module_scope)
