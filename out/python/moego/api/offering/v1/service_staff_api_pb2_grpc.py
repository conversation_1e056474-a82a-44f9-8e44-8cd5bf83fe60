# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

from moego.api.offering.v1 import service_staff_api_pb2 as moego_dot_api_dot_offering_dot_v1_dot_service__staff__api__pb2


class ServiceStaffServiceStub(object):
    """the service_staff service
    """

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.ListServiceStaffs = channel.unary_unary(
                '/moego.api.offering.v1.ServiceStaffService/ListServiceStaffs',
                request_serializer=moego_dot_api_dot_offering_dot_v1_dot_service__staff__api__pb2.ListServiceStaffsParams.SerializeToString,
                response_deserializer=moego_dot_api_dot_offering_dot_v1_dot_service__staff__api__pb2.ListServiceStaffsResult.FromString,
                _registered_method=True)
        self.ListEvaluationStaffs = channel.unary_unary(
                '/moego.api.offering.v1.ServiceStaffService/ListEvaluationStaffs',
                request_serializer=moego_dot_api_dot_offering_dot_v1_dot_service__staff__api__pb2.ListEvaluationStaffsParams.SerializeToString,
                response_deserializer=moego_dot_api_dot_offering_dot_v1_dot_service__staff__api__pb2.ListEvaluationStaffsResult.FromString,
                _registered_method=True)


class ServiceStaffServiceServicer(object):
    """the service_staff service
    """

    def ListServiceStaffs(self, request, context):
        """list service_staff
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ListEvaluationStaffs(self, request, context):
        """list evaluation_staff
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_ServiceStaffServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'ListServiceStaffs': grpc.unary_unary_rpc_method_handler(
                    servicer.ListServiceStaffs,
                    request_deserializer=moego_dot_api_dot_offering_dot_v1_dot_service__staff__api__pb2.ListServiceStaffsParams.FromString,
                    response_serializer=moego_dot_api_dot_offering_dot_v1_dot_service__staff__api__pb2.ListServiceStaffsResult.SerializeToString,
            ),
            'ListEvaluationStaffs': grpc.unary_unary_rpc_method_handler(
                    servicer.ListEvaluationStaffs,
                    request_deserializer=moego_dot_api_dot_offering_dot_v1_dot_service__staff__api__pb2.ListEvaluationStaffsParams.FromString,
                    response_serializer=moego_dot_api_dot_offering_dot_v1_dot_service__staff__api__pb2.ListEvaluationStaffsResult.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'moego.api.offering.v1.ServiceStaffService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('moego.api.offering.v1.ServiceStaffService', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class ServiceStaffService(object):
    """the service_staff service
    """

    @staticmethod
    def ListServiceStaffs(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.api.offering.v1.ServiceStaffService/ListServiceStaffs',
            moego_dot_api_dot_offering_dot_v1_dot_service__staff__api__pb2.ListServiceStaffsParams.SerializeToString,
            moego_dot_api_dot_offering_dot_v1_dot_service__staff__api__pb2.ListServiceStaffsResult.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def ListEvaluationStaffs(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.api.offering.v1.ServiceStaffService/ListEvaluationStaffs',
            moego_dot_api_dot_offering_dot_v1_dot_service__staff__api__pb2.ListEvaluationStaffsParams.SerializeToString,
            moego_dot_api_dot_offering_dot_v1_dot_service__staff__api__pb2.ListEvaluationStaffsResult.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)
