# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

from moego.api.offering.v1 import pet_api_pb2 as moego_dot_api_dot_offering_dot_v1_dot_pet__api__pb2


class PetServiceStub(object):
    """pet service
    """

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.SearchPetForQuickCheckIn = channel.unary_unary(
                '/moego.api.offering.v1.PetService/SearchPetForQuickCheckIn',
                request_serializer=moego_dot_api_dot_offering_dot_v1_dot_pet__api__pb2.SearchPetForQuickCheckInParams.SerializeToString,
                response_deserializer=moego_dot_api_dot_offering_dot_v1_dot_pet__api__pb2.SearchPetForQuickCheckInResult.FromString,
                _registered_method=True)
        self.SearchAssociationInfo = channel.unary_unary(
                '/moego.api.offering.v1.PetService/SearchAssociationInfo',
                request_serializer=moego_dot_api_dot_offering_dot_v1_dot_pet__api__pb2.SearchAssociationInformationParams.SerializeToString,
                response_deserializer=moego_dot_api_dot_offering_dot_v1_dot_pet__api__pb2.SearchAssociationInformationResult.FromString,
                _registered_method=True)
        self.SearchEnrollmentInfo = channel.unary_unary(
                '/moego.api.offering.v1.PetService/SearchEnrollmentInfo',
                request_serializer=moego_dot_api_dot_offering_dot_v1_dot_pet__api__pb2.SearchEnrollmentInfoParams.SerializeToString,
                response_deserializer=moego_dot_api_dot_offering_dot_v1_dot_pet__api__pb2.SearchEnrollmentInfoResult.FromString,
                _registered_method=True)
        self.SearchPetForGroupClassCheckIn = channel.unary_unary(
                '/moego.api.offering.v1.PetService/SearchPetForGroupClassCheckIn',
                request_serializer=moego_dot_api_dot_offering_dot_v1_dot_pet__api__pb2.SearchPetForGroupClassCheckInParams.SerializeToString,
                response_deserializer=moego_dot_api_dot_offering_dot_v1_dot_pet__api__pb2.SearchPetForGroupClassCheckInResult.FromString,
                _registered_method=True)


class PetServiceServicer(object):
    """pet service
    """

    def SearchPetForQuickCheckIn(self, request, context):
        """Search pet for quick check-in
        deprecated, the search interface has been moved to moego/search service, please stop using the search here.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SearchAssociationInfo(self, request, context):
        """search pet and customer association information
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SearchEnrollmentInfo(self, request, context):
        """Search pet info for enrollment
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SearchPetForGroupClassCheckIn(self, request, context):
        """Search pet for group class check-in
        It will be retrieved within the current business day
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_PetServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'SearchPetForQuickCheckIn': grpc.unary_unary_rpc_method_handler(
                    servicer.SearchPetForQuickCheckIn,
                    request_deserializer=moego_dot_api_dot_offering_dot_v1_dot_pet__api__pb2.SearchPetForQuickCheckInParams.FromString,
                    response_serializer=moego_dot_api_dot_offering_dot_v1_dot_pet__api__pb2.SearchPetForQuickCheckInResult.SerializeToString,
            ),
            'SearchAssociationInfo': grpc.unary_unary_rpc_method_handler(
                    servicer.SearchAssociationInfo,
                    request_deserializer=moego_dot_api_dot_offering_dot_v1_dot_pet__api__pb2.SearchAssociationInformationParams.FromString,
                    response_serializer=moego_dot_api_dot_offering_dot_v1_dot_pet__api__pb2.SearchAssociationInformationResult.SerializeToString,
            ),
            'SearchEnrollmentInfo': grpc.unary_unary_rpc_method_handler(
                    servicer.SearchEnrollmentInfo,
                    request_deserializer=moego_dot_api_dot_offering_dot_v1_dot_pet__api__pb2.SearchEnrollmentInfoParams.FromString,
                    response_serializer=moego_dot_api_dot_offering_dot_v1_dot_pet__api__pb2.SearchEnrollmentInfoResult.SerializeToString,
            ),
            'SearchPetForGroupClassCheckIn': grpc.unary_unary_rpc_method_handler(
                    servicer.SearchPetForGroupClassCheckIn,
                    request_deserializer=moego_dot_api_dot_offering_dot_v1_dot_pet__api__pb2.SearchPetForGroupClassCheckInParams.FromString,
                    response_serializer=moego_dot_api_dot_offering_dot_v1_dot_pet__api__pb2.SearchPetForGroupClassCheckInResult.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'moego.api.offering.v1.PetService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('moego.api.offering.v1.PetService', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class PetService(object):
    """pet service
    """

    @staticmethod
    def SearchPetForQuickCheckIn(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.api.offering.v1.PetService/SearchPetForQuickCheckIn',
            moego_dot_api_dot_offering_dot_v1_dot_pet__api__pb2.SearchPetForQuickCheckInParams.SerializeToString,
            moego_dot_api_dot_offering_dot_v1_dot_pet__api__pb2.SearchPetForQuickCheckInResult.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def SearchAssociationInfo(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.api.offering.v1.PetService/SearchAssociationInfo',
            moego_dot_api_dot_offering_dot_v1_dot_pet__api__pb2.SearchAssociationInformationParams.SerializeToString,
            moego_dot_api_dot_offering_dot_v1_dot_pet__api__pb2.SearchAssociationInformationResult.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def SearchEnrollmentInfo(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.api.offering.v1.PetService/SearchEnrollmentInfo',
            moego_dot_api_dot_offering_dot_v1_dot_pet__api__pb2.SearchEnrollmentInfoParams.SerializeToString,
            moego_dot_api_dot_offering_dot_v1_dot_pet__api__pb2.SearchEnrollmentInfoResult.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def SearchPetForGroupClassCheckIn(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.api.offering.v1.PetService/SearchPetForGroupClassCheckIn',
            moego_dot_api_dot_offering_dot_v1_dot_pet__api__pb2.SearchPetForGroupClassCheckInParams.SerializeToString,
            moego_dot_api_dot_offering_dot_v1_dot_pet__api__pb2.SearchPetForGroupClassCheckInResult.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)
