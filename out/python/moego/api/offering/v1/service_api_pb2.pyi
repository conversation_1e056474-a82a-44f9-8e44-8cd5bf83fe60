from moego.models.offering.v1 import service_defs_pb2 as _service_defs_pb2
from moego.models.offering.v1 import service_enum_pb2 as _service_enum_pb2
from moego.models.offering.v1 import service_models_pb2 as _service_models_pb2
from moego.utils.v2 import pagination_messages_pb2 as _pagination_messages_pb2
from validate import validate_pb2 as _validate_pb2
from google.protobuf.internal import containers as _containers
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from collections.abc import Iterable as _Iterable, Mapping as _Mapping
from typing import ClassVar as _ClassVar, Optional as _Optional, Union as _Union

DESCRIPTOR: _descriptor.FileDescriptor

class CreateServiceParams(_message.Message):
    __slots__ = ("service",)
    SERVICE_FIELD_NUMBER: _ClassVar[int]
    service: _service_defs_pb2.CreateServiceDef
    def __init__(self, service: _Optional[_Union[_service_defs_pb2.CreateServiceDef, _Mapping]] = ...) -> None: ...

class CreateServiceResult(_message.Message):
    __slots__ = ("service",)
    SERVICE_FIELD_NUMBER: _ClassVar[int]
    service: _service_models_pb2.ServiceModel
    def __init__(self, service: _Optional[_Union[_service_models_pb2.ServiceModel, _Mapping]] = ...) -> None: ...

class UpdateServiceParams(_message.Message):
    __slots__ = ("service", "apply_upcoming_appt")
    SERVICE_FIELD_NUMBER: _ClassVar[int]
    APPLY_UPCOMING_APPT_FIELD_NUMBER: _ClassVar[int]
    service: _service_defs_pb2.UpdateServiceDef
    apply_upcoming_appt: bool
    def __init__(self, service: _Optional[_Union[_service_defs_pb2.UpdateServiceDef, _Mapping]] = ..., apply_upcoming_appt: bool = ...) -> None: ...

class UpdateServiceResult(_message.Message):
    __slots__ = ("service",)
    SERVICE_FIELD_NUMBER: _ClassVar[int]
    service: _service_models_pb2.ServiceModel
    def __init__(self, service: _Optional[_Union[_service_models_pb2.ServiceModel, _Mapping]] = ...) -> None: ...

class GetServiceListParams(_message.Message):
    __slots__ = ("service_item_type", "pagination", "business_ids", "inactive", "service_type", "keyword", "staff_ids")
    SERVICE_ITEM_TYPE_FIELD_NUMBER: _ClassVar[int]
    PAGINATION_FIELD_NUMBER: _ClassVar[int]
    BUSINESS_IDS_FIELD_NUMBER: _ClassVar[int]
    INACTIVE_FIELD_NUMBER: _ClassVar[int]
    SERVICE_TYPE_FIELD_NUMBER: _ClassVar[int]
    KEYWORD_FIELD_NUMBER: _ClassVar[int]
    STAFF_IDS_FIELD_NUMBER: _ClassVar[int]
    service_item_type: _service_enum_pb2.ServiceItemType
    pagination: _pagination_messages_pb2.PaginationRequest
    business_ids: _containers.RepeatedScalarFieldContainer[int]
    inactive: bool
    service_type: _service_enum_pb2.ServiceType
    keyword: str
    staff_ids: _containers.RepeatedScalarFieldContainer[int]
    def __init__(self, service_item_type: _Optional[_Union[_service_enum_pb2.ServiceItemType, str]] = ..., pagination: _Optional[_Union[_pagination_messages_pb2.PaginationRequest, _Mapping]] = ..., business_ids: _Optional[_Iterable[int]] = ..., inactive: bool = ..., service_type: _Optional[_Union[_service_enum_pb2.ServiceType, str]] = ..., keyword: _Optional[str] = ..., staff_ids: _Optional[_Iterable[int]] = ...) -> None: ...

class GetServiceListResult(_message.Message):
    __slots__ = ("category_list", "pagination")
    CATEGORY_LIST_FIELD_NUMBER: _ClassVar[int]
    PAGINATION_FIELD_NUMBER: _ClassVar[int]
    category_list: _containers.RepeatedCompositeFieldContainer[_service_models_pb2.ServiceCategoryModel]
    pagination: _pagination_messages_pb2.PaginationResponse
    def __init__(self, category_list: _Optional[_Iterable[_Union[_service_models_pb2.ServiceCategoryModel, _Mapping]]] = ..., pagination: _Optional[_Union[_pagination_messages_pb2.PaginationResponse, _Mapping]] = ...) -> None: ...

class GetApplicableServiceListParams(_message.Message):
    __slots__ = ("service_item_type", "business_id", "only_available", "pet_id", "service_type", "selected_service_ids", "selected_lodging_unit_id", "keyword", "pagination", "selected_service_item_type", "inactive", "pet_ids")
    SERVICE_ITEM_TYPE_FIELD_NUMBER: _ClassVar[int]
    BUSINESS_ID_FIELD_NUMBER: _ClassVar[int]
    ONLY_AVAILABLE_FIELD_NUMBER: _ClassVar[int]
    PET_ID_FIELD_NUMBER: _ClassVar[int]
    SERVICE_TYPE_FIELD_NUMBER: _ClassVar[int]
    SELECTED_SERVICE_IDS_FIELD_NUMBER: _ClassVar[int]
    SELECTED_LODGING_UNIT_ID_FIELD_NUMBER: _ClassVar[int]
    KEYWORD_FIELD_NUMBER: _ClassVar[int]
    PAGINATION_FIELD_NUMBER: _ClassVar[int]
    SELECTED_SERVICE_ITEM_TYPE_FIELD_NUMBER: _ClassVar[int]
    INACTIVE_FIELD_NUMBER: _ClassVar[int]
    PET_IDS_FIELD_NUMBER: _ClassVar[int]
    service_item_type: _service_enum_pb2.ServiceItemType
    business_id: int
    only_available: bool
    pet_id: int
    service_type: _service_enum_pb2.ServiceType
    selected_service_ids: _containers.RepeatedScalarFieldContainer[int]
    selected_lodging_unit_id: int
    keyword: str
    pagination: _pagination_messages_pb2.PaginationRequest
    selected_service_item_type: _service_enum_pb2.ServiceItemType
    inactive: bool
    pet_ids: _containers.RepeatedScalarFieldContainer[int]
    def __init__(self, service_item_type: _Optional[_Union[_service_enum_pb2.ServiceItemType, str]] = ..., business_id: _Optional[int] = ..., only_available: bool = ..., pet_id: _Optional[int] = ..., service_type: _Optional[_Union[_service_enum_pb2.ServiceType, str]] = ..., selected_service_ids: _Optional[_Iterable[int]] = ..., selected_lodging_unit_id: _Optional[int] = ..., keyword: _Optional[str] = ..., pagination: _Optional[_Union[_pagination_messages_pb2.PaginationRequest, _Mapping]] = ..., selected_service_item_type: _Optional[_Union[_service_enum_pb2.ServiceItemType, str]] = ..., inactive: bool = ..., pet_ids: _Optional[_Iterable[int]] = ...) -> None: ...

class GetApplicableServiceListResult(_message.Message):
    __slots__ = ("category_list", "pagination", "pet_services", "common_categories")
    CATEGORY_LIST_FIELD_NUMBER: _ClassVar[int]
    PAGINATION_FIELD_NUMBER: _ClassVar[int]
    PET_SERVICES_FIELD_NUMBER: _ClassVar[int]
    COMMON_CATEGORIES_FIELD_NUMBER: _ClassVar[int]
    category_list: _containers.RepeatedCompositeFieldContainer[_service_models_pb2.CustomizedServiceCategoryView]
    pagination: _pagination_messages_pb2.PaginationResponse
    pet_services: _containers.RepeatedCompositeFieldContainer[CustomizedServiceByPet]
    common_categories: _containers.RepeatedCompositeFieldContainer[CommonServiceCategoryView]
    def __init__(self, category_list: _Optional[_Iterable[_Union[_service_models_pb2.CustomizedServiceCategoryView, _Mapping]]] = ..., pagination: _Optional[_Union[_pagination_messages_pb2.PaginationResponse, _Mapping]] = ..., pet_services: _Optional[_Iterable[_Union[CustomizedServiceByPet, _Mapping]]] = ..., common_categories: _Optional[_Iterable[_Union[CommonServiceCategoryView, _Mapping]]] = ...) -> None: ...

class CustomizedServiceByPet(_message.Message):
    __slots__ = ("pet_id", "categories")
    PET_ID_FIELD_NUMBER: _ClassVar[int]
    CATEGORIES_FIELD_NUMBER: _ClassVar[int]
    pet_id: int
    categories: _containers.RepeatedCompositeFieldContainer[_service_models_pb2.CustomizedServiceCategoryView]
    def __init__(self, pet_id: _Optional[int] = ..., categories: _Optional[_Iterable[_Union[_service_models_pb2.CustomizedServiceCategoryView, _Mapping]]] = ...) -> None: ...

class CommonServiceCategoryView(_message.Message):
    __slots__ = ("category_id", "name", "common_services")
    CATEGORY_ID_FIELD_NUMBER: _ClassVar[int]
    NAME_FIELD_NUMBER: _ClassVar[int]
    COMMON_SERVICES_FIELD_NUMBER: _ClassVar[int]
    category_id: int
    name: str
    common_services: _containers.RepeatedCompositeFieldContainer[CommonServiceView]
    def __init__(self, category_id: _Optional[int] = ..., name: _Optional[str] = ..., common_services: _Optional[_Iterable[_Union[CommonServiceView, _Mapping]]] = ...) -> None: ...

class CommonServiceView(_message.Message):
    __slots__ = ("id", "name", "pet_specific_prices", "price_unit", "pet_specific_durations", "type", "category_id", "tax_id", "service_item_type", "description", "require_dedicated_staff", "require_dedicated_lodging", "inactive", "max_duration", "images", "staff_override_list", "available_staffs", "lodging_filter", "customized_lodgings", "bundle_service_ids", "additional_service_rule")
    class AvailableStaffs(_message.Message):
        __slots__ = ("is_all_available", "ids")
        IS_ALL_AVAILABLE_FIELD_NUMBER: _ClassVar[int]
        IDS_FIELD_NUMBER: _ClassVar[int]
        is_all_available: bool
        ids: _containers.RepeatedScalarFieldContainer[int]
        def __init__(self, is_all_available: bool = ..., ids: _Optional[_Iterable[int]] = ...) -> None: ...
    class PetSpecificPrice(_message.Message):
        __slots__ = ("pet_id", "price", "price_override_type")
        PET_ID_FIELD_NUMBER: _ClassVar[int]
        PRICE_FIELD_NUMBER: _ClassVar[int]
        PRICE_OVERRIDE_TYPE_FIELD_NUMBER: _ClassVar[int]
        pet_id: int
        price: float
        price_override_type: _service_enum_pb2.ServiceOverrideType
        def __init__(self, pet_id: _Optional[int] = ..., price: _Optional[float] = ..., price_override_type: _Optional[_Union[_service_enum_pb2.ServiceOverrideType, str]] = ...) -> None: ...
    class PetSpecificDuration(_message.Message):
        __slots__ = ("pet_id", "duration", "duration_override_type")
        PET_ID_FIELD_NUMBER: _ClassVar[int]
        DURATION_FIELD_NUMBER: _ClassVar[int]
        DURATION_OVERRIDE_TYPE_FIELD_NUMBER: _ClassVar[int]
        pet_id: int
        duration: int
        duration_override_type: _service_enum_pb2.ServiceOverrideType
        def __init__(self, pet_id: _Optional[int] = ..., duration: _Optional[int] = ..., duration_override_type: _Optional[_Union[_service_enum_pb2.ServiceOverrideType, str]] = ...) -> None: ...
    ID_FIELD_NUMBER: _ClassVar[int]
    NAME_FIELD_NUMBER: _ClassVar[int]
    PET_SPECIFIC_PRICES_FIELD_NUMBER: _ClassVar[int]
    PRICE_UNIT_FIELD_NUMBER: _ClassVar[int]
    PET_SPECIFIC_DURATIONS_FIELD_NUMBER: _ClassVar[int]
    TYPE_FIELD_NUMBER: _ClassVar[int]
    CATEGORY_ID_FIELD_NUMBER: _ClassVar[int]
    TAX_ID_FIELD_NUMBER: _ClassVar[int]
    SERVICE_ITEM_TYPE_FIELD_NUMBER: _ClassVar[int]
    DESCRIPTION_FIELD_NUMBER: _ClassVar[int]
    REQUIRE_DEDICATED_STAFF_FIELD_NUMBER: _ClassVar[int]
    REQUIRE_DEDICATED_LODGING_FIELD_NUMBER: _ClassVar[int]
    INACTIVE_FIELD_NUMBER: _ClassVar[int]
    MAX_DURATION_FIELD_NUMBER: _ClassVar[int]
    IMAGES_FIELD_NUMBER: _ClassVar[int]
    STAFF_OVERRIDE_LIST_FIELD_NUMBER: _ClassVar[int]
    AVAILABLE_STAFFS_FIELD_NUMBER: _ClassVar[int]
    LODGING_FILTER_FIELD_NUMBER: _ClassVar[int]
    CUSTOMIZED_LODGINGS_FIELD_NUMBER: _ClassVar[int]
    BUNDLE_SERVICE_IDS_FIELD_NUMBER: _ClassVar[int]
    ADDITIONAL_SERVICE_RULE_FIELD_NUMBER: _ClassVar[int]
    id: int
    name: str
    pet_specific_prices: _containers.RepeatedCompositeFieldContainer[CommonServiceView.PetSpecificPrice]
    price_unit: _service_enum_pb2.ServicePriceUnit
    pet_specific_durations: _containers.RepeatedCompositeFieldContainer[CommonServiceView.PetSpecificDuration]
    type: _service_enum_pb2.ServiceType
    category_id: int
    tax_id: int
    service_item_type: _service_enum_pb2.ServiceItemType
    description: str
    require_dedicated_staff: bool
    require_dedicated_lodging: bool
    inactive: bool
    max_duration: int
    images: _containers.RepeatedScalarFieldContainer[str]
    staff_override_list: _containers.RepeatedCompositeFieldContainer[_service_models_pb2.StaffOverrideRule]
    available_staffs: CommonServiceView.AvailableStaffs
    lodging_filter: bool
    customized_lodgings: _containers.RepeatedScalarFieldContainer[int]
    bundle_service_ids: _containers.RepeatedScalarFieldContainer[int]
    additional_service_rule: _service_models_pb2.AdditionalServiceRule
    def __init__(self, id: _Optional[int] = ..., name: _Optional[str] = ..., pet_specific_prices: _Optional[_Iterable[_Union[CommonServiceView.PetSpecificPrice, _Mapping]]] = ..., price_unit: _Optional[_Union[_service_enum_pb2.ServicePriceUnit, str]] = ..., pet_specific_durations: _Optional[_Iterable[_Union[CommonServiceView.PetSpecificDuration, _Mapping]]] = ..., type: _Optional[_Union[_service_enum_pb2.ServiceType, str]] = ..., category_id: _Optional[int] = ..., tax_id: _Optional[int] = ..., service_item_type: _Optional[_Union[_service_enum_pb2.ServiceItemType, str]] = ..., description: _Optional[str] = ..., require_dedicated_staff: bool = ..., require_dedicated_lodging: bool = ..., inactive: bool = ..., max_duration: _Optional[int] = ..., images: _Optional[_Iterable[str]] = ..., staff_override_list: _Optional[_Iterable[_Union[_service_models_pb2.StaffOverrideRule, _Mapping]]] = ..., available_staffs: _Optional[_Union[CommonServiceView.AvailableStaffs, _Mapping]] = ..., lodging_filter: bool = ..., customized_lodgings: _Optional[_Iterable[int]] = ..., bundle_service_ids: _Optional[_Iterable[int]] = ..., additional_service_rule: _Optional[_Union[_service_models_pb2.AdditionalServiceRule, _Mapping]] = ...) -> None: ...

class CustomizedServiceByPetParams(_message.Message):
    __slots__ = ("pet_id",)
    PET_ID_FIELD_NUMBER: _ClassVar[int]
    pet_id: int
    def __init__(self, pet_id: _Optional[int] = ...) -> None: ...

class CustomizedServiceByPetResult(_message.Message):
    __slots__ = ("service_list",)
    SERVICE_LIST_FIELD_NUMBER: _ClassVar[int]
    service_list: _containers.RepeatedCompositeFieldContainer[_service_defs_pb2.ServiceWithPetCustomizedInfo]
    def __init__(self, service_list: _Optional[_Iterable[_Union[_service_defs_pb2.ServiceWithPetCustomizedInfo, _Mapping]]] = ...) -> None: ...

class GetServiceEditableDetailParams(_message.Message):
    __slots__ = ("service_id",)
    SERVICE_ID_FIELD_NUMBER: _ClassVar[int]
    service_id: int
    def __init__(self, service_id: _Optional[int] = ...) -> None: ...

class GetServiceEditableDetailResult(_message.Message):
    __slots__ = ("required_dedicated_staff_editable",)
    REQUIRED_DEDICATED_STAFF_EDITABLE_FIELD_NUMBER: _ClassVar[int]
    required_dedicated_staff_editable: bool
    def __init__(self, required_dedicated_staff_editable: bool = ...) -> None: ...

class ListServicesParams(_message.Message):
    __slots__ = ("business_ids", "service_item_type", "service_type", "pagination", "order_by", "inactive")
    BUSINESS_IDS_FIELD_NUMBER: _ClassVar[int]
    SERVICE_ITEM_TYPE_FIELD_NUMBER: _ClassVar[int]
    SERVICE_TYPE_FIELD_NUMBER: _ClassVar[int]
    PAGINATION_FIELD_NUMBER: _ClassVar[int]
    ORDER_BY_FIELD_NUMBER: _ClassVar[int]
    INACTIVE_FIELD_NUMBER: _ClassVar[int]
    business_ids: _containers.RepeatedScalarFieldContainer[int]
    service_item_type: _service_enum_pb2.ServiceItemType
    service_type: _service_enum_pb2.ServiceType
    pagination: _pagination_messages_pb2.PaginationRequest
    order_by: _service_enum_pb2.ServiceOrderByType
    inactive: bool
    def __init__(self, business_ids: _Optional[_Iterable[int]] = ..., service_item_type: _Optional[_Union[_service_enum_pb2.ServiceItemType, str]] = ..., service_type: _Optional[_Union[_service_enum_pb2.ServiceType, str]] = ..., pagination: _Optional[_Union[_pagination_messages_pb2.PaginationRequest, _Mapping]] = ..., order_by: _Optional[_Union[_service_enum_pb2.ServiceOrderByType, str]] = ..., inactive: bool = ...) -> None: ...

class ListServicesResult(_message.Message):
    __slots__ = ("services", "pagination")
    SERVICES_FIELD_NUMBER: _ClassVar[int]
    PAGINATION_FIELD_NUMBER: _ClassVar[int]
    services: _containers.RepeatedCompositeFieldContainer[_service_models_pb2.ServiceBriefView]
    pagination: _pagination_messages_pb2.PaginationResponse
    def __init__(self, services: _Optional[_Iterable[_Union[_service_models_pb2.ServiceBriefView, _Mapping]]] = ..., pagination: _Optional[_Union[_pagination_messages_pb2.PaginationResponse, _Mapping]] = ...) -> None: ...
