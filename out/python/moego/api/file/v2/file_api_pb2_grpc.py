# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

from moego.api.file.v2 import file_api_pb2 as moego_dot_api_dot_file_dot_v2_dot_file__api__pb2


class FileServiceStub(object):
    """FileService
    """

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.GetUploadPresignedUrl = channel.unary_unary(
                '/moego.api.file.v2.FileService/GetUploadPresignedUrl',
                request_serializer=moego_dot_api_dot_file_dot_v2_dot_file__api__pb2.GetUploadPresignedUrlRequest.SerializeToString,
                response_deserializer=moego_dot_api_dot_file_dot_v2_dot_file__api__pb2.GetUploadPresignedUrlResponse.FromString,
                _registered_method=True)
        self.QueryFile = channel.unary_unary(
                '/moego.api.file.v2.FileService/QueryFile',
                request_serializer=moego_dot_api_dot_file_dot_v2_dot_file__api__pb2.QueryFileRequest.SerializeToString,
                response_deserializer=moego_dot_api_dot_file_dot_v2_dot_file__api__pb2.QueryFileResponse.FromString,
                _registered_method=True)
        self.FlushFile = channel.unary_unary(
                '/moego.api.file.v2.FileService/FlushFile',
                request_serializer=moego_dot_api_dot_file_dot_v2_dot_file__api__pb2.FlushFileRequest.SerializeToString,
                response_deserializer=moego_dot_api_dot_file_dot_v2_dot_file__api__pb2.FlushFileResponse.FromString,
                _registered_method=True)


class FileServiceServicer(object):
    """FileService
    """

    def GetUploadPresignedUrl(self, request, context):
        """GetUploadPresignedUrl
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def QueryFile(self, request, context):
        """QueryFile
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def FlushFile(self, request, context):
        """flush file status
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_FileServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'GetUploadPresignedUrl': grpc.unary_unary_rpc_method_handler(
                    servicer.GetUploadPresignedUrl,
                    request_deserializer=moego_dot_api_dot_file_dot_v2_dot_file__api__pb2.GetUploadPresignedUrlRequest.FromString,
                    response_serializer=moego_dot_api_dot_file_dot_v2_dot_file__api__pb2.GetUploadPresignedUrlResponse.SerializeToString,
            ),
            'QueryFile': grpc.unary_unary_rpc_method_handler(
                    servicer.QueryFile,
                    request_deserializer=moego_dot_api_dot_file_dot_v2_dot_file__api__pb2.QueryFileRequest.FromString,
                    response_serializer=moego_dot_api_dot_file_dot_v2_dot_file__api__pb2.QueryFileResponse.SerializeToString,
            ),
            'FlushFile': grpc.unary_unary_rpc_method_handler(
                    servicer.FlushFile,
                    request_deserializer=moego_dot_api_dot_file_dot_v2_dot_file__api__pb2.FlushFileRequest.FromString,
                    response_serializer=moego_dot_api_dot_file_dot_v2_dot_file__api__pb2.FlushFileResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'moego.api.file.v2.FileService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('moego.api.file.v2.FileService', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class FileService(object):
    """FileService
    """

    @staticmethod
    def GetUploadPresignedUrl(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.api.file.v2.FileService/GetUploadPresignedUrl',
            moego_dot_api_dot_file_dot_v2_dot_file__api__pb2.GetUploadPresignedUrlRequest.SerializeToString,
            moego_dot_api_dot_file_dot_v2_dot_file__api__pb2.GetUploadPresignedUrlResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def QueryFile(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.api.file.v2.FileService/QueryFile',
            moego_dot_api_dot_file_dot_v2_dot_file__api__pb2.QueryFileRequest.SerializeToString,
            moego_dot_api_dot_file_dot_v2_dot_file__api__pb2.QueryFileResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def FlushFile(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.api.file.v2.FileService/FlushFile',
            moego_dot_api_dot_file_dot_v2_dot_file__api__pb2.FlushFileRequest.SerializeToString,
            moego_dot_api_dot_file_dot_v2_dot_file__api__pb2.FlushFileResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)
