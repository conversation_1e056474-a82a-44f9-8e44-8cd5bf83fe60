# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: moego/api/appointment/v1/print_card_api.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'moego/api/appointment/v1/print_card_api.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.type import date_pb2 as google_dot_type_dot_date__pb2
from moego.models.appointment.v1 import appointment_task_enums_pb2 as moego_dot_models_dot_appointment_dot_v1_dot_appointment__task__enums__pb2
from moego.models.appointment.v1 import boarding_split_lodging_defs_pb2 as moego_dot_models_dot_appointment_dot_v1_dot_boarding__split__lodging__defs__pb2
from moego.models.customer.v1 import customer_pet_enums_pb2 as moego_dot_models_dot_customer_dot_v1_dot_customer__pet__enums__pb2
from moego.models.offering.v1 import lodging_unit_models_pb2 as moego_dot_models_dot_offering_dot_v1_dot_lodging__unit__models__pb2
from moego.models.offering.v1 import playgroup_models_pb2 as moego_dot_models_dot_offering_dot_v1_dot_playgroup__models__pb2
from moego.models.offering.v1 import service_enum_pb2 as moego_dot_models_dot_offering_dot_v1_dot_service__enum__pb2
from validate import validate_pb2 as validate_dot_validate__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n-moego/api/appointment/v1/print_card_api.proto\x12\x18moego.api.appointment.v1\x1a\x16google/type/date.proto\x1a\x38moego/models/appointment/v1/appointment_task_enums.proto\x1a=moego/models/appointment/v1/boarding_split_lodging_defs.proto\x1a\x31moego/models/customer/v1/customer_pet_enums.proto\x1a\x32moego/models/offering/v1/lodging_unit_models.proto\x1a/moego/models/offering/v1/playgroup_models.proto\x1a+moego/models/offering/v1/service_enum.proto\x1a\x17validate/validate.proto\"\x97\x02\n\x19ListAppointmentCardParams\x12(\n\x0b\x62usiness_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\nbusinessId\x12%\n\x04\x64\x61te\x18\x02 \x01(\x0b\x32\x11.google.type.DateR\x04\x64\x61te\x12R\n\x05types\x18\x03 \x03(\x0e\x32).moego.models.offering.v1.ServiceItemTypeB\x11\xfa\x42\x0e\x92\x01\x0b\x18\x01\"\x07\x82\x01\x04\x10\x01 \x00R\x05types\x12\x39\n\x17is_pets_checked_in_only\x18\x04 \x01(\x08H\x00R\x13isPetsCheckedInOnly\x88\x01\x01\x42\x1a\n\x18_is_pets_checked_in_only\"\x85\"\n\x19ListAppointmentCardResult\x12^\n\tboardings\x18\x01 \x03(\x0b\<EMAIL>\tboardings\x12[\n\x08\x64\x61ycares\x18\x02 \x03(\x0b\x32?.moego.api.appointment.v1.ListAppointmentCardResult.DaycareViewR\x08\x64\x61ycares\x12^\n\tgroomings\x18\x03 \x03(\x0b\<EMAIL>\tgroomings\x12\x64\n\x0b\x65valuations\x18\x04 \x03(\x0b\x32\x42.moego.api.appointment.v1.ListAppointmentCardResult.EvaluationViewR\x0b\x65valuations\x12\x65\n\x0c\x64og_walkings\x18\x05 \x03(\x0b\x32\x42.moego.api.appointment.v1.ListAppointmentCardResult.DogWalkingViewR\x0b\x64ogWalkings\x12O\n\x04pets\x18\x0b \x03(\x0b\x32;.moego.api.appointment.v1.ListAppointmentCardResult.PetViewR\x04pets\x12g\n\x0c\x61ppointments\x18\x0c \x03(\x0b\x32\x43.moego.api.appointment.v1.ListAppointmentCardResult.AppointmentViewR\x0c\x61ppointments\x12k\n\rlodging_types\x18\r \x03(\x0b\x32\x46.moego.api.appointment.v1.ListAppointmentCardResult.LodgingTypeDayViewR\x0clodgingTypes\x12O\n\rlodging_units\x18\x0e \x03(\x0b\x32*.moego.models.offering.v1.LodgingUnitModelR\x0clodgingUnits\x1a\x9b\x05\n\x0c\x42oardingView\x12%\n\x0e\x61ppointment_id\x18\x01 \x01(\x03R\rappointmentId\x12\x15\n\x06pet_id\x18\x02 \x01(\x03R\x05petId\x12r\n\x0cpet_services\x18\x03 \x03(\x0b\x32O.moego.api.appointment.v1.ListAppointmentCardResult.BoardingView.PetServiceViewR\x0bpetServices\x1a\xd8\x03\n\x0ePetServiceView\x12$\n\x0epet_service_id\x18\x01 \x01(\x03R\x0cpetServiceId\x12!\n\x0cservice_name\x18\x02 \x01(\tR\x0bserviceName\x12\x1d\n\nstart_date\x18\x03 \x01(\tR\tstartDate\x12\x1d\n\nstart_time\x18\x04 \x01(\x05R\tstartTime\x12\x19\n\x08\x65nd_date\x18\x05 \x01(\tR\x07\x65ndDate\x12\x19\n\x08\x65nd_time\x18\x06 \x01(\x05R\x07\x65ndTime\x12*\n\x11lodging_unit_name\x18\x07 \x01(\tR\x0flodgingUnitName\x12*\n\x11lodging_type_name\x18\x08 \x01(\tR\x0flodgingTypeName\x12&\n\x0flodging_type_id\x18\t \x01(\x03R\rlodgingTypeId\x12\x61\n\x0esplit_lodgings\x18\n \x03(\x0b\x32:.moego.models.appointment.v1.BoardingSplitLodgingDetailDefR\rsplitLodgings\x12&\n\x0flodging_unit_id\x18\x0b \x01(\x03R\rlodgingUnitId\x1a\xfc\x03\n\x0b\x44\x61ycareView\x12%\n\x0e\x61ppointment_id\x18\x01 \x01(\x03R\rappointmentId\x12\x15\n\x06pet_id\x18\x02 \x01(\x03R\x05petId\x12q\n\x0cpet_services\x18\x03 \x03(\x0b\x32N.moego.api.appointment.v1.ListAppointmentCardResult.DaycareView.PetServiceViewR\x0bpetServices\x1a\xbb\x02\n\x0ePetServiceView\x12$\n\x0epet_service_id\x18\x01 \x01(\x03R\x0cpetServiceId\x12!\n\x0cservice_name\x18\x02 \x01(\tR\x0bserviceName\x12\x1d\n\nstart_time\x18\x03 \x01(\x05R\tstartTime\x12\x19\n\x08\x65nd_time\x18\x04 \x01(\x05R\x07\x65ndTime\x12*\n\x11lodging_unit_name\x18\x05 \x01(\tR\x0flodgingUnitName\x12*\n\x11lodging_type_name\x18\x06 \x01(\tR\x0flodgingTypeName\x12&\n\x0flodging_type_id\x18\x07 \x01(\x03R\rlodgingTypeId\x12&\n\x0flodging_unit_id\x18\x08 \x01(\x03R\rlodgingUnitId\x1a\xf2\x03\n\x0cGroomingView\x12%\n\x0e\x61ppointment_id\x18\x01 \x01(\x03R\rappointmentId\x12\x15\n\x06pet_id\x18\x02 \x01(\x03R\x05petId\x12r\n\x0cpet_services\x18\x03 \x03(\x0b\x32O.moego.api.appointment.v1.ListAppointmentCardResult.GroomingView.PetServiceViewR\x0bpetServices\x1a\xaf\x02\n\x0ePetServiceView\x12$\n\x0epet_service_id\x18\x01 \x01(\x03R\x0cpetServiceId\x12!\n\x0cservice_name\x18\x02 \x01(\tR\x0bserviceName\x12\x1d\n\nstart_time\x18\x03 \x01(\x05R\tstartTime\x12\x19\n\x08\x65nd_time\x18\x04 \x01(\x05R\x07\x65ndTime\x12(\n\x10staff_first_name\x18\x05 \x01(\tR\x0estaffFirstName\x12&\n\x0fstaff_last_name\x18\x06 \x01(\tR\rstaffLastName\x12H\n\x0cservice_type\x18\x07 \x01(\x0e\x32%.moego.models.offering.v1.ServiceTypeR\x0bserviceType\x1a\x82\x04\n\x0e\x45valuationView\x12%\n\x0e\x61ppointment_id\x18\x01 \x01(\x03R\rappointmentId\x12\x15\n\x06pet_id\x18\x02 \x01(\x03R\x05petId\x12t\n\x0cpet_services\x18\x03 \x03(\x0b\x32Q.moego.api.appointment.v1.ListAppointmentCardResult.EvaluationView.PetServiceViewR\x0bpetServices\x1a\xbb\x02\n\x0ePetServiceView\x12$\n\x0epet_service_id\x18\x01 \x01(\x03R\x0cpetServiceId\x12!\n\x0cservice_name\x18\x02 \x01(\tR\x0bserviceName\x12\x1d\n\nstart_time\x18\x03 \x01(\x05R\tstartTime\x12\x19\n\x08\x65nd_time\x18\x04 \x01(\x05R\x07\x65ndTime\x12*\n\x11lodging_unit_name\x18\x05 \x01(\tR\x0flodgingUnitName\x12*\n\x11lodging_type_name\x18\x06 \x01(\tR\x0flodgingTypeName\x12&\n\x0flodging_type_id\x18\x07 \x01(\x03R\rlodgingTypeId\x12&\n\x0flodging_unit_id\x18\x08 \x01(\x03R\rlodgingUnitId\x1a\xac\x03\n\x0e\x44ogWalkingView\x12%\n\x0e\x61ppointment_id\x18\x01 \x01(\x03R\rappointmentId\x12\x15\n\x06pet_id\x18\x02 \x01(\x03R\x05petId\x12t\n\x0cpet_services\x18\x03 \x03(\x0b\x32Q.moego.api.appointment.v1.ListAppointmentCardResult.DogWalkingView.PetServiceViewR\x0bpetServices\x1a\xe5\x01\n\x0ePetServiceView\x12$\n\x0epet_service_id\x18\x01 \x01(\x03R\x0cpetServiceId\x12!\n\x0cservice_name\x18\x02 \x01(\tR\x0bserviceName\x12\x1d\n\nstart_time\x18\x03 \x01(\x05R\tstartTime\x12\x19\n\x08\x65nd_time\x18\x04 \x01(\x05R\x07\x65ndTime\x12(\n\x10staff_first_name\x18\x05 \x01(\tR\x0estaffFirstName\x12&\n\x0fstaff_last_name\x18\x06 \x01(\tR\rstaffLastName\x1a\xa8\x04\n\x07PetView\x12\x0e\n\x02id\x18\x01 \x01(\x03R\x02id\x12\x12\n\x04name\x18\x02 \x01(\tR\x04name\x12\x1f\n\x0b\x61vatar_path\x18\x03 \x01(\tR\navatarPath\x12;\n\x06gender\x18\x04 \x01(\x0e\x32#.moego.models.customer.v1.PetGenderR\x06gender\x12\x14\n\x05\x62reed\x18\x05 \x01(\tR\x05\x62reed\x12<\n\x08pet_type\x18\x06 \x01(\x0e\x32!.moego.models.customer.v1.PetTypeR\x07petType\x12\x16\n\x06weight\x18\x07 \x01(\tR\x06weight\x12 \n\x0cpet_code_ids\x18\x08 \x03(\x03R\npetCodeIds\x12\x1b\n\tpet_notes\x18\t \x03(\tR\x08petNotes\x12\x30\n\x14pet_appearance_color\x18\n \x01(\tR\x12petAppearanceColor\x12\x30\n\x14pet_appearance_notes\x18\x0b \x01(\tR\x12petAppearanceNotes\x12\x1b\n\tpet_fixed\x18\x0c \x01(\tR\x08petFixed\x12\x1d\n\nowner_name\x18\r \x01(\tR\townerName\x12(\n\x10owner_first_name\x18\x0e \x01(\tR\x0eownerFirstName\x12&\n\x0fowner_last_name\x18\x0f \x01(\tR\rownerLastName\x1aQ\n\x0f\x41ppointmentView\x12\x0e\n\x02id\x18\x01 \x01(\x03R\x02id\x12\x18\n\x07\x63omment\x18\x02 \x01(\tR\x07\x63omment\x12\x14\n\x05\x61lert\x18\x03 \x01(\tR\x05\x61lert\x1a\xc1\x01\n\x12LodgingTypeDayView\x12\x0e\n\x02id\x18\x01 \x01(\x03R\x02id\x12\x12\n\x04name\x18\x02 \x01(\tR\x04name\x12\x1e\n\x0bmax_pet_num\x18\x03 \x01(\x05R\tmaxPetNum\x12)\n\x11max_pet_total_num\x18\x04 \x01(\x05R\x0emaxPetTotalNum\x12(\n\x10occupied_pet_num\x18\x05 \x01(\x05R\x0eoccupiedPetNum\x12\x12\n\x04sort\x18\x06 \x01(\x05R\x04sort\"\xd3\x01\n\x14ListDailyTasksParams\x12(\n\x0b\x62usiness_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\nbusinessId\x12%\n\x04\x64\x61te\x18\x02 \x01(\x0b\x32\x11.google.type.DateR\x04\x64\x61te\x12j\n\x12service_item_types\x18\x03 \x03(\x0e\x32).moego.models.offering.v1.ServiceItemTypeB\x11\xfa\x42\x0e\x92\x01\x0b\x18\x01\"\x07\x82\x01\x04\x10\x01 \x00R\x10serviceItemTypes\"\x94\n\n\x14ListDailyTasksResult\x12R\n\x08\x66\x65\x65\x64ings\x18\x01 \x03(\x0b\x32\x36.moego.api.appointment.v1.ListDailyTasksResult.TaskRowR\x08\x66\x65\x65\x64ings\x12X\n\x0bmedications\x18\x02 \x03(\x0b\x32\x36.moego.api.appointment.v1.ListDailyTasksResult.TaskRowR\x0bmedications\x12R\n\x07\x61\x64\x64_ons\x18\x03 \x03(\x0b\x32\x39.moego.api.appointment.v1.ListDailyTasksResult.AddOnGroupR\x06\x61\x64\x64Ons\x1a~\n\nAddOnGroup\x12\x0e\n\x02id\x18\x01 \x01(\x03R\x02id\x12\x12\n\x04name\x18\x02 \x01(\tR\x04name\x12L\n\x05tasks\x18\x03 \x03(\x0b\x32\x36.moego.api.appointment.v1.ListDailyTasksResult.TaskRowR\x05tasks\x1a\x95\x03\n\x07TaskRow\x12P\n\x08\x63\x61tegory\x18\x01 \x01(\x0e\x32\x34.moego.models.appointment.v1.AppointmentTaskCategoryR\x08\x63\x61tegory\x12J\n\x03pet\x18\x02 \x01(\x0b\x32\x38.moego.api.appointment.v1.ListDailyTasksResult.PetColumnR\x03pet\x12V\n\x07service\x18\x03 \x01(\x0b\x32<.moego.api.appointment.v1.ListDailyTasksResult.ServiceColumnR\x07service\x12 \n\x0binstruction\x18\x04 \x01(\tR\x0binstruction\x12\x17\n\x04time\x18\x05 \x01(\x05H\x00R\x04time\x88\x01\x01\x12P\n\x05staff\x18\x06 \x01(\x0b\x32:.moego.api.appointment.v1.ListDailyTasksResult.StaffColumnR\x05staffB\x07\n\x05_time\x1a\xf9\x01\n\tPetColumn\x12\x0e\n\x02id\x18\x01 \x01(\x03R\x02id\x12\x12\n\x04name\x18\x02 \x01(\tR\x04name\x12<\n\x08pet_type\x18\x03 \x01(\x0e\x32!.moego.models.customer.v1.PetTypeR\x07petType\x12\x14\n\x05\x62reed\x18\x04 \x01(\tR\x05\x62reed\x12;\n\x06gender\x18\x05 \x01(\x0e\x32#.moego.models.customer.v1.PetGenderR\x06gender\x12\x1f\n\x0b\x61vatar_path\x18\x06 \x01(\tR\navatarPath\x12\x16\n\x06weight\x18\x07 \x01(\tR\x06weight\x1a\x8a\x01\n\rServiceColumn\x12\x0e\n\x02id\x18\x01 \x01(\x03R\x02id\x12\x12\n\x04name\x18\x02 \x01(\tR\x04name\x12U\n\x11service_item_type\x18\x03 \x01(\x0e\x32).moego.models.offering.v1.ServiceItemTypeR\x0fserviceItemType\x1aY\n\x0bStaffColumn\x12\x0e\n\x02id\x18\x01 \x01(\x03R\x02id\x12\x1d\n\nfirst_name\x18\x02 \x01(\tR\tfirstName\x12\x1b\n\tlast_name\x18\x03 \x01(\tR\x08lastName\"\xd3\x01\n\x1fListBoardingDepartureCardParams\x12(\n\x0b\x62usiness_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\nbusinessId\x12/\n\x04\x64\x61te\x18\x02 \x01(\x0b\x32\x11.google.type.DateB\x08\xfa\x42\x05\x8a\x01\x02\x10\x01R\x04\x64\x61te\x12\x39\n\x17is_pets_checked_in_only\x18\x03 \x01(\x08H\x00R\x13isPetsCheckedInOnly\x88\x01\x01\x42\x1a\n\x18_is_pets_checked_in_only\"\x84\x07\n\x1fListBoardingDepartureCardResult\x12\x64\n\tboardings\x18\x01 \x03(\x0b\x32\x46.moego.api.appointment.v1.ListBoardingDepartureCardResult.BoardingViewR\tboardings\x12O\n\x04pets\x18\x0b \x03(\x0b\x32;.moego.api.appointment.v1.ListAppointmentCardResult.PetViewR\x04pets\x12g\n\x0c\x61ppointments\x18\x0c \x03(\x0b\x32\x43.moego.api.appointment.v1.ListAppointmentCardResult.AppointmentViewR\x0c\x61ppointments\x12k\n\rlodging_types\x18\r \x03(\x0b\x32\x46.moego.api.appointment.v1.ListAppointmentCardResult.LodgingTypeDayViewR\x0clodgingTypes\x12O\n\rlodging_units\x18\x0e \x03(\x0b\x32*.moego.models.offering.v1.LodgingUnitModelR\x0clodgingUnits\x1a\x82\x03\n\x0c\x42oardingView\x12%\n\x0e\x61ppointment_id\x18\x01 \x01(\x03R\rappointmentId\x12\x15\n\x06pet_id\x18\x02 \x01(\x03R\x05petId\x12r\n\x0cpet_services\x18\x03 \x03(\x0b\x32O.moego.api.appointment.v1.ListAppointmentCardResult.BoardingView.PetServiceViewR\x0bpetServices\x12z\n\x0epet_belongings\x18\x04 \x03(\x0b\x32S.moego.api.appointment.v1.ListBoardingDepartureCardResult.BoardingView.PetBelongingR\rpetBelongings\x1a\x44\n\x0cPetBelonging\x12\x12\n\x04name\x18\x01 \x01(\tR\x04name\x12\x17\n\x04\x61rea\x18\x02 \x01(\tH\x00R\x04\x61rea\x88\x01\x01\x42\x07\n\x05_area\"\xc6\x01\n\x1cListDailyPlaygroupCardParams\x12(\n\x0b\x62usiness_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\nbusinessId\x12%\n\x04\x64\x61te\x18\x02 \x01(\x0b\x32\x11.google.type.DateR\x04\x64\x61te\x12\x39\n\x17is_pets_checked_in_only\x18\x03 \x01(\x08H\x00R\x13isPetsCheckedInOnly\x88\x01\x01\x42\x1a\n\x18_is_pets_checked_in_only\"\xdf\t\n\x1cListDailyPlaygroupCardResult\x12H\n\nplaygroups\x18\x01 \x03(\x0b\x32(.moego.models.offering.v1.PlaygroupModelR\nplaygroups\x12m\n\x0fplaygroup_views\x18\x02 \x03(\x0b\x32\x44.moego.api.appointment.v1.ListDailyPlaygroupCardResult.PlaygroupViewR\x0eplaygroupViews\x12R\n\x04pets\x18\x03 \x03(\x0b\x32>.moego.api.appointment.v1.ListDailyPlaygroupCardResult.PetViewR\x04pets\x1a\xc1\x01\n\rPlaygroupView\x12!\n\x0cplaygroup_id\x18\x01 \x01(\x03R\x0bplaygroupId\x12\x1d\n\npet_number\x18\x02 \x01(\x05R\tpetNumber\x12n\n\x0epet_playgroups\x18\x03 \x03(\x0b\x32G.moego.api.appointment.v1.ListDailyPlaygroupCardResult.PetPlaygroupViewR\rpetPlaygroups\x1ag\n\x10PetPlaygroupView\x12(\n\x10pet_playgroup_id\x18\x01 \x01(\x03R\x0epetPlaygroupId\x12\x15\n\x06pet_id\x18\x02 \x01(\x03R\x05petId\x12\x12\n\x04sort\x18\x03 \x01(\x05R\x04sort\x1a\x9f\x03\n\x07PetView\x12\x15\n\x06pet_id\x18\x01 \x01(\x03R\x05petId\x12\x19\n\x08pet_name\x18\x02 \x01(\tR\x07petName\x12<\n\x08pet_type\x18\x04 \x01(\x0e\x32!.moego.models.customer.v1.PetTypeR\x07petType\x12.\n\x13pet_playgroup_color\x18\x05 \x01(\tR\x11petPlaygroupColor\x12_\n\x08\x63ustomer\x18\x06 \x01(\x0b\x32\x43.moego.api.appointment.v1.ListDailyPlaygroupCardResult.CustomerViewR\x08\x63ustomer\x12v\n\x11pet_code_bindings\x18\x07 \x03(\x0b\x32J.moego.api.appointment.v1.ListDailyPlaygroupCardResult.PetCodeBindingsViewR\x0fpetCodeBindings\x12\x1b\n\tpet_breed\x18\x08 \x01(\tR\x08petBreed\x1ak\n\x0c\x43ustomerView\x12\x1f\n\x0b\x63ustomer_id\x18\x01 \x01(\x03R\ncustomerId\x12\x1d\n\nfirst_name\x18\x02 \x01(\tR\tfirstName\x12\x1b\n\tlast_name\x18\x03 \x01(\tR\x08lastName\x1av\n\x13PetCodeBindingsView\x12\"\n\x0c\x61\x62\x62reviation\x18\x01 \x01(\tR\x0c\x61\x62\x62reviation\x12\x14\n\x05\x63olor\x18\x02 \x01(\tR\x05\x63olor\x12%\n\x0eunique_comment\x18\x03 \x01(\tR\runiqueComment2\xa4\x04\n\x10PrintCardService\x12\x7f\n\x13ListAppointmentCard\x12\x33.moego.api.appointment.v1.ListAppointmentCardParams\x1a\x33.moego.api.appointment.v1.ListAppointmentCardResult\x12p\n\x0eListDailyTasks\x12..moego.api.appointment.v1.ListDailyTasksParams\x1a..moego.api.appointment.v1.ListDailyTasksResult\x12\x91\x01\n\x19ListBoardingDepartureCard\x12\x39.moego.api.appointment.v1.ListBoardingDepartureCardParams\x1a\x39.moego.api.appointment.v1.ListBoardingDepartureCardResult\x12\x88\x01\n\x16ListDailyPlaygroupCard\x12\x36.moego.api.appointment.v1.ListDailyPlaygroupCardParams\x1a\x36.moego.api.appointment.v1.ListDailyPlaygroupCardResultB\x84\x01\n com.moego.idl.api.appointment.v1P\x01Z^github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/appointment/v1;appointmentapipbb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'moego.api.appointment.v1.print_card_api_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n com.moego.idl.api.appointment.v1P\001Z^github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/appointment/v1;appointmentapipb'
  _globals['_LISTAPPOINTMENTCARDPARAMS'].fields_by_name['business_id']._loaded_options = None
  _globals['_LISTAPPOINTMENTCARDPARAMS'].fields_by_name['business_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_LISTAPPOINTMENTCARDPARAMS'].fields_by_name['types']._loaded_options = None
  _globals['_LISTAPPOINTMENTCARDPARAMS'].fields_by_name['types']._serialized_options = b'\372B\016\222\001\013\030\001\"\007\202\001\004\020\001 \000'
  _globals['_LISTDAILYTASKSPARAMS'].fields_by_name['business_id']._loaded_options = None
  _globals['_LISTDAILYTASKSPARAMS'].fields_by_name['business_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_LISTDAILYTASKSPARAMS'].fields_by_name['service_item_types']._loaded_options = None
  _globals['_LISTDAILYTASKSPARAMS'].fields_by_name['service_item_types']._serialized_options = b'\372B\016\222\001\013\030\001\"\007\202\001\004\020\001 \000'
  _globals['_LISTBOARDINGDEPARTURECARDPARAMS'].fields_by_name['business_id']._loaded_options = None
  _globals['_LISTBOARDINGDEPARTURECARDPARAMS'].fields_by_name['business_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_LISTBOARDINGDEPARTURECARDPARAMS'].fields_by_name['date']._loaded_options = None
  _globals['_LISTBOARDINGDEPARTURECARDPARAMS'].fields_by_name['date']._serialized_options = b'\372B\005\212\001\002\020\001'
  _globals['_LISTDAILYPLAYGROUPCARDPARAMS'].fields_by_name['business_id']._loaded_options = None
  _globals['_LISTDAILYPLAYGROUPCARDPARAMS'].fields_by_name['business_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_LISTAPPOINTMENTCARDPARAMS']._serialized_start=443
  _globals['_LISTAPPOINTMENTCARDPARAMS']._serialized_end=722
  _globals['_LISTAPPOINTMENTCARDRESULT']._serialized_start=725
  _globals['_LISTAPPOINTMENTCARDRESULT']._serialized_end=5082
  _globals['_LISTAPPOINTMENTCARDRESULT_BOARDINGVIEW']._serialized_start=1621
  _globals['_LISTAPPOINTMENTCARDRESULT_BOARDINGVIEW']._serialized_end=2288
  _globals['_LISTAPPOINTMENTCARDRESULT_BOARDINGVIEW_PETSERVICEVIEW']._serialized_start=1816
  _globals['_LISTAPPOINTMENTCARDRESULT_BOARDINGVIEW_PETSERVICEVIEW']._serialized_end=2288
  _globals['_LISTAPPOINTMENTCARDRESULT_DAYCAREVIEW']._serialized_start=2291
  _globals['_LISTAPPOINTMENTCARDRESULT_DAYCAREVIEW']._serialized_end=2799
  _globals['_LISTAPPOINTMENTCARDRESULT_DAYCAREVIEW_PETSERVICEVIEW']._serialized_start=2484
  _globals['_LISTAPPOINTMENTCARDRESULT_DAYCAREVIEW_PETSERVICEVIEW']._serialized_end=2799
  _globals['_LISTAPPOINTMENTCARDRESULT_GROOMINGVIEW']._serialized_start=2802
  _globals['_LISTAPPOINTMENTCARDRESULT_GROOMINGVIEW']._serialized_end=3300
  _globals['_LISTAPPOINTMENTCARDRESULT_GROOMINGVIEW_PETSERVICEVIEW']._serialized_start=2997
  _globals['_LISTAPPOINTMENTCARDRESULT_GROOMINGVIEW_PETSERVICEVIEW']._serialized_end=3300
  _globals['_LISTAPPOINTMENTCARDRESULT_EVALUATIONVIEW']._serialized_start=3303
  _globals['_LISTAPPOINTMENTCARDRESULT_EVALUATIONVIEW']._serialized_end=3817
  _globals['_LISTAPPOINTMENTCARDRESULT_EVALUATIONVIEW_PETSERVICEVIEW']._serialized_start=2484
  _globals['_LISTAPPOINTMENTCARDRESULT_EVALUATIONVIEW_PETSERVICEVIEW']._serialized_end=2799
  _globals['_LISTAPPOINTMENTCARDRESULT_DOGWALKINGVIEW']._serialized_start=3820
  _globals['_LISTAPPOINTMENTCARDRESULT_DOGWALKINGVIEW']._serialized_end=4248
  _globals['_LISTAPPOINTMENTCARDRESULT_DOGWALKINGVIEW_PETSERVICEVIEW']._serialized_start=2997
  _globals['_LISTAPPOINTMENTCARDRESULT_DOGWALKINGVIEW_PETSERVICEVIEW']._serialized_end=3226
  _globals['_LISTAPPOINTMENTCARDRESULT_PETVIEW']._serialized_start=4251
  _globals['_LISTAPPOINTMENTCARDRESULT_PETVIEW']._serialized_end=4803
  _globals['_LISTAPPOINTMENTCARDRESULT_APPOINTMENTVIEW']._serialized_start=4805
  _globals['_LISTAPPOINTMENTCARDRESULT_APPOINTMENTVIEW']._serialized_end=4886
  _globals['_LISTAPPOINTMENTCARDRESULT_LODGINGTYPEDAYVIEW']._serialized_start=4889
  _globals['_LISTAPPOINTMENTCARDRESULT_LODGINGTYPEDAYVIEW']._serialized_end=5082
  _globals['_LISTDAILYTASKSPARAMS']._serialized_start=5085
  _globals['_LISTDAILYTASKSPARAMS']._serialized_end=5296
  _globals['_LISTDAILYTASKSRESULT']._serialized_start=5299
  _globals['_LISTDAILYTASKSRESULT']._serialized_end=6599
  _globals['_LISTDAILYTASKSRESULT_ADDONGROUP']._serialized_start=5581
  _globals['_LISTDAILYTASKSRESULT_ADDONGROUP']._serialized_end=5707
  _globals['_LISTDAILYTASKSRESULT_TASKROW']._serialized_start=5710
  _globals['_LISTDAILYTASKSRESULT_TASKROW']._serialized_end=6115
  _globals['_LISTDAILYTASKSRESULT_PETCOLUMN']._serialized_start=6118
  _globals['_LISTDAILYTASKSRESULT_PETCOLUMN']._serialized_end=6367
  _globals['_LISTDAILYTASKSRESULT_SERVICECOLUMN']._serialized_start=6370
  _globals['_LISTDAILYTASKSRESULT_SERVICECOLUMN']._serialized_end=6508
  _globals['_LISTDAILYTASKSRESULT_STAFFCOLUMN']._serialized_start=6510
  _globals['_LISTDAILYTASKSRESULT_STAFFCOLUMN']._serialized_end=6599
  _globals['_LISTBOARDINGDEPARTURECARDPARAMS']._serialized_start=6602
  _globals['_LISTBOARDINGDEPARTURECARDPARAMS']._serialized_end=6813
  _globals['_LISTBOARDINGDEPARTURECARDRESULT']._serialized_start=6816
  _globals['_LISTBOARDINGDEPARTURECARDRESULT']._serialized_end=7716
  _globals['_LISTBOARDINGDEPARTURECARDRESULT_BOARDINGVIEW']._serialized_start=7330
  _globals['_LISTBOARDINGDEPARTURECARDRESULT_BOARDINGVIEW']._serialized_end=7716
  _globals['_LISTBOARDINGDEPARTURECARDRESULT_BOARDINGVIEW_PETBELONGING']._serialized_start=7648
  _globals['_LISTBOARDINGDEPARTURECARDRESULT_BOARDINGVIEW_PETBELONGING']._serialized_end=7716
  _globals['_LISTDAILYPLAYGROUPCARDPARAMS']._serialized_start=7719
  _globals['_LISTDAILYPLAYGROUPCARDPARAMS']._serialized_end=7917
  _globals['_LISTDAILYPLAYGROUPCARDRESULT']._serialized_start=7920
  _globals['_LISTDAILYPLAYGROUPCARDRESULT']._serialized_end=9167
  _globals['_LISTDAILYPLAYGROUPCARDRESULT_PLAYGROUPVIEW']._serialized_start=8222
  _globals['_LISTDAILYPLAYGROUPCARDRESULT_PLAYGROUPVIEW']._serialized_end=8415
  _globals['_LISTDAILYPLAYGROUPCARDRESULT_PETPLAYGROUPVIEW']._serialized_start=8417
  _globals['_LISTDAILYPLAYGROUPCARDRESULT_PETPLAYGROUPVIEW']._serialized_end=8520
  _globals['_LISTDAILYPLAYGROUPCARDRESULT_PETVIEW']._serialized_start=8523
  _globals['_LISTDAILYPLAYGROUPCARDRESULT_PETVIEW']._serialized_end=8938
  _globals['_LISTDAILYPLAYGROUPCARDRESULT_CUSTOMERVIEW']._serialized_start=8940
  _globals['_LISTDAILYPLAYGROUPCARDRESULT_CUSTOMERVIEW']._serialized_end=9047
  _globals['_LISTDAILYPLAYGROUPCARDRESULT_PETCODEBINDINGSVIEW']._serialized_start=9049
  _globals['_LISTDAILYPLAYGROUPCARDRESULT_PETCODEBINDINGSVIEW']._serialized_end=9167
  _globals['_PRINTCARDSERVICE']._serialized_start=9170
  _globals['_PRINTCARDSERVICE']._serialized_end=9718
# @@protoc_insertion_point(module_scope)
