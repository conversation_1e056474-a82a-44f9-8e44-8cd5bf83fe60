from moego.models.online_booking.v1 import ob_access_enums_pb2 as _ob_access_enums_pb2
from validate import validate_pb2 as _validate_pb2
from google.protobuf.internal import containers as _containers
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from collections.abc import Iterable as _Iterable, Mapping as _Mapping
from typing import ClassVar as _ClassVar, Optional as _Optional, Union as _Union

DESCRIPTOR: _descriptor.FileDescriptor

class OBCheckIdentifierRequest(_message.Message):
    __slots__ = ("name", "domain", "phone_number", "email", "include_possible_clients")
    NAME_FIELD_NUMBER: _ClassVar[int]
    DOMAIN_FIELD_NUMBER: _ClassVar[int]
    PHONE_NUMBER_FIELD_NUMBER: _ClassVar[int]
    EMAIL_FIELD_NUMBER: _ClassVar[int]
    INCLUDE_POSSIBLE_CLIENTS_FIELD_NUMBER: _ClassVar[int]
    name: str
    domain: str
    phone_number: str
    email: str
    include_possible_clients: bool
    def __init__(self, name: _Optional[str] = ..., domain: _Optional[str] = ..., phone_number: _Optional[str] = ..., email: _Optional[str] = ..., include_possible_clients: bool = ...) -> None: ...

class OBCheckIdentifierResponse(_message.Message):
    __slots__ = ("exist", "possible_clients")
    class PossibleClient(_message.Message):
        __slots__ = ("id", "first_name", "last_name", "avatar_path", "masked_phone_number")
        ID_FIELD_NUMBER: _ClassVar[int]
        FIRST_NAME_FIELD_NUMBER: _ClassVar[int]
        LAST_NAME_FIELD_NUMBER: _ClassVar[int]
        AVATAR_PATH_FIELD_NUMBER: _ClassVar[int]
        MASKED_PHONE_NUMBER_FIELD_NUMBER: _ClassVar[int]
        id: int
        first_name: str
        last_name: str
        avatar_path: str
        masked_phone_number: str
        def __init__(self, id: _Optional[int] = ..., first_name: _Optional[str] = ..., last_name: _Optional[str] = ..., avatar_path: _Optional[str] = ..., masked_phone_number: _Optional[str] = ...) -> None: ...
    EXIST_FIELD_NUMBER: _ClassVar[int]
    POSSIBLE_CLIENTS_FIELD_NUMBER: _ClassVar[int]
    exist: bool
    possible_clients: _containers.RepeatedCompositeFieldContainer[OBCheckIdentifierResponse.PossibleClient]
    def __init__(self, exist: bool = ..., possible_clients: _Optional[_Iterable[_Union[OBCheckIdentifierResponse.PossibleClient, _Mapping]]] = ...) -> None: ...

class OBGetVerificationSettingRequest(_message.Message):
    __slots__ = ("name", "domain")
    NAME_FIELD_NUMBER: _ClassVar[int]
    DOMAIN_FIELD_NUMBER: _ClassVar[int]
    name: str
    domain: str
    def __init__(self, name: _Optional[str] = ..., domain: _Optional[str] = ...) -> None: ...

class OBGetVerificationSettingResponse(_message.Message):
    __slots__ = ("existing_client_verification_code",)
    EXISTING_CLIENT_VERIFICATION_CODE_FIELD_NUMBER: _ClassVar[int]
    existing_client_verification_code: bool
    def __init__(self, existing_client_verification_code: bool = ...) -> None: ...

class OBSendVerificationCodeRequest(_message.Message):
    __slots__ = ("name", "domain", "phone_number", "possible_client_id", "additional_contact", "access_type")
    NAME_FIELD_NUMBER: _ClassVar[int]
    DOMAIN_FIELD_NUMBER: _ClassVar[int]
    PHONE_NUMBER_FIELD_NUMBER: _ClassVar[int]
    POSSIBLE_CLIENT_ID_FIELD_NUMBER: _ClassVar[int]
    ADDITIONAL_CONTACT_FIELD_NUMBER: _ClassVar[int]
    ACCESS_TYPE_FIELD_NUMBER: _ClassVar[int]
    name: str
    domain: str
    phone_number: str
    possible_client_id: int
    additional_contact: AdditionalContact
    access_type: _ob_access_enums_pb2.AccessType
    def __init__(self, name: _Optional[str] = ..., domain: _Optional[str] = ..., phone_number: _Optional[str] = ..., possible_client_id: _Optional[int] = ..., additional_contact: _Optional[_Union[AdditionalContact, _Mapping]] = ..., access_type: _Optional[_Union[_ob_access_enums_pb2.AccessType, str]] = ...) -> None: ...

class AdditionalContact(_message.Message):
    __slots__ = ("phone_number", "related_client_id")
    PHONE_NUMBER_FIELD_NUMBER: _ClassVar[int]
    RELATED_CLIENT_ID_FIELD_NUMBER: _ClassVar[int]
    phone_number: str
    related_client_id: int
    def __init__(self, phone_number: _Optional[str] = ..., related_client_id: _Optional[int] = ...) -> None: ...

class OBSendVerificationCodeResponse(_message.Message):
    __slots__ = ("token", "success")
    TOKEN_FIELD_NUMBER: _ClassVar[int]
    SUCCESS_FIELD_NUMBER: _ClassVar[int]
    token: str
    success: bool
    def __init__(self, token: _Optional[str] = ..., success: bool = ...) -> None: ...

class OBLoginRequest(_message.Message):
    __slots__ = ("name", "domain", "by_verification_code", "by_ppp", "ads_data")
    NAME_FIELD_NUMBER: _ClassVar[int]
    DOMAIN_FIELD_NUMBER: _ClassVar[int]
    BY_VERIFICATION_CODE_FIELD_NUMBER: _ClassVar[int]
    BY_PPP_FIELD_NUMBER: _ClassVar[int]
    ADS_DATA_FIELD_NUMBER: _ClassVar[int]
    name: str
    domain: str
    by_verification_code: OBLoginByVerificationCodeDef
    by_ppp: OBLoginByPPPTokenDef
    ads_data: OBLoginAdsDataDef
    def __init__(self, name: _Optional[str] = ..., domain: _Optional[str] = ..., by_verification_code: _Optional[_Union[OBLoginByVerificationCodeDef, _Mapping]] = ..., by_ppp: _Optional[_Union[OBLoginByPPPTokenDef, _Mapping]] = ..., ads_data: _Optional[_Union[OBLoginAdsDataDef, _Mapping]] = ...) -> None: ...

class OBLoginByVerificationCodeDef(_message.Message):
    __slots__ = ("phone_number", "possible_client_id", "additional_contact", "access_type", "code", "token")
    PHONE_NUMBER_FIELD_NUMBER: _ClassVar[int]
    POSSIBLE_CLIENT_ID_FIELD_NUMBER: _ClassVar[int]
    ADDITIONAL_CONTACT_FIELD_NUMBER: _ClassVar[int]
    ACCESS_TYPE_FIELD_NUMBER: _ClassVar[int]
    CODE_FIELD_NUMBER: _ClassVar[int]
    TOKEN_FIELD_NUMBER: _ClassVar[int]
    phone_number: str
    possible_client_id: int
    additional_contact: AdditionalContact
    access_type: _ob_access_enums_pb2.AccessType
    code: str
    token: str
    def __init__(self, phone_number: _Optional[str] = ..., possible_client_id: _Optional[int] = ..., additional_contact: _Optional[_Union[AdditionalContact, _Mapping]] = ..., access_type: _Optional[_Union[_ob_access_enums_pb2.AccessType, str]] = ..., code: _Optional[str] = ..., token: _Optional[str] = ...) -> None: ...

class OBLoginByPPPTokenDef(_message.Message):
    __slots__ = ("token",)
    TOKEN_FIELD_NUMBER: _ClassVar[int]
    token: str
    def __init__(self, token: _Optional[str] = ...) -> None: ...

class OBLoginAdsDataDef(_message.Message):
    __slots__ = ("google_ads_str",)
    GOOGLE_ADS_STR_FIELD_NUMBER: _ClassVar[int]
    google_ads_str: str
    def __init__(self, google_ads_str: _Optional[str] = ...) -> None: ...

class OBLoginResponse(_message.Message):
    __slots__ = ()
    def __init__(self) -> None: ...

class OBLogoutRequest(_message.Message):
    __slots__ = ("name", "domain")
    NAME_FIELD_NUMBER: _ClassVar[int]
    DOMAIN_FIELD_NUMBER: _ClassVar[int]
    name: str
    domain: str
    def __init__(self, name: _Optional[str] = ..., domain: _Optional[str] = ...) -> None: ...

class OBLogoutResponse(_message.Message):
    __slots__ = ()
    def __init__(self) -> None: ...

class OBDevModeRequest(_message.Message):
    __slots__ = ()
    def __init__(self) -> None: ...

class OBDevModeResponse(_message.Message):
    __slots__ = ("dev_mode",)
    DEV_MODE_FIELD_NUMBER: _ClassVar[int]
    dev_mode: bool
    def __init__(self, dev_mode: bool = ...) -> None: ...
