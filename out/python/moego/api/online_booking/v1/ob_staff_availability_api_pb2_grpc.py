# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

from moego.api.online_booking.v1 import ob_staff_availability_api_pb2 as moego_dot_api_dot_online__booking_dot_v1_dot_ob__staff__availability__api__pb2


class OBStaffAvailabilityServiceStub(object):
    """staff available service
    """

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.GetStaffAvailability = channel.unary_unary(
                '/moego.api.online_booking.v1.OBStaffAvailabilityService/GetStaffAvailability',
                request_serializer=moego_dot_api_dot_online__booking_dot_v1_dot_ob__staff__availability__api__pb2.GetStaffAvailabilityParams.SerializeToString,
                response_deserializer=moego_dot_api_dot_online__booking_dot_v1_dot_ob__staff__availability__api__pb2.GetStaffAvailabilityResult.FromString,
                _registered_method=True)
        self.UpdateStaffAvailability = channel.unary_unary(
                '/moego.api.online_booking.v1.OBStaffAvailabilityService/UpdateStaffAvailability',
                request_serializer=moego_dot_api_dot_online__booking_dot_v1_dot_ob__staff__availability__api__pb2.UpdateStaffAvailabilityParams.SerializeToString,
                response_deserializer=moego_dot_api_dot_online__booking_dot_v1_dot_ob__staff__availability__api__pb2.UpdateStaffAvailabilityResult.FromString,
                _registered_method=True)


class OBStaffAvailabilityServiceServicer(object):
    """staff available service
    """

    def GetStaffAvailability(self, request, context):
        """get staff available
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def UpdateStaffAvailability(self, request, context):
        """update staff available
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_OBStaffAvailabilityServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'GetStaffAvailability': grpc.unary_unary_rpc_method_handler(
                    servicer.GetStaffAvailability,
                    request_deserializer=moego_dot_api_dot_online__booking_dot_v1_dot_ob__staff__availability__api__pb2.GetStaffAvailabilityParams.FromString,
                    response_serializer=moego_dot_api_dot_online__booking_dot_v1_dot_ob__staff__availability__api__pb2.GetStaffAvailabilityResult.SerializeToString,
            ),
            'UpdateStaffAvailability': grpc.unary_unary_rpc_method_handler(
                    servicer.UpdateStaffAvailability,
                    request_deserializer=moego_dot_api_dot_online__booking_dot_v1_dot_ob__staff__availability__api__pb2.UpdateStaffAvailabilityParams.FromString,
                    response_serializer=moego_dot_api_dot_online__booking_dot_v1_dot_ob__staff__availability__api__pb2.UpdateStaffAvailabilityResult.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'moego.api.online_booking.v1.OBStaffAvailabilityService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('moego.api.online_booking.v1.OBStaffAvailabilityService', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class OBStaffAvailabilityService(object):
    """staff available service
    """

    @staticmethod
    def GetStaffAvailability(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.api.online_booking.v1.OBStaffAvailabilityService/GetStaffAvailability',
            moego_dot_api_dot_online__booking_dot_v1_dot_ob__staff__availability__api__pb2.GetStaffAvailabilityParams.SerializeToString,
            moego_dot_api_dot_online__booking_dot_v1_dot_ob__staff__availability__api__pb2.GetStaffAvailabilityResult.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def UpdateStaffAvailability(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.api.online_booking.v1.OBStaffAvailabilityService/UpdateStaffAvailability',
            moego_dot_api_dot_online__booking_dot_v1_dot_ob__staff__availability__api__pb2.UpdateStaffAvailabilityParams.SerializeToString,
            moego_dot_api_dot_online__booking_dot_v1_dot_ob__staff__availability__api__pb2.UpdateStaffAvailabilityResult.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)
