# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: moego/api/online_booking/v1/ob_pet_api.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'moego/api/online_booking/v1/ob_pet_api.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from moego.models.customer.v1 import customer_pet_defs_pb2 as moego_dot_models_dot_customer_dot_v1_dot_customer__pet__defs__pb2
from moego.models.customer.v1 import customer_pet_models_pb2 as moego_dot_models_dot_customer_dot_v1_dot_customer__pet__models__pb2
from moego.models.customer.v1 import customer_pet_vaccine_defs_pb2 as moego_dot_models_dot_customer_dot_v1_dot_customer__pet__vaccine__defs__pb2
from moego.models.customer.v1 import customer_pet_vaccine_models_pb2 as moego_dot_models_dot_customer_dot_v1_dot_customer__pet__vaccine__models__pb2
from validate import validate_pb2 as validate_dot_validate__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n,moego/api/online_booking/v1/ob_pet_api.proto\x12\x1bmoego.api.online_booking.v1\x1a\x30moego/models/customer/v1/customer_pet_defs.proto\x1a\x32moego/models/customer/v1/customer_pet_models.proto\x1a\x38moego/models/customer/v1/customer_pet_vaccine_defs.proto\x1a:moego/models/customer/v1/customer_pet_vaccine_models.proto\x1a\x17validate/validate.proto\"\x8f\x03\n\x0f\x41\x64\x64OBPetRequest\x12\x14\n\x04name\x18\x01 \x01(\tH\x00R\x04name\x12\x18\n\x06\x64omain\x18\x02 \x01(\tH\x00R\x06\x64omain\x12\x32\n\x03pet\x18\x03 \x01(\x0b\x32 .moego.models.customer.v1.PetDefR\x03pet\x12G\n\x0cvaccine_list\x18\x04 \x03(\x0b\x32$.moego.models.customer.v1.VaccineDefR\x0bvaccineList\x12v\n\x14pet_question_answers\x18\x05 \x03(\x0b\x32\x44.moego.api.online_booking.v1.AddOBPetRequest.PetQuestionAnswersEntryR\x12petQuestionAnswers\x1a\x45\n\x17PetQuestionAnswersEntry\x12\x10\n\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n\x05value\x18\x02 \x01(\tR\x05value:\x02\x38\x01\x42\x10\n\tanonymous\x12\x03\xf8\x42\x01\"\xb5\x03\n\x12UpdateOBPetRequest\x12\x14\n\x04name\x18\x01 \x01(\tH\x00R\x04name\x12\x18\n\x06\x64omain\x18\x02 \x01(\tH\x00R\x06\x64omain\x12\x1e\n\x06pet_id\x18\x03 \x01(\x05\x42\x07\xfa\x42\x04\x1a\x02 \x00R\x05petId\x12\x32\n\x03pet\x18\x04 \x01(\x0b\x32 .moego.models.customer.v1.PetDefR\x03pet\x12G\n\x0cvaccine_list\x18\x05 \x03(\x0b\x32$.moego.models.customer.v1.VaccineDefR\x0bvaccineList\x12y\n\x14pet_question_answers\x18\x06 \x03(\x0b\x32G.moego.api.online_booking.v1.UpdateOBPetRequest.PetQuestionAnswersEntryR\x12petQuestionAnswers\x1a\x45\n\x17PetQuestionAnswersEntry\x12\x10\n\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n\x05value\x18\x02 \x01(\tR\x05value:\x02\x38\x01\x42\x10\n\tanonymous\x12\x03\xf8\x42\x01\"s\n\x0fGetOBPetRequest\x12\x14\n\x04name\x18\x01 \x01(\tH\x00R\x04name\x12\x18\n\x06\x64omain\x18\x02 \x01(\tH\x00R\x06\x64omain\x12\x1e\n\x06pet_id\x18\x03 \x01(\x05\x42\x07\xfa\x42\x04\x1a\x02 \x00R\x05petIdB\x10\n\tanonymous\x12\x03\xf8\x42\x01\"W\n\x13GetOBPetListRequest\x12\x14\n\x04name\x18\x01 \x01(\tH\x00R\x04name\x12\x18\n\x06\x64omain\x18\x02 \x01(\tH\x00R\x06\x64omainB\x10\n\tanonymous\x12\x03\xf8\x42\x01\")\n\x10\x41\x64\x64OBPetResponse\x12\x15\n\x06pet_id\x18\x01 \x01(\x05R\x05petId\",\n\x13UpdateOBPetResponse\x12\x15\n\x06pet_id\x18\x01 \x01(\x05R\x05petId\"\xf8\x02\n\x11OBPetInfoResponse\x12H\n\x03pet\x18\x01 \x01(\x0b\x32\x36.moego.models.customer.v1.CustomerPetOnlineBookingViewR\x03pet\x12x\n\x14pet_question_answers\x18\x02 \x03(\x0b\x32\x46.moego.api.online_booking.v1.OBPetInfoResponse.PetQuestionAnswersEntryR\x12petQuestionAnswers\x12X\n\x0cvaccine_list\x18\x03 \x03(\x0b\x32\x35.moego.models.customer.v1.PetVaccineOnlineBookingViewR\x0bvaccineList\x1a\x45\n\x17PetQuestionAnswersEntry\x12\x10\n\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n\x05value\x18\x02 \x01(\tR\x05value:\x02\x38\x01\"W\n\x11OBPetListResponse\x12\x42\n\x04pets\x18\x01 \x03(\x0b\x32..moego.api.online_booking.v1.OBPetInfoResponseR\x04pets2\xc9\x03\n\x0cOBPetService\x12l\n\x0cGetOBPetInfo\x12,.moego.api.online_booking.v1.GetOBPetRequest\x1a..moego.api.online_booking.v1.OBPetInfoResponse\x12p\n\x0cGetOBPetList\x12\x30.moego.api.online_booking.v1.GetOBPetListRequest\x1a..moego.api.online_booking.v1.OBPetListResponse\x12g\n\x08\x41\x64\x64OBPet\x12,.moego.api.online_booking.v1.AddOBPetRequest\x1a-.moego.api.online_booking.v1.AddOBPetResponse\x12p\n\x0bUpdateOBPet\x12/.moego.api.online_booking.v1.UpdateOBPetRequest\x1a\x30.moego.api.online_booking.v1.UpdateOBPetResponseB\x8c\x01\n#com.moego.idl.api.online_booking.v1P\x01Zcgithub.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/online_booking/v1;onlinebookingapipbb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'moego.api.online_booking.v1.ob_pet_api_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n#com.moego.idl.api.online_booking.v1P\001Zcgithub.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/online_booking/v1;onlinebookingapipb'
  _globals['_ADDOBPETREQUEST_PETQUESTIONANSWERSENTRY']._loaded_options = None
  _globals['_ADDOBPETREQUEST_PETQUESTIONANSWERSENTRY']._serialized_options = b'8\001'
  _globals['_ADDOBPETREQUEST'].oneofs_by_name['anonymous']._loaded_options = None
  _globals['_ADDOBPETREQUEST'].oneofs_by_name['anonymous']._serialized_options = b'\370B\001'
  _globals['_UPDATEOBPETREQUEST_PETQUESTIONANSWERSENTRY']._loaded_options = None
  _globals['_UPDATEOBPETREQUEST_PETQUESTIONANSWERSENTRY']._serialized_options = b'8\001'
  _globals['_UPDATEOBPETREQUEST'].oneofs_by_name['anonymous']._loaded_options = None
  _globals['_UPDATEOBPETREQUEST'].oneofs_by_name['anonymous']._serialized_options = b'\370B\001'
  _globals['_UPDATEOBPETREQUEST'].fields_by_name['pet_id']._loaded_options = None
  _globals['_UPDATEOBPETREQUEST'].fields_by_name['pet_id']._serialized_options = b'\372B\004\032\002 \000'
  _globals['_GETOBPETREQUEST'].oneofs_by_name['anonymous']._loaded_options = None
  _globals['_GETOBPETREQUEST'].oneofs_by_name['anonymous']._serialized_options = b'\370B\001'
  _globals['_GETOBPETREQUEST'].fields_by_name['pet_id']._loaded_options = None
  _globals['_GETOBPETREQUEST'].fields_by_name['pet_id']._serialized_options = b'\372B\004\032\002 \000'
  _globals['_GETOBPETLISTREQUEST'].oneofs_by_name['anonymous']._loaded_options = None
  _globals['_GETOBPETLISTREQUEST'].oneofs_by_name['anonymous']._serialized_options = b'\370B\001'
  _globals['_OBPETINFORESPONSE_PETQUESTIONANSWERSENTRY']._loaded_options = None
  _globals['_OBPETINFORESPONSE_PETQUESTIONANSWERSENTRY']._serialized_options = b'8\001'
  _globals['_ADDOBPETREQUEST']._serialized_start=323
  _globals['_ADDOBPETREQUEST']._serialized_end=722
  _globals['_ADDOBPETREQUEST_PETQUESTIONANSWERSENTRY']._serialized_start=635
  _globals['_ADDOBPETREQUEST_PETQUESTIONANSWERSENTRY']._serialized_end=704
  _globals['_UPDATEOBPETREQUEST']._serialized_start=725
  _globals['_UPDATEOBPETREQUEST']._serialized_end=1162
  _globals['_UPDATEOBPETREQUEST_PETQUESTIONANSWERSENTRY']._serialized_start=635
  _globals['_UPDATEOBPETREQUEST_PETQUESTIONANSWERSENTRY']._serialized_end=704
  _globals['_GETOBPETREQUEST']._serialized_start=1164
  _globals['_GETOBPETREQUEST']._serialized_end=1279
  _globals['_GETOBPETLISTREQUEST']._serialized_start=1281
  _globals['_GETOBPETLISTREQUEST']._serialized_end=1368
  _globals['_ADDOBPETRESPONSE']._serialized_start=1370
  _globals['_ADDOBPETRESPONSE']._serialized_end=1411
  _globals['_UPDATEOBPETRESPONSE']._serialized_start=1413
  _globals['_UPDATEOBPETRESPONSE']._serialized_end=1457
  _globals['_OBPETINFORESPONSE']._serialized_start=1460
  _globals['_OBPETINFORESPONSE']._serialized_end=1836
  _globals['_OBPETINFORESPONSE_PETQUESTIONANSWERSENTRY']._serialized_start=635
  _globals['_OBPETINFORESPONSE_PETQUESTIONANSWERSENTRY']._serialized_end=704
  _globals['_OBPETLISTRESPONSE']._serialized_start=1838
  _globals['_OBPETLISTRESPONSE']._serialized_end=1925
  _globals['_OBPETSERVICE']._serialized_start=1928
  _globals['_OBPETSERVICE']._serialized_end=2385
# @@protoc_insertion_point(module_scope)
