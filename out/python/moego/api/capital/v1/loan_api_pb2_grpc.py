# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

from google.protobuf import empty_pb2 as google_dot_protobuf_dot_empty__pb2
from moego.api.capital.v1 import loan_api_pb2 as moego_dot_api_dot_capital_dot_v1_dot_loan__api__pb2


class LoanServiceStub(object):
    """The service for loan domain.
    """

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.GetLoanEligibility = channel.unary_unary(
                '/moego.api.capital.v1.LoanService/GetLoanEligibility',
                request_serializer=google_dot_protobuf_dot_empty__pb2.Empty.SerializeToString,
                response_deserializer=moego_dot_api_dot_capital_dot_v1_dot_loan__api__pb2.GetLoanEligibilityResponse.FromString,
                _registered_method=True)
        self.GetLoanEligibilityFlags = channel.unary_unary(
                '/moego.api.capital.v1.LoanService/GetLoanEligibilityFlags',
                request_serializer=moego_dot_api_dot_capital_dot_v1_dot_loan__api__pb2.GetLoanEligibilityFlagsRequest.SerializeToString,
                response_deserializer=moego_dot_api_dot_capital_dot_v1_dot_loan__api__pb2.GetLoanEligibilityFlagsResponse.FromString,
                _registered_method=True)
        self.GetOfferDetail = channel.unary_unary(
                '/moego.api.capital.v1.LoanService/GetOfferDetail',
                request_serializer=moego_dot_api_dot_capital_dot_v1_dot_loan__api__pb2.GetOfferDetailRequest.SerializeToString,
                response_deserializer=moego_dot_api_dot_capital_dot_v1_dot_loan__api__pb2.GetOfferDetailResponse.FromString,
                _registered_method=True)
        self.ListOffers = channel.unary_unary(
                '/moego.api.capital.v1.LoanService/ListOffers',
                request_serializer=google_dot_protobuf_dot_empty__pb2.Empty.SerializeToString,
                response_deserializer=moego_dot_api_dot_capital_dot_v1_dot_loan__api__pb2.ListOffersResponse.FromString,
                _registered_method=True)
        self.ListOffersV2 = channel.unary_unary(
                '/moego.api.capital.v1.LoanService/ListOffersV2',
                request_serializer=moego_dot_api_dot_capital_dot_v1_dot_loan__api__pb2.ListOffersV2Request.SerializeToString,
                response_deserializer=moego_dot_api_dot_capital_dot_v1_dot_loan__api__pb2.ListOffersV2Response.FromString,
                _registered_method=True)
        self.ListAllBusinessesOffers = channel.unary_unary(
                '/moego.api.capital.v1.LoanService/ListAllBusinessesOffers',
                request_serializer=google_dot_protobuf_dot_empty__pb2.Empty.SerializeToString,
                response_deserializer=moego_dot_api_dot_capital_dot_v1_dot_loan__api__pb2.ListAllBusinessesOffersResponse.FromString,
                _registered_method=True)
        self.ListAllBusinessesOffersV2 = channel.unary_unary(
                '/moego.api.capital.v1.LoanService/ListAllBusinessesOffersV2',
                request_serializer=moego_dot_api_dot_capital_dot_v1_dot_loan__api__pb2.ListAllBusinessesOffersV2Request.SerializeToString,
                response_deserializer=moego_dot_api_dot_capital_dot_v1_dot_loan__api__pb2.ListAllBusinessesOffersV2Response.FromString,
                _registered_method=True)
        self.GetRepaymentIntervals = channel.unary_unary(
                '/moego.api.capital.v1.LoanService/GetRepaymentIntervals',
                request_serializer=moego_dot_api_dot_capital_dot_v1_dot_loan__api__pb2.GetRepaymentIntervalsRequest.SerializeToString,
                response_deserializer=moego_dot_api_dot_capital_dot_v1_dot_loan__api__pb2.GetRepaymentIntervalsResponse.FromString,
                _registered_method=True)
        self.CreateLink = channel.unary_unary(
                '/moego.api.capital.v1.LoanService/CreateLink',
                request_serializer=moego_dot_api_dot_capital_dot_v1_dot_loan__api__pb2.CreateLinkRequest.SerializeToString,
                response_deserializer=moego_dot_api_dot_capital_dot_v1_dot_loan__api__pb2.CreateLinkResponse.FromString,
                _registered_method=True)
        self.GetRepayments = channel.unary_unary(
                '/moego.api.capital.v1.LoanService/GetRepayments',
                request_serializer=moego_dot_api_dot_capital_dot_v1_dot_loan__api__pb2.GetRepaymentsRequest.SerializeToString,
                response_deserializer=moego_dot_api_dot_capital_dot_v1_dot_loan__api__pb2.GetRepaymentsResponse.FromString,
                _registered_method=True)
        self.GetNotableUpdates = channel.unary_unary(
                '/moego.api.capital.v1.LoanService/GetNotableUpdates',
                request_serializer=google_dot_protobuf_dot_empty__pb2.Empty.SerializeToString,
                response_deserializer=moego_dot_api_dot_capital_dot_v1_dot_loan__api__pb2.GetNotableUpdatesResponse.FromString,
                _registered_method=True)
        self.DismissNotableUpdates = channel.unary_unary(
                '/moego.api.capital.v1.LoanService/DismissNotableUpdates',
                request_serializer=google_dot_protobuf_dot_empty__pb2.Empty.SerializeToString,
                response_deserializer=google_dot_protobuf_dot_empty__pb2.Empty.FromString,
                _registered_method=True)
        self.GetOfferNotableUpdates = channel.unary_unary(
                '/moego.api.capital.v1.LoanService/GetOfferNotableUpdates',
                request_serializer=moego_dot_api_dot_capital_dot_v1_dot_loan__api__pb2.GetOfferNotableUpdatesRequest.SerializeToString,
                response_deserializer=moego_dot_api_dot_capital_dot_v1_dot_loan__api__pb2.GetOfferNotableUpdatesResponse.FromString,
                _registered_method=True)
        self.DismissOfferNotableUpdate = channel.unary_unary(
                '/moego.api.capital.v1.LoanService/DismissOfferNotableUpdate',
                request_serializer=moego_dot_api_dot_capital_dot_v1_dot_loan__api__pb2.DismissOfferNotableUpdateRequest.SerializeToString,
                response_deserializer=moego_dot_api_dot_capital_dot_v1_dot_loan__api__pb2.DismissOfferNotableUpdateResponse.FromString,
                _registered_method=True)
        self.GetOnboardingStatus = channel.unary_unary(
                '/moego.api.capital.v1.LoanService/GetOnboardingStatus',
                request_serializer=moego_dot_api_dot_capital_dot_v1_dot_loan__api__pb2.GetOnboardingStatusRequest.SerializeToString,
                response_deserializer=moego_dot_api_dot_capital_dot_v1_dot_loan__api__pb2.GetOnboardingStatusResponse.FromString,
                _registered_method=True)
        self.AdminSyncOffer = channel.unary_unary(
                '/moego.api.capital.v1.LoanService/AdminSyncOffer',
                request_serializer=moego_dot_api_dot_capital_dot_v1_dot_loan__api__pb2.AdminSyncOfferRequest.SerializeToString,
                response_deserializer=google_dot_protobuf_dot_empty__pb2.Empty.FromString,
                _registered_method=True)


class LoanServiceServicer(object):
    """The service for loan domain.
    """

    def GetLoanEligibility(self, request, context):
        """Get the eligibility information for specific loan user.
        Frontend should not call this rpc later. Use GetLoanEligibilityFlags instead.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetLoanEligibilityFlags(self, request, context):
        """Get the eligibility information for specific loan user.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetOfferDetail(self, request, context):
        """Get the detail of a specific Loan Offer.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ListOffers(self, request, context):
        """List all Loan Offers for current business in all status.
        Frontend should not call this rpc later. Use ListOffersV2 instead. This method only returns Stripe offers.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ListOffersV2(self, request, context):
        """List all Loan Offers for current business in all status, with additional details.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ListAllBusinessesOffers(self, request, context):
        """List all Loan Offers of all businesses for current company, in all status.
        Frontend should not call this rpc later. Use ListAllBusinessesOffersV2 instead. This method only returns Stripe
        offers.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ListAllBusinessesOffersV2(self, request, context):
        """List all Loan Offers of all businesses for current company, in all status.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetRepaymentIntervals(self, request, context):
        """Get all intervals for a specified offer.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def CreateLink(self, request, context):
        """Create a link for a Loan Offer.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetRepayments(self, request, context):
        """Get the repayments for specific Loan Offer.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetNotableUpdates(self, request, context):
        """Get updates worth noting, for current business.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DismissNotableUpdates(self, request, context):
        """Dismiss notable updates, if any.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetOfferNotableUpdates(self, request, context):
        """Get updates worth noting, for offers.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DismissOfferNotableUpdate(self, request, context):
        """Dismiss an offer's notable update, if any.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetOnboardingStatus(self, request, context):
        """Get the onboarding status for current business/company/etc.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def AdminSyncOffer(self, request, context):
        """Admin-only: Sync offer and its transactions
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_LoanServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'GetLoanEligibility': grpc.unary_unary_rpc_method_handler(
                    servicer.GetLoanEligibility,
                    request_deserializer=google_dot_protobuf_dot_empty__pb2.Empty.FromString,
                    response_serializer=moego_dot_api_dot_capital_dot_v1_dot_loan__api__pb2.GetLoanEligibilityResponse.SerializeToString,
            ),
            'GetLoanEligibilityFlags': grpc.unary_unary_rpc_method_handler(
                    servicer.GetLoanEligibilityFlags,
                    request_deserializer=moego_dot_api_dot_capital_dot_v1_dot_loan__api__pb2.GetLoanEligibilityFlagsRequest.FromString,
                    response_serializer=moego_dot_api_dot_capital_dot_v1_dot_loan__api__pb2.GetLoanEligibilityFlagsResponse.SerializeToString,
            ),
            'GetOfferDetail': grpc.unary_unary_rpc_method_handler(
                    servicer.GetOfferDetail,
                    request_deserializer=moego_dot_api_dot_capital_dot_v1_dot_loan__api__pb2.GetOfferDetailRequest.FromString,
                    response_serializer=moego_dot_api_dot_capital_dot_v1_dot_loan__api__pb2.GetOfferDetailResponse.SerializeToString,
            ),
            'ListOffers': grpc.unary_unary_rpc_method_handler(
                    servicer.ListOffers,
                    request_deserializer=google_dot_protobuf_dot_empty__pb2.Empty.FromString,
                    response_serializer=moego_dot_api_dot_capital_dot_v1_dot_loan__api__pb2.ListOffersResponse.SerializeToString,
            ),
            'ListOffersV2': grpc.unary_unary_rpc_method_handler(
                    servicer.ListOffersV2,
                    request_deserializer=moego_dot_api_dot_capital_dot_v1_dot_loan__api__pb2.ListOffersV2Request.FromString,
                    response_serializer=moego_dot_api_dot_capital_dot_v1_dot_loan__api__pb2.ListOffersV2Response.SerializeToString,
            ),
            'ListAllBusinessesOffers': grpc.unary_unary_rpc_method_handler(
                    servicer.ListAllBusinessesOffers,
                    request_deserializer=google_dot_protobuf_dot_empty__pb2.Empty.FromString,
                    response_serializer=moego_dot_api_dot_capital_dot_v1_dot_loan__api__pb2.ListAllBusinessesOffersResponse.SerializeToString,
            ),
            'ListAllBusinessesOffersV2': grpc.unary_unary_rpc_method_handler(
                    servicer.ListAllBusinessesOffersV2,
                    request_deserializer=moego_dot_api_dot_capital_dot_v1_dot_loan__api__pb2.ListAllBusinessesOffersV2Request.FromString,
                    response_serializer=moego_dot_api_dot_capital_dot_v1_dot_loan__api__pb2.ListAllBusinessesOffersV2Response.SerializeToString,
            ),
            'GetRepaymentIntervals': grpc.unary_unary_rpc_method_handler(
                    servicer.GetRepaymentIntervals,
                    request_deserializer=moego_dot_api_dot_capital_dot_v1_dot_loan__api__pb2.GetRepaymentIntervalsRequest.FromString,
                    response_serializer=moego_dot_api_dot_capital_dot_v1_dot_loan__api__pb2.GetRepaymentIntervalsResponse.SerializeToString,
            ),
            'CreateLink': grpc.unary_unary_rpc_method_handler(
                    servicer.CreateLink,
                    request_deserializer=moego_dot_api_dot_capital_dot_v1_dot_loan__api__pb2.CreateLinkRequest.FromString,
                    response_serializer=moego_dot_api_dot_capital_dot_v1_dot_loan__api__pb2.CreateLinkResponse.SerializeToString,
            ),
            'GetRepayments': grpc.unary_unary_rpc_method_handler(
                    servicer.GetRepayments,
                    request_deserializer=moego_dot_api_dot_capital_dot_v1_dot_loan__api__pb2.GetRepaymentsRequest.FromString,
                    response_serializer=moego_dot_api_dot_capital_dot_v1_dot_loan__api__pb2.GetRepaymentsResponse.SerializeToString,
            ),
            'GetNotableUpdates': grpc.unary_unary_rpc_method_handler(
                    servicer.GetNotableUpdates,
                    request_deserializer=google_dot_protobuf_dot_empty__pb2.Empty.FromString,
                    response_serializer=moego_dot_api_dot_capital_dot_v1_dot_loan__api__pb2.GetNotableUpdatesResponse.SerializeToString,
            ),
            'DismissNotableUpdates': grpc.unary_unary_rpc_method_handler(
                    servicer.DismissNotableUpdates,
                    request_deserializer=google_dot_protobuf_dot_empty__pb2.Empty.FromString,
                    response_serializer=google_dot_protobuf_dot_empty__pb2.Empty.SerializeToString,
            ),
            'GetOfferNotableUpdates': grpc.unary_unary_rpc_method_handler(
                    servicer.GetOfferNotableUpdates,
                    request_deserializer=moego_dot_api_dot_capital_dot_v1_dot_loan__api__pb2.GetOfferNotableUpdatesRequest.FromString,
                    response_serializer=moego_dot_api_dot_capital_dot_v1_dot_loan__api__pb2.GetOfferNotableUpdatesResponse.SerializeToString,
            ),
            'DismissOfferNotableUpdate': grpc.unary_unary_rpc_method_handler(
                    servicer.DismissOfferNotableUpdate,
                    request_deserializer=moego_dot_api_dot_capital_dot_v1_dot_loan__api__pb2.DismissOfferNotableUpdateRequest.FromString,
                    response_serializer=moego_dot_api_dot_capital_dot_v1_dot_loan__api__pb2.DismissOfferNotableUpdateResponse.SerializeToString,
            ),
            'GetOnboardingStatus': grpc.unary_unary_rpc_method_handler(
                    servicer.GetOnboardingStatus,
                    request_deserializer=moego_dot_api_dot_capital_dot_v1_dot_loan__api__pb2.GetOnboardingStatusRequest.FromString,
                    response_serializer=moego_dot_api_dot_capital_dot_v1_dot_loan__api__pb2.GetOnboardingStatusResponse.SerializeToString,
            ),
            'AdminSyncOffer': grpc.unary_unary_rpc_method_handler(
                    servicer.AdminSyncOffer,
                    request_deserializer=moego_dot_api_dot_capital_dot_v1_dot_loan__api__pb2.AdminSyncOfferRequest.FromString,
                    response_serializer=google_dot_protobuf_dot_empty__pb2.Empty.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'moego.api.capital.v1.LoanService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('moego.api.capital.v1.LoanService', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class LoanService(object):
    """The service for loan domain.
    """

    @staticmethod
    def GetLoanEligibility(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.api.capital.v1.LoanService/GetLoanEligibility',
            google_dot_protobuf_dot_empty__pb2.Empty.SerializeToString,
            moego_dot_api_dot_capital_dot_v1_dot_loan__api__pb2.GetLoanEligibilityResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetLoanEligibilityFlags(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.api.capital.v1.LoanService/GetLoanEligibilityFlags',
            moego_dot_api_dot_capital_dot_v1_dot_loan__api__pb2.GetLoanEligibilityFlagsRequest.SerializeToString,
            moego_dot_api_dot_capital_dot_v1_dot_loan__api__pb2.GetLoanEligibilityFlagsResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetOfferDetail(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.api.capital.v1.LoanService/GetOfferDetail',
            moego_dot_api_dot_capital_dot_v1_dot_loan__api__pb2.GetOfferDetailRequest.SerializeToString,
            moego_dot_api_dot_capital_dot_v1_dot_loan__api__pb2.GetOfferDetailResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def ListOffers(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.api.capital.v1.LoanService/ListOffers',
            google_dot_protobuf_dot_empty__pb2.Empty.SerializeToString,
            moego_dot_api_dot_capital_dot_v1_dot_loan__api__pb2.ListOffersResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def ListOffersV2(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.api.capital.v1.LoanService/ListOffersV2',
            moego_dot_api_dot_capital_dot_v1_dot_loan__api__pb2.ListOffersV2Request.SerializeToString,
            moego_dot_api_dot_capital_dot_v1_dot_loan__api__pb2.ListOffersV2Response.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def ListAllBusinessesOffers(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.api.capital.v1.LoanService/ListAllBusinessesOffers',
            google_dot_protobuf_dot_empty__pb2.Empty.SerializeToString,
            moego_dot_api_dot_capital_dot_v1_dot_loan__api__pb2.ListAllBusinessesOffersResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def ListAllBusinessesOffersV2(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.api.capital.v1.LoanService/ListAllBusinessesOffersV2',
            moego_dot_api_dot_capital_dot_v1_dot_loan__api__pb2.ListAllBusinessesOffersV2Request.SerializeToString,
            moego_dot_api_dot_capital_dot_v1_dot_loan__api__pb2.ListAllBusinessesOffersV2Response.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetRepaymentIntervals(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.api.capital.v1.LoanService/GetRepaymentIntervals',
            moego_dot_api_dot_capital_dot_v1_dot_loan__api__pb2.GetRepaymentIntervalsRequest.SerializeToString,
            moego_dot_api_dot_capital_dot_v1_dot_loan__api__pb2.GetRepaymentIntervalsResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def CreateLink(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.api.capital.v1.LoanService/CreateLink',
            moego_dot_api_dot_capital_dot_v1_dot_loan__api__pb2.CreateLinkRequest.SerializeToString,
            moego_dot_api_dot_capital_dot_v1_dot_loan__api__pb2.CreateLinkResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetRepayments(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.api.capital.v1.LoanService/GetRepayments',
            moego_dot_api_dot_capital_dot_v1_dot_loan__api__pb2.GetRepaymentsRequest.SerializeToString,
            moego_dot_api_dot_capital_dot_v1_dot_loan__api__pb2.GetRepaymentsResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetNotableUpdates(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.api.capital.v1.LoanService/GetNotableUpdates',
            google_dot_protobuf_dot_empty__pb2.Empty.SerializeToString,
            moego_dot_api_dot_capital_dot_v1_dot_loan__api__pb2.GetNotableUpdatesResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def DismissNotableUpdates(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.api.capital.v1.LoanService/DismissNotableUpdates',
            google_dot_protobuf_dot_empty__pb2.Empty.SerializeToString,
            google_dot_protobuf_dot_empty__pb2.Empty.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetOfferNotableUpdates(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.api.capital.v1.LoanService/GetOfferNotableUpdates',
            moego_dot_api_dot_capital_dot_v1_dot_loan__api__pb2.GetOfferNotableUpdatesRequest.SerializeToString,
            moego_dot_api_dot_capital_dot_v1_dot_loan__api__pb2.GetOfferNotableUpdatesResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def DismissOfferNotableUpdate(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.api.capital.v1.LoanService/DismissOfferNotableUpdate',
            moego_dot_api_dot_capital_dot_v1_dot_loan__api__pb2.DismissOfferNotableUpdateRequest.SerializeToString,
            moego_dot_api_dot_capital_dot_v1_dot_loan__api__pb2.DismissOfferNotableUpdateResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetOnboardingStatus(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.api.capital.v1.LoanService/GetOnboardingStatus',
            moego_dot_api_dot_capital_dot_v1_dot_loan__api__pb2.GetOnboardingStatusRequest.SerializeToString,
            moego_dot_api_dot_capital_dot_v1_dot_loan__api__pb2.GetOnboardingStatusResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def AdminSyncOffer(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.api.capital.v1.LoanService/AdminSyncOffer',
            moego_dot_api_dot_capital_dot_v1_dot_loan__api__pb2.AdminSyncOfferRequest.SerializeToString,
            google_dot_protobuf_dot_empty__pb2.Empty.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)
