# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: moego/api/business_customer/v1/business_pet_incident_type_api.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'moego/api/business_customer/v1/business_pet_incident_type_api.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from moego.models.business_customer.v1 import business_pet_incident_type_defs_pb2 as moego_dot_models_dot_business__customer_dot_v1_dot_business__pet__incident__type__defs__pb2
from moego.models.business_customer.v1 import business_pet_incident_type_models_pb2 as moego_dot_models_dot_business__customer_dot_v1_dot_business__pet__incident__type__models__pb2
from validate import validate_pb2 as validate_dot_validate__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\nCmoego/api/business_customer/v1/business_pet_incident_type_api.proto\x12\x1emoego.api.business_customer.v1\x1aGmoego/models/business_customer/v1/business_pet_incident_type_defs.proto\x1aImoego/models/business_customer/v1/business_pet_incident_type_models.proto\x1a\x17validate/validate.proto\"e\n\x19ListPetIncidentTypeParams\x12\x31\n\x12is_include_deleted\x18\x01 \x01(\x08H\x00R\x10isIncludeDeleted\x88\x01\x01\x42\x15\n\x13_is_include_deleted\"\x83\x01\n\x19ListPetIncidentTypeResult\x12\x66\n\x0eincident_types\x18\x01 \x03(\x0b\x32?.moego.models.business_customer.v1.BusinessPetIncidentTypeModelR\rincidentTypes\"\x91\x01\n\x1b\x43reatePetIncidentTypeParams\x12r\n\rincident_type\x18\x01 \x01(\x0b\x32\x43.moego.models.business_customer.v1.BusinessPetIncidentTypeCreateDefB\x08\xfa\x42\x05\x8a\x01\x02\x10\x01R\x0cincidentType\"\x83\x01\n\x1b\x43reatePetIncidentTypeResult\x12\x64\n\rincident_type\x18\x01 \x01(\x0b\x32?.moego.models.business_customer.v1.BusinessPetIncidentTypeModelR\x0cincidentType\"\xaa\x01\n\x1bUpdatePetIncidentTypeParams\x12\x17\n\x02id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\x02id\x12r\n\rincident_type\x18\x02 \x01(\x0b\x32\x43.moego.models.business_customer.v1.BusinessPetIncidentTypeUpdateDefB\x08\xfa\x42\x05\x8a\x01\x02\x10\x01R\x0cincidentType\"\x1d\n\x1bUpdatePetIncidentTypeResult\"=\n\x19SortPetIncidentTypeParams\x12 \n\x03ids\x18\x01 \x03(\x03\x42\x0e\xfa\x42\x0b\x92\x01\x08\x18\x01\"\x04\"\x02 \x00R\x03ids\"\x1b\n\x19SortPetIncidentTypeResult\"6\n\x1b\x44\x65letePetIncidentTypeParams\x12\x17\n\x02id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\x02id\"\x1d\n\x1b\x44\x65letePetIncidentTypeResult2\xf8\x05\n\x1e\x42usinessPetIncidentTypeService\x12\x8b\x01\n\x13ListPetIncidentType\x12\x39.moego.api.business_customer.v1.ListPetIncidentTypeParams\x1a\x39.moego.api.business_customer.v1.ListPetIncidentTypeResult\x12\x91\x01\n\x15\x43reatePetIncidentType\x12;.moego.api.business_customer.v1.CreatePetIncidentTypeParams\x1a;.moego.api.business_customer.v1.CreatePetIncidentTypeResult\x12\x91\x01\n\x15UpdatePetIncidentType\x12;.moego.api.business_customer.v1.UpdatePetIncidentTypeParams\x1a;.moego.api.business_customer.v1.UpdatePetIncidentTypeResult\x12\x8b\x01\n\x13SortPetIncidentType\x12\x39.moego.api.business_customer.v1.SortPetIncidentTypeParams\x1a\x39.moego.api.business_customer.v1.SortPetIncidentTypeResult\x12\x91\x01\n\x15\x44\x65letePetIncidentType\x12;.moego.api.business_customer.v1.DeletePetIncidentTypeParams\x1a;.moego.api.business_customer.v1.DeletePetIncidentTypeResultB\x95\x01\n&com.moego.idl.api.business_customer.v1P\x01Zigithub.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/business_customer/v1;businesscustomerapipbb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'moego.api.business_customer.v1.business_pet_incident_type_api_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n&com.moego.idl.api.business_customer.v1P\001Zigithub.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/business_customer/v1;businesscustomerapipb'
  _globals['_CREATEPETINCIDENTTYPEPARAMS'].fields_by_name['incident_type']._loaded_options = None
  _globals['_CREATEPETINCIDENTTYPEPARAMS'].fields_by_name['incident_type']._serialized_options = b'\372B\005\212\001\002\020\001'
  _globals['_UPDATEPETINCIDENTTYPEPARAMS'].fields_by_name['id']._loaded_options = None
  _globals['_UPDATEPETINCIDENTTYPEPARAMS'].fields_by_name['id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_UPDATEPETINCIDENTTYPEPARAMS'].fields_by_name['incident_type']._loaded_options = None
  _globals['_UPDATEPETINCIDENTTYPEPARAMS'].fields_by_name['incident_type']._serialized_options = b'\372B\005\212\001\002\020\001'
  _globals['_SORTPETINCIDENTTYPEPARAMS'].fields_by_name['ids']._loaded_options = None
  _globals['_SORTPETINCIDENTTYPEPARAMS'].fields_by_name['ids']._serialized_options = b'\372B\013\222\001\010\030\001\"\004\"\002 \000'
  _globals['_DELETEPETINCIDENTTYPEPARAMS'].fields_by_name['id']._loaded_options = None
  _globals['_DELETEPETINCIDENTTYPEPARAMS'].fields_by_name['id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_LISTPETINCIDENTTYPEPARAMS']._serialized_start=276
  _globals['_LISTPETINCIDENTTYPEPARAMS']._serialized_end=377
  _globals['_LISTPETINCIDENTTYPERESULT']._serialized_start=380
  _globals['_LISTPETINCIDENTTYPERESULT']._serialized_end=511
  _globals['_CREATEPETINCIDENTTYPEPARAMS']._serialized_start=514
  _globals['_CREATEPETINCIDENTTYPEPARAMS']._serialized_end=659
  _globals['_CREATEPETINCIDENTTYPERESULT']._serialized_start=662
  _globals['_CREATEPETINCIDENTTYPERESULT']._serialized_end=793
  _globals['_UPDATEPETINCIDENTTYPEPARAMS']._serialized_start=796
  _globals['_UPDATEPETINCIDENTTYPEPARAMS']._serialized_end=966
  _globals['_UPDATEPETINCIDENTTYPERESULT']._serialized_start=968
  _globals['_UPDATEPETINCIDENTTYPERESULT']._serialized_end=997
  _globals['_SORTPETINCIDENTTYPEPARAMS']._serialized_start=999
  _globals['_SORTPETINCIDENTTYPEPARAMS']._serialized_end=1060
  _globals['_SORTPETINCIDENTTYPERESULT']._serialized_start=1062
  _globals['_SORTPETINCIDENTTYPERESULT']._serialized_end=1089
  _globals['_DELETEPETINCIDENTTYPEPARAMS']._serialized_start=1091
  _globals['_DELETEPETINCIDENTTYPEPARAMS']._serialized_end=1145
  _globals['_DELETEPETINCIDENTTYPERESULT']._serialized_start=1147
  _globals['_DELETEPETINCIDENTTYPERESULT']._serialized_end=1176
  _globals['_BUSINESSPETINCIDENTTYPESERVICE']._serialized_start=1179
  _globals['_BUSINESSPETINCIDENTTYPESERVICE']._serialized_end=1939
# @@protoc_insertion_point(module_scope)
