from moego.models.business_customer.v1 import business_pet_medication_schedule_defs_pb2 as _business_pet_medication_schedule_defs_pb2
from validate import validate_pb2 as _validate_pb2
from google.protobuf.internal import containers as _containers
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from collections.abc import Iterable as _Iterable, Mapping as _Mapping
from typing import ClassVar as _ClassVar, Optional as _Optional, Union as _Union

DESCRIPTOR: _descriptor.FileDescriptor

class CreateMedicationScheduleParams(_message.Message):
    __slots__ = ("medication_schedule",)
    MEDICATION_SCHEDULE_FIELD_NUMBER: _ClassVar[int]
    medication_schedule: _business_pet_medication_schedule_defs_pb2.BusinessPetMedicationScheduleDef
    def __init__(self, medication_schedule: _Optional[_Union[_business_pet_medication_schedule_defs_pb2.BusinessPetMedicationScheduleDef, _Mapping]] = ...) -> None: ...

class CreateMedicationScheduleResult(_message.Message):
    __slots__ = ("id",)
    ID_FIELD_NUMBER: _ClassVar[int]
    id: int
    def __init__(self, id: _Optional[int] = ...) -> None: ...

class UpdateMedicationScheduleParams(_message.Message):
    __slots__ = ("id", "medication_schedule")
    ID_FIELD_NUMBER: _ClassVar[int]
    MEDICATION_SCHEDULE_FIELD_NUMBER: _ClassVar[int]
    id: int
    medication_schedule: _business_pet_medication_schedule_defs_pb2.BusinessPetMedicationScheduleDef
    def __init__(self, id: _Optional[int] = ..., medication_schedule: _Optional[_Union[_business_pet_medication_schedule_defs_pb2.BusinessPetMedicationScheduleDef, _Mapping]] = ...) -> None: ...

class UpdateMedicationScheduleResult(_message.Message):
    __slots__ = ()
    def __init__(self) -> None: ...

class DeleteMedicationScheduleParams(_message.Message):
    __slots__ = ("id",)
    ID_FIELD_NUMBER: _ClassVar[int]
    id: int
    def __init__(self, id: _Optional[int] = ...) -> None: ...

class DeleteMedicationScheduleResult(_message.Message):
    __slots__ = ()
    def __init__(self) -> None: ...

class ListPetMedicationScheduleParams(_message.Message):
    __slots__ = ("pet_id",)
    PET_ID_FIELD_NUMBER: _ClassVar[int]
    pet_id: int
    def __init__(self, pet_id: _Optional[int] = ...) -> None: ...

class ListPetMedicationScheduleResult(_message.Message):
    __slots__ = ("medication_schedules",)
    MEDICATION_SCHEDULES_FIELD_NUMBER: _ClassVar[int]
    medication_schedules: _containers.RepeatedCompositeFieldContainer[_business_pet_medication_schedule_defs_pb2.BusinessPetMedicationScheduleView]
    def __init__(self, medication_schedules: _Optional[_Iterable[_Union[_business_pet_medication_schedule_defs_pb2.BusinessPetMedicationScheduleView, _Mapping]]] = ...) -> None: ...
