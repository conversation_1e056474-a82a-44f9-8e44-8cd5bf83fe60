# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

from moego.api.business_customer.v1 import business_customer_retention_api_pb2 as moego_dot_api_dot_business__customer_dot_v1_dot_business__customer__retention__api__pb2


class BusinessCustomerRetentionServiceStub(object):
    """BusinessCustomerRetentionService: Client & Pets retention api
    """

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.FetchRetentionData = channel.unary_unary(
                '/moego.api.business_customer.v1.BusinessCustomerRetentionService/FetchRetentionData',
                request_serializer=moego_dot_api_dot_business__customer_dot_v1_dot_business__customer__retention__api__pb2.FetchRetentionDataParams.SerializeToString,
                response_deserializer=moego_dot_api_dot_business__customer_dot_v1_dot_business__customer__retention__api__pb2.FetchRetentionDataResult.FromString,
                _registered_method=True)


class BusinessCustomerRetentionServiceServicer(object):
    """BusinessCustomerRetentionService: Client & Pets retention api
    """

    def FetchRetentionData(self, request, context):
        """Fetch retention data
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_BusinessCustomerRetentionServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'FetchRetentionData': grpc.unary_unary_rpc_method_handler(
                    servicer.FetchRetentionData,
                    request_deserializer=moego_dot_api_dot_business__customer_dot_v1_dot_business__customer__retention__api__pb2.FetchRetentionDataParams.FromString,
                    response_serializer=moego_dot_api_dot_business__customer_dot_v1_dot_business__customer__retention__api__pb2.FetchRetentionDataResult.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'moego.api.business_customer.v1.BusinessCustomerRetentionService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('moego.api.business_customer.v1.BusinessCustomerRetentionService', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class BusinessCustomerRetentionService(object):
    """BusinessCustomerRetentionService: Client & Pets retention api
    """

    @staticmethod
    def FetchRetentionData(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.api.business_customer.v1.BusinessCustomerRetentionService/FetchRetentionData',
            moego_dot_api_dot_business__customer_dot_v1_dot_business__customer__retention__api__pb2.FetchRetentionDataParams.SerializeToString,
            moego_dot_api_dot_business__customer_dot_v1_dot_business__customer__retention__api__pb2.FetchRetentionDataResult.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)
