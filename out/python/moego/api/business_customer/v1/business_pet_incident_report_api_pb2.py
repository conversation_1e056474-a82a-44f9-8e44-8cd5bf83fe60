# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: moego/api/business_customer/v1/business_pet_incident_report_api.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'moego/api/business_customer/v1/business_pet_incident_report_api.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from moego.models.business_customer.v1 import business_pet_incident_report_defs_pb2 as moego_dot_models_dot_business__customer_dot_v1_dot_business__pet__incident__report__defs__pb2
from moego.models.business_customer.v1 import business_pet_incident_report_models_pb2 as moego_dot_models_dot_business__customer_dot_v1_dot_business__pet__incident__report__models__pb2
from validate import validate_pb2 as validate_dot_validate__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\nEmoego/api/business_customer/v1/business_pet_incident_report_api.proto\x12\x1emoego.api.business_customer.v1\x1aImoego/models/business_customer/v1/business_pet_incident_report_defs.proto\x1aKmoego/models/business_customer/v1/business_pet_incident_report_models.proto\x1a\x17validate/validate.proto\"|\n\x1bListPetIncidentReportParams\x12\x1e\n\x06pet_id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\x05petId\x12-\n\x0b\x62usiness_id\x18\x02 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00H\x00R\nbusinessId\x88\x01\x01\x42\x0e\n\x0c_business_id\"\x8b\x01\n\x1bListPetIncidentReportResult\x12l\n\x10incident_reports\x18\x01 \x03(\x0b\x32\x41.moego.models.business_customer.v1.BusinessPetIncidentReportModelR\x0fincidentReports\"\x99\x01\n\x1d\x43reatePetIncidentReportParams\x12x\n\x0fincident_report\x18\x01 \x01(\x0b\x32\x45.moego.models.business_customer.v1.BusinessPetIncidentReportCreateDefB\x08\xfa\x42\x05\x8a\x01\x02\x10\x01R\x0eincidentReport\"\x8b\x01\n\x1d\x43reatePetIncidentReportResult\x12j\n\x0fincident_report\x18\x01 \x01(\x0b\x32\x41.moego.models.business_customer.v1.BusinessPetIncidentReportModelR\x0eincidentReport\"\xb2\x01\n\x1dUpdatePetIncidentReportParams\x12\x17\n\x02id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\x02id\x12x\n\x0fincident_report\x18\x02 \x01(\x0b\x32\x45.moego.models.business_customer.v1.BusinessPetIncidentReportUpdateDefB\x08\xfa\x42\x05\x8a\x01\x02\x10\x01R\x0eincidentReport\"\x1f\n\x1dUpdatePetIncidentReportResult\"8\n\x1d\x44\x65letePetIncidentReportParams\x12\x17\n\x02id\x18\x01 \x01(\x03\x42\x07\xfa\x42\x04\"\x02 \x00R\x02id\"\x1f\n\x1d\x44\x65letePetIncidentReportResult2\x84\x05\n BusinessPetIncidentReportService\x12\x91\x01\n\x15ListPetIncidentReport\x12;.moego.api.business_customer.v1.ListPetIncidentReportParams\x1a;.moego.api.business_customer.v1.ListPetIncidentReportResult\x12\x97\x01\n\x17\x43reatePetIncidentReport\x12=.moego.api.business_customer.v1.CreatePetIncidentReportParams\x1a=.moego.api.business_customer.v1.CreatePetIncidentReportResult\x12\x97\x01\n\x17UpdatePetIncidentReport\x12=.moego.api.business_customer.v1.UpdatePetIncidentReportParams\x1a=.moego.api.business_customer.v1.UpdatePetIncidentReportResult\x12\x97\x01\n\x17\x44\x65letePetIncidentReport\x12=.moego.api.business_customer.v1.DeletePetIncidentReportParams\x1a=.moego.api.business_customer.v1.DeletePetIncidentReportResultB\x95\x01\n&com.moego.idl.api.business_customer.v1P\x01Zigithub.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/business_customer/v1;businesscustomerapipbb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'moego.api.business_customer.v1.business_pet_incident_report_api_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n&com.moego.idl.api.business_customer.v1P\001Zigithub.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/business_customer/v1;businesscustomerapipb'
  _globals['_LISTPETINCIDENTREPORTPARAMS'].fields_by_name['pet_id']._loaded_options = None
  _globals['_LISTPETINCIDENTREPORTPARAMS'].fields_by_name['pet_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_LISTPETINCIDENTREPORTPARAMS'].fields_by_name['business_id']._loaded_options = None
  _globals['_LISTPETINCIDENTREPORTPARAMS'].fields_by_name['business_id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_CREATEPETINCIDENTREPORTPARAMS'].fields_by_name['incident_report']._loaded_options = None
  _globals['_CREATEPETINCIDENTREPORTPARAMS'].fields_by_name['incident_report']._serialized_options = b'\372B\005\212\001\002\020\001'
  _globals['_UPDATEPETINCIDENTREPORTPARAMS'].fields_by_name['id']._loaded_options = None
  _globals['_UPDATEPETINCIDENTREPORTPARAMS'].fields_by_name['id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_UPDATEPETINCIDENTREPORTPARAMS'].fields_by_name['incident_report']._loaded_options = None
  _globals['_UPDATEPETINCIDENTREPORTPARAMS'].fields_by_name['incident_report']._serialized_options = b'\372B\005\212\001\002\020\001'
  _globals['_DELETEPETINCIDENTREPORTPARAMS'].fields_by_name['id']._loaded_options = None
  _globals['_DELETEPETINCIDENTREPORTPARAMS'].fields_by_name['id']._serialized_options = b'\372B\004\"\002 \000'
  _globals['_LISTPETINCIDENTREPORTPARAMS']._serialized_start=282
  _globals['_LISTPETINCIDENTREPORTPARAMS']._serialized_end=406
  _globals['_LISTPETINCIDENTREPORTRESULT']._serialized_start=409
  _globals['_LISTPETINCIDENTREPORTRESULT']._serialized_end=548
  _globals['_CREATEPETINCIDENTREPORTPARAMS']._serialized_start=551
  _globals['_CREATEPETINCIDENTREPORTPARAMS']._serialized_end=704
  _globals['_CREATEPETINCIDENTREPORTRESULT']._serialized_start=707
  _globals['_CREATEPETINCIDENTREPORTRESULT']._serialized_end=846
  _globals['_UPDATEPETINCIDENTREPORTPARAMS']._serialized_start=849
  _globals['_UPDATEPETINCIDENTREPORTPARAMS']._serialized_end=1027
  _globals['_UPDATEPETINCIDENTREPORTRESULT']._serialized_start=1029
  _globals['_UPDATEPETINCIDENTREPORTRESULT']._serialized_end=1060
  _globals['_DELETEPETINCIDENTREPORTPARAMS']._serialized_start=1062
  _globals['_DELETEPETINCIDENTREPORTPARAMS']._serialized_end=1118
  _globals['_DELETEPETINCIDENTREPORTRESULT']._serialized_start=1120
  _globals['_DELETEPETINCIDENTREPORTRESULT']._serialized_end=1151
  _globals['_BUSINESSPETINCIDENTREPORTSERVICE']._serialized_start=1154
  _globals['_BUSINESSPETINCIDENTREPORTSERVICE']._serialized_end=1798
# @@protoc_insertion_point(module_scope)
