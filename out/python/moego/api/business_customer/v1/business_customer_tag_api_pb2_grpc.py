# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

from moego.api.business_customer.v1 import business_customer_tag_api_pb2 as moego_dot_api_dot_business__customer_dot_v1_dot_business__customer__tag__api__pb2


class BusinessCustomerTagServiceStub(object):
    """API for customer tag settings
    """

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.ListCustomerTag = channel.unary_unary(
                '/moego.api.business_customer.v1.BusinessCustomerTagService/ListCustomerTag',
                request_serializer=moego_dot_api_dot_business__customer_dot_v1_dot_business__customer__tag__api__pb2.ListCustomerTagParams.SerializeToString,
                response_deserializer=moego_dot_api_dot_business__customer_dot_v1_dot_business__customer__tag__api__pb2.ListCustomerTagResult.FromString,
                _registered_method=True)
        self.ListCustomerTagTemplate = channel.unary_unary(
                '/moego.api.business_customer.v1.BusinessCustomerTagService/ListCustomerTagTemplate',
                request_serializer=moego_dot_api_dot_business__customer_dot_v1_dot_business__customer__tag__api__pb2.ListCustomerTagTemplateParams.SerializeToString,
                response_deserializer=moego_dot_api_dot_business__customer_dot_v1_dot_business__customer__tag__api__pb2.ListCustomerTagTemplateResult.FromString,
                _registered_method=True)
        self.CreateCustomerTag = channel.unary_unary(
                '/moego.api.business_customer.v1.BusinessCustomerTagService/CreateCustomerTag',
                request_serializer=moego_dot_api_dot_business__customer_dot_v1_dot_business__customer__tag__api__pb2.CreateCustomerTagParams.SerializeToString,
                response_deserializer=moego_dot_api_dot_business__customer_dot_v1_dot_business__customer__tag__api__pb2.CreateCustomerTagResult.FromString,
                _registered_method=True)
        self.UpdateCustomerTag = channel.unary_unary(
                '/moego.api.business_customer.v1.BusinessCustomerTagService/UpdateCustomerTag',
                request_serializer=moego_dot_api_dot_business__customer_dot_v1_dot_business__customer__tag__api__pb2.UpdateCustomerTagParams.SerializeToString,
                response_deserializer=moego_dot_api_dot_business__customer_dot_v1_dot_business__customer__tag__api__pb2.UpdateCustomerTagResult.FromString,
                _registered_method=True)
        self.SortCustomerTag = channel.unary_unary(
                '/moego.api.business_customer.v1.BusinessCustomerTagService/SortCustomerTag',
                request_serializer=moego_dot_api_dot_business__customer_dot_v1_dot_business__customer__tag__api__pb2.SortCustomerTagParams.SerializeToString,
                response_deserializer=moego_dot_api_dot_business__customer_dot_v1_dot_business__customer__tag__api__pb2.SortCustomerTagResult.FromString,
                _registered_method=True)
        self.DeleteCustomerTag = channel.unary_unary(
                '/moego.api.business_customer.v1.BusinessCustomerTagService/DeleteCustomerTag',
                request_serializer=moego_dot_api_dot_business__customer_dot_v1_dot_business__customer__tag__api__pb2.DeleteCustomerTagParams.SerializeToString,
                response_deserializer=moego_dot_api_dot_business__customer_dot_v1_dot_business__customer__tag__api__pb2.DeleteCustomerTagResult.FromString,
                _registered_method=True)


class BusinessCustomerTagServiceServicer(object):
    """API for customer tag settings
    """

    def ListCustomerTag(self, request, context):
        """List customer tags of current company
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ListCustomerTagTemplate(self, request, context):
        """List customer tag template
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def CreateCustomerTag(self, request, context):
        """Create a customer tag
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def UpdateCustomerTag(self, request, context):
        """Update a customer tag
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SortCustomerTag(self, request, context):
        """Sort customer tags
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DeleteCustomerTag(self, request, context):
        """Delete a customer tag
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_BusinessCustomerTagServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'ListCustomerTag': grpc.unary_unary_rpc_method_handler(
                    servicer.ListCustomerTag,
                    request_deserializer=moego_dot_api_dot_business__customer_dot_v1_dot_business__customer__tag__api__pb2.ListCustomerTagParams.FromString,
                    response_serializer=moego_dot_api_dot_business__customer_dot_v1_dot_business__customer__tag__api__pb2.ListCustomerTagResult.SerializeToString,
            ),
            'ListCustomerTagTemplate': grpc.unary_unary_rpc_method_handler(
                    servicer.ListCustomerTagTemplate,
                    request_deserializer=moego_dot_api_dot_business__customer_dot_v1_dot_business__customer__tag__api__pb2.ListCustomerTagTemplateParams.FromString,
                    response_serializer=moego_dot_api_dot_business__customer_dot_v1_dot_business__customer__tag__api__pb2.ListCustomerTagTemplateResult.SerializeToString,
            ),
            'CreateCustomerTag': grpc.unary_unary_rpc_method_handler(
                    servicer.CreateCustomerTag,
                    request_deserializer=moego_dot_api_dot_business__customer_dot_v1_dot_business__customer__tag__api__pb2.CreateCustomerTagParams.FromString,
                    response_serializer=moego_dot_api_dot_business__customer_dot_v1_dot_business__customer__tag__api__pb2.CreateCustomerTagResult.SerializeToString,
            ),
            'UpdateCustomerTag': grpc.unary_unary_rpc_method_handler(
                    servicer.UpdateCustomerTag,
                    request_deserializer=moego_dot_api_dot_business__customer_dot_v1_dot_business__customer__tag__api__pb2.UpdateCustomerTagParams.FromString,
                    response_serializer=moego_dot_api_dot_business__customer_dot_v1_dot_business__customer__tag__api__pb2.UpdateCustomerTagResult.SerializeToString,
            ),
            'SortCustomerTag': grpc.unary_unary_rpc_method_handler(
                    servicer.SortCustomerTag,
                    request_deserializer=moego_dot_api_dot_business__customer_dot_v1_dot_business__customer__tag__api__pb2.SortCustomerTagParams.FromString,
                    response_serializer=moego_dot_api_dot_business__customer_dot_v1_dot_business__customer__tag__api__pb2.SortCustomerTagResult.SerializeToString,
            ),
            'DeleteCustomerTag': grpc.unary_unary_rpc_method_handler(
                    servicer.DeleteCustomerTag,
                    request_deserializer=moego_dot_api_dot_business__customer_dot_v1_dot_business__customer__tag__api__pb2.DeleteCustomerTagParams.FromString,
                    response_serializer=moego_dot_api_dot_business__customer_dot_v1_dot_business__customer__tag__api__pb2.DeleteCustomerTagResult.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'moego.api.business_customer.v1.BusinessCustomerTagService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('moego.api.business_customer.v1.BusinessCustomerTagService', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class BusinessCustomerTagService(object):
    """API for customer tag settings
    """

    @staticmethod
    def ListCustomerTag(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.api.business_customer.v1.BusinessCustomerTagService/ListCustomerTag',
            moego_dot_api_dot_business__customer_dot_v1_dot_business__customer__tag__api__pb2.ListCustomerTagParams.SerializeToString,
            moego_dot_api_dot_business__customer_dot_v1_dot_business__customer__tag__api__pb2.ListCustomerTagResult.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def ListCustomerTagTemplate(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.api.business_customer.v1.BusinessCustomerTagService/ListCustomerTagTemplate',
            moego_dot_api_dot_business__customer_dot_v1_dot_business__customer__tag__api__pb2.ListCustomerTagTemplateParams.SerializeToString,
            moego_dot_api_dot_business__customer_dot_v1_dot_business__customer__tag__api__pb2.ListCustomerTagTemplateResult.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def CreateCustomerTag(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.api.business_customer.v1.BusinessCustomerTagService/CreateCustomerTag',
            moego_dot_api_dot_business__customer_dot_v1_dot_business__customer__tag__api__pb2.CreateCustomerTagParams.SerializeToString,
            moego_dot_api_dot_business__customer_dot_v1_dot_business__customer__tag__api__pb2.CreateCustomerTagResult.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def UpdateCustomerTag(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.api.business_customer.v1.BusinessCustomerTagService/UpdateCustomerTag',
            moego_dot_api_dot_business__customer_dot_v1_dot_business__customer__tag__api__pb2.UpdateCustomerTagParams.SerializeToString,
            moego_dot_api_dot_business__customer_dot_v1_dot_business__customer__tag__api__pb2.UpdateCustomerTagResult.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def SortCustomerTag(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.api.business_customer.v1.BusinessCustomerTagService/SortCustomerTag',
            moego_dot_api_dot_business__customer_dot_v1_dot_business__customer__tag__api__pb2.SortCustomerTagParams.SerializeToString,
            moego_dot_api_dot_business__customer_dot_v1_dot_business__customer__tag__api__pb2.SortCustomerTagResult.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def DeleteCustomerTag(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.api.business_customer.v1.BusinessCustomerTagService/DeleteCustomerTag',
            moego_dot_api_dot_business__customer_dot_v1_dot_business__customer__tag__api__pb2.DeleteCustomerTagParams.SerializeToString,
            moego_dot_api_dot_business__customer_dot_v1_dot_business__customer__tag__api__pb2.DeleteCustomerTagResult.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)
