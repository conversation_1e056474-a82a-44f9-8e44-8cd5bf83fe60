from moego.models.business_customer.v1 import business_customer_retention_defs_pb2 as _business_customer_retention_defs_pb2
from moego.utils.v2 import condition_messages_pb2 as _condition_messages_pb2
from moego.utils.v2 import pagination_messages_pb2 as _pagination_messages_pb2
from google.protobuf.internal import containers as _containers
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from collections.abc import Iterable as _Iterable, Mapping as _Mapping
from typing import ClassVar as _ClassVar, Optional as _Optional, Union as _Union

DESCRIPTOR: _descriptor.FileDescriptor

class FetchRetentionDataParams(_message.Message):
    __slots__ = ("retention_filter", "client_filter", "pagination", "order_bys")
    RETENTION_FILTER_FIELD_NUMBER: _ClassVar[int]
    CLIENT_FILTER_FIELD_NUMBER: _ClassVar[int]
    PAGINATION_FIELD_NUMBER: _ClassVar[int]
    ORDER_BYS_FIELD_NUMBER: _ClassVar[int]
    retention_filter: _business_customer_retention_defs_pb2.RetentionFilter
    client_filter: str
    pagination: _pagination_messages_pb2.PaginationRequest
    order_bys: _containers.RepeatedCompositeFieldContainer[_condition_messages_pb2.OrderBy]
    def __init__(self, retention_filter: _Optional[_Union[_business_customer_retention_defs_pb2.RetentionFilter, _Mapping]] = ..., client_filter: _Optional[str] = ..., pagination: _Optional[_Union[_pagination_messages_pb2.PaginationRequest, _Mapping]] = ..., order_bys: _Optional[_Iterable[_Union[_condition_messages_pb2.OrderBy, _Mapping]]] = ...) -> None: ...

class FetchRetentionDataResult(_message.Message):
    __slots__ = ("data", "pagination")
    DATA_FIELD_NUMBER: _ClassVar[int]
    PAGINATION_FIELD_NUMBER: _ClassVar[int]
    data: _containers.RepeatedCompositeFieldContainer[_business_customer_retention_defs_pb2.CustomerRetentionData]
    pagination: _pagination_messages_pb2.PaginationResponse
    def __init__(self, data: _Optional[_Iterable[_Union[_business_customer_retention_defs_pb2.CustomerRetentionData, _Mapping]]] = ..., pagination: _Optional[_Union[_pagination_messages_pb2.PaginationResponse, _Mapping]] = ...) -> None: ...
