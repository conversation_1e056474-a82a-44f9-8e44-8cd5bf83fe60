from moego.service.payment.v2 import payment_ops_service_pb2 as _payment_ops_service_pb2
from google.protobuf.internal import containers as _containers
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from collections.abc import Iterable as _Iterable, Mapping as _Mapping
from typing import ClassVar as _ClassVar, Optional as _Optional, Union as _Union

DESCRIPTOR: _descriptor.FileDescriptor

class ImportExternalSquareCofToStripeParams(_message.Message):
    __slots__ = ("target_company_id", "target_business_id", "cof_entries", "dry_run")
    TARGET_COMPANY_ID_FIELD_NUMBER: _ClassVar[int]
    TARGET_BUSINESS_ID_FIELD_NUMBER: _ClassVar[int]
    COF_ENTRIES_FIELD_NUMBER: _ClassVar[int]
    DRY_RUN_FIELD_NUMBER: _ClassVar[int]
    target_company_id: int
    target_business_id: int
    cof_entries: _containers.RepeatedCompositeFieldContainer[_payment_ops_service_pb2.ImportExternalSquareCofToStripeRequest.CofEntry]
    dry_run: bool
    def __init__(self, target_company_id: _Optional[int] = ..., target_business_id: _Optional[int] = ..., cof_entries: _Optional[_Iterable[_Union[_payment_ops_service_pb2.ImportExternalSquareCofToStripeRequest.CofEntry, _Mapping]]] = ..., dry_run: bool = ...) -> None: ...

class ImportExternalSquareCofToStripeResult(_message.Message):
    __slots__ = ()
    def __init__(self) -> None: ...
