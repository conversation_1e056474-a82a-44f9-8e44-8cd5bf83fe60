from moego.models.sms.v1 import sms_defs_pb2 as _sms_defs_pb2
from moego.models.sms.v1 import sms_models_pb2 as _sms_models_pb2
from validate import validate_pb2 as _validate_pb2
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from collections.abc import Mapping as _Mapping
from typing import ClassVar as _ClassVar, Optional as _Optional, Union as _Union

DESCRIPTOR: _descriptor.FileDescriptor

class CreateSmsRequest(_message.Message):
    __slots__ = ("sms_def",)
    SMS_DEF_FIELD_NUMBER: _ClassVar[int]
    sms_def: _sms_defs_pb2.SmsDef
    def __init__(self, sms_def: _Optional[_Union[_sms_defs_pb2.SmsDef, _Mapping]] = ...) -> None: ...

class GetSmsRequest(_message.Message):
    __slots__ = ("id",)
    ID_FIELD_NUMBER: _ClassVar[int]
    id: int
    def __init__(self, id: _Optional[int] = ...) -> None: ...
