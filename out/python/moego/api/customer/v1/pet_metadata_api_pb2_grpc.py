# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

from google.protobuf import empty_pb2 as google_dot_protobuf_dot_empty__pb2
from moego.api.customer.v1 import pet_metadata_api_pb2 as moego_dot_api_dot_customer_dot_v1_dot_pet__metadata__api__pb2


class PetMetadataServiceStub(object):
    """pet metadata service
    """

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.GetPetMetadataList = channel.unary_unary(
                '/moego.api.customer.v1.PetMetadataService/GetPetMetadataList',
                request_serializer=google_dot_protobuf_dot_empty__pb2.Empty.SerializeToString,
                response_deserializer=moego_dot_api_dot_customer_dot_v1_dot_pet__metadata__api__pb2.GetPetMetadataListResponse.FromString,
                _registered_method=True)


class PetMetadataServiceServicer(object):
    """pet metadata service
    """

    def GetPetMetadataList(self, request, context):
        """get pet metadata list
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_PetMetadataServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'GetPetMetadataList': grpc.unary_unary_rpc_method_handler(
                    servicer.GetPetMetadataList,
                    request_deserializer=google_dot_protobuf_dot_empty__pb2.Empty.FromString,
                    response_serializer=moego_dot_api_dot_customer_dot_v1_dot_pet__metadata__api__pb2.GetPetMetadataListResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'moego.api.customer.v1.PetMetadataService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('moego.api.customer.v1.PetMetadataService', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class PetMetadataService(object):
    """pet metadata service
    """

    @staticmethod
    def GetPetMetadataList(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/moego.api.customer.v1.PetMetadataService/GetPetMetadataList',
            google_dot_protobuf_dot_empty__pb2.Empty.SerializeToString,
            moego_dot_api_dot_customer_dot_v1_dot_pet__metadata__api__pb2.GetPetMetadataListResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)
