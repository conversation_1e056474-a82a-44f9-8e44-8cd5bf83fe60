package com.moego.server.payment.util;

/**
 * <AUTHOR>
 * @since 2022/6/14 5:04 PM
 */
public interface StripeReader {
    String SMART_READER_TYPE = "BBPOS_WISEPOS_E";

    // readerID:payId
    String READER_PROCESS_KEY = "stripe:reader:status:%s:%s";
    Integer READER_PROCESS_KEY_TIMEOUT = 300;

    String READER_ACTION_PROCESS_STATUS = "in_progress";

    String READER_ID_META_DATA = "reader_id";

    enum ReaderStatus {
        WAITING,
        SUCCEEDED,
        FAILED,
    }
}
