package com.moego.server.payment.dto.square;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SquareCreateCustomerResponse {

    private Integer customerId;
    private String squareCustomerId;
    private String firstName;
    private String lastName;
    private String phoneNumber;
    private String email;

    private SquareCard card;
}
