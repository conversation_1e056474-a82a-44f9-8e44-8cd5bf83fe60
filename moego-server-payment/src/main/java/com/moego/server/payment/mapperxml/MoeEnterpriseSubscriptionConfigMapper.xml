<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.moego.server.payment.mapper.MoeEnterpriseSubscriptionConfigMapper">
  <resultMap id="BaseResultMap" type="com.moego.server.payment.mapperbean.MoeEnterpriseSubscriptionConfig">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="enterprise_id" jdbcType="BIGINT" property="enterpriseId" />
    <result column="stripe_subscriptions_id" jdbcType="VARCHAR" property="stripeSubscriptionsId" />
    <result column="level" jdbcType="INTEGER" property="level" />
    <result column="location_num" jdbcType="INTEGER" property="locationNum" />
    <result column="vans_num" jdbcType="INTEGER" property="vansNum" />
    <result column="begin_date" jdbcType="BIGINT" property="beginDate" />
    <result column="expire_date" jdbcType="BIGINT" property="expireDate" />
    <result column="current_plan_id" jdbcType="INTEGER" property="currentPlanId" />
    <result column="auto_renew" jdbcType="TINYINT" property="autoRenew" />
    <result column="next_plan_id" jdbcType="INTEGER" property="nextPlanId" />
    <result column="charge_status" jdbcType="TINYINT" property="chargeStatus" />
    <result column="charge_msg" jdbcType="VARCHAR" property="chargeMsg" />
    <result column="charge_failed_time" jdbcType="BIGINT" property="chargeFailedTime" />
    <result column="pay_company_subscription" jdbcType="TINYINT" property="payCompanySubscription" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, enterprise_id, stripe_subscriptions_id, level, location_num, vans_num, begin_date,
    expire_date, current_plan_id, auto_renew, next_plan_id, charge_status, charge_msg,
    charge_failed_time, pay_company_subscription, created_at, updated_at
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <include refid="Base_Column_List" />
    from moe_enterprise_subscription_config
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from moe_enterprise_subscription_config
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.moego.server.payment.mapperbean.MoeEnterpriseSubscriptionConfig">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into moe_enterprise_subscription_config (enterprise_id, stripe_subscriptions_id,
      level, location_num, vans_num,
      begin_date, expire_date, current_plan_id,
      auto_renew, next_plan_id, charge_status,
      charge_msg, charge_failed_time, pay_company_subscription,
      created_at, updated_at)
    values (#{enterpriseId,jdbcType=BIGINT}, #{stripeSubscriptionsId,jdbcType=VARCHAR},
      #{level,jdbcType=INTEGER}, #{locationNum,jdbcType=INTEGER}, #{vansNum,jdbcType=INTEGER},
      #{beginDate,jdbcType=BIGINT}, #{expireDate,jdbcType=BIGINT}, #{currentPlanId,jdbcType=INTEGER},
      #{autoRenew,jdbcType=TINYINT}, #{nextPlanId,jdbcType=INTEGER}, #{chargeStatus,jdbcType=TINYINT},
      #{chargeMsg,jdbcType=VARCHAR}, #{chargeFailedTime,jdbcType=BIGINT}, #{payCompanySubscription,jdbcType=TINYINT},
      #{createdAt,jdbcType=TIMESTAMP}, #{updatedAt,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.moego.server.payment.mapperbean.MoeEnterpriseSubscriptionConfig">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into moe_enterprise_subscription_config
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="enterpriseId != null">
        enterprise_id,
      </if>
      <if test="stripeSubscriptionsId != null">
        stripe_subscriptions_id,
      </if>
      <if test="level != null">
        level,
      </if>
      <if test="locationNum != null">
        location_num,
      </if>
      <if test="vansNum != null">
        vans_num,
      </if>
      <if test="beginDate != null">
        begin_date,
      </if>
      <if test="expireDate != null">
        expire_date,
      </if>
      <if test="currentPlanId != null">
        current_plan_id,
      </if>
      <if test="autoRenew != null">
        auto_renew,
      </if>
      <if test="nextPlanId != null">
        next_plan_id,
      </if>
      <if test="chargeStatus != null">
        charge_status,
      </if>
      <if test="chargeMsg != null">
        charge_msg,
      </if>
      <if test="chargeFailedTime != null">
        charge_failed_time,
      </if>
      <if test="payCompanySubscription != null">
        pay_company_subscription,
      </if>
      <if test="createdAt != null">
        created_at,
      </if>
      <if test="updatedAt != null">
        updated_at,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="enterpriseId != null">
        #{enterpriseId,jdbcType=BIGINT},
      </if>
      <if test="stripeSubscriptionsId != null">
        #{stripeSubscriptionsId,jdbcType=VARCHAR},
      </if>
      <if test="level != null">
        #{level,jdbcType=INTEGER},
      </if>
      <if test="locationNum != null">
        #{locationNum,jdbcType=INTEGER},
      </if>
      <if test="vansNum != null">
        #{vansNum,jdbcType=INTEGER},
      </if>
      <if test="beginDate != null">
        #{beginDate,jdbcType=BIGINT},
      </if>
      <if test="expireDate != null">
        #{expireDate,jdbcType=BIGINT},
      </if>
      <if test="currentPlanId != null">
        #{currentPlanId,jdbcType=INTEGER},
      </if>
      <if test="autoRenew != null">
        #{autoRenew,jdbcType=TINYINT},
      </if>
      <if test="nextPlanId != null">
        #{nextPlanId,jdbcType=INTEGER},
      </if>
      <if test="chargeStatus != null">
        #{chargeStatus,jdbcType=TINYINT},
      </if>
      <if test="chargeMsg != null">
        #{chargeMsg,jdbcType=VARCHAR},
      </if>
      <if test="chargeFailedTime != null">
        #{chargeFailedTime,jdbcType=BIGINT},
      </if>
      <if test="payCompanySubscription != null">
        #{payCompanySubscription,jdbcType=TINYINT},
      </if>
      <if test="createdAt != null">
        #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedAt != null">
        #{updatedAt,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.moego.server.payment.mapperbean.MoeEnterpriseSubscriptionConfig">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_enterprise_subscription_config
    <set>
      <if test="enterpriseId != null">
        enterprise_id = #{enterpriseId,jdbcType=BIGINT},
      </if>
      <if test="stripeSubscriptionsId != null">
        stripe_subscriptions_id = #{stripeSubscriptionsId,jdbcType=VARCHAR},
      </if>
      <if test="level != null">
        level = #{level,jdbcType=INTEGER},
      </if>
      <if test="locationNum != null">
        location_num = #{locationNum,jdbcType=INTEGER},
      </if>
      <if test="vansNum != null">
        vans_num = #{vansNum,jdbcType=INTEGER},
      </if>
      <if test="beginDate != null">
        begin_date = #{beginDate,jdbcType=BIGINT},
      </if>
      <if test="expireDate != null">
        expire_date = #{expireDate,jdbcType=BIGINT},
      </if>
      <if test="currentPlanId != null">
        current_plan_id = #{currentPlanId,jdbcType=INTEGER},
      </if>
      <if test="autoRenew != null">
        auto_renew = #{autoRenew,jdbcType=TINYINT},
      </if>
      <if test="nextPlanId != null">
        next_plan_id = #{nextPlanId,jdbcType=INTEGER},
      </if>
      <if test="chargeStatus != null">
        charge_status = #{chargeStatus,jdbcType=TINYINT},
      </if>
      <if test="chargeMsg != null">
        charge_msg = #{chargeMsg,jdbcType=VARCHAR},
      </if>
      <if test="chargeFailedTime != null">
        charge_failed_time = #{chargeFailedTime,jdbcType=BIGINT},
      </if>
      <if test="payCompanySubscription != null">
        pay_company_subscription = #{payCompanySubscription,jdbcType=TINYINT},
      </if>
      <if test="createdAt != null">
        created_at = #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedAt != null">
        updated_at = #{updatedAt,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.moego.server.payment.mapperbean.MoeEnterpriseSubscriptionConfig">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_enterprise_subscription_config
    set enterprise_id = #{enterpriseId,jdbcType=BIGINT},
      stripe_subscriptions_id = #{stripeSubscriptionsId,jdbcType=VARCHAR},
      level = #{level,jdbcType=INTEGER},
      location_num = #{locationNum,jdbcType=INTEGER},
      vans_num = #{vansNum,jdbcType=INTEGER},
      begin_date = #{beginDate,jdbcType=BIGINT},
      expire_date = #{expireDate,jdbcType=BIGINT},
      current_plan_id = #{currentPlanId,jdbcType=INTEGER},
      auto_renew = #{autoRenew,jdbcType=TINYINT},
      next_plan_id = #{nextPlanId,jdbcType=INTEGER},
      charge_status = #{chargeStatus,jdbcType=TINYINT},
      charge_msg = #{chargeMsg,jdbcType=VARCHAR},
      charge_failed_time = #{chargeFailedTime,jdbcType=BIGINT},
      pay_company_subscription = #{payCompanySubscription,jdbcType=TINYINT},
      created_at = #{createdAt,jdbcType=TIMESTAMP},
      updated_at = #{updatedAt,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <select id="selectByEnterpriseId" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from moe_enterprise_subscription_config
    where enterprise_id = #{enterpriseId,jdbcType=BIGINT}
  </select>
  <select id="selectByEnterpriseIds"  resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from moe_enterprise_subscription_config
    where enterprise_id in
    <foreach close=")" collection="enterpriseIds" index="index" item="item" open="(" separator=",">
    #{item,jdbcType=BIGINT}
    </foreach>
  </select>
  <select id="selectBySubscriptionId" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from moe_enterprise_subscription_config
    where stripe_subscriptions_id = #{subscriptionId,jdbcType=VARCHAR}
  </select>
</mapper>
