package com.moego.server.payment.dto.square;

import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;
import lombok.Data;

@Data
public class TerminalChargeRequest {

    /*
     One of amount or unit_amount have to be present
     unit_amount will be used (ignore amount) if both are present
    */
    private BigDecimal amount;

    /*
    unit amount is the smallest unit amount of amount.
    e.g. 1 amount USD = 100 unit amount USD (cent)
    However, 1 amount JYP = 1 unit amount JYP
    Refer to ISO 4217 https://en.wikipedia.org/wiki/ISO_4217
     */
    private Long unit_amount;

    @NotNull
    private String currency;

    private Integer groomingId;
    private Integer invoiceId;
    private Integer customerId;
    private Integer staffId;
    // grooming, package or product
    private String module;
    private String deviceId;
}
