package com.moego.server.payment.server;

import com.moego.server.payment.api.IPaymentRefundServiceBase;
import com.moego.server.payment.dto.PaymentDTO;
import com.moego.server.payment.dto.RefundChannelDTO;
import com.moego.server.payment.dto.RefundDTO;
import com.moego.server.payment.mapper.RefundMapper;
import com.moego.server.payment.params.CheckRefundChannelParams;
import com.moego.server.payment.params.CreateRefundByPaymentIdParams;
import com.moego.server.payment.params.CreateRefundParams;
import com.moego.server.payment.params.SubmitRefundParams;
import com.moego.server.payment.params.refund.RefundHistoryListParams;
import com.moego.server.payment.service.RefundService;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
public class PaymentRefundServer extends IPaymentRefundServiceBase {

    @Autowired
    private RefundService refundService;

    @Autowired
    private RefundMapper refundMapper;

    @Override
    public List<RefundDTO> getRefunds(@RequestParam("module") String module, @RequestBody List<Integer> invoiceIds) {
        return refundService.getRefundsByInvoiceIdsAndModule(module, invoiceIds);
    }

    @Override
    public PaymentDTO createRefund(
            @RequestParam("businessId") Integer businessId, @RequestBody CreateRefundParams params) {
        return refundService.createRefund(params);
    }

    @Override
    public void submitRefund(@RequestBody SubmitRefundParams submitRefundParams) {
        refundService.submitRefund(
                submitRefundParams.getInvoiceId(),
                submitRefundParams.getRefundAmount(),
                submitRefundParams.getRefunds(),
                submitRefundParams.getRefundReason());
    }

    @Override
    public RefundDTO getRefundByRefundOrderPaymentId(
            @RequestParam("businessId") Integer businessId,
            @RequestParam("refundOrderPaymentId") Long refundOrderPaymentId) {
        return refundService.getRefundByRefundOrderPaymentId(businessId, refundOrderPaymentId);
    }

    @Override
    public PaymentDTO createRefundByPaymentId(
            @RequestParam("businessId") Integer businessId, @RequestBody CreateRefundByPaymentIdParams params) {
        return refundService.createRefundByPaymentId(businessId, params);
    }

    @Override
    public RefundChannelDTO refundCheckByChangeAmount(CheckRefundChannelParams params) {
        return refundService.getUsefulRefundChannel(
                params.getBusinessId(), params.getInvoiceId(), params.getChangedAmount());
    }

    @Override
    public List<RefundDTO> getRefunds(Integer paymentId) {
        return refundService.getRefundsByPaymentId(paymentId);
    }

    @Override
    public List<RefundDTO> getRefundsByStripeApi(String chargeId, Long limit) {
        return refundService.getRefundsByStripeApi(chargeId, limit);
    }

    @Override
    public List<RefundDTO> getRefundsByParams(RefundHistoryListParams params) {
        return refundService.getRefundsByParams(params);
    }
}
