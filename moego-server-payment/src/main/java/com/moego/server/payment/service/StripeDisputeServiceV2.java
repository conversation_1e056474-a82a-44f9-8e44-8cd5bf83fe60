package com.moego.server.payment.service;

import com.moego.api.thirdparty.IPaymentSlackClient;
import com.moego.common.dto.notificationDto.NotificationEnum;
import com.moego.common.dto.notificationDto.NotificationExtraDisputeDto;
import com.moego.common.enums.ResponseCodeEnum;
import com.moego.common.exception.CommonException;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.payment.v1.DisputeEventLogModel;
import com.moego.idl.models.payment.v1.DisputeFundingOperateModel;
import com.moego.idl.models.payment.v1.DisputeModel;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.lib.common.thread.ThreadPool;
import com.moego.server.business.api.IBusinessBusinessService;
import com.moego.server.business.client.IBusinessBusinessClient;
import com.moego.server.business.dto.CompanyDto;
import com.moego.server.business.dto.MoeBusinessDto;
import com.moego.server.business.params.InfoIdParams;
import com.moego.server.message.client.INotificationClient;
import com.moego.server.message.params.notification.NotificationDisputeParams;
import com.moego.server.payment.constant.StripeDisputeFeeStatusEnum;
import com.moego.server.payment.constant.StripeDisputeStatusEnum;
import com.moego.server.payment.dto.ListDisputeRequest;
import com.moego.server.payment.mapper.MmStripeAccountMapper;
import com.moego.server.payment.mapper.MmStripeDisputeEventLogMapper;
import com.moego.server.payment.mapper.MmStripeDisputeFeeMapper;
import com.moego.server.payment.mapper.MmStripeDisputeFundFlowMapper;
import com.moego.server.payment.mapper.MmStripeDisputeMapper;
import com.moego.server.payment.mapperbean.MmStripeAccount;
import com.moego.server.payment.mapperbean.MmStripeDispute;
import com.moego.server.payment.mapperbean.MmStripeDisputeEventLog;
import com.moego.server.payment.mapperbean.MmStripeDisputeFee;
import com.moego.server.payment.mapperbean.MmStripeDisputeFundFlow;
import com.moego.server.payment.mapperbean.MoeCustomizedPaymentSetting;
import com.moego.server.payment.mapperbean.Payment;
import com.moego.server.payment.params.BookFeeClaimParam;
import com.moego.server.payment.params.dispute.QueryStripeDisputeParams;
import com.moego.server.payment.util.StripeEvent;
import com.moego.server.payment.web.vo.DisputeQueryVO;
import com.moego.server.payment.web.vo.DisputeReadStatusVO;
import com.stripe.exception.StripeException;
import com.stripe.model.Charge;
import com.stripe.model.Customer;
import com.stripe.model.Dispute;
import com.stripe.model.DisputeCollection;
import com.stripe.model.Event;
import com.stripe.model.Transfer;
import com.stripe.net.ApiResource;
import com.stripe.net.RequestOptions;
import com.stripe.param.ChargeCreateParams;
import com.stripe.param.TransferCreateParams;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionOperations;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR> By EasyCode
 * @since 2022-12-05 17:02:45
 */
@Service
@Slf4j
public class StripeDisputeServiceV2 {

    @Autowired
    private MmStripeDisputeMapper stripeDisputeMapper;

    @Autowired
    private MmStripeAccountMapper stripeAccountMapper;

    @Autowired
    private TransactionOperations transactionOperations;

    @Autowired
    private CustomizedPaymentSettingService customizedPaymentSettingService;

    @Autowired
    private IBusinessBusinessClient iBusinessBusinessClient;

    @Autowired
    private MmStripeDisputeFeeMapper disputeFeeMapper;

    @Autowired
    private IPaymentSlackClient iPaymentSlackClient;

    @Autowired
    private PaymentService paymentService;

    @Autowired
    private IBusinessBusinessService iBusinessBusinessService;

    @Autowired
    private StripeDisputeService stripeDisputeService;

    @Autowired
    private MessageDeliveryService messageDeliveryService;

    @Autowired
    private INotificationClient iNotificationClient;

    @Autowired
    private MmStripeDisputeEventLogMapper mmStripeDisputeEventLogMapper;

    @Autowired
    private MmStripeDisputeFundFlowMapper mmStripeDisputeFundFlowMapper;

    /**
     * 目前固定15 usd
     */
    public static final Long STRIPE_DISPUTE_FEE = 1500L;

    public static final String DISPUTE_CREATE_REVERSE_DESC = "Transaction disputed";
    public static final String DISPUTE_CREATE_FEE_DESC = "Dispute fee";
    public static final String DISPUTE_REFUND_REVERSE_DESC = "Dispute won";

    private final Date NEW_PACT_DATE = new GregorianCalendar(2023, Calendar.MARCH, 11).getTime();

    private static final String DISPUTE_FUND_WITHDRAW_IDEMPOTENCY_KEY = "withdraw-dispute-";
    private static final String DISPUTE_FUND_INSTATE_IDEMPOTENCY_KEY = "instate-dispute-";

    @Autowired
    private MmStripeDisputeMapper mmStripeDisputeMapper;

    /**
     * webhook事件同步处理器
     *
     * @param event:   stripe 传入的 event obj
     * @param dispute: stripe 传入的 dispute obj
     */
    public void handleStripeDisputeWebhook(Event event, Dispute dispute) {
        // 获取 event obj 中的 event_id
        String eventId = event.getId();

        // 根据 流水表 DDL 定义，event_id 是唯一索引，不需要在 service 层做幂等判断。
        // 构建 MmStripeDisputeEventLog obj
        MmStripeDisputeEventLog mmStripeDisputeEventLog = buildStripeDisputeEventLogByStripeObj(event, dispute);
        try {
            mmStripeDisputeEventLogMapper.insertSelective(mmStripeDisputeEventLog);
        } catch (DuplicateKeyException e) {
            log.warn("Webhook {} already processed", eventId, e);
            return;
        }

        // 至此，webhook 事件已持久化，后续任何错误都返回成功

        try {
            processEvent(mmStripeDisputeEventLog);
        } catch (Exception e) {
            log.error("Webhook {} process failed", eventId, e);
        }
    }

    /**
     * 持久化dispute webhook 事件流水记录
     *
     * @param event:   stripe 传入的 event obj
     * @param dispute: stripe 传入的 dispute obj
     * @return ： MmStripeDisputeEventLog 数据库DAO 映射 obj
     */
    public MmStripeDisputeEventLog buildStripeDisputeEventLogByStripeObj(Event event, Dispute dispute) {
        MmStripeDisputeEventLog mmStripeDisputeEventLog = new MmStripeDisputeEventLog();
        mmStripeDisputeEventLog.setEventId(event.getId());
        mmStripeDisputeEventLog.setEventType(event.getType());
        mmStripeDisputeEventLog.setWebhookCreatedTime(event.getCreated());
        // 处理 dispute 转换为 JSON
        try {
            mmStripeDisputeEventLog.setWebhookBody(dispute.toJson());
        } catch (Exception e) {
            throw new RuntimeException("Failed to convert event data to JSON", e);
        }
        mmStripeDisputeEventLog.setDisputeId(dispute.getId());
        mmStripeDisputeEventLog.setDisputeStatus(dispute.getStatus());
        mmStripeDisputeEventLog.setStatus(DisputeEventLogModel.Status.INIT.name());

        return mmStripeDisputeEventLog;
    }

    public void processAllInitEvent() {
        // 根据 init 状态查询流水表的记录
        List<MmStripeDisputeEventLog> mmStripeDisputeEventLogs =
                mmStripeDisputeEventLogMapper.selectByRecordStatus(DisputeEventLogModel.Status.INIT.name());
        if (CollectionUtils.isEmpty(mmStripeDisputeEventLogs)) {
            return;
        }
        for (MmStripeDisputeEventLog mmStripeDisputeEventLog : mmStripeDisputeEventLogs) {
            try {
                processEvent(mmStripeDisputeEventLog);
            } catch (Exception e) {
                log.error("process event failed, disputeId={}", mmStripeDisputeEventLog.getDisputeId(), e);
            }
        }
    }

    /**
     * 【异步处理1】处理 dispute 主表 and dispute fund flow
     */
    private void processEvent(MmStripeDisputeEventLog mmStripeDisputeEventLog) {
        transactionOperations.executeWithoutResult(status -> {
            // 查询 dispute 主表记录并加上悲观锁
            MmStripeDispute mmStripeDispute =
                    stripeDisputeMapper.selectByDisputeIdForUpdateNoWait(mmStripeDisputeEventLog.getDisputeId());

            // 更新 event 状态
            var updateEventLog = new MmStripeDisputeEventLog();
            updateEventLog.setId(mmStripeDisputeEventLog.getId());
            updateEventLog.setStatus(DisputeEventLogModel.Status.SUCCEED.name());
            mmStripeDisputeEventLogMapper.updateByPrimaryKeySelective(updateEventLog);

            // 如果 dispute 记录不存在，才去反序列化 JSON 构建 Dispute 对象
            if (mmStripeDispute == null) {
                // 首次执行 dispute 任务
                initialExecution(mmStripeDisputeEventLog);
                return;
            }
            // 在这里判断是否走老逻辑
            // fundWithDrawStatus 是新加的，如果没有值，说明是老逻辑
            if (!StringUtils.hasText(mmStripeDispute.getFundWithdrawStatus())
                    || DisputeModel.FundWithdrawStatus.FUND_WITHDRAW_STATUS_UNSPECIFIED
                            .name()
                            .equalsIgnoreCase(mmStripeDispute.getFundWithdrawStatus())) {
                processEventForDisputeV1(mmStripeDisputeEventLog);
                return;
            }

            // 非首次执行 dispute 任务
            nonFirstTimeExecution(mmStripeDispute, mmStripeDisputeEventLog);
        });

        // 事务成功提交后，开始处理相关 dispute 资金
        var fundFlowList = mmStripeDisputeFundFlowMapper.selectByDisputeIdAndStatus(
                mmStripeDisputeEventLog.getDisputeId(), DisputeFundingOperateModel.Status.INIT.name());
        if (CollectionUtils.isEmpty(fundFlowList)) {
            return;
        }
        for (MmStripeDisputeFundFlow mmStripeDisputeFundFlow : fundFlowList) {
            try {
                processDisputeFundFlow(mmStripeDisputeFundFlow);
            } catch (Exception e) {
                log.error("processDisputeFundFlow error:{}", mmStripeDisputeFundFlow.getId(), e);
            }
        }
    }

    /**
     * 首次执行dispute任务
     *
     * @param mmStripeDisputeEventLog The event log related to the dispute.
     */
    private void initialExecution(MmStripeDisputeEventLog mmStripeDisputeEventLog) {
        var dispute = ApiResource.INTERNAL_GSON.fromJson(mmStripeDisputeEventLog.getWebhookBody(), Dispute.class);
        var payment = paymentService.getPaymentByPaymentIntentId(dispute.getPaymentIntent());
        if (payment == null) {
            log.info("payment is null, paymentIntentId:{}", dispute.getPaymentIntent());
            notifySlack(dispute, null, "", false, "");
            return;
        }

        var sd = buildStripeDisputeRecord(mmStripeDisputeEventLog, dispute, payment);
        stripeDisputeMapper.insertSelective(sd);

        if (DisputeModel.FundWithdrawStatus.IS_WITHDRAWN.name().equalsIgnoreCase(sd.getFundWithdrawStatus())) {
            buildAndInsertStripeDisputeFundFlow(sd, DisputeFundingOperateModel.FundType.WITHDRAWN);
        }

        asyncSendNotificationDispute(dispute, payment, "create", sd.getDisputeFee() > 0);
    }

    private MmStripeDispute buildStripeDisputeRecord(
            MmStripeDisputeEventLog mmStripeDisputeEventLog, Dispute dispute, Payment payment) {
        MmStripeDispute sd = new MmStripeDispute();
        sd.setBusinessId(payment.getBusinessId());
        sd.setDisputeId(dispute.getId());
        sd.setPaymentId(payment.getId());
        sd.setPaymentIntentId(dispute.getPaymentIntent());
        sd.setAmount(dispute.getAmount());
        sd.setCurrency(dispute.getCurrency());
        sd.setStatus(dispute.getStatus());
        sd.setReason(dispute.getReason());
        sd.setDisputedOn(dispute.getCreated());
        // Set the newly added fields
        sd.setFundWithdrawStatus(StripeDisputeStatusEnum.matchDisputeEnum(dispute.getStatus())
                .decideFundWithdrawStatus()
                .name());
        sd.setLastHandledTime(mmStripeDisputeEventLog.getWebhookCreatedTime());

        if (StringUtils.hasText(dispute.getCharge())) {
            Charge charge;
            try {
                charge = Charge.retrieve(dispute.getCharge());
            } catch (StripeException e) {
                throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, e.getMessage(), e);
            }
            if (charge != null) {
                sd.setChargedOn(charge.getCreated());
                populateCustomer(sd, charge);
                populatePaymentMethod(sd, charge);
            }
        }
        Optional.ofNullable(dispute.getEvidenceDetails()).ifPresent(ed -> sd.setRespondedOn(ed.getDueBy()));

        // 检查是否需要 dispute_fee，如果商家是定制费率的话，就需要加上固定的 dispute_fee
        CompanyDto companyDto = iBusinessBusinessClient.getCompanyByBusinessId(payment.getBusinessId());
        MoeCustomizedPaymentSetting paymentSetting = customizedPaymentSettingService.getByCompanyId(companyDto.getId());
        if (paymentSetting != null) {
            sd.setDisputeFee(STRIPE_DISPUTE_FEE);
        } else {
            sd.setDisputeFee(0L);
        }

        return sd;
    }

    private void processEventForDisputeV1(MmStripeDisputeEventLog mmStripeDisputeEventLog) {
        var dispute = ApiResource.INTERNAL_GSON.fromJson(mmStripeDisputeEventLog.getWebhookBody(), Dispute.class);
        switch (mmStripeDisputeEventLog.getEventType()) {
            case StripeEvent.Dispute.CREATED:
                try {
                    stripeDisputeService.createDispute(dispute);
                    return;
                } catch (Exception e) {
                    log.error("Create dispute failed", e);
                    throw ExceptionUtil.bizException(Code.CODE_SERVER_ERROR, e.getMessage());
                }

            case StripeEvent.Dispute.CLOSED: {
                try {
                    stripeDisputeService.closeDispute(dispute, 0);
                    return;
                } catch (Exception e) {
                    log.error("Close dispute failed", e);
                    throw ExceptionUtil.bizException(Code.CODE_SERVER_ERROR, e.getMessage());
                }
            }
            case StripeEvent.Dispute.UPDATED:
                //                    case StripeEvent.Dispute.REINSTATED:
                //                    case StripeEvent.Dispute.WITHDRAWN:
                try {
                    stripeDisputeService.updateStripeDispute(dispute);
                    return;
                } catch (Exception e) {
                    log.error("Update dispute failed", e);
                    throw ExceptionUtil.bizException(Code.CODE_SERVER_ERROR, e.getMessage());
                }
        }
    }

    /**
     * 非首次执行dispute任务
     *
     * @param mmStripeDispute         The existing dispute record from the main table.
     * @param mmStripeDisputeEventLog The event log related to the dispute.
     */
    private void nonFirstTimeExecution(
            MmStripeDispute mmStripeDispute, MmStripeDisputeEventLog mmStripeDisputeEventLog) {

        // 判断即将要执行的webhook_create_time 与主表中的大小，如果 webhook 事件更早，则无需处理
        if (mmStripeDispute.getLastHandledTime() > mmStripeDisputeEventLog.getWebhookCreatedTime()) {
            log.warn("earlier event alreay handled, ignore eventId={}", mmStripeDisputeEventLog.getEventId());
            return;
        }

        Dispute dispute = ApiResource.INTERNAL_GSON.fromJson(mmStripeDisputeEventLog.getWebhookBody(), Dispute.class);
        mmStripeDispute.setLastHandledTime(mmStripeDisputeEventLog.getWebhookCreatedTime());
        mmStripeDispute.setStatus(mmStripeDisputeEventLog.getDisputeStatus());
        Optional.ofNullable(dispute.getEvidenceDetails())
                .ifPresent(ed -> mmStripeDispute.setRespondedOn(ed.getDueBy()));

        String currentFundWithdrawStatus = mmStripeDispute.getFundWithdrawStatus();
        String expectedFundWithDrawStatus = StripeDisputeStatusEnum.matchDisputeEnum(
                        mmStripeDisputeEventLog.getDisputeStatus())
                .decideFundWithdrawStatus()
                .name();
        if (!org.apache.commons.lang3.StringUtils.equalsIgnoreCase(
                currentFundWithdrawStatus, expectedFundWithDrawStatus)) {
            mmStripeDispute.setFundWithdrawStatus(expectedFundWithDrawStatus);
            DisputeFundingOperateModel.FundType fundType;
            if (DisputeModel.FundWithdrawStatus.NOT_WITHDRAWN.name().equalsIgnoreCase(expectedFundWithDrawStatus)) {
                fundType = DisputeFundingOperateModel.FundType.REINSTATED;
            } else {
                fundType = DisputeFundingOperateModel.FundType.WITHDRAWN;
                Payment payment = paymentService.getPaymentByPaymentIntentId(dispute.getPaymentIntent());
                asyncSendNotificationDispute(dispute, payment, "update", mmStripeDispute.getDisputeFee() > 0);
            }

            buildAndInsertStripeDisputeFundFlow(mmStripeDispute, fundType);
        }

        MmStripeDispute updateMmStripeDispute = new MmStripeDispute();
        updateMmStripeDispute.setId(mmStripeDispute.getId());
        updateMmStripeDispute.setLastHandledTime(mmStripeDispute.getLastHandledTime());
        updateMmStripeDispute.setStatus(mmStripeDispute.getStatus());
        updateMmStripeDispute.setRespondedOn(mmStripeDispute.getRespondedOn());
        updateMmStripeDispute.setFundWithdrawStatus(mmStripeDispute.getFundWithdrawStatus());
        mmStripeDisputeMapper.updateByPrimaryKeySelective(updateMmStripeDispute);
    }

    /**
     * 构建 Stripe 争议资金流对象 (MmStripeDisputeFundFlow)
     */
    private void buildAndInsertStripeDisputeFundFlow(
            MmStripeDispute mmStripeDispute, DisputeFundingOperateModel.FundType fundType) {

        MmStripeDisputeFundFlow mmStripeDisputeFundFlow = new MmStripeDisputeFundFlow();

        // 填入 connected account 和 company_id
        MmStripeAccount stripeAccount = stripeAccountMapper.selectByBusinessId(mmStripeDispute.getBusinessId());
        mmStripeDisputeFundFlow.setConnectedAccount(stripeAccount.getStripeAccountId());
        mmStripeDisputeFundFlow.setCompanyId(stripeAccount.getCompanyId());

        // 填入其他字段
        mmStripeDisputeFundFlow.setBusinessId(mmStripeDispute.getBusinessId().longValue());
        mmStripeDisputeFundFlow.setDisputeId(mmStripeDispute.getDisputeId());
        mmStripeDisputeFundFlow.setFundType(fundType.name());
        mmStripeDisputeFundFlow.setStatus(DisputeFundingOperateModel.Status.INIT.name());

        // 开始处理资金字段
        mmStripeDisputeFundFlow.setCurrency(mmStripeDispute.getCurrency());
        mmStripeDisputeFundFlow.setDisputeAmount(mmStripeDispute.getAmount());

        mmStripeDisputeFundFlow.setDisputeFee(0L);
        // 对于 dispute fee 有很多特殊逻辑要处理
        if (mmStripeDispute.getDisputeFee() > 0
                && processDisputeFee(mmStripeDispute, mmStripeDisputeFundFlow, fundType)) {
            mmStripeDisputeFundFlow.setDisputeFee(mmStripeDispute.getDisputeFee());
            log.info("dispute fee:{}", mmStripeDispute.getDisputeFee());
        }

        mmStripeDisputeFundFlow.setTotalAmount(
                mmStripeDisputeFundFlow.getDisputeAmount() + mmStripeDisputeFundFlow.getDisputeFee());

        mmStripeDisputeFundFlowMapper.insertSelective(mmStripeDisputeFundFlow);
    }

    // 处理 dispute fee，返回是否要操作 dispute fee
    private Boolean processDisputeFee(
            MmStripeDispute mmStripeDispute,
            MmStripeDisputeFundFlow mmStripeDisputeFundFlow,
            DisputeFundingOperateModel.FundType fundType) {

        var fee = disputeFeeMapper.selectByDisputeId(mmStripeDispute.getDisputeId());

        if (fundType == DisputeFundingOperateModel.FundType.WITHDRAWN) {
            // 第一次 charge 要生成 dispute fee 记录
            if (fee == null) {
                buildAndInsertNewDisputeFee(mmStripeDispute, mmStripeDisputeFundFlow);
                return true;
            }
            // 只有已经 refund 过的 dispute fee 才需要重新 charge
            if (StripeDisputeFeeStatusEnum.refund.name().equalsIgnoreCase(fee.getStatus())) {
                fee.setStatus(StripeDisputeFeeStatusEnum.charge.name());

                MmStripeDisputeFee updateFee = new MmStripeDisputeFee();
                updateFee.setId(fee.getId());
                updateFee.setStatus(fee.getStatus());
                disputeFeeMapper.updateByPrimaryKeySelective(updateFee);
                return true;
            }
            // 对于处于 charge 状态的，不需要再次 charge 了
            // 有什么情况可能导致这个分支：
            // 一个定制费率的商家，且不退还 dispute fee
            // needs_response(收取 dispute_amount + dispute_fee) -> won (仅退还 dispute_amount) -> lost (**此时**仅需收取
            // dispute_amount)
            return false;
        }

        if (fundType == DisputeFundingOperateModel.FundType.REINSTATED) {
            // 必须保证 refund 之前有过 charge
            // 且 该笔 dispute_fee 是需要退回的
            if (fee == null) {
                log.error("dispute fee not found, disputeId={}", mmStripeDispute.getDisputeId());
                throw ExceptionUtil.bizException(
                        Code.CODE_INVALID_CODE, "dispute fee not found, disputeId={}", mmStripeDispute.getDisputeId());
            }
            if (!StripeDisputeFeeStatusEnum.charge.name().equalsIgnoreCase(fee.getStatus())) {
                log.error(
                        "dispute fee status is invalid, disputeId={}, status={}",
                        mmStripeDispute.getDisputedOn(),
                        fee.getStatus());
                throw ExceptionUtil.bizException(Code.CODE_INVALID_CODE, "dispute fee status is invalid");
            }
            if (!fee.getNeedRefund()) {
                return false;
            }

            fee.setStatus(StripeDisputeFeeStatusEnum.refund.name());

            MmStripeDisputeFee updateFee = new MmStripeDisputeFee();
            updateFee.setId(fee.getId());
            updateFee.setStatus(fee.getStatus());
            disputeFeeMapper.updateByPrimaryKeySelective(updateFee);

            return true;
        }

        throw ExceptionUtil.bizException(Code.CODE_INVALID_CODE, "unsupported fund type in dispute fund flow");
    }

    private void buildAndInsertNewDisputeFee(
            MmStripeDispute mmStripeDispute, MmStripeDisputeFundFlow mmStripeDisputeFundFlow) {
        var fee = new MmStripeDisputeFee();
        fee.setAmount(mmStripeDispute.getDisputeFee());
        // 新逻辑：只要插入 fund flow 表，则认为已经 charge 过了，所以此处直接置为 charge
        fee.setStatus(StripeDisputeFeeStatusEnum.charge.name());
        fee.setDisputeId(mmStripeDispute.getDisputeId());
        fee.setBusinessId(mmStripeDisputeFundFlow.getBusinessId().intValue());
        fee.setCompanyId(mmStripeDisputeFundFlow.getCompanyId());

        CompanyDto companyDto = iBusinessBusinessClient.getCompanyByBusinessId(mmStripeDispute.getBusinessId());
        MoeCustomizedPaymentSetting paymentSetting = customizedPaymentSettingService.getByCompanyId(companyDto.getId());
        fee.setNeedRefund(paymentSetting.getCreateTime().before(NEW_PACT_DATE));

        disputeFeeMapper.insertSelective(fee);
    }

    public void processAllDisputeFundFlow() {
        List<MmStripeDisputeFundFlow> mmStripeDisputeFundFlows =
                mmStripeDisputeFundFlowMapper.selectByStatus(DisputeFundingOperateModel.Status.INIT.name());
        if (CollectionUtils.isEmpty(mmStripeDisputeFundFlows)) {
            return;
        }
        for (MmStripeDisputeFundFlow mmStripeDisputeFundFlow : mmStripeDisputeFundFlows) {
            try {
                processDisputeFundFlow(mmStripeDisputeFundFlow);
            } catch (Exception e) {
                log.error("processDisputeFundFlow error:{}", mmStripeDisputeFundFlow.getId(), e);
            }
        }
    }

    private void processDisputeFundFlow(MmStripeDisputeFundFlow mmStripeDisputeFundFlow) {
        transactionOperations.executeWithoutResult(status -> {
            MmStripeDispute mmStripeDispute =
                    mmStripeDisputeMapper.queryByDisputeId(mmStripeDisputeFundFlow.getDisputeId());
            if (DisputeFundingOperateModel.FundType.WITHDRAWN
                    .name()
                    .equalsIgnoreCase(mmStripeDisputeFundFlow.getFundType())) {
                try {
                    processDisputeFundFlowWithdraw(mmStripeDisputeFundFlow);
                } catch (StripeException e) {
                    throw new RuntimeException(e);
                }
                messageDeliveryService.saveDisputeFundFlowMessageDelivery(mmStripeDisputeFundFlow, mmStripeDispute);
                return;
            }
            if (DisputeFundingOperateModel.FundType.REINSTATED
                    .name()
                    .equalsIgnoreCase(mmStripeDisputeFundFlow.getFundType())) {
                try {
                    processDisputeFundFlowInstate(mmStripeDisputeFundFlow);
                } catch (StripeException e) {
                    throw new RuntimeException(e);
                }
                messageDeliveryService.saveDisputeFundFlowMessageDelivery(mmStripeDisputeFundFlow, mmStripeDispute);
                return;
            }
            log.error("unsupported fund type:{}", mmStripeDisputeFundFlow.getFundType());
            throw ExceptionUtil.bizException(Code.CODE_INVALID_CODE, "nonsupport fund type in dispute fund flow");
        });
    }

    private void processDisputeFundFlowWithdraw(MmStripeDisputeFundFlow mmStripeDisputeFundFlow)
            throws StripeException {
        RequestOptions options = RequestOptions.builder()
                .setIdempotencyKey(DISPUTE_FUND_WITHDRAW_IDEMPOTENCY_KEY + mmStripeDisputeFundFlow.getId())
                .build();
        Charge charge = Charge.create(
                ChargeCreateParams.builder()
                        .setAmount(mmStripeDisputeFundFlow.getTotalAmount())
                        .setCurrency(mmStripeDisputeFundFlow.getCurrency())
                        .setSource(mmStripeDisputeFundFlow.getConnectedAccount())
                        .setDescription(DISPUTE_CREATE_FEE_DESC)
                        .build(),
                options);

        if (mmStripeDisputeFundFlow.getDisputeFee() > 0) {
            var fee = disputeFeeMapper.selectByDisputeId(mmStripeDisputeFundFlow.getDisputeId());

            MmStripeDisputeFee updateFee = new MmStripeDisputeFee();
            updateFee.setId(fee.getId());
            updateFee.setAmount(charge.getAmount());
            updateFee.setChargeId(charge.getId());
            disputeFeeMapper.updateByPrimaryKeySelective(updateFee);
        }

        mmStripeDisputeFundFlow.setTransactionId(charge.getId());
        mmStripeDisputeFundFlow.setOperatedTime(new Date(charge.getCreated() * 1000));
        mmStripeDisputeFundFlow.setStatus(DisputeFundingOperateModel.Status.SUCCEED.name());

        MmStripeDisputeFundFlow updateMmStripeDisputeFundFlow = new MmStripeDisputeFundFlow();
        updateMmStripeDisputeFundFlow.setId(mmStripeDisputeFundFlow.getId());
        updateMmStripeDisputeFundFlow.setTransactionId(mmStripeDisputeFundFlow.getTransactionId());
        updateMmStripeDisputeFundFlow.setOperatedTime(mmStripeDisputeFundFlow.getOperatedTime());
        updateMmStripeDisputeFundFlow.setStatus(mmStripeDisputeFundFlow.getStatus());
        mmStripeDisputeFundFlowMapper.updateByPrimaryKeySelective(updateMmStripeDisputeFundFlow);
    }

    private void processDisputeFundFlowInstate(MmStripeDisputeFundFlow mmStripeDisputeFundFlow) throws StripeException {
        RequestOptions options = RequestOptions.builder()
                .setIdempotencyKey(DISPUTE_FUND_INSTATE_IDEMPOTENCY_KEY + mmStripeDisputeFundFlow.getId())
                .build();
        Transfer transfer = Transfer.create(
                TransferCreateParams.builder()
                        .setAmount(mmStripeDisputeFundFlow.getTotalAmount())
                        .setDestination(mmStripeDisputeFundFlow.getConnectedAccount())
                        .setCurrency(mmStripeDisputeFundFlow.getCurrency())
                        .setDescription(DISPUTE_REFUND_REVERSE_DESC)
                        .build(),
                options);
        mmStripeDisputeFundFlow.setTransactionId(transfer.getId());
        mmStripeDisputeFundFlow.setOperatedTime(new Date(transfer.getCreated() * 1000));
        mmStripeDisputeFundFlow.setStatus(DisputeFundingOperateModel.Status.SUCCEED.name());

        MmStripeDisputeFundFlow updateMmStripeDisputeFundFlow = new MmStripeDisputeFundFlow();
        updateMmStripeDisputeFundFlow.setId(mmStripeDisputeFundFlow.getId());
        updateMmStripeDisputeFundFlow.setTransactionId(mmStripeDisputeFundFlow.getTransactionId());
        updateMmStripeDisputeFundFlow.setOperatedTime(mmStripeDisputeFundFlow.getOperatedTime());
        updateMmStripeDisputeFundFlow.setStatus(mmStripeDisputeFundFlow.getStatus());
        mmStripeDisputeFundFlowMapper.updateByPrimaryKeySelective(updateMmStripeDisputeFundFlow);

        log.info(
                "transfer to {} amount:{} success:{}",
                mmStripeDisputeFundFlow.getConnectedAccount(),
                mmStripeDisputeFundFlow.getTotalAmount(),
                transfer.getId());
    }

    /**
     * Page query StripeDisputes by given StripeDispute conditions.
     *
     * @param stripeDispute StripeDispute conditions
     * @param pageRequest   PageRequest
     * @return Page of StripeDispute
     */
    public Page<MmStripeDispute> queryByPage(MmStripeDispute stripeDispute, PageRequest pageRequest) {
        long total = this.stripeDisputeMapper.count(stripeDispute);
        if (total == 0) {
            return new PageImpl<>(Collections.emptyList(), pageRequest, 0);
        }
        return new PageImpl<>(this.stripeDisputeMapper.queryAllByLimit(stripeDispute, pageRequest), pageRequest, total);
    }

    private void populatePaymentMethod(MmStripeDispute sd, Charge charge) {
        if (charge.getPaymentMethodDetails() != null
                && charge.getPaymentMethodDetails().getCard() != null) {
            Charge.PaymentMethodDetails.Card card =
                    charge.getPaymentMethodDetails().getCard();
            String brand = card.getBrand();
            String last4 = card.getLast4();
            sd.setPaymentMethod(String.format("%s (%s)", brand, last4));
        }
    }

    private void populateCustomer(MmStripeDispute sd, Charge charge) {
        String customerId = charge.getCustomer();
        if (StringUtils.hasText(customerId)) {
            Customer customer;
            try {
                customer = Customer.retrieve(customerId);
            } catch (StripeException e) {
                throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, e.getMessage(), e);
            }
            if (customer != null) {
                sd.setCustomer(customer.getName());
            }
        }
    }

    private void notifySlack(
            Dispute customerDispute,
            Payment existingPayment,
            String exceptionInfo,
            boolean chargeFee,
            String transferId) {
        StringBuilder bizStr = new StringBuilder("New Dispute Event")
                .append(System.lineSeparator())
                .append("DisputeId: ")
                .append(customerDispute.getId())
                .append(System.lineSeparator())
                .append("Date: ")
                .append(LocalDateTime.ofEpochSecond(customerDispute.getCreated(), 0, ZoneOffset.UTC))
                .append(System.lineSeparator())
                .append("Status: ")
                .append(customerDispute.getStatus())
                .append(System.lineSeparator())
                .append("Amount: ")
                .append(BigDecimal.valueOf(customerDispute.getAmount())
                        .divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP))
                .append(System.lineSeparator())
                .append("PaymentIntent: ")
                .append(customerDispute.getPaymentIntent())
                .append(System.lineSeparator());
        if (StringUtils.hasText(transferId)) {
            bizStr.append(System.lineSeparator())
                    .append("TransferId: ")
                    .append(transferId)
                    .append(System.lineSeparator());
        }
        if (existingPayment != null) {
            MoeBusinessDto bizInfo = iBusinessBusinessService.getBusinessInfoWithOwnerEmail(InfoIdParams.builder()
                    .infoId(existingPayment.getBusinessId())
                    .build());
            bizStr.append(System.lineSeparator())
                    .append("Business id: ")
                    .append(existingPayment.getBusinessId())
                    .append(System.lineSeparator())
                    .append("Business name: ")
                    .append(bizInfo.getBusinessName())
                    .append(System.lineSeparator())
                    .append("Business email: ")
                    .append(bizInfo.getOwnerEmail())
                    .append(System.lineSeparator())
                    .append("Customer name: ")
                    .append(existingPayment.getPaidBy());
        } else {
            bizStr.append(System.lineSeparator()).append("not found biz info");
        }
        if (chargeFee) {
            bizStr.append(System.lineSeparator())
                    .append("Dispute Fee:")
                    .append(STRIPE_DISPUTE_FEE / 100)
                    .append(".00$");
        }
        if (StringUtils.hasLength(exceptionInfo)) {
            bizStr.append(System.lineSeparator()).append("ERROR!!!");
            bizStr.append(System.lineSeparator()).append("Exception info: ").append(exceptionInfo);
        }
        bizStr.append(System.lineSeparator()).append("live mode: ").append(customerDispute.getLivemode());
        iPaymentSlackClient.sendBonusClaim(
                BookFeeClaimParam.builder().bookingFee(bizStr.toString()).build());
    }

    public DisputeCollection listDispute(ListDisputeRequest request) throws StripeException {
        Map<String, Object> params = new HashMap<>();
        params.put("limit", request.getLimit());
        if (request.getStartingAfter() != null) {
            params.put("starting_after", request.getStartingAfter());
        }
        List<String> expandList = new ArrayList<>();
        expandList.add("data.charge");
        expandList.add("data.charge.customer");
        expandList.add("data.payment_intent");
        expandList.add("data.payment_intent.payment_method");
        expandList.add("data.balance_transactions");
        expandList.add("data.evidence.cancellation_policy");
        expandList.add("data.evidence.customer_communication");
        expandList.add("data.evidence.customer_signature");
        expandList.add("data.evidence.duplicate_charge_documentation");
        expandList.add("data.evidence.receipt");
        expandList.add("data.evidence.refund_policy");
        expandList.add("data.evidence.service_documentation");
        expandList.add("data.evidence.shipping_documentation");
        expandList.add("data.evidence.uncategorized_file");
        params.put("expand", expandList);
        RequestOptions requestOptions = RequestOptions.builder()
                .setStripeAccount(request.getAccountId())
                .build();
        return com.stripe.model.Dispute.list(params, requestOptions);
    }

    public Page<MmStripeDispute> listStripeDisputesByPage(Integer businessId, PageRequest pageRequest) {
        MmStripeDispute stripeDispute = new MmStripeDispute();
        stripeDispute.setBusinessId(businessId);
        return queryByPage(stripeDispute, pageRequest);
    }

    public Page<MmStripeDispute> listStripeDisputesByRangePage(
            Integer businessId, Long startTimeStamp, Long endTimestamp, DisputeQueryVO queryVO) {
        PageRequest pageRequest = PageRequest.of(queryVO.getPageNo() - 1, queryVO.getPageSize());
        QueryStripeDisputeParams params = QueryStripeDisputeParams.builder()
                .businessId(Long.valueOf(businessId))
                .status(queryVO.getStatus())
                .dateStart(startTimeStamp)
                .dateEnd(endTimestamp)
                .orderByDesc((byte) 1)
                .build();
        long total = this.stripeDisputeMapper.countByParams(params);
        if (total == 0) {
            return new PageImpl<>(Collections.emptyList(), pageRequest, 0);
        }
        return new PageImpl<>(
                stripeDisputeMapper.queryStripeDisputeList(
                        params, pageRequest.getPageNumber(), pageRequest.getPageSize()),
                pageRequest,
                total);
    }

    public void updateDisputeReadStatus(Integer businessId) {
        stripeDisputeMapper.updateReadStatusByBizId(businessId);
    }

    public DisputeReadStatusVO getDisputeReadStatus(Integer businessId) {
        int count = stripeDisputeMapper.countReadStatusByBizId(businessId);
        return DisputeReadStatusVO.builder().hasUnread(count > 0).build();
    }

    /**
     * 异步方法 产生 dispute 之后 进行 notification 通知，适用于正式争议的场景
     *
     * @param dispute     dispute stripe 发送的实体
     * @param payment     payment payment 记录
     * @param disputeType disputeType 争议类型 ，上游决定，如果是非正式争议上下文文案会不一致
     * @param chargeFee   是否是定制费率商家
     */
    private void asyncSendNotificationDispute(Dispute dispute, Payment payment, String disputeType, Boolean chargeFee) {
        ThreadPool.submit(() -> {
            // 声明obj
            NotificationExtraDisputeDto disputeDto = new NotificationExtraDisputeDto();
            NotificationDisputeParams disputeParams = new NotificationDisputeParams();
            // 通用赋值
            disputeParams.setBusinessId(payment.getBusinessId());
            disputeDto.setDisputeAmount(
                    BigDecimal.valueOf(dispute.getAmount()).divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP));
            disputeDto.setDisputeId(dispute.getId());
            disputeDto.setPaymentId(payment.getId());
            disputeDto.setCustomerId(payment.getCustomerId());
            disputeParams.setWebPushDto(disputeDto);
            // 非正式争议升级为正式争议场景；默认情况会创建正式争议的notification；update的意思是升级成正式争议的意思。
            if (("update").equals(disputeType)) {
                disputeParams.setTitle("Dispute inquiry");
                // todo 需要等待CRM进行配合发版 https://moego.atlassian.net/browse/CRM-2365，在发布之前暂时使用这个notification 类型
                disputeParams.setType(NotificationEnum.TYPE_ACTIVITY_PAYMENT_DISPUTE);
                disputeParams.setMobilePushTitle("Dispute inquiry");
                disputeParams.setMobilePushBody(
                        "A {totalPaidAmount} payment with {customerFullName} has received dispute inquiry .");
            }
            iNotificationClient.sendNotificationDispute(disputeParams);
            notifySlack(dispute, payment, "", chargeFee, "");
        });
    }
}
