package com.moego.server.payment.service;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.model.CannedAccessControlList;
import com.amazonaws.services.s3.model.ObjectMetadata;
import com.amazonaws.services.s3.model.PutObjectRequest;
import com.moego.common.utils.CommonUtil;
import com.moego.idl.models.errors.v1.Code;
import com.moego.lib.common.exception.ExceptionUtil;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

/**
 * @create: 2023/3/9 11:11
 * @author: channy.shu
 **/
@Service
@Slf4j
public class ExportService {

    @Autowired
    private AmazonS3 s3Client;

    @Value("${s3.payment.public.bucket}")
    private String publicCustomerBucket;

    @Value("${s3.payment.public.key.prefix}")
    private String publicCustomerPrefix;

    @Value("${s3.domain}")
    private String endpoint;

    public static final String DEFAULT_EXPORT_TYPE = ExcelTypeEnum.CSV.name();

    public String exportAndUploadToS3(List<?> data, Class<?> clazz, String fileName, String fileType) {
        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
            if (!StringUtils.hasText(fileName)) {
                fileName = CommonUtil.getUuid();
            } else {
                fileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8).replaceAll("\\+", "%20");
            }
            if (!StringUtils.hasText(fileType)) {
                fileType = DEFAULT_EXPORT_TYPE;
            }
            ExcelTypeEnum type = ExcelTypeEnum.CSV;
            if (!fileType.equalsIgnoreCase(ExcelTypeEnum.CSV.name())) {
                type = ExcelTypeEnum.XLSX;
            }
            EasyExcel.write(outputStream, clazz)
                    .excelType(type)
                    .autoCloseStream(false)
                    .sheet("sheet")
                    .doWrite(data);
            ObjectMetadata omd = new ObjectMetadata();
            omd.setHeader("Content-Disposition", "attachment; filename=\"" + fileName + "." + fileType + "\"");
            ByteArrayInputStream inputStream = new ByteArrayInputStream(outputStream.toByteArray());
            omd.setContentLength(inputStream.available());
            // 保存在s3上的文件名
            String key = publicCustomerPrefix + CommonUtil.getUuid() + "." + fileType;
            s3Client.putObject(new PutObjectRequest(publicCustomerBucket, key, inputStream, omd)
                    .withCannedAcl(CannedAccessControlList.PublicRead));
            inputStream.close();
            return endpoint + key;
        } catch (Exception e) {
            log.error("export error", e);
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "Export Excel Error:" + e.getMessage());
        }
    }
}
