package com.moego.server.payment.web.dto;

import lombok.Getter;
import lombok.Setter;

/**
 * payout 关联的 transaction 信息，一笔 payout 由若干个 transaction 结算而来
 * 此结构是对第三方支付平台 transaction 信息的封装
 *  - 目前仅支持 stripe，详情参见：https://stripe.com/docs/api/balance_transactions/object
 */
@Getter
@Setter
public class PayoutTransaction {

    // transaction id
    private String id;

    // 交易类型
    private String type;

    // 交易金额
    private Long amount;

    // 交易产生的 fee
    private Long fee;

    // net = amount - fee
    private Long net;

    // 币种：标准 ISO 三位货币代码
    private String currency;

    // 交易方名称
    private String transferredBy;

    // 交易状态：available/pending
    private String status;

    // 产生此交易的原因，一般 dispute 类型此字段有值
    private String reason;

    // 第三方平台的数据结构类型名
    private String sourceObject;

    // stripe payment intent id
    private String paymentIntentId;

    // 交易关联的业务类型：grooming/retail
    private String serviceType;

    // 交易关联的业务子类型：appointment/no-show/package/product
    private String sourceType;

    // payment 表 id
    private Long paymentId;

    // order 表 id
    private Long orderId;

    // 视 serviceType 不同，可能为 appointmentId
    private Long targetId;

    // customer 表 id
    private Long customerId;

    // 描述信息
    private String description;

    // 用于前端展示的备注信息，根据交易类型生成
    private String notes;

    // 交易创建时间：以秒为单位的时间戳
    private Long createTime;

    // 金额可用时间：以秒为单位的时间戳
    private Long availableTime;

    private String refundId;

    // 对应 refund 表的主键id
    private Integer refundPrimaryId;
}
