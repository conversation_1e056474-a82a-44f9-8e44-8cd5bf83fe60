package com.moego.server.payment.mapstruct;

import com.moego.server.payment.dto.refund.TransactionHistoryView;
import com.moego.server.payment.mapperbean.Payment;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @since 2024/1/2
 */
@Mapper
public interface TransactionHistoryMapper {
    TransactionHistoryMapper INSTANCE = Mappers.getMapper(TransactionHistoryMapper.class);

    @Mapping(target = "status", ignore = true)
    @Mapping(target = "stripePaymentMethod", ignore = true)
    @Mapping(target = "squarePaymentMethod", ignore = true)
    TransactionHistoryView boToView(Payment bo);
}
