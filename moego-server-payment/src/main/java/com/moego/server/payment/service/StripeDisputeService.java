package com.moego.server.payment.service;

import com.moego.api.thirdparty.IPaymentSlackClient;
import com.moego.common.constant.ActiveMQConstant;
import com.moego.common.distributed.LockManager;
import com.moego.common.dto.notificationDto.NotificationEnum;
import com.moego.common.dto.notificationDto.NotificationExtraDisputeDto;
import com.moego.common.enums.ResponseCodeEnum;
import com.moego.common.enums.StripeApi;
import com.moego.common.enums.payment.SplitSyncRecordTypeEnum;
import com.moego.common.exception.CommonException;
import com.moego.common.utils.CommonUtil;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.split_payment.v1.Vendor;
import com.moego.lib.actimvemq.autoconfigure.MoeMessageSender;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.lib.common.thread.ThreadPool;
import com.moego.server.business.api.IBusinessBusinessService;
import com.moego.server.business.client.IBusinessBusinessClient;
import com.moego.server.business.dto.CompanyDto;
import com.moego.server.business.dto.MoeBusinessDto;
import com.moego.server.business.params.InfoIdParams;
import com.moego.server.message.client.INotificationClient;
import com.moego.server.message.params.notification.NotificationDisputeParams;
import com.moego.server.payment.constant.StripeDisputeFeeStatusEnum;
import com.moego.server.payment.constant.StripeDisputeStatusEnum;
import com.moego.server.payment.mapper.MmStripeAccountMapper;
import com.moego.server.payment.mapper.MmStripeDisputeFeeMapper;
import com.moego.server.payment.mapper.MmStripeDisputeMapper;
import com.moego.server.payment.mapperbean.MmStripeAccount;
import com.moego.server.payment.mapperbean.MmStripeDispute;
import com.moego.server.payment.mapperbean.MmStripeDisputeFee;
import com.moego.server.payment.mapperbean.MoeCustomizedPaymentSetting;
import com.moego.server.payment.mapperbean.Payment;
import com.moego.server.payment.params.BookFeeClaimParam;
import com.moego.server.payment.util.DisputeStateMachine;
import com.stripe.exception.StripeException;
import com.stripe.model.Charge;
import com.stripe.model.Customer;
import com.stripe.model.Dispute;
import com.stripe.model.Refund;
import com.stripe.model.Transfer;
import com.stripe.param.ChargeCreateParams;
import com.stripe.param.ChargeUpdateParams;
import com.stripe.param.RefundCreateParams;
import com.stripe.param.TransferCreateParams;
import com.stripe.param.TransferReversalCollectionCreateParams;
import jakarta.jms.JMSException;
import jakarta.jms.Queue;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;
import org.apache.activemq.ScheduledMessage;
import org.apache.activemq.command.ActiveMQTextMessage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.jms.annotation.JmsListener;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionOperations;
import org.springframework.util.StringUtils;

@Service
@Slf4j
public class StripeDisputeService {

    @Autowired
    private MmStripeDisputeMapper stripeDisputeMapper;

    @Autowired
    private MmStripeAccountMapper stripeAccountMapper;

    @Autowired
    private TransactionOperations transactionOperations;

    @Autowired
    private CustomizedPaymentSettingService customizedPaymentSettingService;

    @Autowired
    private IBusinessBusinessClient iBusinessBusinessClient;

    @Autowired
    private MmStripeDisputeFeeMapper disputeFeeMapper;

    @Autowired
    private IPaymentSlackClient iPaymentSlackClient;

    @Autowired
    private PaymentService paymentService;

    @Autowired
    private IBusinessBusinessService iBusinessBusinessService;

    @Autowired
    private LockManager lockManager;

    @Autowired
    private MoeMessageSender moeMessageSender;

    @Autowired
    @Qualifier("disputeRetryQueue")
    private Queue disputeRetryQueue;

    @Autowired
    private INotificationClient iNotificationClient;

    @Autowired
    private SplitPaymentService splitPaymentService;

    /**
     * 目前固定15 usd
     */
    public static final Long STRIPE_DISPUTE_FEE = 1500L;

    public static final String DISPUTE_CREATE_REVERSE_DESC = "Transaction disputed";
    public static final String DISPUTE_CREATE_FEE_DESC = "Dispute fee";
    public static final String DISPUTE_REFUND_REVERSE_DESC = "Dispute won";
    private final Date NEW_PACT_DATE = new GregorianCalendar(2023, Calendar.MARCH, 11).getTime();

    public MmStripeDispute queryByDisputeId(String disputeId) {
        return this.stripeDisputeMapper.queryByDisputeId(disputeId);
    }

    /**
     * Page query StripeDisputes by given StripeDispute conditions.
     *
     * @param stripeDispute StripeDispute conditions
     * @param pageRequest   PageRequest
     * @return Page of StripeDispute
     */
    public Page<MmStripeDispute> queryByPage(MmStripeDispute stripeDispute, PageRequest pageRequest) {
        long total = this.stripeDisputeMapper.count(stripeDispute);
        if (total == 0) {
            return new PageImpl<>(Collections.emptyList(), pageRequest, 0);
        }
        return new PageImpl<>(this.stripeDisputeMapper.queryAllByLimit(stripeDispute, pageRequest), pageRequest, total);
    }

    /**
     * 处理update 类型的 dispute web-hook ，有资金操作
     *
     * @param dispute stripe 发送过来的 dispute 实体
     * @throws StripeException stripe 定义的异常
     */
    public void updateStripeDispute(Dispute dispute) throws Exception {
        Payment payment = paymentService.getPaymentByPaymentIntentId(dispute.getPaymentIntent());
        if (payment == null) {
            return;
        }
        if (StripeDisputeStatusEnum.needs_response.name().equalsIgnoreCase(dispute.getStatus())) {
            // 处理需要资金操作的状态
            handleFundsOperationForUpdateEvent(dispute, payment, StripeDisputeStatusEnum.needs_response);
        } else if (StripeDisputeStatusEnum.under_review.name().equalsIgnoreCase(dispute.getStatus())
                || StripeDisputeStatusEnum.warning_under_review.name().equalsIgnoreCase(dispute.getStatus())) {
            // 处理直接持久化的中间状态
            insertStripeDisputeOnly(dispute);
        } else {
            // 其他状态抛出异常
            throw ExceptionUtil.bizException(Code.CODE_INVALID_CODE, "nonsupport dispute status in update web-hook");
        }
    }

    /**
     * UPDATE event 处理需要资金操作的状态
     *
     * @param dispute     Stripe dispute 实体
     * @param payment     支付记录
     * @param targetState 需要进行处理的状态
     * @throws StripeException Stripe 包装好的 Exception
     */
    private void handleFundsOperationForUpdateEvent(
            Dispute dispute, Payment payment, StripeDisputeStatusEnum targetState) throws Exception {

        String resourceKey = lockManager.getResourceKey(LockManager.DISPUTE_CREATE, dispute.getId());
        String value = CommonUtil.getUuid();
        try {
            if (lockManager.lock(resourceKey, value)) {
                MmStripeDispute stripeDisputeInfo = queryByDisputeId(dispute.getId());
                if (stripeDisputeInfo == null) {
                    throw ExceptionUtil.bizException(Code.CODE_SERVER_ERROR);
                }
                // 当前状态是needs_response 并且 是update 事件 并且 当前数据库状态为 under_review状态，不处理后续流程
                if (dispute.getStatus().equalsIgnoreCase(String.valueOf(StripeDisputeStatusEnum.needs_response))
                        && (stripeDisputeInfo
                                        .getStatus()
                                        .equalsIgnoreCase(String.valueOf(StripeDisputeStatusEnum.under_review))
                                || stripeDisputeInfo
                                        .getStatus()
                                        .equalsIgnoreCase(String.valueOf(StripeDisputeStatusEnum.needs_response)))) {
                    return;
                }
                StripeDisputeStatusEnum currentState =
                        StripeDisputeStatusEnum.matchDisputeEnum(stripeDisputeInfo.getStatus());
                // 执行 dispute 状态机
                handleDisputeStateTransition(stripeDisputeInfo, currentState, targetState);
                collectDisputeForUnderDispute(dispute, payment);
            }
        } catch (Exception e) {
            notifySlack(dispute, payment, e.getMessage(), false, "");
            log.error("create dispute error:{}", dispute.getId(), e);
            throw e;
        } finally {
            lockManager.unlock(resourceKey, value);
        }
    }

    /**
     * 处理不需要资金操作的状态，直接持久化
     *
     * @param dispute stripe 发送过来的dispute 资金实体
     */
    private void insertStripeDisputeOnly(Dispute dispute) {
        MmStripeDispute stripeDisputeInfo = queryByDisputeId(dispute.getId());
        StripeDisputeStatusEnum currentState = StripeDisputeStatusEnum.matchDisputeEnum(stripeDisputeInfo.getStatus());
        // 执行状态机
        handleDisputeStateTransition(
                stripeDisputeInfo, currentState, StripeDisputeStatusEnum.valueOf(dispute.getStatus()));
        insertStripeDispute(dispute, stripeDisputeInfo);
    }

    /**
     * 处理不需要资金操作的状态，直接持久化 针对 close 事件处理
     *
     * @param dispute stripe 发送过来的dispute 资金实体
     */
    private void updateStripeDisputeOnlyForClosedEvent(Dispute dispute) {
        MmStripeDispute stripeDisputeInfo = queryByDisputeId(dispute.getId());
        StripeDisputeStatusEnum currentState = StripeDisputeStatusEnum.matchDisputeEnum(stripeDisputeInfo.getStatus());
        // 执行状态机
        handleDisputeStateTransition(
                stripeDisputeInfo, currentState, StripeDisputeStatusEnum.valueOf(dispute.getStatus()));
        updateDisputeAndDisputeFee(dispute, stripeDisputeInfo);
    }

    /**
     * dispute状态机处理器
     *
     * @param stripeDispute stripe 发送过来的 dispute 实体
     * @param currentState  当前 current 状态机
     * @param targetState   目标 current 状态机
     */
    private void handleDisputeStateTransition(
            MmStripeDispute stripeDispute, StripeDisputeStatusEnum currentState, StripeDisputeStatusEnum targetState) {
        // 判空，内部错误，不需要 stripe 重试
        if (stripeDispute == null) {
            throw ExceptionUtil.bizException(Code.CODE_INVALID_CODE);
        }
        // 状态机对象声明
        DisputeStateMachine stateMachine = new DisputeStateMachine();
        // 执行 dispute 状态机
        if (stateMachine.canTransitionTo(currentState, targetState)) {
            log.info(
                    "Dispute state machine executed successfully; "
                            + "transition from current state {} to target state {} is allowed.",
                    currentState,
                    targetState);
        } else { // 内部错误，不需要 stripe 重试
            throw ExceptionUtil.bizException(Code.CODE_INVALID_CODE);
        }
    }

    /**
     * 持久化当前 stripe dispute status
     *
     * @param dispute Stripe dispute 实体
     */
    private void insertStripeDispute(Dispute dispute, MmStripeDispute stripeDispute) {
        MmStripeDispute sd = new MmStripeDispute();
        sd.setId(stripeDispute.getId());
        sd.setPaymentIntentId(dispute.getPaymentIntent());
        sd.setReason(dispute.getReason());
        sd.setStatus(dispute.getStatus());
        Optional.ofNullable(dispute.getEvidenceDetails()).ifPresent(ed -> sd.setRespondedOn(ed.getDueBy()));
        stripeDisputeMapper.updateByPrimaryKeySelective(sd);
    }

    private MmStripeDispute buildStripeDispute(Dispute dispute, Payment payment, Long readDisputeAmount) {
        MmStripeDispute sd = new MmStripeDispute();
        sd.setBusinessId(payment.getBusinessId());
        sd.setDisputeId(dispute.getId());
        sd.setPaymentId(payment.getId());
        sd.setPaymentIntentId(dispute.getPaymentIntent());
        sd.setCurrency(dispute.getCurrency());
        sd.setAmount(readDisputeAmount);
        sd.setReason(dispute.getReason());
        sd.setStatus(dispute.getStatus());
        sd.setDisputedOn(dispute.getCreated());
        if (StringUtils.hasText(dispute.getCharge())) {
            Charge charge;
            try {
                charge = Charge.retrieve(dispute.getCharge());
            } catch (StripeException e) {
                throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, e.getMessage(), e);
            }
            if (charge != null) {
                sd.setChargedOn(charge.getCreated());
                populateCustomer(sd, charge);
                populatePaymentMethod(sd, charge);
            }
        }
        Optional.ofNullable(dispute.getEvidenceDetails()).ifPresent(ed -> sd.setRespondedOn(ed.getDueBy()));
        return sd;
    }

    private void populatePaymentMethod(MmStripeDispute sd, Charge charge) {
        if (charge.getPaymentMethodDetails() != null
                && charge.getPaymentMethodDetails().getCard() != null) {
            Charge.PaymentMethodDetails.Card card =
                    charge.getPaymentMethodDetails().getCard();
            String brand = card.getBrand();
            String last4 = card.getLast4();
            sd.setPaymentMethod(String.format("%s (%s)", brand, last4));
        }
    }

    private void populateCustomer(MmStripeDispute sd, Charge charge) {
        String customerId = charge.getCustomer();
        if (StringUtils.hasText(customerId)) {
            Customer customer;
            try {
                customer = Customer.retrieve(customerId);
            } catch (StripeException e) {
                throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, e.getMessage(), e);
            }
            if (customer != null) {
                sd.setCustomer(customer.getName());
            }
        }
    }

    /**
     * WebHook 处理 Dispute 金额
     *
     * @param dispute Dispute 实体
     * @return 实际 进入dispute流程的 dispute amount 或 null
     * @throws StripeException Stripe通用Exception
     */
    public Long createDispute(Dispute dispute) throws StripeException {
        Payment payment = paymentService.getPaymentByPaymentIntentId(dispute.getPaymentIntent());
        if (payment == null) {
            // B端退款订阅费,只通知slack
            notifySlack(dispute, null, "", false, "");
            return null;
        }
        if (StripeDisputeStatusEnum.warning_needs_response.name().equalsIgnoreCase(dispute.getStatus())) {
            // 在create 事件中，如果 dispute status == StripeDisputeStatusEnum.warning_needs_response 只需要做持久化处理
            insertStripeDispute(dispute, payment, getReverseAmount(dispute));
            // 异步 发送 notification
            asyncSendNotificationDispute(dispute, payment, "create", false);
        } else if (StripeDisputeStatusEnum.needs_response.name().equalsIgnoreCase(dispute.getStatus())) {
            // 在create 事件中，如果dispute status == StripeDisputeStatusEnum.needs_response 并需要进行资金操作
            String resourceKey = lockManager.getResourceKey(LockManager.DISPUTE_CREATE, dispute.getId());
            String value = CommonUtil.getUuid();
            try {
                if (lockManager.lock(resourceKey, value)) {
                    return collectDisputeAndSendNotification(dispute, payment);
                }
            } catch (Exception e) {
                notifySlack(dispute, payment, e.getMessage(), false, "");
                log.error("create dispute error:{}", dispute.getId(), e);
                throw e;
            } finally {
                lockManager.unlock(resourceKey, value);
            }
        } else {
            // 其他情况抛异常处理
            throw ExceptionUtil.bizException(Code.CODE_INVALID_CODE, "nonsupport dispute status in create web-hook");
        }
        return null;
    }

    /**
     * 收取dispute相关费用
     *
     * @param dispute stripe 发送过来的dispute 实体
     * @throws StripeException stripe 封装的异常
     */
    public Long collectDisputeAndSendNotification(Dispute dispute, Payment payment) throws StripeException {
        Long amount = reverseTransfer(dispute, payment);
        // 定制费率额外收取费用
        boolean chargeFee = checkDisputeFee(payment.getBusinessId(), payment.getCompanyId(), dispute);
        asyncSendNotificationDispute(dispute, payment, "create", chargeFee);
        return amount;
    }

    /**
     * 收取dispute相关费用 用于升级争议的场景
     *
     * @param dispute Stripe 发送的dispute 实体
     * @throws StripeException Stripe 封装异常
     */
    public void collectDisputeForUnderDispute(Dispute dispute, Payment payment) throws StripeException {
        reverseTransfer(dispute, payment);
        // 定制费率额外收取费用
        boolean chargeFee = checkDisputeFee(payment.getBusinessId(), payment.getCompanyId(), dispute);
        // 发送非正式争议升级为正式争议notification
        asyncSendNotificationDispute(dispute, payment, "update", chargeFee);
    }

    private boolean checkDisputeFee(Integer businessId, Long companyId, Dispute dispute) throws StripeException {
        CompanyDto companyDto = iBusinessBusinessClient.getCompanyByBusinessId(businessId);
        MoeCustomizedPaymentSetting paymentSetting = customizedPaymentSettingService.getByCompanyId(companyDto.getId());
        if (paymentSetting == null) {
            return false;
        }
        // 定制费率需要收 15$ dispute fee
        MmStripeDisputeFee dbDisputeFee = disputeFeeMapper.selectByDisputeId(dispute.getId());
        if (dbDisputeFee != null) {
            // 保证每个dispute只收一次
            log.info("dispute fee exists:{}", dbDisputeFee.getDisputeId());
            return false;
        }
        MmStripeAccount stripeAccount = stripeAccountMapper.selectByBusinessId(businessId);
        // https://dashboard.stripe.com/test/logs/req_ljVijtkMhVwHsZ
        // generate a Transfer
        Charge charge = Charge.create(ChargeCreateParams.builder()
                .setAmount(STRIPE_DISPUTE_FEE)
                .setCurrency(dispute.getCurrency())
                .setSource(stripeAccount.getStripeAccountId())
                .setDescription(DISPUTE_CREATE_FEE_DESC)
                .build());
        MmStripeDisputeFee fee = new MmStripeDisputeFee();
        fee.setAmount(charge.getAmount());
        fee.setStatus(StripeDisputeFeeStatusEnum.charge.name());
        fee.setDisputeId(dispute.getId());
        fee.setBusinessId(businessId);
        fee.setCompanyId(companyId);
        fee.setChargeId(charge.getId());
        fee.setNeedRefund(paymentSetting.getCreateTime().before(NEW_PACT_DATE));
        disputeFeeMapper.insertSelective(fee);
        return true;
    }

    /**
     * won之后处理商家退款
     *
     * @param dispute
     * @param dbDispute
     * @throws StripeException
     */
    private long refundDispute(Dispute dispute, MmStripeDispute dbDispute) throws StripeException {
        MmStripeAccount stripeAccount = stripeAccountMapper.selectByBusinessId(dbDispute.getBusinessId());
        if (stripeAccount == null) {
            throw ExceptionUtil.bizException(Code.CODE_STRIPE_ACCOUNT_NOT_FOUND);
        }
        String transferId = "";
        Payment payment = paymentService.getPaymentByPaymentIntentId(dispute.getPaymentIntent());
        if (!StripeDisputeStatusEnum.won.name().equals(dbDispute.getStatus())) {
            // 判断是否存在逆向dispute单据(即dispute create时从商家账号里拿钱是不是通过分账拿的), 如果存在则此时也需要调用分账还回去
            if (splitPaymentService.checkRecordExist(dbDispute.getId(), SplitSyncRecordTypeEnum.DISPUTE_REVERSE)) {
                transactionOperations.executeWithoutResult(status -> {
                    // 插入一条新的split sync record 正向单据, 然后执行分账, 如果调用失败, 抛出异常让stripe重试
                    splitPaymentService.createSplitRecord(dbDispute.getId(), SplitSyncRecordTypeEnum.DISPUTE_FORWARD);
                    dbDispute.setStatus(StripeDisputeStatusEnum.won.name());
                    stripeDisputeMapper.updateByPrimaryKeySelective(dbDispute);
                });
                try {
                    if (!splitPaymentService.split(
                            Vendor.STRIPE, dbDispute.getId(), SplitSyncRecordTypeEnum.DISPUTE_FORWARD)) {
                        log.warn("refund dispute split payment failed");
                    }
                } catch (Exception e) {
                    log.error("refund dispute split error, msg: {}", e.getMessage());
                }
            } else {
                // 如果没有分账单, 那还是老逻辑
                TransferCreateParams transferCreateParams = TransferCreateParams.builder()
                        .setAmount(dbDispute.getAmount())
                        .setDestination(stripeAccount.getStripeAccountId())
                        .setCurrency(dispute.getCurrency())
                        .setSourceTransaction(dispute.getCharge())
                        .setDescription(DISPUTE_REFUND_REVERSE_DESC)
                        .build();
                Transfer transfer = Transfer.create(transferCreateParams);
                // transfer bind to dispute
                transferId = transfer.getId();
                Charge retrieve = Charge.retrieve(dispute.getCharge());
                retrieve.update(ChargeUpdateParams.builder()
                        .putMetadata(StripeApi.TRANSFER_ID, transferId)
                        .build());
                log.info(
                        "transfer to {} amount:{} success:{}",
                        stripeAccount.getStripeAccountId(),
                        dbDispute.getAmount(),
                        transfer.getId());
                dbDispute.setStatus(StripeDisputeStatusEnum.won.name());
                stripeDisputeMapper.updateByPrimaryKeySelective(dbDispute);
            }
        }
        long disputeFee = refundDisputeFee(dispute.getId());
        notifySlack(dispute, payment, "", disputeFee > 0, transferId);
        return StringUtils.hasText(transferId) ? dbDispute.getAmount() : disputeFee;
    }

    private Long refundDisputeFee(String id) throws StripeException {
        MmStripeDisputeFee disputeFee = disputeFeeMapper.selectByDisputeId(id);
        if (disputeFee != null
                && disputeFee.getNeedRefund()
                && disputeFee.getStatus().equals(StripeDisputeFeeStatusEnum.charge.name())) {
            Refund.create(RefundCreateParams.builder()
                    .setAmount(disputeFee.getAmount())
                    .setCharge(disputeFee.getChargeId())
                    .build());
            disputeFee.setStatus(StripeDisputeFeeStatusEnum.refund.name());
            log.info("{} refund dispute fee  amount:{} success", id, disputeFee.getAmount());
            disputeFeeMapper.updateByPrimaryKeySelective(disputeFee);
            return disputeFee.getAmount();
        }
        return 0L;
    }

    private Long reverseTransfer(Dispute dispute, Payment payment) throws StripeException {
        String chargeId = dispute.getCharge();
        if (!StringUtils.hasText(chargeId)) {
            log.warn("charge id is empty, dispute id: {}", chargeId);
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR);
        }
        Charge charge = Charge.retrieve(chargeId);
        if (charge == null) {
            log.warn("charge is null, charge id: {}", chargeId);
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR);
        }
        Long reverseAmount = checkDisputeAmount(dispute.getAmount(), charge.getAmountRefunded());

        // 走分账
        if (splitPaymentService.checkRecordExist(payment.getId(), SplitSyncRecordTypeEnum.PAYMENT)) {
            // 先落单据后调分账
            Integer dbDisputeId = transactionOperations.execute(status -> {
                Integer disputeId = insertOrUpdateStripeDispute(dispute, payment, reverseAmount);
                // 创建一个逆向dispute单据, 然后调用分账, 如果调用分账失败, 抛出异常并回滚, 让stripe 重试
                splitPaymentService.createSplitRecord(disputeId, SplitSyncRecordTypeEnum.DISPUTE_REVERSE);
                return disputeId;
            });
            // 后调用分账，这里单据落下不管怎样都要成功，否则就要一直重试
            try {
                if (!splitPaymentService.split(Vendor.STRIPE, dbDisputeId, SplitSyncRecordTypeEnum.DISPUTE_REVERSE)) {
                    log.warn("dispute reverse split payment failed, wait for retry");
                }
            } catch (Exception e) {
                log.error("dispute reverse split payment error, wait for retry", e);
            }
            return reverseAmount;
        }
        // 走到这里是不走分账的，继续查询transfer
        String transferId = charge.getTransfer();
        if (!StringUtils.hasText(transferId)) {
            log.warn("transfer id is empty, charge id: {}", chargeId);
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR);
        }
        Transfer transfer = Transfer.retrieve(transferId);
        if (transfer == null) {
            log.warn("transfer is null, transfer id: {}", transferId);
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR);
        }
        transfer.getReversals()
                .create(TransferReversalCollectionCreateParams.builder()
                        .setDescription(DISPUTE_CREATE_REVERSE_DESC)
                        .setAmount(reverseAmount)
                        .build());
        insertOrUpdateStripeDispute(dispute, payment, reverseAmount);
        return reverseAmount;
    }

    private Long checkDisputeAmount(Long amount, Long amountRefunded) {
        return Math.max(amount - amountRefunded, 0L);
    }

    private Integer insertOrUpdateStripeDispute(Dispute dispute, Payment payment, Long amount) {
        MmStripeDispute sd = buildStripeDispute(dispute, payment, amount);
        MmStripeDispute stripeDispute = queryByDisputeId(dispute.getId());
        if (stripeDispute == null) {
            stripeDisputeMapper.insertSelective(sd);
            return sd.getId();
        } else {
            sd.setId(stripeDispute.getId());
            stripeDisputeMapper.updateByPrimaryKeySelective(sd);
        }
        return stripeDispute.getId();
    }

    private void notifySlack(
            Dispute customerDispute,
            Payment existingPayment,
            String exceptionInfo,
            boolean chargeFee,
            String transferId) {
        StringBuilder bizStr = new StringBuilder("New Dispute Event")
                .append(System.lineSeparator())
                .append("DisputeId: ")
                .append(customerDispute.getId())
                .append(System.lineSeparator())
                .append("Date: ")
                .append(LocalDateTime.ofEpochSecond(customerDispute.getCreated(), 0, ZoneOffset.UTC))
                .append(System.lineSeparator())
                .append("Status: ")
                .append(customerDispute.getStatus())
                .append(System.lineSeparator())
                .append("Amount: ")
                .append(BigDecimal.valueOf(customerDispute.getAmount())
                        .divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP))
                .append(System.lineSeparator())
                .append("PaymentIntent: ")
                .append(customerDispute.getPaymentIntent())
                .append(System.lineSeparator());
        if (StringUtils.hasText(transferId)) {
            bizStr.append(System.lineSeparator())
                    .append("TransferId: ")
                    .append(transferId)
                    .append(System.lineSeparator());
        }
        if (existingPayment != null) {
            MoeBusinessDto bizInfo = iBusinessBusinessService.getBusinessInfoWithOwnerEmail(InfoIdParams.builder()
                    .infoId(existingPayment.getBusinessId())
                    .build());
            bizStr.append(System.lineSeparator())
                    .append("Business id: ")
                    .append(existingPayment.getBusinessId())
                    .append(System.lineSeparator())
                    .append("Business name: ")
                    .append(bizInfo.getBusinessName())
                    .append(System.lineSeparator())
                    .append("Business email: ")
                    .append(bizInfo.getOwnerEmail())
                    .append(System.lineSeparator())
                    .append("Customer name: ")
                    .append(existingPayment.getPaidBy());
        } else {
            bizStr.append(System.lineSeparator()).append("not found biz info");
        }
        if (chargeFee) {
            bizStr.append(System.lineSeparator())
                    .append("Dispute Fee:")
                    .append(STRIPE_DISPUTE_FEE / 100)
                    .append(".00$");
        }
        if (StringUtils.hasLength(exceptionInfo)) {
            bizStr.append(System.lineSeparator()).append("ERROR!!!");
            bizStr.append(System.lineSeparator()).append("Exception info: ").append(exceptionInfo);
        }
        bizStr.append(System.lineSeparator()).append("live mode: ").append(customerDispute.getLivemode());
        iPaymentSlackClient.sendBonusClaim(
                BookFeeClaimParam.builder().bookingFee(bizStr.toString()).build());
    }

    /**
     * 关闭 Stripe dispute 事件处理
     *
     * @param dispute    Stripe 发送过来的dispute实体
     * @param retryCount 上游规定重试次数
     * @return 处理的dispute金额
     * @throws StripeException stripe 封装 的异常
     */
    public long closeDispute(Dispute dispute, int retryCount) throws StripeException {
        log.info("dispute close:{},{}", dispute.getId(), dispute.getStatus());
        // check exists; business 的 dispute 直接忽略，这里只处理 client dispute.
        MmStripeDispute stripeDispute = stripeDisputeMapper.queryByDisputeId(dispute.getId());
        if (stripeDispute == null) {
            return 0;
        }

        //  商家胜诉  处理需要资金操作的状态
        if (StripeDisputeStatusEnum.won.name().equalsIgnoreCase(dispute.getStatus())) {
            return handleFundsOperationForCloseEvent(dispute, StripeDisputeStatusEnum.won, retryCount);
        } else if (StripeDisputeStatusEnum.warning_closed.name().equalsIgnoreCase(dispute.getStatus())
                || StripeDisputeStatusEnum.lost.name().equalsIgnoreCase(dispute.getStatus())) {
            // 商家败诉、非正式争议取消场景 直接持久化的中间状态
            updateStripeDisputeOnlyForClosedEvent(dispute);
            return 0L;
        } else {
            // 其他状态抛出异常
            throw ExceptionUtil.bizException(Code.CODE_INVALID_CODE, "nonsupport dispute status in close web-hook");
        }
    }

    /***
     * CLOSE Event 资金操作 ，当前适用于business 胜诉的场景
     * @param dispute stripe 发送过来的 dispute 实体
     * @param targetState 目标的dispute 状态 e.g. 当前应该之后 won 才会走到这个流程
     * @throws StripeException Stripe 封装的异常
     */
    private Long handleFundsOperationForCloseEvent(
            Dispute dispute, StripeDisputeStatusEnum targetState, Integer retryCount) throws StripeException {

        // business 赢 成功则退款 lock 防止重复退款 防止幻读
        String resourceKey = lockManager.getResourceKey(LockManager.DISPUTE_CLOSE, dispute.getId());
        String value = CommonUtil.getUuid();
        try {
            if (lockManager.lock(resourceKey, value)) {
                MmStripeDispute stripeDisputeInfo = queryByDisputeId(dispute.getId());
                if (stripeDisputeInfo == null) { // 并发情况下可能会有记录查不到的情况，需要上游重试
                    throw ExceptionUtil.bizException(
                            Code.CODE_INVALID_CODE, "Dispute record not " + "found; upstream retry is required.");
                }
                StripeDisputeStatusEnum currentState =
                        StripeDisputeStatusEnum.matchDisputeEnum(stripeDisputeInfo.getStatus());
                handleDisputeStateTransition(stripeDisputeInfo, currentState, targetState);
                return refundDispute(dispute, stripeDisputeInfo);
            }
        } catch (Exception e) {
            Payment payment = paymentService.getPaymentByPaymentIntentId(dispute.getPaymentIntent());
            notifySlack(
                    dispute,
                    payment,
                    e.getMessage() + System.lineSeparator() + "Current Retry Count:" + retryCount,
                    false,
                    "");
            delayMsgForRetry(dispute.getId(), retryCount);
            log.error("close dispute error:{}", dispute.getId(), e);
            throw e;
        } finally {
            lockManager.unlock(resourceKey, value);
        }
        return 0L;
    }

    private void delayMsgForRetry(String disputeId, int count) {
        if (count >= 3) {
            return;
        }
        try {
            ActiveMQTextMessage message = new ActiveMQTextMessage();
            // delay 24h
            message.setLongProperty(ScheduledMessage.AMQ_SCHEDULED_DELAY, 24 * 60 * 60 * 1000);
            message.setIntProperty(ActiveMQConstant.PAYMENT_DISPUTE_RETRY_TAG, count);
            message.setText(disputeId);
            moeMessageSender.send(disputeRetryQueue, message);
        } catch (Exception e) {
            log.error("DisputeCloseError:delayMsgForRetry error:{}", disputeId, e);
        }
    }

    @JmsListener(
            destination = ActiveMQConstant.PAYMENT_DISPUTE_AUTO_RETRY_QUEUE,
            containerFactory = "queueListenerFactory")
    public void onmessage(ActiveMQTextMessage message) throws JMSException {
        String disputeId = message.getText();
        try {
            int count = message.getIntProperty(ActiveMQConstant.PAYMENT_DISPUTE_RETRY_TAG) + 1;
            Dispute dispute = Dispute.retrieve(disputeId);
            closeDispute(dispute, count);
        } catch (Exception e) {
            log.error("DisputeCloseError:onmessage error:{}", message, e);
        } finally {
            message.acknowledge();
        }
    }

    @Transactional
    public boolean updateDisputeAndDisputeFee(Dispute dispute, MmStripeDispute stripeDispute) {
        insertStripeDispute(dispute, stripeDispute);
        return disputeFeeMapper.updateStatusByDisputeId(dispute.getId(), StripeDisputeFeeStatusEnum.deduct.name()) > 0;
    }

    /**
     * 根据 dispute 实体获取撤回金额
     *
     * @return ReverseAmount
     */
    private Long getReverseAmount(Dispute dispute) throws StripeException {
        String chargeId = dispute.getCharge();
        if (!StringUtils.hasText(chargeId)) {
            log.error("charge id is empty, dispute id: {}", chargeId);
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "charge id is empty, dispute id: {}", chargeId);
        }
        Charge charge = Charge.retrieve(chargeId);
        if (charge == null) {
            log.error("charge is null, charge id: {}", chargeId);
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "charge is null, charge id: {}", chargeId);
        }
        return checkDisputeAmount(dispute.getAmount(), charge.getAmountRefunded());
    }

    /**
     * 异步方法 产生 dispute 之后 进行 notification 通知，适用于正式争议的场景
     *
     * @param dispute     dispute stripe 发送的实体
     * @param payment     payment payment 记录
     * @param disputeType disputeType 争议类型 ，上游决定，如果是非正式争议上下文文案会不一致
     * @param chargeFee   是否是定制费率商家
     */
    private void asyncSendNotificationDispute(Dispute dispute, Payment payment, String disputeType, Boolean chargeFee) {
        ThreadPool.submit(() -> {
            // 声明obj
            NotificationExtraDisputeDto disputeDto = new NotificationExtraDisputeDto();
            NotificationDisputeParams disputeParams = new NotificationDisputeParams();
            // 通用赋值
            disputeParams.setBusinessId(payment.getBusinessId());
            disputeDto.setDisputeAmount(
                    BigDecimal.valueOf(dispute.getAmount()).divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP));
            disputeDto.setDisputeId(dispute.getId());
            disputeDto.setPaymentId(payment.getId());
            disputeDto.setCustomerId(payment.getCustomerId());
            disputeParams.setWebPushDto(disputeDto);
            // 非正式争议升级为正式争议场景；默认情况会创建正式争议的notification；update的意思是升级成正式争议的意思。
            if (("update").equals(disputeType)) {
                disputeParams.setTitle("Dispute inquiry");
                // todo 需要等待CRM进行配合发版 https://moego.atlassian.net/browse/CRM-2365，在发布之前暂时使用这个notification 类型
                disputeParams.setType(NotificationEnum.TYPE_ACTIVITY_PAYMENT_DISPUTE);
                disputeParams.setMobilePushTitle("Dispute inquiry");
                disputeParams.setMobilePushBody(
                        "A {totalPaidAmount} payment with {customerFullName} has received dispute inquiry .");
            }
            iNotificationClient.sendNotificationDispute(disputeParams);
            notifySlack(dispute, payment, "", chargeFee, "");
        });
    }

    /**
     * 持久化 dispute 当前信息
     */
    private void insertStripeDispute(Dispute dispute, Payment payment, Long amount) {
        MmStripeDispute sd = buildStripeDispute(dispute, payment, amount);
        MmStripeDispute stripeDispute = queryByDisputeId(dispute.getId());
        if (stripeDispute == null) {
            stripeDisputeMapper.insertSelective(sd);
        }
    }
}
