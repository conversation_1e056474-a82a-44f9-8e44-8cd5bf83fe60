package com.moego.server.payment.mapper;

import com.moego.server.payment.mapperbean.MoeCompanyEmailAutoReload;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Select;

public interface MoeCompanyEmailAutoReloadMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_company_email_auto_reload
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_company_email_auto_reload
     *
     * @mbg.generated
     */
    int insert(MoeCompanyEmailAutoReload record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_company_email_auto_reload
     *
     * @mbg.generated
     */
    int insertSelective(MoeCompanyEmailAutoReload record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_company_email_auto_reload
     *
     * @mbg.generated
     */
    MoeCompanyEmailAutoReload selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_company_email_auto_reload
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(MoeCompanyEmailAutoReload record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_company_email_auto_reload
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(MoeCompanyEmailAutoReload record);

    @Insert(
            "insert into moe_company_email_auto_reload (company_id, status, create_time, update_time) values (#{companyId}, #{status}, #{createTime}, #{updateTime}) on duplicate key update status = #{status}, update_time = #{updateTime}")
    int insertOrUpdate(MoeCompanyEmailAutoReload record);

    @Select("select status,pay_by_enterprise_id from moe_company_email_auto_reload where company_id = #{companyId}")
    @ResultMap("BaseResultMap")
    MoeCompanyEmailAutoReload selectByCompanyId(@Param("companyId") Integer companyId);
}
