package com.moego.server.payment.mapper;

import com.moego.server.payment.mapperbean.BusinessStripeLocation;

public interface BusinessStripeLocationMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table business_stripe_location
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table business_stripe_location
     *
     * @mbg.generated
     */
    int insert(BusinessStripeLocation record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table business_stripe_location
     *
     * @mbg.generated
     */
    int insertSelective(BusinessStripeLocation record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table business_stripe_location
     *
     * @mbg.generated
     */
    BusinessStripeLocation selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table business_stripe_location
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(BusinessStripeLocation record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table business_stripe_location
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(BusinessStripeLocation record);

    BusinessStripeLocation selectByBusinessId(Integer businessId);
}
