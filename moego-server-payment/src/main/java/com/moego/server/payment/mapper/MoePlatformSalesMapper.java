package com.moego.server.payment.mapper;

import com.moego.server.payment.mapperbean.MoePlatformSales;
import com.moego.server.payment.mapperbean.MoePlatformSalesExample;
import com.moego.server.payment.params.PlatformSalesQueryParams;
import java.util.List;

public interface MoePlatformSalesMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_platform_sales
     *
     * @mbg.generated
     */
    long countByExample(MoePlatformSalesExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_platform_sales
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_platform_sales
     *
     * @mbg.generated
     */
    int insert(MoePlatformSales record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_platform_sales
     *
     * @mbg.generated
     */
    int insertSelective(MoePlatformSales record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_platform_sales
     *
     * @mbg.generated
     */
    List<MoePlatformSales> selectByExample(MoePlatformSalesExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_platform_sales
     *
     * @mbg.generated
     */
    MoePlatformSales selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_platform_sales
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(MoePlatformSales record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_platform_sales
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(MoePlatformSales record);

    Integer selectCount(PlatformSalesQueryParams params);

    List<MoePlatformSales> pageSelectByParams(PlatformSalesQueryParams params, Integer offset, Integer limit);
}
