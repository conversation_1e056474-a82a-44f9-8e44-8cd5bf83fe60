package com.moego.server.payment.mapper;

import com.moego.server.payment.dto.feature.FeatureRelation;
import com.moego.server.payment.mapperbean.MoePlanFeatureRelation;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface MoePlanFeatureRelationMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_plan_feature_relation
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_plan_feature_relation
     *
     * @mbg.generated
     */
    int insert(MoePlanFeatureRelation record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_plan_feature_relation
     *
     * @mbg.generated
     */
    int insertSelective(MoePlanFeatureRelation record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_plan_feature_relation
     *
     * @mbg.generated
     */
    MoePlanFeatureRelation selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_plan_feature_relation
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(MoePlanFeatureRelation record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_plan_feature_relation
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(MoePlanFeatureRelation record);

    List<FeatureRelation> selectByLevel(@Param("level") Integer level);

    List<MoePlanFeatureRelation> selectByLevelList(@Param("levelList") List<Integer> level);
}
