package com.moego.server.payment.web.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2022/11/11
 */
@Data
@Schema(description = "stripe connected account info")
public class StripeConnectedInfo {

    @Schema(
            description = "Funds that are available to be transferred or paid out"
                    + "https://stripe.com/docs/api/balance/balance_object#balance_object-available")
    private BigDecimal availableBalance;

    @Schema(description = "Funds that are not yet available in the balance, due to the 7-day rolling pay cycle.")
    private BigDecimal pendingBalance;

    String currency;
}
