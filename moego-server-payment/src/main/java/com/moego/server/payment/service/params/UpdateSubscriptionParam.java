package com.moego.server.payment.service.params;

import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2022/7/6 11:36 AM
 */
@Data
@Builder
public class UpdateSubscriptionParam {
    Integer subscriptionType; // null for company
    public static final int SUBSCRIPTION_TYPE_COMPANY = 1;
    public static final int SUBSCRIPTION_TYPE_ENTERPRISE = 2;
    Long enterpriseId;
    Long companyId;
    String subscriptionId;
    String stripePlanId;
    String vanStripePlanId;
    Integer businessNum;
    Boolean isDowngrade;
    Integer level;
    Integer vansNum;
    Integer locationsNum;
    String stripeCouponId;
}
