package com.moego.server.payment.service;

import static com.moego.common.enums.PaymentMethodEnum.METHOD_NAME_SQUARE;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.ImmutableList;
import com.moego.common.distributed.LockManager;
import com.moego.common.enums.BooleanEnum;
import com.moego.common.enums.CompanyFunctionControlConst;
import com.moego.common.enums.PaymentMethodEnum;
import com.moego.common.enums.PaymentStatusEnum;
import com.moego.common.enums.PaymentStripeStatus;
import com.moego.common.enums.ResponseCodeEnum;
import com.moego.common.exception.CommonException;
import com.moego.common.security.AESUtil;
import com.moego.common.utils.CommonUtil;
import com.moego.common.utils.DateUtil;
import com.moego.common.utils.PaymentUtil;
import com.moego.common.utils.PrimitiveTypeUtil;
import com.moego.common.utils.RandomUtil;
import com.moego.idl.models.order.v1.OrderModel;
import com.moego.idl.service.order.v1.GetOrderRequest;
import com.moego.idl.service.order.v1.OrderServiceGrpc;
import com.moego.idl.service.order.v1.SetTipsRequest;
import com.moego.lib.common.thread.ThreadPool;
import com.moego.server.business.client.IBusinessBusinessClient;
import com.moego.server.business.client.IBusinessSmartSchedulingClient;
import com.moego.server.business.dto.CompanyDto;
import com.moego.server.business.params.InfoIdParams;
import com.moego.server.business.params.SmartScheduleSettingParams;
import com.moego.server.customer.client.ICustomerContactClient;
import com.moego.server.customer.client.ICustomerCustomerClient;
import com.moego.server.customer.client.ICustomerReportClient;
import com.moego.server.customer.dto.GroomingCustomerInfoDTO;
import com.moego.server.grooming.client.IDepositClient;
import com.moego.server.grooming.client.IGroomingInvoiceClient;
import com.moego.server.grooming.params.DepositVo;
import com.moego.server.grooming.params.InvoiceValueType;
import com.moego.server.payment.dto.BillingAddress;
import com.moego.server.payment.dto.CardDTO;
import com.moego.server.payment.dto.GetSquareTokenResponse;
import com.moego.server.payment.dto.PaymentDTO;
import com.moego.server.payment.dto.ReaderAuthCodeResponse;
import com.moego.server.payment.dto.square.GetSquareAuthUrlResponse;
import com.moego.server.payment.dto.square.SetSquareDefaultLocationResponse;
import com.moego.server.payment.dto.square.SquareCard;
import com.moego.server.payment.dto.square.SquareCreateCOFResponse;
import com.moego.server.payment.dto.square.SquareCreateCustomerRequest;
import com.moego.server.payment.dto.square.SquareCreateCustomerResponse;
import com.moego.server.payment.dto.square.SquareCustomerInfo;
import com.moego.server.payment.dto.square.SquareDevice;
import com.moego.server.payment.dto.square.SquareDeviceCodeResponse;
import com.moego.server.payment.dto.square.SquareLocation;
import com.moego.server.payment.dto.square.SquarePaymentMethodEnum;
import com.moego.server.payment.dto.square.SquarePaymentRequest;
import com.moego.server.payment.dto.square.SquareReaderUpdateResponse;
import com.moego.server.payment.dto.square.SquareRefundRequest;
import com.moego.server.payment.dto.square.SquareTakePaymentResponse;
import com.moego.server.payment.dto.square.SquareTerminalCheckoutResponse;
import com.moego.server.payment.dto.square.SquareTokenUpdateResponse;
import com.moego.server.payment.dto.square.TerminalChargeResponse;
import com.moego.server.payment.mapper.BusinessSquareMapper;
import com.moego.server.payment.mapper.CustomerSquareMapper;
import com.moego.server.payment.mapper.PaymentMapper;
import com.moego.server.payment.mapper.RefundMapper;
import com.moego.server.payment.mapperbean.BusinessSquare;
import com.moego.server.payment.mapperbean.CustomerSquare;
import com.moego.server.payment.mapperbean.Payment;
import com.moego.server.payment.params.IntakeFormCustomerStripRequest;
import com.moego.server.payment.service.util.BusinessInfoHelper;
import com.moego.server.payment.service.util.SquareCardUtil;
import com.moego.server.payment.service.util.TipsUtil;
import com.squareup.square.Environment;
import com.squareup.square.SquareClient;
import com.squareup.square.api.CustomersApi;
import com.squareup.square.api.DevicesApi;
import com.squareup.square.api.LocationsApi;
import com.squareup.square.api.MobileAuthorizationApi;
import com.squareup.square.api.OAuthApi;
import com.squareup.square.api.OrdersApi;
import com.squareup.square.api.PaymentsApi;
import com.squareup.square.api.RefundsApi;
import com.squareup.square.api.TerminalApi;
import com.squareup.square.api.TransactionsApi;
import com.squareup.square.exceptions.ApiException;
import com.squareup.square.http.response.HttpStringResponse;
import com.squareup.square.models.CancelTerminalCheckoutResponse;
import com.squareup.square.models.CaptureTransactionResponse;
import com.squareup.square.models.Card;
import com.squareup.square.models.CreateCustomerCardRequest;
import com.squareup.square.models.CreateCustomerCardResponse;
import com.squareup.square.models.CreateCustomerRequest;
import com.squareup.square.models.CreateCustomerResponse;
import com.squareup.square.models.CreateDeviceCodeRequest;
import com.squareup.square.models.CreateDeviceCodeResponse;
import com.squareup.square.models.CreateMobileAuthorizationCodeRequest;
import com.squareup.square.models.CreateMobileAuthorizationCodeResponse;
import com.squareup.square.models.CreatePaymentRequest;
import com.squareup.square.models.CreatePaymentResponse;
import com.squareup.square.models.CreateTerminalCheckoutRequest;
import com.squareup.square.models.CreateTerminalCheckoutResponse;
import com.squareup.square.models.Customer;
import com.squareup.square.models.DeleteCustomerCardResponse;
import com.squareup.square.models.DeleteCustomerResponse;
import com.squareup.square.models.DeviceCheckoutOptions;
import com.squareup.square.models.DeviceCode;
import com.squareup.square.models.Error;
import com.squareup.square.models.GetPaymentResponse;
import com.squareup.square.models.ListDeviceCodesResponse;
import com.squareup.square.models.ListLocationsResponse;
import com.squareup.square.models.Location;
import com.squareup.square.models.Money;
import com.squareup.square.models.ObtainTokenRequest;
import com.squareup.square.models.ObtainTokenResponse;
import com.squareup.square.models.OrderLineItem;
import com.squareup.square.models.ProcessingFee;
import com.squareup.square.models.RefundPaymentRequest;
import com.squareup.square.models.RefundPaymentResponse;
import com.squareup.square.models.RetrieveCustomerResponse;
import com.squareup.square.models.RetrieveLocationResponse;
import com.squareup.square.models.RetrieveOrderResponse;
import com.squareup.square.models.RevokeTokenRequest;
import com.squareup.square.models.RevokeTokenResponse;
import com.squareup.square.models.TerminalCheckout;
import com.squareup.square.models.TipSettings;
import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

@Service
@Slf4j
public class SquareService {

    public static final String AUTH_SEPARATOR = "-";
    /**
     * Square Permissions List :
     * https://developer.squareup.com/docs/oauth-api/square-permissions
     */
    private static final String[] PERMISSIONS = new String[] {
        "CUSTOMERS_WRITE",
        "CUSTOMERS_READ",
        "DEVICE_CREDENTIAL_MANAGEMENT",
        "MERCHANT_PROFILE_READ",
        "PAYMENTS_WRITE",
        "PAYMENTS_WRITE_IN_PERSON",
        "ORDERS_READ",
        "PAYMENTS_READ",
    };

    private final SquareClient squareClient;

    @Value("${square.application.id}")
    private String applicationId;

    @Value("${square.application.secret}")
    private String applicationSecret;

    @Value("${square.access.token}")
    private String moegoAccessToken;

    @Value("${square.auth.baseUrl}")
    private String authBaseUrl;

    @Value("${security.aes.key}")
    private String aesKey;

    @Value("${square.env}")
    private String squareEnv;

    @Value("${square.webhook.secret:}")
    private String endpointSecret;

    @Value("${square.webhook.url:}")
    private String endpointUrl;

    @Autowired
    private PaymentService paymentService;

    @Autowired
    private IBusinessBusinessClient iBusinessBusinessClient;

    @Autowired
    private IBusinessSmartSchedulingClient iBusinessSmartSchedulingClient;

    @Autowired
    private ICustomerContactClient iCustomerContactClient;

    @Autowired
    private ICustomerReportClient iCustomerReportClient;

    @Autowired
    private IGroomingInvoiceClient groomingInvoiceClient;

    @Autowired
    private IDepositClient iDepositClient;

    @Autowired
    private BusinessInfoHelper businessInfoHelper;

    private final BusinessSquareMapper squareMapper;
    private final CustomerSquareMapper customerSquareMapper;
    private final PaymentMapper paymentMapper;
    private final RefundMapper refundMapper;
    private final OAuthApi oAuthApi;

    @Autowired
    private ICustomerCustomerClient iCustomerGroomingClient;

    @Autowired
    private OrderServiceGrpc.OrderServiceBlockingStub orderClient;

    @Autowired
    private LockManager lockManager;

    @Autowired
    public SquareService(
            SquareClient squareClient,
            BusinessSquareMapper squareMapper,
            CustomerSquareMapper customerSquareMapper,
            PaymentMapper paymentMapper,
            RefundMapper refundMapper) {
        this.customerSquareMapper = customerSquareMapper;
        this.refundMapper = refundMapper;
        this.squareMapper = squareMapper;
        this.paymentMapper = paymentMapper;
        this.squareClient = squareClient;
        oAuthApi = squareClient.getOAuthApi();
    }

    public GetSquareAuthUrlResponse getAuthUrl(Integer businessId) {
        // OAuth update:
        // https://squareup.com/t/cmtp_performance/pr_cross_product/d_partnerships/p_PARTNERNAME/l_us/?route=oauth2/authorize&client_id=UNIQUE ID
        // sandbox保持不变，production更新后配置项兼容
        String urlPattern = authBaseUrl + "?route=oauth2/authorize&client_id=%s&scope=%s&session=false&state=%s";
        String scope = String.join("+", PERMISSIONS);
        String state = businessId + AUTH_SEPARATOR + RandomUtil.randomLowerCharString(10);

        return GetSquareAuthUrlResponse.builder()
                .authUrl(String.format(urlPattern, applicationId, scope, state))
                .permissions(Arrays.asList(PERMISSIONS))
                .applicationId(applicationId)
                .state(state)
                .build();
    }

    /**
     * For "Authorization code is already claimed.", response with message: "HTTP Response Not OK"
     * <p>
     * code有效期是五分钟，如果过期，需要重新请求授权码
     * see： https://developer.squareup.com/docs/oauth-api/how-oauth-works#authorizing-an-application-and-obtaining-oauth-tokens
     */
    public SquareTokenUpdateResponse saveTokenWithAuthCode(Integer businessId, Long companyId, String authCode) {
        // before insert ,check existing record
        BusinessSquare existingSquare = squareMapper.selectByBusinessId(businessId);
        if (existingSquare != null) {
            throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "this business already integrated with square");
        }
        ObtainTokenRequest request = new ObtainTokenRequest.Builder(
                        applicationId, applicationSecret, PaymentMethodEnum.SQUARE_GRANT_OAUTH)
                .code(authCode)
                .build();

        ObtainTokenResponse squareResponse;
        try {
            squareResponse = oAuthApi.obtainToken(request);
            log.info("get square token successfully: {}", squareResponse.toString());
        } catch (ApiException | IOException e) {
            throwRequestFailure(
                    "Failed to obtain token from square. businessId: " + businessId + ", auth code: " + authCode + ". ",
                    e);
            return null; // should never be accessed
        }

        BusinessSquare businessSquare = new BusinessSquare();
        businessSquare.setBusinessId(businessId);
        businessSquare.setCompanyId(companyId);
        businessSquare.setMerchantId(squareResponse.getMerchantId());
        businessSquare.setExpiresAt(squareResponse.getExpiresAt());
        businessSquare.setAccessToken(encode(squareResponse.getAccessToken()));
        businessSquare.setRefreshToken(encode(squareResponse.getRefreshToken()));
        squareMapper.insertSelective(businessSquare);

        SquareTokenUpdateResponse result = new SquareTokenUpdateResponse();
        BeanUtils.copyProperties(squareResponse, result);
        result.setBusinessId(businessId);
        return result;
    }

    public ObtainTokenResponse refreshToken(BusinessSquare currentToken) {
        ObtainTokenRequest request = new ObtainTokenRequest.Builder(
                        applicationId, applicationSecret, PaymentMethodEnum.SQUARE_GRANT_REFRESH)
                .refreshToken(decode(currentToken.getRefreshToken()))
                .build();

        ObtainTokenResponse squareResponse;
        try {
            squareResponse = oAuthApi.obtainToken(request);
        } catch (ApiException | IOException e) {
            throwRequestFailure(
                    "Failed to refresh token from square. businessId: " + currentToken.getBusinessId() + ". ", e);
            return null; // should never be accessed
        }

        BusinessSquare toUpdate = new BusinessSquare();
        toUpdate.setId(currentToken.getId());
        toUpdate.setExpiresAt(squareResponse.getExpiresAt());
        toUpdate.setAccessToken(encode(squareResponse.getAccessToken()));
        squareMapper.updateByPrimaryKeySelective(toUpdate);
        return squareResponse;
    }

    public GetSquareTokenResponse getToken(Integer businessId) {
        BusinessSquare token = squareMapper.selectByBusinessId(businessId);
        CompanyDto companyDto = iBusinessBusinessClient.getCompanyByBusinessId(businessId);
        // 是否在square白名单内
        boolean squareAccess = companyDto != null
                && Objects.equals(companyDto.getEnableSquare(), CompanyFunctionControlConst.SQUARE_ENABLE_WHITELIST);
        if (token == null) {
            return GetSquareTokenResponse.builder()
                    .businessId(businessId)
                    .squareConnected(false)
                    .squareAccess(squareAccess)
                    .build();
        }

        return GetSquareTokenResponse.builder()
                .businessId(businessId)
                .squareConnected(true)
                .squareAccess(squareAccess)
                .defaultLocationId(token.getDefaultLocationId())
                .merchantId(token.getMerchantId())
                .expiresAt(token.getExpiresAt())
                .appId(applicationId)
                .build();
    }

    /**
     * Return businessId been processed
     */
    public Integer disconnectSquare(Integer businessId) {
        BusinessSquare businessSquare = squareMapper.selectByBusinessId(businessId);
        if (businessSquare == null) {
            return businessId;
        }

        try {
            RevokeTokenResponse squareResponse;
            RevokeTokenRequest body = new RevokeTokenRequest.Builder()
                    .clientId(applicationId)
                    .accessToken(getValidAccessToken(businessSquare))
                    .revokeOnlyAccessToken(false)
                    .build();
            squareResponse = oAuthApi.revokeToken(body, "Client " + applicationSecret);
            log.info(
                    "revoke business[{}] for {} result: {}",
                    businessId,
                    businessSquare.getMerchantId(),
                    squareResponse.getSuccess());
        } catch (ApiException | IOException e) {
            log.error("revoke business_square[{}] failed.", businessSquare.getId(), e);
        } finally {
            disconnectFromSquare(businessId, businessSquare.getId());
        }

        return businessId;
    }

    private void disconnectFromSquare(Integer businessId, Integer businessSquarePrimaryId) {
        squareMapper.deleteBy(businessSquarePrimaryId);
        ThreadPool.execute(() -> {
            // reset primary card processor id if it is square
            Byte payType = iBusinessBusinessClient
                    .getBusinessInfo(InfoIdParams.builder().infoId(businessId).build())
                    .getPrimaryPayType();
            if (PaymentMethodEnum.CARD_PROCESSOR_TYPE_SQUARE.equals(payType)) {
                SmartScheduleSettingParams settingParam = SmartScheduleSettingParams.builder()
                        .primaryPayType(PaymentMethodEnum.CARD_PROCESSOR_TYPE_NONE)
                        .build();
                iBusinessSmartSchedulingClient.updateSmartScheduleSetting(businessId, settingParam);
            }
        });
    }

    public List<SquareLocation> queryActivePaymentLocations(Integer businessId) {
        BusinessSquare businessSquare = squareMapper.selectByBusinessId(businessId);
        if (businessSquare == null) {
            return Collections.emptyList();
        }
        LocationsApi locationsApi =
                createClient(getValidAccessToken(businessSquare)).getLocationsApi();

        ListLocationsResponse squareResponse;
        try {
            squareResponse = locationsApi.listLocations();
        } catch (ApiException | IOException e) {
            throwRequestFailure("Failed to get locations from Square.", e);
            return null; // should never be accessed
        }

        if (!CollectionUtils.isEmpty(squareResponse.getErrors())) {
            throwResponseError(squareResponse.getErrors().get(0));
        }

        return squareResponse.getLocations().stream()
                .filter(this::isSquareLocationValid)
                .map(l -> {
                    SquareLocation currLocation = SquareLocation.builder()
                            .id(l.getId())
                            .name(l.getName())
                            .isDefault(businessSquare.getDefaultLocationId().equals(l.getId()))
                            .createdAt(l.getCreatedAt())
                            .merchantId(l.getMerchantId())
                            .currency(l.getCurrency())
                            .status(l.getStatus())
                            .build();
                    if (l.getAddress() != null) {
                        currLocation.setAddress1(l.getAddress().getAddressLine1());
                        currLocation.setAddress2(l.getAddress().getAddressLine2());
                        currLocation.setCity(l.getAddress().getLocality());
                        currLocation.setState(l.getAddress().getAdministrativeDistrictLevel1());
                        currLocation.setCountry(l.getAddress().getCountry());
                        currLocation.setZipcode(l.getAddress().getPostalCode());
                    }
                    return currLocation;
                })
                .collect(Collectors.toList());
    }

    private boolean isSquareLocationValid(Location l) {
        return (PaymentMethodEnum.SQUARE_LOCATION_ACTIVE.equals(l.getStatus())
                && l.getCapabilities() != null
                && l.getCapabilities().contains(PaymentMethodEnum.SQUARE_LOCATION_CARD_PROCESSING));
    }

    private Location getSquareLocationDetail(BusinessSquare businessSquare, String locationId) {
        if (StringUtils.isEmpty(locationId)) {
            return null;
        }
        LocationsApi locationsApi =
                createClient(getValidAccessToken(businessSquare)).getLocationsApi();
        RetrieveLocationResponse location;
        try {
            location = locationsApi.retrieveLocation(locationId);
        } catch (ApiException | IOException e) {
            throwRequestFailure("Given location id not found from Square for the business.", e);
            return null; // should never be accessed
        }

        if (!CollectionUtils.isEmpty(location.getErrors())) {
            throwResponseError(location.getErrors().get(0));
        }

        if (!isSquareLocationValid(location.getLocation())) {
            throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "Location not active/valid.");
        }
        return location.getLocation();
    }

    public SetSquareDefaultLocationResponse setDefaultLocation(Integer businessId, String locationId) {
        BusinessSquare businessSquare = this.getBusinessSquareWithException(businessId);
        Location location = getSquareLocationDetail(businessSquare, locationId);
        BusinessSquare defaultLocation = new BusinessSquare();
        if (location != null) {
            log.info("business {} set square default location to {}", businessId, location.getName());
            String moegoCurrency =
                    iBusinessBusinessClient.getBusinessPreference(businessId).getCurrencyCode();
            if (!location.getCurrency().equals(moegoCurrency)) {
                throw new CommonException(
                        ResponseCodeEnum.PARAMS_ERROR,
                        String.format(
                                "square currency %s is not same with moego business currency %s",
                                location.getCurrency(), moegoCurrency));
            }
        } else {
            throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "invalid location id");
        }
        defaultLocation.setId(businessSquare.getId());
        defaultLocation.setDefaultLocationId(locationId);
        squareMapper.updateByPrimaryKeySelective(defaultLocation);

        return new SetSquareDefaultLocationResponse(businessId, locationId);
    }

    public SquareTakePaymentResponse takePayment(Integer businessId, SquarePaymentRequest request) {
        BusinessSquare businessSquare = this.getBusinessSquareWithException(businessId);
        PaymentsApi paymentsApi =
                createClient(getValidAccessToken(businessSquare)).getPaymentsApi();

        request.setLocationId(businessSquare.getDefaultLocationId());
        request.setMerchant(businessSquare.getMerchantId());
        Payment payment = buildTransactionRecord(businessId, request);
        paymentMapper.insertSelective(payment);

        CreatePaymentRequest body = new CreatePaymentRequest.Builder(
                        request.getCardNonce(),
                        payment.getId().toString(),
                        new Money(toUnitAmount(request.getAmount(), request.getCurrency()), request.getCurrency()))
                .locationId(request.getLocationId())
                .customerId(Boolean.TRUE.equals(request.getUseCOF()) ? request.getSquareCustomerId() : null)
                .note("Created by MoeGo API for invoice:" + request.getInvoiceId())
                .build();

        CreatePaymentResponse squareResponse;
        try {
            squareResponse = paymentsApi.createPayment(body);
        } catch (ApiException | IOException e) {
            updatePaymentDBRecordFailure(payment);
            throwRequestFailure("Failed to create payment via Square, ", e);
            return null; // should never be accessed
        }

        if (!CollectionUtils.isEmpty(squareResponse.getErrors())) {
            updatePaymentDBRecordFailure(payment);
            throwResponseError(squareResponse.getErrors().get(0));
        }
        String squareStatus = squareResponse.getPayment().getStatus();
        if (!PaymentMethodEnum.CHECK_OUT_STATUS_COMPLETED.equals(squareStatus)) {
            log.warn("square payment status is not completed, detail: {}", squareResponse.getPayment());
            updatePaymentDBRecordFailure(payment);
            throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "square status is not completed. " + squareStatus);
        }
        // 支付成功，更新支付状态为 paid
        updatePaymentDBRecordSuccess(payment, squareResponse);
        if (TipsUtil.shouldSyncTips(request.getModule(), request.getTipsAmount())) {
            SetTipsRequest tipsParam = SetTipsRequest.newBuilder()
                    .setInvoiceId(payment.getInvoiceId())
                    .setBusinessId(payment.getBusinessId())
                    .setOmitResult(true)
                    .setValue(request.getTipsAmount().doubleValue())
                    .setValueType(InvoiceValueType.AMOUNT.value())
                    .build();
            orderClient.setTips(tipsParam);
        }
        if (BooleanEnum.VALUE_TRUE.equals(request.getIsDeposit())) {
            DepositVo depositVo = new DepositVo();
            BeanUtils.copyProperties(payment, depositVo);
            iDepositClient.createOrUpdateDeposit(depositVo);
        }
        // square 返回的支付状态应该是completed
        return SquareTakePaymentResponse.builder()
                .primaryId(payment.getId())
                .amount(request.getAmount())
                .currency(request.getCurrency())
                .locationId(squareResponse.getPayment().getLocationId())
                .paymentId(squareResponse.getPayment().getId())
                .sourceType(squareResponse.getPayment().getSourceType())
                .status(squareResponse.getPayment().getStatus())
                .createdAt(squareResponse.getPayment().getCreatedAt())
                .cartLast4(
                        squareResponse.getPayment().getCardDetails().getCard().getLast4())
                .build();
    }

    private void updatePaymentDBRecordSuccess(Payment payment, CreatePaymentResponse squareResponse) {
        Card card = squareResponse.getPayment().getCardDetails().getCard();
        Payment toUpdate = new Payment();
        toUpdate.setId(payment.getId());
        toUpdate.setStatus(PaymentStatusEnum.PAID);
        toUpdate.setStripeChargeId(squareResponse.getPayment().getId());
        setSquareCardDetailForPayment(toUpdate, card);
        toUpdate.setUpdateTime(DateUtil.get10Timestamp());
        paymentMapper.updateByPrimaryKeySelective(toUpdate);
    }

    private void setSquareCardDetailForPayment(Payment toUpdate, Card card) {
        toUpdate.setCardNumber(card.getLast4());
        toUpdate.setCardType(card.getCardBrand());
        toUpdate.setExpMonth(card.getExpMonth().toString());
        toUpdate.setExpYear(card.getExpYear().toString());
    }

    /**
     * after updating invoice successfully, call this method
     *
     * @param paymentPrimaryId
     */
    public void updatePaymentDBRecordCompleted(Integer paymentPrimaryId) {
        Payment toUpdate = new Payment();
        toUpdate.setId(paymentPrimaryId);
        toUpdate.setStatus(PaymentStatusEnum.COMPLETED);
        toUpdate.setUpdateTime(DateUtil.get10Timestamp());
        paymentMapper.updateByPrimaryKeySelective(toUpdate);
    }

    private void updatePaymentDBRecordFailure(Payment payment) {
        Payment toUpdate = new Payment();
        toUpdate.setId(payment.getId());
        toUpdate.setStatus(PaymentStatusEnum.FAILED);
        toUpdate.setUpdateTime(DateUtil.get10Timestamp());
        paymentMapper.updateByPrimaryKeySelective(toUpdate);
    }

    private Payment buildTransactionRecord(Integer businessId, SquarePaymentRequest request) {
        Byte method = SquarePaymentMethodEnum.CARD_PAY;
        if (Boolean.TRUE.equals(request.getUseCOF())) {
            method = SquarePaymentMethodEnum.COF_PAY;
        }
        Payment payment = new Payment();
        BeanUtils.copyProperties(request, payment);
        payment.setVendor(PaymentMethodEnum.METHOD_NAME_SQUARE);
        payment.setBusinessId(businessId);
        payment.setCompanyId(businessInfoHelper.getCompanyIdByBusinessId(businessId));
        payment.setModule(request.getModule());
        payment.setSignature(request.getSignature());
        payment.setStatus(PaymentStatusEnum.CREATED);
        payment.setMethod(PaymentMethodEnum.METHOD_NAME_CREDIT_CARD);
        payment.setMethodId(PaymentMethodEnum.METHOD_SQUARE);
        payment.setCreateTime(DateUtil.get10Timestamp());
        payment.setUpdateTime(payment.getCreateTime());
        payment.setSquarePaymentMethod(method);
        return payment;
    }

    public RefundPaymentResponse refundPayment(
            Integer businessId,
            SquareRefundRequest request,
            Payment payment,
            com.moego.server.payment.mapperbean.Refund refund) {
        BusinessSquare businessSquare = this.getBusinessSquareWithException(businessId);
        if (!businessSquare.getMerchantId().equals(payment.getMerchant())) {
            throw new CommonException(
                    ResponseCodeEnum.PARAMS_ERROR,
                    "The Square account associated with this transaction has been disconnected. \n"
                            + "You can refund from your Square dashboard, or connect the Square account with MoeGo again. ");
        }
        RefundsApi refundsApi =
                createClient(getValidAccessToken(businessSquare)).getRefundsApi();
        RefundPaymentRequest body = new RefundPaymentRequest.Builder(
                        refund.getCreateTime().toString(),
                        new Money(toUnitAmount(request.getAmount(), request.getCurrency()), request.getCurrency()),
                        request.getPaymentId())
                .reason(StringUtils.isEmpty(request.getReason()) ? null : request.getReason())
                .build();
        log.info("square refund param: {}", body.toString());
        RefundPaymentResponse squareResponse;
        try {
            squareResponse = refundsApi.refundPayment(body);
            if (!CollectionUtils.isEmpty(squareResponse.getErrors())) {
                Error error = squareResponse.getErrors().get(0);
                refund.setStatus(PaymentStatusEnum.FAILED);
                refund.setError(error.getDetail());
                log.warn("Failed to refund Square payment for: {}", error.toString());
            } else {
                refund.setStripeRefundId(squareResponse.getRefund().getId());
                log.info("successful refund: {}", squareResponse.getRefund().toString());
            }
            return squareResponse;
        } catch (ApiException e) {
            refund.setStatus(PaymentStatusEnum.FAILED);
            String respBody = ((HttpStringResponse) e.getHttpContext().getResponse()).getBody();
            refund.setError(respBody);
            log.error("Failed to refund Square payment. ", e);
            return null;
        } catch (IOException e) {
            refund.setStatus(PaymentStatusEnum.FAILED);
            refund.setError(e.getMessage());
            log.error("Failed to refund Square payment. ", e);
            return null;
        }
    }

    /**
     * call by intake form (customer module) before creating profile
     *
     * @param businessId
     * @param squareCustomerIdWithMerchantId squareCusId:squareMerchantId
     * @return
     */
    public List<CardDTO> getSquareCards(Integer businessId, String squareCustomerIdWithMerchantId) {
        BusinessSquare businessSquare = squareMapper.selectByBusinessId(businessId);
        if (businessSquare == null) {
            log.info("this business {} has no square integration", businessId);
            return null;
        }
        SquareCardUtil.SquareCustomer squareCustomer =
                SquareCardUtil.decodeIntakeFormSavedCus(squareCustomerIdWithMerchantId);
        if (!businessSquare.getMerchantId().equals(squareCustomer.getMerchantId())) {
            log.warn(
                    "save merchantId {} is not equal to current {}",
                    squareCustomer.getMerchantId(),
                    businessSquare.getMerchantId());
            return null;
        }
        RetrieveCustomerResponse squareResponse =
                getRetrieveCustomerResponse(businessSquare, squareCustomer.getSquareOriginCusId());
        if (squareResponse != null) {
            List<Card> squareOriginalCard = squareResponse.getCustomer().getCards();
            if (!CollectionUtils.isEmpty(squareOriginalCard)) {
                return squareOriginalCard.stream()
                        .map(c -> CardDTO.builder()
                                .id(c.getId())
                                .expMonth(Math.toIntExact(c.getExpMonth()))
                                .expYear(Math.toIntExact(c.getExpYear()))
                                .last4(c.getLast4())
                                .cardHolderName(c.getCardholderName())
                                .cardType(c.getCardType())
                                .cardBrand(c.getCardBrand())
                                .build())
                        .collect(Collectors.toList());
            }
        }
        return Collections.emptyList();
    }

    private RetrieveCustomerResponse getRetrieveCustomerResponse(
            BusinessSquare businessSquare, String squareCustomerId) {
        CustomersApi customersApi =
                createClient(getValidAccessToken(businessSquare)).getCustomersApi();
        RetrieveCustomerResponse squareResponse;
        try {
            squareResponse = customersApi.retrieveCustomer(squareCustomerId);
        } catch (ApiException | IOException e) {
            log.warn(
                    "Failed to retrieve customer from square: " + squareCustomerId
                            + ". for "
                            + businessSquare.getBusinessId(),
                    e);
            return null;
        }
        if (!CollectionUtils.isEmpty(squareResponse.getErrors())) {
            Error error = squareResponse.getErrors().get(0);
            log.error("get square customer info failed: {} in {}", error.getDetail(), error.getField());
            return null;
        }
        return squareResponse;
    }

    /**
     * Get square customer information as well as cards COF saved for the customer.
     */
    public SquareCustomerInfo getCustomerInfo(Integer businessId, Integer customerId) {
        BusinessSquare businessSquare = squareMapper.selectByBusinessId(businessId);
        if (businessSquare == null) {
            log.info("this business {} has no square integration", businessId);
            return null;
        }
        CustomerSquare customerSquare =
                customerSquareMapper.selectByCustomerId(businessId, customerId, businessSquare.getMerchantId());
        if (customerSquare == null) {
            log.info("this customer {} of  business {} has no square customer", customerId, businessId);
            return null;
        }
        RetrieveCustomerResponse squareResponse =
                getRetrieveCustomerResponse(businessSquare, customerSquare.getSquareCustomerId());
        if (squareResponse == null) {
            return null;
        }
        Customer customer = squareResponse.getCustomer();
        List<Card> squareOriginalCard = customer.getCards();
        List<SquareCard> cards = CollectionUtils.isEmpty(squareOriginalCard)
                ? Collections.emptyList()
                : squareOriginalCard.stream()
                        .map(c -> {
                            BillingAddress billingAddress = BillingAddress.fromSquareAddress(c.getBillingAddress());
                            return SquareCard.builder()
                                    .id(c.getId())
                                    .expMonth(Math.toIntExact(c.getExpMonth()))
                                    .expYear(Math.toIntExact(c.getExpYear()))
                                    .last4(c.getLast4())
                                    .cardHolderName(c.getCardholderName())
                                    .cardType(c.getCardType())
                                    .cardBrand(c.getCardBrand())
                                    .billingAddress(billingAddress)
                                    .build();
                        })
                        .collect(Collectors.toList());
        return SquareCustomerInfo.builder()
                .customerId(customerId)
                .squareCustomerId(customer.getId())
                .firstName(customer.getGivenName())
                .lastName(customer.getFamilyName())
                .phoneNumber(customer.getPhoneNumber())
                .email(customer.getEmailAddress())
                .cards(cards)
                .build();
    }

    /**
     * 向square 平台发起删除customer 的请求，不用等待返回结果
     *
     * @param businessId       商家ID
     * @param squareCustomerId square customer id from square platform
     */
    public void deleteSquareCustomer(Integer businessId, String squareCustomerId) {
        BusinessSquare businessSquare = this.getBusinessSquareWithException(businessId);
        CustomersApi customersApi =
                createClient(getValidAccessToken(businessSquare)).getCustomersApi();
        customersApi.deleteCustomerAsync(squareCustomerId);
    }

    /**
     * intake form add card: create customer for creating profile
     */
    public String createSquareCustomer(IntakeFormCustomerStripRequest request) {
        String squareCustomerId;
        BusinessSquare businessSquare = this.getBusinessSquareWithException(request.getBusinessId());
        CreateCustomerRequest body = new CreateCustomerRequest.Builder()
                .idempotencyKey(CommonUtil.getUuid())
                .givenName(request.getFirstName())
                .familyName(request.getLastName())
                .emailAddress(request.getEmail())
                .phoneNumber(convertSquarePhoneNumber(request.getPhone()))
                .referenceId("businessId" + request.getBusinessId())
                .note(request.getDesc())
                .build();
        CustomersApi customersApi =
                createClient(getValidAccessToken(businessSquare)).getCustomersApi();
        try {
            CreateCustomerResponse customerResponse = customersApi.createCustomer(body);
            if (!CollectionUtils.isEmpty(customerResponse.getErrors())) {
                throwResponseError(customerResponse.getErrors().get(0));
            }
            squareCustomerId = customerResponse.getCustomer().getId();
            CreateCustomerCardRequest cardRequest = new CreateCustomerCardRequest.Builder(request.getChargeToken())
                    .cardholderName(body.getGivenName())
                    .build();
            CreateCustomerCardResponse customerCard = customersApi.createCustomerCard(squareCustomerId, cardRequest);
            if (!CollectionUtils.isEmpty(customerCard.getErrors())) {
                throwResponseError(customerCard.getErrors().get(0));
            }
        } catch (ApiException | IOException e) {
            String errMsg = getErrorMessageForSquare("Failed to create customer in square. ", e);
            throw new CommonException(ResponseCodeEnum.STRIPE_CARD_EXCEPTION, errMsg, e);
        }
        return SquareCardUtil.getIntakeFormSavedCus(squareCustomerId, businessSquare.getMerchantId());
    }

    private static String convertSquarePhoneNumber(String moegoPhoneNum) {
        if (StringUtils.isEmpty(moegoPhoneNum)) {
            return null;
        } else if (moegoPhoneNum.length() == 10 || moegoPhoneNum.length() == 11) {
            return moegoPhoneNum;
        } else {
            return null;
        }
    }

    /**
     * query customer info: phone number, name, email,
     * create customer on square
     * save squareCustomerId - customerId relation
     */
    public SquareCreateCustomerResponse createCustomer(Integer businessId, SquareCreateCustomerRequest request) {
        BusinessSquare businessSquare = this.getBusinessSquareWithException(businessId);
        GroomingCustomerInfoDTO mgCustomer =
                iCustomerGroomingClient.getCustomerContactInfo(businessId, request.getCustomerId());
        if (mgCustomer == null) {
            throw new CommonException(
                    ResponseCodeEnum.PARAMS_ERROR,
                    "No customer found. businessId: " + businessId + ", customerId: " + request.getCustomerId());
        }

        CustomerSquare customerSquare = customerSquareMapper.selectByCustomerId(
                businessId, request.getCustomerId(), businessSquare.getMerchantId());
        CustomersApi customersApi =
                createClient(getValidAccessToken(businessSquare)).getCustomersApi();
        if (customerSquare == null) {
            // square customer doesn't exist, create one
            log.info("create new square customer for {}", request.getCustomerId());
            CreateCustomerRequest body = new CreateCustomerRequest.Builder()
                    .idempotencyKey(request.getCustomerId().toString())
                    .givenName(mgCustomer.getFirstName())
                    .familyName(mgCustomer.getLastName())
                    .emailAddress(mgCustomer.getEmail())
                    .phoneNumber(convertSquarePhoneNumber(mgCustomer.getPhoneNumber()))
                    .referenceId(mgCustomer.getCustomerId().toString())
                    .note("Created by MoeGo API for Customer:" + request.getCustomerId())
                    .build();

            CreateCustomerResponse customerResponse;
            try {
                customerResponse = customersApi.createCustomer(body);
            } catch (ApiException | IOException e) {
                throwRequestFailure("Failed to create customer in square. ", e);
                return null; // should never be accessed
            }
            if (!CollectionUtils.isEmpty(customerResponse.getErrors())) {
                throwResponseError(customerResponse.getErrors().get(0));
                return null;
            }

            customerSquare = new CustomerSquare();
            customerSquare.setBusinessId(businessId);
            customerSquare.setCustomerId(request.getCustomerId());
            customerSquare.setSquareCustomerId(customerResponse.getCustomer().getId());
            customerSquare.setMerchantId(businessSquare.getMerchantId());
            customerSquareMapper.insertSelective(customerSquare);
        } else {
            log.warn("square customer already created for {}", request.getCustomerId());
        }
        if (StringUtils.isEmpty(request.getCardHolderName())) {
            request.setCardHolderName(CommonUtil.nameFormat(mgCustomer.getFirstName(), mgCustomer.getLastName()));
        }
        CreateCustomerCardResponse customerCard = createCard(request, customerSquare, customersApi);

        return SquareCreateCustomerResponse.builder()
                .customerId(request.getCustomerId())
                .email(mgCustomer.getEmail())
                .phoneNumber(mgCustomer.getPhoneNumber())
                .firstName(mgCustomer.getFirstName())
                .lastName(mgCustomer.getLastName())
                .squareCustomerId(customerSquare.getSquareCustomerId())
                .card(toMoegoResponseCard(customerCard.getCard()))
                .build();
    }

    /**
     * Create customer card (COF) only. If square customer not exist, throw exception.
     */
    public SquareCreateCOFResponse createCustomerCard(Integer businessId, SquareCreateCustomerRequest request) {
        BusinessSquare businessSquare = this.getBusinessSquareWithException(businessId);
        CustomerSquare customerSquare = customerSquareMapper.selectByCustomerId(
                businessId, request.getCustomerId(), businessSquare.getMerchantId());
        if (customerSquare == null) {
            throw new CommonException(
                    ResponseCodeEnum.PARAMS_ERROR,
                    "Square customer not created yet. Please create square customer first.");
        }

        CustomersApi customersApi =
                createClient(getValidAccessToken(businessSquare)).getCustomersApi();
        CreateCustomerCardResponse customerCard = createCard(request, customerSquare, customersApi);

        return SquareCreateCOFResponse.builder()
                .customerId(request.getCustomerId())
                .squareCustomerId(customerSquare.getSquareCustomerId())
                .card(toMoegoResponseCard(customerCard.getCard()))
                .build();
    }

    private CreateCustomerCardResponse createCard(
            SquareCreateCustomerRequest request, CustomerSquare customerSquare, CustomersApi customersApi) {
        com.squareup.square.models.Address billingAddress = BillingAddress.toSquareAddress(request.getBillingAddress());

        CreateCustomerCardRequest body = new CreateCustomerCardRequest.Builder(request.getCardNonce())
                .billingAddress(billingAddress)
                .cardholderName(request.getCardHolderName())
                .build();
        String errorMsg = "Failed to create customer card on file in square. ";
        CreateCustomerCardResponse customerCard;
        try {
            customerCard = customersApi.createCustomerCard(customerSquare.getSquareCustomerId(), body);
        } catch (ApiException e) {
            // possible error:
            // https://developer.squareup.com/reference/square/customers/create-customer-card#error-descriptions
            HttpStringResponse response =
                    (HttpStringResponse) e.getHttpContext().getResponse();
            log.error("call square api for create card failed: {}", response);
            throw new CommonException(ResponseCodeEnum.STRIPE_CARD_EXCEPTION, errorMsg + response.getBody(), e);
        } catch (IOException e) {
            throw new CommonException(ResponseCodeEnum.SERVER_ERROR, errorMsg, e);
        }
        if (!CollectionUtils.isEmpty(customerCard.getErrors())) {
            throwResponseError(customerCard.getErrors().get(0));
        }
        return customerCard;
    }

    private SquareCard toMoegoResponseCard(Card card) {
        return SquareCard.builder()
                .expYear(Math.toIntExact(card.getExpYear()))
                .expMonth(Math.toIntExact(card.getExpMonth()))
                .billingAddress(BillingAddress.fromSquareAddress(card.getBillingAddress()))
                .cardType(card.getCardType())
                .id(card.getId())
                .cardHolderName(card.getCardholderName())
                .last4(card.getLast4())
                .cardBrand(card.getCardBrand())
                .build();
    }

    public String deleteCustomer(Integer businessId, Integer customerId) {
        BusinessSquare businessSquare = this.getBusinessSquareWithException(businessId);
        CustomerSquare customerSquare =
                customerSquareMapper.selectByCustomerId(businessId, customerId, businessSquare.getMerchantId());
        if (customerSquare == null) {
            throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "Square customer is not found.");
        }

        CustomersApi customersApi =
                createClient(getValidAccessToken(businessSquare)).getCustomersApi();
        DeleteCustomerResponse squareResponse;
        try {
            squareResponse = customersApi.deleteCustomer(customerSquare.getSquareCustomerId());
        } catch (ApiException | IOException e) {
            throwRequestFailure("Failed to delete customer in square. ", e);
            return null; // should never be accessed
        } finally {
            customerSquareMapper.deleteBy(customerSquare.getId());
        }

        if (!CollectionUtils.isEmpty(squareResponse.getErrors())) {
            throwResponseError(squareResponse.getErrors().get(0));
        }

        return "Success";
    }

    public String deleteCustomerCard(Integer businessId, Integer customerId, String cardId) {
        BusinessSquare businessSquare = this.getBusinessSquareWithException(businessId);
        CustomerSquare customerSquare =
                customerSquareMapper.selectByCustomerId(businessId, customerId, businessSquare.getMerchantId());
        if (customerSquare == null) {
            throw new CommonException(
                    ResponseCodeEnum.PARAMS_ERROR,
                    "Square customer not created yet. Please create square customer first.");
        }

        CustomersApi customersApi =
                createClient(getValidAccessToken(businessSquare)).getCustomersApi();
        DeleteCustomerCardResponse squareResponse;
        try {
            squareResponse = customersApi.deleteCustomerCard(customerSquare.getSquareCustomerId(), cardId);
        } catch (ApiException | IOException e) {
            throwRequestFailure("Failed to delete customer card in square. ", e);
            return null; // should never be accessed
        }
        if (!CollectionUtils.isEmpty(squareResponse.getErrors())) {
            throwResponseError(squareResponse.getErrors().get(0));
        }

        return "Success";
    }

    /**
     * Get existing unpaired device code
     */
    public List<SquareDeviceCodeResponse> getDeviceCodeForTerminalPairing(Integer businessId) {
        BusinessSquare businessSquare = this.getBusinessSquareWithException(businessId);

        DevicesApi devicesApi =
                createClient(getValidAccessToken(businessSquare)).getDevicesApi();

        ListDeviceCodesResponse unpairedCodes = null;
        try {
            // get all unpaired devices codes
            unpairedCodes = devicesApi.listDeviceCodes(
                    null,
                    businessSquare.getDefaultLocationId(),
                    SquarePaymentMethodEnum.TERMINAL_DEVICE_PAY_TYPE,
                    SquarePaymentMethodEnum.TERMINAL_DEVICE_UNPAIRED);
        } catch (ApiException | IOException e) {
            log.error("Failed get device codes from square: {}", getMessage(e), e);
        }
        if (unpairedCodes != null && !CollectionUtils.isEmpty(unpairedCodes.getDeviceCodes())) {
            return unpairedCodes.getDeviceCodes().stream()
                    .map(squareDeviceCode -> {
                        SquareDeviceCodeResponse result = new SquareDeviceCodeResponse();
                        BeanUtils.copyProperties(squareDeviceCode, result);
                        return result;
                    })
                    .collect(Collectors.toList());
        }
        return Collections.emptyList();
    }

    public SquareDeviceCodeResponse createDeviceCodeForTerminalPairing(Integer businessId, SquareDevice params) {
        BusinessSquare businessSquare = this.getBusinessSquareWithException(businessId);

        DevicesApi devicesApi =
                createClient(getValidAccessToken(businessSquare)).getDevicesApi();
        try {
            DeviceCode deviceCode = new DeviceCode.Builder(SquarePaymentMethodEnum.TERMINAL_DEVICE_PAY_TYPE)
                    .locationId(businessSquare.getDefaultLocationId())
                    .name(params.getName())
                    .build();
            CreateDeviceCodeRequest body =
                    new CreateDeviceCodeRequest.Builder(CommonUtil.getUuid(), deviceCode).build();
            CreateDeviceCodeResponse squareResponse = devicesApi.createDeviceCode(body);
            log.info(
                    "create new square device code {}",
                    squareResponse.getDeviceCode().toString());
            if (squareResponse.getDeviceCode() != null) {
                SquareDeviceCodeResponse result = new SquareDeviceCodeResponse();
                BeanUtils.copyProperties(squareResponse.getDeviceCode(), result);
                return result;
            } else {
                throwResponseError(squareResponse.getErrors().get(0));
            }
        } catch (ApiException | IOException e) {
            throwRequestFailure("Failed creating device code in Square. ", e);
        }
        return null;
    }

    public List<SquareDevice> listTerminalDevices(Integer businessId) {
        BusinessSquare businessSquare = squareMapper.selectByBusinessId(businessId);
        if (businessSquare == null) {
            log.warn("business {} has no square integration but will get paired devices code", businessId);
            return Collections.emptyList();
        }

        DevicesApi devicesApi =
                createClient(getValidAccessToken(businessSquare)).getDevicesApi();

        List<DeviceCode> deviceCodesList = new ArrayList<>();
        try {
            String cursor = null;
            // 最多翻三页
            int maxPageNum = 3;
            do {
                ListDeviceCodesResponse squareResponse = devicesApi.listDeviceCodes(
                        cursor,
                        businessSquare.getDefaultLocationId(),
                        SquarePaymentMethodEnum.TERMINAL_DEVICE_PAY_TYPE,
                        SquarePaymentMethodEnum.TERMINAL_DEVICE_PAIRED);
                if (!StringUtils.isEmpty(squareResponse.getDeviceCodes())) {
                    deviceCodesList.addAll(squareResponse.getDeviceCodes());
                }
                /**
                 * no cursor:
                 * {
                 *   "device_codes": [
                 *     {
                 *       "id": "GWCKMP7JVMHYF",
                 *       "name": "demo 2",
                 *       "code": "HWCDEP",
                 *       "device_id": "024CS108A6002142",
                 *       "product_type": "TERMINAL_API",
                 *       "location_id": "LSVFJ4B3JANZC",
                 *       "created_at": "2021-07-20T12:39:02.000Z",
                 *       "status": "PAIRED",
                 *       "status_changed_at": "2021-07-20T12:39:12.000Z",
                 *       "paired_at": "2021-07-20T12:39:12.000Z"
                 *     }
                 *   ]
                 * }
                 * has cursor:
                 * {
                 *   "cursor": "2588739"
                 * }
                 */
                // if available , get more
                cursor = squareResponse.getCursor();
            } while (cursor != null && maxPageNum-- > 0);
        } catch (ApiException | IOException e) {
            throwRequestFailure("Failed list devices from square. ", e);
            return null;
        }

        if (CollectionUtils.isEmpty(deviceCodesList)) {
            return ImmutableList.of();
        }

        return deviceCodesList.stream()
                .map(d -> {
                    SquareDevice device = new SquareDevice();
                    BeanUtils.copyProperties(d, device);
                    return device;
                })
                .collect(Collectors.toList());
    }

    public BusinessSquare getBusinessSquareWithException(Integer businessId) {
        BusinessSquare businessSquare = squareMapper.selectByBusinessId(businessId);
        if (businessSquare == null) {
            throw new CommonException(
                    ResponseCodeEnum.PARAMS_ERROR,
                    "The Square authorization has not been completed yet. Please set up and try again.");
        }
        return businessSquare;
    }

    public List<BusinessSquare> getBusinessSquareListWithBusinessIds(List<Integer> businessIds) {
        if (CollectionUtils.isEmpty(businessIds)) {
            return Collections.emptyList();
        }
        return squareMapper.selectByBusinessIds(businessIds);
    }

    public List<PaymentDTO> getTerminalCheckoutStatus(Integer businessId, String checkoutId) {
        List<Payment> paymentList = paymentMapper.selectPaymentsByCheckoutId(businessId, checkoutId);
        return paymentList.stream()
                .map(p -> {
                    PaymentDTO dto = new PaymentDTO();
                    BeanUtils.copyProperties(p, dto);
                    return dto;
                })
                .collect(Collectors.toList());
    }

    public PaymentDTO getPaymentStatus(Integer businessId, Integer primaryId) {
        if (PrimitiveTypeUtil.isNullOrZero(primaryId)) {
            return null;
        }
        Payment payment = paymentMapper.selectByPrimaryKey(primaryId);
        if (!payment.getBusinessId().equals(businessId)) {
            return null;
        }
        PaymentDTO dto = new PaymentDTO();
        BeanUtils.copyProperties(payment, dto);
        return dto;
    }

    public TerminalChargeResponse cancelTerminalPayment(Integer businessId, String checkoutId) {
        BusinessSquare businessSquare = this.getBusinessSquareWithException(businessId);
        TerminalApi terminalApi =
                createClient(getValidAccessToken(businessSquare)).getTerminalApi();
        CancelTerminalCheckoutResponse terminalCheckout;
        try {
            terminalCheckout = terminalApi.cancelTerminalCheckout(checkoutId);
        } catch (ApiException | IOException e) {
            throwRequestFailure("Failed to cancel terminal checkout via Square. ", e);
            return null;
        }
        if (!CollectionUtils.isEmpty(terminalCheckout.getErrors())) {
            throwResponseError(terminalCheckout.getErrors().get(0));
        }
        return TerminalChargeResponse.builder()
                .checkoutId(terminalCheckout.getCheckout().getId())
                .status(terminalCheckout.getCheckout().getStatus())
                .deadlineDuration(terminalCheckout.getCheckout().getDeadlineDuration())
                .build();
    }

    public TerminalChargeResponse terminalCharge(Integer businessId, SquarePaymentRequest request) {
        // 检查定金支付状态
        if (PaymentMethodEnum.MODULE_GROOMING.equals(request.getModule())) {
            paymentService.checkDepositStatus(request.getIsDeposit(), request.getInvoiceId());
        }
        BusinessSquare businessSquare = this.getBusinessSquareWithException(businessId);
        request.setLocationId(businessSquare.getDefaultLocationId());
        request.setMerchant(businessSquare.getMerchantId());
        Payment payment = buildTransactionRecord(businessId, request);
        payment.setSquarePaymentMethod(SquarePaymentMethodEnum.TERMINAL_PAY);
        paymentMapper.insertSelective(payment);

        TerminalApi terminalApi =
                createClient(getValidAccessToken(businessSquare)).getTerminalApi();
        DeviceCheckoutOptions deviceOptions = new DeviceCheckoutOptions.Builder(request.getDeviceId())
                .skipReceiptScreen(false)
                .tipSettings(new TipSettings.Builder()
                        .separateTipScreen(true)
                        .allowTipping(!Boolean.TRUE.equals(request.getSkipTipping()))
                        .customTipField(true)
                        .build())
                .build();
        CreateTerminalCheckoutRequest body = new CreateTerminalCheckoutRequest.Builder(
                        payment.getId().toString(),
                        new TerminalCheckout.Builder(
                                        new Money(
                                                toUnitAmount(request.getAmount(), request.getCurrency()),
                                                request.getCurrency()),
                                        deviceOptions)
                                .referenceId(request.getInvoiceId() + "-" + payment.getId())
                                .note("Created by MoeGo API for invoice:" + request.getInvoiceId())
                                .build())
                .build();

        CreateTerminalCheckoutResponse terminalCheckout;
        try {
            terminalCheckout = terminalApi.createTerminalCheckout(body);
        } catch (ApiException | IOException e) {
            updatePaymentDBRecordFailure(payment);
            throwRequestFailure("Failed Creating terminal checkout via Square. ", e);
            return null;
        }
        if (!CollectionUtils.isEmpty(terminalCheckout.getErrors())) {
            updatePaymentDBRecordFailure(payment);
            throwResponseError(terminalCheckout.getErrors().get(0));
        }

        // only add checkout id here, status change updated by terminal.checkout.updated webhook asynchronous
        Payment toUpdate = new Payment();
        toUpdate.setId(payment.getId());
        toUpdate.setSquareCheckoutId(terminalCheckout.getCheckout().getId());
        toUpdate.setUpdateTime(DateUtil.get10Timestamp());
        paymentMapper.updateByPrimaryKeySelective(toUpdate);

        if (BooleanEnum.VALUE_TRUE.equals(request.getIsDeposit())) {
            DepositVo depositVo = new DepositVo();
            BeanUtils.copyProperties(payment, depositVo);
            iDepositClient.createOrUpdateDeposit(depositVo);
        }
        return TerminalChargeResponse.builder()
                .amount(request.getAmount())
                .currency(request.getCurrency())
                .deviceId(request.getDeviceId())
                .checkoutId(terminalCheckout.getCheckout().getId())
                .moePaymentId(payment.getId())
                .status(terminalCheckout.getCheckout().getStatus())
                .deadlineDuration(terminalCheckout.getCheckout().getDeadlineDuration())
                .build();
    }

    /**
     * Updated the terminal payment status after it is finished (success or fail)
     * {
     * "merchant_id":"ML4G2JYD6HW6C",
     * "type":"terminal.checkout.updated",
     * "event_id":"5b147fbb-5be2-4cea-836b-9b895d1d9aab",
     * "created_at":"2021-07-12T08:23:15.1933266Z",
     * "data":{
     * "type":"checkout.event",
     * "id":"dhgENdnFOPXqO",
     * "object":{
     * "checkout":{
     * "amount_money":{
     * "amount":111,
     * "currency":"USD"
     * },
     * "app_id":"*****************************",
     * "created_at":"2020-04-10T14:43:55.262Z",
     * "deadline_duration":"PT5M",
     * "device_options":{
     * "device_id":"907CS13101300122",
     * "skip_receipt_screen":false,
     * "tip_settings":{
     * "allow_tipping":false
     * }
     * },
     * "id":"dhgENdnFOPXqO",
     * "note":"A simple note",
     * "payment_ids":[
     * "dgzrZTeIeVuOGwYgekoTHsPouaB"
     * ],
     * "reference_id":"id72709",
     * "status":"COMPLETED",
     * "updated_at":"2020-04-10T14:44:06.039Z"
     * }
     * }
     * }
     * }
     * set-tips: terminal pay with tips:
     * {
     * "payment": {
     * "id": "zxsUkPA5ar6Q3UlmPjK5GnvPBMEZY",
     * "created_at": "2021-07-28T06:49:45.076Z",
     * "updated_at": "2021-07-28T07:40:27.849Z",
     * "amount_money": {
     * "amount": 150,
     * "currency": "USD"
     * },
     * "tip_money": {
     * "amount": 22,
     * "currency": "USD"
     * },
     * "refunded_money": {
     * "amount": 172,
     * "currency": "USD"
     * },
     * "status": "COMPLETED",
     * "delay_duration": "PT36H",
     * "source_type": "CARD",
     * "card_details": {
     * "status": "CAPTURED",
     * "card": {
     * "card_brand": "VISA",
     * "last_4": "9054",
     * "exp_month": 4,
     * "exp_year": 2026,
     * "cardholder_name": "CARDHOLDER/VISA",
     * "fingerprint": "sq-1-iOTNvNyB2mQ9APtCc0OKrDdt8IujxmJ5-iacQGW2T_KB9MDp9v0eLSH3K1g4Swti2Q",
     * "card_type": "DEBIT",
     * "prepaid_type": "NOT_PREPAID",
     * "bin": "486796"
     * },
     * "entry_method": "CONTACTLESS",
     * "cvv_status": "CVV_NOT_CHECKED",
     * "avs_status": "AVS_NOT_CHECKED",
     * "auth_result_code": "054902",
     * "application_identifier": "A0000000031010",
     * "application_name": "VISA DEBIT",
     * "application_cryptogram": "985743bf2b277b06",
     * "verification_method": "NONE",
     * "verification_results": "UNKNOWN",
     * "statement_description": "SQ *MOEGOMOBILE",
     * "device_details": {
     * "device_id": "110CS149A2000272",
     * "device_installation_id": "3c16236a-6d4f-4048-8518-45092bb69f3f"
     * },
     * "card_payment_timeline": {
     * "authorized_at": "2021-07-28T06:49:45.472Z",
     * "captured_at": "2021-07-28T06:50:00.359Z"
     * }
     * },
     * "location_id": "8F911ZTXTYB23",
     * "order_id": "3Mw0r7492LGaDHs5SUo2jsLvvFLZY",
     * "reference_id": "105574-52323",
     * "refund_ids": [
     * "zxsUkPA5ar6Q3UlmPjK5GnvPBMEZY_QaLpdpyxMTtJnQ5h0mgEJZflqmR5Y0oTGd56ux2UpvU"
     * ],
     * "terminal_checkout_id": "nN9Q6wTEQ0bqO",
     * "processing_fee": [
     * {
     * "effective_at": "2021-07-28T08:50:01.000Z",
     * "type": "INITIAL",
     * "amount_money": {
     * "amount": 14,
     * "currency": "USD"
     * }
     * }
     * ],
     * "note": "Note: Created by MoeGo API for invoice:105574",
     * "total_money": {
     * "amount": 172,
     * "currency": "USD"
     * },
     * "approved_money": {
     * "amount": 150,
     * "currency": "USD"
     * },
     * "receipt_number": "zxsU",
     * "receipt_url": "https://squareup.com/receipt/preview/zxsUkPA5ar6Q3UlmPjK5GnvPBMEZY",
     * "delay_action": "CANCEL",
     * "delayed_until": "2021-07-29T18:49:45.076Z",
     * "version_token": "aWoLqZWgzIDGbM8S6PbXihLApZzCh3UEAh98WLloHIn6o"
     * }
     * }
     */
    public SquareTerminalCheckoutResponse terminalChargeUpdated(JSONObject jsonObject) {
        SquareTerminalCheckoutResponse result = SquareTerminalCheckoutResponse.builder()
                .success(false)
                .moegoPaymentIds(new ArrayList<>())
                .build();
        String merchantId = jsonObject.getString(SquarePaymentMethodEnum.KEY_MERCHANT);
        String checkoutId = jsonObject.getJSONObject("data").getString("id");
        JSONObject checkout =
                jsonObject.getJSONObject("data").getJSONObject("object").getJSONObject("checkout");
        String referenceId = checkout.getString("reference_id");
        JSONArray paymentIds = checkout.getJSONArray("payment_ids");
        if (checkoutId == null || referenceId == null) {
            log.info("Notification contains no paymentId or checkoutId or referenceId, skip.");
            result.setReason("checkout id or reference id is null");
            result.setSuccess(false);
            return result;
        }
        // check checkout status to be completed
        String status = checkout.getString("status");
        log.info("Checkout {} status is {}", checkoutId, status);
        if (PaymentMethodEnum.CHECK_OUT_STATUS_PENDING.equals(status)
                || PaymentMethodEnum.CHECK_OUT_STATUS_IN_PROGRESS.equals(status)
                || PaymentMethodEnum.CHECK_OUT_STATUS_CANCEL_REQUESTED.equals(status)) {
            log.info("omit this event for {}", referenceId);
            result.setReason(status);
            return result;
        }
        // invoiceId-paymentRecordId
        String[] referIds = referenceId.split("-", 2);
        if (referIds.length != 2) {
            throw new CommonException(
                    ResponseCodeEnum.PARAMS_ERROR, "invalid reference id for this terminal checkout " + checkoutId);
        }
        final Integer invoiceId = Integer.parseInt(referIds[0]);
        BigDecimal tipsAmount = new BigDecimal(0.00);
        Payment payment = paymentMapper.selectByPrimaryKey(Integer.parseInt(referIds[1]));
        result.setIsDeposit(BooleanEnum.VALUE_TRUE.equals(payment.getIsDeposit()));
        // 处理cancel 事件
        if (PaymentMethodEnum.CHECK_OUT_STATUS_CANCELED.equals(status)) {
            String cancelReason = checkout.getString("cancel_reason");
            Payment toCanceledPay = new Payment();
            toCanceledPay.setId(payment.getId());
            toCanceledPay.setStatus(PaymentStatusEnum.FAILED);
            toCanceledPay.setUpdateTime(DateUtil.get10Timestamp());
            toCanceledPay.setCancelReason(cancelReason);
            paymentMapper.updateByPrimaryKeySelective(toCanceledPay);
            return result;
        }
        // 处理completed事件
        if (CollectionUtils.isEmpty(paymentIds)) {
            log.warn("Checkout completed, but no paymentIds");
            Payment toCanceledPay = new Payment();
            toCanceledPay.setId(payment.getId());
            toCanceledPay.setStatus(PaymentStatusEnum.FAILED);
            toCanceledPay.setUpdateTime(DateUtil.get10Timestamp());
            toCanceledPay.setCancelReason("NO PAYMENTS ID COLLECTED IN CHECKOUT");
            paymentMapper.updateByPrimaryKeySelective(toCanceledPay);
            return result;
        }
        BusinessSquare businessSquare = getBusinessSquareWithException(payment.getBusinessId());
        if (!businessSquare.getMerchantId().equals(merchantId)) {
            log.warn(
                    "merchantId not match for this business {} and checkout {}",
                    businessSquare.getBusinessId(),
                    merchantId);
        }
        result.setInvoiceId(payment.getInvoiceId());
        result.setModule(payment.getModule());
        result.setIsOnline(payment.getIsOnline());
        PaymentsApi paymentsApi =
                createClient(getValidAccessToken(businessSquare)).getPaymentsApi();
        for (int i = 0; i < paymentIds.size(); i++) {
            String paymentId = paymentIds.getString(i);
            GetPaymentResponse squareResponse;
            try {
                squareResponse = paymentsApi.getPayment(paymentId);
            } catch (ApiException | IOException e) {
                throwRequestFailure("Failed to get payment from Square in checkout callback.", e);
                return result;
            }
            if (!CollectionUtils.isEmpty(squareResponse.getErrors())) {
                throwResponseError(squareResponse.getErrors().get(0));
            }

            Payment toUpdate = new Payment();
            Money tipsMoney = squareResponse.getPayment().getTipMoney();
            if (tipsMoney != null && tipsMoney.getAmount() != null && tipsMoney.getAmount() > 0) {
                tipsAmount = toDecimalAmount(tipsMoney.getAmount(), tipsMoney.getCurrency());
            }
            if (i == 0) {
                // update payment record
                toUpdate.setId(payment.getId());
                assignRecordUpdate(squareResponse, toUpdate);
                paymentMapper.updateByPrimaryKeySelective(toUpdate);
            } else {
                // insert payment record for more payments
                BeanUtils.copyProperties(payment, toUpdate);
                assignRecordUpdate(squareResponse, toUpdate);
                paymentMapper.insertSelective(toUpdate);
            }
            result.getMoegoPaymentIds().add(toUpdate.getId());
        }
        // if customer add tips， then sync to invoice table
        final BigDecimal sumOfTips = tipsAmount;
        SetTipsRequest tipsParam = SetTipsRequest.newBuilder()
                .setBusinessId(payment.getBusinessId())
                .setInvoiceId(invoiceId)
                .setOmitResult(true)
                .setValue(sumOfTips.doubleValue())
                .setValueType(InvoiceValueType.AMOUNT.value())
                .build();
        updateSquareTips(payment.getId(), tipsParam);

        result.setSuccess(true);
        return result;
    }

    private void assignRecordUpdate(GetPaymentResponse squareResponse, Payment toUpdate) {
        Card card = squareResponse.getPayment().getCardDetails().getCard();
        toUpdate.setStatus(PaymentStatusEnum.PAID);
        toUpdate.setStripeChargeId(squareResponse.getPayment().getId());
        toUpdate.setUpdateTime(DateUtil.get10Timestamp());
        Money money = squareResponse.getPayment().getTotalMoney();
        toUpdate.setAmount(toDecimalAmount(money.getAmount(), money.getCurrency()));
        setSquareCardDetailForPayment(toUpdate, card);
    }

    public ReaderAuthCodeResponse getReaderAuthCode(Integer businessId, String locationId) {
        BusinessSquare businessSquare = this.getBusinessSquareWithException(businessId);
        MobileAuthorizationApi mobileAuthApi =
                createClient(getValidAccessToken(businessSquare)).getMobileAuthorizationApi();

        String location = locationId == null ? businessSquare.getDefaultLocationId() : locationId;
        CreateMobileAuthorizationCodeResponse squareResponse;
        try {
            squareResponse =
                    mobileAuthApi.createMobileAuthorizationCode(new CreateMobileAuthorizationCodeRequest(location));
        } catch (ApiException | IOException e) {
            throwRequestFailure("Failed getting mobile reader auth code. ", e);
            return null;
        }
        if (squareResponse.getError() != null) {
            throwResponseError(squareResponse.getError());
        }

        ReaderAuthCodeResponse result = new ReaderAuthCodeResponse();
        result.setBusinessId(businessId);
        result.setLocationId(location);
        result.setAuthCode(squareResponse.getAuthorizationCode());
        result.setExpiresAt(squareResponse.getExpiresAt());
        return result;
    }

    private void updateSquareTips(Integer paymentId, SetTipsRequest tips) {
        String resourceCacheKey = lockManager.getResourceKey(LockManager.PAYMENT_WEBHOOK_SET_TIPS, paymentId);
        String randomValue = CommonUtil.getUuid();
        try {
            if (!lockManager.lock(resourceCacheKey, randomValue)) {
                return;
            }
            Payment payment = paymentMapper.selectByPrimaryKey(paymentId);
            log.info("update tips lock: payId {}, status {}", paymentId, payment.getStatus());
            if (payment.getTips().compareTo(BigDecimal.valueOf(tips.getValue())) != 0 && tips.getValue() > 0) {
                orderClient.setTips(tips);
                Payment p = new Payment();
                p.setId(paymentId);
                p.setTips(BigDecimal.valueOf(tips.getValue()));
                paymentMapper.updateByPrimaryKeySelective(p);
                log.info("update tips[{}] for square: payId {}", tips, paymentId);
            }
        } catch (Exception e) {
            log.error("update square tips failed:{}", paymentId, e);
        } finally {
            lockManager.unlock(resourceCacheKey, randomValue);
        }
    }

    public SquareReaderUpdateResponse webhookPaymentUpdate(JSONObject jsonObject) {
        // note: MoeGo #999-6666-4   MoeGo #INVOICE_ID-PAYMENT_ID-square_payment_method
        SquareReaderUpdateResponse result = new SquareReaderUpdateResponse();
        result.setSuccess(false);
        String merchantId = jsonObject.getString(SquarePaymentMethodEnum.KEY_MERCHANT);
        JSONObject paymentJsonObj =
                jsonObject.getJSONObject("data").getJSONObject("object").getJSONObject("payment");
        String squarePaymentId = jsonObject.getJSONObject("data").getString("id");
        String status = paymentJsonObj.getString("status");
        String orderId = paymentJsonObj.getString("order_id");
        if (!PaymentMethodEnum.CHECK_OUT_STATUS_COMPLETED.equals(status)) {
            return result;
        }
        Payment existingPayment = paymentMapper.selectByStripeChargeId(squarePaymentId);
        if (existingPayment == null) {
            // 如果payment对象包含 refunded_money 表示是退款触发的payment 更新，不用继续处理
            if (paymentJsonObj.containsKey("refunded_money")) {
                log.warn("payment updated for square refund. order {}  in payment {}.", orderId, squarePaymentId);
                return result;
            }

            // get order and check note
            List<BusinessSquare> businessSquares = squareMapper.selectByMerchantId(merchantId);
            log.info("found business square records for {}, size={}", merchantId, businessSquares.size());
            for (BusinessSquare businessSquare : businessSquares) {
                try {
                    OrdersApi ordersApi =
                            createClient(getValidAccessToken(businessSquare)).getOrdersApi();
                    RetrieveOrderResponse squareResponse = ordersApi.retrieveOrder(orderId);
                    List<OrderLineItem> orderLineItems =
                            squareResponse.getOrder().getLineItems();
                    if (CollectionUtils.isEmpty(orderLineItems)) {
                        log.warn("found square order {} in payment {} , but no line items.", orderId, squarePaymentId);
                        return result;
                    }
                    String readerNote = orderLineItems.get(0).getNote();
                    if (ObjectUtils.isEmpty(readerNote)) {
                        log.warn("found square order {}  in payment {} , but no note.", orderId, squarePaymentId);
                        return result;
                    }
                    if (!readerNote.startsWith(PaymentMethodEnum.SQUARE_CREDIT_NOTE_PREFIX)) {
                        log.info("ignore square pos for {} , orderId {}", readerNote, orderId);
                        return result;
                    }
                    Integer moegoPaymentId = SquareCardUtil.getMoegoPaymentId(readerNote);
                    SquarePaymentRequest request = new SquarePaymentRequest();
                    request.setIsOnline(false);
                    request.setSquarePaymentId(squarePaymentId);
                    request.setBusinessId(businessSquare.getBusinessId());
                    Integer updatedCount = updateReaderPayment(moegoPaymentId, request);
                    if (updatedCount > 0) {
                        result.setSuccess(true);
                        result.setRequest(request);
                        result.setPrimaryId(moegoPaymentId);
                    }
                    return result;
                } catch (Exception e) {
                    log.error("update reader payment for {} failed", squarePaymentId, e);
                }
            }
            log.warn("no payment can be updated for square payment id {}", squarePaymentId);
            return result;
        }
        Payment toUpdatePay = new Payment();
        toUpdatePay.setId(existingPayment.getId());
        toUpdatePay.setUpdateTime(CommonUtil.get10Timestamp());
        /**
         * https://jira.moego.pet/browse/MOE-2283
         * 1. check status: only completed should be processed
         * 2. find businessSquare by merchantId and locationId
         * 3. get order detail by order id and get note: order.line_items[0].note
         * {
         *   "order": {
         *     "id": "AlGb9ypkovy7BvqNATGVX3oeV",
         *     "location_id": "L9Y1VYGG2HKBX",
         *     "line_items": [
         *       {
         *         "uid": "FCD386EF-0B9A-47D0-849F-ED35F939810F",
         *         "quantity": "1",
         *         "base_price_money": {
         *           "amount": 200,
         *           "currency": "USD"
         *         },
         *         "note": "MoeGo #105882-52593-4"
         * 4. update reader  payment result and invoice by paymentId and orderId(transactionId)
         *  4.1 并发控制/幂等
         */

        // update payment tips and invoice for terminal
        if (paymentJsonObj.containsKey(PaymentStripeStatus.SQUARE_TIPS_KEY)
                && existingPayment.getTips().compareTo(BigDecimal.ZERO) < 0) {
            // moe-2736 double check terminal tips and amount for each payment by charge_id
            JSONObject tipsMoney = paymentJsonObj.getJSONObject(PaymentStripeStatus.SQUARE_TIPS_KEY);
            BigDecimal gotTips = toDecimalAmount(tipsMoney.getLong("amount"), tipsMoney.getString("currency"));
            SetTipsRequest tipsParam = SetTipsRequest.newBuilder()
                    .setBusinessId(existingPayment.getBusinessId())
                    .setInvoiceId(existingPayment.getInvoiceId())
                    .setOmitResult(true)
                    .setValue(gotTips.doubleValue())
                    .setValueType(InvoiceValueType.AMOUNT.value())
                    .build();
            updateSquareTips(existingPayment.getId(), tipsParam);
            JSONObject totalMoney = paymentJsonObj.getJSONObject("total_money");
            toUpdatePay.setAmount(toDecimalAmount(totalMoney.getLong("amount"), tipsMoney.getString("currency")));
            // 同步tips后，保证同步 payment result
            SquarePaymentRequest request = new SquarePaymentRequest();
            request.setInvoiceId(existingPayment.getInvoiceId());
            request.setModule(existingPayment.getModule());
            request.setIsOnline(false);
            request.setSquarePaymentId(squarePaymentId);
            request.setBusinessId(existingPayment.getBusinessId());
            result.setSuccess(true);
            result.setRequest(request);
            result.setPrimaryId(existingPayment.getId());
        }
        // update processing fee for all square payment
        if (paymentJsonObj.containsKey(PaymentStripeStatus.SQUARE_FEE_KEY)) {
            JSONArray processFeeArray = paymentJsonObj.getJSONArray(PaymentStripeStatus.SQUARE_FEE_KEY);
            if (processFeeArray.size() > 1) {
                log.warn("not expected fee size for square payment {} ", processFeeArray.size());
            }
            JSONObject feeMoney = processFeeArray.getJSONObject(0).getJSONObject("amount_money");
            BigDecimal gotProcessingFee = toDecimalAmount(feeMoney.getLong("amount"), feeMoney.getString("currency"));
            toUpdatePay.setProcessingFee(gotProcessingFee);
            log.info("got processing fee {} for {}", squarePaymentId, gotProcessingFee);
        }
        paymentMapper.updateByPrimaryKeySelective(toUpdatePay);
        return result;
    }

    public Integer recordMobileReaderPayment(SquarePaymentRequest request) {
        BusinessSquare businessSquare = getBusinessSquareWithException(request.getBusinessId());
        request.setLocationId(businessSquare.getDefaultLocationId());
        request.setMerchant(businessSquare.getMerchantId());
        Payment record = buildTransactionRecord(request.getBusinessId(), request);
        if (Boolean.TRUE.equals(request.getIsSquarePos())) {
            record.setSquarePaymentMethod(SquarePaymentMethodEnum.SQUARE_POS);
        } else {
            record.setSquarePaymentMethod(SquarePaymentMethodEnum.READER_PAY);
        }
        paymentMapper.insertSelective(record);
        if (BooleanEnum.VALUE_TRUE.equals(request.getIsDeposit())) {
            DepositVo depositVo = new DepositVo();
            BeanUtils.copyProperties(record, depositVo);
            iDepositClient.createOrUpdateDeposit(depositVo);
        }
        return record.getId();
    }

    public RetrieveOrderResponse getSquareOder(Integer businessId, String orderId) {
        BusinessSquare businessSquare = getBusinessSquareWithException(businessId);
        OrdersApi ordersApi = createClient(getValidAccessToken(businessSquare)).getOrdersApi();
        RetrieveOrderResponse squareResponse;
        try {
            squareResponse = ordersApi.retrieveOrder(orderId);
        } catch (ApiException | IOException e) {
            throwRequestFailure("Failed to get order from square.", e);
            return null;
        }
        if (!CollectionUtils.isEmpty(squareResponse.getErrors())) {
            throwResponseError(squareResponse.getErrors().get(0));
        }
        log.info(
                "Order reference {}: tender-> {} ",
                squareResponse.getOrder().getLineItems().get(0).getNote(),
                squareResponse.getOrder().getTenders());
        return squareResponse;
    }

    public Integer updateReaderPayment(Integer primaryId, SquarePaymentRequest request) {
        Payment currentReaderPay = paymentMapper.selectByPrimaryKey(primaryId);
        if (currentReaderPay == null || !currentReaderPay.getBusinessId().equals(request.getBusinessId())) {
            throw new CommonException(ResponseCodeEnum.PAYMENT_NOT_FOUND);
        }
        request.setBusinessId(currentReaderPay.getBusinessId());
        request.setStaffId(currentReaderPay.getStaffId());
        request.setInvoiceId(currentReaderPay.getInvoiceId());
        request.setGroomingId(currentReaderPay.getGroomingId());
        request.setCustomerId(currentReaderPay.getCustomerId());
        request.setModule(currentReaderPay.getModule());
        BusinessSquare businessSquare = getBusinessSquareWithException(request.getBusinessId());
        Payment record = new Payment();
        record.setId(primaryId);
        record.setStatus(PaymentStatusEnum.PAID);
        record.setStripeChargeId(request.getSquarePaymentId());
        record.setUpdateTime(DateUtil.get10Timestamp());
        log.info("update reader pay for {} in {}", record.getStripeChargeId(), record.getId());
        paymentMapper.updateByPrimaryKeySelective(record);
        // save card info
        PaymentsApi paymentsApi =
                createClient(getValidAccessToken(businessSquare)).getPaymentsApi();
        try {
            GetPaymentResponse squareResponse;
            squareResponse = paymentsApi.getPayment(request.getSquarePaymentId());
            if (!CollectionUtils.isEmpty(squareResponse.getErrors())) {
                throwResponseError(squareResponse.getErrors().get(0));
            }
            if (squareResponse.getPayment() == null) {
                log.error("get payment [{}] failed : {}", request.getSquarePaymentId(), squareResponse.toString());
                throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "transaction is invalid. No valid tender id.");
            } else {
                log.info("reader payment : {}", squareResponse.getPayment());
            }
            // check status is APPROVED
            if (PaymentMethodEnum.CHECK_OUT_STATUS_COMPLETED.equals(
                    squareResponse.getPayment().getStatus())) {
                log.warn("tender status is {}", squareResponse.getPayment().getStatus());
            } else if (PaymentMethodEnum.PAYMENT_DELAY_STATUS_APPROVED.equals(
                    squareResponse.getPayment().getStatus())) {
                // change payment status from APPROVED to COMPLETED
                TransactionsApi transactionsApi =
                        createClient(getValidAccessToken(businessSquare)).getTransactionsApi();
                CaptureTransactionResponse completePaymentResponse = transactionsApi.captureTransaction(
                        squareResponse.getPayment().getLocationId(),
                        squareResponse.getPayment().getOrderId());
                // https://developer.squareup.com/reference/square/orders/pay-order
                log.info("complete this order {}", squareResponse.getPayment().getOrderId());
            } else {
                throw new CommonException(
                        ResponseCodeEnum.PARAMS_ERROR,
                        "Invalid payment status: " + squareResponse.getPayment().getStatus());
            }
            List<ProcessingFee> fees = squareResponse.getPayment().getProcessingFee();
            if (fees != null) {
                // square 生成fee之后的最后一次更新
                Long squareFee = 0L;
                for (ProcessingFee fee : fees) {
                    squareFee += fee.getAmountMoney().getAmount();
                }
                record.setProcessingFee(
                        toDecimalAmount(squareFee, fees.get(0).getAmountMoney().getCurrency()));
                log.info("update reader fee[{}] for {}", record.getId(), request.getSquarePaymentId());
            } else {
                // 支付成功后，立即更新 add tips if available
                Money tipsMoney = squareResponse.getPayment().getTipMoney();
                if (tipsMoney != null && tipsMoney.getAmount() != null && tipsMoney.getAmount() > 0) {
                    BigDecimal tips = toDecimalAmount(tipsMoney.getAmount(), tipsMoney.getCurrency());
                    SetTipsRequest tipsParam = SetTipsRequest.newBuilder()
                            .setBusinessId(currentReaderPay.getBusinessId())
                            .setInvoiceId(currentReaderPay.getInvoiceId())
                            .setOmitResult(true)
                            .setValue(tips.doubleValue())
                            .setValueType(InvoiceValueType.AMOUNT.value())
                            .build();
                    updateSquareTips(record.getId(), tipsParam);
                    record.setAmount(currentReaderPay.getAmount().add(tips));
                }
                setSquareCardDetailForPayment(
                        record, squareResponse.getPayment().getCardDetails().getCard());
                log.info("update reader pay[{}] tips for {}", record.getId(), request.getSquarePaymentId());
            }
        } catch (ApiException | IOException e) {
            throwRequestFailure("Failed to complete payment from Square. ", e);
            return null;
        }
        record.setUpdateTime(DateUtil.get10Timestamp());
        return paymentMapper.updateByPrimaryKeySelective(record);
    }

    public String getEndpointSecret() {
        return endpointSecret;
    }

    public String getEndpointUrl() {
        return endpointUrl;
    }

    /**
     * 请求来自public接口，数据不能直接使用，需要请求square验证
     * Delete Square customer in Moego DB if it is deleted manually from Square seller dashboard.
     * Before deletion, verification MUST done:
     * 1. Requesting Square and make sure the customer not exist anymore.
     * 2. Querying Moego DB and Square should use both merchantId and squareCustomerId. And they should be the same
     * 3. (Optimize) Most delete notification are not from Seller's manually operation.
     * So check DB to see if customer already been deleted, if yes skip
     */
    public void webhookDeleteCustomer(JSONObject jsonObject) {
        String merchantId = jsonObject.getString(SquarePaymentMethodEnum.KEY_MERCHANT);
        String squareCustomerId = jsonObject.getJSONObject("data").getString("id");
        // 用户在square pos app merge多个customer时，会生成一个新的square_customer_id，并发送merge事件把多个customer的square_customer_id更新成同一个
        // 后续再删除这个customer时，这里会查出不止一个，所以修改成list接收
        List<CustomerSquare> customerSquareList =
                customerSquareMapper.selectBySquareCustomerId(merchantId, squareCustomerId);
        if (customerSquareList == null || customerSquareList.size() == 0) {
            log.info("customer already deleted. no more action needed.");
            return;
        }
        if (jsonObject.getJSONObject("data").getBoolean("deleted")) {
            Set<Integer> updateIdSet =
                    customerSquareList.stream().map(CustomerSquare::getId).collect(Collectors.toSet());
            JSONObject customerInfo = jsonObject.getJSONObject("data").getJSONObject("object");
            if (customerInfo.containsKey("event_context")) {
                if (customerInfo.getJSONObject("event_context").containsKey("merge")) {
                    String newSquareCustomerId = customerInfo
                            .getJSONObject("event_context")
                            .getJSONObject("merge")
                            .getString("to_customer_id");

                    // 批量更新相同的square customer id到新的square customer id
                    customerSquareMapper.batchUpdateSquareCustomerIdByIds(updateIdSet, newSquareCustomerId);
                    log.info(
                            "square merge customer{} from {} to {} ",
                            updateIdSet,
                            squareCustomerId,
                            newSquareCustomerId);
                    return;
                }
            }
            // 如果不是merge，则批量失效customer_square记录
            customerSquareMapper.deleteByIds(updateIdSet);
            log.info("Square customer deleted, customerIds: {}, squareCustomerId: {}", updateIdSet, squareCustomerId);
        } else {
            log.warn("customer[{}] not deleted yet in square", squareCustomerId);
        }
    }

    /**
     * https://developer.squareup.com/docs/oauth-api/best-practices#ensure-api-calls-made-with-oauth-tokens-handle-token-based-errors-appropriately
     *
     * @param jsonObject
     */
    public void webhookRevokeAuth(Map<String, Object> jsonObject) {
        String merchantId = String.valueOf(jsonObject.get(SquarePaymentMethodEnum.KEY_MERCHANT));
        List<BusinessSquare> businessSquares = squareMapper.selectByMerchantId(merchantId);
        if (CollectionUtils.isEmpty(businessSquares)) {
            log.warn("this merchant[{}] has been revoke sync from disconnect", merchantId);
            return;
        }
        for (BusinessSquare businessSquare : businessSquares) {
            try {
                // Use location api to confirm authorization has been revoked. MUST DO!
                LocationsApi locationsApi =
                        createClient(getValidAccessToken(businessSquare)).getLocationsApi();
                locationsApi.listLocations();
                log.warn(
                        "Business Square authorization is active(malicious event received), businessId: {}, merchantId: {}",
                        businessSquare.getBusinessId(),
                        businessSquare.getMerchantId());
            } catch (ApiException | IOException e) {
                log.info(
                        "Can not retrieving location while processing oauth.authorization.revoked notification. {}",
                        getMessage(e),
                        e);
                disconnectFromSquare(businessSquare.getBusinessId(), businessSquare.getId());
                log.info(
                        "Business Square authorization deleted, businessId: {}, merchantId: {}",
                        businessSquare.getBusinessId(),
                        businessSquare.getMerchantId());
            }
        }
    }

    public void syncProcessingFee() {
        /**
         * 1. 针对square
         * 2. 获取所有fee未处理的payment记录
         * 3. 逐一处理每条payment的fee
         */
        List<Integer> businesses = paymentMapper.selectFeeOfAllBusiness(METHOD_NAME_SQUARE);
        for (Integer businessId : businesses) {
            List<Payment> noFeePayment = paymentMapper.selectPaymentsForProcessingFee(businessId, METHOD_NAME_SQUARE);
            BusinessSquare businessSquare = squareMapper.selectByBusinessId(businessId);
            if (businessSquare == null || businessSquare.getMerchantId() == null) {
                log.warn("invalid square account of {}", businessId);
                continue;
            }
            for (Payment payment : noFeePayment) {
                Payment toUpdateFee = new Payment();
                toUpdateFee.setId(payment.getId());
                Long squareFee = 0L;
                try {
                    PaymentsApi paymentsApi =
                            createClient(getValidAccessToken(businessSquare)).getPaymentsApi();
                    GetPaymentResponse squareResponse = paymentsApi.getPayment(payment.getStripeChargeId());
                    List<ProcessingFee> fees = squareResponse.getPayment().getProcessingFee();
                    if (fees != null) {
                        for (ProcessingFee fee : fees) {
                            squareFee += fee.getAmountMoney().getAmount();
                        }
                        toUpdateFee.setProcessingFee(toDecimalAmount(
                                squareFee, fees.get(0).getAmountMoney().getCurrency()));
                    } else {
                        toUpdateFee.setProcessingFee(PaymentUtil.getAmountFromUnit(squareFee));
                    }
                } catch (Exception e) {
                    log.error("Failed to get payment[{}] from Square {}", payment.getId(), e.getMessage());
                    toUpdateFee.setProcessingFee(PaymentUtil.getAmountFromUnit(squareFee));
                }
                log.info(
                        "get square fee {} from  payment< {} > for biz {}",
                        squareFee,
                        payment.getStripeChargeId(),
                        payment.getBusinessId());
                paymentMapper.updateByPrimaryKeySelective(toUpdateFee);
            }
        }
    }

    // ################### Private common utility methods #####################
    private void throwResponseError(Error error) {
        if ("AUTHENTICATION_ERROR".equals(error.getCategory())) {
            throw new CommonException(
                    ResponseCodeEnum.UNAUTHORIZED_ERROR, error.getDetail() + ", for " + error.getField());
        } else if ("INVALID_REQUEST_ERROR".equals(error.getCategory())
                || "RATE_LIMIT_ERROR".equals(error.getCategory())
                || "PAYMENT_METHOD_ERROR".equals(error.getCategory())) {
            throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, error.getDetail() + ", for " + error.getField());
        } else {
            throw new CommonException(ResponseCodeEnum.SERVER_ERROR, error.getDetail() + ", for " + error.getField());
        }
    }

    private void throwHumanReadableResponseError(String message, List<Error> errors) {
        StringBuffer finalMsg = new StringBuffer(message);
        Error error = errors.get(0);
        errors.forEach(err -> {
            log.info("error message: {}", err.toString());
            finalMsg.append(System.lineSeparator())
                    .append(err.getCategory())
                    .append(": ")
                    .append(err.getDetail());
        });
        if ("AUTHENTICATION_ERROR".equals(error.getCategory())
                || "INVALID_REQUEST_ERROR".equals(error.getCategory())
                || "RATE_LIMIT_ERROR".equals(error.getCategory())
                || "PAYMENT_METHOD_ERROR".equals(error.getCategory())) {
            throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, finalMsg);
        } else {
            throw new CommonException(ResponseCodeEnum.SERVER_ERROR, finalMsg);
        }
    }

    private String getErrorMessageForSquare(String errorMessage, Exception e) {
        String responseBody = e.getMessage();
        if (e instanceof ApiException) {
            HttpStringResponse response =
                    (HttpStringResponse) ((ApiException) e).getHttpContext().getResponse();
            responseBody = response.getBody();
        }
        return errorMessage + responseBody;
    }

    private void throwRequestFailure(String errorMessage, Exception e) {
        String message = e.getMessage();
        ResponseCodeEnum errorCode = ResponseCodeEnum.SERVER_ERROR;
        String responseBody = message;
        if (e instanceof ApiException) {
            ApiException errorResp = (ApiException) e;
            if (!CollectionUtils.isEmpty(errorResp.getErrors())) {
                throwHumanReadableResponseError(errorMessage, errorResp.getErrors());
            }
            HttpStringResponse response =
                    (HttpStringResponse) ((ApiException) e).getHttpContext().getResponse();
            message = response.toString();
            if (response.getStatusCode() < 500) {
                errorCode = ResponseCodeEnum.PARAMS_ERROR;
            }
            responseBody = response.getBody();
        }
        log.error("call square api failed: {}", message);

        throw new CommonException(errorCode, errorMessage + responseBody, e);
    }

    private String getMessage(Exception e) {
        String message = e.getMessage();
        if (e instanceof ApiException) {
            message = ((ApiException) e).getHttpContext().getResponse().toString();
        }
        return message;
    }

    private SquareClient createClient(String accessToken) {
        return new SquareClient.Builder()
                .environment(Environment.fromString(squareEnv))
                .accessToken(accessToken)
                .build();
    }

    /**
     * The smallest denomination(unit) refer to https://en.wikipedia.org/wiki/ISO_4217
     * Except JPY has smallest unit of 1 yen, other currencies we support (USD, AUD, CAD, SGD, EUR, GBP)
     * has a smallest unit of 1 cent (1/100 dollar)
     */
    public BigDecimal toDecimalAmount(long amount, String currency) {
        if ("JPY".equals(currency)) {
            return BigDecimal.valueOf(amount);
        }
        return BigDecimal.valueOf(amount).divide(BigDecimal.valueOf(100L), 2, BigDecimal.ROUND_UP);
    }

    long toUnitAmount(BigDecimal amount, String currency) {
        if ("JPY".equals(currency)) {
            return amount.longValue();
        }

        return amount.multiply(BigDecimal.valueOf(100L)).longValue();
    }

    private String getValidAccessToken(BusinessSquare businessSquare) {
        // 检查 expiresAt是否过期
        if (DateUtil.isSquareExpiresBeforeNow(businessSquare.getExpiresAt())) {
            ObtainTokenResponse tokenResponse = refreshToken(businessSquare);
            return tokenResponse.getAccessToken();
        }
        return decode(businessSquare.getAccessToken());
    }

    private String decode(String token) {
        return AESUtil.decode(aesKey, token);
    }

    private String encode(String token) {
        return AESUtil.encode(aesKey, token);
    }

    public void syncTips(Integer paymentId) {
        List<Payment> payments = new ArrayList<>();
        if (paymentId != null) {
            // for test single
            payments.add(paymentMapper.selectByPrimaryKey(paymentId));
        } else {
            // time range UTC: 2024-1-18:12:00 - 2024-1-19:3:00  sum: 15hours
            long start = LocalDateTime.of(2024, 1, 18, 12, 0, 0)
                    .atZone(ZoneId.of("UTC"))
                    .toInstant()
                    .toEpochMilli();
            long end = LocalDateTime.of(2024, 1, 19, 3, 0, 0)
                    .atZone(ZoneId.of("UTC"))
                    .toInstant()
                    .toEpochMilli();
            start = start / 1000;
            end = end / 1000;
            payments = paymentMapper.selectErrorTipsByTime(start, end);
        }
        sync(payments);
    }

    private void sync(List<Payment> payments) {
        log.info("sync tips size:{}", payments.size());
        List<Integer> errorIds = new ArrayList<>();
        List<Integer> successIds = new ArrayList<>();
        List<Integer> notmatch = new ArrayList<>();
        for (int i = 0; i < payments.size(); i++) {
            Payment payment = payments.get(i);
            try {
                BusinessSquare businessSquare = getBusinessSquareWithException(payment.getBusinessId());
                PaymentsApi paymentsApi =
                        createClient(getValidAccessToken(businessSquare)).getPaymentsApi();
                GetPaymentResponse squareResponse = paymentsApi.getPayment(payment.getStripeChargeId());
                if (!CollectionUtils.isEmpty(squareResponse.getErrors())) {
                    errorIds.add(payment.getId());
                    continue;
                }
                Money tipsMoney = squareResponse.getPayment().getTipMoney();
                if (tipsMoney != null && tipsMoney.getAmount() != null && tipsMoney.getAmount() > 0) {
                    BigDecimal squareTips = toDecimalAmount(tipsMoney.getAmount(), tipsMoney.getCurrency());
                    OrderModel order = orderClient.getOrder(GetOrderRequest.newBuilder()
                            .setId(payment.getInvoiceId())
                            .setBusinessId(payment.getBusinessId())
                            .build());
                    BigDecimal subtract = BigDecimal.valueOf(order.getPaidAmount())
                            .subtract(BigDecimal.valueOf(order.getRefundedAmount()))
                            .subtract(BigDecimal.valueOf(order.getTotalAmount()));
                    if (subtract.compareTo(squareTips) == 0) {
                        SetTipsRequest tipsParam = SetTipsRequest.newBuilder()
                                .setBusinessId(payment.getBusinessId())
                                .setInvoiceId(payment.getInvoiceId())
                                .setOmitResult(true)
                                .setValue(squareTips.doubleValue())
                                .setValueType(InvoiceValueType.AMOUNT.value())
                                .build();
                        orderClient.setTips(tipsParam);
                        Payment p = new Payment();
                        p.setId(payment.getId());
                        p.setTips(squareTips);
                        paymentMapper.updateByPrimaryKeySelective(p);
                        log.info("sync update tips[{}] for square: payId {}", squareTips, payment.getId());
                        successIds.add(payment.getId());
                    }
                } else {
                    notmatch.add(payment.getId());
                    log.info("sync not match: payId {}", payment.getId());
                    continue;
                }
            } catch (Exception e) {
                log.error("sync update square tips failed:{}", payment.getId(), e);
                errorIds.add(payment.getId());
            }
            log.info("sync tips progress: {}/{}", i + 1, payments.size());
        }
        log.info("sync tips success size:{}", successIds.size());
        log.info(
                "sync success payment ids:{}",
                successIds.stream().map(String::valueOf).collect(Collectors.joining(",")));
        log.info(
                "sync error payment ids:{}",
                errorIds.stream().map(String::valueOf).collect(Collectors.joining(",")));
        log.info(
                "not match payment ids:{}",
                notmatch.stream().map(String::valueOf).collect(Collectors.joining(",")));
    }
}
