package com.moego.server.payment.web.dto;

import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.validator.constraints.URL;

/**
 * <AUTHOR>
 * @since 2023/10/27
 */
@Getter
@Setter
@NoArgsConstructor
public class UpdatePlatformCareRecordDTO {
    // signature
    @URL
    @NotNull
    private String signature;
    // customer id
    @Min(0)
    @NotNull
    private Long businessId;
    // email
    @Email
    @NotNull
    private String email;
    // account id
    @Min(0)
    @NotNull
    private Long accountId;
    // agreement id
    @Min(0)
    @NotNull
    private Long agreementId;
    // company id
    @Min(0)
    @NotNull
    private Long companyId;
}
