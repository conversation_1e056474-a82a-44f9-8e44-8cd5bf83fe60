package com.moego.server.payment.util;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.stripe.model.StripeObject;
import java.io.IOException;
import java.lang.reflect.Field;

public class StripeObjectSerializer extends JsonSerializer<StripeObject> {

    private static Gson createGson() {
        GsonBuilder builder = StripeObject.PRETTY_PRINT_GSON.newBuilder();
        try {
            // trick: force do not pretty print
            Field field = GsonBuilder.class.getDeclaredField("prettyPrinting");
            field.setAccessible(true);
            field.set(builder, false);
        } catch (Exception ignored) {
        }
        return builder.create();
    }

    public static final Gson GSON = createGson();

    @Override
    public void serialize(StripeObject stripeObject, JsonGenerator jsonGenerator, SerializerProvider serializerProvider)
            throws IOException, JsonProcessingException {
        jsonGenerator.writeRawValue(GSON.toJson(stripeObject));
    }
}
