<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.moego.server.payment.mapper.MoeIdentitySessionMapper">
  <resultMap id="BaseResultMap" type="com.moego.server.payment.mapperbean.MoeIdentitySession">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="account_id" jdbcType="INTEGER" property="accountId" />
    <result column="business_id" jdbcType="INTEGER" property="businessId" />
    <result column="staff_id" jdbcType="INTEGER" property="staffId" />
    <result column="verification_session_id" jdbcType="VARCHAR" property="verificationSessionId" />
    <result column="session_url" jdbcType="VARCHAR" property="sessionUrl" />
    <result column="session_status" jdbcType="VARCHAR" property="sessionStatus" />
    <result column="expire_time" jdbcType="BIGINT" property="expireTime" />
    <result column="last_event_time" jdbcType="BIGINT" property="lastEventTime" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="update_time" jdbcType="BIGINT" property="updateTime" />
    <result column="company_id" jdbcType="BIGINT" property="companyId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, account_id, business_id, staff_id, verification_session_id, session_url, session_status, 
    expire_time, last_event_time, create_time, update_time, company_id
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from moe_identity_session
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from moe_identity_session
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.moego.server.payment.mapperbean.MoeIdentitySession">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into moe_identity_session (account_id, business_id, staff_id, 
      verification_session_id, session_url, session_status, 
      expire_time, last_event_time, create_time, 
      update_time, company_id)
    values (#{accountId,jdbcType=INTEGER}, #{businessId,jdbcType=INTEGER}, #{staffId,jdbcType=INTEGER}, 
      #{verificationSessionId,jdbcType=VARCHAR}, #{sessionUrl,jdbcType=VARCHAR}, #{sessionStatus,jdbcType=VARCHAR}, 
      #{expireTime,jdbcType=BIGINT}, #{lastEventTime,jdbcType=BIGINT}, #{createTime,jdbcType=BIGINT}, 
      #{updateTime,jdbcType=BIGINT}, #{companyId,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" parameterType="com.moego.server.payment.mapperbean.MoeIdentitySession">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into moe_identity_session
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="accountId != null">
        account_id,
      </if>
      <if test="businessId != null">
        business_id,
      </if>
      <if test="staffId != null">
        staff_id,
      </if>
      <if test="verificationSessionId != null">
        verification_session_id,
      </if>
      <if test="sessionUrl != null">
        session_url,
      </if>
      <if test="sessionStatus != null">
        session_status,
      </if>
      <if test="expireTime != null">
        expire_time,
      </if>
      <if test="lastEventTime != null">
        last_event_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="companyId != null">
        company_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="accountId != null">
        #{accountId,jdbcType=INTEGER},
      </if>
      <if test="businessId != null">
        #{businessId,jdbcType=INTEGER},
      </if>
      <if test="staffId != null">
        #{staffId,jdbcType=INTEGER},
      </if>
      <if test="verificationSessionId != null">
        #{verificationSessionId,jdbcType=VARCHAR},
      </if>
      <if test="sessionUrl != null">
        #{sessionUrl,jdbcType=VARCHAR},
      </if>
      <if test="sessionStatus != null">
        #{sessionStatus,jdbcType=VARCHAR},
      </if>
      <if test="expireTime != null">
        #{expireTime,jdbcType=BIGINT},
      </if>
      <if test="lastEventTime != null">
        #{lastEventTime,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=BIGINT},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.moego.server.payment.mapperbean.MoeIdentitySession">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_identity_session
    <set>
      <if test="accountId != null">
        account_id = #{accountId,jdbcType=INTEGER},
      </if>
      <if test="businessId != null">
        business_id = #{businessId,jdbcType=INTEGER},
      </if>
      <if test="staffId != null">
        staff_id = #{staffId,jdbcType=INTEGER},
      </if>
      <if test="verificationSessionId != null">
        verification_session_id = #{verificationSessionId,jdbcType=VARCHAR},
      </if>
      <if test="sessionUrl != null">
        session_url = #{sessionUrl,jdbcType=VARCHAR},
      </if>
      <if test="sessionStatus != null">
        session_status = #{sessionStatus,jdbcType=VARCHAR},
      </if>
      <if test="expireTime != null">
        expire_time = #{expireTime,jdbcType=BIGINT},
      </if>
      <if test="lastEventTime != null">
        last_event_time = #{lastEventTime,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=BIGINT},
      </if>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.moego.server.payment.mapperbean.MoeIdentitySession">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_identity_session
    set account_id = #{accountId,jdbcType=INTEGER},
      business_id = #{businessId,jdbcType=INTEGER},
      staff_id = #{staffId,jdbcType=INTEGER},
      verification_session_id = #{verificationSessionId,jdbcType=VARCHAR},
      session_url = #{sessionUrl,jdbcType=VARCHAR},
      session_status = #{sessionStatus,jdbcType=VARCHAR},
      expire_time = #{expireTime,jdbcType=BIGINT},
      last_event_time = #{lastEventTime,jdbcType=BIGINT},
      create_time = #{createTime,jdbcType=BIGINT},
      update_time = #{updateTime,jdbcType=BIGINT},
      company_id = #{companyId,jdbcType=BIGINT}
    where id = #{id,jdbcType=INTEGER}
  </update>
    <select id="selectOwnerIdentity" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from moe_identity_session
        where staff_id = #{ownerStaffId,jdbcType=INTEGER}
        and business_id = #{businessId,jdbcType=INTEGER}
        order by id desc limit 1
    </select>
    <select id="selectByVsId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from moe_identity_session
        where verification_session_id = #{vsId,jdbcType=VARCHAR}
        order by id desc limit 1
    </select>
</mapper>