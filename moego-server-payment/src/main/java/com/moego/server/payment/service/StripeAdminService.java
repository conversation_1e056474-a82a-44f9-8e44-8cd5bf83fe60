package com.moego.server.payment.service;

import static com.moego.common.enums.StripeApi.LIST_DEFAULT_LIMIT;

import com.moego.common.enums.StripeApi;
import com.moego.common.utils.DateUtil;
import com.moego.idl.models.errors.v1.Code;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.lib.common.thread.ThreadPool;
import com.moego.lib.common.util.FileUtils;
import com.moego.lib.common.util.JsonUtil;
import com.moego.server.payment.dto.StripeBalanceTransaction;
import com.moego.server.payment.dto.StripeCouponInfo;
import com.moego.server.payment.dto.StripeCustomerInfo;
import com.moego.server.payment.dto.StripeDisputeInfo;
import com.moego.server.payment.dto.StripeInvoiceInfo;
import com.moego.server.payment.dto.StripeSubscriptionInfo;
import com.moego.server.payment.mapper.MoeCouponMapper;
import com.moego.server.payment.mapperbean.MoeCoupon;
import com.moego.server.payment.params.CouponParams;
import com.moego.server.payment.params.StripeChargeDisputeParams;
import com.stripe.exception.StripeException;
import com.stripe.model.Coupon;
import com.stripe.model.Customer;
import com.stripe.model.CustomerBalanceTransaction;
import com.stripe.model.Discount;
import com.stripe.model.Dispute;
import com.stripe.model.File;
import com.stripe.model.Invoice;
import com.stripe.model.Subscription;
import com.stripe.param.CouponCreateParams;
import com.stripe.param.CustomerBalanceTransactionCollectionCreateParams;
import com.stripe.param.CustomerBalanceTransactionCollectionListParams;
import com.stripe.param.CustomerRetrieveParams;
import com.stripe.param.DisputeUpdateParams;
import com.stripe.param.FileCreateParams;
import com.stripe.param.InvoiceUpcomingParams;
import com.stripe.param.SubscriptionRetrieveParams;
import com.stripe.param.SubscriptionUpdateParams;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

/**
 * <AUTHOR>
 * @since 2023/11/23
 */
@Service
@Slf4j
public class StripeAdminService {
    private static final Long COUPON_DEFAULT_MAX_REDEMPTIONS = 500L;

    @Autowired
    private MoeCouponMapper moeCouponMapper;

    public String uploadFileByMultipartFile(MultipartFile file) {
        try {
            // https://stripe.com/docs/api/files/create
            FileCreateParams params = FileCreateParams.builder()
                    .setFile(FileUtils.convertMultiPartToFile(file))
                    .setPurpose(FileCreateParams.Purpose.DISPUTE_EVIDENCE)
                    .build();
            File upload = File.create(params);
            return upload.getId();
        } catch (StripeException | IOException e) {
            throw ExceptionUtil.bizException(
                    Code.CODE_PARAMS_ERROR, String.format("update load file failed: %s", e.getMessage()));
        }
    }

    public String uploadFileByInputStream(InputStream file) {
        try {
            // https://stripe.com/docs/api/files/create
            FileCreateParams params = FileCreateParams.builder()
                    .setFile(file)
                    .setPurpose(FileCreateParams.Purpose.DISPUTE_EVIDENCE)
                    .build();
            File upload = File.create(params);
            return upload.getId();
        } catch (StripeException e) {
            throw ExceptionUtil.bizException(
                    Code.CODE_PARAMS_ERROR, String.format("update load file failed: %s", e.getMessage()));
        }
    }

    private StripeDisputeInfo buildDisputeInfo(Dispute dispute) {
        return StripeDisputeInfo.builder()
                .id(dispute.getId())
                .amount(dispute.getAmount())
                .chargeId(dispute.getCharge())
                .currency(dispute.getCurrency())
                .metadata(JsonUtil.toJson(dispute.getMetadata()))
                .reason(dispute.getReason())
                .status(dispute.getStatus())
                .created(dispute.getCreated())
                .evidenceDetails(StripeDisputeInfo.EvidenceDetails.builder()
                        .dueBy(dispute.getEvidenceDetails().getDueBy())
                        .hasEvidence(dispute.getEvidenceDetails().getHasEvidence())
                        .pastDue(dispute.getEvidenceDetails().getPastDue())
                        .submissionCount(dispute.getEvidenceDetails().getSubmissionCount())
                        .build())
                .evidence(StripeDisputeInfo.Evidence.builder()
                        .accessActivityLog(dispute.getEvidence().getAccessActivityLog())
                        .billingAddress(dispute.getEvidence().getBillingAddress())
                        .cancellationPolicy(dispute.getEvidence().getCancellationPolicy())
                        .cancellationPolicyDisclosure(dispute.getEvidence().getCancellationPolicyDisclosure())
                        .cancellationRebuttal(dispute.getEvidence().getCancellationRebuttal())
                        .customerCommunication(dispute.getEvidence().getCustomerCommunication())
                        .customerEmailAddress(dispute.getEvidence().getCustomerEmailAddress())
                        .customerName(dispute.getEvidence().getCustomerName())
                        .customerPurchaseIp(dispute.getEvidence().getCustomerPurchaseIp())
                        .customerSignature(dispute.getEvidence().getCustomerSignature())
                        .duplicateChargeDocumentation(dispute.getEvidence().getDuplicateChargeDocumentation())
                        .duplicateChargeExplanation(dispute.getEvidence().getDuplicateChargeExplanation())
                        .duplicateChargeId(dispute.getEvidence().getDuplicateChargeId())
                        .productDescription(dispute.getEvidence().getProductDescription())
                        .receipt(dispute.getEvidence().getReceipt())
                        .refundPolicy(dispute.getEvidence().getRefundPolicy())
                        .refundPolicyDisclosure(dispute.getEvidence().getRefundPolicyDisclosure())
                        .refundRefusalExplanation(dispute.getEvidence().getRefundRefusalExplanation())
                        .serviceDate(dispute.getEvidence().getServiceDate())
                        .serviceDocumentation(dispute.getEvidence().getServiceDocumentation())
                        .shippingAddress(dispute.getEvidence().getShippingAddress())
                        .shippingDate(dispute.getEvidence().getShippingDate())
                        .shippingCarrier(dispute.getEvidence().getShippingCarrier())
                        .shippingDocumentation(dispute.getEvidence().getShippingDocumentation())
                        .shippingTrackingNumber(dispute.getEvidence().getShippingTrackingNumber())
                        .uncategorizedFile(dispute.getEvidence().getUncategorizedFile())
                        .uncategorizedText(dispute.getEvidence().getUncategorizedText())
                        .build())
                .build();
    }

    public StripeDisputeInfo getDisputeDetail(String disputeId) {
        try {
            Dispute resource = Dispute.retrieve(disputeId);
            return buildDisputeInfo(resource);
        } catch (StripeException e) {
            throw ExceptionUtil.bizException(
                    Code.CODE_PARAMS_ERROR, String.format("retrieve dispute failed: %s", e.getMessage()));
        }
    }

    public StripeDisputeInfo updateLoadEvidence(String disputeId, StripeChargeDisputeParams evidenceParams) {
        try {
            Dispute resource = Dispute.retrieve(disputeId);
            DisputeUpdateParams params = DisputeUpdateParams.builder()
                    //                    .putAllExtraParam(evidenceParams.getExtra())
                    .setEvidence(DisputeUpdateParams.Evidence.builder()
                            .setAccessActivityLog(evidenceParams.getAccessActivityLog())
                            .setBillingAddress(evidenceParams.getBillingAddress())
                            .setCancellationPolicy(evidenceParams.getCancellationPolicy())
                            .setCancellationPolicyDisclosure(evidenceParams.getCancellationPolicyDisclosure())
                            .setCancellationRebuttal(evidenceParams.getCancellationRebuttal())
                            .setCustomerCommunication(evidenceParams.getCustomerCommunication())
                            .setCustomerEmailAddress(evidenceParams.getCustomerEmailAddress())
                            .setCustomerName(evidenceParams.getCustomerName())
                            .setCustomerPurchaseIp(evidenceParams.getCustomerPurchaseIp())
                            .setCustomerSignature(evidenceParams.getCustomerSignature())
                            .setDuplicateChargeDocumentation(evidenceParams.getDuplicateChargeDocumentation())
                            .setDuplicateChargeExplanation(evidenceParams.getDuplicateChargeExplanation())
                            .setDuplicateChargeId(evidenceParams.getDuplicateChargeId())
                            //                            .putAllExtraParam(evidenceParams.getEvidenceExtra())
                            .setProductDescription(evidenceParams.getProductDescription())
                            .setReceipt(evidenceParams.getReceipt())
                            .setRefundPolicy(evidenceParams.getRefundPolicy())
                            .setRefundPolicyDisclosure(evidenceParams.getRefundPolicyDisclosure())
                            .setRefundRefusalExplanation(evidenceParams.getRefundRefusalExplanation())
                            .setServiceDate(evidenceParams.getServiceDate())
                            .setServiceDocumentation(evidenceParams.getServiceDocumentation())
                            .setUncategorizedFile(evidenceParams.getUncategorizedFile())
                            .setUncategorizedText(evidenceParams.getUncategorizedText())
                            .setShippingAddress(evidenceParams.getShippingAddress())
                            .setShippingDate(evidenceParams.getShippingDate())
                            .setShippingCarrier(evidenceParams.getShippingCarrier())
                            .setShippingDocumentation(evidenceParams.getShippingDocumentation())
                            .setShippingTrackingNumber(evidenceParams.getShippingTrackingNumber())
                            .build())
                    .putMetadata("Credit Voucher", evidenceParams.getCreditVoucher())
                    .putMetadata("Goverment Order", evidenceParams.getGovermentOrder())
                    .putMetadata("Terms Disclosure", evidenceParams.getTermsDisclosure())
                    .build();
            Dispute dispute = resource.update(params);
            return buildDisputeInfo(dispute);
        } catch (StripeException e) {
            throw ExceptionUtil.bizException(
                    Code.CODE_PARAMS_ERROR, String.format("update load evidence failed: %s", e.getMessage()));
        }
    }

    public Coupon createNewCoupon(CouponParams couponParams) {

        // 如果percentoff和amountoff都为空，说明前段传参有问题，这里调用stripe接口不能同时为空，所以这里判断出现这种情况直接返回null
        if (couponParams.getPercentOff() == null && couponParams.getAmountOff() == null) {
            throw ExceptionUtil.bizException(
                    Code.CODE_PARAMS_ERROR, "PercentOff and AmountOff params are all null,please input the one of it");
        }

        try {
            CouponCreateParams.Builder builder = new CouponCreateParams.Builder()
                    .setName(couponParams.getCustomName())
                    .setPercentOff(Optional.ofNullable(couponParams.getPercentOff())
                            .map(BigDecimal::valueOf)
                            .orElse(null));
            if (couponParams.getAmountOff() != null) {
                builder.setCurrency("USD");
                builder.setAmountOff(couponParams.getAmountOff().longValue());
            }

            // 这里和stripe保持一致，支持三种duration模式
            switch (couponParams.getDuration()) {
                case "repeating":
                    builder.setDuration(CouponCreateParams.Duration.REPEATING)
                            .setDurationInMonths(couponParams.getValidMonth());
                    break;
                case "once":
                    builder.setDuration(CouponCreateParams.Duration.ONCE);
                    break;
                default:
                    builder.setDuration(CouponCreateParams.Duration.FOREVER);
                    break;
            }

            Optional.ofNullable(couponParams.getRedeemBy()).ifPresent(builder::setRedeemBy);
            Optional.ofNullable(couponParams.getMaxRedemptions()).ifPresent(builder::setMaxRedemptions);

            return Coupon.create(builder.build());
        } catch (StripeException e) {
            log.error("stripe create coupon error: ", e);
            throw ExceptionUtil.bizException(
                    Code.CODE_PARAMS_ERROR,
                    String.format(
                            "create stripe coupon failed (custom name=%s, percent off=%d): %s,%s",
                            couponParams.getCustomName(), couponParams.getPercentOff(), e.getMessage(), couponParams));
        }
    }

    /**
     * 更新或者删除当前subscription 关联的coupon
     *
     * @param couponId       设置couponId为null, 表示删除;  新传入的couponId会覆盖旧的coupon(如果已有coupon)
     * @param subscriptionId 要修改的套餐Id
     * @return 被修改的套餐Id
     */
    public String updateSubscriptionCoupon(String couponId, String subscriptionId) {
        try {
            Subscription subscription = Subscription.retrieve(subscriptionId);
            subscription.update(
                    SubscriptionUpdateParams.builder().setCoupon(couponId).build());
            return subscriptionId;
        } catch (StripeException e) {
            throw ExceptionUtil.bizException(
                    Code.CODE_PARAMS_ERROR,
                    String.format(
                            "update stripe subscription failed (subscriptionId=%s, couponId=%s): %s",
                            subscriptionId, couponId, e.getMessage()));
        } finally {
            // sync coupon time redeemed to moe_coupon
            ThreadPool.execute(() -> {
                try {
                    MoeCoupon existingCoupon = moeCouponMapper.selectByStripeCouponId(couponId);
                    if (existingCoupon == null) {
                        log.error("not found coupon id {}", couponId);
                        return;
                    }
                    Coupon coupon = Coupon.retrieve(couponId);
                    MoeCoupon toUpdate = new MoeCoupon();
                    toUpdate.setId(existingCoupon.getId());
                    toUpdate.setTimesRedeemed(coupon.getTimesRedeemed().intValue());
                    toUpdate.setUpdatedTime(DateUtil.get10Timestamp());
                    moeCouponMapper.updateByPrimaryKeySelective(toUpdate);
                } catch (StripeException e) {
                    log.error("invalid coupon id {}", couponId, e);
                }
            });
        }
    }

    public StripeSubscriptionInfo getSubscription(String subscriptionId) {
        try {
            SubscriptionRetrieveParams params = SubscriptionRetrieveParams.builder()
                    .addExpand(StripeApi.EXPAND_LATEST_INVOICE)
                    .build();
            Subscription subscription = Subscription.retrieve(subscriptionId, params, null);
            StripeSubscriptionInfo result = StripeSubscriptionInfo.builder()
                    .cancelAtPeriodEnd(subscription.getCancelAtPeriodEnd())
                    .currentPeriodEnd(subscription.getCurrentPeriodEnd())
                    .currentPeriodStart(subscription.getCurrentPeriodStart())
                    .startDate(subscription.getStartDate())
                    .status(subscription.getStatus())
                    .metadata(JsonUtil.toJson(subscription.getMetadata()))
                    .created(subscription.getCreated())
                    .description(subscription.getDescription())
                    .id(subscriptionId)
                    .build();
            // format invoice summary
            if (subscription.getLatestInvoice() != null) {
                Invoice latestInvoice = subscription.getLatestInvoiceObject();
                result.setLatestInvoice(StripeInvoiceInfo.builder()
                        .amountDue(latestInvoice.getAmountDue())
                        .created(latestInvoice.getCreated())
                        .periodStart(latestInvoice.getPeriodStart())
                        .accountCountry(latestInvoice.getAccountCountry())
                        .hostedInvoiceUrl(latestInvoice.getHostedInvoiceUrl())
                        .billingReason(latestInvoice.getBillingReason())
                        .total(latestInvoice.getTotal())
                        .description(latestInvoice.getDescription())
                        .paymentIntent(latestInvoice.getPaymentIntent())
                        .build());
            }
            if (subscription.getDiscount() != null) {
                Discount discount = subscription.getDiscount();
                Coupon stripeCoupon = discount.getCoupon();

                StripeCouponInfo stripeCouponInfo = StripeCouponInfo.builder()
                        .id(stripeCoupon.getId())
                        .name(stripeCoupon.getName())
                        .created(stripeCoupon.getCreated())
                        .duration(stripeCoupon.getDuration())
                        .durationInMonths(stripeCoupon.getDurationInMonths())
                        .livemode(stripeCoupon.getLivemode())
                        .maxRedemptions(stripeCoupon.getMaxRedemptions())
                        .metadata(JsonUtil.toJson(stripeCoupon.getMetadata()))
                        .percentOff(stripeCoupon.getPercentOff())
                        .redeemBy(stripeCoupon.getRedeemBy())
                        .timesRedeemed(stripeCoupon.getTimesRedeemed())
                        .valid(stripeCoupon.getValid())
                        .start(discount.getStart())
                        .end(discount.getEnd())
                        .build();
                result.setDiscountInfo(stripeCouponInfo);
            }
            InvoiceUpcomingParams updateComingParam = InvoiceUpcomingParams.builder()
                    .setCustomer(subscription.getCustomer())
                    .setSubscription(subscriptionId)
                    .build();
            Invoice upcomingInvoice = Invoice.upcoming(updateComingParam);
            if (upcomingInvoice != null) {
                result.setUpcomingInvoice(StripeInvoiceInfo.builder()
                        .amountDue(upcomingInvoice.getAmountDue())
                        .created(upcomingInvoice.getCreated())
                        .periodStart(upcomingInvoice.getPeriodStart())
                        .periodEnd(upcomingInvoice.getPeriodEnd())
                        .accountCountry(upcomingInvoice.getAccountCountry())
                        .hostedInvoiceUrl(upcomingInvoice.getHostedInvoiceUrl())
                        .billingReason(upcomingInvoice.getBillingReason())
                        .total(upcomingInvoice.getTotal())
                        .description(upcomingInvoice.getDescription())
                        .paymentIntent(upcomingInvoice.getPaymentIntent())
                        .build());
            }
            return result;
        } catch (StripeException e) {
            throw ExceptionUtil.bizException(
                    Code.CODE_PARAMS_ERROR,
                    String.format("get stripe customer info %s failed: %s", subscriptionId, e.getMessage()));
        }
    }

    /**
     * @param stripeCustomerId 操作对象
     * @param amount           单位为美分, 给credit $100, 传入 -10000,   给 debit $1, 传入 10000
     * @param note             internal note(附加描述)
     * @return 返回balance transaction id
     */
    public String changeCustomerBalance(String stripeCustomerId, Long amount, String note) {
        try {
            Customer stripecustomer = Customer.retrieve(stripeCustomerId);
            CustomerBalanceTransaction result = stripecustomer
                    .balanceTransactions()
                    .create(
                            CustomerBalanceTransactionCollectionCreateParams.builder()
                                    .setAmount(amount)
                                    .setCurrency(stripecustomer.getCurrency())
                                    .setDescription(note)
                                    .build(),
                            null);
            log.info(
                    "apply {}{} to stripe customer balance success (id = [{}])",
                    stripecustomer.getCurrency(),
                    result.getAmount(),
                    stripeCustomerId);
            return result.getId();
        } catch (StripeException e) {
            throw ExceptionUtil.bizException(
                    Code.CODE_PARAMS_ERROR,
                    String.format(
                            "change stripe customer balance failed (id=%s) %s", stripeCustomerId, e.getMessage()));
        }
    }

    public StripeCustomerInfo getStripeCustomerInfo(String stripeCustomerId) {
        try {
            Customer stripecustomer = Customer.retrieve(
                    stripeCustomerId,
                    CustomerRetrieveParams.builder()
                            .addExpand(StripeApi.EXPAND_INVOICE_BALANCE)
                            .addExpand(StripeApi.EXPAND_DEFAULT_SOURCE)
                            .build(),
                    null);
            StripeCustomerInfo stripeCustomerInfo = StripeCustomerInfo.builder()
                    .customerId(stripeCustomerId)
                    .email(stripecustomer.getEmail())
                    .name(stripecustomer.getName())
                    .defaultSource(stripecustomer.getDefaultSource())
                    .invoiceBalance(stripecustomer.getBalance())
                    .created(stripecustomer.getCreated())
                    .meta(JsonUtil.toJson(stripecustomer.getMetadata()))
                    .description(stripecustomer.getDescription())
                    .build();
            List<CustomerBalanceTransaction> balanceTxn = stripecustomer
                    .balanceTransactions()
                    .list(CustomerBalanceTransactionCollectionListParams.builder()
                            .setLimit(LIST_DEFAULT_LIMIT)
                            .build())
                    .getData();
            if (CollectionUtils.isEmpty(balanceTxn)) {
                stripeCustomerInfo.setBalanceTransactions(Collections.emptyList());
            } else {
                stripeCustomerInfo.setBalanceTransactions(balanceTxn.stream()
                        .map(txn -> StripeBalanceTransaction.builder()
                                .id(txn.getId())
                                .amount(txn.getAmount())
                                .created(txn.getCreated())
                                .type(txn.getType())
                                .currency(txn.getCurrency())
                                .description(txn.getDescription())
                                .invoice(txn.getInvoice())
                                .endingBalance(txn.getEndingBalance())
                                .build())
                        .toList());
            }
            return stripeCustomerInfo;
        } catch (StripeException e) {
            throw ExceptionUtil.bizException(
                    Code.CODE_PARAMS_ERROR,
                    String.format("get stripe customer info %s failed: %s", stripeCustomerId, e.getMessage()));
        }
    }
}
