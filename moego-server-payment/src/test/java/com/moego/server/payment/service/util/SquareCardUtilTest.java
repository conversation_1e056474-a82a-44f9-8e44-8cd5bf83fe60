package com.moego.server.payment.service.util;

import static org.junit.jupiter.api.Assertions.assertEquals;

import org.junit.jupiter.api.Test;

/**
 * <AUTHOR>
 * @since 2021/10/8 3:19 PM
 */
public class SquareCardUtilTest {

    @Test
    public void getMoegoPaymentId() {
        Integer expectedId = 52593;
        String note = "MoeGo #105882-52593-4";
        Integer paymentId = SquareCardUtil.getMoegoPaymentId(note);
        assertEquals(expectedId, paymentId);
    }
}
