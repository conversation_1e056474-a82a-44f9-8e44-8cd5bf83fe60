package com.moego.server.payment.service;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.anyInt;
import static org.mockito.Mockito.when;

import com.moego.common.utils.payment.ProcessingFeeUtil;
import com.moego.server.business.client.IBusinessBusinessClient;
import com.moego.server.business.dto.CompanyDto;
import com.moego.server.payment.dto.CompanyProcessingFeeDTO;
import com.moego.server.payment.mapperbean.MoeCustomizedPaymentSetting;
import com.moego.server.payment.mapperbean.MoePaymentSetting;
import java.math.BigDecimal;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class PaymentSettingServiceTest {

    @Mock
    private IBusinessBusinessClient iBusinessBusinessClient;

    @Mock
    private ProcessingFeeService processingFeeService;

    @Mock
    private CustomizedPaymentSettingService customizedPaymentSettingService;

    @InjectMocks
    private PaymentSettingService paymentSettingService;

    @Test
    // 验证getCompanySetting方法：默认费率和定制费率
    void testGetDefaultCompanyProcessingFee() {
        when(customizedPaymentSettingService.getByCompanyId(anyInt())).thenReturn(null);
        // 检查cof默认费率为 3.4
        CompanyProcessingFeeDTO companyProcessingFeeDTO = paymentSettingService.getCompanySetting(1);
        assertThat(companyProcessingFeeDTO.getOnlineFeeRate().compareTo(BigDecimal.valueOf(3.4)))
                .isEqualTo(0);
    }

    @Test
    void testGetCustomizedCompanyProcessingFee() {
        MoeCustomizedPaymentSetting customizedPaymentSetting = new MoeCustomizedPaymentSetting();
        customizedPaymentSetting.setCompanyId(1);
        customizedPaymentSetting.setOnlineFeeRate(BigDecimal.valueOf(3.2));
        customizedPaymentSetting.setOnlineFeeCents(30);
        customizedPaymentSetting.setReaderFeeRate(BigDecimal.valueOf(2.8));
        customizedPaymentSetting.setReaderFeeCents(50);

        when(customizedPaymentSettingService.getByCompanyId(anyInt())).thenReturn(customizedPaymentSetting);

        // 检查cof定制费率为 3.2
        CompanyProcessingFeeDTO companyProcessingFeeDTO = paymentSettingService.getCompanySetting(1);
        assertThat(companyProcessingFeeDTO.getOnlineFeeRate().compareTo(BigDecimal.valueOf(3.2)))
                .isEqualTo(0);
    }

    // 验证初始化费率是否正常
    @Test
    void getInitialCustomizedProcessingFeeForOldPlan() {
        MoePaymentSetting paymentSetting = new MoePaymentSetting();
        paymentSetting.setBusinessId(10000);

        CompanyDto companyDto1 = new CompanyDto();
        companyDto1.setId(1);
        companyDto1.setLevel(9);
        when(iBusinessBusinessClient.getCompanyByBusinessId(any())).thenReturn(companyDto1);

        when(processingFeeService.getProcessingFeeForCompany(any(), anyInt()))
                .thenReturn(ProcessingFeeUtil.OLD_PLAN_FEE);

        // 获取初始化费率
        paymentSettingService.initializeCustomizedFee(paymentSetting);

        // 老费率：online: 3.4%+30 reader: 2.9%+50， paymentSetting的表里存的是百分比后的数字
        assertThat(paymentSetting.getOnlineFeeRate().compareTo(BigDecimal.valueOf(3.4)))
                .isEqualTo(0);
        assertThat(paymentSetting.getOnlineFeeCents() == 30).isTrue();
        assertThat(paymentSetting.getReaderFeeRate().compareTo(BigDecimal.valueOf(2.9)))
                .isEqualTo(0);
        assertThat(paymentSetting.getReaderFeeCents() == 50).isTrue();
    }

    @Test
    void getInitialCustomizedProcessingFeeForNewPlan() {
        MoePaymentSetting paymentSetting = new MoePaymentSetting();
        paymentSetting.setBusinessId(10000);

        // new pricing-tier1费率 = 老套餐费率 online: 3.4%+30 reader: 2.9%+50
        CompanyDto companyDto1 = new CompanyDto();
        companyDto1.setId(1);
        companyDto1.setLevel(1000);
        when(iBusinessBusinessClient.getCompanyByBusinessId(any())).thenReturn(companyDto1);
        when(processingFeeService.getProcessingFeeForCompany(any(), anyInt()))
                .thenReturn(ProcessingFeeUtil.OLD_PLAN_FEE);
        // 获取初始化费率
        paymentSettingService.initializeCustomizedFee(paymentSetting);
        assertThat(paymentSetting.getOnlineFeeRate().compareTo(BigDecimal.valueOf(3.4)))
                .isEqualTo(0);
        assertThat(paymentSetting.getOnlineFeeCents() == 30).isTrue();
        assertThat(paymentSetting.getReaderFeeRate().compareTo(BigDecimal.valueOf(2.9)))
                .isEqualTo(0);
        assertThat(paymentSetting.getReaderFeeCents() == 50).isTrue();

        // new pricing-tier2费率：online: 3.4%+30 reader: 2.75%+50
        CompanyDto companyDto2 = new CompanyDto();
        companyDto2.setId(1);
        companyDto2.setLevel(1100);
        when(iBusinessBusinessClient.getCompanyByBusinessId(any())).thenReturn(companyDto2);
        when(processingFeeService.getProcessingFeeForCompany(any(), anyInt()))
                .thenReturn(ProcessingFeeUtil.OLD_PLAN_FEE);
        paymentSettingService.initializeCustomizedFee(paymentSetting);
        assertThat(paymentSetting.getOnlineFeeRate().compareTo(BigDecimal.valueOf(3.4)))
                .isEqualTo(0);
        assertThat(paymentSetting.getOnlineFeeCents() == 30).isTrue();
        assertThat(paymentSetting
                        .getReaderFeeRate()
                        .compareTo(BigDecimal.valueOf(ProcessingFeeUtil.T2_PLAN_FEE
                                        .getTerminal()
                                        .getPercent())
                                .multiply(BigDecimal.valueOf(100))))
                .isEqualTo(0);
        assertThat(paymentSetting.getReaderFeeCents() == 50).isTrue();

        // new pricing-tier3费率：online: 3.4%+30 reader: 2.75%+50
        CompanyDto companyDto3 = new CompanyDto();
        companyDto3.setId(1);
        companyDto3.setLevel(1200);
        when(iBusinessBusinessClient.getCompanyByBusinessId(any())).thenReturn(companyDto3);
        when(processingFeeService.getProcessingFeeForCompany(any(), anyInt()))
                .thenReturn(ProcessingFeeUtil.OLD_PLAN_FEE);
        paymentSettingService.initializeCustomizedFee(paymentSetting);
        assertThat(paymentSetting.getOnlineFeeRate().compareTo(BigDecimal.valueOf(3.4)))
                .isEqualTo(0);
        assertThat(paymentSetting.getOnlineFeeCents() == 30).isTrue();
        assertThat(paymentSetting
                        .getReaderFeeRate()
                        .compareTo(BigDecimal.valueOf(ProcessingFeeUtil.T3_PLAN_FEE
                                        .getTerminal()
                                        .getPercent())
                                .multiply(BigDecimal.valueOf(100))))
                .isEqualTo(0);
        assertThat(paymentSetting.getReaderFeeCents() == 50).isTrue();
    }
}
