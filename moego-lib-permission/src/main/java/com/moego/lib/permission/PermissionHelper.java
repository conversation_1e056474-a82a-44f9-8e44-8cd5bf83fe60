package com.moego.lib.permission;

import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.organization.v1.LocationBriefView;
import com.moego.idl.models.organization.v1.StaffEmployeeCategory;
import com.moego.idl.models.organization.v1.StaffModel;
import com.moego.idl.models.permission.v1.EditCategoryPermissionDef;
import com.moego.idl.models.permission.v1.EditPermissionDef;
import com.moego.idl.models.permission.v1.PermissionModel;
import com.moego.idl.models.permission.v1.RoleType;
import com.moego.idl.service.organization.v1.BusinessServiceGrpc;
import com.moego.idl.service.organization.v1.GetLocationListRequest;
import com.moego.idl.service.organization.v1.GetStaffDetailRequest;
import com.moego.idl.service.organization.v1.GetStaffDetailResponse;
import com.moego.idl.service.organization.v1.StaffServiceGrpc;
import com.moego.idl.service.permission.v1.GetOwnerPermissionRequest;
import com.moego.idl.service.permission.v1.GetRoleDetailRequest;
import com.moego.idl.service.permission.v1.GetRoleDetailResponse;
import com.moego.idl.service.permission.v1.PermissionServiceGrpc;
import com.moego.lib.common.exception.ExceptionUtil;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
public class PermissionHelper {
    private final PermissionServiceGrpc.PermissionServiceBlockingStub permissionServiceBlockingStub;
    private final StaffServiceGrpc.StaffServiceBlockingStub staffServiceBlockingStub;
    private final BusinessServiceGrpc.BusinessServiceBlockingStub businessServiceBlockingStub;

    private StaffModel getStaffDetail(Long staffId, Long companyId) {
        GetStaffDetailResponse staffDetailResponse =
                staffServiceBlockingStub.getStaffDetail(GetStaffDetailRequest.newBuilder()
                        .setId(staffId)
                        .setCompanyId(companyId)
                        .build());
        return staffDetailResponse.getStaff();
    }

    private List<PermissionKey> getGrantedPermissionList(Long companyId, Long roleId) {
        GetRoleDetailResponse response = permissionServiceBlockingStub.getRoleDetail(GetRoleDetailRequest.newBuilder()
                .setRoleId(roleId)
                .setCompanyId(companyId)
                .build());
        return response.getRoleDetail().getPermissionCategoryListList().stream()
                .flatMap(it -> {
                    List<PermissionModel> permissionModelList = new ArrayList<>();
                    it.getPermissionListList().forEach(p -> {
                        permissionModelList.add(p);
                        permissionModelList.addAll(p.getSubPermissionListList());
                    });
                    return permissionModelList.stream();
                })
                .filter(PermissionModel::getIsSelected)
                .map(PermissionKey::new)
                .filter(it -> it.permissionName() != PermissionEnums.Unknown)
                .toList();
    }

    private List<LocationBriefView> getLocations(Long companyId, Long staffId) {
        return businessServiceBlockingStub
                .getLocationList(GetLocationListRequest.newBuilder()
                        .setTokenStaffId(staffId)
                        .setTokenCompanyId(companyId)
                        .build())
                .getLocationList();
    }

    public Map<PermissionEnums, PermissionKey> getPermissionKeyMapByRoleId(Long companyId, Long roleId) {
        List<PermissionKey> grantedPermissionList = getGrantedPermissionList(companyId, roleId);

        // 将 granted permission list 转换成 Map:
        // key 为 permission name, value 为 permission key 本身.
        return grantedPermissionList.stream()
                .collect(Collectors.toMap(PermissionKey::permissionName, permissionKey -> permissionKey));
    }

    public Map<PermissionEnums, Long> getPermissionMapByRoleId(Long companyId, Long roleId) {
        List<PermissionKey> grantedPermissionList = getGrantedPermissionList(companyId, roleId);

        // 将 granted permission list 转换成 Map，key 为 permission name，value 为 permission
        // 对应的 scope index，如果不需要 index 则为 0

        return grantedPermissionList.stream()
                .collect(Collectors.toMap(PermissionKey::permissionName, PermissionKey::scopeIndex));
    }

    public Map<PermissionEnums, PermissionKey> getPermissionKeyMapByStaffId(Long companyId, Long staffId) {
        StaffModel staffModel = getStaffDetail(staffId, companyId);
        Long roleId = staffModel.getRoleId();

        return getPermissionKeyMapByRoleId(companyId, roleId);
    }

    public Map<PermissionEnums, Long> getPermissionMapByStaffId(Long companyId, Long staffId) {
        StaffModel staffModel = getStaffDetail(staffId, companyId);
        Long roleId = staffModel.getRoleId();

        return getPermissionMapByRoleId(companyId, roleId);
    }

    /**
     * 判断 staff 是否有某个权限
     * @param companyId: company id
     * @param staffId: staff id
     * @param permissionName: permission name to check
     * @return: true if staff has the permission, false otherwise
     */
    public boolean hasPermission(Long companyId, Long staffId, PermissionEnums permissionName) {
        var permissionMap = getPermissionMapByStaffId(companyId, staffId);
        return permissionMap.containsKey(permissionName);
    }

    /**
     * 判断 staff 是否有某个权限, 同时会考虑需要读写的 businessID 是否在权限范围内
     * @param companyId: company id
     * @param businessIdsToCheck: business id to check
     * @param staffId: staff id
     * @param permissionName: permission name to check
     * @return: true if staff has the permission, false otherwise
     */
    public void checkPermission(
            Long companyId, Set<Long> businessIdsToCheck, Long staffId, PermissionEnums permissionName) {
        Set<Long> locationIds = getMaxLocationPermission(companyId, staffId, permissionName);
        if (!locationIds.containsAll(businessIdsToCheck)) {
            throw ExceptionUtil.bizException(
                    Code.CODE_PERMISSION_NOT_ENOUGH,
                    String.format(
                            "[%s] is only allowed for appointments in working business.",
                            permissionName.getPermissionName()));
        }
    }

    /**
     * null 表示没有权限
     * @param companyId
     * @param staffId
     * @param permissionName
     * @return
     */
    public Long getPermissionScopeIndex(Long companyId, Long staffId, PermissionEnums permissionName) {
        var permissionMap = getPermissionMapByStaffId(companyId, staffId);
        return permissionMap.get(permissionName);
    }

    /**
     * 有部分权限，需要限制只能在 working location 使用，目前配置能力没有完全放开，部分权限的作用范围是写死的
     */
    private boolean onlyWorkingLocations(PermissionEnums permission, Long scopeIndex) {
        return switch (permission) {
                // 需要写死 working location 的权限
            case ACCESS_PAYMENT_SETTING,
                    PROCESS_PAYMENT,
                    REMOVE_PROCESSING_FEE_BY_CLIENT,
                    CREATE_APPOINTMENT,
                    EDIT_APPOINTMENT,
                    CANCEL_APPOINTMENT,
                    EDIT_TICKET_COMMENT_AND_GROOMING_REPORT,
                    CREATE_AND_EDIT_BLOCK,
                    ADD_NEW_CLIENT,
                    ACCESS_BOOKING_REQUEST_AND_WAITING_LIST,
                    ACCESS_ABANDON_BOOKING_REQUEST,
                    ONLINE_BOOKING_SETTINGS,
                    ACCESS_STAFF_MEMBERS,
                    ADD_NEW_STAFF,
                    EDIT_ROLE_PERMISSIONS,
                    EDIT_STAFF_PROFILE,
                    EDIT_STAFF_NOTIFICATION,
                    EDIT_STAFF_PAY_RATE,
                    ACCESS_STAFF_SHIFT,
                    CLOCK_IN_OR_OUT,
                    EDIT_CLOCK_IN_OR_OUT_RECORDS,
                    ACCESS_RETAIL,
                    ACCESS_PACKAGE,
                    ACCESS_SERVICE_SETTINGS,
                    ACCESS_AGREEMENT_SETTINGS,
                    ACCESS_AUTO_MESSAGE_SETTINGS,
                    ACCESS_GROOMING_REPORT_SETTINGS,
                    ACCESS_MOBILE_GROOMING_SETTINGS,
                    ACCESS_INTAKE_FORM_SETTINGS,
                    ACCESS_QUICKBOOK_SYNC,
                    SELL_PACKAGES,
                    SELL_RETAIL_PRODUCTS -> true;
            case ACCESS_CLIENT,
                    DELETE_CLIENT,
                    UPDATE_CLIENT_AND_PET_STATUS,
                    IMPORT_CLIENT,
                    EXPORT_CLIENT,
                    MERGE_CLIENT,
                    ACCESS_CLIENT_PACKAGE_LIST,
                    EDIT_CLIENT_PACKAGE,
                    CREATE_OR_EDIT_INCIDENT_REPORT -> scopeIndex == 1;
            case ACCESS_REPORT, ACCESS_PAYROLL_REPORT -> scopeIndex == 1 || scopeIndex == 2;
            case MESSAGE_CLIENT, DELETE_CHAT_HISTORY, BLOCK_CLIENT_MESSAGE -> scopeIndex == 1
                    || scopeIndex == 2
                    || scopeIndex == 3;
            default -> false;
        };
    }

    // 返回有权限的最大 location 集合，可能是 working location，也可能是 all location，取决于权限的配置情况
    // 返回 null 表示没有权限
    public Set<Long> getMaxLocationPermission(Long companyId, Long staffId, PermissionEnums permission) {
        var allLocationList = getLocations(companyId, staffId);
        Set<Long> allLocationIds =
                allLocationList.stream().map(LocationBriefView::getId).collect(Collectors.toSet());
        Set<Long> workingLocationIds = allLocationList.stream()
                .filter(LocationBriefView::getIsWorkingLocation)
                .map(LocationBriefView::getId)
                .collect(Collectors.toSet());
        var permissionResult = getPermissionMapByStaffId(companyId, staffId);
        Long scopeIndex = permissionResult.get(permission);
        if (scopeIndex == null) {
            throw ExceptionUtil.bizException(
                    Code.CODE_PERMISSION_NOT_ENOUGH,
                    String.format("need permission: %s", permission.getPermissionName()));
        }
        return onlyWorkingLocations(permission, scopeIndex) ? workingLocationIds : allLocationIds;
    }

    // check the staff has the permission to edit the role's permissions
    public void checkEditPermission(
            Long companyId, Long staff, Long editRoleId, List<EditCategoryPermissionDef> editCategoryPermissionDef) {
        StaffModel staffModel = getStaffDetail(staff, companyId);
        if (Objects.equals(staffModel.getRoleId(), editRoleId)) {
            throw ExceptionUtil.bizException(Code.CODE_PERMISSION_NOT_ENOUGH, "can't edit your own role permissions");
        }
        var permissionEnumsScopeIndexMap = getPermissionMapByRoleId(companyId, staffModel.getRoleId());
        if (permissionEnumsScopeIndexMap.keySet().stream()
                .noneMatch(it -> it.equals(PermissionEnums.EDIT_ROLE_PERMISSIONS))) {
            throw ExceptionUtil.bizException(
                    Code.CODE_PERMISSION_NOT_ENOUGH,
                    String.format("permission %s is required", PermissionEnums.EDIT_ROLE_PERMISSIONS));
        }
        if (staffModel.getEmployeeCategory() == StaffEmployeeCategory.COMPANY_STAFF
                || staffModel.getEmployeeCategory() == StaffEmployeeCategory.COMPANY_OWNER) {

            var editRole = permissionServiceBlockingStub
                    .getRoleDetail(GetRoleDetailRequest.newBuilder()
                            .setRoleId(editRoleId)
                            .setCompanyId(companyId)
                            .build())
                    .getRoleDetail();
            // company owner 和 staff 只能编辑 company staff 的 role
            // 兼容未刷 type 的 role
            if (!editRole.getType().equals(RoleType.ROLE_TYPE_COMPANY_STAFF)
                    && !editRole.getType().equals(RoleType.ROLE_TYPE_UNSPECIFIED)) {
                throw ExceptionUtil.bizException(Code.CODE_PERMISSION_NOT_ENOUGH, "can't edit this role");
            }
            var roleExitPermissionMap = editRole.getPermissionCategoryListList().stream()
                    .flatMap(it -> {
                        List<PermissionModel> permissionModelList = new ArrayList<>();
                        it.getPermissionListList().forEach(p -> {
                            permissionModelList.add(p);
                            permissionModelList.addAll(p.getSubPermissionListList());
                        });
                        return permissionModelList.stream();
                    })
                    .filter(PermissionModel::getIsSelected)
                    .collect(Collectors.toMap(PermissionModel::getId, PermissionModel::getSelectedScopeIndex));

            // company owner 和 staff 只能赋予别的 staff company owner 拥有的权限
            var ownerPermissionCategory = permissionServiceBlockingStub
                    .getOwnerPermission(GetOwnerPermissionRequest.newBuilder()
                            .setCompanyId(companyId)
                            .build())
                    .getPermissionCategoryListList();
            var ownerPermissionsMap = ownerPermissionCategory.stream()
                    .flatMap(it -> {
                        List<PermissionModel> permissionModelList = new ArrayList<>();
                        it.getPermissionListList().forEach(p -> {
                            permissionModelList.add(p);
                            // 改成三级权限后会有问题
                            permissionModelList.addAll(p.getSubPermissionListList());
                        });
                        return permissionModelList.stream();
                    })
                    .filter(PermissionModel::getIsSelected)
                    .collect(Collectors.toMap(PermissionModel::getId, PermissionModel::getSelectedScopeIndex));

            for (EditCategoryPermissionDef editCategoryPermission : editCategoryPermissionDef) {
                if (editCategoryPermission.getPermissionsList().stream()
                        .filter(EditPermissionDef::getIsActive)
                        .filter(i -> !roleExitPermissionMap.containsKey(i.getPermissionId()))
                        .anyMatch(it -> !ownerPermissionsMap.containsKey(it.getPermissionId()))) {
                    throw ExceptionUtil.bizException(
                            Code.CODE_PERMISSION_NOT_ENOUGH, "can't grant permission that owner doesn't have");
                }
            }
        }
    }
}
