{"$schema": "https://moego.s3.us-west-2.amazonaws.com/ops/github-actions/ci-json-schema.json", "service_name": "moego-svc-metadata", "slack": ["#team-backend"], "language": {"type": "java", "version": "17"}, "install": {"commands": ["./gradlew classes --stacktrace --no-daemon -Dsbc.scriptBranch=GitHubActions"], "cache_dir": ".gradle"}, "lint": {"commands": ["./gradlew spotlessCheck spotbugsMain --stacktrace --no-daemon -Dsbc.scriptBranch=GitHubActions"]}, "test": {"commands": ["./gradlew test jacocoToCobertura --stacktrace --no-daemon -Dsbc.scriptBranch=GitHubActions"], "report": "./build/test-results/test", "coverage": "./build/reports/jacoco/test/cobertura-jacocoTestReport.xml", "coverage_gate": 1}, "build": {"commands": ["./gradlew bootJar --stacktrace --no-daemon -Dsbc.scriptBranch=GitHubActions"], "build_image": [{"dockerfile": "ci/Dockerfile", "context": "."}]}, "deploy": {"type": "service"}}