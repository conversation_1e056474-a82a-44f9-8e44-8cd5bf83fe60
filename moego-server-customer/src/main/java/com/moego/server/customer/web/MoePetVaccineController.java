package com.moego.server.customer.web;

import com.moego.common.exception.CommonException;
import com.moego.common.response.ResponseResult;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.migrate.MigrateHelper;
import com.moego.server.customer.dto.AddResultDTO;
import com.moego.server.customer.dto.PetVaccineListDTO;
import com.moego.server.customer.helper.PetHelper;
import com.moego.server.customer.mapstruct.PetVaccineConverter;
import com.moego.server.customer.params.VaccineBindingSaveVo;
import com.moego.server.customer.service.CustomerPetService;
import com.moego.server.customer.service.MoePetVaccineService;
import com.moego.server.customer.service.params.CommonIdParams;
import com.moego.server.customer.service.params.CommonIdsParams;
import com.moego.server.customer.web.params.MoePetVaccineParams;
import com.moego.server.customer.web.vo.VaccineBindingDeleteVo;
import com.moego.server.customer.web.vo.VaccineBindingUpdateVo;
import com.moego.svc.activitylog.event.enums.Action;
import com.moego.svc.activitylog.event.enums.ResourceType;
import com.moego.svc.activitylog.processor.ActivityLog;
import jakarta.validation.Valid;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(path = "/customer")
@RequiredArgsConstructor
public class MoePetVaccineController {

    private final MoePetVaccineService moePetVaccineService;
    private final CustomerPetService customerPetService;
    private final MigrateHelper migrateHelper;
    private final PetHelper petHelper;

    /**
     * DONE(account structure): 迁移后的用户请使用新接口：moego.api.business_customer.v1.CreatePetVaccine
     */
    @Deprecated
    @PostMapping(value = "/pet/vaccine")
    @Auth(AuthType.BUSINESS)
    @ActivityLog(
            action = Action.CREATE,
            resourceType = ResourceType.PET_VACCINE,
            resourceId = "#result.data.id",
            details = "#params")
    public ResponseResult<AddResultDTO> addMoePetVaccine(AuthContext context, @RequestBody MoePetVaccineParams params) {

        migrateHelper.blockMigrated(context);

        var moePetVaccine = PetVaccineConverter.INSTANCE.toEntity(params);
        moePetVaccine.setId(null);
        moePetVaccine.setCompanyId(context.companyId());
        moePetVaccine.setBusinessId(context.getBusinessId());
        return moePetVaccineService.addMoePetVaccine(moePetVaccine);
    }

    /**
     * DONE(account structure): 迁移后的用户请使用新接口：moego.api.business_customer.v1.UpdatePetVaccine
     */
    @Deprecated
    @PutMapping(value = "/pet/vaccine")
    @Auth(AuthType.BUSINESS)
    @ActivityLog(
            action = Action.UPDATE,
            resourceType = ResourceType.PET_VACCINE,
            resourceId = "#params.id",
            details = "#params")
    public ResponseResult<Integer> modifyMoePetVaccine(AuthContext context, @RequestBody MoePetVaccineParams params) {

        migrateHelper.blockMigrated(context);

        if (params.getId() == null) {
            throw new CommonException("id is not null");
        }

        var moePetVaccine = PetVaccineConverter.INSTANCE.toEntity(params);
        moePetVaccine.setCompanyId(context.companyId());
        moePetVaccine.setBusinessId(context.getBusinessId());
        return moePetVaccineService.modifyMoePetVaccine(moePetVaccine);
    }

    /**
     * DONE(account structure): 迁移后的用户请使用新接口：moego.api.business_customer.v1.DeletePetVaccine
     */
    @Deprecated
    @DeleteMapping(value = "/pet/vaccine")
    @Auth(AuthType.BUSINESS)
    @ActivityLog(
            action = Action.DELETE,
            resourceType = ResourceType.PET_VACCINE,
            resourceId = "#id.id",
            details = "#id")
    public ResponseResult<Integer> deleteMoePetVaccine(AuthContext context, @RequestBody CommonIdParams id) {

        migrateHelper.blockMigrated(context);

        return moePetVaccineService.deleteMoePetVaccine(context.getBusinessId(), id.getId());
    }

    /**
     * DONE(account structure): 迁移后的用户请使用新接口：moego.api.business_customer.v1.SortPetVaccine
     *
     * DONE(Ritchie): 需要对 idlist -> businessId 校验，排除非 businessId 下的 tagId，或拒绝排序。
     */
    @Deprecated
    @PutMapping(value = "/pet/vaccines/sort")
    @Auth(AuthType.BUSINESS)
    public ResponseResult<Integer> sortMoePetVaccine(AuthContext context, @RequestBody CommonIdsParams idList) {

        migrateHelper.blockMigrated(context);

        return moePetVaccineService.sortMoePetVaccine(context.getBusinessId(), idList.getIds());
    }

    /**
     * DONE(account structure): 迁移后的用户请使用新接口：moego.api.business_customer.v1.ListPetVaccine
     */
    @Deprecated
    @GetMapping(value = "/pet/vaccine/list")
    @Auth(AuthType.BUSINESS)
    public ResponseResult<List<PetVaccineListDTO>> queryMoePetVaccines(AuthContext context, String name) {

        migrateHelper.blockMigrated(context);

        return ResponseResult.success(moePetVaccineService.queryMoePetVaccines(context.getBusinessId(), name));
    }

    /**
     * DONE(account structure)
     */
    @PostMapping("/pet/vaccine/binding")
    @Auth(AuthType.BUSINESS)
    @ActivityLog(
            action = Action.CREATE,
            resourceType = ResourceType.PET_VACCINE_BINDING,
            resourceId = "#result.data",
            details = "#recordSaveVo")
    public ResponseResult<Integer> savePetBindingRecord(
            AuthContext context, @Valid @RequestBody VaccineBindingSaveVo recordSaveVo) {

        var migrate = migrateHelper.isMigrate(context);
        var companyId = context.companyId();
        var businessId = context.getBusinessId();
        var petId = recordSaveVo.getPetId();
        var vaccineId = recordSaveVo.getVaccineId();

        customerPetService.checkingBizByPetId(petId, companyId, migrate ? null : businessId, null);
        moePetVaccineService.checkingBizByVaccineId(vaccineId, companyId, migrate ? null : businessId);

        return ResponseResult.success(moePetVaccineService.saveVaccineBinding(recordSaveVo));
    }

    /**
     * DONE(account structure)
     */
    @PutMapping("/pet/vaccine/binding")
    @Auth(AuthType.BUSINESS)
    @ActivityLog(
            action = Action.UPDATE,
            resourceType = ResourceType.PET_VACCINE_BINDING,
            resourceId = "#recordUpdateVo.vaccineBindingId",
            details = "#recordUpdateVo")
    public ResponseResult<Boolean> updatePetBindingRecord(
            AuthContext context, @Valid @RequestBody VaccineBindingUpdateVo recordUpdateVo) {

        var migrate = migrateHelper.isMigrate(context);
        var companyId = context.companyId();
        var businessId = context.getBusinessId();
        var vaccineId = recordUpdateVo.getVaccineId();
        var bindingId = recordUpdateVo.getVaccineBindingId();

        moePetVaccineService.checkingBizByVaccineId(vaccineId, companyId, migrate ? null : businessId);
        moePetVaccineService.checkingBizByBindingId(bindingId, companyId, migrate ? null : businessId);
        return ResponseResult.success(moePetVaccineService.updateVaccineBinding(recordUpdateVo));
    }

    /**
     * DONE(account structure)
     */
    @DeleteMapping("/pet/vaccine/binding")
    @Auth(AuthType.BUSINESS)
    @ActivityLog(
            action = Action.DELETE,
            resourceType = ResourceType.PET_VACCINE_BINDING,
            resourceId = "#recordDeleteVo.vaccineBindingId",
            details = "#recordDeleteVo")
    public ResponseResult<Boolean> deletePetBindingRecord(
            AuthContext context, @Valid @RequestBody VaccineBindingDeleteVo recordDeleteVo) {

        var companyId = context.companyId();

        checkForDeletePetBindingRecord(companyId, recordDeleteVo.getVaccineBindingId());

        return ResponseResult.success(moePetVaccineService.deleteVaccineBinding(recordDeleteVo));
    }

    private void checkForDeletePetBindingRecord(Long companyId, Integer vaccineBindingId) {
        var binding = moePetVaccineService.getVaccineBinding(vaccineBindingId);
        if (binding == null) {
            return;
        }

        // check if pet exists
        petHelper.mustGetPet(companyId, binding.getPetId());
    }
}
