package com.moego.server.customer.mapperbean;

public class MoeIntakeFormDetail {

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_intake_form_detail.id
     *
     * @mbg.generated
     */
    private Integer id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_intake_form_detail.form_id
     *
     * @mbg.generated
     */
    private Integer formId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_intake_form_detail.business_id
     *
     * @mbg.generated
     */
    private Integer businessId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_intake_form_detail.question
     *
     * @mbg.generated
     */
    private String question;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_intake_form_detail.placeholder
     *
     * @mbg.generated
     */
    private String placeholder;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_intake_form_detail.is_show
     *
     * @mbg.generated
     */
    private Byte isShow;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_intake_form_detail.is_required
     *
     * @mbg.generated
     */
    private Byte isRequired;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_intake_form_detail.type
     *
     * @mbg.generated
     */
    private Byte type;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_intake_form_detail.is_allow_delete
     *
     * @mbg.generated
     */
    private Byte isAllowDelete;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_intake_form_detail.is_allow_change
     *
     * @mbg.generated
     */
    private Byte isAllowChange;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_intake_form_detail.is_allow_edit
     *
     * @mbg.generated
     */
    private Byte isAllowEdit;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_intake_form_detail.sort
     *
     * @mbg.generated
     */
    private Integer sort;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_intake_form_detail.status
     *
     * @mbg.generated
     */
    private Byte status;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_intake_form_detail.create_time
     *
     * @mbg.generated
     */
    private Long createTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_intake_form_detail.update_time
     *
     * @mbg.generated
     */
    private Long updateTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_intake_form_detail.question_type
     *
     * @mbg.generated
     */
    private Byte questionType;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_intake_form_detail.company_id
     *
     * @mbg.generated
     */
    private Long companyId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_intake_form_detail.extra_json
     *
     * @mbg.generated
     */
    private String extraJson;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_intake_form_detail.id
     *
     * @return the value of moe_intake_form_detail.id
     *
     * @mbg.generated
     */
    public Integer getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_intake_form_detail.id
     *
     * @param id the value for moe_intake_form_detail.id
     *
     * @mbg.generated
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_intake_form_detail.form_id
     *
     * @return the value of moe_intake_form_detail.form_id
     *
     * @mbg.generated
     */
    public Integer getFormId() {
        return formId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_intake_form_detail.form_id
     *
     * @param formId the value for moe_intake_form_detail.form_id
     *
     * @mbg.generated
     */
    public void setFormId(Integer formId) {
        this.formId = formId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_intake_form_detail.business_id
     *
     * @return the value of moe_intake_form_detail.business_id
     *
     * @mbg.generated
     */
    public Integer getBusinessId() {
        return businessId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_intake_form_detail.business_id
     *
     * @param businessId the value for moe_intake_form_detail.business_id
     *
     * @mbg.generated
     */
    public void setBusinessId(Integer businessId) {
        this.businessId = businessId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_intake_form_detail.question
     *
     * @return the value of moe_intake_form_detail.question
     *
     * @mbg.generated
     */
    public String getQuestion() {
        return question;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_intake_form_detail.question
     *
     * @param question the value for moe_intake_form_detail.question
     *
     * @mbg.generated
     */
    public void setQuestion(String question) {
        this.question = question == null ? null : question.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_intake_form_detail.placeholder
     *
     * @return the value of moe_intake_form_detail.placeholder
     *
     * @mbg.generated
     */
    public String getPlaceholder() {
        return placeholder;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_intake_form_detail.placeholder
     *
     * @param placeholder the value for moe_intake_form_detail.placeholder
     *
     * @mbg.generated
     */
    public void setPlaceholder(String placeholder) {
        this.placeholder = placeholder == null ? null : placeholder.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_intake_form_detail.is_show
     *
     * @return the value of moe_intake_form_detail.is_show
     *
     * @mbg.generated
     */
    public Byte getIsShow() {
        return isShow;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_intake_form_detail.is_show
     *
     * @param isShow the value for moe_intake_form_detail.is_show
     *
     * @mbg.generated
     */
    public void setIsShow(Byte isShow) {
        this.isShow = isShow;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_intake_form_detail.is_required
     *
     * @return the value of moe_intake_form_detail.is_required
     *
     * @mbg.generated
     */
    public Byte getIsRequired() {
        return isRequired;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_intake_form_detail.is_required
     *
     * @param isRequired the value for moe_intake_form_detail.is_required
     *
     * @mbg.generated
     */
    public void setIsRequired(Byte isRequired) {
        this.isRequired = isRequired;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_intake_form_detail.type
     *
     * @return the value of moe_intake_form_detail.type
     *
     * @mbg.generated
     */
    public Byte getType() {
        return type;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_intake_form_detail.type
     *
     * @param type the value for moe_intake_form_detail.type
     *
     * @mbg.generated
     */
    public void setType(Byte type) {
        this.type = type;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_intake_form_detail.is_allow_delete
     *
     * @return the value of moe_intake_form_detail.is_allow_delete
     *
     * @mbg.generated
     */
    public Byte getIsAllowDelete() {
        return isAllowDelete;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_intake_form_detail.is_allow_delete
     *
     * @param isAllowDelete the value for moe_intake_form_detail.is_allow_delete
     *
     * @mbg.generated
     */
    public void setIsAllowDelete(Byte isAllowDelete) {
        this.isAllowDelete = isAllowDelete;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_intake_form_detail.is_allow_change
     *
     * @return the value of moe_intake_form_detail.is_allow_change
     *
     * @mbg.generated
     */
    public Byte getIsAllowChange() {
        return isAllowChange;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_intake_form_detail.is_allow_change
     *
     * @param isAllowChange the value for moe_intake_form_detail.is_allow_change
     *
     * @mbg.generated
     */
    public void setIsAllowChange(Byte isAllowChange) {
        this.isAllowChange = isAllowChange;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_intake_form_detail.is_allow_edit
     *
     * @return the value of moe_intake_form_detail.is_allow_edit
     *
     * @mbg.generated
     */
    public Byte getIsAllowEdit() {
        return isAllowEdit;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_intake_form_detail.is_allow_edit
     *
     * @param isAllowEdit the value for moe_intake_form_detail.is_allow_edit
     *
     * @mbg.generated
     */
    public void setIsAllowEdit(Byte isAllowEdit) {
        this.isAllowEdit = isAllowEdit;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_intake_form_detail.sort
     *
     * @return the value of moe_intake_form_detail.sort
     *
     * @mbg.generated
     */
    public Integer getSort() {
        return sort;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_intake_form_detail.sort
     *
     * @param sort the value for moe_intake_form_detail.sort
     *
     * @mbg.generated
     */
    public void setSort(Integer sort) {
        this.sort = sort;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_intake_form_detail.status
     *
     * @return the value of moe_intake_form_detail.status
     *
     * @mbg.generated
     */
    public Byte getStatus() {
        return status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_intake_form_detail.status
     *
     * @param status the value for moe_intake_form_detail.status
     *
     * @mbg.generated
     */
    public void setStatus(Byte status) {
        this.status = status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_intake_form_detail.create_time
     *
     * @return the value of moe_intake_form_detail.create_time
     *
     * @mbg.generated
     */
    public Long getCreateTime() {
        return createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_intake_form_detail.create_time
     *
     * @param createTime the value for moe_intake_form_detail.create_time
     *
     * @mbg.generated
     */
    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_intake_form_detail.update_time
     *
     * @return the value of moe_intake_form_detail.update_time
     *
     * @mbg.generated
     */
    public Long getUpdateTime() {
        return updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_intake_form_detail.update_time
     *
     * @param updateTime the value for moe_intake_form_detail.update_time
     *
     * @mbg.generated
     */
    public void setUpdateTime(Long updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_intake_form_detail.question_type
     *
     * @return the value of moe_intake_form_detail.question_type
     *
     * @mbg.generated
     */
    public Byte getQuestionType() {
        return questionType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_intake_form_detail.question_type
     *
     * @param questionType the value for moe_intake_form_detail.question_type
     *
     * @mbg.generated
     */
    public void setQuestionType(Byte questionType) {
        this.questionType = questionType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_intake_form_detail.company_id
     *
     * @return the value of moe_intake_form_detail.company_id
     *
     * @mbg.generated
     */
    public Long getCompanyId() {
        return companyId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_intake_form_detail.company_id
     *
     * @param companyId the value for moe_intake_form_detail.company_id
     *
     * @mbg.generated
     */
    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_intake_form_detail.extra_json
     *
     * @return the value of moe_intake_form_detail.extra_json
     *
     * @mbg.generated
     */
    public String getExtraJson() {
        return extraJson;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_intake_form_detail.extra_json
     *
     * @param extraJson the value for moe_intake_form_detail.extra_json
     *
     * @mbg.generated
     */
    public void setExtraJson(String extraJson) {
        this.extraJson = extraJson == null ? null : extraJson.trim();
    }
}
