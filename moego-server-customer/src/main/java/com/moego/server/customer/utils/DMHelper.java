package com.moego.server.customer.utils;

import com.moego.common.utils.PermissionUtil;
import com.moego.server.business.api.IBusinessStaffService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@AllArgsConstructor
public class DMHelper {
    private static final String RAW_ID = "0NONwg67/MZgcXoqJD46ww==";

    private final IBusinessStaffService iBusinessStaffService;

    public boolean withRawData(String rawId, Integer staffId) {
        if (!RAW_ID.equals(rawId)) {
            return false;
        }

        if (staffId == null) {
            return false;
        }
        var staffPermissions = iBusinessStaffService.getBusinessRoleByStaffId(staffId);
        return PermissionUtil.isOwner(staffPermissions);
    }
}
