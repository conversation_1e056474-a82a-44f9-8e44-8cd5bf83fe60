<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.moego.server.customer.mapper.MoePetTypeMapper">
  <resultMap id="BaseResultMap" type="com.moego.server.customer.mapperbean.MoePetType">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="business_id" jdbcType="INTEGER" property="businessId" />
    <result column="pet_type_id" jdbcType="INTEGER" property="petTypeId" />
    <result column="type_name" jdbcType="VARCHAR" property="typeName" />
    <result column="is_available" jdbcType="TINYINT" property="isAvailable" />
      <result column="book_online_available" jdbcType="TINYINT" property="bookOnlineAvailable" />
      <result column="sort" jdbcType="INTEGER" property="sort" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="update_time" jdbcType="BIGINT" property="updateTime" />
    <result column="company_id" jdbcType="BIGINT" property="companyId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, business_id, pet_type_id, type_name, is_available, sort, status, create_time,
    update_time, book_online_available, company_id
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <include refid="Base_Column_List" />
    from moe_pet_type
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from moe_pet_type
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.moego.server.customer.mapperbean.MoePetType">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into moe_pet_type (business_id, pet_type_id, type_name,
      is_available, sort, status,
      create_time, update_time, book_online_available, company_id)
    values (#{businessId,jdbcType=INTEGER}, #{petTypeId,jdbcType=INTEGER}, #{typeName,jdbcType=VARCHAR},
      #{isAvailable,jdbcType=TINYINT}, #{sort,jdbcType=INTEGER}, #{status,jdbcType=TINYINT},
      #{createTime,jdbcType=BIGINT}, #{updateTime,jdbcType=BIGINT},
      #{bookOnlineAvailable,jdbcType=TINYINT}, #{companyId,jdbcType=BIGINT})
  </insert>
   <update id="updateByPrimaryKeySelective" parameterType="com.moego.server.customer.mapperbean.MoePetType">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_pet_type
    <set>
      <if test="businessId != null">
        business_id = #{businessId,jdbcType=INTEGER},
      </if>
      <if test="petTypeId != null">
        pet_type_id = #{petTypeId,jdbcType=INTEGER},
      </if>
      <if test="typeName != null">
        type_name = #{typeName,jdbcType=VARCHAR},
      </if>
      <if test="isAvailable != null">
        is_available = #{isAvailable,jdbcType=TINYINT},
      </if>
      <if test="sort != null">
        sort = #{sort,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=BIGINT},
      </if>
        <if test="bookOnlineAvailable != null">
            book_online_available = #{bookOnlineAvailable,jdbcType=TINYINT},
        </if>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.moego.server.customer.mapperbean.MoePetType">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_pet_type
    set business_id = #{businessId,jdbcType=INTEGER},
      pet_type_id = #{petTypeId,jdbcType=INTEGER},
      type_name = #{typeName,jdbcType=VARCHAR},
      is_available = #{isAvailable,jdbcType=TINYINT},
      sort = #{sort,jdbcType=INTEGER},
      status = #{status,jdbcType=TINYINT},
      create_time = #{createTime,jdbcType=BIGINT},
      update_time = #{updateTime,jdbcType=BIGINT},
      company_id = #{companyId,jdbcType=BIGINT}
    where id = #{id,jdbcType=INTEGER}
  </update>

  <resultMap id="PetTypeListDTOResultMap" type="com.moego.server.customer.dto.PetTypeListDTO">
    <result column="id" jdbcType="INTEGER" property="id" />
    <result column="business_id" jdbcType="INTEGER" property="businessId" />
    <result column="pet_type_id" jdbcType="INTEGER" property="petTypeId" />
    <result column="type_name" jdbcType="VARCHAR" property="typeName" />
    <result column="is_available" jdbcType="TINYINT" property="isAvailable" />
    <result column="book_online_available" jdbcType="TINYINT" property="bookOnlineAvailable" />
    <result column="sort" jdbcType="INTEGER" property="sort" />
    <result column="create_time" jdbcType="INTEGER" property="createTime" />
    <result column="update_time" jdbcType="INTEGER" property="updateTime" />
  </resultMap>

  <insert id="addMoePetType" parameterType="com.moego.server.customer.mapperbean.MoePetType">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    INSERT INTO moe_pet_type
    <trim prefix=" (" suffix=")" suffixOverrides=",">
      <if test="id != null"> id,</if>
      <if test="businessId != null"> business_id,</if>
      <if test="typeName != null"> type_name,</if>
      <if test="isAvailable != null"> is_available,</if>
      <if test="sort != null"> sort,</if>
      <if test="status != null"> status,</if>
      <if test="createTime != null"> create_time,</if>
      <if test="updateTime != null"> update_time,</if>
      <if test="companyId != null"> company_id,</if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null"> #{id},</if>
      <if test="businessId != null"> #{businessId},</if>
      <if test="typeName != null"> #{typeName},</if>
      <if test="isAvailable != null"> #{isAvailable},</if>
      <if test="sort != null"> #{sort},</if>
      <if test="status != null"> #{status},</if>
      <if test="createTime != null"> #{createTime},</if>
      <if test="updateTime != null"> #{updateTime},</if>
      <if test="companyId != null"> #{companyId},</if>
    </trim>
  </insert>

  <update id="modifyMoePetType" parameterType="com.moego.server.customer.mapperbean.MoePetType">
    UPDATE moe_pet_type SET
    <trim suffixOverrides=",">
      <if test="businessId != null"> business_id = #{businessId},</if>
      <if test="typeName != null"> type_name = #{typeName},</if>
      <if test="isAvailable != null"> is_available = #{isAvailable},</if>
      <if test="sort != null"> sort = #{sort},</if>
      <if test="updateTime != null"> update_time = #{updateTime},</if>
    </trim>
    WHERE id = #{id}
  </update>

  <update id="sortMoePetType">
    <foreach collection="sortDtos" item="item"  separator=";">
      update moe_pet_type set sort = #{item.sort} where id = #{item.id} and business_id = #{businessId}
    </foreach>
  </update>
  <select id="queryMoePetTypeByBusinessId" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List"/>
    FROM moe_pet_type
    WHERE business_id = #{businessId}
    <if test="businessId == 0">
      and company_id = 0
    </if>
    <if test="onlyActive">
      AND is_available = 1
    </if>
    ORDER BY sort desc
  </select>
  <select id="getPetTypeByIdList" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List"/>
    FROM moe_pet_type
    WHERE business_id = #{businessId} and `pet_type_id` in
    <foreach close=")" collection="petTypeIdList" item="id" open="(" separator=",">
      #{id}
    </foreach>
  </select>

  <select id="queryMoePetType" resultMap="PetTypeListDTOResultMap">
     select id, business_id, type_name, is_available, sort, create_time, update_time,pet_type_id
      ,book_online_available
    from moe_pet_type
    where status = 1
    and business_id = #{businessId}
    order by sort desc
  </select>

  <select id="selectTypeNameCountByBusinessId" resultType="com.moego.server.customer.service.dto.PetCountTotalDto">
    SELECT
    p.pet_type_id as petTypeId,
    p.count as count,
    pt.type_name as typeName
    FROM
    ( SELECT pet_type_id, count(*) AS count FROM moe_customer_pet WHERE `status` = 1 AND business_id = #{businessId}
    GROUP BY
    pet_type_id ) AS p
    LEFT JOIN moe_pet_type AS pt ON ( p.pet_type_id = pt.pet_type_id )
    WHERE
    pt.business_id = #{businessId}
  </select>

    <!--  根据筛选条件统计pet type name count  -->
    <select id="selectTypeNameCount" resultType="com.moego.server.customer.service.dto.PetCountTotalDto">
        SELECT
            p.pet_type_id as petTypeId,
            p.count as count,
            pt.type_name as typeName
        FROM
            (
            SELECT pet_type_id, count(*) AS count FROM moe_customer_pet
            WHERE `status` = 1
            AND life_status = 1
            AND
            company_id = #{companyId}
            <if test="businessIds != null and businessIds.size &gt; 0">
              and business_id in
              <foreach close=")" collection="businessIds" item="businessId" open="(" separator=",">
                #{businessId}
              </foreach>
            </if>
            AND customer_id in (
                SELECT id FROM moe_business_customer
                <where>
                    company_id = #{companyId}
                    <if test="businessIds != null and businessIds.size &gt; 0">
                      and business_id in
                      <foreach close=")" collection="businessIds" item="businessId" open="(" separator=",">
                        #{businessId}
                      </foreach>
                    </if>
                    and status=1
                    <if test="searchVo.statusSetting.all != null and searchVo.statusSetting.all == false">
                        <if test="searchVo.statusSetting.activeClient != null and searchVo.statusSetting.activeClient == true">
                            and inactive = 0
                        </if>
                        <if test="searchVo.statusSetting.inactiveClient != null and searchVo.statusSetting.inactiveClient == true">
                            and inactive = 1
                        </if>
                        <if test="searchVo.statusSetting.fromOnlineBooking != null and searchVo.statusSetting.fromOnlineBooking == true">
                            and source = 0
                        </if>
                        <if test="searchVo.statusSetting.fromIntakeForm != null and searchVo.statusSetting.fromIntakeForm == true">
                            and inactive = 0
                        </if>
                        <if test="searchVo.statusSetting.blockMessage != null and searchVo.statusSetting.blockMessage == true">
                            and is_block_message = 1
                        </if>
                        <if test="searchVo.statusSetting.blockOnlineBooking != null and searchVo.statusSetting.blockOnlineBooking == true">
                            and is_block_online_booking = 1
                        </if>
                    </if>
                    <if test="customerIdFilterSet != null and customerIdFilterSet.size &gt;0">
                        and `id` in
                        <foreach close=")" collection="customerIdFilterSet" item="item" open="(" separator=",">
                            #{item}
                        </foreach>
                    </if>
                    <if test="searchVo.keyword != null and searchVo.keyword != ''">
                        AND (
                        email like concat('%',#{searchVo.keyword},'%')
                        <if test="searchVo.findNameKeyword != null and searchVo.findNameKeyword != ''">
                            OR CONCAT(first_name,last_name) like concat('%',#{searchVo.findNameKeyword},'%')
                        </if>
                        <if test="customerIdSetByKeyword != null and customerIdSetByKeyword.size &gt; 0">
                            OR `id` IN
                            <foreach close=")" collection="customerIdSetByKeyword" item="item" open="(" separator=",">
                                #{item}
                            </foreach>
                        </if>
                        )
                    </if>
                </where>
            )
            GROUP BY
            pet_type_id
            ) AS p
        LEFT JOIN moe_pet_type AS pt ON ( p.pet_type_id = pt.pet_type_id
            and pt.company_id = #{companyId}
            and pt.status = 1
        )
    </select>

  <select id="selectPetTypeNameCountByPetIdList" resultType="com.moego.server.customer.service.dto.PetCountTotalDto">
    SELECT
    p.pet_type_id as petTypeId,
    p.count as count,
    pt.type_name as typeName
    FROM
    ( SELECT pet_type_id, count(*) AS count FROM moe_customer_pet WHERE `status` = 1 AND business_id = #{businessId}
    and `id` in
    <foreach close=")" collection="petIdList" item="id" open="(" separator=",">
      #{id}
    </foreach>
    GROUP BY
    pet_type_id ) AS p
    LEFT JOIN moe_pet_type AS pt ON ( p.pet_type_id = pt.pet_type_id )
    WHERE
    pt.business_id = #{businessId}
  </select>
</mapper>
