package com.moego.server.customer.mapstruct;

import com.moego.idl.models.business_customer.v1.BusinessPetCoatTypeModel;
import com.moego.server.customer.dto.MoePetHairLengthDTO;
import com.moego.server.customer.mapperbean.MoePetHairLength;
import com.moego.server.customer.web.params.MoePetHairLengthParams;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 */
@Mapper
public interface PetHairLengthConverter {
    PetHairLengthConverter INSTANCE = Mappers.getMapper(PetHairLengthConverter.class);

    MoePetHairLength toEntity(MoePetHairLengthParams params);

    MoePetHairLengthDTO toDTO(BusinessPetCoatTypeModel model);
}
