package com.moego.server.customer.mapperbean;

public class MoePetExpand {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_pet_expand.id
     *
     * @mbg.generated
     */
    private Integer id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_pet_expand.pet_id
     *
     * @mbg.generated
     */
    private Integer petId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_pet_expand.deactivate_reason
     *
     * @mbg.generated
     */
    private String deactivateReason;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_pet_expand.id
     *
     * @return the value of moe_pet_expand.id
     *
     * @mbg.generated
     */
    public Integer getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_pet_expand.id
     *
     * @param id the value for moe_pet_expand.id
     *
     * @mbg.generated
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_pet_expand.pet_id
     *
     * @return the value of moe_pet_expand.pet_id
     *
     * @mbg.generated
     */
    public Integer getPetId() {
        return petId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_pet_expand.pet_id
     *
     * @param petId the value for moe_pet_expand.pet_id
     *
     * @mbg.generated
     */
    public void setPetId(Integer petId) {
        this.petId = petId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_pet_expand.deactivate_reason
     *
     * @return the value of moe_pet_expand.deactivate_reason
     *
     * @mbg.generated
     */
    public String getDeactivateReason() {
        return deactivateReason;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_pet_expand.deactivate_reason
     *
     * @param deactivateReason the value for moe_pet_expand.deactivate_reason
     *
     * @mbg.generated
     */
    public void setDeactivateReason(String deactivateReason) {
        this.deactivateReason = deactivateReason == null ? null : deactivateReason.trim();
    }
}
