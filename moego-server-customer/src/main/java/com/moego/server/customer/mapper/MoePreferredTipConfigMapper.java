package com.moego.server.customer.mapper;

import com.moego.server.customer.mapperbean.MoePreferredTipConfig;

public interface MoePreferredTipConfigMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_preferred_tip_config
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_preferred_tip_config
     *
     * @mbg.generated
     */
    int insert(MoePreferredTipConfig record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_preferred_tip_config
     *
     * @mbg.generated
     */
    int insertSelective(MoePreferredTipConfig record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_preferred_tip_config
     *
     * @mbg.generated
     */
    MoePreferredTipConfig selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_preferred_tip_config
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(MoePreferredTipConfig record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_preferred_tip_config
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(MoePreferredTipConfig record);

    MoePreferredTipConfig getByCustomerId(Integer customerId);
}
