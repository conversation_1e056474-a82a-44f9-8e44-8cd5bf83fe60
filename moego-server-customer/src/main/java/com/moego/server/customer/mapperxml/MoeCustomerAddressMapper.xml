<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.moego.server.customer.mapper.MoeCustomerAddressMapper">
    <resultMap id="BaseResultMap" type="com.moego.server.customer.mapperbean.MoeCustomerAddress">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="customer_id" jdbcType="INTEGER" property="customerId"/>
        <result column="business_id" jdbcType="INTEGER" property="businessId"/>
        <result column="address1" jdbcType="VARCHAR" property="address1"/>
        <result column="address2" jdbcType="VARCHAR" property="address2"/>
        <result column="city" jdbcType="VARCHAR" property="city"/>
        <result column="state" jdbcType="VARCHAR" property="state"/>
        <result column="zipcode" jdbcType="VARCHAR" property="zipcode"/>
        <result column="country" jdbcType="VARCHAR" property="country"/>
        <result column="lat" jdbcType="VARCHAR" property="lat"/>
        <result column="lng" jdbcType="VARCHAR" property="lng"/>
        <result column="status" jdbcType="TINYINT" property="status"/>
        <result column="is_primary" jdbcType="TINYINT" property="isPrimary"/>
        <result column="create_time" jdbcType="BIGINT" property="createTime"/>
        <result column="update_time" jdbcType="BIGINT" property="updateTime"/>
        <result column="company_id" jdbcType="BIGINT" property="companyId"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        id, customer_id, business_id, address1, address2, city, state, zipcode, country,
        lat, lng, status, is_primary, create_time, update_time, company_id
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        select
        <include refid="Base_Column_List"/>
        from moe_customer_address
        where id = #{id,jdbcType=INTEGER}
    </select>
    <insert id="insertSelective" parameterType="com.moego.server.customer.mapperbean.MoeCustomerAddress">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
            SELECT LAST_INSERT_ID()
        </selectKey>
        insert into moe_customer_address
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="customerId != null">
                customer_id,
            </if>
            <if test="businessId != null">
                business_id,
            </if>
            <if test="address1 != null">
                address1,
            </if>
            <if test="address2 != null">
                address2,
            </if>
            <if test="city != null">
                city,
            </if>
            <if test="state != null">
                state,
            </if>
            <if test="zipcode != null">
                zipcode,
            </if>
            <if test="country != null">
                country,
            </if>
            <if test="lat != null">
                lat,
            </if>
            <if test="lng != null">
                lng,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="isPrimary != null">
                is_primary,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="companyId != null">
                company_id,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="customerId != null">
                #{customerId,jdbcType=INTEGER},
            </if>
            <if test="businessId != null">
                #{businessId,jdbcType=INTEGER},
            </if>
            <if test="address1 != null">
                #{address1,jdbcType=VARCHAR},
            </if>
            <if test="address2 != null">
                #{address2,jdbcType=VARCHAR},
            </if>
            <if test="city != null">
                #{city,jdbcType=VARCHAR},
            </if>
            <if test="state != null">
                #{state,jdbcType=VARCHAR},
            </if>
            <if test="zipcode != null">
                #{zipcode,jdbcType=VARCHAR},
            </if>
            <if test="country != null">
                #{country,jdbcType=VARCHAR},
            </if>
            <if test="lat != null">
                #{lat,jdbcType=VARCHAR},
            </if>
            <if test="lng != null">
                #{lng,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                #{status,jdbcType=TINYINT},
            </if>
            <if test="isPrimary != null">
                #{isPrimary,jdbcType=TINYINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=BIGINT},
            </if>
            <if test="companyId != null">
                #{companyId,jdbcType=BIGINT},
            </if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="com.moego.server.customer.mapperbean.MoeCustomerAddress">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        update moe_customer_address
        <set>
            <if test="customerId != null">
                customer_id = #{customerId,jdbcType=INTEGER},
            </if>
            <if test="businessId != null">
                business_id = #{businessId,jdbcType=INTEGER},
            </if>
            <if test="address1 != null">
                address1 = #{address1,jdbcType=VARCHAR},
            </if>
            <if test="address2 != null">
                address2 = #{address2,jdbcType=VARCHAR},
            </if>
            <if test="city != null">
                city = #{city,jdbcType=VARCHAR},
            </if>
            <if test="state != null">
                state = #{state,jdbcType=VARCHAR},
            </if>
            <if test="zipcode != null">
                zipcode = #{zipcode,jdbcType=VARCHAR},
            </if>
            <if test="country != null">
                country = #{country,jdbcType=VARCHAR},
            </if>
            <if test="lat != null">
                lat = #{lat,jdbcType=VARCHAR},
            </if>
            <if test="lng != null">
                lng = #{lng,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=TINYINT},
            </if>
            <if test="isPrimary != null">
                is_primary = #{isPrimary,jdbcType=TINYINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=BIGINT},
            </if>
            <if test="companyId != null">
                company_id = #{companyId,jdbcType=BIGINT},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>

    <select id="listCustomerAddresses" resultMap="BaseResultMap">
        select
        id, address1, address2, city, state, zipcode, country,
        lat, lng, status, is_primary, business_id, customer_id
        FROM moe_customer_address
        WHERE customer_id = #{customerId} and status=1
    </select>

    <select id="queryCustomerIdByKeyword" resultType="int">
        select customer_id
        from moe_customer_address address
        left join moe_business_customer customer on address.customer_id = customer.id
        where CONCAT(address1,' ',address2,', ',city,', ',state,', ',country,', ',zipcode) LIKE
        concat('%',#{keyword},'%')
        AND address.status = 1
        AND customer.status = 1
        AND address.company_id = #{companyId}
        <if test="businessIds != null and businessIds.size() > 0">
          AND address.business_id IN
          <foreach collection="businessIds" item="businessId" open="(" close=")" separator=",">
            #{businessId,jdbcType=INTEGER}
          </foreach>
        </if>
    </select>

    <select id="queryCustomerIdByLatAndLng" resultType="int">
        SELECT customer_id
        FROM moe_customer_address
        where status = 1
        and is_primary = 1
        and company_id = #{companyId}
        <if test="businessIds != null and businessIds.size() > 0">
          and business_id in
          <foreach collection="businessIds" item="businessId" open="(" close=")" separator=",">
            #{businessId}
          </foreach>
        </if>
         and  ROUND(6378.138 * 2 * ASIN(SQRT(POW(SIN(( #{lat} * PI() / 180 - lat * PI() / 180) / 2),2) + COS(#{lat} * PI() / 180) * COS(lat * PI() / 180) * POW(SIN((#{lng} * PI() / 180 - lng * PI() / 180) / 2),2))) * 1000) &lt;= #{radius}
    </select>

  <select id="listCustomerIdByFilter" resultType="int">
    <foreach collection="clientsFilter.filters" item="filter">
    SELECT
        DISTINCT mbc.id
    FROM
        moe_business_customer mbc
    LEFT JOIN
        moe_customer_address mca
    ON mbc.id = mca.customer_id
       AND mca.zipcode IN
       <foreach collection="filter.values" item="value" open="(" separator="," close=")">
         #{value}
       </foreach>
       AND mca.status = 1
    WHERE
      mbc.company_id = #{clientsFilter.companyId}
      <if test="clientsFilter.preferredBusinessIds != null and clientsFilter.preferredBusinessIds.size() > 0">
        AND mbc.business_id IN
        <foreach collection="clientsFilter.preferredBusinessIds" item="businessId" open="(" close=")" separator=",">
          #{businessId,jdbcType=INTEGER}
        </foreach>
      </if>

      <if test="clientsFilter.customerIds != null and clientsFilter.customerIds.size > 0">
        AND mbc.id IN
        <foreach collection="clientsFilter.customerIds" item="customerId" open="(" separator="," close=")">
          #{customerId}
        </foreach>
      </if>
      AND mbc.status = 1
      AND mca.customer_id IS
      <if test="filter.operator == @com.moego.common.enums.filter.OperatorEnum@OPERATOR_IN">
        NOT NULL
      </if>
      <if test="filter.operator == @com.moego.common.enums.filter.OperatorEnum@OPERATOR_NOT_IN">
        NULL
      </if>
    </foreach>
  </select>

  <select id="listCustomerIdByCountFilter" resultType="int">
    SELECT
    mbc.id
    FROM moe_business_customer mbc
    LEFT JOIN moe_customer_address mca
    ON mbc.id = mca.customer_id
    AND mca.status = 1
    WHERE mbc.status = 1
    AND mbc.company_id = #{clientsFilter.companyId}
    <if test="clientsFilter.preferredBusinessIds != null and clientsFilter.preferredBusinessIds.size() > 0">
      AND mbc.business_id IN
      <foreach collection="clientsFilter.preferredBusinessIds" item="businessId" open="(" close=")" separator=",">
        #{businessId,jdbcType=INTEGER}
      </foreach>
    </if>

    <if test="clientsFilter.customerIds != null and clientsFilter.customerIds.size > 0">
      AND mbc.id IN
      <foreach collection="clientsFilter.customerIds" item="customerId" open="(" separator="," close=")">
        #{customerId}
      </foreach>
    </if>
    GROUP BY mbc.id
    <if test="clientsFilter.connector != null and clientsFilter.filters != null and clientsFilter.filters.size > 0">
      HAVING
      <include refid="com.moego.server.customer.mapper.MoeBusinessCustomerMapper.criteriaFilter" />
    </if>
  </select>
  <select id="findAddressIdsByKeyword" resultType="java.lang.Integer">
      select
          id
      from
          moe_customer_address
      <where>
          <if test="param.keyword != null and param.keyword != ''">
              concat(address1, address2, city, state, country, zipcode) LIKE concat('%',#{param.keyword},'%')
          </if>
          <if test="param.businessId != null">
              and business_id = #{param.businessId}
          </if>
          <if test="param.addressIds != null and param.addressIds.size > 0">
              and id in
              <foreach collection="param.addressIds" item="addressId" open="(" separator="," close=")">
                  #{addressId}
              </foreach>
          </if>
      </where>
  </select>
</mapper>
