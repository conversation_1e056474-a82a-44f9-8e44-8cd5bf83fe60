package com.moego.server.customer.mapperbean;

public class MoeCustomerContact {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_customer_contact.id
     *
     * @mbg.generated
     */
    private Integer id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_customer_contact.business_id
     *
     * @mbg.generated
     */
    private Integer businessId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_customer_contact.customer_id
     *
     * @mbg.generated
     */
    private Integer customerId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_customer_contact.first_name
     *
     * @mbg.generated
     */
    private String firstName;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_customer_contact.last_name
     *
     * @mbg.generated
     */
    private String lastName;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_customer_contact.phone_number
     *
     * @mbg.generated
     */
    private String phoneNumber;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_customer_contact.email
     *
     * @mbg.generated
     */
    private String email;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_customer_contact.title
     *
     * @mbg.generated
     */
    private String title;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_customer_contact.type
     *
     * @mbg.generated
     */
    private Byte type;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_customer_contact.is_primary
     *
     * @mbg.generated
     */
    private Byte isPrimary;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_customer_contact.status
     *
     * @mbg.generated
     */
    private Byte status;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_customer_contact.create_time
     *
     * @mbg.generated
     */
    private Long createTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_customer_contact.update_time
     *
     * @mbg.generated
     */
    private Long updateTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_customer_contact.company_id
     *
     * @mbg.generated
     */
    private Long companyId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_customer_contact.e164_phone_number
     *
     * @mbg.generated
     */
    private String e164PhoneNumber;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_customer_contact.id
     *
     * @return the value of moe_customer_contact.id
     *
     * @mbg.generated
     */
    public Integer getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_customer_contact.id
     *
     * @param id the value for moe_customer_contact.id
     *
     * @mbg.generated
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_customer_contact.business_id
     *
     * @return the value of moe_customer_contact.business_id
     *
     * @mbg.generated
     */
    public Integer getBusinessId() {
        return businessId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_customer_contact.business_id
     *
     * @param businessId the value for moe_customer_contact.business_id
     *
     * @mbg.generated
     */
    public void setBusinessId(Integer businessId) {
        this.businessId = businessId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_customer_contact.customer_id
     *
     * @return the value of moe_customer_contact.customer_id
     *
     * @mbg.generated
     */
    public Integer getCustomerId() {
        return customerId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_customer_contact.customer_id
     *
     * @param customerId the value for moe_customer_contact.customer_id
     *
     * @mbg.generated
     */
    public void setCustomerId(Integer customerId) {
        this.customerId = customerId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_customer_contact.first_name
     *
     * @return the value of moe_customer_contact.first_name
     *
     * @mbg.generated
     */
    public String getFirstName() {
        return firstName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_customer_contact.first_name
     *
     * @param firstName the value for moe_customer_contact.first_name
     *
     * @mbg.generated
     */
    public void setFirstName(String firstName) {
        this.firstName = firstName == null ? null : firstName.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_customer_contact.last_name
     *
     * @return the value of moe_customer_contact.last_name
     *
     * @mbg.generated
     */
    public String getLastName() {
        return lastName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_customer_contact.last_name
     *
     * @param lastName the value for moe_customer_contact.last_name
     *
     * @mbg.generated
     */
    public void setLastName(String lastName) {
        this.lastName = lastName == null ? null : lastName.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_customer_contact.phone_number
     *
     * @return the value of moe_customer_contact.phone_number
     *
     * @mbg.generated
     */
    public String getPhoneNumber() {
        return phoneNumber;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_customer_contact.phone_number
     *
     * @param phoneNumber the value for moe_customer_contact.phone_number
     *
     * @mbg.generated
     */
    public void setPhoneNumber(String phoneNumber) {
        this.phoneNumber = phoneNumber == null ? null : phoneNumber.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_customer_contact.email
     *
     * @return the value of moe_customer_contact.email
     *
     * @mbg.generated
     */
    public String getEmail() {
        return email;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_customer_contact.email
     *
     * @param email the value for moe_customer_contact.email
     *
     * @mbg.generated
     */
    public void setEmail(String email) {
        this.email = email == null ? null : email.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_customer_contact.title
     *
     * @return the value of moe_customer_contact.title
     *
     * @mbg.generated
     */
    public String getTitle() {
        return title;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_customer_contact.title
     *
     * @param title the value for moe_customer_contact.title
     *
     * @mbg.generated
     */
    public void setTitle(String title) {
        this.title = title == null ? null : title.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_customer_contact.type
     *
     * @return the value of moe_customer_contact.type
     *
     * @mbg.generated
     */
    public Byte getType() {
        return type;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_customer_contact.type
     *
     * @param type the value for moe_customer_contact.type
     *
     * @mbg.generated
     */
    public void setType(Byte type) {
        this.type = type;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_customer_contact.is_primary
     *
     * @return the value of moe_customer_contact.is_primary
     *
     * @mbg.generated
     */
    public Byte getIsPrimary() {
        return isPrimary;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_customer_contact.is_primary
     *
     * @param isPrimary the value for moe_customer_contact.is_primary
     *
     * @mbg.generated
     */
    public void setIsPrimary(Byte isPrimary) {
        this.isPrimary = isPrimary;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_customer_contact.status
     *
     * @return the value of moe_customer_contact.status
     *
     * @mbg.generated
     */
    public Byte getStatus() {
        return status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_customer_contact.status
     *
     * @param status the value for moe_customer_contact.status
     *
     * @mbg.generated
     */
    public void setStatus(Byte status) {
        this.status = status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_customer_contact.create_time
     *
     * @return the value of moe_customer_contact.create_time
     *
     * @mbg.generated
     */
    public Long getCreateTime() {
        return createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_customer_contact.create_time
     *
     * @param createTime the value for moe_customer_contact.create_time
     *
     * @mbg.generated
     */
    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_customer_contact.update_time
     *
     * @return the value of moe_customer_contact.update_time
     *
     * @mbg.generated
     */
    public Long getUpdateTime() {
        return updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_customer_contact.update_time
     *
     * @param updateTime the value for moe_customer_contact.update_time
     *
     * @mbg.generated
     */
    public void setUpdateTime(Long updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_customer_contact.company_id
     *
     * @return the value of moe_customer_contact.company_id
     *
     * @mbg.generated
     */
    public Long getCompanyId() {
        return companyId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_customer_contact.company_id
     *
     * @param companyId the value for moe_customer_contact.company_id
     *
     * @mbg.generated
     */
    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_customer_contact.e164_phone_number
     *
     * @return the value of moe_customer_contact.e164_phone_number
     *
     * @mbg.generated
     */
    public String getE164PhoneNumber() {
        return e164PhoneNumber;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_customer_contact.e164_phone_number
     *
     * @param e164PhoneNumber the value for moe_customer_contact.e164_phone_number
     *
     * @mbg.generated
     */
    public void setE164PhoneNumber(String e164PhoneNumber) {
        this.e164PhoneNumber = e164PhoneNumber == null ? null : e164PhoneNumber.trim();
    }
}
