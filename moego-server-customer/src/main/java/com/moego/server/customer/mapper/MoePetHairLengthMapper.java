package com.moego.server.customer.mapper;

import com.moego.common.dto.SortDto;
import com.moego.server.customer.dto.PetHairLengthListDTO;
import com.moego.server.customer.mapperbean.MoePetHairLength;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface MoePetHairLengthMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_pet_hair_length
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_pet_hair_length
     *
     * @mbg.generated
     */
    int insert(MoePetHairLength record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_pet_hair_length
     *
     * @mbg.generated
     */
    MoePetHairLength selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_pet_hair_length
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(MoePetHairLength record);

    int updateByPrimaryKeySelective(MoePetHairLength record);

    Integer addMoePetHairLength(MoePetHairLength moePetHairLength);

    Integer deleteMoePetHairLength(@Param("id") Integer id);

    Integer sortMoePetHairLength(@Param("businessId") Integer businessId, @Param("sortDtos") List<SortDto> sortDtos);

    List<MoePetHairLength> selectByBusinessId(@Param("businessId") Integer businessId);

    List<PetHairLengthListDTO> queryMoePetHairLength(
            @Param("businessId") Integer tokenBusinessId, @Param("name") String name);

    MoePetHairLength selectHairLengthByName(@Param("businessId") Integer tokenBusinessId, @Param("name") String name);
}
