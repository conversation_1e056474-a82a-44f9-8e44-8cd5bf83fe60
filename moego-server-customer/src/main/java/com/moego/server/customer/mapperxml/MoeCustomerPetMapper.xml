<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.moego.server.customer.mapper.MoeCustomerPetMapper">
  <resultMap id="BaseResultMap" type="com.moego.server.customer.mapperbean.MoeCustomerPet">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="business_id" jdbcType="INTEGER" property="businessId" />
    <result column="customer_id" jdbcType="INTEGER" property="customerId" />
    <result column="pet_name" jdbcType="VARCHAR" property="petName" />
    <result column="pet_type_id" jdbcType="INTEGER" property="petTypeId" />
    <result column="avatar_path" jdbcType="VARCHAR" property="avatarPath" />
    <result column="breed" jdbcType="VARCHAR" property="breed" />
    <result column="breed_mix" jdbcType="TINYINT" property="breedMix" />
    <result column="birthday" jdbcType="VARCHAR" property="birthday" />
    <result column="gender" jdbcType="TINYINT" property="gender" />
    <result column="hair_length" jdbcType="VARCHAR" property="hairLength" />
    <result column="behavior" jdbcType="VARCHAR" property="behavior" />
    <result column="weight" jdbcType="VARCHAR" property="weight" />
    <result column="fixed" jdbcType="VARCHAR" property="fixed" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="life_status" jdbcType="TINYINT" property="lifeStatus" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="update_time" jdbcType="BIGINT" property="updateTime" />
    <result column="expiry_notification" jdbcType="TINYINT" property="expiryNotification" />
    <result column="last_visit" jdbcType="VARCHAR" property="lastVisit" />
    <result column="last_addons_ids" jdbcType="VARCHAR" property="lastAddonsIds" />
    <result column="last_service_ids" jdbcType="VARCHAR" property="lastServiceIds" />
    <result column="vet_name" jdbcType="VARCHAR" property="vetName" />
    <result column="vet_phone" jdbcType="VARCHAR" property="vetPhone" />
    <result column="vet_address" jdbcType="VARCHAR" property="vetAddress" />
    <result column="emergency_contact_name" jdbcType="VARCHAR" property="emergencyContactName" />
    <result column="emergency_contact_phone" jdbcType="VARCHAR" property="emergencyContactPhone" />
    <result column="health_issues" jdbcType="VARCHAR" property="healthIssues" />
    <result column="pet_id" jdbcType="BIGINT" property="petId" />
    <result column="company_id" jdbcType="BIGINT" property="companyId" />
    <result column="evaluation_status" jdbcType="TINYINT" property="evaluationStatus" />
    <result column="pet_appearance_notes" jdbcType="VARCHAR" property="petAppearanceNotes" />
    <result column="pet_appearance_color" jdbcType="VARCHAR" property="petAppearanceColor" />
    <result column="playgroup_id" jdbcType="BIGINT" property="playgroupId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, business_id, customer_id, pet_name, pet_type_id, avatar_path, breed, breed_mix,
    birthday, gender, hair_length, behavior, weight, fixed, status, life_status, create_time,
    update_time, expiry_notification, last_visit, last_addons_ids, last_service_ids,
    vet_name, vet_phone, vet_address, emergency_contact_name, emergency_contact_phone,
    health_issues, pet_id, company_id, evaluation_status, pet_appearance_notes, pet_appearance_color,
    playgroup_id
  </sql>
  <select id="selectByExample" parameterType="com.moego.server.customer.mapperbean.MoeCustomerPetExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from moe_customer_pet
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <include refid="Base_Column_List" />
    from moe_customer_pet
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from moe_customer_pet
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.moego.server.customer.mapperbean.MoeCustomerPet">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into moe_customer_pet (business_id, customer_id, pet_name,
      pet_type_id, avatar_path, breed,
      breed_mix, birthday, gender,
      hair_length, behavior, weight,
      fixed, status, life_status,
      create_time, update_time, expiry_notification,
      last_visit, last_addons_ids, last_service_ids,
      vet_name, vet_phone, vet_address,
      emergency_contact_name, emergency_contact_phone,
      health_issues, pet_id, company_id,
      evaluation_status, pet_appearance_notes, pet_appearance_color,
      playgroup_id)
    values (#{businessId,jdbcType=INTEGER}, #{customerId,jdbcType=INTEGER}, #{petName,jdbcType=VARCHAR},
      #{petTypeId,jdbcType=INTEGER}, #{avatarPath,jdbcType=VARCHAR}, #{breed,jdbcType=VARCHAR},
      #{breedMix,jdbcType=TINYINT}, #{birthday,jdbcType=VARCHAR}, #{gender,jdbcType=TINYINT},
      #{hairLength,jdbcType=VARCHAR}, #{behavior,jdbcType=VARCHAR}, #{weight,jdbcType=VARCHAR},
      #{fixed,jdbcType=VARCHAR}, #{status,jdbcType=TINYINT}, #{lifeStatus,jdbcType=TINYINT},
      #{createTime,jdbcType=BIGINT}, #{updateTime,jdbcType=BIGINT}, #{expiryNotification,jdbcType=TINYINT},
      #{lastVisit,jdbcType=VARCHAR}, #{lastAddonsIds,jdbcType=VARCHAR}, #{lastServiceIds,jdbcType=VARCHAR},
      #{vetName,jdbcType=VARCHAR}, #{vetPhone,jdbcType=VARCHAR}, #{vetAddress,jdbcType=VARCHAR},
      #{emergencyContactName,jdbcType=VARCHAR}, #{emergencyContactPhone,jdbcType=VARCHAR},
      #{healthIssues,jdbcType=VARCHAR}, #{petId,jdbcType=BIGINT}, #{companyId,jdbcType=BIGINT},
      #{evaluationStatus,jdbcType=TINYINT}, #{petAppearanceNotes,jdbcType=VARCHAR}, #{petAppearanceColor,jdbcType=VARCHAR},
      #{playgroupId,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" parameterType="com.moego.server.customer.mapperbean.MoeCustomerPet">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into moe_customer_pet
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="businessId != null">
        business_id,
      </if>
      <if test="customerId != null">
        customer_id,
      </if>
      <if test="petName != null">
        pet_name,
      </if>
      <if test="petTypeId != null">
        pet_type_id,
      </if>
      <if test="avatarPath != null">
        avatar_path,
      </if>
      <if test="breed != null">
        breed,
      </if>
      <if test="breedMix != null">
        breed_mix,
      </if>
      <if test="birthday != null">
        birthday,
      </if>
      <if test="gender != null">
        gender,
      </if>
      <if test="hairLength != null">
        hair_length,
      </if>
      <if test="behavior != null">
        behavior,
      </if>
      <if test="weight != null">
        weight,
      </if>
      <if test="fixed != null">
        fixed,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="lifeStatus != null">
        life_status,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="expiryNotification != null">
        expiry_notification,
      </if>
      <if test="lastVisit != null">
        last_visit,
      </if>
      <if test="lastAddonsIds != null">
        last_addons_ids,
      </if>
      <if test="lastServiceIds != null">
        last_service_ids,
      </if>
      <if test="vetName != null">
        vet_name,
      </if>
      <if test="vetPhone != null">
        vet_phone,
      </if>
      <if test="vetAddress != null">
        vet_address,
      </if>
      <if test="emergencyContactName != null">
        emergency_contact_name,
      </if>
      <if test="emergencyContactPhone != null">
        emergency_contact_phone,
      </if>
      <if test="healthIssues != null">
        health_issues,
      </if>
      <if test="petId != null">
        pet_id,
      </if>
      <if test="companyId != null">
        company_id,
      </if>
      <if test="evaluationStatus != null">
        evaluation_status,
      </if>
      <if test="petAppearanceNotes != null">
        pet_appearance_notes,
      </if>
      <if test="petAppearanceColor != null">
        pet_appearance_color,
      </if>
      <if test="playgroupId != null">
        playgroup_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="businessId != null">
        #{businessId,jdbcType=INTEGER},
      </if>
      <if test="customerId != null">
        #{customerId,jdbcType=INTEGER},
      </if>
      <if test="petName != null">
        #{petName,jdbcType=VARCHAR},
      </if>
      <if test="petTypeId != null">
        #{petTypeId,jdbcType=INTEGER},
      </if>
      <if test="avatarPath != null">
        #{avatarPath,jdbcType=VARCHAR},
      </if>
      <if test="breed != null">
        #{breed,jdbcType=VARCHAR},
      </if>
      <if test="breedMix != null">
        #{breedMix,jdbcType=TINYINT},
      </if>
      <if test="birthday != null">
        #{birthday,jdbcType=VARCHAR},
      </if>
      <if test="gender != null">
        #{gender,jdbcType=TINYINT},
      </if>
      <if test="hairLength != null">
        #{hairLength,jdbcType=VARCHAR},
      </if>
      <if test="behavior != null">
        #{behavior,jdbcType=VARCHAR},
      </if>
      <if test="weight != null">
        #{weight,jdbcType=VARCHAR},
      </if>
      <if test="fixed != null">
        #{fixed,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="lifeStatus != null">
        #{lifeStatus,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=BIGINT},
      </if>
      <if test="expiryNotification != null">
        #{expiryNotification,jdbcType=TINYINT},
      </if>
      <if test="lastVisit != null">
        #{lastVisit,jdbcType=VARCHAR},
      </if>
      <if test="lastAddonsIds != null">
        #{lastAddonsIds,jdbcType=VARCHAR},
      </if>
      <if test="lastServiceIds != null">
        #{lastServiceIds,jdbcType=VARCHAR},
      </if>
      <if test="vetName != null">
        #{vetName,jdbcType=VARCHAR},
      </if>
      <if test="vetPhone != null">
        #{vetPhone,jdbcType=VARCHAR},
      </if>
      <if test="vetAddress != null">
        #{vetAddress,jdbcType=VARCHAR},
      </if>
      <if test="emergencyContactName != null">
        #{emergencyContactName,jdbcType=VARCHAR},
      </if>
      <if test="emergencyContactPhone != null">
        #{emergencyContactPhone,jdbcType=VARCHAR},
      </if>
      <if test="healthIssues != null">
        #{healthIssues,jdbcType=VARCHAR},
      </if>
      <if test="petId != null">
        #{petId,jdbcType=BIGINT},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=BIGINT},
      </if>
      <if test="evaluationStatus != null">
        #{evaluationStatus,jdbcType=TINYINT},
      </if>
      <if test="petAppearanceNotes != null">
        #{petAppearanceNotes,jdbcType=VARCHAR},
      </if>
      <if test="petAppearanceColor != null">
        #{petAppearanceColor,jdbcType=VARCHAR},
      </if>
      <if test="playgroupId != null">
        #{playgroupId,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.moego.server.customer.mapperbean.MoeCustomerPet">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_customer_pet
    <set>
      <if test="businessId != null">
        business_id = #{businessId,jdbcType=INTEGER},
      </if>
      <if test="customerId != null">
        customer_id = #{customerId,jdbcType=INTEGER},
      </if>
      <if test="petName != null">
        pet_name = #{petName,jdbcType=VARCHAR},
      </if>
      <if test="petTypeId != null">
        pet_type_id = #{petTypeId,jdbcType=INTEGER},
      </if>
      <if test="avatarPath != null">
        avatar_path = #{avatarPath,jdbcType=VARCHAR},
      </if>
      <if test="breed != null">
        breed = #{breed,jdbcType=VARCHAR},
      </if>
      <if test="breedMix != null">
        breed_mix = #{breedMix,jdbcType=TINYINT},
      </if>
      <if test="birthday != null">
        birthday = #{birthday,jdbcType=VARCHAR},
      </if>
      <if test="gender != null">
        gender = #{gender,jdbcType=TINYINT},
      </if>
      <if test="hairLength != null">
        hair_length = #{hairLength,jdbcType=VARCHAR},
      </if>
      <if test="behavior != null">
        behavior = #{behavior,jdbcType=VARCHAR},
      </if>
      <if test="weight != null">
        weight = #{weight,jdbcType=VARCHAR},
      </if>
      <if test="fixed != null">
        fixed = #{fixed,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="lifeStatus != null">
        life_status = #{lifeStatus,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=BIGINT},
      </if>
      <if test="expiryNotification != null">
        expiry_notification = #{expiryNotification,jdbcType=TINYINT},
      </if>
      <if test="lastVisit != null">
        last_visit = #{lastVisit,jdbcType=VARCHAR},
      </if>
      <if test="lastAddonsIds != null">
        last_addons_ids = #{lastAddonsIds,jdbcType=VARCHAR},
      </if>
      <if test="lastServiceIds != null">
        last_service_ids = #{lastServiceIds,jdbcType=VARCHAR},
      </if>
      <if test="vetName != null">
        vet_name = #{vetName,jdbcType=VARCHAR},
      </if>
      <if test="vetPhone != null">
        vet_phone = #{vetPhone,jdbcType=VARCHAR},
      </if>
      <if test="vetAddress != null">
        vet_address = #{vetAddress,jdbcType=VARCHAR},
      </if>
      <if test="emergencyContactName != null">
        emergency_contact_name = #{emergencyContactName,jdbcType=VARCHAR},
      </if>
      <if test="emergencyContactPhone != null">
        emergency_contact_phone = #{emergencyContactPhone,jdbcType=VARCHAR},
      </if>
      <if test="healthIssues != null">
        health_issues = #{healthIssues,jdbcType=VARCHAR},
      </if>
      <if test="petId != null">
        pet_id = #{petId,jdbcType=BIGINT},
      </if>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=BIGINT},
      </if>
      <if test="evaluationStatus != null">
        evaluation_status = #{evaluationStatus,jdbcType=TINYINT},
      </if>
      <if test="petAppearanceNotes != null">
        pet_appearance_notes = #{petAppearanceNotes,jdbcType=VARCHAR},
      </if>
      <if test="petAppearanceColor != null">
        pet_appearance_color = #{petAppearanceColor,jdbcType=VARCHAR},
      </if>
      <if test="playgroupId != null">
        playgroup_id = #{playgroupId,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.moego.server.customer.mapperbean.MoeCustomerPet">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_customer_pet
    set business_id = #{businessId,jdbcType=INTEGER},
      customer_id = #{customerId,jdbcType=INTEGER},
      pet_name = #{petName,jdbcType=VARCHAR},
      pet_type_id = #{petTypeId,jdbcType=INTEGER},
      avatar_path = #{avatarPath,jdbcType=VARCHAR},
      breed = #{breed,jdbcType=VARCHAR},
      breed_mix = #{breedMix,jdbcType=TINYINT},
      birthday = #{birthday,jdbcType=VARCHAR},
      gender = #{gender,jdbcType=TINYINT},
      hair_length = #{hairLength,jdbcType=VARCHAR},
      behavior = #{behavior,jdbcType=VARCHAR},
      weight = #{weight,jdbcType=VARCHAR},
      fixed = #{fixed,jdbcType=VARCHAR},
      status = #{status,jdbcType=TINYINT},
      life_status = #{lifeStatus,jdbcType=TINYINT},
      create_time = #{createTime,jdbcType=BIGINT},
      update_time = #{updateTime,jdbcType=BIGINT},
      expiry_notification = #{expiryNotification,jdbcType=TINYINT},
      last_visit = #{lastVisit,jdbcType=VARCHAR},
      last_addons_ids = #{lastAddonsIds,jdbcType=VARCHAR},
      last_service_ids = #{lastServiceIds,jdbcType=VARCHAR},
      vet_name = #{vetName,jdbcType=VARCHAR},
      vet_phone = #{vetPhone,jdbcType=VARCHAR},
      vet_address = #{vetAddress,jdbcType=VARCHAR},
      emergency_contact_name = #{emergencyContactName,jdbcType=VARCHAR},
      emergency_contact_phone = #{emergencyContactPhone,jdbcType=VARCHAR},
      health_issues = #{healthIssues,jdbcType=VARCHAR},
      pet_id = #{petId,jdbcType=BIGINT},
      company_id = #{companyId,jdbcType=BIGINT},
      evaluation_status = #{evaluationStatus,jdbcType=TINYINT},
      pet_appearance_notes = #{petAppearanceNotes,jdbcType=VARCHAR},
      pet_appearance_color = #{petAppearanceColor,jdbcType=VARCHAR},
      playgroup_id = #{playgroupId,jdbcType=BIGINT}
    where id = #{id,jdbcType=INTEGER}
  </update>

  <resultMap id="PetReportMap" type="com.moego.server.customer.service.dto.ReportPet">
    <id column="id" jdbcType="INTEGER" property="petId" />
    <result column="business_id" jdbcType="INTEGER" property="businessId" />
    <result column="customer_id" jdbcType="INTEGER" property="customerId" />
    <result column="pet_name" jdbcType="VARCHAR" property="petName" />
    <result column="pet_type_id" jdbcType="INTEGER" property="petTypeId" />
    <result column="avatar_path" jdbcType="VARCHAR" property="avatarPath" />
    <result column="breed" jdbcType="VARCHAR" property="breed" />
    <result column="breed_mix" jdbcType="TINYINT" property="breedMix" />
    <result column="birthday" jdbcType="VARCHAR" property="birthday" />
    <result column="gender" jdbcType="TINYINT" property="gender" />
    <result column="hair_length" jdbcType="VARCHAR" property="hairLength" />
    <result column="behavior" jdbcType="VARCHAR" property="behavior" />
    <result column="weight" jdbcType="VARCHAR" property="weight" />
    <result column="fixed" jdbcType="VARCHAR" property="fixed" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="life_status" jdbcType="TINYINT" property="lifeStatus" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="update_time" jdbcType="BIGINT" property="updateTime" />
    <result column="expiry_notification" jdbcType="TINYINT" property="expiryNotification" />
    <result column="last_visit" jdbcType="VARCHAR" property="lastVisit" />
    <result column="last_addons_ids" jdbcType="VARCHAR" property="lastAddonsIds" />
    <result column="last_service_ids" jdbcType="VARCHAR" property="lastServiceIds" />
    <result column="vet_name" jdbcType="VARCHAR" property="vetName" />
    <result column="vet_phone" jdbcType="VARCHAR" property="vetPhone" />
    <result column="vet_address" jdbcType="VARCHAR" property="vetAddress" />
    <result column="emergency_contact_name" jdbcType="VARCHAR" property="emergencyContactName" />
    <result column="emergency_contact_phone" jdbcType="VARCHAR" property="emergencyContactPhone" />
    <result column="health_issues" jdbcType="VARCHAR" property="healthIssues" />

    <result column="first_name" jdbcType="VARCHAR" property="firstName" />
    <result column="last_name" jdbcType="VARCHAR" property="lastName" />

    <collection ofType="com.moego.server.customer.mapperbean.MoePetCode" property="petCodes">
      <result column="business_id" jdbcType="INTEGER" property="businessId" />
      <result column="code_number" jdbcType="VARCHAR" property="codeNumber" />
      <result column="description" jdbcType="VARCHAR" property="description" />
      <result column="color" jdbcType="VARCHAR" property="color" />
      <result column="sort" jdbcType="INTEGER" property="sort" />
      <result column="status" jdbcType="TINYINT" property="status" />
      <result column="create_time" jdbcType="BIGINT" property="createTime" />
      <result column="update_time" jdbcType="BIGINT" property="updateTime" />
    </collection>
  </resultMap>

  <resultMap id="PetVaccinationMap" type="com.moego.server.customer.service.dto.ReportPet">
    <id column="id" jdbcType="INTEGER" property="petId" />
    <result column="business_id" jdbcType="INTEGER" property="businessId" />
    <result column="customer_id" jdbcType="INTEGER" property="customerId" />
    <result column="pet_name" jdbcType="VARCHAR" property="petName" />
    <result column="vet_name" jdbcType="VARCHAR" property="vetName" />
    <result column="vet_phone" jdbcType="VARCHAR" property="vetPhone" />
    <result column="vet_address" jdbcType="VARCHAR" property="vetAddress" />
    <result column="emergency_contact_name" jdbcType="VARCHAR" property="emergencyContactName" />
    <result column="emergency_contact_phone" jdbcType="VARCHAR" property="emergencyContactPhone" />
    <result column="health_issues" jdbcType="VARCHAR" property="healthIssues" />

    <collection ofType="com.moego.server.customer.mapperbean.MoeCustomerContact" property="customerContacts">
      <result column="email" jdbcType="VARCHAR" property="email" />
      <result column="first_name" jdbcType="VARCHAR" property="firstName" />
      <result column="last_name" jdbcType="VARCHAR" property="lastName" />
      <result column="phone_number" jdbcType="VARCHAR" property="phoneNumber" />
      <result column="email" jdbcType="VARCHAR" property="email" />
    </collection>

    <collection ofType="com.moego.server.customer.service.dto.PetVaccineInfo" property="vaccines">
      <result column="name" jdbcType="VARCHAR" property="name" />
      <result column="business_id" jdbcType="INTEGER" property="businessId" />
      <result column="sort" jdbcType="INTEGER" property="sort" />
      <result column="pet_id" jdbcType="INTEGER" property="petId" />
      <result column="vaccine_id" jdbcType="INTEGER" property="vaccineId" />
      <result column="expiration_date" jdbcType="VARCHAR" property="expirationDate" />
      <result column="document_urls" jdbcType="VARCHAR" property="documentUrls" />
      <result column="status" jdbcType="TINYINT" property="status" />
      <result column="type" jdbcType="TINYINT" property="type" />
    </collection>
  </resultMap>

  <resultMap id="PetCodeCountMap" type="com.moego.server.customer.dto.ReportWebPet">
    <result column="code_number" jdbcType="INTEGER" property="petCode" />
    <result column="total_pets" jdbcType="VARCHAR" property="totalPets" />
  </resultMap>

  <sql id="Join_Column_List">
    cp.id, cp.business_id, cp.customer_id, cp.pet_name, cp.pet_type_id, cp.avatar_path, cp.breed,
    cp.breed_mix, cp.birthday, cp.gender, cp.hair_length, cp.behavior, cp.weight, cp.fixed,
    cp.status, cp.life_status, cp.create_time, cp.update_time, cp.expiry_notification, cp.last_visit,
    cp.last_addons_ids, cp.last_service_ids, cp.vet_name, cp.vet_phone, cp.vet_address,
    cp.emergency_contact_name, cp.emergency_contact_phone, cp.health_issues
  </sql>
    <update id="updatePetDeleteByCustomerId">
        update moe_customer_pet
        set status = 2,
        update_time = #{updateTime,jdbcType=BIGINT}
        where customer_id = #{customerId,jdbcType=INTEGER}
    </update>

    <select id="getCustomerPetByFindPetNameAndBreed" resultType="int">
        SELECT customer_id FROM `moe_customer_pet`
        where company_id = #{companyId}
        <if test="businessIds != null and businessIds.size() &gt; 0">
          AND business_id IN
          <foreach close=")" collection="businessIds" item="businessId" open="(" separator=",">
            #{businessId,jdbcType=INTEGER}
          </foreach>
        </if>
        AND (`pet_name` LIKE concat(concat('%',#{keyword}),'%') OR
        `breed` LIKE concat(concat('%',#{keyword}),'%'))
        AND `life_status` = 1 AND `status` = 1
    </select>

    <select id="getCustomerPetListWithLifeStatus" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM moe_customer_pet WHERE business_id = #{businessId} and customer_id = #{customerId} and status=1 and
        life_status=#{lifeStatus}
    </select>
    <select id="selectCustomerPetDetailDTO" resultType="com.moego.server.customer.dto.CustomerPetDetailDTO">
        SELECT
        pet.id as petId,
        pet.pet_name as petName,
        pet.pet_type_id as petTypeId,
        petType.type_name as typeName,
        pet.avatar_path as avatarPath,
        pet.breed as breed,
        pet.breed_mix as breedMix,
        pet.life_status as lifeStatus,
        pet.birthday,
        pet.gender,
        pet.hair_length as hairLength,
        pet.weight,
        pet.fixed,
        pet.behavior,
        pet.vet_name as vetName,
        pet.vet_phone as vetPhone,
        pet.vet_address as vetAddress,
        pet.emergency_contact_name as emergencyContactName,
        pet.emergency_contact_phone as emergencyContactPhone,
        pet.pet_appearance_color as petAppearanceColor,
        pet.pet_appearance_notes as petAppearanceNotes,
        pet.health_issues as healthIssues
        FROM moe_customer_pet pet
        left join moe_pet_type petType on petType.id=pet.pet_type_id
        WHERE pet.status=1
        and pet.company_id = #{companyId}
        <if test="businessId != null">
            AND pet.business_id = #{businessId}
        </if>
        and pet.customer_id = #{customerId}
    </select>

    <select id="selectCustomerPetDetailDTOById" resultType="com.moego.server.customer.dto.CustomerPetDetailDTO">
        SELECT
        pet.id AS petId,
        pet.pet_name AS petName,
        pet.pet_type_id AS petTypeId,
        pet.customer_id AS customerId,
        c.avatar_path AS ownerAvatarPath,
        petType.type_name AS typeName,
        pet.avatar_path AS avatarPath,
        pet.breed AS breed,
        pet.breed_mix AS breedMix,
        pet.life_status AS lifeStatus,
        pet.birthday,
        pet.gender,
        pet.hair_length AS hairLength,
        pet.expiry_notification AS expiryNotification,
        pet.weight,
        pet.fixed,
        pet.behavior,
        if(pet.status = 2, true, false) AS deleted,
        if(pet.life_status = 2, true, false) AS passedAway
        FROM
        moe_customer_pet pet
        LEFT JOIN moe_pet_type petType ON petType.id = pet.pet_type_id
        LEFT JOIN moe_business_customer c ON c.id = pet.customer_id
        WHERE pet.id = #{petId}
          AND pet.company_id = #{companyId}
          <if test="businessId != null">
            AND pet.business_id = #{businessId}
          </if>
    </select>
    <select id="selectPetNameBreedByCustomerId" resultType="com.moego.server.customer.service.dto.PetNameBreedDto">
        select
        id as petId,customer_id as customerId,pet_name as petName,breed,life_status AS lifeStatus,evaluation_status AS evaluationStatus
        FROM moe_customer_pet
        <where>
            status = 1
            AND life_status = 1
            and company_id = #{companyId}
            <if test="businessIds != null and !businessIds.isEmpty() ">
              and business_id in
              <foreach close=")" collection="businessIds" item="businessId" open="(" separator=",">
                #{businessId}
              </foreach>
            </if>
            AND customer_id IN
            <foreach close=")" collection="customerIds" item="item" open="(" separator=",">
                #{item}
            </foreach>
        </where>
    </select>
    <select id="selectPetByCustomerId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        FROM moe_customer_pet
        <where>
            status = 1
            AND customer_id IN
            <foreach close=")" collection="customerIds" item="item" open="(" separator=",">
                #{item}
            </foreach>
        </where>
    </select>
    <select id="listAllCustomerPetDetailDTOByIds" resultType="com.moego.server.customer.dto.CustomerPetDetailDTO">
        SELECT
        pet.id as petId,
        pet.pet_name as petName,
        pet.business_id as businessId,
        pet.pet_type_id as petTypeId,
        pet.customer_id as customerId,
        pet.avatar_path  as avatarPath,
        pet.breed as petBreed,
        petType.type_name as typeName,
        pet.avatar_path as avatarPath,
        pet.breed as breed,
        pet.breed_mix as breedMix,
        pet.life_status as lifeStatus,
        pet.birthday,
        pet.gender,
        pet.hair_length as hairLength,
        pet.weight,
        pet.fixed,
        pet.behavior,
        pet.expiry_notification as expiryNotification ,
        pet.status,
        pet.vet_name as vetName,
        pet.vet_phone as vetPhone,
        pet.vet_address as vetAddress,
        pet.emergency_contact_name  as emergencyContactName ,
        pet.emergency_contact_phone  as emergencyContactPhone ,
        pet.health_issues  as healthIssues,
        pet.pet_id as platformPetId,
        pet.company_id as companyId,
        pet.pet_appearance_color as petAppearanceColor,
        pet.pet_appearance_notes as petAppearanceNotes,
        pet.evaluation_status as evaluationStatus,
        pet.playgroup_id as playgroupId,
        if(pet.status = 2, true, false) AS deleted,
        if(pet.life_status = 2, true, false) AS passedAway
        FROM moe_customer_pet pet
        left join moe_pet_type petType on petType.id=pet.pet_type_id
        <where>
            <if test="petIds != null">
                AND pet.id IN
                <foreach close=")" collection="petIds" item="item" open="(" separator=",">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>
    <select id="getCustomerPetListByCustomerId" resultType="com.moego.server.customer.dto.CustomerPetDetailDTO">
      SELECT pet.id                      as petId,
             pet.pet_name                as petName,
             pet.business_id             as businessId,
             pet.pet_type_id             as petTypeId,
             pet.customer_id             as customerId,
             pet.avatar_path             as avatarPath,
             petType.type_name           as typeName,
             pet.avatar_path             as avatarPath,
             pet.breed                   as breed,
             pet.breed_mix               as breedMix,
             pet.life_status             as lifeStatus,
             pet.birthday,
             pet.gender,
             pet.hair_length             as hairLength,
             pet.weight,
             pet.fixed,
             pet.behavior,
             pet.expiry_notification     as expiryNotification,
             pet.status,
             pet.vet_name                as vetName,
             pet.vet_phone               as vetPhone,
             pet.vet_address             as vetAddress,
             pet.emergency_contact_name  as emergencyContactName,
             pet.emergency_contact_phone as emergencyContactPhone,
             pet.health_issues           as healthIssues
      FROM moe_customer_pet pet
             left join moe_pet_type petType on petType.id = pet.pet_type_id
      where pet.status = 1
        and pet.life_status = 1
        and pet.customer_id in
      <foreach close=")" collection="customerIdList" item="item" open="(" separator=",">
        #{item}
      </foreach>
    </select>
  <select id="listPetByCustomerId" resultMap="BaseResultMap">
    SELECT
        <include refid="Base_Column_List" />
    FROM
        moe_customer_pet
    WHERE status = 1
     AND life_status = 1
     AND company_id = #{companyId}
      <if test="customerId != null">
        AND customer_id = #{customerId}
      </if>
      <if test="petIds != null and petIds.size &gt; 0">
        AND id IN
        <foreach close=")" collection="petIds" item="item" open="(" separator=",">
          #{item}
        </foreach>
      </if>
  </select>
  <select id="listPetByCustomerIds" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List" />
    FROM
    moe_customer_pet
    WHERE status = 1
    AND life_status = 1
    AND company_id = #{companyId}
    AND customer_id IN
    <foreach close=")" collection="customerIds" item="customerId" open="(" separator=",">
      #{customerId}
    </foreach>
  </select>
    <select id="selectPetByIdList" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        from moe_customer_pet
        <where>
            <if test="petIds != null">
                id IN
                <foreach close=")" collection="petIds" item="item" open="(" separator=",">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>
    <select id="selectBusinessPetsByMonth" resultType="com.moego.server.customer.dto.CustomerPetReminderDTO">
        SELECT
        pet.customer_id as customerId,
        customer.first_name as customerFirstName,
        customer.last_name as customerLastName,
        pet.id as petId,
        pet.pet_name as petName,
        pet.pet_type_id as petTypeId,
        petType.type_name as typeName,
        pet.avatar_path as avatarPath,
        pet.breed as breed,
        pet.breed_mix as breedMix,
        pet.life_status as lifeStatus,
        pet.birthday,
        pet.gender,
        pet.hair_length as hairLength,
        pet.weight,
        pet.fixed,
        pet.behavior
        FROM moe_customer_pet pet
        left join moe_pet_type petType on petType.id=pet.pet_type_id
        left join moe_business_customer customer on pet.customer_id=customer.id
        where pet.business_id=#{businessId}
        and pet.status=1
        and customer.inactive=0
        and pet.life_status=1
        <if test="type ==1">
            and substring(REPLACE(pet.birthday,'/','-'), 6) between #{startDate} and #{endDate}
        </if>
        <if test="type ==2">
            and (substring(REPLACE(pet.birthday,'/','-'), 6) &gt;= #{startDate} or
                (substring(REPLACE(pet.birthday,'/','-'), 6) &lt;= #{endDate} and pet.birthday &gt;''))
        </if>
        <if test="dismissIds!=null and dismissIds.size()&gt;0">
            AND pet.id NOT IN
            <foreach close=")" collection="dismissIds" item="item" open="(" separator=",">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="queryCustomerPetByCustomerIds" parameterType="int" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from moe_customer_pet
        where status = 1
        and life_status =1
        and customer_id in
        <foreach close=")" collection="list" item="item" open="(" separator=",">
            #{item}
        </foreach>
        order by create_time desc
    </select>
    <select id="queryCustomerPetByCustomerId" parameterType="int" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from moe_customer_pet
        where status = 1
        and life_status =1
        and customer_id = #{customerId}
        order by create_time desc
    </select>

    <select id="selectNotificationBirthdayCustomerPet" resultType="com.moego.server.customer.dto.CustomerPetReminderDTO">
        SELECT
        pet.customer_id as customerId,
        customer.first_name as customerFirstName,
        customer.last_name as customerLastName,
        pet.id as petId,
        pet.pet_name as petName,
        pet.pet_type_id as petTypeId,
        petType.type_name as typeName,
        pet.avatar_path as avatarPath,
        pet.breed as breed,
        pet.breed_mix as breedMix,
        pet.life_status as lifeStatus,
        pet.birthday,
        pet.gender,
        pet.hair_length as hairLength,
        pet.weight,
        pet.fixed,
        pet.behavior
        FROM moe_customer_pet pet
        left join moe_pet_type petType on petType.id=pet.pet_type_id
        left join moe_business_customer customer on pet.customer_id=customer.id
        where pet.business_id=#{businessId}
        and pet.status=1
        and pet.life_status=1
        and customer.inactive = 0
        and customer.status = 1
        and RIGHT(REPLACE(pet.birthday,'/','-'),5) = RIGHT(#{birthday},5)
        <if test="dismissIds!=null and dismissIds.size()&gt;0">
            AND pet.id NOT IN
            <foreach close=")" collection="dismissIds" item="item" open="(" separator=",">
                #{item}
            </foreach>
        </if>
    </select>

  <select id="queryPetsReport" parameterType="int" resultMap="PetReportMap">
    SELECT bc.first_name, bc.last_name, pc.code_number,
    <include refid="Join_Column_List" />
    from moe_customer_pet cp
    join moe_business_customer bc
    on cp.customer_id = bc.id
    left join moe_pet_pet_code_binding cb
    on cp.id = cb.pet_id
    left join moe_pet_code pc
    on cb.pet_code_id = pc.id and pc.status = 1
    where cp.business_id = #{businessId}
    and cp.status = 1
  </select>

  <select id="queryPetVaccine" resultMap="PetVaccinationMap">
    SELECT cc.first_name, cc.last_name, cc.email, cc.phone_number, cp.pet_name, cp.id, cp.vet_name, cp.vet_phone, cp.vet_address,
    cp.emergency_contact_name, cp.emergency_contact_phone, cp.health_issues, pv.name, vb.expiration_date, vb.status, pv.status
    from moe_customer_pet cp
    left join moe_customer_contact cc
    on cp.customer_id = cc.customer_id and cc.is_primary = 1
    join moe_pet_pet_vaccine_binding vb
    on vb.pet_id = cp.id
    join moe_pet_vaccine pv
    on pv.id = vb.vaccine_id
    where cp.business_id = #{businessId}
    and cp.status = 1
    and vb.status = 1
    and pv.status = 1
    <if test="startDate != null">
      and vb.expiration_date &gt;= #{startDate}
    </if>
    <if test="endDate != null">
      and vb.expiration_date &lt;= #{endDate}
    </if>
  </select>

  <select id="queryPetsByBusinessId" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List" />
    FROM moe_customer_pet
    WHERE business_id = #{businessId}
    and status = 1
  </select>

  <select id="countPetCodes" resultMap="PetCodeCountMap">
    SELECT count(cp.id) as total_pets, pc.code_number
    from moe_customer_pet cp
    join moe_pet_pet_code_binding cb
    on cb.pet_id = cp.id
    join moe_pet_code pc
    on pc.id = cb.pet_code_id
    WHERE cp.business_id = #{businessId}
    and pc.status = 1
    GROUP by pc.code_number
  </select>

  <update id="updatePetId">
    update moe_customer_pet
    set pet_id = #{petId}
    where id = #{id}
  </update>

  <select id="getVaccineByCustomerId" resultMap="PetVaccinationMap">
    SELECT cp.id,
           pv.name,
           vb.expiration_date,
           vb.document_urls
    from moe_customer_pet cp
           join moe_pet_pet_vaccine_binding vb
                on vb.pet_id = cp.id
           join moe_pet_vaccine pv
                on pv.id = vb.vaccine_id
    where cp.customer_id = #{customerId}
      and cp.life_status = 1
      and cp.status = 1
      and vb.status = 1
      and pv.status = 1
  </select>

  <select id="countBetween" resultType="com.moego.server.customer.mapper.po.WebsitePetSummaryPO">
    select max(id) as `maxId`, count(*) as `count`
    from moe_customer_pet
    where 1 = 1
    <if test="startId != null">
      and id &gt;= #{startId}
    </if>
    <if test="endId != null">
      and id &lt;= #{endId}
    </if>
  </select>

  <select id="selectCustomerWithNotPassAwayPet" resultType="integer">
    select distinct customer_id
    from moe_customer_pet
    where
      company_id = #{companyId}
    <if test="businessIds != null and businessIds.size &gt; 0">
      and business_id in
      <foreach close=")" collection="businessIds" item="businessId" open="(" separator=",">
        #{businessId}
      </foreach>
    </if>
    <if test="customerIds != null and customerIds.size &gt; 0">
      and customer_id in
        <foreach close=")" collection="customerIds" item="customerId" open="(" separator=",">
          #{customerId}
        </foreach>
    </if>
    and status = 1
    and life_status = 1
  </select>


  <select id="listCustomerIdByFilter" resultType="int">
    SELECT
        DISTINCT customer_id
    FROM moe_customer_pet
    WHERE status = 1
    AND life_status = 1
    AND company_id = #{clientsFilter.companyId}
    <if test="clientsFilter.preferredBusinessIds != null and clientsFilter.preferredBusinessIds.size() &gt; 0">
      AND business_id IN
      <foreach close=")" collection="clientsFilter.preferredBusinessIds" item="businessId" open="(" separator=",">
        #{businessId,jdbcType=INTEGER}
      </foreach>
    </if>
    <if test="clientsFilter.customerIds != null and clientsFilter.customerIds.size &gt; 0">
      AND customer_id IN
      <foreach close=")" collection="clientsFilter.customerIds" item="customerId" open="(" separator=",">
        #{customerId}
      </foreach>
    </if>
    <if test="clientsFilter.connector != null and clientsFilter.filters != null and clientsFilter.filters.size &gt; 0">
      AND
      <include refid="com.moego.server.customer.mapper.MoeBusinessCustomerMapper.criteriaFilter" />
    </if>
  </select>

  <select id="listCustomerIdByCountFilter" resultType="int">
    SELECT
    mbc.id
    FROM moe_business_customer mbc
    LEFT JOIN moe_customer_pet mcp
    ON mbc.id = mcp.customer_id
    AND mcp.status = 1
    AND mcp.life_status = 1
    WHERE mbc.status = 1
      AND mbc.company_id = #{clientsFilter.companyId}
      <if test="clientsFilter.preferredBusinessIds != null and clientsFilter.preferredBusinessIds.size() &gt; 0">
        AND mbc.business_id IN
        <foreach close=")" collection="clientsFilter.preferredBusinessIds" item="businessId" open="(" separator=",">
          #{businessId,jdbcType=INTEGER}
        </foreach>
      </if>

    <if test="clientsFilter.customerIds != null and clientsFilter.customerIds.size &gt; 0">
      AND mbc.id IN
      <foreach close=")" collection="clientsFilter.customerIds" item="customerId" open="(" separator=",">
        #{customerId}
      </foreach>
    </if>
    GROUP BY mbc.id
    <if test="clientsFilter.connector != null and clientsFilter.filters != null and clientsFilter.filters.size &gt; 0">
      HAVING
      <include refid="com.moego.server.customer.mapper.MoeBusinessCustomerMapper.criteriaFilter" />
    </if>
  </select>

  <select id="countPetByCustomerIds" resultType="com.moego.server.customer.service.dto.PetCountTotalDto">
    SELECT
      mcp.pet_type_id petTypeId,
      mpt.type_name typeName,
      count(*) AS count
    FROM
      moe_customer_pet mcp
    LEFT JOIN moe_pet_type mpt
        ON mcp.pet_type_id = mpt.pet_type_id
        AND mpt.status = 1
    <if test="businessId != null">
      AND mcp.business_id = mpt.business_id
    </if>
    <if test="businessId == null">
      AND mcp.company_id = mpt.company_id
    </if>
    WHERE
      mcp.status = 1
      AND mcp.life_status = 1
      <if test="businessId != null">
        AND mcp.business_id = #{businessId}
      </if>
      <if test="businessId == null">
        AND mcp.company_id = #{companyId}
        <if test="businessIds != null and businessIds.size() &gt; 0">
          AND mcp.business_id IN
          <foreach close=")" collection="businessIds" item="bid" open="(" separator=",">
            #{bid}
          </foreach>
        </if>
      </if>
      <if test="customerIds != null and customerIds.size &gt; 0">
        AND customer_id IN
        <foreach close=")" collection="customerIds" item="customerId" open="(" separator=",">
          #{customerId}
        </foreach>
      </if>
    GROUP BY
        mcp.pet_type_id
  </select>

  <select id="searchCustomerIds" resultType="java.lang.Integer">
    select distinct customer_id
    from moe_customer_pet
    where company_id = #{companyId}
      <if test="businessId != null">
        and business_id = #{businessId}
      </if>
      <if test="keyword != null and keyword != ''">
        and (pet_name like concat('%', #{keyword}, '%') or breed like concat('%', #{keyword}, '%'))
      </if>
      and life_status = 1
      and status = 1
  </select>

  <update id="batchDeleteByCustomerId">
    UPDATE
        moe_customer_pet
    SET
        status = 2,
        update_time = #{updateTime}
    WHERE
        customer_id IN
        <foreach close=")" collection="customerIds" item="customerId" open="(" separator=",">
            #{customerId}
        </foreach>
  </update>
    <update id="updatePetCustomerId">
      update moe_customer_pet set customer_id = #{toCustomerId} where customer_id = #{fromCustomerId} and company_id = #{companyId}
    </update>

  <select id="searchPetCount" parameterType="com.moego.server.customer.service.dto.SearchPetCondition" resultType="java.lang.Integer">
    select count(*)
    from moe_customer_pet pet
    join moe_business_customer customer
    <include refid="searchPetCondition" />
  </select>

  <select id="searchPet" parameterType="com.moego.server.customer.service.dto.SearchPetCondition" resultType="com.moego.server.customer.service.dto.PetWithCustomerDTO">
    select pet.id as petId,
           pet.pet_name as petName,
           pet.avatar_path as petAvatarPath,
           pet.pet_type_id as petTypeId,
           pet.breed as petBreed,
           pet.weight as petWeight,
           pet.hair_length as petCoatType,
           pet.birthday as petBirthday,
           customer.id as customerId,
           customer.first_name as customerFirstName,
           customer.last_name as customerLastName
    from moe_customer_pet pet
    join moe_business_customer customer
    <include refid="searchPetCondition" />
    order by pet.id desc
    limit #{offset}, #{limit}
  </select>

  <sql id="searchPetCondition">
    on pet.customer_id = customer.id
    and customer.company_id = #{companyId}
    and customer.status = 1
    and pet.company_id = #{companyId}
    and pet.status = 1
    and pet.life_status = 1
    <where>
      <if test="businessIds != null and businessIds.size() &gt; 0">
        and customer.business_id in
        <foreach close=")" collection="businessIds" item="businessId" open="(" separator=",">
          #{businessId}
        </foreach>
      </if>

      <if test="term != null and term != ''">
        and (
        concat(customer.first_name, ' ', customer.last_name) like concat('%', #{term}, '%')
        or pet.pet_name like concat('%', #{term}, '%')
        )
      </if>

      <if test="petTypeAndBreeds != null and petTypeAndBreeds.size() &gt; 0">
        and
        <foreach close=")" collection="petTypeAndBreeds" item="item" open="(" separator=" or ">
          (
          pet.pet_type_id = #{item.petTypeId}
          <if test="item.breedNames != null and item.breedNames.size() &gt; 0">
            and pet.breed in
            <foreach close=")" collection="item.breedNames" item="breedName" open="(" separator=",">
              #{breedName}
            </foreach>
          </if>
          )
        </foreach>
      </if>

      <if test="petWeightRanges != null and petWeightRanges.size() &gt; 0">
        and
        <foreach close=")" collection="petWeightRanges" item="range" open="(" separator=" or ">
          (
          <if test="range.weightLow != null">
            #{range.weightLow} &lt;= pet.weight
          </if>
          <if test="range.weightLow != null and range.weightHigh != null">
            and
          </if>
          <if test="range.weightHigh != null">
            pet.weight &lt;= #{range.weightHigh}
          </if>
          )
        </foreach>
      </if>

      <if test="coatTypeNames != null and coatTypeNames.size() &gt; 0">
        and pet.hair_length in
        <foreach close=")" collection="coatTypeNames" item="name" open="(" separator=",">
          #{name}
        </foreach>
      </if>
    </where>
  </sql>

    <select id="searchPetV2" parameterType="com.moego.server.customer.service.dto.SearchPetCondition" resultMap="BaseResultMap">
    select <include refid="Base_Column_List" />
    from moe_customer_pet
    where MATCH(pet_name) AGAINST(#{term} IN NATURAL LANGUAGE MODE)
      and company_id = #{companyId}
      and status = 1
      and life_status = 1
      <if test="petTypeAndBreeds != null and petTypeAndBreeds.size() &gt; 0">
        and
        <foreach close=")" collection="petTypeAndBreeds" item="item" open="(" separator=" or ">
          (
          pet_type_id = #{item.petTypeId}
          <if test="item.breedNames != null and item.breedNames.size() &gt; 0">
            and breed in
            <foreach close=")" collection="item.breedNames" item="breedName" open="(" separator=",">
              #{breedName}
            </foreach>
          </if>
          )
        </foreach>
      </if>
      <if test="petWeightRanges != null and petWeightRanges.size() &gt; 0">
        and
        <foreach close=")" collection="petWeightRanges" item="range" open="(" separator=" or ">
          (
          <if test="range.weightLow != null">
            #{range.weightLow} &lt;= weight
          </if>
          <if test="range.weightLow != null and range.weightHigh != null">
            and
          </if>
          <if test="range.weightHigh != null">
            weight &lt;= #{range.weightHigh}
          </if>
          )
        </foreach>
      </if>
      <if test="coatTypeNames != null and coatTypeNames.size() &gt; 0">
        and hair_length in
        <foreach close=")" collection="coatTypeNames" item="name" open="(" separator=",">
          #{name}
        </foreach>
      </if>
  </select>

  <select id="countPetV2" parameterType="com.moego.server.customer.service.dto.SearchPetCondition" resultType="java.lang.Integer">
    select count(*)
    from moe_customer_pet
    where MATCH(pet_name) AGAINST(#{term} IN NATURAL LANGUAGE MODE)
      and company_id = #{companyId}
      and status = 1
      and life_status = 1
      <if test="petTypeAndBreeds != null and petTypeAndBreeds.size() &gt; 0">
        and
        <foreach close=")" collection="petTypeAndBreeds" item="item" open="(" separator=" or ">
          (
          pet_type_id = #{item.petTypeId}
          <if test="item.breedNames != null and item.breedNames.size() &gt; 0">
            and breed in
            <foreach close=")" collection="item.breedNames" item="breedName" open="(" separator=",">
              #{breedName}
            </foreach>
          </if>
          )
        </foreach>
      </if>
      <if test="petWeightRanges != null and petWeightRanges.size() &gt; 0">
        and
        <foreach close=")" collection="petWeightRanges" item="range" open="(" separator=" or ">
          (
          <if test="range.weightLow != null">
            #{range.weightLow} &lt;= weight
          </if>
          <if test="range.weightLow != null and range.weightHigh != null">
            and
          </if>
          <if test="range.weightHigh != null">
            weight &lt;= #{range.weightHigh}
          </if>
          )
        </foreach>
      </if>
      <if test="coatTypeNames != null and coatTypeNames.size() &gt; 0">
        and hair_length in
        <foreach close=")" collection="coatTypeNames" item="name" open="(" separator=",">
          #{name}
        </foreach>
      </if>
  </select>

</mapper>
