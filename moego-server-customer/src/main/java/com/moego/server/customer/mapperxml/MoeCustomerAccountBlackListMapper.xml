<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.moego.server.customer.mapper.MoeCustomerAccountBlackListMapper">
  <resultMap id="BaseResultMap" type="com.moego.server.customer.mapperbean.MoeCustomerAccountBlackList">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="type" jdbcType="TINYINT" property="type" />
    <result column="value" jdbcType="VARCHAR" property="value" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="create_time" jdbcType="INTEGER" property="createTime" />
    <result column="update_time" jdbcType="INTEGER" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, type, value, status, create_time, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <include refid="Base_Column_List" />
    from moe_customer_account_black_list
    where id = #{id,jdbcType=INTEGER}
  </select>

  <select id="checkPhoneNumber" parameterType="java.lang.String" resultType="java.lang.Integer">
    select count(*)
    from moe_customer_account_black_list
    where type = 1 and status = 1
    and value in
    <foreach collection="phoneNumberList" item="phoneNumber" open="(" separator="," close=")">
      #{phoneNumber,jdbcType=VARCHAR}
    </foreach>
  </select>

  <select id="checkEmail" parameterType="java.lang.String" resultType="java.lang.Integer">
    select count(*)
    from moe_customer_account_black_list
    where type = 2 and status = 1
    and value in
    <foreach collection="emailList" item="email" open="(" separator="," close=")">
      #{email,jdbcType=VARCHAR}
    </foreach>
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from moe_customer_account_black_list
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.moego.server.customer.mapperbean.MoeCustomerAccountBlackList">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into moe_customer_account_black_list (type, value, status,
      create_time, update_time)
    values (#{type,jdbcType=TINYINT}, #{value,jdbcType=VARCHAR}, #{status,jdbcType=TINYINT},
      #{createTime,jdbcType=INTEGER}, #{updateTime,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.moego.server.customer.mapperbean.MoeCustomerAccountBlackList">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into moe_customer_account_black_list
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="type != null">
        type,
      </if>
      <if test="value != null">
        value,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="type != null">
        #{type,jdbcType=TINYINT},
      </if>
      <if test="value != null">
        #{value,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.moego.server.customer.mapperbean.MoeCustomerAccountBlackList">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_customer_account_black_list
    <set>
      <if test="type != null">
        type = #{type,jdbcType=TINYINT},
      </if>
      <if test="value != null">
        value = #{value,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.moego.server.customer.mapperbean.MoeCustomerAccountBlackList">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_customer_account_black_list
    set type = #{type,jdbcType=TINYINT},
      value = #{value,jdbcType=VARCHAR},
      status = #{status,jdbcType=TINYINT},
      create_time = #{createTime,jdbcType=INTEGER},
      update_time = #{updateTime,jdbcType=INTEGER}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>
