package com.moego.server.customer.web.params;

import com.moego.server.customer.account.web.params.code.CodeVerificationParams;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2023/2/7
 */
@Data
@Deprecated
public class OBClientLoginParams {

    @NotBlank
    @Schema(description = "Online booking name, url business name")
    private String businessName;

    @NotEmpty
    @Schema(description = "Phone number")
    private String phoneNumber;

    @Valid
    @NotNull
    private CodeVerificationParams emailCodeParams;
}
