<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.moego.server.customer.mapper.MoeCustomerTagMapper">
  <resultMap id="BaseResultMap" type="com.moego.server.customer.mapperbean.MoeCustomerTag">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="business_id" jdbcType="INTEGER" property="businessId" />
    <result column="sort" jdbcType="INTEGER" property="sort" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="create_time" jdbcType="BIGINT" property="createTime" />
    <result column="update_time" jdbcType="BIGINT" property="updateTime" />
    <result column="company_id" jdbcType="BIGINT" property="companyId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, name, business_id, sort, status, create_time, update_time, company_id
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <include refid="Base_Column_List" />
    from moe_customer_tag
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from moe_customer_tag
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.moego.server.customer.mapperbean.MoeCustomerTag">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into moe_customer_tag (name, business_id, sort,
      status, create_time, update_time, company_id)
    values (#{name,jdbcType=VARCHAR}, #{businessId,jdbcType=INTEGER}, #{sort,jdbcType=INTEGER},
      #{status,jdbcType=TINYINT}, #{createTime,jdbcType=BIGINT}, #{updateTime,jdbcType=BIGINT},
      #{companyId,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" parameterType="com.moego.server.customer.mapperbean.MoeCustomerTag">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into moe_customer_tag
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="name != null">
        name,
      </if>
      <if test="businessId != null">
        business_id,
      </if>
      <if test="sort != null">
        sort,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="companyId != null">
        company_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="businessId != null">
        #{businessId,jdbcType=INTEGER},
      </if>
      <if test="sort != null">
        #{sort,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=BIGINT},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.moego.server.customer.mapperbean.MoeCustomerTag">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_customer_tag
    <set>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="businessId != null">
        business_id = #{businessId,jdbcType=INTEGER},
      </if>
      <if test="sort != null">
        sort = #{sort,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=BIGINT},
      </if>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.moego.server.customer.mapperbean.MoeCustomerTag">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_customer_tag
    set name = #{name,jdbcType=VARCHAR},
      business_id = #{businessId,jdbcType=INTEGER},
      sort = #{sort,jdbcType=INTEGER},
      status = #{status,jdbcType=TINYINT},
      create_time = #{createTime,jdbcType=BIGINT},
      update_time = #{updateTime,jdbcType=BIGINT},
      company_id = #{companyId,jdbcType=BIGINT}
    where id = #{id,jdbcType=INTEGER}
  </update>

  <resultMap id="CustomerTagListDTOResultMap" type="com.moego.server.customer.dto.CustomerTagListDTO">
    <result column="id" property="id" />
    <result column="name" property="name" />
    <result column="business_id" property="businessId" />
    <result column="sort" property="sort" />
    <result column="create_time" property="createTime" />
    <result column="update_time" property="updateTime" />
  </resultMap>

  <update id="deleteMoeCustomerTag">
    update moe_customer_tag
    set status = 2
    where id = #{id}
  </update>


  <update id="sortMoeCustomerTag">
    <foreach collection="sortDtos" item="item" separator=";">
      update moe_customer_tag set sort = #{item.sort} where id = #{item.id} and business_id = #{businessId}
    </foreach>
  </update>

  <select id="queryMoeCustomerTags" resultMap="CustomerTagListDTOResultMap">
        select
        id,name,business_id,sort,create_time,update_time
        from moe_customer_tag
        where status = 1
        and business_id = #{businessId}
        <if test="name !=null and name != &quot;&quot;">and name like concat('%',#{name},'%') </if>
  </select>

  <select id="getTagsByIds" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from moe_customer_tag where id in
    <foreach close=")" collection="tagIds" index="index" item="item" open="(" separator=",">
      #{item}
    </foreach>
  </select>

  <select id="selectCustomerByBusinessAndName" resultMap="BaseResultMap">
    select
    id,name,business_id,sort,create_time,update_time
    from moe_customer_tag
    where status = 1
    and business_id = #{businessId}
    <if test="name !=null and name != &quot;&quot;">and name = #{name} </if>
  </select>

  <select id="selectCustomerByBusinessAndCustomerIds" resultMap="BaseResultMap">
    select
    id,name,business_id,sort,create_time,update_time
    from moe_customer_tag
    where status = 1
    and company_id = #{companyId}
    <if test="businessId != null">and business_id = #{businessId} </if>
    and id in
    (
    select customer_tag_id from moe_customer_tag_binding where customer_id in
    <foreach collection="customerIds" open="(" close=")" separator="," item="item">
        #{item}
    </foreach>
    )

  </select>
</mapper>
