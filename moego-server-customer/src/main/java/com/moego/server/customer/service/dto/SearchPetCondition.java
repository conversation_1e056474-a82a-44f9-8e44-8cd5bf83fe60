package com.moego.server.customer.service.dto;

import java.util.List;
import java.util.Set;
import lombok.Data;

@Data
public class SearchPetCondition {
    // company id should not be null or 0
    private Long companyId;

    private Set<Integer> businessIds;

    // customer name or pet name, optional
    private String term;

    // pet types and breeds, optional
    private List<PetTypeAndBreed> petTypeAndBreeds;

    // pet weight ranges, optional
    private List<PetWeightRange> petWeightRanges;

    // pet coat type names, optional
    private Set<String> coatTypeNames;

    private Integer offset;
    private Integer limit;

    public record PetTypeAndBreed(
            Integer petTypeId,
            // pet breeds, empty means all breeds
            List<String> breedNames
            // List<Long> breedIds
            ) {}

    // pet weight range (weightLow <= weight <= weightHigh)
    public record PetWeightRange(Double weightLow, Double weightHigh) {}
}
