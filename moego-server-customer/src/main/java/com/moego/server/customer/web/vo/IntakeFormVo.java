package com.moego.server.customer.web.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2021/1/11 6:07 PM
 */
@Data
public class IntakeFormVo {

    private Integer id;

    @Schema(description = "form primary id, used when updating")
    private Integer formId;

    @NotNull
    private String title;

    private String themeColor;
    private String message;

    @Schema(description = "是否card选项 1：显示 0：不显示")
    private Byte isCardShow;

    @Schema(description = "是否card选项 1： 必填 0：不必填")
    private Byte isCardRequired;

    private Integer businessId;
}
