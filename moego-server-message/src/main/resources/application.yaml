server:
  port: 9205

spring:
  application:
    name: moego-server-message
  profiles:
    active: local
  cloud:
    aws:
      region:
        static: ${AWS_REGION:us-west-2}
  datasource:
    url: jdbc:mysql://${secret.datasource.mysql.url.master}:${secret.datasource.mysql.port}/moe_message?useUnicode=true&characterEncoding=utf8&allowMultiQueries=true&useSSL=false
    username: ${secret.datasource.mysql.username}
    password: ${secret.datasource.mysql.password}
  freemarker:
    prefer-file-system-access: false
  data:
    redis:
      host: ${secret.redis.host}
      password: ${secret.redis.password}
      port: ${secret.redis.port}
      timeout: 60000
      key:
        delimiter: ":"
        prefix: "apiv2"
      ssl:
        enabled: ${secret.redis.tls}

twilio:
  account:
    auth:
      token: ${secret.twilio.account.auth_token}
    id: ${secret.twilio.account.id}
  sms:
    receive:
      callback: "https://api.t2.moego.pet/message/callback/twilio/receive/new?businessId=%s"
    status:
      callback:
        url: "https://api.t2.moego.pet/message/callback/twilio/sms/status?id=%s"
    verification:
      callback:
        url: 'https://api.t2.moego.pet/message/callback/twilio/sms/verification/status'
    test:
      send:
        from: "+***********"
  voice:
    status:
      webhook: "https://api.t2.moego.pet/message/voice/status/webhook"
    twiml:
      url: "https://api.t2.moego.pet/message/voice/twiml?apptId=%s"
    webhook: "https://api.t2.moego.pet/message/voice/webhook"

contact:
  us:
    email: "<EMAIL>"
customer:
  unsigned:
    url: "https://go.t2.moego.pet/agreement/sign/"
  # customer upcoming url
  upcoming:
    url: "https://client.t2.moego.pet/appointment/upcoming?id="
  invite:
    url: "https://my.t2.moego.pet/invite?code="

message:
  verification:
    ob-code:
      frequency: 60
      validity: 600
      length: 6
    code:
      # send code frequency, default 60s (1min)
      frequency: 60
      # code validity, default 300s (5min)
      validity: 300

firebase:
  adminsdk:
    file: '/opt/moego-api/config/firebase-admin-sdk.json'
  database:
    url: "https://boarding-65ef1.firebaseio.com"
  json:
    path: "/data/wwwroot/jar/boarding-65ef1-firebase-adminsdk-j1lmd-7be83386fc.json"

firebasedynamiclinks:
  googleapis:
    url: "https://firebasedynamiclinks.googleapis.com/v1/shortLinks?key=AIzaSyDdLULyT61wHy2GjA0Y_kFjpnvzOJ41Jcs"
    url.shot.prefix: "https://sh.moego.pet/short?link=%s"

mandrill:
  api:
    key: ${secret.mandrill.api_key}
  send:
    from:
      email:
        'no':
          reply: "<EMAIL>"
      emailPrefix: "t2_notifications"
      emailSuffix: "@cheers.moego.pet"
      name: "MoeGo"
  webhook:
    moment:
      inboundKey: ${secret.mandrill.webhook.moment.inbound_key}
    moego:
      inboundKey: ${secret.mandrill.webhook.moego.inbound_key}
      eventKey: ${secret.mandrill.webhook.moego.event_key}

mybatis:
  mapper-locations: classpath*:/com/moego/server/message/mapperxml/*.xml
  type-aliases-package: com.moego.server.message.mapperbean

native:
  android:
    channel:
      id: moego-default
    database:
      url: https://moego-8efd0.firebaseio.com
    sound: moego.wav
  ios:
    apns:
      host: api.push.apple.com
    p12:
      password: ${secret.mobile.ios.apns.file.pwd}
    sound: moego.wav
    topic: com.moement.moego.business

pagehelper:
  helperDialect: mysql
  page-size-zero: true
  params: count=countSql
  reasonable: false
  supportMethodsArguments: true

pay:
  online:
    url: https://go.t2.moego.pet/payment/online/%s

s3:
  domain: https://moegonew.s3-us-west-2.amazonaws.com/
  region: ${secret.aws.region}
  key: ${secret.aws.access_key_id}
  secret: ${secret.aws.secret_access_key}

sentry:
  dsn: "https://<EMAIL>/5"

short:
  link:
    url: "https://go.t2.moego.pet/s/%s"

moego:
  event-bus:
    brokers:
      - name: default
        addresses:
          - ${secret.mq.kafka.broker_url_0}
          - ${secret.mq.kafka.broker_url_1}
          - ${secret.mq.kafka.broker_url_2}
    consumer:
      # 如果 enabled 为 false (默认值), 则不会启动 consumer 监听消息, 但是 consumer 可以被初始化.
      enabled: true
      # 接收到消息时是否打印日志, 默认为 false
      log-receive: false
    producer:
      enabled: true
  messaging:
    pulsar:
      service-url: ${secret.mq.pulsar.service_url}
      authentication: ${secret.mq.pulsar.token}
      tenant: test2
  membership:
    client-buy-link: "https://client.t2.moego.dev/membership/buy/%s"
  mobile:
    apns:
      file: '/opt/moego-api/config/ios-apns.p12'
  push:
    httpAddress: http://moego-ws-push-http.t2.moego/moego.service.ws/Push
  server:
    url:
      business: http://moego-service-business:9203
      customer: http://moego-service-customer:9201
      grooming: http://moego-service-grooming:9206
      payment: http://moego-service-payment:9204
      retail: http://moego-service-retail:9207
  grpc:
    server:
      enabled: false
    client:
      stubs:
        - service: moego.service.agreement.**
          authority: moego-svc-agreement:9090
        - service: moego.service.message.**
          authority: moego-svc-message:9090
        - service: moego.service.sms.**
          authority: moego-svc-sms:9090
        - service: moego.service.order.**
          authority: moego-svc-order:9090
        - service: moego.service.notification.**
          authority: moego-svc-notification:9090
        - service: moego.service.reporting.**
          authority: moego-svc-reporting:9090
        - service: moego.service.account.v1.**
          authority: moego-svc-account:9090
        - service: moego.service.metadata.v1.**
          authority: moego-svc-metadata:9090
        - service: moego.service.organization.**
          authority: moego-svc-organization:9090
        - service: moego.service.permission.**
          authority: moego-svc-permission:9090
        - service: moego.service.file.**
          authority: moego-svc-file:9090
        - service: moego.service.appointment.v1.*
          authority: moego-svc-appointment:9090
        - service: moego.service.auto_message.v1.*
          authority: moego-svc-auto-message:9090
        - service: moego.service.business_customer.**
          authority: moego-svc-business-customer:9090
        - service: moego.service.online_booking.**
          authority: moego-svc-online-booking:9090
        - service: moego.service.offering.**
          authority: moego-svc-offering:9090
        - service: moego.service.ws.**
          authority: moego-app-ws:9302
        - service: moego.service.enterprise.v1.*
          authority: moego-svc-enterprise:9090
        - service: moego.service.branded_app.**
          authority: moego-svc-branded-app:9090
        - service: moego.service.membership.**
          authority: moego-svc-membership:9090
  grooming-report:
    book-again-url: "https://booking.t2.moego.pet/ol/portal/bookAgain?name=%s&reportId=%s"
    client-url: "https://my.t2.moego.pet/grooming/report/%s"
  online-booking:
    landing-url: "https://booking.t2.moego.dev/ol/%s/landing"
  intake-form:
    form-url: "https://form.t2.moego.dev/go/form?formId=%s"
  cof:
    cof-url: "https://client.t2.moego.dev/payment/cof/client?c=%s"
  data-sources:
    - name: reader
      url: jdbc:mysql://${secret.datasource.mysql.url.reader}:${secret.datasource.mysql.port}/moe_message?useUnicode=true&characterEncoding=utf8&allowMultiQueries=true&useSSL=false
      username: ${secret.datasource.mysql.username}
      password: ${secret.datasource.mysql.password}
  feature-flag:
    growth-book:
      api-host: ${secret.growthbook.host}
      client-key: ${secret.growthbook.client_key}
springdoc:
  packages-to-scan:
    - com.moego.server.message.web
  swagger-ui:
    enabled: false
  api-docs:
    enabled: false
