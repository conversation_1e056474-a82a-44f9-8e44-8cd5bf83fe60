<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.moego.server.message.mapper.MessageReportMapper">
  <select
    id="batchGetMessageReport"
    resultType="com.moego.server.message.mapper.bo.MessageReportBO"
  >
    SELECT business_id AS businessId, target_type AS targetType, count(*) AS total FROM
    moe_business_message_detail
    WHERE create_time BETWEEN ${startTime} AND ${endTime}
    AND business_id IN
    <foreach collection="businessIds" item="businessId" separator="," open="(" close=")">
      #{businessId}
    </foreach>
    AND method = 1 AND type = 1 GROUP BY business_id, target_type
  </select>
  <select
    id="batchGetMassTextReport"
    resultType="com.moego.server.message.mapper.bo.MassTextReportBO"
  >
    SELECT business_id AS businessId, count(*) AS total, sum(batch_size) AS targetTotal FROM
    moe_business_message_batch
    WHERE create_time BETWEEN ${startTime} AND ${endTime}
    AND business_id IN
    <foreach collection="businessIds" item="businessId" separator="," open="(" close=")">
      #{businessId}
    </foreach>
    GROUP BY business_id
  </select>
</mapper>
