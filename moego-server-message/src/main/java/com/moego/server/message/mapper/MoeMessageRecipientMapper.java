package com.moego.server.message.mapper;

import com.moego.server.message.mapperbean.MoeMessageRecipient;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Select;

public interface MoeMessageRecipientMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_message_recipient
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_message_recipient
     *
     * @mbg.generated
     */
    int insert(MoeMessageRecipient record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_message_recipient
     *
     * @mbg.generated
     */
    int insertSelective(MoeMessageRecipient record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_message_recipient
     *
     * @mbg.generated
     */
    MoeMessageRecipient selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_message_recipient
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(MoeMessageRecipient record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_message_recipient
     *
     * @mbg.generated
     */
    int updateByPrimaryKeyWithBLOBs(MoeMessageRecipient record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_message_recipient
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(MoeMessageRecipient record);

    @Select(
            "select business_id, customer_id from moe_message_recipient where mandrill_message_id = #{mandrillMessageId}")
    @ResultMap("BaseResultMap")
    MoeMessageRecipient selectByMandrillMessageId(String mandrillMessageId);
}
