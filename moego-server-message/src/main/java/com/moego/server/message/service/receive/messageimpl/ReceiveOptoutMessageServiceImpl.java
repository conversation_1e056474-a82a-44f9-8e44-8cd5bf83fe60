package com.moego.server.message.service.receive.messageimpl;

import com.moego.server.business.client.IBusinessBusinessClient;
import com.moego.server.business.dto.MoeBusinessDto;
import com.moego.server.business.params.InfoIdParams;
import com.moego.server.message.enums.MessageDetailEnum;
import com.moego.server.message.enums.MessageTargetTypeEnums;
import com.moego.server.message.params.SendMsgParams;
import com.moego.server.message.params.TwilioReceiveMessageCallbackParams;
import com.moego.server.message.service.MessageOptoutService;
import com.moego.server.message.service.receive.ReceiveMessageService;
import com.moego.server.message.service.sendmessage.MessageSendRouterService;
import com.moego.server.message.web.params.UpdateMessageOptoutCustomerParam;
import com.moego.server.message.web.vo.MessageOptoutConfigVO;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR>
 * @date 2020-08-04 00:52
 */
@Service
@Slf4j
public class ReceiveOptoutMessageServiceImpl implements ReceiveMessageService {

    @Autowired
    private MessageOptoutService messageOptoutService;

    @Autowired
    private MessageSendRouterService messageSendRouterService;

    @Autowired
    private IBusinessBusinessClient iBusinessBusinessClient;

    private final Map<String, String> supportOptoutSourceMethodMap =
            Map.of(MessageDetailEnum.MESSAGE_SOURCE_WORKFLOW.name(), MessageDetailEnum.MESSAGE_METHOD_MSG.name());

    @Override
    public boolean hit(TwilioReceiveMessageCallbackParams twilioReceiveMessageCallbackParams) {
        log.info(
                "ReceiveOptoutMessageServiceImpl hit callBackParams:{}", twilioReceiveMessageCallbackParams.toString());

        // check
        String body = twilioReceiveMessageCallbackParams.getBody();
        Long companyID = twilioReceiveMessageCallbackParams.getCompanyId();
        if (StringUtils.isEmpty(body) || (companyID == null || companyID == 0L)) {
            log.info("ReceiveOptoutMessageServiceImpl hit false, callBackParams invalid");
            return false;
        }

        // get optout/optin config
        List<MessageOptoutConfigVO> configs = new ArrayList<>();
        for (Map.Entry<String, String> entry : supportOptoutSourceMethodMap.entrySet()) {
            configs.add(messageOptoutService.getMessageOptoutConfig(
                    companyID.intValue(), entry.getKey(), entry.getValue()));
        }

        // check config hit
        for (MessageOptoutConfigVO config : configs) {
            if (!CollectionUtils.isEmpty(config.getOptoutKeyword())) {
                for (String optoutKeyword : config.getOptoutKeyword()) {
                    if (body.equalsIgnoreCase(optoutKeyword)) {
                        log.info("ReceiveOptoutMessageServiceImpl hit optout true, config:{}", config);
                        return true;
                    }
                }
            }
            if (!CollectionUtils.isEmpty(config.getOptinKeyword())) {
                for (String optinKeyword : config.getOptinKeyword()) {
                    if (body.equalsIgnoreCase(optinKeyword)) {
                        log.info("ReceiveOptoutMessageServiceImpl hit optin true, config:{}", config);
                        return true;
                    }
                }
            }
        }

        log.info("ReceiveOptoutMessageServiceImpl hit false");
        return false;
    }

    @Override
    public boolean execMessage(TwilioReceiveMessageCallbackParams callBackParams) {
        log.info("ReceiveOptoutMessageServiceImpl execMessage callBackParams:{}", callBackParams.toString());
        // check
        String body = callBackParams.getBody();
        Long companyID = callBackParams.getCompanyId();
        Integer customerID = callBackParams.getCustomerId();
        if (StringUtils.isEmpty(body)
                || (companyID == null || companyID == 0L)
                || (customerID == null || customerID == 0)) {
            log.info("ReceiveOptoutMessageServiceImpl execMessage params invalid");
            return false;
        }

        // load business
        MoeBusinessDto businessInfo =
                iBusinessBusinessClient.getBusinessInfo(new InfoIdParams(callBackParams.getBusinessId()));
        if (businessInfo == null || StringUtils.isEmpty(businessInfo.getBusinessName())) {
            log.info("ReceiveOptoutMessageServiceImpl execMessage load business invalid");
            return false;
        }

        // get optout config
        List<MessageOptoutConfigVO> configs = new ArrayList<>();
        for (Map.Entry<String, String> entry : supportOptoutSourceMethodMap.entrySet()) {
            configs.add(messageOptoutService.getMessageOptoutConfig(
                    companyID.intValue(), entry.getKey(), entry.getValue()));
        }

        // config exec
        boolean isConfigExec = false;
        for (MessageOptoutConfigVO config : configs) {
            log.info(
                    "ReceiveOptoutMessageServiceImpl execMessage config start, config:{}, customerID:{}",
                    config,
                    customerID);

            // check optout/optin hit
            boolean isOptoutKeywordHit = false;
            boolean isOptinKeywordHit = false;
            if (!CollectionUtils.isEmpty(config.getOptoutKeyword())) {
                for (String optoutKeyword : config.getOptoutKeyword()) {
                    if (body.equalsIgnoreCase(optoutKeyword)) {
                        isOptoutKeywordHit = true;
                        break;
                    }
                }
            }
            if (!CollectionUtils.isEmpty(config.getOptinKeyword())) {
                for (String optinKeyword : config.getOptinKeyword()) {
                    if (body.equalsIgnoreCase(optinKeyword)) {
                        isOptinKeywordHit = true;
                        break;
                    }
                }
            }

            if (!isOptoutKeywordHit && !isOptinKeywordHit) {
                log.info("ReceiveOptoutMessageServiceImpl execMessage keywords are not hit optout/optin");
                continue;
            }

            // exec
            isConfigExec = true;
            if (isOptoutKeywordHit) {
                execCustomer(
                        config.getSource(),
                        config.getMethod(),
                        callBackParams,
                        businessInfo,
                        MessageOptoutService.MessageOptoutCustomerStatusValid,
                        config.getOptoutReplyContent()
                                .replaceAll(
                                        Pattern.quote(config.getOptinContentPlaceholder()), config.getOptinContent()));
            }
            if (isOptinKeywordHit) {
                execCustomer(
                        config.getSource(),
                        config.getMethod(),
                        callBackParams,
                        businessInfo,
                        MessageOptoutService.MessageOptoutCustomerStatusDeleted,
                        config.getOptinReplyContent());
            }
        }

        return isConfigExec;
    }

    private void execCustomer(
            String source,
            String method,
            TwilioReceiveMessageCallbackParams callBackParams,
            MoeBusinessDto businessInfo,
            Byte status,
            String reply) {
        try {
            // hit add customer to block list
            messageOptoutService.updateMessageOptoutCustomers(
                    callBackParams.getCompanyId().intValue(),
                    UpdateMessageOptoutCustomerParam.builder()
                            .source(source)
                            .method(method)
                            .customerIDs(List.of(callBackParams.getCustomerId().longValue()))
                            .status(status)
                            .build());
            log.info("ReceiveOptoutMessageServiceImpl execCustomer updateMessageOptoutCustomers success");

            // replay
            if (StringUtils.isBlank(reply)) {
                log.info("ReceiveOptoutMessageServiceImpl reply reply is blank, no need to reply");
                return;
            }
            messageSendRouterService.sendMessage(SendMsgParams.builder()
                    .businessId(callBackParams.getBusinessId())
                    .customerId(callBackParams.getCustomerId())
                    .targetId(0)
                    .targetType(MessageTargetTypeEnums.OPTOUT_REPLY.getValue())
                    .msgChannelType(MessageDetailEnum.getValueByName(method))
                    .msgContentType(1)
                    .senderType(1)
                    .source(MessageDetailEnum.MESSAGE_SOURCE_OPTOUT.getValue())
                    .msgBody(reply)
                    .senderName(businessInfo.getBusinessName())
                    .toPhoneNumber(callBackParams.getFrom())
                    .build());
            log.info("ReceiveOptoutMessageServiceImpl execCustomer replay success");
        } catch (Exception e) {
            log.error(
                    "ReceiveOptoutMessageServiceImpl execCustomer fail, source:{}, method:{}, customerID:{}, exception:{}",
                    source,
                    method,
                    callBackParams.getCustomerId(),
                    e.getMessage());
        }
    }
}
