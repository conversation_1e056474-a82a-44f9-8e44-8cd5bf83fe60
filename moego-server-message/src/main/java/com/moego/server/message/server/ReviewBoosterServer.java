package com.moego.server.message.server;

import com.moego.common.dto.clients.ClientsFilterDTO;
import com.moego.common.utils.PageUtil;
import com.moego.lib.common.util.JsonUtil;
import com.moego.server.message.api.IReviewBoosterServiceBase;
import com.moego.server.message.dto.ListReviewBoosterRecordsDTO;
import com.moego.server.message.dto.ReviewBoosterRecordDTO;
import com.moego.server.message.dto.ReviewBoosterSummaryDTO;
import com.moego.server.message.mapper.MoeBusinessReviewBoosterRecordMapper;
import com.moego.server.message.mapperbean.MoeBusinessReviewBooster;
import com.moego.server.message.mapperbean.MoeBusinessReviewBoosterRecord;
import com.moego.server.message.mapstruct.ReviewBoosterMapper;
import com.moego.server.message.params.ListReviewBoosterRecordsParams;
import com.moego.server.message.params.ReviewBoosterSummaryParams;
import com.moego.server.message.service.MoeBusinessReviewBoosterService;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.RestController;

@RestController
@AllArgsConstructor
public class ReviewBoosterServer extends IReviewBoosterServiceBase {

    private final MoeBusinessReviewBoosterService reviewBoosterService;
    private final MoeBusinessReviewBoosterRecordMapper reviewBoosterRecordMapper;

    @Override
    public Set<Integer> listCustomerIdByFilter(ClientsFilterDTO clientsFilter) {
        return reviewBoosterService.listCustomerIdByFilter(clientsFilter);
    }

    @Override
    public List<ReviewBoosterRecordDTO> getReviewBoosterListByAppointmentId(
            Integer businessId, Integer customerId, Integer appointmentId) {
        List<MoeBusinessReviewBoosterRecord> records =
                reviewBoosterRecordMapper.selectAppointmentReviewRecordList(businessId, customerId, appointmentId);
        return records.stream().map(ReviewBoosterMapper.INSTANCE::entityToDto).toList();
    }

    @Override
    public Boolean createReviewBooster(ReviewBoosterRecordDTO dto) {
        MoeBusinessReviewBooster reviewBooster = reviewBoosterService.findMoeBusinessReviewBooster(dto.getBusinessId());
        if (Objects.isNull(reviewBooster)) {
            return Boolean.FALSE;
        }
        MoeBusinessReviewBoosterRecord record = ReviewBoosterMapper.INSTANCE.dtoToEntity(dto);
        record.setReviewBoosterId(reviewBooster.getId());
        record.setCompanyId(reviewBooster.getCompanyId());
        return reviewBoosterRecordMapper.insertSelective(record) > 0;
    }

    @Override
    public ReviewBoosterSummaryDTO reviewBoosterSummary(ReviewBoosterSummaryParams params) {
        // find records
        List<MoeBusinessReviewBoosterRecord> records = reviewBoosterRecordMapper.selectReviewBoosterByTimeRange(
                params.getBusinessId(), params.getStartDate(), params.getEndDate(), true);
        int businessReviewTotal = 0;
        Integer businessSumScore = 0;
        int staffReviewTotal = 0;
        Integer staffSumScore = 0;
        for (MoeBusinessReviewBoosterRecord record : records) {
            businessReviewTotal++;
            businessSumScore += record.getPositiveScore();
            List<Integer> staffIds = JsonUtil.toList(record.getStaffIds(), Integer.class);
            if (staffIds == null || !staffIds.contains(params.getStaffId())) {
                continue;
            }
            staffReviewTotal++;
            staffSumScore += record.getPositiveScore();
        }
        ReviewBoosterSummaryDTO.Summary businessSummary = new ReviewBoosterSummaryDTO.Summary();
        businessSummary.setReviewTotal(businessReviewTotal);
        businessSummary.setAvgScore(
                businessReviewTotal == 0
                        ? BigDecimal.ZERO
                        : new BigDecimal(businessSumScore)
                                .divide(new BigDecimal(businessReviewTotal), 1, RoundingMode.HALF_UP));
        ReviewBoosterSummaryDTO.Summary staffSummary = new ReviewBoosterSummaryDTO.Summary();
        staffSummary.setReviewTotal(staffReviewTotal);
        staffSummary.setAvgScore(
                staffReviewTotal == 0
                        ? BigDecimal.ZERO
                        : new BigDecimal(staffSumScore)
                                .divide(new BigDecimal(staffReviewTotal), 1, RoundingMode.HALF_UP));
        ReviewBoosterSummaryDTO summaryDTO = new ReviewBoosterSummaryDTO();
        summaryDTO.setBusinessSummary(businessSummary);
        summaryDTO.setStaffSummary(staffSummary);
        return summaryDTO;
    }

    @Override
    public ListReviewBoosterRecordsDTO listReviewBoosterRecords(ListReviewBoosterRecordsParams params) {
        var result = PageUtil.selectPage(
                params.pagination(), () -> reviewBoosterRecordMapper.listReviewBoosterRecords(params));
        return new ListReviewBoosterRecordsDTO(
                result.getFirst().stream()
                        .map(ReviewBoosterMapper.INSTANCE::entityToDto)
                        .toList(),
                result.getSecond());
    }
}
