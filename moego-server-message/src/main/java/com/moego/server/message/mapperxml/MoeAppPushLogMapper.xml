<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.moego.server.message.mapper.MoeAppPushLogMapper">
  <resultMap id="BaseResultMap" type="com.moego.server.message.mapperbean.MoeAppPushLog">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="business_id" jdbcType="INTEGER" property="businessId" />
    <result column="staff_id" jdbcType="INTEGER" property="staffId" />
    <result column="noification_record_id" jdbcType="INTEGER" property="noificationRecordId" />
    <result column="push_token" jdbcType="TINYINT" property="pushToken" />
    <result column="push_time" jdbcType="TINYINT" property="pushTime" />
    <result column="is_success" jdbcType="TINYINT" property="isSuccess" />
    <result column="error_code" jdbcType="VARCHAR" property="errorCode" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, business_id, staff_id, noification_record_id, push_token, push_time, is_success, 
    error_code
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from moe_app_push_log
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from moe_app_push_log
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.moego.server.message.mapperbean.MoeAppPushLog">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into moe_app_push_log (business_id, staff_id, noification_record_id, 
      push_token, push_time, is_success, 
      error_code)
    values (#{businessId,jdbcType=INTEGER}, #{staffId,jdbcType=INTEGER}, #{noificationRecordId,jdbcType=INTEGER}, 
      #{pushToken,jdbcType=TINYINT}, #{pushTime,jdbcType=TINYINT}, #{isSuccess,jdbcType=TINYINT}, 
      #{errorCode,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.moego.server.message.mapperbean.MoeAppPushLog">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into moe_app_push_log
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="businessId != null">
        business_id,
      </if>
      <if test="staffId != null">
        staff_id,
      </if>
      <if test="noificationRecordId != null">
        noification_record_id,
      </if>
      <if test="pushToken != null">
        push_token,
      </if>
      <if test="pushTime != null">
        push_time,
      </if>
      <if test="isSuccess != null">
        is_success,
      </if>
      <if test="errorCode != null">
        error_code,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="businessId != null">
        #{businessId,jdbcType=INTEGER},
      </if>
      <if test="staffId != null">
        #{staffId,jdbcType=INTEGER},
      </if>
      <if test="noificationRecordId != null">
        #{noificationRecordId,jdbcType=INTEGER},
      </if>
      <if test="pushToken != null">
        #{pushToken,jdbcType=TINYINT},
      </if>
      <if test="pushTime != null">
        #{pushTime,jdbcType=TINYINT},
      </if>
      <if test="isSuccess != null">
        #{isSuccess,jdbcType=TINYINT},
      </if>
      <if test="errorCode != null">
        #{errorCode,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.moego.server.message.mapperbean.MoeAppPushLog">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_app_push_log
    <set>
      <if test="businessId != null">
        business_id = #{businessId,jdbcType=INTEGER},
      </if>
      <if test="staffId != null">
        staff_id = #{staffId,jdbcType=INTEGER},
      </if>
      <if test="noificationRecordId != null">
        noification_record_id = #{noificationRecordId,jdbcType=INTEGER},
      </if>
      <if test="pushToken != null">
        push_token = #{pushToken,jdbcType=TINYINT},
      </if>
      <if test="pushTime != null">
        push_time = #{pushTime,jdbcType=TINYINT},
      </if>
      <if test="isSuccess != null">
        is_success = #{isSuccess,jdbcType=TINYINT},
      </if>
      <if test="errorCode != null">
        error_code = #{errorCode,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.moego.server.message.mapperbean.MoeAppPushLog">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update moe_app_push_log
    set business_id = #{businessId,jdbcType=INTEGER},
      staff_id = #{staffId,jdbcType=INTEGER},
      noification_record_id = #{noificationRecordId,jdbcType=INTEGER},
      push_token = #{pushToken,jdbcType=TINYINT},
      push_time = #{pushTime,jdbcType=TINYINT},
      is_success = #{isSuccess,jdbcType=TINYINT},
      error_code = #{errorCode,jdbcType=VARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>
