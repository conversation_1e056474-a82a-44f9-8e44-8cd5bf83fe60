package com.moego.server.message.mapper;

import com.moego.server.message.mapperbean.MoeBusinessCallDetail;
import java.util.Set;
import org.apache.ibatis.annotations.Param;

public interface MoeBusinessCallDetailMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business_call_detail
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business_call_detail
     *
     * @mbg.generated
     */
    int insert(MoeBusinessCallDetail record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business_call_detail
     *
     * @mbg.generated
     */
    int insertSelective(MoeBusinessCallDetail record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business_call_detail
     *
     * @mbg.generated
     */
    MoeBusinessCallDetail selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business_call_detail
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(MoeBusinessCallDetail record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business_call_detail
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(MoeBusinessCallDetail record);

    MoeBusinessCallDetail selectByCallSid(@Param("callSid") String callSid);

    MoeBusinessCallDetail getFirstCall(
            @Param("businessId") Integer businessId, @Param("customerId") Integer customerId);

    int dismissUnassignRecordByPhoneNumber(
            @Param("businessIds") Set<Integer> businessIds, @Param("phoneNumber") String phoneNumber);

    int associateUnassignedCallRecords(
            @Param("businessIds") Set<Integer> businessIds,
            @Param("customerId") Long customerId,
            @Param("phoneNumber") String phoneNumber);

    int setUnassignMessageDetailRead(@Param("businessIds") Set<Integer> businessIds);

    int queryUnassignMessageDetailUnreadCount(@Param("businessIds") Set<Integer> businessIds);
}
