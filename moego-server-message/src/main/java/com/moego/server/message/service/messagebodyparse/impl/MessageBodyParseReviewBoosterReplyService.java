package com.moego.server.message.service.messagebodyparse.impl;

import com.moego.server.message.mapper.MoeBusinessReviewBoosterMapper;
import com.moego.server.message.mapperbean.MoeBusinessReviewBooster;
import com.moego.server.message.params.SendMessagesParams;
import com.moego.server.message.service.messagebodyparse.MessageBodyParseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class MessageBodyParseReviewBoosterReplyService implements MessageBodyParseService {

    private static final String HIT_FACEBOOK = "{Facebook}";
    private static final String HIT_GOOGLE = "{Google}";
    private static final String HIT_YELP = "{Yelp}";

    @Autowired
    private MoeBusinessReviewBoosterMapper reviewBoosterMapper;

    @Override
    public boolean hit(String body) {
        return body.contains(HIT_FACEBOOK) || body.contains(HIT_GOOGLE) || body.contains(HIT_YELP);
    }

    @Override
    public String parse(String body, SendMessagesParams sendMessagesParams) {
        MoeBusinessReviewBooster reviewBooster =
                reviewBoosterMapper.selectByBusinessId(sendMessagesParams.getBusinessId());
        if (reviewBooster == null) {
            return body;
        }

        body = body.replace(HIT_FACEBOOK, reviewBooster.getPositiveFacebook());
        body = body.replace(HIT_GOOGLE, reviewBooster.getPositiveGoogle());
        body = body.replace(HIT_YELP, reviewBooster.getPositiveYelp());
        return body;
    }
}
