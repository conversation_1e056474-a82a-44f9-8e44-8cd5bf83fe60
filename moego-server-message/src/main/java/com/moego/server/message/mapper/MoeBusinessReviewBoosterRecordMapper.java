package com.moego.server.message.mapper;

import com.moego.common.dto.clients.ClientsFilterDTO;
import com.moego.server.message.dto.CustomerAvgRateDTO;
import com.moego.server.message.dto.MoeBusinessReviewBoosterCountDTO;
import com.moego.server.message.mapperbean.MoeBusinessReviewBoosterRecord;
import com.moego.server.message.mapperbean.MoeBusinessReviewBoosterRecordExample;
import com.moego.server.message.params.GetReviewBoosterRecordListParams;
import com.moego.server.message.params.ListReviewBoosterRecordsParams;
import java.util.Date;
import java.util.List;
import java.util.Set;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface MoeBusinessReviewBoosterRecordMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business_review_booster_record
     *
     * @mbg.generated
     */
    long countByExample(MoeBusinessReviewBoosterRecordExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business_review_booster_record
     *
     * @mbg.generated
     */
    int deleteByExample(MoeBusinessReviewBoosterRecordExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business_review_booster_record
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business_review_booster_record
     *
     * @mbg.generated
     */
    int insert(MoeBusinessReviewBoosterRecord record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business_review_booster_record
     *
     * @mbg.generated
     */
    int insertSelective(MoeBusinessReviewBoosterRecord record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business_review_booster_record
     *
     * @mbg.generated
     */
    List<MoeBusinessReviewBoosterRecord> selectByExampleWithBLOBs(MoeBusinessReviewBoosterRecordExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business_review_booster_record
     *
     * @mbg.generated
     */
    List<MoeBusinessReviewBoosterRecord> selectByExample(MoeBusinessReviewBoosterRecordExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business_review_booster_record
     *
     * @mbg.generated
     */
    MoeBusinessReviewBoosterRecord selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business_review_booster_record
     *
     * @mbg.generated
     */
    int updateByExampleSelective(
            @Param("record") MoeBusinessReviewBoosterRecord record,
            @Param("example") MoeBusinessReviewBoosterRecordExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business_review_booster_record
     *
     * @mbg.generated
     */
    int updateByExampleWithBLOBs(
            @Param("record") MoeBusinessReviewBoosterRecord record,
            @Param("example") MoeBusinessReviewBoosterRecordExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business_review_booster_record
     *
     * @mbg.generated
     */
    int updateByExample(
            @Param("record") MoeBusinessReviewBoosterRecord record,
            @Param("example") MoeBusinessReviewBoosterRecordExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business_review_booster_record
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(MoeBusinessReviewBoosterRecord record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business_review_booster_record
     *
     * @mbg.generated
     */
    int updateByPrimaryKeyWithBLOBs(MoeBusinessReviewBoosterRecord record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_business_review_booster_record
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(MoeBusinessReviewBoosterRecord record);

    MoeBusinessReviewBoosterRecord selectLastMoeBusinessReviewBoosterRecord(
            @Param("businessId") Integer businessId, @Param("customerId") Integer customerId);

    List<MoeBusinessReviewBoosterRecord> selectMoeBusinessReviewBoosterRecord(
            @Param("businessId") Integer businessId, @Param("customerId") Integer customerId);

    List<MoeBusinessReviewBoosterRecord> selectMoeBusinessReviewBoosterRecordByCompany(
            @Param("companyId") Long companyId, @Param("customerId") Integer customerId);

    List<MoeBusinessReviewBoosterRecord> selectReviewBoosterByTimeRange(
            @Param("businessId") Integer businessId,
            @Param("reviewTimeStart") Date reviewTimeStart,
            @Param("reviewTimeEnd") Date reviewTimeEnd,
            @Param("repliedOnly") Boolean repliedOnly);

    MoeBusinessReviewBoosterCountDTO countReviewBooster(
            @Param("businessId") Integer businessId, @Param("customerId") Integer customerId);

    MoeBusinessReviewBoosterCountDTO countReviewBoosterByCompany(
            @Param("companyId") Long companyId, @Param("customerId") Integer customerId);

    List<CustomerAvgRateDTO> getAvgReviewRate(List<Integer> customerIds);

    MoeBusinessReviewBoosterRecord selectAppointmentReviewRecord(
            @Param("businessId") Integer businessId,
            @Param("customerId") Integer customerId,
            @Param("appointmentId") Integer appointmentId);

    List<MoeBusinessReviewBoosterRecord> selectAppointmentReviewRecordList(
            @Param("businessId") Integer businessId,
            @Param("customerId") Integer customerId,
            @Param("appointmentId") Integer appointmentId);

    List<MoeBusinessReviewBoosterRecord> selectOneAppointmentReviewRecords(
            @Param("businessId") Integer businessId,
            @Param("customerId") Integer customerId,
            @Param("appointmentId") Integer appointmentId,
            @Param("source") Byte source);

    List<MoeBusinessReviewBoosterRecord> selectAppointmentReviewRecords(
            @Param("businessId") Integer businessId,
            @Param("customerId") Integer customerId,
            @Param("source") Byte source,
            @Param("appointmentIds") List<Integer> appointmentIds);

    List<MoeBusinessReviewBoosterRecord> selectByQueryParams(GetReviewBoosterRecordListParams params);

    Integer countByQueryParams(GetReviewBoosterRecordListParams params);

    Set<Integer> listCustomerIdByFilter(@Param("clientsFilter") ClientsFilterDTO clientsFilter);

    List<MoeBusinessReviewBoosterRecord> listClientReviewForLandingPageConfig(
            @Param("businessId") Integer businessId,
            @Param("minScore") Integer minScore,
            @Param("excludeKeywords") List<String> excludeKeywords);

    List<MoeBusinessReviewBoosterRecord> listReviewBoosterRecords(ListReviewBoosterRecordsParams params);
}
