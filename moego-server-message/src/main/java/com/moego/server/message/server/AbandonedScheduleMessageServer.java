package com.moego.server.message.server;

import static java.util.Optional.ofNullable;

import com.moego.common.utils.PhoneUtil;
import com.moego.server.message.api.IAbandonedScheduleMessageServiceBase;
import com.moego.server.message.dto.AbandonedScheduleMessageDTO;
import com.moego.server.message.mapper.AbandonedScheduleMessageMapper;
import com.moego.server.message.mapperbean.AbandonedScheduleMessageExample;
import com.moego.server.message.mapstruct.AbandonedScheduleMessageConverter;
import com.moego.server.message.service.AbandonedScheduleMessageService;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@RestController
@RequiredArgsConstructor
public class AbandonedScheduleMessageServer extends IAbandonedScheduleMessageServiceBase {

    private final AbandonedScheduleMessageService abandonedScheduleMessageService;
    private final AbandonedScheduleMessageMapper abandonedScheduleMessageMapper;

    @Override
    public int insert(AbandonedScheduleMessageDTO dto) {
        return abandonedScheduleMessageService.insert(AbandonedScheduleMessageConverter.INSTANCE.dtoToEntity(dto));
    }

    @Override
    public List<AbandonedScheduleMessageDTO> listByCondition(ListByConditionParam param) {
        AbandonedScheduleMessageExample example = new AbandonedScheduleMessageExample();
        AbandonedScheduleMessageExample.Criteria criteria = example.createCriteria();
        ofNullable(param.businessIdIn())
                .filter(businessIds -> !businessIds.isEmpty())
                .ifPresent(businessIds -> criteria.andBusinessIdIn(List.copyOf(businessIds)));
        ofNullable(param.customerPhoneIn())
                .map(customerPhones -> customerPhones.stream()
                        .map(PhoneUtil::removeCountryCode)
                        .toList())
                .filter(customerPhones -> !customerPhones.isEmpty())
                .ifPresent(customerPhones -> criteria.andCustomerPhoneIn(List.copyOf(customerPhones)));
        ofNullable(param.customerReplyTimeRange()).ifPresent(customerReplyTimeRange -> {
            ofNullable(customerReplyTimeRange.lower()).ifPresent(criteria::andCustomerReplyTimeGreaterThanOrEqualTo);
            ofNullable(customerReplyTimeRange.upper()).ifPresent(criteria::andCustomerReplyTimeLessThanOrEqualTo);
        });
        ofNullable(param.createdAtRange()).ifPresent(createdAtRange -> {
            ofNullable(createdAtRange.lower()).ifPresent(criteria::andCreatedAtGreaterThanOrEqualTo);
            ofNullable(createdAtRange.upper()).ifPresent(criteria::andCreatedAtLessThanOrEqualTo);
        });

        return abandonedScheduleMessageMapper.selectByExample(example).stream()
                .map(AbandonedScheduleMessageConverter.INSTANCE::entityToDTO)
                .toList();
    }
}
