package com.moego.server.message.mapperbean;

public class MoeBusinessMessageThread {

    /**
     * 用户给前端下发clientColor
     */
    private String clientColor;

    public String getClientColor() {
        return clientColor;
    }

    public void setClientColor(String clientColor) {
        this.clientColor = clientColor;
    }

    private Boolean isNewCustomer;

    public Boolean getIsNewCustomer() {
        return isNewCustomer;
    }

    public void setIsNewCustomer(Boolean isNewCustomer) {
        this.isNewCustomer = isNewCustomer;
    }

    private Boolean isProspectCustomer;

    public Boolean getIsProspectCustomer() {
        return isProspectCustomer;
    }

    public void setIsProspectCustomer(Boolean isProspectCustomer) {
        this.isProspectCustomer = isProspectCustomer;
    }

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_message_thread.id
     *
     * @mbg.generated
     */
    private Integer id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_message_thread.business_id
     *
     * @mbg.generated
     */
    private Integer businessId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_message_thread.staff_id
     *
     * @mbg.generated
     */
    private Integer staffId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_message_thread.customer_id
     *
     * @mbg.generated
     */
    private Integer customerId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_message_thread.first_name
     *
     * @mbg.generated
     */
    private String firstName;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_message_thread.last_name
     *
     * @mbg.generated
     */
    private String lastName;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_message_thread.customer_avatar
     *
     * @mbg.generated
     */
    private String customerAvatar;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_message_thread.last_message_text
     *
     * @mbg.generated
     */
    private String lastMessageText;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_message_thread.last_message_time
     *
     * @mbg.generated
     */
    private Integer lastMessageTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_message_thread.unread_count
     *
     * @mbg.generated
     */
    private Integer unreadCount;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_message_thread.status
     *
     * @mbg.generated
     */
    private Integer status;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_message_thread.create_time
     *
     * @mbg.generated
     */
    private Integer createTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_message_thread.update_time
     *
     * @mbg.generated
     */
    private Integer updateTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_message_thread.delete_time
     *
     * @mbg.generated
     */
    private Integer deleteTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_message_thread.customer_unread_count
     *
     * @mbg.generated
     */
    private Integer customerUnreadCount;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_message_thread.open_status
     *
     * @mbg.generated
     */
    private Integer openStatus;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_message_thread.stars_time
     *
     * @mbg.generated
     */
    private Integer starsTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_message_thread.block_time
     *
     * @mbg.generated
     */
    private Integer blockTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_message_thread.old_group_id
     *
     * @mbg.generated
     */
    private Integer oldGroupId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_message_thread.company_id
     *
     * @mbg.generated
     */
    private Long companyId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_message_thread.last_error_code
     *
     * @mbg.generated
     */
    private Integer lastErrorCode;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_message_thread.last_error_msg
     *
     * @mbg.generated
     */
    private String lastErrorMsg;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_message_thread.last_message_id
     *
     * @mbg.generated
     */
    private Integer lastMessageId;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_message_thread.id
     *
     * @return the value of moe_business_message_thread.id
     *
     * @mbg.generated
     */
    public Integer getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_message_thread.id
     *
     * @param id the value for moe_business_message_thread.id
     *
     * @mbg.generated
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_message_thread.business_id
     *
     * @return the value of moe_business_message_thread.business_id
     *
     * @mbg.generated
     */
    public Integer getBusinessId() {
        return businessId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_message_thread.business_id
     *
     * @param businessId the value for moe_business_message_thread.business_id
     *
     * @mbg.generated
     */
    public void setBusinessId(Integer businessId) {
        this.businessId = businessId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_message_thread.staff_id
     *
     * @return the value of moe_business_message_thread.staff_id
     *
     * @mbg.generated
     */
    public Integer getStaffId() {
        return staffId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_message_thread.staff_id
     *
     * @param staffId the value for moe_business_message_thread.staff_id
     *
     * @mbg.generated
     */
    public void setStaffId(Integer staffId) {
        this.staffId = staffId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_message_thread.customer_id
     *
     * @return the value of moe_business_message_thread.customer_id
     *
     * @mbg.generated
     */
    public Integer getCustomerId() {
        return customerId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_message_thread.customer_id
     *
     * @param customerId the value for moe_business_message_thread.customer_id
     *
     * @mbg.generated
     */
    public void setCustomerId(Integer customerId) {
        this.customerId = customerId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_message_thread.first_name
     *
     * @return the value of moe_business_message_thread.first_name
     *
     * @mbg.generated
     */
    public String getFirstName() {
        return firstName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_message_thread.first_name
     *
     * @param firstName the value for moe_business_message_thread.first_name
     *
     * @mbg.generated
     */
    public void setFirstName(String firstName) {
        this.firstName = firstName == null ? null : firstName.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_message_thread.last_name
     *
     * @return the value of moe_business_message_thread.last_name
     *
     * @mbg.generated
     */
    public String getLastName() {
        return lastName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_message_thread.last_name
     *
     * @param lastName the value for moe_business_message_thread.last_name
     *
     * @mbg.generated
     */
    public void setLastName(String lastName) {
        this.lastName = lastName == null ? null : lastName.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_message_thread.customer_avatar
     *
     * @return the value of moe_business_message_thread.customer_avatar
     *
     * @mbg.generated
     */
    public String getCustomerAvatar() {
        return customerAvatar;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_message_thread.customer_avatar
     *
     * @param customerAvatar the value for moe_business_message_thread.customer_avatar
     *
     * @mbg.generated
     */
    public void setCustomerAvatar(String customerAvatar) {
        this.customerAvatar = customerAvatar == null ? null : customerAvatar.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_message_thread.last_message_text
     *
     * @return the value of moe_business_message_thread.last_message_text
     *
     * @mbg.generated
     */
    public String getLastMessageText() {
        return lastMessageText;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_message_thread.last_message_text
     *
     * @param lastMessageText the value for moe_business_message_thread.last_message_text
     *
     * @mbg.generated
     */
    public void setLastMessageText(String lastMessageText) {
        this.lastMessageText = lastMessageText;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_message_thread.last_message_time
     *
     * @return the value of moe_business_message_thread.last_message_time
     *
     * @mbg.generated
     */
    public Integer getLastMessageTime() {
        return lastMessageTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_message_thread.last_message_time
     *
     * @param lastMessageTime the value for moe_business_message_thread.last_message_time
     *
     * @mbg.generated
     */
    public void setLastMessageTime(Integer lastMessageTime) {
        this.lastMessageTime = lastMessageTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_message_thread.unread_count
     *
     * @return the value of moe_business_message_thread.unread_count
     *
     * @mbg.generated
     */
    public Integer getUnreadCount() {
        return unreadCount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_message_thread.unread_count
     *
     * @param unreadCount the value for moe_business_message_thread.unread_count
     *
     * @mbg.generated
     */
    public void setUnreadCount(Integer unreadCount) {
        this.unreadCount = unreadCount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_message_thread.status
     *
     * @return the value of moe_business_message_thread.status
     *
     * @mbg.generated
     */
    public Integer getStatus() {
        return status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_message_thread.status
     *
     * @param status the value for moe_business_message_thread.status
     *
     * @mbg.generated
     */
    public void setStatus(Integer status) {
        this.status = status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_message_thread.create_time
     *
     * @return the value of moe_business_message_thread.create_time
     *
     * @mbg.generated
     */
    public Integer getCreateTime() {
        return createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_message_thread.create_time
     *
     * @param createTime the value for moe_business_message_thread.create_time
     *
     * @mbg.generated
     */
    public void setCreateTime(Integer createTime) {
        this.createTime = createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_message_thread.update_time
     *
     * @return the value of moe_business_message_thread.update_time
     *
     * @mbg.generated
     */
    public Integer getUpdateTime() {
        return updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_message_thread.update_time
     *
     * @param updateTime the value for moe_business_message_thread.update_time
     *
     * @mbg.generated
     */
    public void setUpdateTime(Integer updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_message_thread.delete_time
     *
     * @return the value of moe_business_message_thread.delete_time
     *
     * @mbg.generated
     */
    public Integer getDeleteTime() {
        return deleteTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_message_thread.delete_time
     *
     * @param deleteTime the value for moe_business_message_thread.delete_time
     *
     * @mbg.generated
     */
    public void setDeleteTime(Integer deleteTime) {
        this.deleteTime = deleteTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_message_thread.customer_unread_count
     *
     * @return the value of moe_business_message_thread.customer_unread_count
     *
     * @mbg.generated
     */
    public Integer getCustomerUnreadCount() {
        return customerUnreadCount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_message_thread.customer_unread_count
     *
     * @param customerUnreadCount the value for moe_business_message_thread.customer_unread_count
     *
     * @mbg.generated
     */
    public void setCustomerUnreadCount(Integer customerUnreadCount) {
        this.customerUnreadCount = customerUnreadCount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_message_thread.open_status
     *
     * @return the value of moe_business_message_thread.open_status
     *
     * @mbg.generated
     */
    public Integer getOpenStatus() {
        return openStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_message_thread.open_status
     *
     * @param openStatus the value for moe_business_message_thread.open_status
     *
     * @mbg.generated
     */
    public void setOpenStatus(Integer openStatus) {
        this.openStatus = openStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_message_thread.stars_time
     *
     * @return the value of moe_business_message_thread.stars_time
     *
     * @mbg.generated
     */
    public Integer getStarsTime() {
        return starsTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_message_thread.stars_time
     *
     * @param starsTime the value for moe_business_message_thread.stars_time
     *
     * @mbg.generated
     */
    public void setStarsTime(Integer starsTime) {
        this.starsTime = starsTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_message_thread.block_time
     *
     * @return the value of moe_business_message_thread.block_time
     *
     * @mbg.generated
     */
    public Integer getBlockTime() {
        return blockTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_message_thread.block_time
     *
     * @param blockTime the value for moe_business_message_thread.block_time
     *
     * @mbg.generated
     */
    public void setBlockTime(Integer blockTime) {
        this.blockTime = blockTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_message_thread.old_group_id
     *
     * @return the value of moe_business_message_thread.old_group_id
     *
     * @mbg.generated
     */
    public Integer getOldGroupId() {
        return oldGroupId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_message_thread.old_group_id
     *
     * @param oldGroupId the value for moe_business_message_thread.old_group_id
     *
     * @mbg.generated
     */
    public void setOldGroupId(Integer oldGroupId) {
        this.oldGroupId = oldGroupId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_message_thread.company_id
     *
     * @return the value of moe_business_message_thread.company_id
     *
     * @mbg.generated
     */
    public Long getCompanyId() {
        return companyId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_message_thread.company_id
     *
     * @param companyId the value for moe_business_message_thread.company_id
     *
     * @mbg.generated
     */
    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_message_thread.last_error_code
     *
     * @return the value of moe_business_message_thread.last_error_code
     *
     * @mbg.generated
     */
    public Integer getLastErrorCode() {
        return lastErrorCode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_message_thread.last_error_code
     *
     * @param lastErrorCode the value for moe_business_message_thread.last_error_code
     *
     * @mbg.generated
     */
    public void setLastErrorCode(Integer lastErrorCode) {
        this.lastErrorCode = lastErrorCode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_message_thread.last_error_msg
     *
     * @return the value of moe_business_message_thread.last_error_msg
     *
     * @mbg.generated
     */
    public String getLastErrorMsg() {
        return lastErrorMsg;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_message_thread.last_error_msg
     *
     * @param lastErrorMsg the value for moe_business_message_thread.last_error_msg
     *
     * @mbg.generated
     */
    public void setLastErrorMsg(String lastErrorMsg) {
        this.lastErrorMsg = lastErrorMsg == null ? null : lastErrorMsg.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_message_thread.last_message_id
     *
     * @return the value of moe_business_message_thread.last_message_id
     *
     * @mbg.generated
     */
    public Integer getLastMessageId() {
        return lastMessageId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_message_thread.last_message_id
     *
     * @param lastMessageId the value for moe_business_message_thread.last_message_id
     *
     * @mbg.generated
     */
    public void setLastMessageId(Integer lastMessageId) {
        this.lastMessageId = lastMessageId;
    }
}
