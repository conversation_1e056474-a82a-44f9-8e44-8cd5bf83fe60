package com.moego.server.message.service.pushNotification.impl;

import com.eatthepath.pushy.apns.ApnsClient;
import com.eatthepath.pushy.apns.ApnsClientBuilder;
import com.eatthepath.pushy.apns.util.ApnsPayloadBuilder;
import com.eatthepath.pushy.apns.util.SimpleApnsPayloadBuilder;
import com.eatthepath.pushy.apns.util.SimpleApnsPushNotification;
import com.eatthepath.pushy.apns.util.TokenUtil;
import com.moego.server.message.service.pushNotification.params.PushTokenParams;
import jakarta.annotation.PostConstruct;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class APNSImpl {

    private ApnsClient apnsClient = null;

    @Value("${native.ios.topic}")
    private String iosTopic;

    @Value("${native.ios.sound}")
    private String iosSound;

    @Value("${native.ios.p12.password}")
    private String p12Password;

    @Value("${native.ios.apns.host}")
    private String iosApnsHost;

    @Value("${moego.mobile.apns.file}")
    private String apnsP12File;

    @PostConstruct
    public void initPushClient() {
        try {
            try (FileInputStream inputStream = new FileInputStream(apnsP12File)) {
                apnsClient = new ApnsClientBuilder()
                        .setApnsServer(iosApnsHost)
                        .setClientCredentials(inputStream, p12Password)
                        .build();
            }
        } catch (IOException e) {
            log.warn("{}", e.getMessage());
        }
    }

    public void sendByParams(List<PushTokenParams> tokenParamsList) {
        for (PushTokenParams pushTokenParams : tokenParamsList) {
            final ApnsPayloadBuilder payloadBuilder = new SimpleApnsPayloadBuilder();
            if (!pushTokenParams.getSilent()) {
                if (StringUtils.isBlank(pushTokenParams.getTitle()) && StringUtils.isBlank(pushTokenParams.getBody())) {
                    continue;
                }
                payloadBuilder.setAlertTitle(pushTokenParams.getTitle());
                payloadBuilder.setAlertBody(pushTokenParams.getBody());
                payloadBuilder.setBadgeNumber(pushTokenParams.getBadge().intValue());
                payloadBuilder.setSound(iosSound);
            }
            payloadBuilder.addCustomProperty("body", pushTokenParams.getData());
            // 是否是静默通知
            payloadBuilder.setContentAvailable(pushTokenParams.getSilent());
            if (pushTokenParams.getGroup() != null) {
                payloadBuilder.setThreadId(pushTokenParams.getGroup().getId());
                payloadBuilder.setSummaryArgument(pushTokenParams.getGroup().getSummaryArgument());
            }
            final String payload = payloadBuilder.build();
            if (log.isDebugEnabled()) {
                log.debug("APNS payload: {}", payload);
            }
            final String token = TokenUtil.sanitizeTokenString(pushTokenParams.getPushToken());
            try {
                apnsClient.sendNotification(new SimpleApnsPushNotification(token, iosTopic, payload));
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
        }
    }
}
