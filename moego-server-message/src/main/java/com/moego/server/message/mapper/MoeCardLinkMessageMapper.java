package com.moego.server.message.mapper;

import com.moego.common.dto.clients.ClientsFilterDTO;
import com.moego.server.message.mapperbean.MoeCardLinkMessage;
import com.moego.server.message.mapperbean.MoeCardLinkMessageExample;
import java.util.List;
import java.util.Set;
import org.apache.ibatis.annotations.Param;

public interface MoeCardLinkMessageMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_card_link_message
     *
     * @mbg.generated
     */
    long countByExample(MoeCardLinkMessageExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_card_link_message
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_card_link_message
     *
     * @mbg.generated
     */
    int insert(MoeCardLinkMessage record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_card_link_message
     *
     * @mbg.generated
     */
    int insertSelective(MoeCardLinkMessage record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_card_link_message
     *
     * @mbg.generated
     */
    List<MoeCardLinkMessage> selectByExample(MoeCardLinkMessageExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_card_link_message
     *
     * @mbg.generated
     */
    MoeCardLinkMessage selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_card_link_message
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(MoeCardLinkMessage record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table moe_card_link_message
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(MoeCardLinkMessage record);

    MoeCardLinkMessage selectByCustomerId(Integer customerId);

    List<MoeCardLinkMessage> getReminderTaskList(Integer businessId, Long startTime, Long endTime);

    Set<Integer> listCustomerIdByFilter(@Param("clientsFilter") ClientsFilterDTO clientsFilter);
}
