package com.moego.server.message.mapperbean;

public class MoeBusinessTwilioBinding {

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_twilio_binding.id
     *
     * @mbg.generated
     */
    private Integer id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_twilio_binding.business_id
     *
     * @mbg.generated
     */
    private Integer businessId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_twilio_binding.twilio_sid
     *
     * @mbg.generated
     */
    private String twilioSid;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_twilio_binding.twilio_token
     *
     * @mbg.generated
     */
    private String twilioToken;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_twilio_binding.ms_sid
     *
     * @mbg.generated
     */
    private String msSid;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_twilio_binding.twilio_number
     *
     * @mbg.generated
     */
    private String twilioNumber;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_twilio_binding.friendly_name
     *
     * @mbg.generated
     */
    private String friendlyName;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_twilio_binding.call_handle_type
     *
     * @mbg.generated
     */
    private Byte callHandleType;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_twilio_binding.call_phone_number
     *
     * @mbg.generated
     */
    private String callPhoneNumber;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_twilio_binding.call_reply_type
     *
     * @mbg.generated
     */
    private Byte callReplyType;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_twilio_binding.reply_message
     *
     * @mbg.generated
     */
    private String replyMessage;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_twilio_binding.assign_status
     *
     * @mbg.generated
     */
    private Integer assignStatus;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_twilio_binding.use_status
     *
     * @mbg.generated
     */
    private Integer useStatus;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_twilio_binding.share
     *
     * @mbg.generated
     */
    private Integer share;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_twilio_binding.create_time
     *
     * @mbg.generated
     */
    private Integer createTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column moe_business_twilio_binding.remark
     *
     * @mbg.generated
     */
    private String remark;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_twilio_binding.id
     *
     * @return the value of moe_business_twilio_binding.id
     *
     * @mbg.generated
     */
    public Integer getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_twilio_binding.id
     *
     * @param id the value for moe_business_twilio_binding.id
     *
     * @mbg.generated
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_twilio_binding.business_id
     *
     * @return the value of moe_business_twilio_binding.business_id
     *
     * @mbg.generated
     */
    public Integer getBusinessId() {
        return businessId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_twilio_binding.business_id
     *
     * @param businessId the value for moe_business_twilio_binding.business_id
     *
     * @mbg.generated
     */
    public void setBusinessId(Integer businessId) {
        this.businessId = businessId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_twilio_binding.twilio_sid
     *
     * @return the value of moe_business_twilio_binding.twilio_sid
     *
     * @mbg.generated
     */
    public String getTwilioSid() {
        return twilioSid;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_twilio_binding.twilio_sid
     *
     * @param twilioSid the value for moe_business_twilio_binding.twilio_sid
     *
     * @mbg.generated
     */
    public void setTwilioSid(String twilioSid) {
        this.twilioSid = twilioSid == null ? null : twilioSid.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_twilio_binding.twilio_token
     *
     * @return the value of moe_business_twilio_binding.twilio_token
     *
     * @mbg.generated
     */
    public String getTwilioToken() {
        return twilioToken;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_twilio_binding.twilio_token
     *
     * @param twilioToken the value for moe_business_twilio_binding.twilio_token
     *
     * @mbg.generated
     */
    public void setTwilioToken(String twilioToken) {
        this.twilioToken = twilioToken == null ? null : twilioToken.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_twilio_binding.ms_sid
     *
     * @return the value of moe_business_twilio_binding.ms_sid
     *
     * @mbg.generated
     */
    public String getMsSid() {
        return msSid;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_twilio_binding.ms_sid
     *
     * @param msSid the value for moe_business_twilio_binding.ms_sid
     *
     * @mbg.generated
     */
    public void setMsSid(String msSid) {
        this.msSid = msSid == null ? null : msSid.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_twilio_binding.twilio_number
     *
     * @return the value of moe_business_twilio_binding.twilio_number
     *
     * @mbg.generated
     */
    public String getTwilioNumber() {
        return twilioNumber;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_twilio_binding.twilio_number
     *
     * @param twilioNumber the value for moe_business_twilio_binding.twilio_number
     *
     * @mbg.generated
     */
    public void setTwilioNumber(String twilioNumber) {
        this.twilioNumber = twilioNumber == null ? null : twilioNumber.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_twilio_binding.friendly_name
     *
     * @return the value of moe_business_twilio_binding.friendly_name
     *
     * @mbg.generated
     */
    public String getFriendlyName() {
        return friendlyName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_twilio_binding.friendly_name
     *
     * @param friendlyName the value for moe_business_twilio_binding.friendly_name
     *
     * @mbg.generated
     */
    public void setFriendlyName(String friendlyName) {
        this.friendlyName = friendlyName == null ? null : friendlyName.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_twilio_binding.call_handle_type
     *
     * @return the value of moe_business_twilio_binding.call_handle_type
     *
     * @mbg.generated
     */
    public Byte getCallHandleType() {
        return callHandleType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_twilio_binding.call_handle_type
     *
     * @param callHandleType the value for moe_business_twilio_binding.call_handle_type
     *
     * @mbg.generated
     */
    public void setCallHandleType(Byte callHandleType) {
        this.callHandleType = callHandleType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_twilio_binding.call_phone_number
     *
     * @return the value of moe_business_twilio_binding.call_phone_number
     *
     * @mbg.generated
     */
    public String getCallPhoneNumber() {
        return callPhoneNumber;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_twilio_binding.call_phone_number
     *
     * @param callPhoneNumber the value for moe_business_twilio_binding.call_phone_number
     *
     * @mbg.generated
     */
    public void setCallPhoneNumber(String callPhoneNumber) {
        this.callPhoneNumber = callPhoneNumber == null ? null : callPhoneNumber.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_twilio_binding.call_reply_type
     *
     * @return the value of moe_business_twilio_binding.call_reply_type
     *
     * @mbg.generated
     */
    public Byte getCallReplyType() {
        return callReplyType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_twilio_binding.call_reply_type
     *
     * @param callReplyType the value for moe_business_twilio_binding.call_reply_type
     *
     * @mbg.generated
     */
    public void setCallReplyType(Byte callReplyType) {
        this.callReplyType = callReplyType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_twilio_binding.reply_message
     *
     * @return the value of moe_business_twilio_binding.reply_message
     *
     * @mbg.generated
     */
    public String getReplyMessage() {
        return replyMessage;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_twilio_binding.reply_message
     *
     * @param replyMessage the value for moe_business_twilio_binding.reply_message
     *
     * @mbg.generated
     */
    public void setReplyMessage(String replyMessage) {
        this.replyMessage = replyMessage == null ? null : replyMessage.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_twilio_binding.assign_status
     *
     * @return the value of moe_business_twilio_binding.assign_status
     *
     * @mbg.generated
     */
    public Integer getAssignStatus() {
        return assignStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_twilio_binding.assign_status
     *
     * @param assignStatus the value for moe_business_twilio_binding.assign_status
     *
     * @mbg.generated
     */
    public void setAssignStatus(Integer assignStatus) {
        this.assignStatus = assignStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_twilio_binding.use_status
     *
     * @return the value of moe_business_twilio_binding.use_status
     *
     * @mbg.generated
     */
    public Integer getUseStatus() {
        return useStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_twilio_binding.use_status
     *
     * @param useStatus the value for moe_business_twilio_binding.use_status
     *
     * @mbg.generated
     */
    public void setUseStatus(Integer useStatus) {
        this.useStatus = useStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_twilio_binding.share
     *
     * @return the value of moe_business_twilio_binding.share
     *
     * @mbg.generated
     */
    public Integer getShare() {
        return share;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_twilio_binding.share
     *
     * @param share the value for moe_business_twilio_binding.share
     *
     * @mbg.generated
     */
    public void setShare(Integer share) {
        this.share = share;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_twilio_binding.create_time
     *
     * @return the value of moe_business_twilio_binding.create_time
     *
     * @mbg.generated
     */
    public Integer getCreateTime() {
        return createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_twilio_binding.create_time
     *
     * @param createTime the value for moe_business_twilio_binding.create_time
     *
     * @mbg.generated
     */
    public void setCreateTime(Integer createTime) {
        this.createTime = createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column moe_business_twilio_binding.remark
     *
     * @return the value of moe_business_twilio_binding.remark
     *
     * @mbg.generated
     */
    public String getRemark() {
        return remark;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column moe_business_twilio_binding.remark
     *
     * @param remark the value for moe_business_twilio_binding.remark
     *
     * @mbg.generated
     */
    public void setRemark(String remark) {
        this.remark = remark == null ? null : remark.trim();
    }
}
