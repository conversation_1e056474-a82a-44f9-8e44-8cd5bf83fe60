package com.moego.server.message.web;

import com.moego.common.response.ResponseResult;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthType;
import com.moego.server.message.service.receive.ReceiveEmailService;
import com.moego.server.message.service.util.EmailDecodeUtil;
import jakarta.servlet.http.HttpServletRequest;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * Mandrill Email 回调接口
 *
 * <AUTHOR>
 * @date 2020-06-16 21:34
 */
@Slf4j
@RestController
@RequestMapping("/message/email")
public class EmailController {

    @Autowired
    private ReceiveEmailService receiveEmailService;

    @Value("${mandrill.webhook.moment.inboundKey}")
    private String momentInboundKey;

    @Value("${mandrill.webhook.moego.inboundKey}")
    private String moegoInboundKey;

    @Value("${mandrill.webhook.moego.eventKey}")
    private String moegoEventKey;

    // FIXME(Ritchie, P2): add signature verification
    @PostMapping(value = "/receive", consumes = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
    @Auth(AuthType.ANONYMOUS)
    public ResponseResult<String> receiveEmail(
            @RequestParam Map<String, String> payload,
            @RequestHeader("X-Mandrill-Signature") String signature,
            HttpServletRequest request) {
        // 检查 signature
        checkSignature(payload, signature, request.getRequestURL().toString(), momentInboundKey);

        String inboundEmail = payload.get("mandrill_events");
        try {
            inboundEmail = EmailDecodeUtil.decode(inboundEmail);
        } catch (Exception e) {
            log.warn("Can't decode inbound email body, don't decode", e);
        }
        receiveEmailService.updateAppointmentsByEmail(inboundEmail);
        return ResponseResult.success();
    }

    @PostMapping(value = "/events", consumes = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
    @Auth(AuthType.ANONYMOUS)
    public ResponseResult<String> handleEmailEvent(
            @RequestParam Map<String, String> payload,
            @RequestHeader("X-Mandrill-Signature") String signature,
            HttpServletRequest request) {
        // 检查 signature
        checkSignature(payload, signature, request.getRequestURL().toString(), moegoEventKey);

        String mandrillEvents = payload.get("mandrill_events");
        try {
            mandrillEvents = EmailDecodeUtil.decode(mandrillEvents);
        } catch (Exception e) {
            log.warn("Can't decode mandrill events body, don't decode", e);
        }
        receiveEmailService.handleMarketingEmailEvents(mandrillEvents);
        return ResponseResult.success();
    }

    @PostMapping(value = "/reply", consumes = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
    @Auth(AuthType.ANONYMOUS)
    public ResponseResult<String> handleEmailReply(
            @RequestParam Map<String, String> payload,
            @RequestHeader("X-Mandrill-Signature") String signature,
            HttpServletRequest request) {
        // 检查 signature
        checkSignature(payload, signature, request.getRequestURL().toString(), moegoInboundKey);

        String mandrillEvents = payload.get("mandrill_events");
        try {
            mandrillEvents = EmailDecodeUtil.decode(mandrillEvents);
        } catch (Exception e) {
            log.warn("Can't decode mandrill events body, don't decode", e);
        }
        receiveEmailService.handleEmailReplies(mandrillEvents);
        return ResponseResult.success();
    }

    private void checkSignature(Map<String, String> payload, String signature, String url, String key) {
        if (!receiveEmailService.isSignatureValid(payload, signature, url, key)) {
            // 暂时只打日志记录，不实际拦截
            log.error(
                    "Signature is invalid, payload: {}, signature: {}, url: {}, key: {}",
                    payload,
                    signature,
                    url,
                    key.substring(0, 5) + "********" + key.substring(key.length() - 5));
        }
    }
}
