package com.moego.server.message.web;

import static com.moego.lib.common.exception.ExceptionUtil.bizException;

import com.microtripit.mandrillapp.lutung.view.MandrillMessageStatus;
import com.moego.common.dto.CommonResultDto;
import com.moego.common.dto.PageDTO;
import com.moego.common.enums.BusinessConst;
import com.moego.common.enums.ResponseCodeEnum;
import com.moego.common.exception.CommonException;
import com.moego.common.response.ResponseResult;
import com.moego.common.utils.CommonUtil;
import com.moego.common.utils.Pagination;
import com.moego.idl.models.errors.v1.Code;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.server.business.client.IBusinessBusinessClient;
import com.moego.server.business.dto.MoeBusinessDto;
import com.moego.server.business.params.InfoIdParams;
import com.moego.server.customer.client.ICustomerCustomerClient;
import com.moego.server.customer.dto.CustomerPrimaryDto;
import com.moego.server.customer.dto.MoeBusinessCustomerDTO;
import com.moego.server.customer.params.CustomerInfoIdParams;
import com.moego.server.grooming.client.IGroomingAppointmentClient;
import com.moego.server.grooming.client.IGroomingOnlineBookingClient;
import com.moego.server.grooming.dto.GroomingTicketWindowDetailDTO;
import com.moego.server.grooming.dto.appointment.history.SendNotificationLogDTO;
import com.moego.server.grooming.enums.AppointmentAction;
import com.moego.server.grooming.enums.NotificationTypeEnum;
import com.moego.server.message.consts.MessageAction;
import com.moego.server.message.dto.EmailReportItem;
import com.moego.server.message.dto.MessageCountDTO;
import com.moego.server.message.dto.MessageDetailDTO;
import com.moego.server.message.dto.MessageReportItem;
import com.moego.server.message.enums.AutoMessageTemplateEnum;
import com.moego.server.message.enums.MessageDetailEnum;
import com.moego.server.message.enums.MessageMethodTypeEnum;
import com.moego.server.message.enums.MessageTargetTypeEnums;
import com.moego.server.message.enums.ReminderTypeEnum;
import com.moego.server.message.mapperbean.MoeBusinessMessageBatch;
import com.moego.server.message.mapperbean.MoeBusinessMessageDetail;
import com.moego.server.message.mapperbean.MoeBusinessMessageThread;
import com.moego.server.message.params.AutoMessageParams;
import com.moego.server.message.params.AutoTemplateSendParams;
import com.moego.server.message.params.BatchAutoTemplateSendParams;
import com.moego.server.message.params.SendMessagesParams;
import com.moego.server.message.params.SendReadyNotificationParams;
import com.moego.server.message.params.ThreadUpdateParams;
import com.moego.server.message.service.AutoMessageService;
import com.moego.server.message.service.BusinessMessageControl;
import com.moego.server.message.service.CustomerVerifyCodeService;
import com.moego.server.message.service.MessageDetailService;
import com.moego.server.message.service.MoeBusinessMessageBatchService;
import com.moego.server.message.service.MoeBusinessMessageDetailService;
import com.moego.server.message.service.MoeBusinessMessageThreadService;
import com.moego.server.message.service.MoeBusinessReminderService;
import com.moego.server.message.service.OBImpersonateService;
import com.moego.server.message.service.client.AppointmentServiceClient;
import com.moego.server.message.service.dto.BatchSendMessageResultDto;
import com.moego.server.message.service.dto.UnassignRecordListDto;
import com.moego.server.message.service.sendmessage.MessageSendRouterService;
import com.moego.server.message.service.sendmessage.impl.TwilioMMSUtils;
import com.moego.server.message.service.util.FileHelper;
import com.moego.server.message.web.dto.ob.FileInfoDto;
import com.moego.server.message.web.dto.ob.SendMMSResult;
import com.moego.server.message.web.dto.ob.SendVerifyCodeResultDto;
import com.moego.server.message.web.model.CodeSendRequest;
import com.moego.server.message.web.model.CodeSendResponse;
import com.moego.server.message.web.params.FileInfoGroupParams;
import com.moego.server.message.web.params.SendMMSParams;
import com.moego.server.message.web.vo.ManualSendReminderVo;
import com.moego.server.message.web.vo.MoeBusinessMessageDetailVO;
import com.moego.server.message.web.vo.PhoneNumberVo;
import com.moego.svc.activitylog.event.enums.Action;
import com.moego.svc.activitylog.event.enums.ResourceType;
import com.moego.svc.activitylog.processor.ActivityLog;
import com.moego.svc.activitylog.processor.ActivityLogRecorder;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2020-07-05 15:44
 */
@RestController
@RequestMapping("/message/send")
@Slf4j
@RequiredArgsConstructor
public class MessageSendController {

    private final MessageSendRouterService messageSendRouterService;
    private final MoeBusinessMessageDetailService messageDetailService;
    private final MessageDetailService messageDetailServiceV2;
    private final MoeBusinessMessageThreadService messageThreadService;
    private final MoeBusinessMessageBatchService messageBatchService;
    private final CustomerVerifyCodeService customerVerifyCodeService;
    private final IGroomingAppointmentClient groomingAppointmentClient;
    private final IGroomingOnlineBookingClient groomingOnlineBookingClient;
    private final IBusinessBusinessClient iBusinessBusinessClient;
    private final BusinessMessageControl messageControl;
    private final AutoMessageService autoMessageService;
    private final MoeBusinessReminderService businessReminderService;
    private final OBImpersonateService obImpersonateService;
    private final ICustomerCustomerClient iCustomerCustomerClient;
    private final FileHelper fileHelper;
    private final AppointmentServiceClient appointmentServiceClient;
    ;

    /**
     * Get parsed auto message text, type refer to AutoMessageTemplateEnum
     *
     * <p>Only support 4, 5 (ready for pick up, send ETA) currently
     * <p>
     * DONE(JX): 需要校验 customerId, appointmentId 是否属于当前 business
     */
    @GetMapping("/autoMessage")
    @Auth(AuthType.BUSINESS)
    public ResponseResult<String> getParsedAutoMessage(
            AuthContext context, Integer type, Integer customerId, Integer appointmentId) {
        Integer tokenBusinessId = context.getBusinessId();
        return ResponseResult.success(autoMessageService.getAutoMessage(
                context.companyId(), tokenBusinessId, type, customerId, appointmentId));
    }

    /**
     * 根据type获取reminder信息，用于商家手动发送，目前接口仅支持rebook reminder
     *
     * @param context
     * @param type 4-rebook, 3-pet birthday
     * @param targetId 当type=4（rebook）时，targetId -> customerId
     * @return 根据reminder模板解析后的消息内容
     */
    @GetMapping("/reminder/content")
    @Auth(AuthType.BUSINESS)
    public String getParsedReminderMessage(AuthContext context, @RequestParam("type") Integer type, Integer targetId) {
        if (Objects.equals(type, ReminderTypeEnum.REBOOK.getReminderType())) {
            return businessReminderService.getParsedRebookReminderMsg(
                    context.companyId(), context.getBusinessId(), targetId);
        }
        return "";
    }

    @DeleteMapping("/delete")
    @Auth(AuthType.BUSINESS)
    @ActivityLog(
            action = Action.DELETE,
            resourceType = ResourceType.MESSAGE,
            resourceId = "#messageDetailId",
            beforeInvocation = true)
    public ResponseResult<String> deleteSendMessage(AuthContext context, Integer messageDetailId) {
        // DONE(JX): no unsafe
        Integer tokenBusinessId = context.getBusinessId();
        // DONE(JX): no unsafe
        Integer tokenStaffId = context.getStaffId();
        return messageDetailService.deleteSendMessage(true, tokenBusinessId, tokenStaffId, messageDetailId);
    }

    @PutMapping("/set/all/read")
    @Auth(AuthType.BUSINESS)
    @ActivityLog(action = MessageAction.MARK_ALL_AS_READ, resourceType = ResourceType.MESSAGE)
    public ResponseResult<Boolean> setAllMessageRead(AuthContext context) {
        return ResponseResult.success(messageDetailService.setAllMessageRead(context.getBusinessId()));
    }

    @GetMapping("/unassign/list")
    @Auth(AuthType.COMPANY)
    public UnassignRecordListDto listUnassignMessageCallDetail(AuthContext context, Integer pageNum, Integer pageSize) {
        pageNum = Optional.ofNullable(pageNum).orElse(1);
        pageSize = Optional.ofNullable(pageSize).orElse(10);
        var unassignList = messageDetailServiceV2.listUnassignMessageCallDetail(
                context.companyId(),
                Pagination.builder().pageNum(pageNum).pageSize(pageSize).build());
        return UnassignRecordListDto.builder()
                .unassignRecordList(unassignList)
                .unreadCount((long) messageDetailServiceV2.queryUnassignMessageDetailUnreadCount(context.companyId()))
                .build();
    }

    @PutMapping("/unassign/dismiss")
    @Auth(AuthType.BUSINESS)
    public Boolean dismissUnassignMessageDetail(AuthContext context, @Valid @RequestBody PhoneNumberVo phoneNumberVo) {
        return messageDetailServiceV2.dismissUnassignMessageDetail(context.companyId(), phoneNumberVo.getPhoneNumber())
                > 0;
    }

    @PutMapping("/unassign/read")
    @Auth(AuthType.BUSINESS)
    public Boolean setUnassignMessageDetailRead(AuthContext context) {
        return messageDetailServiceV2.setUnassignMessageDetailRead(context.companyId()) > 0;
    }

    @GetMapping("/thread/list")
    @Auth(AuthType.BUSINESS)
    public ResponseResult<PageDTO<MoeBusinessMessageDetailVO>> listThreadSendMessages(
            AuthContext context, @Valid SendMessagesParams sendMessagesParams) {
        Integer tokenBusinessId = context.getBusinessId();
        Integer tokenStaffId = context.getStaffId();
        if (sendMessagesParams.getPageNo() == null) {
            sendMessagesParams.setPageNo(1);
        }
        if (sendMessagesParams.getPageSize() == null) {
            sendMessagesParams.setPageSize(10);
        }

        sendMessagesParams.setBusinessId(tokenBusinessId);
        sendMessagesParams.setTargetType(MessageTargetTypeEnums.TARGET_TYPE_THREAD.getValue());
        sendMessagesParams.setOrderBy("create_time desc,id desc");
        log.info("获取消息发送列表，sendMessagesParams={}", sendMessagesParams);
        ResponseResult<MoeBusinessMessageThread> threadDetailResult =
                messageThreadService.findThreadDetail(tokenBusinessId, sendMessagesParams.getTargetId());
        if (threadDetailResult == null || threadDetailResult.getData() == null) {
            return ResponseResult.success(null);
        }

        MoeBusinessMessageThread thread = threadDetailResult.getData();
        // 判断是否有权限看到当前的customer聊天内容
        if (!messageThreadService.isStaffAbleToReachCustomer(tokenBusinessId, tokenStaffId, thread.getCustomerId())) {
            // 无权限查看
            throw new CommonException(ResponseCodeEnum.NO_PERMISSION_SHOW_MESSAGE_THREAD);
        }
        sendMessagesParams.getCustomer().setCustomerId(thread.getCustomerId());
        PageDTO<MoeBusinessMessageDetail> threadMessageDetailPage =
                messageDetailService.listSendMessagesForMessageCenter(sendMessagesParams);
        List<Integer> unreadMessageIds = threadMessageDetailPage.getDataList().stream()
                .filter(this::isCustomerUnread)
                .map(MoeBusinessMessageDetail::getId)
                .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(unreadMessageIds)) {
            messageDetailService.batchUpdateMessageBusinessRead(unreadMessageIds);
            ThreadUpdateParams threadUpdateParams = new ThreadUpdateParams();
            threadUpdateParams.setBusinessId(sendMessagesParams.getBusinessId());
            threadUpdateParams.setStaffId(sendMessagesParams.getStaffId());
            threadUpdateParams.setIds(Collections.singletonList(sendMessagesParams.getTargetId()));
            threadUpdateParams.setUpdateType(ThreadUpdateParams.UPDATE_TYPE_READ);
            messageThreadService.updateThread(threadUpdateParams);
        }

        List<MoeBusinessMessageDetailVO> messageDetailDTOList =
                messageDetailService.parseMessageDetail(tokenBusinessId, threadMessageDetailPage.getDataList());
        PageDTO<MoeBusinessMessageDetailVO> pageDTO = PageDTO.create(
                messageDetailDTOList,
                threadMessageDetailPage.getTotal(),
                threadMessageDetailPage.getPageNo(),
                threadMessageDetailPage.getPageSize());
        return ResponseResult.success(pageDTO);
    }

    boolean isCustomerUnread(MoeBusinessMessageDetail threadMessageDetail) {
        return (MessageDetailEnum.SEND_BY_CUSTOMER.getValue().equals(threadMessageDetail.getType())
                && MessageDetailEnum.MESSAGE_READ_STATUS_UNREAD.getValue().equals(threadMessageDetail.getIsRead()));
    }

    @GetMapping("/batch/list")
    @Auth(AuthType.BUSINESS)
    public ResponseResult<PageDTO<MoeBusinessMessageBatch>> listBatchSendMessages(
            AuthContext context, SendMessagesParams sendMessagesParams) {
        sendMessagesParams.setBusinessId(context.getBusinessId());
        sendMessagesParams.setTargetType(MessageTargetTypeEnums.TARGET_TYPE_BATCH.getValue());
        sendMessagesParams.setOrderBy("create_time desc");
        if (sendMessagesParams.getPageNo() == null) {
            sendMessagesParams.setPageNo(1);
        }
        if (sendMessagesParams.getPageSize() == null) {
            sendMessagesParams.setPageSize(20);
        }
        PageDTO<MoeBusinessMessageBatch> batchMessageDetailList =
                messageBatchService.listBatchMessages(sendMessagesParams);
        return ResponseResult.success(batchMessageDetailList);
    }

    @GetMapping("/batch/detail")
    @Auth(AuthType.BUSINESS)
    public ResponseResult<PageDTO<MoeBusinessMessageDetailVO>> detailBatchSendMessages(
            AuthContext context, SendMessagesParams sendMessagesParams) {
        // DONE(JX): 这里的 businessId 没有被检查
        sendMessagesParams.setBusinessId(context.getBusinessId());
        sendMessagesParams.setStaffId(context.getStaffId());
        if (sendMessagesParams.getPageNo() == null) {
            sendMessagesParams.setPageNo(1);
        }
        if (sendMessagesParams.getPageSize() == null) {
            sendMessagesParams.setPageSize(20);
        }
        return ResponseResult.success(messageDetailService.queryBatchSendMessageDetail(sendMessagesParams));
    }

    /**
     * If message amount run out, response with error code: ResponseCodeEnum.MESSAGE_AMOUNT_RUN_OUT : 80109
     */
    @PostMapping("/toCustomer/code")
    @Auth(AuthType.DENY)
    @Deprecated
    public ResponseResult<CodeSendResponse> sendCode(AuthContext context, @RequestBody CodeSendRequest request) {
        Integer tokenBusinessId = context.getBusinessId();
        return ResponseResult.success(messageSendRouterService.sendCodeToCustomer(tokenBusinessId, request));
    }

    @PostMapping("/toCustomer/one")
    @Auth(AuthType.BUSINESS)
    public ResponseResult<MoeBusinessMessageDetailVO> sendOneMessageToCustomer(
            AuthContext context, @Valid @RequestBody SendMessagesParams sendMessagesParams) {
        sendMessagesParams.setBusinessId(context.getBusinessId());
        sendMessagesParams.setStaffId(context.getStaffId());
        if (!MessageTargetTypeEnums.AUTO_RECEIPT.getValue().equals(sendMessagesParams.getTargetType())) {
            sendMessagesParams.setTargetType(MessageTargetTypeEnums.TARGET_TYPE_THREAD.getValue());
        }
        sendMessagesParams.setSource(MessageDetailEnum.MESSAGE_SOURCE_MESSAGE_CENTER.getValue());
        if (sendMessagesParams.getCustomer() != null
                && !StringUtils.isEmpty(sendMessagesParams.getCustomer().getCustomerNumber())) {
            sendMessagesParams
                    .getCustomer()
                    .setCustomerNumber(CommonUtil.getNumeric(
                            sendMessagesParams.getCustomer().getCustomerNumber()));
        }
        MoeBusinessMessageDetail messageDetail = messageSendRouterService.sendMessageToCustomer(sendMessagesParams);

        List<MoeBusinessMessageDetailVO> detailDTOList = messageDetailService.parseMessageDetail(
                context.getBusinessId(), Collections.singletonList(messageDetail));
        if (detailDTOList.isEmpty()) {
            return ResponseResult.fail(ResponseCodeEnum.FAIL_TO_SEND_CODE);
        }
        return ResponseResult.success(detailDTOList.get(0));
    }

    @PostMapping("/toCustomer/mms/one")
    @Auth(AuthType.BUSINESS)
    public SendMMSResult sendOneMMsToCustomer(AuthContext context, @Valid @RequestBody SendMMSParams sendMMSParams) {
        if (CollectionUtils.isEmpty(sendMMSParams.getFileInfoGroupList())
                && CollectionUtils.isEmpty(sendMMSParams.getFileInfoList())) {
            throw bizException(Code.CODE_PARAMS_ERROR, "need mms media list");
        }
        // 按分组发送，兼容所有 media case
        List<List<FileInfoDto>> fileInfoGroupList;
        // check params valid
        if (!CollectionUtils.isEmpty(sendMMSParams.getFileInfoGroupList())) {
            TwilioMMSUtils.checkFileList(sendMMSParams.getFileInfoGroupList());
            fileInfoGroupList = sendMMSParams.getFileInfoGroupList();
        } else {
            fileInfoGroupList = TwilioMMSUtils.spliteFileList(sendMMSParams.getFileInfoList());
        }
        var messageDetailList = messageSendRouterService.sendOneMMsToCustomer(
                context.companyId(),
                context.businessId(),
                context.staffId(),
                sendMMSParams.getCustomer(),
                fileInfoGroupList);
        var detailDTOList = messageDetailService.parseMessageDetail(context.getBusinessId(), messageDetailList);
        return new SendMMSResult(detailDTOList);
    }

    @PostMapping("/mms/group")
    @Auth(AuthType.BUSINESS)
    public List<List<FileInfoDto>> fileInfoGroupCheck(@Valid @RequestBody FileInfoGroupParams fileInfoGroupParams) {
        return TwilioMMSUtils.spliteFileList(fileInfoGroupParams.getFileInfoList());
    }

    @PostMapping("/toCustomer/ready/notification")
    @Auth(AuthType.BUSINESS)
    public ResponseResult<MoeBusinessMessageDetail> sendReadyNotification(
            AuthContext context, @Valid @RequestBody SendReadyNotificationParams params) {
        SendMessagesParams sendMessagesParams = new SendMessagesParams();
        BeanUtils.copyProperties(params, sendMessagesParams);
        sendMessagesParams.setBusinessId(context.getBusinessId());
        sendMessagesParams.setStaffId(context.getStaffId());
        sendMessagesParams.setTargetType(MessageTargetTypeEnums.TARGET_TYPE_THREAD.getValue());
        sendMessagesParams.setSource(MessageDetailEnum.MESSAGE_SOURCE_MESSAGE_CENTER.getValue());

        MoeBusinessMessageDetail result = null;

        String failedReason = "";
        try {
            result = messageSendRouterService.sendMessageToCustomer(sendMessagesParams);
        } catch (CommonException exception) {
            log.error("sendReadyNotification error", exception);
            failedReason = messageSendRouterService.processErrorMsg(exception.getCode());
        }

        groomingAppointmentClient.updateReadyNotificationResult(params.getGroomingId(), failedReason);
        return ResponseResult.success(result);
    }

    @PostMapping("/toCustomer/batch")
    @Deprecated
    @Auth(AuthType.BUSINESS) // DONE(JX) : 确定前端不再用了？
    public ResponseResult<List<MoeBusinessMessageDetail>> sendBatchMessageToCustomer(
            AuthContext context, @Valid @RequestBody SendMessagesParams sendMessagesParams) {
        sendMessagesParams.setBusinessId(context.getBusinessId());
        sendMessagesParams.setCompanyId(context.companyId());
        sendMessagesParams.setStaffId(context.getBusinessId());
        sendMessagesParams.setMethod(MessageDetailEnum.MESSAGE_METHOD_MSG.getValue());
        sendMessagesParams.setTargetType(MessageTargetTypeEnums.TARGET_TYPE_BATCH.getValue());
        return messageSendRouterService.sendBatchMessageToCustomer(sendMessagesParams);
    }

    @GetMapping("/batch")
    @Auth(AuthType.BUSINESS)
    public BatchSendMessageResultDto getBatchTaskInfo(AuthContext context, @RequestParam Integer batchId) {
        return messageSendRouterService.getBatchTaskInfo(batchId, context.getBusinessId());
    }

    @PostMapping("/toCustomer/batch/asyn")
    @Auth(AuthType.BUSINESS)
    public BatchSendMessageResultDto sendBatchMessageToCustomerAsyn(
            AuthContext context, @Valid @RequestBody SendMessagesParams sendMessagesParams) {
        sendMessagesParams.setBusinessId(context.getBusinessId());
        sendMessagesParams.setCompanyId(context.companyId());
        sendMessagesParams.setStaffId(context.getStaffId());
        if (Objects.isNull(sendMessagesParams.getMethod())) {
            sendMessagesParams.setMethod(MessageDetailEnum.MESSAGE_METHOD_MSG.getValue());
        }
        sendMessagesParams.setTargetType(MessageTargetTypeEnums.TARGET_TYPE_BATCH.getValue());
        return messageSendRouterService.sendBatchMessageToCustomerAsynV2(sendMessagesParams);
    }

    // FIXME(JX): 这俩接口太复杂, 没看明白, 主要原因就是 SendMessagesParams 太多参数, 太难阅读了, 看看能不能优化一下 (+1)
    @PostMapping("/services/message")
    @Auth(AuthType.BUSINESS)
    public ResponseResult<String> sendServiceMessageToCustomer(
            AuthContext context, @Valid @RequestBody SendMessagesParams sendMessagesParams) {
        Integer tokenBusinessId = context.getBusinessId();
        Integer tokenStaffId = context.getStaffId();
        if (sendMessagesParams.getBusinessId() == null) {
            sendMessagesParams.setBusinessId(tokenBusinessId);
        }
        sendMessagesParams.setStaffId(tokenStaffId);
        sendMessagesParams.setCompanyId(context.companyId());
        Integer groomingId =
                (Integer) sendMessagesParams.getExtParams().get(SendMessagesParams.EXT_PARAM_NAME_GROOMINGID);

        if (MessageTargetTypeEnums.TARGET_TYPE_AUTO_PICKUP.getValue().equals(sendMessagesParams.getTargetType())
                && groomingId != null) {
            ResponseResult<GroomingTicketWindowDetailDTO> result =
                    groomingAppointmentClient.queryTicketDetailWithWindow(tokenBusinessId, groomingId);
            log.info("查询预约信息GroomingTicketWindowDetailDTO={}", result.getData());
            if (result.getData() == null) {
                throw new CommonException(ResponseCodeEnum.APPOINTMENT_NOT_FOUND);
            }
            GroomingTicketWindowDetailDTO groomingTicketWindowDetailDTO = result.getData();
            Integer customerId = groomingTicketWindowDetailDTO.getCustomerId();

            CustomerInfoIdParams customerInfoIdParams = new CustomerInfoIdParams();
            customerInfoIdParams.setBusinessId(tokenBusinessId);
            customerInfoIdParams.setCustomerId(customerId);
            CustomerPrimaryDto customerDetailWithPrimary =
                    iCustomerCustomerClient.getCustomerDetailWithPrimary(customerInfoIdParams);

            boolean success = false;
            RuntimeException failResp = null;
            if (customerDetailWithPrimary.getSendAutoEmail().intValue() == 1) {
                sendMessagesParams.setMethod(MessageDetailEnum.MESSAGE_METHOD_EMAIL.getValue());
                try {
                    messageSendRouterService.sendServicesMessageToCustomer(sendMessagesParams);
                    success = true;
                } catch (RuntimeException ex) {
                    failResp = ex;
                }
            }
            if (customerDetailWithPrimary.getSendAutoMessage().intValue() == 1) {
                sendMessagesParams.setMethod(MessageDetailEnum.MESSAGE_METHOD_MSG.getValue());
                try {
                    messageSendRouterService.sendServicesMessageToCustomer(sendMessagesParams);
                    success = true;
                } catch (RuntimeException ex) {
                    if (failResp == null) {
                        failResp = ex;
                    }
                }
            }

            if (success) {
                return ResponseResult.success();
            }
            if (failResp != null) {
                throw failResp;
            }
            throw new CommonException(
                    ResponseCodeEnum.PARAMS_ERROR,
                    "Receive auto message is off for this client. customerId= " + customerId);
        }

        String failedReason = "";
        CommonException exception = null;
        try {
            messageSendRouterService.sendServicesMessageToCustomer(sendMessagesParams);
        } catch (CommonException ex) {
            log.error("sendReadyForPickupMessage error: ", ex);
            failedReason = messageSendRouterService.processErrorMsg(ex.getCode());
            exception = ex;
        }
        if (MessageTargetTypeEnums.TARGET_TYPE_REVIEW.getValue().equals(sendMessagesParams.getTargetType())) {
            ActivityLogRecorder.record(
                    AppointmentAction.SEND_NOTIFICATION,
                    ResourceType.APPOINTMENT,
                    groomingId,
                    new SendNotificationLogDTO(
                            MessageMethodTypeEnum.MESSAGE_METHOD_MSG,
                            Strings.isEmpty(failedReason),
                            failedReason,
                            NotificationTypeEnum.REVIEW_BOOSTER));
        }

        // see https://moego.atlassian.net/jira/software/c/projects/ERP/issues/ERP-8595
        if (exception != null) {
            throw exception;
        }

        return ResponseResult.success(failedReason);
    }

    @GetMapping("account/email/dataImport")
    @Auth(AuthType.DENY)
    @Deprecated
    public ResponseResult<MandrillMessageStatus[]> sendAccountEmailDataImport(
            @RequestParam("businessId") Integer businessId,
            @RequestParam("toEmail") String toEmail,
            @RequestParam("dataImportUrl") String dataImportUrl) {
        return messageSendRouterService.sendDataImport(businessId, toEmail, dataImportUrl);
    }

    // FIXME(JX): 这俩接口太复杂, 没看明白, 主要原因就是 SendMessagesParams 太多参数, 太难阅读了, 看看能不能优化一下
    @PostMapping("auto/template/send/appointment")
    @Auth(AuthType.BUSINESS)
    public ResponseResult<String> autoTemplateSendAppointment(
            AuthContext context, @RequestBody AutoTemplateSendParams autoTemplateSendParams) {
        Integer tokenBusinessId = context.getBusinessId();
        Integer appointmentId = autoTemplateSendParams.getAppointmentId();
        // 从登录态获取到的 businessId 可能和 appointment 内的不一致，以 appointment 内的为准
        Integer businessIdByAppointment = appointmentServiceClient.getBusinessIdByAppointment(appointmentId);
        if (CommonUtil.isNormal(businessIdByAppointment)) {
            tokenBusinessId = businessIdByAppointment;
        }
        Integer tokenStaffId = context.getStaffId();
        Integer autoTemplateType = autoTemplateSendParams.getAutoTemplateType();
        AutoMessageTemplateEnum autoMessageTemplateEnum = AutoMessageTemplateEnum.APPOINTMENT_BOOK; // 设置默认值
        for (AutoMessageTemplateEnum autoMessageTemplateEnumTmp : AutoMessageTemplateEnum.values()) {
            if (autoMessageTemplateEnumTmp.getValue().equals(autoTemplateType)) {
                autoMessageTemplateEnum = autoMessageTemplateEnumTmp;
                break;
            }
        }
        return messageSendRouterService.autoTemplateSendAppointment(
                AuthContext.get().companyId(),
                tokenBusinessId,
                tokenStaffId,
                autoTemplateSendParams.getCustomerId(),
                appointmentId,
                autoMessageTemplateEnum);
    }

    @PostMapping("batch/auto/template/send/appointment")
    @Auth(AuthType.BUSINESS)
    public CommonResultDto batchAutoTemplateBatchSendAppointment(
            AuthContext context, @RequestBody BatchAutoTemplateSendParams autoTemplateSendParams) {
        Integer tokenBusinessId = context.getBusinessId();
        Integer tokenStaffId = context.getStaffId();
        Integer autoTemplateType = autoTemplateSendParams.getAutoTemplateType();

        AutoMessageTemplateEnum autoMessageTemplateEnum = Arrays.stream(AutoMessageTemplateEnum.values())
                .filter(template -> template.getValue().equals(autoTemplateType))
                .findFirst()
                .orElse(AutoMessageTemplateEnum.APPOINTMENT_BOOK); // 默认值

        autoTemplateSendParams.getAppointmentCustomers().forEach(appointmentCustomer -> {
            // 确保所有customer都能发出消息
            try {
                // 从登录态获取到的 businessId 可能和 appointment 内的不一致，以 appointment 内的为准
                Integer appointmentId = appointmentCustomer.getAppointmentId();
                Integer businessId = tokenBusinessId;
                Integer businessIdByAppointment = appointmentServiceClient.getBusinessIdByAppointment(appointmentId);
                if (CommonUtil.isNormal(businessId)) {
                    businessId = businessIdByAppointment;
                }
                messageSendRouterService.autoTemplateSendAppointment(
                        AuthContext.get().companyId(),
                        businessId,
                        tokenStaffId,
                        appointmentCustomer.getCustomerId(),
                        appointmentId,
                        autoMessageTemplateEnum);
            } catch (Exception e) {
                log.error(
                        "Error sending auto template appointment for customerId: {}, appointmentId: {}",
                        appointmentCustomer.getCustomerId(),
                        appointmentCustomer.getAppointmentId(),
                        e);
            }
        });

        CommonResultDto resultDto = new CommonResultDto();
        resultDto.setResult(true);
        return resultDto;
    }

    @PostMapping("/auto/reminder/send/appointment")
    @Auth(AuthType.BUSINESS)
    public CommonResultDto autoTemplateSendAppointment(
            AuthContext context, @Valid @RequestBody ManualSendReminderVo sendReminderVo) {
        businessReminderService.manualSendReminder(
                context.companyId(), context.getBusinessId(), sendReminderVo, context.getStaffId());
        CommonResultDto resultDto = new CommonResultDto();
        resultDto.setResult(true);
        return resultDto;
    }

    @GetMapping("/count/report")
    @Auth(AuthType.BUSINESS)
    public ResponseResult<List<MessageReportItem>> getMessageReport(AuthContext context) {
        return ResponseResult.success(messageControl.getMessageReport(context.getBusinessId()));
    }

    @GetMapping("/email/count/report")
    @Auth(AuthType.BUSINESS)
    public ResponseResult<List<EmailReportItem>> getEmailMessageReport(AuthContext context) {
        return ResponseResult.success(messageControl.getEmailReport(context.getBusinessId()));
    }

    @GetMapping("/countUsed")
    @Auth(AuthType.BUSINESS)
    public ResponseResult<MessageCountDTO> messageCountCurrentMonth(AuthContext context) {
        return ResponseResult.success(messageControl.getCurrentCycleMessageCount(context.getBusinessId()));
    }

    @Deprecated
    @GetMapping("/count")
    @Operation(summary = "废弃接口，请调用/countUsed接口")
    @Auth(AuthType.BUSINESS) // DONE(JX): GET	/message/send/count	16
    public ResponseResult<MessageCountDTO> getMessageCount(
            AuthContext context,
            @RequestParam(value = "startDate", defaultValue = "2019-01-01") String startDate,
            @RequestParam(value = "endDate", defaultValue = "2100-12-31") String endDate) {
        return messageCountCurrentMonth(context);
    }

    /* ###################### OB C端接口 ######################### */

    @PostMapping("/bookOnline/sendVerifyCode")
    @Auth(AuthType.ANONYMOUS)
    public ResponseResult<SendVerifyCodeResultDto> sendVerifyCode(
            @RequestParam String businessName, @RequestParam String phoneNumber) {
        if (StringUtils.isNotBlank(AuthContext.get().impersonator())) {
            log.info("send verify code by impersonator: [{}]", AuthContext.get().sessionId());
            SendVerifyCodeResultDto resultDto = obImpersonateService.sendVerifyCode(businessName, phoneNumber);
            return ResponseResult.success(resultDto);
        }
        boolean result = false;
        boolean findAccount = false;
        SendVerifyCodeResultDto returnMap = new SendVerifyCodeResultDto();
        // 获取business信息，
        Integer businessId = groomingOnlineBookingClient.getBusinessId(businessName);
        InfoIdParams businessIdParams = new InfoIdParams();
        businessIdParams.setInfoId(businessId);
        MoeBusinessDto businessDto = iBusinessBusinessClient.getBusinessInfo(businessIdParams);
        MoeBusinessCustomerDTO customerDTO =
                iCustomerCustomerClient.getCustomerInfoByPhoneNumberForOnlineBooking(businessId, phoneNumber);
        if (customerDTO != null && customerDTO.getCustomerId() != null) {
            findAccount = true;
            // 是us，需要发送验证码，其它国家不需要
            if (businessDto.getCountry().equals(BusinessConst.COUNTRY_US)
                    || businessDto.getCountry().equals(BusinessConst.COUNTRY_US2)) {
                log.info("need send code");
                customerDTO.setBusinessId(businessId);
                result = customerVerifyCodeService.sendVerifyCode(customerDTO, phoneNumber);
            } else {
                log.info("business country setting:" + businessDto.getCountry());
            }
        }
        returnMap.setResult(result);
        returnMap.setFindAccount(findAccount);
        return ResponseResult.success(returnMap);
    }

    @PostMapping("/autoMessage/querySent")
    @Auth(AuthType.BUSINESS)
    public MessageDetailDTO getAutoMessageSentRecords(
            AuthContext context, @RequestBody @Validated AutoMessageParams params) {
        params.setBusinessId(context.getBusinessId());
        return messageDetailService.getAutoMessageSentRecords(params).stream()
                .findFirst()
                .orElse(null);
    }
}
