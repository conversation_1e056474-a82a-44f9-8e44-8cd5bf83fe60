package com.moego.server.message.service.messagebodyparse.impl;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

import com.moego.server.business.client.IBusinessBusinessClient;
import com.moego.server.business.dto.MoeBusinessDto;
import com.moego.server.business.params.InfoIdParams;
import com.moego.server.grooming.dto.GroomingTicketWindowDetailDTO;
import com.moego.server.message.params.SendMessagesParams;
import java.math.BigDecimal;
import java.util.Collections;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

class MessageBodyParseAppointmentServiceTest {
    @Mock
    IBusinessBusinessClient iBusinessClient;

    @InjectMocks
    MessageBodyParseAppointmentService service;

    @BeforeEach
    public void init() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void test_estimateTotal() {
        final Integer businessId = 2333;
        final String expectedParsedBody = "Total cost is $123,456.79";

        when(iBusinessClient.getBusinessInfo(
                        InfoIdParams.builder().infoId(businessId).build()))
                .thenReturn(MoeBusinessDto.builder().currencySymbol("$").build());

        String body = "Total cost is {estimateTotal}";
        GroomingTicketWindowDetailDTO detail = new GroomingTicketWindowDetailDTO();
        detail.setBusinessId(businessId);
        detail.setEstimatedTotalPrice(new BigDecimal("123456.789"));
        SendMessagesParams params = new SendMessagesParams();
        params.setExtParams(Collections.singletonMap(SendMessagesParams.EXT_PARAM_NAME_APPOINTMENT, detail));

        assertThat(service.hit(body)).isTrue();
        assertThat(service.parse(body, params)).isEqualTo(expectedParsedBody);
    }

    @Test
    void parse_appointmentNights() {
        final String appointmentDate = "2024-01-01";
        final String appointmentEndDate = "2024-01-10";
        final String expectedParsedBody = "Your appointment is 10 nights";

        String body = "Your appointment is {appointmentNights} nights";
        GroomingTicketWindowDetailDTO detail = new GroomingTicketWindowDetailDTO();
        detail.setAppointmentDate("2024-01-01");
        detail.setAppointmentEndDate("2024-01-11");
        SendMessagesParams params = new SendMessagesParams();
        params.setExtParams(Collections.singletonMap(SendMessagesParams.EXT_PARAM_NAME_APPOINTMENT, detail));

        assertThat(service.hit(body)).isTrue();
        assertThat(service.parse(body, params)).isEqualTo(expectedParsedBody);
    }

    @Test
    void parse_appointmentNights_theSameDay() {
        final String date = "2024-01-01";
        final String expectedParsedBody = "Your appointment is 0 nights";

        String body = "Your appointment is {appointmentNights} nights";
        GroomingTicketWindowDetailDTO detail = new GroomingTicketWindowDetailDTO();
        detail.setAppointmentDate(date);
        detail.setAppointmentEndDate(date);
        SendMessagesParams params = new SendMessagesParams();
        params.setExtParams(Collections.singletonMap(SendMessagesParams.EXT_PARAM_NAME_APPOINTMENT, detail));

        assertThat(service.hit(body)).isTrue();
        assertThat(service.parse(body, params)).isEqualTo(expectedParsedBody);
    }

    @Test
    void parse_endDate() {
        final String endDate = "2024-01-10";
        final String dateFormat = "MM/dd/yyyy";
        final String expectedParsedBody = "Your appointment is end at 01/10/2024";

        String body = "Your appointment is end at {appointmentEndDate}";
        GroomingTicketWindowDetailDTO detail = new GroomingTicketWindowDetailDTO();
        detail.setAppointmentEndDate(endDate);
        SendMessagesParams params = new SendMessagesParams();
        params.setExtParams(Collections.singletonMap(SendMessagesParams.EXT_PARAM_NAME_APPOINTMENT, detail));
        params.setDateFormat(dateFormat);

        assertThat(service.hit(body)).isTrue();
        assertThat(service.parse(body, params)).isEqualTo(expectedParsedBody);
    }
}
