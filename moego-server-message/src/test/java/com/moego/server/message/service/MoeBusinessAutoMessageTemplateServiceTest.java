package com.moego.server.message.service;

import static org.junit.jupiter.api.Assertions.assertArrayEquals;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.argThat;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doReturn;

import com.moego.idl.models.offering.v1.ServiceItemType;
import com.moego.lib.featureflag.FeatureFlagApi;
import com.moego.lib.featureflag.features.FeatureFlags;
import com.moego.server.grooming.dto.EvaluationServiceDetailDTO;
import com.moego.server.grooming.dto.GroomingPetDetailDTO;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

class MoeBusinessAutoMessageTemplateServiceTest {

    @InjectMocks
    private MoeBusinessAutoMessageTemplateService service;

    @Mock
    private FeatureFlagApi featureFlagApi;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    @DisplayName("Should return all service types when all are present")
    void shouldReturnAllServiceTypesWhenAllArePresent() {
        GroomingPetDetailDTO petDetail1 = new GroomingPetDetailDTO();
        petDetail1.setServiceItemType(1);

        GroomingPetDetailDTO petDetail2 = new GroomingPetDetailDTO();
        petDetail2.setServiceItemType(2);

        GroomingPetDetailDTO petDetail3 = new GroomingPetDetailDTO();
        petDetail3.setServiceItemType(3);

        doReturn(false).when(featureFlagApi).isOn(any(), any());

        List<ServiceItemType> result = service.collectAndSortServiceTypesByPriority(
                Arrays.asList(petDetail1, petDetail2, petDetail3), Collections.emptyList());
        assertEquals(3, result.size());
        assertArrayEquals(
                new ServiceItemType[] {ServiceItemType.BOARDING, ServiceItemType.DAYCARE, ServiceItemType.GROOMING},
                result.toArray());
    }

    @Test
    @DisplayName("Should return only evaluation service type when only evaluation details are present")
    void shouldReturnOnlyEvaluationServiceTypeWhenOnlyEvaluationDetailsArePresent() {
        doReturn(false).when(featureFlagApi).isOn(any(), any());
        List<ServiceItemType> result = service.collectAndSortServiceTypesByPriority(
                Collections.emptyList(), Collections.singletonList(new EvaluationServiceDetailDTO()));
        assertEquals(1, result.size());
        assertEquals(ServiceItemType.EVALUATION, result.get(0));
    }

    @Test
    @DisplayName("Should return empty list when no service types are present")
    void shouldReturnEmptyListWhenNoServiceTypesArePresent() {
        doReturn(false).when(featureFlagApi).isOn(any(), any());
        List<ServiceItemType> result =
                service.collectAndSortServiceTypesByPriority(Collections.emptyList(), Collections.emptyList());
        assertEquals(0, result.size());
    }

    @Test
    @DisplayName("Should return only daycare when one grooming service is in whitelist")
    void shouldReturnOnlyDaycareWhenOneGroomingServiceInWhitelist() {
        // Daycare service A
        GroomingPetDetailDTO daycareDetail = new GroomingPetDetailDTO();
        daycareDetail.setServiceId(1);
        daycareDetail.setServiceItemType(ServiceItemType.DAYCARE.getNumber());

        // Grooming service B (in whitelist)
        GroomingPetDetailDTO groomingDetailB = new GroomingPetDetailDTO();
        groomingDetailB.setServiceId(2);
        groomingDetailB.setServiceItemType(ServiceItemType.GROOMING.getNumber());

        // Grooming service C
        GroomingPetDetailDTO groomingDetailC = new GroomingPetDetailDTO();
        groomingDetailC.setServiceId(3);
        groomingDetailC.setServiceItemType(ServiceItemType.GROOMING.getNumber());

        // Mock feature flag: only service B is in whitelist
        doReturn(false).when(featureFlagApi).isOn(any(), any());
        doReturn(true)
                .when(featureFlagApi)
                .isOn(
                        eq(FeatureFlags.CLOSE_AUTO_REMINDER_SERVICE),
                        argThat(context -> context.attributes().get("service").equals("2")));

        List<ServiceItemType> result = service.collectAndSortServiceTypesByPriority(
                Arrays.asList(daycareDetail, groomingDetailB, groomingDetailC), Collections.emptyList());
        var except = new ServiceItemType[] {ServiceItemType.DAYCARE};
        assertEquals(1, result.size());
        assertArrayEquals(except, result.toArray());
    }

    @Test
    @DisplayName("Should return empty list when all services are in whitelist")
    void shouldReturnEmptyListWhenAllServicesInWhitelist() {
        // Daycare service A
        GroomingPetDetailDTO daycareDetail = new GroomingPetDetailDTO();
        daycareDetail.setServiceId(1);
        daycareDetail.setServiceItemType(ServiceItemType.DAYCARE.getNumber());

        // Grooming service B
        GroomingPetDetailDTO groomingDetail = new GroomingPetDetailDTO();
        groomingDetail.setServiceId(2);
        groomingDetail.setServiceItemType(ServiceItemType.GROOMING.getNumber());

        // Mock feature flag: both services are in whitelist
        doReturn(true).when(featureFlagApi).isOn(eq(FeatureFlags.CLOSE_AUTO_REMINDER_SERVICE), any());

        List<ServiceItemType> result = service.collectAndSortServiceTypesByPriority(
                Arrays.asList(daycareDetail, groomingDetail), Collections.emptyList());

        assertTrue(result.isEmpty());
    }
}
