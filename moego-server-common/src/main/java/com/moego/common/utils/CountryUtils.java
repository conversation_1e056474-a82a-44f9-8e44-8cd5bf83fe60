package com.moego.common.utils;

import static java.util.Map.entry;

import com.moego.common.enums.BusinessConst;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

/**
 * <AUTHOR>
 */
public class CountryUtils {

    /**
     * A complete list of the 249 current officially assigned ISO 3166-1 alpha-2 codes
     */
    private static final Map<String, String> COUNTRY_ALPHA2_CODE = Map.ofEntries(
            entry("Andorra", "AD"),
            entry("United Arab Emirates", "AE"),
            entry("Afghanistan", "AF"),
            entry("Antigua And Barbuda", "AG"),
            entry("Anguilla", "AI"),
            entry("Albania", "AL"),
            entry("Armenia", "AM"),
            entry("Angola", "AO"),
            entry("Antarctica", "AQ"),
            entry("Argentina", "AR"),
            entry("American Samoa", "AS"),
            entry("Austria", "AT"),
            entry("Australia", "AU"),
            entry("Aruba", "AW"),
            entry("Åland Islands", "AX"),
            entry("Azerbaijan", "AZ"),
            entry("Bosnia & Herzegovina", "BA"),
            entry("Barbados", "BB"),
            entry("Bangladesh", "BD"),
            entry("Belgium", "BE"),
            entry("Burkina Faso", "BF"),
            entry("Bulgaria", "BG"),
            entry("Bahrain", "BH"),
            entry("Burundi", "BI"),
            entry("Benin", "BJ"),
            entry("Saint Barthélemy", "BL"),
            entry("Bermuda", "BM"),
            entry("Brunei Darussalam", "BN"),
            entry("Bolivia, Plurinational State Of", "BO"),
            entry("Bonaire, Sint Eustatius And Saba", "BQ"),
            entry("Brazil", "BR"),
            entry("Bahamas", "BS"),
            entry("Bhutan", "BT"),
            entry("Bouvet Island", "BV"),
            entry("Botswana", "BW"),
            entry("Belarus", "BY"),
            entry("Belize", "BZ"),
            entry("Canada", "CA"),
            entry("Cocos (Keeling) Islands", "CC"),
            entry("Democratic Republic Of Congo", "CD"),
            entry("Central African Republic", "CF"),
            entry("Republic Of Congo", "CG"),
            entry("Switzerland", "CH"),
            entry("Côte d'Ivoire", "CI"),
            entry("Cook Islands", "CK"),
            entry("Chile", "CL"),
            entry("Cameroon", "CM"),
            entry("China", "CN"),
            entry("Colombia", "CO"),
            entry("Costa Rica", "CR"),
            entry("Cuba", "CU"),
            entry("Cabo Verde", "CV"),
            entry("Curacao", "CW"),
            entry("Christmas Island", "CX"),
            entry("Cyprus", "CY"),
            entry("Czech Republic", "CZ"),
            entry("Germany", "DE"),
            entry("Djibouti", "DJ"),
            entry("Denmark", "DK"),
            entry("Dominica", "DM"),
            entry("Dominican Republic", "DO"),
            entry("Algeria", "DZ"),
            entry("Ecuador", "EC"),
            entry("Estonia", "EE"),
            entry("Egypt", "EG"),
            entry("Western Sahara", "EH"),
            entry("Eritrea", "ER"),
            entry("Spain", "ES"),
            entry("Ethiopia", "ET"),
            entry("Finland", "FI"),
            entry("Fiji", "FJ"),
            entry("Falkland Islands", "FK"),
            entry("Micronesia, Federated States Of", "FM"),
            entry("Faroe Islands", "FO"),
            entry("France", "FR"),
            entry("Gabon", "GA"),
            entry("United Kingdom", "GB"),
            entry("Grenada", "GD"),
            entry("Georgia", "GE"),
            entry("French Guiana", "GF"),
            entry("Guernsey", "GG"),
            entry("Ghana", "GH"),
            entry("Gibraltar", "GI"),
            entry("Greenland", "GL"),
            entry("Gambia", "GM"),
            entry("Guinea", "GN"),
            entry("Guadeloupe", "GP"),
            entry("Equatorial Guinea", "GQ"),
            entry("Greece", "GR"),
            entry("South Georgia And The South Sandwich Islands", "GS"),
            entry("Guatemala", "GT"),
            entry("Guam", "GU"),
            entry("Guinea-bissau", "GW"),
            entry("Guyana", "GY"),
            entry("Hong Kong", "HK"),
            entry("Heard Island And McDonald Islands", "HM"),
            entry("Honduras", "HN"),
            entry("Croatia", "HR"),
            entry("Haiti", "HT"),
            entry("Hungary", "HU"),
            entry("Indonesia", "ID"),
            entry("Ireland", "IE"),
            entry("Israel", "IL"),
            entry("Isle Of Man", "IM"),
            entry("India", "IN"),
            entry("British Indian Ocean Territory", "IO"),
            entry("Iraq", "IQ"),
            entry("Iran, Islamic Republic Of", "IR"),
            entry("Iceland", "IS"),
            entry("Italy", "IT"),
            entry("Jersey", "JE"),
            entry("Jamaica", "JM"),
            entry("Jordan", "JO"),
            entry("Japan", "JP"),
            entry("Kenya", "KE"),
            entry("Kyrgyzstan", "KG"),
            entry("Cambodia", "KH"),
            entry("Kiribati", "KI"),
            entry("Comoros", "KM"),
            entry("Saint Kitts And Nevis", "KN"),
            entry("Korea, Democratic People's Republic Of", "KP"),
            entry("Korea, Republic Of", "KR"),
            entry("Kuwait", "KW"),
            entry("Cayman Islands", "KY"),
            entry("Kazakhstan", "KZ"),
            entry("Lao People's Democratic Republic", "LA"),
            entry("Lebanon", "LB"),
            entry("Saint Lucia", "LC"),
            entry("Liechtenstein", "LI"),
            entry("Sri Lanka", "LK"),
            entry("Liberia", "LR"),
            entry("Lesotho", "LS"),
            entry("Lithuania", "LT"),
            entry("Luxembourg", "LU"),
            entry("Latvia", "LV"),
            entry("Libya", "LY"),
            entry("Morocco", "MA"),
            entry("Monaco", "MC"),
            entry("Moldova", "MD"),
            entry("Montenegro", "ME"),
            entry("Saint Martin", "MF"),
            entry("Madagascar", "MG"),
            entry("Marshall Islands", "MH"),
            entry("Macedonia, The Former Yugoslav Republic Of", "MK"),
            entry("Mali", "ML"),
            entry("Myanmar", "MM"),
            entry("Mongolia", "MN"),
            entry("Macao", "MO"),
            entry("Northern Mariana Islands", "MP"),
            entry("Martinique", "MQ"),
            entry("Mauritania", "MR"),
            entry("Montserrat", "MS"),
            entry("Malta", "MT"),
            entry("Mauritius", "MU"),
            entry("Maldives", "MV"),
            entry("Malawi", "MW"),
            entry("Mexico", "MX"),
            entry("Malaysia", "MY"),
            entry("Mozambique", "MZ"),
            entry("Namibia", "NA"),
            entry("New Caledonia", "NC"),
            entry("Niger", "NE"),
            entry("Norfolk Island", "NF"),
            entry("Nigeria", "NG"),
            entry("Nicaragua", "NI"),
            entry("Netherlands", "NL"),
            entry("Norway", "NO"),
            entry("Nepal", "NP"),
            entry("Nauru", "NR"),
            entry("Niue", "NU"),
            entry("New Zealand", "NZ"),
            entry("Oman", "OM"),
            entry("Panama", "PA"),
            entry("Peru", "PE"),
            entry("French Polynesia", "PF"),
            entry("Papua New Guinea", "PG"),
            entry("Philippines", "PH"),
            entry("Pakistan", "PK"),
            entry("Poland", "PL"),
            entry("Saint Pierre And Miquelon", "PM"),
            entry("Pitcairn", "PN"),
            entry("Puerto Rico", "PR"),
            entry("Palestinian Territory, Occupied", "PS"),
            entry("Portugal", "PT"),
            entry("Palau", "PW"),
            entry("Paraguay", "PY"),
            entry("Qatar", "QA"),
            entry("Reunion", "RE"),
            entry("Romania", "RO"),
            entry("Serbia", "RS"),
            entry("Russia", "RU"),
            entry("Rwanda", "RW"),
            entry("Saudi Arabia", "SA"),
            entry("Solomon Islands", "SB"),
            entry("Seychelles", "SC"),
            entry("Sudan", "SD"),
            entry("Sweden", "SE"),
            entry("Singapore", "SG"),
            entry("Saint Helena, Ascension And Tristan Da Cunha", "SH"),
            entry("Slovenia", "SI"),
            entry("Svalbard And Jan Mayen", "SJ"),
            entry("Slovakia", "SK"),
            entry("Sierra Leone", "SL"),
            entry("San Marino", "SM"),
            entry("Senegal", "SN"),
            entry("Somalia", "SO"),
            entry("Suriname", "SR"),
            entry("South Sudan", "SS"),
            entry("Sao Tome and Principe", "ST"),
            entry("El Salvador", "SV"),
            entry("Sint Maarten", "SX"),
            entry("Syrian Arab Republic", "SY"),
            entry("Swaziland", "SZ"),
            entry("Turks And Caicos Islands", "TC"),
            entry("Chad", "TD"),
            entry("French Southern Territories", "TF"),
            entry("Togo", "TG"),
            entry("Thailand", "TH"),
            entry("Tajikistan", "TJ"),
            entry("Tokelau", "TK"),
            entry("Timor-Leste, Democratic Republic of", "TL"),
            entry("Turkmenistan", "TM"),
            entry("Tunisia", "TN"),
            entry("Tonga", "TO"),
            entry("Turkey", "TR"),
            entry("Trinidad And Tobago", "TT"),
            entry("Tuvalu", "TV"),
            entry("Taiwan", "TW"),
            entry("Tanzania, United Republic Of", "TZ"),
            entry("Ukraine", "UA"),
            entry("Uganda", "UG"),
            entry("United States Minor Outlying Islands", "UM"),
            entry("United States", "US"),
            entry("Uruguay", "UY"),
            entry("Uzbekistan", "UZ"),
            entry("Vatican City State", "VA"),
            entry("Saint Vincent And The Grenadines", "VC"),
            entry("Venezuela, Bolivarian Republic Of", "VE"),
            entry("Virgin Islands (British)", "VG"),
            entry("Virgin Islands (US)", "VI"),
            entry("Viet Nam", "VN"),
            entry("Vanuatu", "VU"),
            entry("Wallis And Futuna", "WF"),
            entry("Samoa", "WS"),
            entry("Yemen", "YE"),
            entry("Mayotte", "YT"),
            entry("South Africa", "ZA"),
            entry("Zambia", "ZM"),
            entry("Zimbabwe", "ZW"));

    private static final Set<String> ALPHA2_CODES = Set.of(
            "PR", "PS", "PT", "PW", "PY", "QA", "AD", "AE", "AF", "AG", "AI", "AL", "AM", "AO", "AQ", "AR", "AS", "AT",
            "RE", "AU", "AW", "AX", "AZ", "RO", "BA", "BB", "RS", "BD", "BE", "RU", "BF", "BG", "RW", "BH", "BI", "BJ",
            "BL", "BM", "BN", "BO", "SA", "BQ", "SB", "BR", "SC", "BS", "SD", "SE", "BT", "SG", "BV", "BW", "SH", "SI",
            "SJ", "BY", "SK", "BZ", "SL", "SM", "SN", "SO", "CA", "SR", "SS", "CC", "CD", "ST", "SV", "CF", "CG", "SX",
            "CH", "CI", "SY", "SZ", "CK", "CL", "CM", "CN", "CO", "TC", "CR", "TD", "CU", "TF", "CV", "TG", "CW", "TH",
            "CX", "CY", "TJ", "CZ", "TK", "TL", "TM", "TN", "TO", "TR", "TT", "DE", "TV", "TW", "DJ", "TZ", "DK", "DM",
            "DO", "UA", "UG", "DZ", "UM", "US", "EC", "EE", "EG", "EH", "UY", "UZ", "VA", "ER", "VC", "ES", "VE", "ET",
            "VG", "VI", "VN", "VU", "FI", "FJ", "FK", "FM", "FO", "FR", "WF", "GA", "GB", "WS", "GD", "GE", "GF", "GG",
            "GH", "GI", "GL", "GM", "GN", "GP", "GQ", "GR", "GS", "GT", "GU", "GW", "GY", "HK", "HM", "HN", "HR", "YE",
            "HT", "HU", "YT", "ID", "IE", "IL", "IM", "IN", "IO", "ZA", "IQ", "IR", "IS", "IT", "ZM", "JE", "ZW", "JM",
            "JO", "JP", "KE", "KG", "KH", "KI", "KM", "KN", "KP", "KR", "KW", "KY", "KZ", "LA", "LB", "LC", "LI", "LK",
            "LR", "LS", "LT", "LU", "LV", "LY", "MA", "MC", "MD", "ME", "MF", "MG", "MH", "MK", "ML", "MM", "MN", "MO",
            "MP", "MQ", "MR", "MS", "MT", "MU", "MV", "MW", "MX", "MY", "MZ", "NA", "NC", "NE", "NF", "NG", "NI", "NL",
            "NO", "NP", "NR", "NU", "NZ", "OM", "PA", "PE", "PF", "PG", "PH", "PK", "PL", "PM", "PN");

    /**
     * 通过英语的单词获取国家两位缩写码
     *
     * @param countryWord
     * @return
     */
    public static String getCountryTwoCodes(String countryWord) {
        return COUNTRY_ALPHA2_CODE.get(countryWord);
    }

    /**
     * 判断是否在美国，用于：判断是否需要send verify code
     *
     * @param country
     * @return
     */
    public static boolean isUnitedStates(String country) {
        return BusinessConst.COUNTRY_US.equals(country) || BusinessConst.COUNTRY_US2.equals(country);
    }

    /**
     * Check country alpha2 code
     *
     * @param alpha2Code country code
     * @return valid result
     */
    public static boolean isValidAlpha2Code(String alpha2Code) {
        return (Objects.nonNull(alpha2Code) && alpha2Code.length() == 2 && ALPHA2_CODES.contains(alpha2Code));
    }
}
