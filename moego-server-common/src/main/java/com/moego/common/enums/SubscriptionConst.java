package com.moego.common.enums;

/**
 * <AUTHOR>
 * @since 2021/2/23 10:19 AM
 */
public interface SubscriptionConst {
    /**
     * 等级，0-free trial  1-beginner / cool solo  2-rising star  3-bussiness elite  4-enterprise
     */
    String[] LEVEL = new String[] {"free trial", "cool solo", "rising star", "biz elite", "enterprise"};
    /**
     * auto_renew  是否自动续费，0-不自动续费，1-自动续费
     */
    Byte AUTO_RENEW_NO = 0;

    Byte AUTO_RENEW_YES = 1;
    /**
     * 0-mobile 1-salon
     */
    Byte BUSINESS_TYPE_MOBILE = 0;

    Byte BUSINESS_TYPE_SALON = 1;
    /**
     * 套餐类型，0-月套餐，1-年套餐
     */
    Byte PLAN_TYPE_MONTH = 0;

    Byte PLAN_TYPE_ANNUAL = 1;
    /**
     * 1-start 2-success 3-failed
     */
    Byte MSG_START = 1;

    Byte MSG_SUCCESS = 2;
    Byte MSG_FAILED = 3;
    /**
     * 自动购买短信包条件-剩余短信数小于10
     */
    int AUTO_BUY_LEFT_MSG_COUNT = 10;
    /**
     * 是否已增加50%短信
     */
    int MESSAGE_IN_PLAN_ADD_FLAG = 1;
    /**
     * permission state中的 package_msg_num 可以覆盖message cycle
     */
    int MESSAGE_IN_PLAN_IS_RIGHT = 2;

    int MESSAGE_IN_PLAN_IS_DEFAULT = 0;
    /**
     * new pricing type
     */
    Byte NEW_PRICING_TYPE = 2;
    /**
     * charge_status used to indicate switching plan status
     */
    Byte IS_DOWNGRADE = 3;

    Byte IS_NORMAL = 0;
    /**
     * subscription metadata key
     */
    String LEVEL_KEY = "level";

    String VANS_KEY = "vans";
    String LOCATIONS_KEY = "locations";
    /**
     * pricing 订阅变更记录
     */
    Byte RECORD_TYPE_CREATE = 1;

    Byte RECORD_TYPE_SWITCH = 2;
    Byte RECORD_TYPE_CANCEL = 3;
    Byte RECORD_TYPE_REACTIVE = 4;
    Byte RECORD_TYPE_END = 5;

    /**
     * hardware order coupon code
     */
    String HARDWARE_ORDER_COUPON_CODE = "MoeGoHW";

    Integer HARDWARE_PERCENT_OFF = 10;

    /**
     * slack notification const
     */
    String DATE = "Date: ";

    String TIME = "Time：";

    String ENV = "ENV: ";
    String ORDER = "Order: ";
    String ACCOUNT = "Account: ";
}
