package com.moego.common.enums.groomingreport;

import java.util.Arrays;
import org.springframework.util.StringUtils;

public enum GroomingReportQuestionTypeEnum {
    single_choice,
    multi_choice,
    text_input,
    body_view;

    public static boolean checkTypeValid(String typeName) {
        if (!StringUtils.hasText(typeName)) {
            return false;
        }
        return Arrays.stream(GroomingReportQuestionTypeEnum.values())
                .anyMatch(typeEnum -> typeName.toLowerCase().equals(typeEnum.name()));
    }

    public static boolean isChoiceQuestion(String type) {
        return single_choice.name().equals(type) || multi_choice.name().equals(type);
    }

    public static boolean isBodyViewQuestion(String type) {
        return body_view.name().equals(type);
    }
}
