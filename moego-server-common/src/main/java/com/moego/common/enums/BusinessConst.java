package com.moego.common.enums;

/**
 * <AUTHOR>
 */
public interface BusinessConst {
    Byte SEND_DAILY_TRUE = 1;
    Byte SEND_DAILY_FALSE = 2;

    // stripe account metadata key
    String BUSINESS_ID_KEY = "business_id";

    /**
     * List from FrontEnd:
     * https://github.com/Sonatrix/country-list/blob/master/src/data/countries.js
     */
    String COUNTRY_US = "US";

    String COUNTRY_US2 = "United States";
    String COUNTRY_CA = "Canada";
    String COUNTRY_CA2 = "CA";
    String COUNTRY_UK = "United Kingdom";
    String COUNTRY_AU = "Australia";
    String DEFAULT_OWNER = "owner";
    /**
     * 1-USA CA  2-UK AU  3-spain other 4-US
     */
    Byte AREA_CA = 1;

    Byte AREA_UK_AU = 2;
    Byte AREA_OTHER = 3;
    Byte AREA_US = 4;

    Byte OWNER_YES = 1;
    Byte OWNER_NOT = 0;
    /**
     * 0-Mobile Grooming  1-Grooming Salon  2-Hybrid
     */
    Byte APP_TYPE_MOBILE = 0;

    Byte APP_TYPE_SALON = 1;
    Byte APP_TYPE_HYBRID = 2;
    /**
     * 0-Mobile Grooming  1-Grooming Salon for business_mode
     */
    Byte BIZ_MODE_MOBILE = 0;

    Byte BIZ_MODE_SALON = 1;
    /**
     * beginner and rising start price
     */
    Integer BEGINNER_PRICE = 39;

    Integer RISING_PRICE = 69;
    /**
     * stripe 扣费是否成功;0-正常, 1-失败
     */
    Byte CHARGE_SUCCESS = 0;

    Byte CHARGE_FAILED = 1;
    Byte CHARGE_MSG_AUTO_PROCESSING = 2;
    /**
     * free biz max appointment  number for one business
     */
    Integer MAX_APPOINTMENT_NUM = 100;
    /**
     * cache nummber
     */
    Integer INIT_BIZ_ENTRIES = 10;

    Integer MAX_BIZ_ENTRIES = 1000;
    /**
     * business time format type
     */
    Byte TIME_24_HOURS_TYPE = 1;

    Byte TIME_12_HOURS_TYPE = 2;

    /**
     * business daily email time format
     */
    String DAILY_EMAIL_TIME_FORMAT = "MMMM dd, yyyy, hh:mm a";

    /**
     * 默认的van_num数量
     */
    Integer DEFAULT_SALON_MAX_VAN_NUM = 0;

    Integer DEFAULT_MOBILE_MAX_VAN_NUM = 1;

    /**
     * 首选支付方式，1-stripe， 2-square
     */
    Byte PRIMARY_PAY_TYPE_STRIPE = 1;

    Byte PRIMARY_PAY_TYPE_SQUARE = 2;
}
