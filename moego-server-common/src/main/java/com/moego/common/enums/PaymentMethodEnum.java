package com.moego.common.enums;

/**
 * <AUTHOR>
 */
public interface PaymentMethodEnum {
    /**
     * 是否是预设 1是预设 2不是预设 预设无法修改name
     */
    Byte TYPE_DEFAULT_TRUE = 1;

    Byte TYPE_DEFAULT_FALSE = 2;
    /**
     * primary credit card processor
     */
    Byte CARD_PROCESSOR_TYPE_NONE = 0;

    Byte CARD_PROCESSOR_TYPE_STRIPE = 1;
    Byte CARD_PROCESSOR_TYPE_SQUARE = 2;
    /**
     * method_id 小于50为预设值，无法删改，可排序 大于50可排序
     */
    Integer METHOD_CREDIT_CARD = 1;

    Integer METHOD_CASH = 2;
    Integer METHOD_CHECK = 3;
    Integer METHOD_VENMO = 4;
    Integer METHOD_PAYPAL = 5;
    Integer METHOD_CHASE = 6;
    Integer METHOD_OTHER_CARD_READER = 7;
    Integer METHOD_SQUARE = 8;
    String METHOD_NAME_CREDIT_CARD = "Credit card";
    String METHOD_NAME_CASH = "Cash";
    String METHOD_NAME_CHECK = "Check";
    String METHOD_NAME_VENMO = "Venmo";
    String METHOD_NAME_SQUARE = "Square";
    String METHOD_NAME_STRIPE = "Stripe";
    String METHOD_NAME_ZELLE = "Zelle";
    String METHOD_NAME_PAYPAL = "Paypal";
    String METHOD_NAME_CHASE = "Chase";
    String METHOD_NAME_OTHER_CARD_READER = "Other";
    String METHOD_NAME_STRIPE_CREDIT_CARD = "Credit card - Stripe card";
    String METHOD_NAME_SQUARE_CREDIT_CARD = "Credit card - Square card";
    String METHOD_NAME_SQUARE_CREDIT_CARD_TERMINAL = "Credit card - Square terminal";
    String METHOD_NAME_SQUARE_CREDIT_CARD_READER = "Credit card - Square reader";
    //// square_payment_method	1 - card, 2 - card on file, 3- terminal, 4 - reader
    Byte SQUARE_PAYMENT_METHOD_CARD = 1;
    Byte SQUARE_PAYMENT_METHOD_CARD_ON_FILE = 2;
    Byte SQUARE_PAYMENT_METHOD_TERMINAL = 3;
    Byte SQUARE_PAYMENT_METHOD_READER = 4;
    /**
     * MODULE NAME LIST
     */
    String MODULE_GROOMING = "grooming";

    String MODULE_FULFILLMENT = "fulfillment";

    String MODULE_RETAIL = "retail";
    String MODULE_SUBSCRIPTION = "subscription";
    String MODULE_MEMBERSHIP = "membership";
    /**
     * square auth type
     */
    String SQUARE_GRANT_OAUTH = "authorization_code";

    String SQUARE_GRANT_REFRESH = "refresh_token";
    /**
     * square location status
     */
    String SQUARE_LOCATION_ACTIVE = "ACTIVE";
    /**
     * square location capability
     */
    String SQUARE_LOCATION_CARD_PROCESSING = "CREDIT_CARD_PROCESSING";
    /**
     * square pos cash note prefix
     */
    String SQUARE_CREDIT_NOTE_PREFIX = "MoeGo ";
    /**
     *
     * @see <a href="https://developer.squareup.com/reference/square/objects/TerminalCheckout">
     *      square checkout object defination
     *     </a>
     *
     * The status of the TerminalCheckout. Options: PENDING, IN_PROGRESS, CANCEL_REQUESTED, CANCELED, COMPLETED
     */
    String CHECK_OUT_STATUS_PENDING = "PENDING";

    String CHECK_OUT_STATUS_IN_PROGRESS = "IN_PROGRESS";
    String CHECK_OUT_STATUS_CANCELED = "CANCELED";
    String CHECK_OUT_STATUS_COMPLETED = "COMPLETED";
    String CHECK_OUT_STATUS_CANCEL_REQUESTED = "CANCEL_REQUESTED";
    String PAYMENT_DELAY_STATUS_APPROVED = "APPROVED";
    /**
     * refund status list:
     * https://developer.squareup.com/docs/payments-api/refund-payments#refund-status
     * PENDING, COMPLETED, REJECTED, or FAILED.
     */
    String REFUND_STATUS_PENDING = "PENDING";

    String REFUND_STATUS_COMPLETED = "COMPLETED";
    String REFUND_STATUS_REJECTED = "REJECTED";
    String REFUND_STATUS_FAILED = "FAILED";

    String DEPOSIT_PREFIX = "de_";
}
