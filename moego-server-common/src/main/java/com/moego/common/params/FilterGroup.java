package com.moego.common.params;

import com.fasterxml.jackson.annotation.JsonTypeName;
import com.moego.common.enums.filter.TypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import java.util.List;
import lombok.Builder;

/**
 * Multi-condition filter structure group
 *
 * <AUTHOR>
 * @since 2023/4/1
 */
@Builder
@JsonTypeName(value = "filterGroup")
public record FilterGroup(
        @Schema(description = "Filter group conditions, contains and or") TypeEnum type,
        @Schema(description = "Group of filters") List<@NotNull FilterStructure> filters)
        implements FilterStructure {}
