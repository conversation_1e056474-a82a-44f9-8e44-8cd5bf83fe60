-- moe_customer.pet_metadata definition

CREATE TABLE `pet_metadata`
(
  `id`             bigint      NOT NULL AUTO_INCREMENT,
  `company_id`     bigint      NOT NULL DEFAULT '0',
  `metadata_name`  int         NOT NULL DEFAULT '0' COMMENT 'the enumeration values of metadata types. 1-feeding_schedule, 2-feeding_unit etc.',
  `metadata_value` varchar(50) NOT NULL DEFAULT '',
  `extra_json`     json        NOT NULL DEFAULT (json_object()) COMMENT 'extra json information, such as schedule alias name etc.',
  `sort`           int         NOT NULL DEFAULT '0',
  `created_at`     datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at`     datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `deleted_at`     datetime             DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_name` (`company_id`, `metadata_name`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci;

-- moe_customer.pet_feeding definition

CREATE TABLE `pet_feeding`
(
  `id`                  bigint       NOT NULL AUTO_INCREMENT,
  `company_id`          bigint       NOT NULL DEFAULT '0',
  `pet_id`              bigint       NOT NULL DEFAULT '0',
  `feeding_amount`      varchar(255) NOT NULL DEFAULT '' COMMENT 'such as 1.2, 1/2, 1 etc.',
  `feeding_unit`        varchar(255) NOT NULL DEFAULT '' COMMENT 'pet_metadata.metadata_value, metadata_name = 2',
  `feeding_type`        varchar(255) NOT NULL DEFAULT '' COMMENT 'pet_metadata.metadata_value, metadata_name = 3',
  `feeding_source`      varchar(255) NOT NULL DEFAULT '' COMMENT 'pet_metadata.metadata_value, metadata_name = 4',
  `feeding_instruction` varchar(255) NOT NULL DEFAULT '' COMMENT 'pet_metadata.metadata_value, metadata_name = 5',
  `created_at`          datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at`          datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `deleted_at`          datetime              DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_pet` (`company_id`, `pet_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci;


-- moe_customer.pet_medication definition

CREATE TABLE `pet_medication`
(
  `id`                bigint       NOT NULL AUTO_INCREMENT,
  `company_id`        bigint       NOT NULL DEFAULT '0',
  `pet_id`            bigint       NOT NULL DEFAULT '0',
  `medication_amount` varchar(255) NOT NULL DEFAULT '' COMMENT 'such as 1.2, 1/2, 1 etc.',
  `medication_unit`   varchar(255) NOT NULL DEFAULT '' COMMENT 'pet_metadata.metadata_value, metadata_name = 7',
  `medication_name`   varchar(255) NOT NULL DEFAULT '' COMMENT 'medication name, user input',
  `medication_note`   varchar(255) NOT NULL DEFAULT '' COMMENT 'medication note, user input',
  `created_at`        datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at`        datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `deleted_at`        datetime              DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_pet` (`company_id`, `pet_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci;

-- moe_customer.pet_schedule_setting definition

CREATE TABLE `pet_schedule_setting`
(
  `id`                  bigint NOT NULL AUTO_INCREMENT,
  `company_id`          bigint NOT NULL DEFAULT '0',
  `schedule_type`       int    NOT NULL DEFAULT '0' COMMENT '1-feeding, 2-medication',
  `schedule_id`         bigint NOT NULL DEFAULT '0' COMMENT 'pet_feeding.id, pet_medication.id',
  `schedule_time`       int    NOT NULL DEFAULT '0' COMMENT 'Scheduled time, unit in minutes. 09:00 AM = 540, 09:30 AM = 570 etc.',
  `schedule_extra_json` json   NOT NULL DEFAULT (json_object()) COMMENT 'Schedule extra information, such as schedule alias name etc.',
  PRIMARY KEY (`id`),
  KEY `idx_company_schedule` (`company_id`, `schedule_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci;
