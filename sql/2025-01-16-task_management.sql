-- auto-generated definition
create table appointment_task
(
  id             bigint
    primary key,
  company_id     bigint       default 0                     not null,
  business_id    bigint       default 0                     not null,
  task_category  varchar(50)                                not null,
  instruction    varchar(512) default ''::character varying not null,
  appointment_id bigint       default 0,
  service_id     bigint       default 0,
  care_type      integer      default 0,
  pet_id         bigint       default 0,
  lodging_id     bigint       default 0,
  staff_id       bigint       default 0,
  start_date     date         default CURRENT_DATE,
  start_time     integer,
  time_label     varchar(50)  default NULL::character varying,
  duration       integer,
  status         varchar(50)  default 'INCOMPLETE'::character varying,
  note_status    varchar(50)  default NULL::character varying,
  note_content   varchar(256) default NULL::character varying,
  created_at     timestamp    default now()                 not null,
  updated_at     timestamp    default now()                 not null,
  deleted_at     timestamp,
  add_on_id      bigint       default 0
);

comment on table appointment_task is 'appointment task management';

comment on column appointment_task.task_category is 'feeding/medication/add_on/pee/poo/custom...';

comment on column appointment_task.appointment_id is 'optional, moe_grooming_appointment.id';

comment on column appointment_task.service_id is 'optional, main service, moe_grooming_service.id';

comment on column appointment_task.care_type is 'optional, related service item type, 1-grooming, 2-boarding, 3-daycare, 4-evaluation';

comment on column appointment_task.pet_id is 'optional, moe_customer_pet.id';

comment on column appointment_task.staff_id is 'optional, moe_staff.id';

comment on column appointment_task.start_date is 'feeding/medication date or add-on date';

comment on column appointment_task.start_time is 'feeding/medication time, field is null when it is add-on';

comment on column appointment_task.time_label is 'feeding/medication time label, example 09:00 is AM';

comment on column appointment_task.note_status is 'record status information on the completion of various tasks';

create index idx_company_business_id_start_date
  on appointment_task (company_id, business_id, start_date);

create index idx_appointment_id
  on appointment_task (appointment_id);

